# 上下文
文件名：task.md
创建于：2025/5/13 下午4:08:51
创建者：AI
关联协议：RIPER-5 + Multidimensional + Agent Protocol 

# 任务描述
使用 Ant Design 实现如图功能。

# 项目概述
创建一个设置页面，包含用户信息、登录设备、微信绑定和注销账号功能。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
*   需要使用 Ant Design 和 zui 组件库来实现页面功能。
*   Ant Design 组件用于展示基本信息和操作。
*   zui 组件用于页面布局和样式。
*   需要使用 `MUView` 组件作为页面的容器。
*   需要使用 `MUNavbar` 组件作为导航栏。
*   需要使用 `MUList` 组件展示用户信息、登录设备等列表。
*   需要使用 `MUInput` 组件展示和编辑文本信息。
*   需要使用 `MUAvatar` 组件展示头像。
*   需要使用 `MUImagePicker` 组件修改头像。
*   需要使用 `MUButton` 组件触发操作。

# 提议的解决方案 (由 INNOVATE 模式填充)
*   使用 zui 组件库的组件来实现页面功能，并遵循 madp 框架的开发规范。
*   使用 `MUView` 组件作为页面的容器。
*   使用 `MUNavbar` 组件作为导航栏。
*   使用 `MUList` 组件来分组展示用户信息、登录设备、微信绑定和注销账号等信息。
*   用户信息：使用 `MUInput` 组件展示和编辑用户名、职业、手机号、邮箱。使用 `MUAvatar` 组件展示头像，并使用 `MUImagePicker` 组件修改头像。使用 `MUButton` 组件触发重置密码的操作。
*   登录设备：使用 `MUList` 组件展示登录设备列表。
*   微信：使用 `MUButton` 组件触发绑定微信的操作。
*   注销账号：使用 `MUButton` 组件触发注销账号的操作。

# 实施计划 (由 PLAN 模式生成)
