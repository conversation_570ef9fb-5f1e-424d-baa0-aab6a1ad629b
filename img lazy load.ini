graph TD
    A[开始] --> B["HTML准备: <br> <img> 标签使用 data-src 存真实图片URL <br> src 为空或占位图 <br> 添加标记 class (如 lazy)"];
    B --> C["JS: 获取所有待懒加载图片元素 <br> (例: document.querySelectorAll('.lazy'))"];
    C --> D["JS: 创建IntersectionObserver实例 <br> 定义回调函数 callback(entries, observer) <br> 配置选项 options (root, rootMargin, threshold)"];
    D --> E["JS: 遍历图片元素, 对每个图片调用 <br> observer.observe(imageElement)"];
    E --> F["浏览器持续监测被观察元素的可见性 <br> (用户滚动等操作)"];

    F -- 元素可见性变化 --> G["IntersectionObserver回调函数被触发 <br> (传入 entries 数组)"];
    G --> H["JS: 遍历 entries 数组中的每个 entry"];

    H -- 对每个entry --> I{"entry.isIntersecting === true?"};
    I -- 是 (图片进入视口) --> K["JS: 从 img.dataset.src 读取真实图片URL"];
    K --> L["JS: 设置图片 src: img.src = realSrc"];
    L --> M["浏览器开始加载并显示真实图片"];
    M --> O["JS: 停止观察此图片: observer.unobserve(img)"];

    I -- 否 (图片离开视口或未交叉) --> H;
    O --> H;
    H -- 所有entries处理完毕 --> F;