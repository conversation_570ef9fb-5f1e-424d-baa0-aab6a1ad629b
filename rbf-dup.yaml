@mu/agreement:
[
  {
    name: '@mu/agreement',
    version: '1.5.22-nostyle.37',
    from: '@mu/agreement@1.5.22-nostyle.37',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/agreement',
    version: '1.5.22-nostyle.4',
    from: '@mu/agreement@1.5.22-nostyle.4',
    path: '@mumod/repayment/@mu/tarosdk-mu-bio-auth/@mu/bio-auth-zfb'
  }
] 

@mu/render-taro:
[
  {
    name: '@mu/render-taro',
    version: '1.4.0',
    from: '@mu/render-taro@1.4.0',
    path: '@mumod/repayment/@mu/agreement'
  },
  {
    name: '@mu/render-taro',
    version: '1.7.0-beta.1',
    from: '@mu/render-taro@1.7.0-beta.1',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/render-taro',
    version: '1.6.1',
    from: '@mu/render-taro@1.6.1',
    path: '@mumod/repayment/@mu/safe-sms-shell'
  },
  {
    name: '@mu/render-taro',
    version: '1.4.2',
    from: '@mu/render-taro@1.4.2',
    path: '@mumod/repayment/@mu/trade-password-encrypted-shell'
  }
] 

imagemin-svgo:
[
  {
    name: 'imagemin-svgo',
    version: '7.1.0',
    from: 'imagemin-svgo@7.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader'
  },
  {
    name: 'imagemin-svgo',
    version: '10.0.1',
    from: 'imagemin-svgo@10.0.1',
    path: '@mumod/repayment'
  }
] 

babel-polyfill:
[
  {
    name: 'babel-polyfill',
    version: '6.23.0',
    from: 'babel-polyfill@6.23.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/opencollective'
  },
  {
    name: 'babel-polyfill',
    version: '6.26.0',
    from: 'babel-polyfill@6.26.0',
    path: '@mumod/repayment'
  }
] 

dayjs:
[
  {
    name: 'dayjs',
    version: '1.11.6',
    from: 'dayjs@1.11.6',
    path: '@mumod/repayment/@mu/business-plugin'
  },
  {
    name: 'dayjs',
    version: '1.10.7',
    from: 'dayjs@1.10.7',
    path: '@mumod/repayment/@mu/lui'
  },
  {
    name: 'dayjs',
    version: '1.8.20',
    from: 'dayjs@1.8.20',
    path: '@mumod/repayment/@mu/survey/taro-ui'
  }
] 

@mu/onepass-shell:
[
  {
    name: '@mu/onepass-shell',
    version: '1.0.9-beta.4',
    from: '@mu/onepass-shell@1.0.9-beta.4',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/onepass-shell',
    version: '1.0.9-beta.2',
    from: '@mu/onepass-shell@1.0.9-beta.2',
    path: '@mumod/repayment/@mu/safe-sms-shell'
  },
  {
    name: '@mu/onepass-shell',
    version: '1.0.9',
    from: '@mu/onepass-shell@1.0.9',
    path: '@mumod/repayment/@mu/trade-verify-shell'
  }
] 

@mu/safe-sms-shell:
[
  {
    name: '@mu/safe-sms-shell',
    version: '1.2.3-beta.2',
    from: '@mu/safe-sms-shell@1.2.3-beta.2',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/safe-sms-shell',
    version: '1.2.3-beta.3',
    from: '@mu/safe-sms-shell@1.2.3-beta.3',
    path: '@mumod/repayment/@mu/trade-verify-shell'
  }
] 

@mu/zui:
[
  {
    name: '@mu/zui',
    version: '1.21.0',
    from: '@mu/zui@1.21.0',
    path: '@mumod/repayment/@mu/taro-adv'
  },
  {
    name: '@mu/zui',
    version: '1.24.5-beta.43',
    from: '@mu/zui@1.24.5-beta.43',
    path: '@mumod/repayment'
  }
] 

@mu/tarosdk-mu-bio-auth:
[
  {
    name: '@mu/tarosdk-mu-bio-auth',
    version: '1.7.4-beta.12',
    from: '@mu/tarosdk-mu-bio-auth@1.7.4-beta.12',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/tarosdk-mu-bio-auth',
    version: '1.7.3-beta.3',
    from: '@mu/tarosdk-mu-bio-auth@1.7.3-beta.3',
    path: '@mumod/repayment/@mu/trade-verify-shell'
  }
] 

@mu/trade-password-encrypted-shell:
[
  {
    name: '@mu/trade-password-encrypted-shell',
    version: '1.1.3-beta.4',
    from: '@mu/trade-password-encrypted-shell@1.1.3-beta.4',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/trade-password-encrypted-shell',
    version: '1.1.3-beta.6',
    from: '@mu/trade-password-encrypted-shell@1.1.3-beta.6',
    path: '@mumod/repayment/@mu/trade-verify-shell'
  }
] 