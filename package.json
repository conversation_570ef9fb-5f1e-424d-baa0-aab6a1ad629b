{"type": "module", "name": "@mumod/repayment", "version": "3.7.20-50", "private": false, "npm": true, "offline": true, "description": "还款", "templateInfo": {"typescript": false}, "muLocales": ["FQHB", "HK.HK01", "HK.HK02", "HK.HK03", "XSH.XSH01", "HC.HC01"], "scripts": {"prebuild": "node config/copy.js", "predev": "node config/copy.js", "build:h5": "madp config && madp build --type h5 --css lui", "build:weapp": "madp config && madp build --type weapp", "build:alipay": "madp config && madp build --type alipay", "build:swan": "madp config && taro build --type swan", "build:tt": "madp config && taro build --type tt", "build:rn": "madp build --type rn", "build:quickapp": "madp build --type quickapp", "build:qq": "madp build --type qq", "build:h5-report": "madp build --type h5 report", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:h5-se2": "cross-env ENV=se2 npm run dev:h5", "dev:h5-st1": "cross-env ENV=st1 npm run dev:h5", "dev:h5-st2": "cross-env ENV=st2 npm run dev:h5", "dev:h5-uat": "cross-env ENV=uat npm run dev:h5", "dev:h5-se": "cross-env ENV=se npm run dev:h5", "dev:h5-pre": "cross-env ENV=prod npm run dev:h5", "dev:rn": "npm run build:rn -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:qq": "npm run build:qq -- --watch", "lint": "eslint **/**.js **/**.jsx src --fix", "lint:style": "stylelint **/**.scss --fix", "release": "standard-version --release-as prerelease && git push --follow-tags && npm publish", "standard": "standard-version", "release:pre": "npm run standard -- -p && git push --follow-tags && npm run build:weapp && npm publish", "release:tt": "npm run standard -- -p && git push --follow-tags && npm run build:tt && npm publish", "server": "madp server"}, "author": "mucfc", "license": "MIT", "lint-staged": {"src/**/*.{js, jsx}": ["eslint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "dependencies": {"@mu/agreement": "1.5.22-nostyle.37", "@mu/basic-library": "1.24.0-beta.20", "@mu/biometrics": "1.3.5", "@mu/biometrics-shell": "1.1.6-beta.13", "@mu/business-plugin": "1.8.11-4", "@mu/chat-entry-component": "2.6.10", "@mu/coupon-selector": "3.8.0-alpha.8", "@mu/coupon-selector-utils": "1.1.0-alpha.2", "@mu/cui": "1.3.6-beta.10", "@mu/dependency-analyzer": "file:.yalc/@mu/dependency-analyzer", "@mu/geestest": "1.2.0", "@mu/lui": "2.22.37-beta.1", "@mu/onepass-shell": "1.0.9-beta.4", "@mu/op-comp": "1.0.9-beta.1", "@mu/render-taro": "1.7.0-beta.1", "@mu/safe-sms-shell": "1.2.3-beta.2", "@mu/subscribe-btn": "1.0.1-beta.8", "@mu/survey": "2.2.6-beta.5", "@mu/swiper-custom": "4.5.3", "@mu/taro-adv": "1.10.2", "@mu/tarosdk-mu-bio-auth": "1.7.4-beta.12", "@mu/trade-password-encrypted-shell": "1.1.3-beta.4", "@mu/trade-verify-shell": "1.1.4-beta.68", "@mu/wa-richtext": "1.0.1-beta.12", "@mu/zui": "1.24.5-beta.43", "@mucfc.com/services": "2.21.1", "babel-polyfill": "6.26.0", "copy-to-clipboard": "3.3.1", "dayjs": "1.8.20", "find-duplicate-dependencies": "^3.0.0", "graphql": "14.3.1", "graphql-anywhere": "4.2.4", "graphql-tag": "2.10.1", "imagemin-svgo": "^10.0.1", "lodash.round": "4.0.4", "node-sass": "^4.14.1", "rhyke": "1.0.5", "template7": "1.4.2", "viewerjs": "1.9.0"}, "devDependencies": {"@commitlint/cli": "9.1.2", "@commitlint/config-conventional": "8.2.0", "@mu/coupon-selector-utils": "1.0.0-beta.13", "@mu/madp-cli": "1.9.5-beta.17", "@types/react": "16.9.16", "@types/webpack-env": "1.14.1", "babel-eslint": "10.0.3", "babel-plugin-transform-class-properties": "6.24.1", "babel-plugin-transform-decorators-legacy": "1.3.5", "babel-plugin-transform-jsx-stylesheet": "0.6.8", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-preset-env": "1.7.0", "cross-env": "7.0.3", "eslint": "4.19.1", "eslint-config-airbnb": "17.1.1", "eslint-config-taro": "1.3.5", "eslint-plugin-import": "2.19.0", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-react": "7.30.1", "image-webpack-loader": "6.0.0", "lint-staged": "9.5.0", "lodash-es": "4.17.15", "preload-webpack-plugin": "3.0.0-beta.4", "script-ext-html-webpack-plugin": "2.1.5", "standard-version": "9.0.0", "stylelint": "14.9.1", "stylelint-config-standard": "26.0.0", "vconsole-webpack-plugin": "1.5.0", "webpack-bundle-analyzer": "3.4.1"}, "routeWhiteList": ["pages/index/index", "pages/index/over-due-effect", "pages/bill-list-near/index", "pages/bill-list-future/index", "pages/bill-list-all/index", "pages/repay-success/index", "pages/repay-fail/index", "pages/repay-detail/index", "pages/transfer/guide", "pages/common-result/result", "pages/dispatch/index", "pages/payday-modify/index", "pages/fee-reduce/index", "pages/web-view/index", "pages/identity/contact", "pages/identity/information", "pages/express-repay/index", "pages/credit-product/index", "pages/credit-product/list", "pages/credit-product/result", "pages/home/<USER>", "pages/identity/faqs"], "repo": "ssh://*******************:8082/tbf/tbf-repayment-fe/taro-repayment.git", "basicLibrary": 1, "files": ["src", "config"]}