import { Component } from '@tarojs/taro';
import {
  MUView, MUText,
} from '@mu/zui';
import PropTypes from 'prop-types';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

export default class TradeChecker extends Component {
  static propTypes = {
    emptyText: PropTypes.string,
  }

  static defaultProps = {
    emptyText: '暂无待还账单',
  };

  static options = {
    addGlobalClass: true
  }

  config = {
    "styleIsolation" : "shared"
  }

  render() {
    const {
      emptyText,
    } = this.props;

    return (
      <MUView className="empty-sign">
        <MUView className="empty-icon" />
        <MUText className="empty-text">{emptyText}</MUText>
      </MUView>
    );
  }
}
