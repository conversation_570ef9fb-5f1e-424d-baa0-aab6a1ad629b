/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
/* eslint-disable max-len */
/* eslint-disable no-nested-ternary */
import {
  useEffect, useState, useMemo, useCallback
} from '@tarojs/taro';
import Madp from '@mu/madp';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { getStore } from '@api/store';
import RepayModal from '@components/repay-modal/index';
import Util from '@utils/maxin-util';
import CustomConfig from '@config/index';
import Dispatch from '@api/actions';
import {
  MUView,
  MUIcon,
  MURadio
} from '@mu/zui';
import { AgreementDrawer } from '@mu/agreement';
import { getLoginInfo } from '@mu/business-basic';

const themeColor = Util.getThemeColor(CustomConfig.theme);


/**
 * channelAgreementPlanCfgDtoList 数据结构-[{}]
 * {
 * catalogueCode
 * contractList-预览协议列表 []
 * contractText: contracts.text的合集，用于外漏的底部合同名称展示
 * contracts [id, text(外漏的底部合同名称), contractType]
 * list-强读协议列表，点击后处理该数据 []
 * readDuration-强度时长
 * showContract-是否展示强读弹窗
 * }
 */

function contractIconClick() {
  Madp.showModal({
    content: '您的单笔还款限额不足，请签署支付扣款协议提升单笔还款限额',
    showCancel: false,
    confirmColor: themeColor,
    confirmText: '我知道了'
  });
}

let channelParams;
async function getChannelParams() {
  if (channelParams) return channelParams;
  const data = {
    catalogueCodeList: ['13D'],
    paramTypeList: ['AGREEMENT']
  };
  channelParams = await Dispatch.repayment.getChannelParams(data);
  return channelParams;
}

/**
 * 协议相关组件
 */
export default function Contract({
  onChecked,
  showFPAYContract,
  showPREREPAYContract,
  capitalConstractNum,
  showContractMUModal, onModalClose, onModalConfirm, // 补签弹窗
  showDelayRepayConstract, // 展示延期还款协议
  billExtendInfo, // 延期还款协议信息
  trackPrefix, // 埋点页面前缀
  contractInfos // 合同配置
}) {
  const contractApplyList = getStore('contractApplyList') || []; // 还款合同列表
  const selectedCard = getStore('selectedCard');
  const [channelAgreementPlanCfgDtoList, setChannelAgreementPlanCfgDtoList] = useState([]);
  const [readOnce, setReadOnce] = useState(false);
  const [radioValue, setRadioValue] = useState('');
  const storageType = Madp.getChannel() === '3CMBAPP' ? 'LOCAL' : 'SESSION';

  useMemo(() => {
    const contractText = []; // TODO: 可以删除, 未用到此变量
    const contracts = [];
    if (showFPAYContract) {
      if (!capitalConstractNum) {
        // contractText.push('银行卡信息授权书');
        contracts.push({
          id: 1, // id做什么的，保持不动
          text: contractInfos.YHKXXSQ_BZ && contractInfos.YHKXXSQ_BZ.contractName, // 外漏的协议名称
          contractType: contractApplyList.length ? 'BANK_CARD_AUTH' : 'BANKCARDAUTH' // 合同类型
        });
        contracts.push({
          id: 2,
          text: contractInfos.KHWTZFKK_BZ && contractInfos.KHWTZFKK_BZ.contractName,
          contractType: capitalConstractNum ? 'WITHHOLDUN' : contractApplyList.length ? 'PAY_DEDUCT' : 'PAYWITHHOLD'
        });
      } else { // 0：100
        contracts.push({
          id: 1,
          text: contractInfos.KHWTZFKK_LHD0B100 && contractInfos.KHWTZFKK_LHD0B100.contractName,
          contractType: capitalConstractNum ? 'WITHHOLDUN' : contractApplyList.length ? 'PAY_DEDUCT' : 'PAYWITHHOLD'
        });
      }
      // contractText.push('支付扣款协议');
      dispatchTrackEvent({
        event: EventTypes.SO,
        beaconId: `${trackPrefix}.ShowFPAYContract`,
        beaconContent: { cus: { scene: (selectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } },
      });
    }
    if (showDelayRepayConstract) {
      contracts.push({
        id: 1,
        text: '延期还款服务单',
        contractType: 'DELAY_REPAY'
      });
    }
    if (showPREREPAYContract) {
      contracts.push({
        id: 1,
        text: '预还款须知',
        contractType: 'PREPAY_NOTICE'
      });
    }

    // channelAgreementPlanCfgDtoList添加新处理的属性
    channelAgreementPlanCfgDtoList.forEach((item) => {
      item.contracts = contracts;
      item.contractText = contracts.map((contract) => contract.text).join('、');
    });
  }, [channelAgreementPlanCfgDtoList, showFPAYContract, capitalConstractNum, showPREREPAYContract, showDelayRepayConstract, trackPrefix]);

  useMemo(() => {
    const expressRepayContracts = Madp.getStorageSync('expressRepayContracts', storageType);
    if (channelAgreementPlanCfgDtoList && channelAgreementPlanCfgDtoList.length > 0) {
      if (expressRepayContracts === JSON.stringify(channelAgreementPlanCfgDtoList[0].contracts)) {
        setRadioValue('select1');
        onChecked(true);
        setReadOnce(true);
      }
    }
  }, [channelAgreementPlanCfgDtoList, radioValue]);

  const hasContract = channelAgreementPlanCfgDtoList.length > 0 && channelAgreementPlanCfgDtoList[0].contracts && channelAgreementPlanCfgDtoList[0].contracts.length > 0;

  // 单协议跳转或组装协议展示需要的数据
  const onContractClick = useCallback(async ({
    url, text, formData
  } = {}) => {
    if (url) {
      Madp.navigateTo({ url: `/pages/web-view/index?pageUrl=${encodeURIComponent(url)}` });
      return;
    }
    const bankCard = getStore('selectedCard');

    const result = await getLoginInfo();
    const { mobile, custName, idNo } = result || {};
    const date = new Date();
    const { dateFormat } = Util.getCurrentDateTimeInFormat();
    let contractParam = {
      // 此参数在合同3.0无用
      contractType: contractApplyList.some((item) => item.contractType === 'PAY_DEDUCT') ? 'PAY_DEDUCT' : 'PAYWITHHOLD',
      // todo
      bankName: bankCard.bankName,
      accountName: bankCard.bankCustName,
      accountNo: bankCard.bankCardNoMask,
      name: custName,
      certId: idNo,
      yearNow: date.getFullYear(),
      monthNow: date.getMonth() + 1,
      dayNow: date.getDate(),
      // 合同3.0参数，之前的参数不动，待改造后可删除
      // 银行卡信息
      bankCardInfo: {
        accountName: bankCard.bankCustName, // 账户名
        bankName: bankCard.bankName, // 开户银行名称
        accountNo: bankCard.bankCardNoMask, // 账号
      /** 二类户参数是否需要
      * seAccountName // 二类户关联一类户的账户名
      * seBankName // 二类户关联一类户的开户银行
      * seAccountNo // 二类户关联一类户的账号
      */
      },
      // 基础合同信息
      baseContractInfo: {
        signDate: dateFormat, // 签署时间-yyyyMMdd
      }
    };
    // 如果是0：100
    if (capitalConstractNum) {
      const { merchantInfo = {} } = await Dispatch.repayment.queryMerchantInfo({
        queryMerchantNo: capitalConstractNum
      });
      contractParam = {
        contractType: 'WITHHOLDUN',
        name: custName,
        certId: idNo,
        partnerId: capitalConstractNum,
        partner: merchantInfo.merchantName,
        accountName: bankCard.bankCustName,
        bankName: bankCard.bankName,
        accountNo: bankCard.bankCardNoMask,
        yearNow: date.getFullYear(),
        monthNow: date.getMonth() + 1,
        dayNow: date.getDate(),

        // 合同3.0参数，之前的参数不动，待改造后可删除
        // 银行卡信息
        bankCardInfo: {
          accountName: bankCard.bankCustName, // 账户名
          bankName: bankCard.bankName, // 开户银行名称
          accountNo: bankCard.bankCardNoMask, // 账号
        },
        // 基础合同信息
        baseContractInfo: {
          signDate: dateFormat, // 签署时间-yyyyMMdd
          partnerInfoList: [{// 机构信息
            partnerId: capitalConstractNum,
            partner: merchantInfo.merchantName,
            partnerType: '02'
          }]
        }
      };
    }
    if (text === '银行卡信息授权书') {
      contractParam.contractType = contractApplyList.some((item) => item.contractType === 'BANK_CARD_AUTH') ? 'BANK_CARD_AUTH' : 'BANKCARDAUTH';
    }
    if (text === '延期还款服务单') {
      const extendContract = contractApplyList.find((item) => item.contractType === 'DELAY_REPAY') || {};
      contractParam = {
        ...CustomConfig.contractParams,
        contractCategory: 'DELAY_REPAY',
        contractEdition: 'COMMON',
        bankName: bankCard.bankName,
        accountName: bankCard.bankCustName,
        accountNo: bankCard.bankCardNoMask,
        name: custName,
        certId: idNo,
        mobile: mobile,
        yearNow: date.getFullYear(),
        monthNow: date.getMonth() + 1,
        dayNow: date.getDate(),
        bringParam: 1,
        ...extendContract,
        ...billExtendInfo
      };
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'repayment.ExtendList.ClickContractLink', target: this });
    }

    if (text === '预还款须知') {
      contractParam = {
        contractCategory: 'PREPAY_NOTICE',
        contractEdition: 'COMMON',
        bankName: bankCard.bankName,
        accountName: bankCard.bankCustName,
        accountNo: bankCard.bankCardNoMask,
        name: custName,
        certId: idNo,
        yearNow: date.getFullYear(),
        monthNow: date.getMonth() + 1,
        dayNow: date.getDate(),
      };
    }
    contractApplyList.forEach((item) => {
      if (item.contractType === contractParam.contractType) {
        contractParam = { ...contractParam, ...item, contractCategory: item.contractType };
      }
    });
    if (formData) return contractParam;
    Util.viewContract(contractParam);
  }, [capitalConstractNum, billExtendInfo]);

  const transChannelParams = async (channels) => {
    if (!channels) return;
    const newChannels = [];
    // 客户端业务参数配置
    const channelList = [{ channelCatalogueAgreementDtoList: [] }];
    let newChannelList = [];
    const viewContract0 = await onContractClick({ url: '', text: '银行卡信息授权书', formData: true });
    const viewContract1 = await onContractClick({ url: '', text: '', formData: true });
    if (channels.length > 1) {
      channelList[0].channelCatalogueAgreementDtoList = channels.reduce((total, num) => (total.channelCatalogueAgreementDtoList || []).concat(num.channelCatalogueAgreementDtoList));
      newChannelList = [channelList[0]];
    } else {
      newChannelList = channels;
    }
    newChannelList.forEach((channel, index) => {
      newChannels.push({
        catalogueCode: channel.catalogueCode,
        list: [], // 强读协议列表
        contractList: [], // 预览协议列表
        contracts: [], // 协议列表
        showContract: false, // 是否展示强读弹窗
        readDuration: 5, // 强读时长
        contractText: '' // 外露协议名
      });

      // 遍历客户端业务参数配置，forceReadFlag属性是否等于'Y'。如果等于'Y'，则进一步处理该项。
      // 此段代码看起来无作用？？？
      channel.channelCatalogueAgreementDtoList.forEach((item, key) => {
        if (item.forceReadFlag === 'Y') {
          if (contractApplyList.length && item.contractTypeFlag) {
            contractApplyList.forEach((contract) => {
              if (item.contractType === 'BANK_CARD_AUTH' && contract.contractType === 'BANK_CARD_AUTH') {
                item.custShowName = contractInfos.YHKXXSQ_BZ && contractInfos.YHKXXSQ_BZ.contractName;
                item = { ...viewContract0, ...item, ...contract, forceReadFlag: 'Y' };
              } else if (item.contractType === 'PAY_DEDUCT' && contract.contractType === 'PAY_DEDUCT') {
                item.custShowName = contractInfos.KHWTZFKK_BZ && contractInfos.KHWTZFKK_BZ.contractName;
                item = { ...viewContract1, ...item, ...contract, forceReadFlag: 'Y' };
              } else if (item.contractType === 'WITHHOLDUN' && contract.contractType === 'WITHHOLDUN') {
                item.custShowName = contractInfos.KHWTZFKK_LHD0B100 && contractInfos.KHWTZFKK_LHD0B100.contractName;
                item = { ...viewContract1, ...item, ...contract, forceReadFlag: 'Y' };
              }
            });

            if ((item.contractType === 'BANK_CARD_AUTH' || item.contractType === 'PAY_DEDUCT' || item.contractType === 'WITHHOLDUN') && !readOnce) {
              if (item.custShowName) {
                newChannels[index].list.push({
                  title: item.custShowName,
                  params: item
                });
              }
            }
            newChannels[index].readDuration = item.readDuration > newChannels[index].readDuration ? item.readDuration : newChannels[index].readDuration;
          } else if (!item.contractTypeFlag) {
            if (item.contractType === 'BANKCARDAUTH') {
              item.custShowName = contractInfos.YHKXXSQ_BZ && contractInfos.YHKXXSQ_BZ.contractName;
              item = { ...viewContract0, ...item };
            } else if (item.contractType === 'PAYWITHHOLD') {
              item.custShowName = contractInfos.KHWTZFKK_BZ && contractInfos.KHWTZFKK_BZ.contractName;
              item = { ...viewContract1, ...item };
            } else if (item.contractType === 'WITHHOLDUN') {
              item.custShowName = contractInfos.KHWTZFKK_LHD0B100 && contractInfos.KHWTZFKK_LHD0B100.contractName;
              item = { ...viewContract1, ...item };
            }
            if ((item.contractType === 'BANKCARDAUTH' || item.contractType === 'PAYWITHHOLD' || item.contractType === 'WITHHOLDUN') && !readOnce) {
              newChannels[index].list.push({
                title: item.custShowName,
                params: item
              });
            }
            newChannels[index].readDuration = item.readDuration > newChannels[index].readDuration ? item.readDuration : newChannels[index].readDuration;
          }
        }
      });
    });
    return newChannels;
  };

  // 强读点击确认或取消回调
  const handleSubmitClick = useCallback((value, item, index) => {
    if (value) {
      Madp.setStorageSync(
        'expressRepayContracts',
        JSON.stringify(item && item.contracts),
        storageType
      );
      setReadOnce(true);
      setRadioValue('select1');
      channelAgreementPlanCfgDtoList[index].showContract = false;
      channelAgreementPlanCfgDtoList[index].list = [];
      onChecked(value);
    } else {
      channelAgreementPlanCfgDtoList[index].showContract = false;
    }
    setChannelAgreementPlanCfgDtoList([...channelAgreementPlanCfgDtoList]);
  }, [channelAgreementPlanCfgDtoList, readOnce]);

  // 勾选协议按钮
  const handlerCheckboxClick = async (item) => {
    const cps = await getChannelParams();
    const capcdl = await transChannelParams(cps.data.channelAgreementPlanCfgDtoList);

    const newArrList = [];
    capcdl[0].list.forEach((data) => {
      item.contracts.forEach((contract) => {
        if (contract.contractType === data.params.contractType || (!contract.contractType && contract.contractCategory === data.params.contractCategory && contract.contractEdition === data.params.contractEdition)) {
          newArrList.push(data);
        }
      });
    });
    const haveForceFlag = newArrList.some((one) => one.params.forceReadFlag === 'Y');

    if (radioValue) { // 当前选中状态，置为未选中
      setRadioValue('');
      onChecked(false);
      Madp.setStorageSync(
        'expressRepayContracts',
        '',
        storageType
      );
    } else if (haveForceFlag && !readOnce) { // 需强读但未强读
      capcdl[0].list = newArrList;
      capcdl[0].showContract = true;
    } else { // 当前未选中，置为选中
      Madp.setStorageSync(
        'expressRepayContracts',
        JSON.stringify(item && item.contracts),
        storageType
      );
      setRadioValue('select1');
      onChecked(true);
    }
    setChannelAgreementPlanCfgDtoList(capcdl);
  };

  // 请求预览html
  const htmlFilePrivew = (contractCode, contractPreviewData) => {
    return Dispatch.repayment.queryContractInfo({
      scene: 'PREVIEW',
      interfaceVersion: '3.0',
      contractCode,
      contractPreviewData,
    }).then(res => {
      const { data } = res || {};
      const { contractList } = data || {};
      return contractList && contractList[0] && contractList[0].htmlFile;
    });
  };

  // 点击协议文字事件
  const clickProtocolAction = async (i, item = {}) => {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'ShowPagePreviewProtocal', target: this });
    const cps = await getChannelParams();
    const capcdl = await transChannelParams(cps.data.channelAgreementPlanCfgDtoList);
    const newArrList = [];
    capcdl[0].list.forEach((data) => {
      item.contracts && item.contracts.forEach((contract) => {
        if (contract.contractType === data.params.contractType || (!contract.contractType && contract.contractCategory === data.params.contractCategory && contract.contractEdition === data.params.contractEdition)) {
          newArrList.push(data);
        }
      });
    });
    const haveForceFlag = newArrList.some((one) => one.params.forceReadFlag === 'Y');
    let readDuration = 0;
    if (haveForceFlag && !readOnce && !radioValue) {
      readDuration = item.readDuration || 5;
    }
    const viewContract0 = await onContractClick({ url: '', text: '银行卡信息授权书', formData: true });
    // 委托代扣协议，分标准与0：100
    const viewContract1 = await onContractClick({ url: '', text: '', formData: true });
    const htmlFile0 = await htmlFilePrivew(contractInfos.YHKXXSQ_BZ.contractCode, viewContract0);
    const htmlFile1 = await htmlFilePrivew(contractInfos.KHWTZFKK_BZ.contractCode, viewContract1);
    let htmlFile2 = {};
    // 是资方才返回0：100配置
    if (capitalConstractNum) {
      htmlFile2 = await htmlFilePrivew(contractInfos.KHWTZFKK_LHD0B100.contractCode, viewContract1);
    }
    const contractList = [];
    if (item.contracts && item.contracts.length > 0) {
      item.contracts && item.contracts.forEach((contract) => {
        if (contract.contractType === 'BANK_CARD_AUTH' || contract.contractType === 'BANKCARDAUTH') {
          contractList.push({
            title: contractInfos.YHKXXSQ_BZ && contractInfos.YHKXXSQ_BZ.contractName,
            htmlFile: htmlFile0
          });
        }
        if (contract.contractType === 'PAY_DEDUCT' || contract.contractType === 'PAYWITHHOLD') {
          contractList.push({
            title: contractInfos.KHWTZFKK_BZ && contractInfos.KHWTZFKK_BZ.contractName,
            htmlFile: htmlFile1
          });
        }
        // 0:100
        if (capitalConstractNum && contract.contractType === 'WITHHOLDUN') {
          contractList.push({
            title: contractInfos.KHWTZFKK_LHD0B100 && contractInfos.KHWTZFKK_LHD0B100.contractName, // 合同组件预览展示的底部合同名称
            htmlFile: htmlFile2
          });
        }
      });
    }
    channelAgreementPlanCfgDtoList.forEach((contract, index) => {
      channelAgreementPlanCfgDtoList[index].showContract = true;
      channelAgreementPlanCfgDtoList[index].list = contractList;
      channelAgreementPlanCfgDtoList[index].readDuration = readDuration;
    });
    setChannelAgreementPlanCfgDtoList([...channelAgreementPlanCfgDtoList]);
  };

  useEffect(() => {
    getChannelParams()
      .then((cps) => cps.data && transChannelParams(cps.data.channelAgreementPlanCfgDtoList))
      .then((capcdl) => {
        setChannelAgreementPlanCfgDtoList(capcdl || []);
      });
  }, []);

  return (
    <MUView>
      {
        hasContract ? (
          <MUView className="repay-contract">
            {
              channelAgreementPlanCfgDtoList.map((item, index) => (
                <MUView className="repay-contract-container">
                  <MURadio
                    beaconId="ContractChecker"
                    className="repay-contract-container-radio"
                    type="row"
                    onClick={() => handlerCheckboxClick(item)}
                    value={radioValue}
                    options={[
                      {
                        type: 'protocal',
                        labelLeft: '我已阅读并同意',
                        linkText: item.contractText,
                        onLink: () => clickProtocolAction(index, item),
                        value: 'select1'
                      },
                    ]}
                  />
                  {!capitalConstractNum && showFPAYContract && (selectedCard || {}).forceSignFlag !== 'Y'
                    && <MUIcon beaconId="ContractIcon" className="repay-contract-icon" size="16" value="info" onClick={contractIconClick} />}
                  {
                    item.showContract && (
                      <AgreementDrawer
                        agreementViewProps={{
                          type: (item.contracts && item.contracts.length > 1) ? 1 : 2,
                          list: item.list,
                          current: 0,
                        }}
                        show={item.showContract}
                        close={() => handleSubmitClick(false, item, index)}
                        submit={() => handleSubmitClick(true, item, index)}
                        totalCount={item.readDuration}
                      />
                    )
                  }
                </MUView>
              ))
            }
          </MUView>
        ) : null
      }

      <RepayModal
        className="repay-contractModal"
        beaconId="FPAYContractModal"
        isOpened={showContractMUModal}
        closeOnClickOverlay
        onClose={onModalClose}
        confirmText="我已阅读并同意"
        onConfirm={onModalConfirm}
      >
        <MUView className="modal-content">
          <MUView>{`储蓄卡(${selectedCard.bankCardNoMask && selectedCard.bankCardNoMask.slice(-4)})代扣协议已失效，需重新签署`}</MUView>
          <MUView
            onClick={onContractClick}
            beaconId="MUModalContract"
            className="brand-text"
          >
            支付扣款协议
          </MUView>
        </MUView>
      </RepayModal>
    </MUView>

  );
}
