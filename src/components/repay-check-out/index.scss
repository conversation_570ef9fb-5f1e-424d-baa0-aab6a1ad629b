@import '~@mu/zui/dist/style/mixins/index.scss';
@import '~@mu/zui/dist/style/variables/default.scss';
@import '../../components/weapp/index.scss';

.repay-check-out-warper {
  .repay-check-out {
    display: flex;
    flex: 1;
    height: 100%;
    flex-direction: column;
    background: #f3f3f3;

    &-top {
      flex-direction: row;
      height: 170px;
      width: 100%;
      display: flex;
      // align-items: ;
      justify-content: center;
      background: #FFFFFF;
      z-index: 801;
      position: fixed;

      &-holder {
        padding-left: 36px;
        height: 32px;
        width: 32px;
      }

      &-title {
        margin-top: 30px;
        font-size: $font-size-h2;
        color: #333333;
        font-weight: 700;
        text-align: center;
        // line-height: 1;

        .inline-block {
          display: inline-block;
        }

        .amount-symbol {
          font-size: 40px;
          line-height: 40px;
        }

        .title-amount {
          font-size: 70px;
          padding-left: 10px;
          padding-right: 20px;
          line-height: 70px;
        }

        .edit-img {
          width: 36px;
          height: 36px;
        }

        .random-reduce {
          margin: 10px 0 6px;
          &__text {
            font-size: 20px;
            line-height: 20px;
            padding: 5px 20px;
            font-weight: 400;
            color: #FF8844;
            border: 1PX solid #FF8844;
            border-radius: 34px;
            position: relative;
          }
          &__text::after {
            content: "";
            position: absolute;
            width: 12px;
            height: 12px;
            border-right: 1PX solid #FF8844;
            border-top: 1PX solid #FF8844;
            left: 50%;
            margin-left: -8px;
            top: -8px;
            transform: rotate(-45deg);
            background-color: #fff;
            border-top-right-radius: 1PX;
            overflow: hidden;
          }
        }

        .court-cost-amt {
          margin: 6px 0;
          font-size: 28px;
          line-height: 28px;
          color: #a6a6a6;
          font-weight: 400;
        }

        .mask-name {
          margin: 14px 0 30px;
          height: 26px;
          font-size: 26px;
          color: #808080;
          line-height: 26px;
          text-align: center;
          font-weight: 400;
        }
      }

      &-close {
        display: block;
        height: 30px;
        width: 30px;
        position: absolute;
        top: 38px;
        right: 39px;
        z-index: 801;
      }

      &-margin-bottom {
        margin-bottom: 19px;
      }

      &-random-reduce {
        height: 230px;
      }
      
      &-court {
        height: 210px;
      }
    }

    &-content {
      z-index: 801;
      position: fixed;
      width: 100%;
      top: 170px;

      &--random {
        top: 230px;
      }
      &--court {
        top: 210px;
      }

      .overpay-row {
        border-top: 1px solid #f3f3f3;
      }
    }

    .transferGuide-content {
      height: 180px;
    }

    &-select-repay-way {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 30px;
      padding-right: 40px;
      font-size: 32px;
      color: #808080;
      position: relative;
      // z-index: 801;
      background-color: #fff;
      height: 120px;
      border-top: 1px solid #f3f3f3;
      border-bottom: 1px solid #f3f3f3;

      .transferGuide-icon {
        width: 15px;
        height: 26px;
        margin: auto;
        margin-right: 0;
        color: #cacaca;
      }
    }

    &-select-text {
      height: 48px;
      line-height: 48px;
      font-weight: 400;
      font-size: 32px;
      color: #333333;
      text-align: center;
    }

    &-select-button {
      display: flex;
      align-items: center;
      height: 100%;

      &-text {
        height: 32px;
        font-weight: 400;
        font-size: 32px;
        color: #333333;
        line-height: 32px;
        margin-left: 12px;
      }

      &-bank-image {
        width: $mu-list-thumb-size;
        height: $mu-list-thumb-size;
        display: block;
      }

      &-icon {
        width: 15px;
        height: 26px;
        display: block;
        color: #cacaca;
      }

      .transferGuide-text {
        margin-left: 30px;

        &-top {
          font-size: 32px;
          color: #333333;
          line-height: 48px;
          height: 48px;
          margin-top: 15px;
          margin-bottom: 2px;
        }

        &-bottom {
          font-size: 26px;
          line-height: 38px;
          height: 38px;
          margin-bottom: 17px;
        }

        .title-banner-content {
          // margin-top: 10px;
          font-size: 22px;
          color: #FF8844;
          line-height: 1.2;
          border: 1PX solid #FF8844;
          border-radius: 2px;
          padding: 4px;
          display: inline-block;
        }
      }
    }

    &__repay-way-tile {
      width: 100%;
      z-index: 799;
      position: fixed;
      overflow-y: scroll;
      top: 294px;
      max-height: 634px;
      &--random {
        top: 354px;
      }
      &--court {
        top: 334px;
      }
      &--contract {
        max-height: 592px;
      }
      &--transfer {
        max-height: 574px;
      }
    }

    &-space-line {
      margin-top: 20px;
    }

    &-bottom-content {
      position: fixed;
      bottom: 0;
      width: 100%;
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
      background: #fff;
      box-shadow: 0 -6px 6px 0 #0000000a;

      .transfer-text {
        margin-top: 34px;
        height: 26px;
        font-size: 26px;
        color: #808080;
        text-align: center;
        line-height: 26px;
      }

      .button-content {
        margin: 30px;
        .bottom-button {
          height: 100px;
        }
      }

      &-z-index {
        z-index: 802;
      }
    }

    .repay-contract {
      .repay-contract-container {
        display: flex;
        align-items: center;
        margin-bottom: -30px;

        &-radio {
          padding-left: 30px;
          padding-right: 30px;
        }

        .mu-contract-checker {
          padding-bottom: 0;
        }
      }

      &-icon {
        margin-right: 30px;
        color: #808080;
      }
    }

    &-wx-repay-dialog {
      z-index: 9999;

      &-icon {
        margin-top: 24px;
      }

      &-title {
        margin-top: 20px;
        color: #333333;
        font-weight: bold;
      }

      &-desc {
        margin-top: 10px;
        color: #808080;
        font-size: 26px;
      }

      &-highlight {
        color: #FF8844;
      }

      &-btn {
        margin-top: 25px;
        font-size: 32px;
      }

      &-countdown {
        margin-top: 25px;
        font-size: 28px;
        color: #A6A6A6;
      }
    }

  }


  .at-drawer__content {
    overflow: hidden;
    border-radius: 16px 16px 0 0;
  }

  .bottom-height {
    margin-bottom: 100px;
  }

  .visibility-hidden {
    visibility: hidden;
  }

  .mu-trade-password-keboard {
    .mu-dialog {
      z-index: 800;

      .mu-dialog__overlay {
        display: none;
      }

      .mu-dialog__container {
        border-radius: 0;
        
        .mu-dialog__content {
          .mu-trade-password {
            .mu-trade-password__close {
              display: none;
            }
          }
        }
      }
    }

    .mu-keyboard__row__key__fold {
      // visibility: hidden;
      pointer-events: none;

      span {
        display: none;
      }
    }

    .at-action-sheet {
      z-index: 800;
    }
  }

  .repay-check-out-repay-way-drawer {
    .repay-payway-list-add {
      background-color: #fff;
      margin-top: 20px;
    }

    .repay-payway-wxAliList {
      margin-top: 0;
    }

    .repay-payway-list-transferGuide {
      margin-top: 0;
    }

    &-normal-height {
      .drawer-content {
        height: 956px !important;
        height: calc(956px + constant(safe-area-inset-bottom)) !important;
        height: calc(956px + env(safe-area-inset-bottom)) !important;
      }
    }
  }

  .loading-dialog {
    .mu-dialog__overlay {
      background-color: rgba(0, 0, 0, 0.3);
    }
  }

  .overPayAmtDetail-modal {
    font-size: 28px;

    &-text {
      margin: 0 20px;
      text-align: center;
      word-wrap: break-word;
      font-family: 'PingFangSC-Regular';
    }

    .overPayAmtDetail-title {
      font-weight: 600;
      text-align: center;
      font-size: 38px;
      line-height: 38px;
      margin-bottom: 30px;
    }

    .overPayAmtDetail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      color: #808080;
      padding: 0 40px;
    }

    .overPayAmtDetail-but {
      margin-top: 30px;
      height: 98px;
      line-height: 98px;
      text-align: center;
      color: #3477FF;
      text-align: center;
      font-size: 36px;
      /* stylelint-disable-next-line */
      border-top: 1PX solid #ddd;
    }
  }
}

.repay-check-out-normal-height {
  .at-drawer {
    .at-drawer__content {
      // height: calc(#{$mu-keyboard-key-height} + 74px + #{$font-size-h1} + 68px + #{$font-size-list} + #{$mu-input-password-item-height} + 2 * #{$spacing-v-md} + 30px + #{$font-size-list} + 120px + 170px);
      height: 1088px !important;
      height: calc(1088px + constant(safe-area-inset-bottom)) !important;
      height: calc(1088px + env(safe-area-inset-bottom)) !important;
    }
  }
}

.repay-check-out-random-height {
  .at-drawer {
    .at-drawer__content {
      // height: calc(#{$mu-keyboard-key-height} + 74px + #{$font-size-h1} + 68px + #{$font-size-list} + #{$mu-input-password-item-height} + 2 * #{$spacing-v-md} + 30px + #{$font-size-list} + 120px + 170px);
      height: 1148px !important;
      height: calc(1148px + constant(safe-area-inset-bottom)) !important;
      height: calc(1148px + env(safe-area-inset-bottom)) !important;
    }
  }
}

.repay-check-out-court-height {
  .at-drawer {
    .at-drawer__content {
      // height: calc(#{$mu-keyboard-key-height} + 74px + #{$font-size-h1} + 68px + #{$font-size-list} + #{$mu-input-password-item-height} + 2 * #{$spacing-v-md} + 30px + #{$font-size-list} + 120px + 170px);
      height: 1128px !important;
      height: calc(1128px + constant(safe-area-inset-bottom)) !important;
      height: calc(1128px + env(safe-area-inset-bottom)) !important;
    }
  }
}