/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-nested-ternary */
/* eslint-disable max-len */
import {
  Component
} from '@tarojs/taro';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MUView,
  MUText,
  MUImage,
  MUButton,
  MUModal,
  MUDialog,
  MUIcon
} from '@mu/zui';
import Madp from '@mu/madp';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import {
  isMuapp,
  isWechat,
  isAlipay,
  Url,
  debounce,
  getCurrentPageUrlWithArgs,
  getWebviewName
} from '@mu/madp-utils';
import { MUSafeSmsCodeHalfWrap } from '@mu/safe-sms-shell';
import { MUTradePasswordEncryptedWrap } from '@mu/trade-password-encrypted-shell';
import Util from '@utils/maxin-util';
import { getIsYyjOrYjkLoan } from '@utils/getLoanMoreTips';
import CustomConfig from '@config/index';
import LoadingDialog from '@components/loading-dialog';
import RepayWay from '@components/repay-way/index';
import BottomDrawer from '@components/bottom-drawer/new-index';
import RepayModal from '@components/repay-modal/index';
import RepayContractModal from '@components/repay-contract-modal/index';
import RepayErrorDailog from '@components/repay-error-dailog/index';
import { getStore, setStore } from '@api/store';
import { OnePassOrSmsCodeWrap, BiomtOrPasswOrLivenWrap } from '@mu/trade-verify-shell';
import Dispatch from '@api/actions';
import { urlDomain } from '@utils/url_config';
import {
  commonSortBankList, getFundTransAmtDetails, commonSortNewBankList,
  commonGetOverPayAmtRepayParams, commonGetCommonRepayInfo,
  commonGetSetZFBOnly, isNeedSignSupplement, handleErrorCode,
  repayCheckOutInitDefaultCard, getIsHideWxAliRepay, isAliRepayAvailable, isWxRepayAvailable, getFeeInteRightInfo,
} from '@utils/repay-util';
import { getBusinessFullName, getLoginInfo } from '@mu/business-basic';
import PropTypes from 'prop-types';
import classNames from 'classnames';

import Contract from './components/contract';
import AutoRepayModal from '@components/modals/AutoRepayModal';
import {
  clipboardUrl, StandardService, miniProgramChannel
} from '@utils/constants';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

const themeColor = Util.getThemeColor(CustomConfig.theme);
const transferImg = 'https://file.mucfc.com/zlh/3/0/202305/202305182014223a4b9a.png';
const editImg = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828520342.png';
const icClose = 'https://file.mucfc.com/zlh/3/0/202305/202305182019591e0b87.png';
const BANKCARD_CANT_AFFORD_ALLOW_WECHAT_REPAY_INTERVAL = 30 * 60 * 1000; // “银行卡余额不足而允许微信支付方式”的标记有效期（30分钟）

export default class RepayCheckOut extends Component {
  static propTypes = {
    title: PropTypes.string,
    onClose: PropTypes.func,
    trackPrefix: PropTypes.string,
    randomReduceFlag: PropTypes.string,
    randomReduceText: PropTypes.string,
    interestTotalAmt: PropTypes.number,
    costTotalAmt: PropTypes.number,
    randomReduceSwitch: PropTypes.string,
    afterEditClick: PropTypes.func,
  }

  static defaultProps = {
    title: '',
    onClose: () => { },
    trackPrefix: 'repayment.expressRepay',
    randomReduceFlag: '',
    randomReduceText: '',
    interestTotalAmt: 0,
    costTotalAmt: 0,
    randomReduceSwitch: '',
    afterEditClick: () => {},
  }

  constructor(props) {
    super(props);
    this.courtOnly = Url.getParam('courtOnly'); // 仅剩法诉费，无其他账单
    this.billType = Url.getParam('billType') || '7days'; // 如果url上没有传billType,是渠道进来的,渠道的都走的是7days
    /** 前端自己生成了流水号。和后端自己的流水号不一样。不知道什么用了 */
    this.transRefNo = '';
    /** 选择的账单列表 */
    /** 交易密码校验组件返回的 token */
    this.pwdResultToken = '';
    /** 还款提交之后接口出参 */
    this.payResult = {};
    this.state = {
      openPwdTip: false,
      openSmsCodeTip: false,
      bankCards: [],
      hasOverdueFlag: false, // 选中的账单是否有逾期
      /** 选中待还账单是否有利息或费用 */
      showTradePwdDialog: false,
      showSmsCodeDialog: false,
      smsCodeToken: '', // 动码token
      clearAndDisableInput: false, // 控制是否情况密码输入框
      alipayOnly: false, // 仅支持支付宝支付
      repayWayNoticeConfig: {}, // 还款方式提醒弹窗配置
      showRepayCardContent: false, // 是否展示银行卡组件
      showFPAYContract: false, // 选择的银行卡需要签署支付扣款协议
      showContractMUModal: false, // 补签弹窗
      SignContract: false, // 提交还款之后激活的补签流程，补签后直接提交立即还款
      bankCardErrorStatusList: {}, // 自定义的银行卡错误状态列表，避免获取银行卡刷新掉自定义状态
      maskRealName: '', // 客户姓名掩码
      capitalConstractNum: null, // 需要补签的资方商户号
      selectBankTransferFlag: false, // 是否选中银行卡转账项目
      bankCardLimitSplitFlag: 'N', // 是否需要大额拆分。选中了优惠券不能拆分
      show: false,
      showErrorTipModal: false, // 是否打开支付失败提示模态框
      showTranseferModal: false, // 已预约还款弹窗
      showRepayWay: false,
      openTransferCouponTip: false, // 预约还款优惠券不可用提示弹窗
      setContractZIndex: false, // 是否需要设置底部容器的zindex，用于控制协议抽屉层级
      fullName: '招联消费金融股份有限公司',
      modeOfPayment: [], // 当前渠道支持的还款方式数组，['1','2','3','4']，1: "银行卡支付"，2: "支付宝支付"，3: "微信支付"，4: "银行卡转账还款"
      firstRepayWay: '', // 优先展示的支付方式
      curShowTradePwdDialog: false, // 当前密码弹窗是否打开
      curShowSmsCodeDialog: false, // 当前动码弹窗是否打开
      bankcardOpen: false, // 银行卡展开
      isCantAffordDialogOpened: false, // 余额不足提示框是否打开
      cantAffordDialogCountdown: 3, // 余额不足提示框倒计时
      cantAffordDialogBankcardName: '', // 余额不足提示框银行卡名称
      cantAffordDialogHintWxRepay: false, // 余额不足提示框是否需强调新增了微信支付方式
      showWxPayOnBcCantAfford: false, // 当前渠道是否允许击中“余额不足”时展示微信支付方式
      openWaitOffersTipModal: false, // 存在待激活的息费减免优惠提示弹窗
      openAvoidDupPayModal: false, // 长时间还款批扣处理弹窗
      assignLoadingTitle: '', // 在途取消支付轮询需指定轮询标题
      errCode: '' // 提交还款后的错误码
    };
    /** 账单涉及资方信息(0:100类型的资方)对象。{ "10000": 13123 } */
    this.selectedBillCapitals = {};
    this.alipayWayInfo = {
      isAlipay: true,
      bankCardId: 'alipay',
      bankOrg: {
        bankName: '支付宝还款',
        bankImage: 'https://file.mucfc.com/ebn/3/18/2023010/202310121438282ab9c7.png',
        bankCode: '2088'
      }
    };
    // 微信支付信息
    this.wxPayWayInfo = {
      isWxPay: true,
      bankCardId: 'wxpay',
      bankOrg: {
        bankName: '微信还款',
        bankImage: 'https://file.mucfc.com/ebn/3/18/2023010/202310121438282331af.png',
        bankCode: '2089' // 交易给出
      }
    };
    this.transferGuideInfo = {
      istTransferGuide: true,
      bankCardId: 'transferGuide',
      bankOrg: {
        bankName: '银行卡转账还款',
        bankCode: 'transfer' // 自定义
      }
    };
    /** fix: 存在重复调用还款接口问题 */
    this.debounceApi = debounce((bo) => {
      this.immediatePayApi(bo);
    }, 2000, {
      leading: true, // 指定调用在节流开始前
      trailing: false
    });
    /** 需要大额拆分的银行卡 */
    this.overLimitCards = [];
    this.bankList = []; // 接口回来的银行卡数据
    this.contractInfos = {}; // 接口回来的合同参数
    this.selectedBillAmountList = []; // 查询银行卡入参
    this.isCheckedContract = false; // 是否勾选了协议

    this.selectedCoupon = {};
    this.totalWaiveAmt = 0;
    this.transTotalAmt = 0;
    this.totalCanPayAmt = 0;
    this.settleWaiveAmt = 0;
    this.repayDetailList = [];
    this.repayTrialDetailList = [];
    this.repayMode = '';
    this.is0100Bills = '';
    // this.isFirstEnter = true;
    this.queryDialog = {
      show: () => { },
      hide: () => { },
    };
    this.showLoadingDialog = false;
    this.finishLoading = false;
    this.isFirstEnterCheckOut = true; // 是否第一次进入收银台
    this.isOverDueUserFlag = false; // 是否是逾期客户，只针对逾期3天以上的客户
    this.isDueTagCust = 'N'; // 是否为打标用户
    this.callbackUrl = '';
    this.transSeqno = '';
    this.imGoOutDone = false; // im场景的特殊处理参数，防止muDrawer关闭事件多次调用导致页面多次跳转
    this.zfbPayGoBackNeedToResult = false; // 是否h5页面的支付宝支付完毕，这个情况需要回跳结果页
    this.wxPayGoBackNeedToResult = false; // 是否h5页面的微信支付，这个情况需要回跳结果页
    this.needReChooseCard = false; // 提交还款提示卡状态异常时，标识是否需要重新选卡
    this.useTileShow = true; // 由App渠道放开到所有渠道
    this.hasSentWxPayAvailablebeacon = false; // 是否已发送过“支付方式包含微信”埋点
    this.applyNo = Url.getParam('applyNo') || ''; // 贷后（延后还）案件号
    this.supportZFBCardChannelFlag = false; // 支持引导从支付宝选卡渠道技参
    this.supportOneBindCardChannelFlag = false; // 支持引导一键绑卡渠道技参配置
    this.onRepayWayClick = this.onRepayWayClick.bind(this);
    this.confirmedWaitOffersTip = false; // 是否确认过在途息费减免弹窗
    this.newScene = ''; // 还款场景
    this.repaymentFlag = Url.getParam('repaymentFlag') || ''; // 是否从还款模块跳入标识
    this.loginInfoHash = {}; // 用户信息 hash
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
    this.custId = '';
    this.userId = '';
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  async componentDidMount() {
    await this.getLoginInfo();
    await this.initUserInfor();
    // 这里查全部待还借据是为了结果页的一个弹窗显示，需求：催收保卫战
    this.getNewFullName();
    this.setMaskRealName();
    this.transRefNo = this.transRefNo || Util.getTransRefNo();
    // 获取是否为一元借或者一口借借据信息
    const { selectedBillList } = this.props || {};
    this.isYyjOrYjkLoan = getIsYyjOrYjkLoan(selectedBillList);
  }

  getLoginInfo = async () => {
    const { userIdHash, custIdHash } = await getLoginInfo() || {};
    this.loginInfoHash = { userHashNo: userIdHash, custHashNo: custIdHash };
  }

  async initUserInfor() {
    // 多接口用到用户id，存储在this中
    const { basicCustDto } = getStore('sessionInfo') || {};
    let { custId, userId } = basicCustDto || {};
    if (!custId) {
      const { basicCustDto: newBasicCustDto } = await Dispatch.repayment.getNewUserInfo();
      const { custId: newCustId, userId: newUserId } = newBasicCustDto || {};
      custId = newCustId || '';
      userId = newUserId || '';
    }
    this.custId = custId;
    this.userId = userId;
  }

  /**
  * 密码输入错误次数埋点
  * @param { num } 剩余输入次数
  */
  verifyFailedNum(num) {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `PasswordError0${5 - num}` });
  }

  getNewFullName = async () => {
    try {
      const res = await getBusinessFullName();
      const { data = '招联消费金融股份有限公司' } = res || {};
      this.setState({ fullName: data });
    } catch (error) {
    }
  }

  // 这个方法目前仅针对h5环境下的微信支付跳转，走h5的是联通和招行渠道
  async needToLoading() {
    if (process.env.TARO_ENV !== 'h5') {
      return null;
    }
    const payResult = this.payResult && Object.keys(this.payResult).length > 0 ? this.payResult : Madp.getStorageSync('payResult', 'LOCAL') || {};
    if (this.zfbPayGoBackNeedToResult || this.wxPayGoBackNeedToResult || (payResult && Object.keys(payResult).length > 0 && payResult.transSeqno)) {
      const repayConfig = await Dispatch.repayment.getCommonConfig('repayPayment.config');
      this.repayConfig = repayConfig;
      this.zfbPayGoBackNeedToResult = false;
      this.wxPayGoBackNeedToResult = false;
      this.payResult = payResult;
      Madp.removeStorageSync('wxNeedToLoading', 'LOCAL');
      Madp.removeStorageSync('zfbNeedToLoading', 'LOCAL');
      Madp.removeStorageSync('payResult', 'LOCAL');
      this.onPayResultJump('success');
    }
  }

  /**
   *  设置用户掩码名字
   */
  async setMaskRealName() {
    const { custName = '' } = await getLoginInfo() || {};
    this.setState({ maskRealName: custName });
  }

  async initBankCardList() {
    // setZFBOnly中可能有Abtest逻辑，会影响展示，故setZFBOnly完再display
    this.setZFBOnly();
    await this.queryBankCardInfo();
    this.setState({ showRepayCardContent: true });
  }

  initPage(noPreSubmit = false) {
    this.initSelectCard();
    this.initPageShow(noPreSubmit);
  }

  initSelectCard() {
    const { bankCardErrorStatusList, modeOfPayment, firstRepayWay } = this.state;
    const { alipayOnly } = this.state;
    const { isOverDueUserFlag } = this;
    const isHideWxAliRepay = getIsHideWxAliRepay();
    const aliRepayAvailable = isAliRepayAvailable(
      alipayOnly, this.closeWxAlipayFromCC, this.closeAlipay0100BillFlag, isOverDueUserFlag, this.isDueTagCust === 'Y', false, true, isHideWxAliRepay, modeOfPayment
    );
    const wxRepayAvailable = isWxRepayAvailable(
      this.closeWxAlipayFromCC, isOverDueUserFlag, this.isDueTagCust === 'Y', false, true, isHideWxAliRepay, modeOfPayment
    );
    if (!this.hasSentWxPayAvailablebeacon && wxRepayAvailable) {
      dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.WxRepayAvailable` });
      this.hasSentWxPayAvailablebeacon = true;
    }
    // const isFirstEnter = (getStore('shouldReselectCard') || '') !== '1';
    const initCard = repayCheckOutInitDefaultCard(
      this.bankCards, bankCardErrorStatusList, true, alipayOnly,
      this.is0100Bills, false, aliRepayAvailable, wxRepayAvailable, modeOfPayment, firstRepayWay
    );
    if (this.justNeedInitCard) {
      this.justNeedInitCard = false;
      return initCard;
    }
    // 用完就销毁
    setStore({ shouldReselectCard: '' });
    if (!initCard) { // 又没卡又不能用支付宝和微信，拉起支付方式让用户自己选
      setStore({
        selectedCard: {}
      });
      if (!this.useTileShow) {
        this.setState({ showRepayWay: true });
      } else {
        this.setState({ bankcardOpen: true });
      }
    } else if (initCard && initCard.needSelectCard) {
      setStore({
        selectedCard: initCard.needSelectCard
      });
    } else {
      if (initCard.needSelectCardType === 'aliPay') {
        setStore({
          selectedCard: this.alipayWayInfo
        });
      }
      if (initCard.needSelectCardType === 'wxpay') {
        setStore({
          selectedCard: this.wxPayWayInfo
        });
      }
      if (initCard.needSelectCardType === 'transfer') {
        setStore({
          selectedCard: this.transferGuideInfo
        });
      }
    }
    // 刷新下页面
    this.setState({});
  }

  initPageShow(noPreSubmit = false) {
    const { alipayOnly, modeOfPayment } = this.state;
    const { isOverDueUserFlag } = this;
    const isHideWxAliRepay = getIsHideWxAliRepay();
    const selectCard = getStore('selectedCard');
    const { istTransferGuide, showFPAYContract, capitalConstractNum, bankCardLimitSplitFlag } = isNeedSignSupplement(selectCard || {}, this.selectedBillCapitals);
    if (istTransferGuide && this.selectedCoupon && this.selectedCoupon.awardType && this.selectedCoupon.awardType !== '216') {
      this.setState({ openTransferCouponTip: true });
    }
    this.setState({
      showFPAYContract,
      capitalConstractNum,
      selectBankTransferFlag: istTransferGuide,
      bankCardLimitSplitFlag
    }, () => {
      if (noPreSubmit || selectCard.isAlipay || selectCard.isWxPay || selectCard.istTransferGuide || showFPAYContract) {
        // 关闭密码组件
        if (this.useTranComponentSign) {
          this.biomtOrPasswOrLivenRef.handleTpClose();
          this.setState({
            curShowTradePwdDialog: false,
          });
        } else {
          this.setState({
            showTradePwdDialog: false,
            clearAndDisableInput: true, // 清除密码残留
            curShowTradePwdDialog: false,
          });
        }
        this.setState({
          setContractZIndex: true
        });
        return null;
      }
      // 没有补签协议或者微光卡协议就直接拉起密码组件
      const payNeedBreak = Madp.getStorageSync('payNeedBreak', 'LOCAL');
      if (!showFPAYContract) {
        if ((!this.useTileShow || (this.useTileShow && !isAliRepayAvailable(
          alipayOnly, this.closeWxAlipayFromCC, this.closeAlipay0100BillFlag, isOverDueUserFlag, this.isDueTagCust === 'Y', false, true, isHideWxAliRepay, modeOfPayment
        ))) && payNeedBreak !== '1') {
          this.preSubmit();
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${this.props.trackPrefix}.AutoRepayButton`,
            beaconContent: { cus: { expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay, awardNo: (this.selectedCoupon || {}).awardNo, repayWay: (selectCard || {}).bankCardId, repayAmount: this.actualRepayAmt } }
          });
        }
      }
      Madp.removeStorageSync('payNeedBreak', 'LOCAL');
    });
  }

  /**
   * @description: 根据展位数据和cc配置决定是否只能使用支付宝
   */
  setZFBOnly() {
    const { bankCardErrorStatusList } = this.state;
    const {
      alipayOnly, repayWayNoticeConfig
    } = commonGetSetZFBOnly(
      this.repayConfig, bankCardErrorStatusList, Object.keys(this.selectedBillCapitals),
      this.getZFBSwitchData()
    );
    this.setState({ alipayOnly, repayWayNoticeConfig });

    if (alipayOnly) {
      this.bankCards = [];
      setStore({
        bankCards: this.bankCards,
        selectedCard: this.alipayWayInfo
      });
      this.setState({
        bankCards: this.bankCards,
      });
      dispatchTrackEvent({ event: EventTypes.PO, beaconId: `${this.props.trackPrefix}.ShowAlipayOnly` });
    } else {
      dispatchTrackEvent({ event: EventTypes.PO, beaconId: `${this.props.trackPrefix}.ShowCommon` });
    }
  }

  /**
   * @description: 获取虚拟展位的数据，判断用户是否仅能使用支付宝
   */
  getZFBSwitchData() {
    const { dataObj = {} } = this.virtualFuncSwitch;
    const { contentList = [] } = dataObj;
    const onlyAlipayStore = getStore('onlyAlipay');
    // 支付宝还款小程序入口只支持支付宝支付
    if (onlyAlipayStore) {
      return true;
    } else if (Object.keys(this.virtualFuncSwitch).length && Object.keys(contentList).length) {
      dispatchTrackEvent({
        event: EventTypes.EV,
        beaconId: `${this.props.trackPrefix}.LimitContentList`,
        beaconContent: { cus: dataObj }
      });
      return true;
    } else {
      dispatchTrackEvent({
        event: EventTypes.EV,
        beaconId: `${this.props.trackPrefix}.NoVirtualFuncSwitch`,
        beaconContent: { cus: dataObj }
      });
      return false;
    }
  }

  async queryBankCardInfo() {
    const { alipayOnly } = this.state;
    // 仅支持支付宝时不需要获取银行卡
    if (alipayOnly) {
      this.bankCards = [];
      this.setState({
        bankCards: this.bankCards,
      });
      return;
    }
    const needPayAcount = this.actualRepayAmt;
    dispatchTrackEvent({
      event: EventTypes.EV,
      beaconId: `${this.props.trackPrefix}.QueryBankCard`,
      beaconContent: { cus: { amount: needPayAcount } }
    });

    const bill0100Flag = this.is0100Bills;
    // 进收银台之前一定会试算
    const { selectedBillAmountList } = getFundTransAmtDetails(true, bill0100Flag, this.repayTrialDetailList, this.repayDetailList);
    this.selectedBillAmountList = selectedBillAmountList;

    const { bankCardInfoList, contractInfos } = await Dispatch.repayment.getBankCardsList({
      transAmt: String(needPayAcount),
      fundTransAmtDetails: this.selectedBillAmountList
    });
    // 接口银行卡数据
    this.bankList = bankCardInfoList || [];
    // 合同参数
    this.contractInfos = this.handleContractInfos(contractInfos) || {};

    // 初始化时先将被限制的卡开放
    this.setOverLimitCardStatus(false);

    // 银行卡排序 非0:100 0:100的
    if (this.is0100Bills === 'only0100Bill' || this.is0100Bills === 'minxBill') {
      this.sortNewBankList();
    } else if (this.is0100Bills === 'normalBill') {
      this.sortBankList();
      // 试算后如果是非0:100 不选券的话还要走一次
      const coupon = this.selectedCoupon || {};
      if (!coupon.awardNo) {
        // 限制的卡需要放开
        this.setOverLimitCardStatus(false);
      }
    }

    this.setState({
      bankCards: this.bankCards,
    });
  }

  // 处理银行卡返回的合同配置包： 将数组转成key-value对象，方便直接读取，不用每次读取时遍历
  handleContractInfos = (contractInfos = []) => {
    if ((contractInfos || []).length === 0) {
      return {};
    }
    let fomatContractInfos = {};
    contractInfos.forEach((contractItem) => {
      fomatContractInfos[contractItem.contractCode] = contractItem;
    });
    return fomatContractInfos;
  }

  /**
   * @description: 成功选中优惠券后。如果选中的银行卡超限需拆分，则该卡不可使用并重新选中银行卡。取消优惠券再该卡恢复可用
   * @param { Boolean } useCopon 选中优惠券，默认true
   */
  setOverLimitCardStatus(useCopon = true) {
    const { bankCardErrorStatusList } = this.state;
    const limitCards = this.overLimitCards;
    if (!limitCards.length) return;
    limitCards.forEach((bankCardId, index) => {
      if (useCopon) {
        // 选中优惠券
        this.setSelectCardsStatus(bankCardId, '暂不支持还款', index === (limitCards.length - 1), false, 'limit');
      } else {
        // 取消优惠券
        const failCard = bankCardErrorStatusList[bankCardId];

        if (!failCard) return;
        const limitIndex = failCard.reasonList.indexOf('limit');
        // 删除不可用原因：limit
        failCard.reasonList.splice(limitIndex, 1);
        // 没有其他原因了，就可以使用该卡
        if (!failCard.reasonList.length) {
          delete bankCardErrorStatusList[bankCardId];
        }
        this.setState({
          bankCardErrorStatusList
        });
      }
    });
  }

  sortBankList() {
    const originalCards = getStore('bankCards');
    const coupon = this.selectedCoupon || {};
    this.overLimitCards = [];
    // 收银台一定会走试算
    const {
      overLimitCards,
      bankCards,
    } = commonSortBankList(originalCards, coupon, this.selectedBillAmountList, (a, b, c, d, e) => this.setSelectCardsStatus(a, b, c, d, e), this.billType);
    this.overLimitCards = overLimitCards;
    this.bankCards = bankCards;
  }

  // 0:100 银行卡需求 排序
  sortNewBankList() {
    const coupon = this.selectedCoupon || {};
    // 收银台一定会走试算
    const { cardTempList } = commonSortNewBankList(this.bankList, coupon, this.selectedBillAmountList, (a, b, c, d, e) => this.setSelectCardsStatus(a, b, c, d, e), this.is0100Bills);
    this.bankCards = cardTempList;
  }

  // 展示收银台
  async show(param) {
    this.repayConfig = param.repayConfig || {};
    this.selectedCoupon = param.selectedCoupon || {};
    this.totalWaiveAmt = param.totalWaiveAmt || 0;
    this.transTotalAmt = param.transTotalAmt || 0;
    this.totalCanPayAmt = param.totalCanPayAmt || 0;
    this.actualRepayAmt = param.actualRepayAmt || 0;
    this.settleWaiveAmt = param.settleWaiveAmt || 0;
    this.repayDetailList = param.repayDetailList || [];
    this.repayMode = param.repayMode;
    this.is0100Bills = param.is0100Bills;
    this.virtualFuncSwitch = param.virtualFuncSwitch;
    this.couponObject = param.couponObject;
    this.repayTrialDetailList = param.repayTrialDetailList;
    this.editable = param.editable;
    this.expressScene = param.expressScene || '11';
    this.directCallCounter = param.directCallCounter;
    this.selectedBillCapitals = param.selectedBillCapitals;
    this.isOverDueUserFlag = param.isOverDueUserFlag;
    this.isDueTagCust = param.isDueTagCust;
    this.callbackUrl = param.callbackUrl;
    this.courtCostAmt = param.courtCostAmt;
    this.specialRepayScene = param.specialRepayScene;
    this.extendRepayQualificationCoupon = param.extendRepayQualificationCoupon;
    this.extendRepayFutureCoupon = param.extendRepayFutureCoupon;
    this.prepayFeeRightsObject = param.prepayFeeRightsObject;
    this.overPayAmt = param.overPayAmt;
    this.remitTotalAmount = param.remitTotalAmount;
    this.preRepayAmt = param.preRepayAmt;
    this.availableAmount = param.availableAmount;
    this.closeAlipay0100BillFlag = param.closeAlipay0100BillFlag;
    this.overPayAmtRepayFlag = param.overPayAmtRepayFlag;
    this.xiaozhaoAmount = param.xiaozhaoAmount;
    this.preRepayUsedAmount = param.preRepayUsedAmount;
    this.newScene = param.newScene;
    this.repaySence = param.repaySence;
    if (this.editable) {
      dispatchTrackEvent({
        event: EventTypes.SO,
        beaconId: `${this.props.trackPrefix}.CheckOutCanEditable`,
        beaconContent: { cus: { expressScene: this.expressScene, scene: this.billType === 'advanced-stage' ? 'SCENE_SXF' : '', newScene: this.newScene } },
      });
    }

    this.setState({
      show: true,
      clearAndDisableInput: false,
    }, async () => {
      await this.getModeOfPayment();
      dispatchTrackEvent({
        event: EventTypes.EV,
        beaconId: `${this.props.trackPrefix}.RepayCheckOutOpen`,
        beaconContent: { cus: { expressScene: this.expressScene, scene: this.billType === 'advanced-stage' ? 'SCENE_SXF' : '' } },
      });
      await this.initBankCardList();
      if (this.isFirstEnterCheckOut) {
        this.isFirstEnterCheckOut = false;
        dispatchTrackEvent({
          event: EventTypes.EV,
          beaconId: `${this.props.trackPrefix}.${this.directCallCounter ? 'directEnterCheckOut' : 'notDirectEnterCheckOut'}`,
          beaconContent: { cus: { expressScene: this.expressScene, scene: this.billType === 'advanced-stage' ? 'SCENE_SXF' : '' } },
        });
      }
      const { bankCardErrorStatusList } = this.state;
      // 没有可用银行卡的时候直接拉起支付方式让用户选择
      if (!this.bankList.length || this.bankList.length === 0 || Object.keys(bankCardErrorStatusList).length === this.bankList.length) {
        dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.noDefaultBankCard` });
        this.initPage();
      } else { // 有银行卡的情况下为用户自动选择
        dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.haveDefaultBankCard` });
        this.initPage();
      }
    });
  }

  // 关闭收银台
  hide() {
    const {
      onClose
    } = this.props;
    onClose();
    dispatchTrackEvent({
      event: EventTypes.EV,
      beaconId: `${this.props.trackPrefix}.RepayCheckOutClose`,
      beaconContent: { cus: { expressScene: this.expressScene, scene: this.billType === 'advanced-stage' ? 'SCENE_SXF' : '' } },
    });
    // im场景关闭收银台需要回到im页面
    if (this.expressScene === '31' && !this.imGoOutDone) {
      this.imGoOutDone = true;
      if (this.callbackUrl) {
        Util.router.replace(this.imCallbackUrl);
      }
    }
    this.setState({
      show: false,
      showRepayWay: false,
      clearAndDisableInput: true,
    });
  }

  /**
   * 返回展位中对于每一种还款方式的优惠信息配置
   */
  get ledaBankConfig() {
    // 展位数据
    const { virtualBankTag } = this.props || {};
    // 展位配置的支付方式优惠信息
    const config = {};
    if (!virtualBankTag) return config;
    if (Object.keys(virtualBankTag).length) {
      virtualBankTag.dataObj.contentList.forEach((item) => {
        config[item.bankOrg] = item.title;
      });
    }
    // 银行卡转账选项的优惠文案，由前端写死，根据金额判断展示,只在0APP展示
    if (Number(this.totalCanPayAmt) > 50000
      && Madp.getChannel() === '0APP'
      && this.repayConfig.transferChannels
      && this.repayConfig.transferChannels.indexOf('0APP') > -1) {
      config.transfer = 'Y';
    }
    return config;
  }

  async onTransferGuide(go) {
    const urlParam = {
      path: '/pages/transfer/guide',
      query: {
        reserveType: this.billType === '7days' ? '02' : '03',
        amount: this.transferAmt,
      }
    };
    if (go) {
      dispatchTrackEvent({
        event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.GoToReservation`, beaconContent: { cus: { reserved: '1', expressScene: this.expressScene, newScene: this.newScene, overDueDay: this.overdueDay } }
      });
      Util.router.push(urlParam);
      return;
    }
    const { reservationAmt } = await Dispatch.repayment.queryReserveState({ custNo: this.custId });
    if (reservationAmt === null || go) { // 未预约
      dispatchTrackEvent({
        event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.GoToReservation`, beaconContent: { cus: { reserved: '0', expressScene: this.expressScene, newScene: this.newScene, overDueDay: this.overdueDay } }
      });
      Util.router.push(urlParam);
    } else {
      dispatchTrackEvent({
        event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.GoToReservation`, beaconContent: { cus: { reserved: '1', expressScene: this.expressScene, newScene: this.newScene, overDueDay: this.overdueDay } }
      });
      this.setState({ showTranseferModal: true });
    }
  }

  onAddBankCard() {
    this.storeExternalFillData();
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.AddBankCard`, beaconContent: { cus: { expressScene: this.expressScene, newScene: this.newScene, overDueDay: this.overdueDay } } });
    const currentUrl = process.env.TARO_ENV === 'h5' ? getCurrentPageUrlWithArgs() : `/${getCurrentPageUrlWithArgs()}`;
    const param = {
      redirectUrl: 'goBack',
      backDelta: '1',
      action: 'repayment',
      returnUrl: currentUrl, // 跳转方法会将参数encode，去掉错误的多余encode
    };
    // 只有0:100借据还款时，添加银行卡先签约资方通道
    const selectedBillCapitalsList = Object.keys(this.selectedBillCapitals);
    if (selectedBillCapitalsList.length === 1 && selectedBillCapitalsList[0] !== '10000') {
      param.fundMerchantNo = selectedBillCapitalsList[0];
    }
    Util.externalJump('ADD_BANK_CARD', param);
  }

  userCardSelect(card, isUserClick = false) {
    this.setState({ showRepayWay: false });
    setStore({
      selectedCard: card,
    });
    this.initPageShow();
    if ((card || {}).istTransferGuide) {
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.BankCardTransfer`, beaconContent: { cus: { expressScene: this.expressScene, newScene: this.newScene, overDueDay: this.overdueDay } } });
    }
  }

  /**
   * 动码验证通过。或动码豁免
   * 立即还款之前的补签，下一步是验证交易密码；
   * 立即还款之后的补签，下一步是再次提交立即还款。
   */
  async onSmsVerifyOk(token) {
    const { SignContract, capitalConstractNum } = this.state;
    // 补签成功之后重新获取下银行卡,更新下这张卡的状态
    // 用户补签完成后更新下银行卡信息
    await this.queryBankCardInfo();
    const selectedCard = getStore('selectedCard');
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.AuthCodeSuccess`, beaconContent: { cus: { scene: (selectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER'), expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay } } });
    const afterFPAYCardList = this.bankCards.filter(item => item.bankCardNo === selectedCard.bankCardNo);
    setStore({
      selectedCard: afterFPAYCardList[0]
    });
    this.setState({
      showSmsCodeDialog: false,
      showFPAYContract: false,
      curShowSmsCodeDialog: false
    });
    if (token) {
      this.setState({ smsCodeToken: token });
    }
    if (SignContract || CustomConfig.payBySms) { // 渠道支持动码还款
      this.debounceApi(SignContract);
      this.setState({
        SignContract: false
      });
    } else if (this.useTranComponentSign) {
      dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.NeedPassword`, beaconContent: { cus: { expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay } } });
      this.biomtOrPasswOrLivenRef.startProcess();
      this.setState({
        curShowTradePwdDialog: true,
        setContractZIndex: false,
      });
    } else {
      dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.NeedPassword`, beaconContent: { cus: { expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay } } });
      this.setState({
        showTradePwdDialog: true,
        clearAndDisableInput: false,
        curShowTradePwdDialog: true,
        setContractZIndex: false,
      });
    }
  }

  /** 补签失败处理 */
  smsVerifyFail() {
  }

  openIconModal() {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.clickTipModal`, });
    this.setState({
      openSmsCodeTip: true
    });
  }

  closeIconModal() {
    this.setState({
      openSmsCodeTip: false
    });
  }

  openPwdIconModal() {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.clickPwdTipModal`, });
    this.setState({
      openPwdTip: true
    });
  }

  closePwdIconModal() {
    this.setState({
      openPwdTip: false
    });
  }

  goChangePhoneNum() {
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.ResetPhone` });
    this.storeExternalFillData();
    Util.externalJump('RESET_PHONE_URL');
  }

  storeExternalFillData() {
    const { onSaveExternalFillData } = this.props;
    onSaveExternalFillData && onSaveExternalFillData();
  }

  // 交易组件，回跳地址方式的结果处理。（优惠券、银行卡、金额设置，恢复原状）
  checkBioResult() {
    if (Madp.getChannel() !== '3CMBAPP' || process.env.TARO_ENV !== 'h5') return;
    const resultToken = Url.getParam('token');
    const bioResult = Url.getParam('bioResult') === 'pass';
    if (resultToken && bioResult) {
      this.onPasswordOk(resultToken);
    }
  }

  async onPasswordOk(token) {
    dispatchTrackEvent({
      event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.PasswordSuccess`, beaconContent: { cus: { result: '1', repayMode: this.specialRepayScene === 'consult' ? 'consultRepay' : '', expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay } }
    });
    this.pwdResultToken = token;
    if (!this.useTranComponentSign) {
      this.setState({
        showTradePwdDialog: false,
        clearAndDisableInput: true, // 清除密码残留
      });
    }
    await this.debounceApi();
  }

  onPasswordClose() {
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.ClosedPassword` });
    this.setState({
      showTradePwdDialog: false,
    });
  }

  onNeedModifyPass() {
    const that = this;
    Madp.showModal({
      title: '升级6位数字交易密码',
      content: '为提供更便捷的支付体验，特邀您升级为6位数字的交易密码',
      confirmText: '立即升级',
      confirmColor: themeColor,
      showCancel: false,
      success(res) {
        if (res.confirm) {
          that.onForgotPass();
        }
      }
    });
  }

  onForgotPass() {
    this.storeExternalFillData();
    Util.externalJump('SET_PWD');
  }

  signInContract() {
    this.setState({
      showContractMUModal: false,
      SignContract: true
    });
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.AgreeContract` });
    this.initToken('SCENE_SUPPLEMENT');
  }

  /**
   * 准备输入交易密码
   * 首先判断是否存在交易密码，若无则提示，若有则出现交易密码输入框和键盘
   */
  async preSubmit() {
    const { alipayOnly, showFPAYContract, bankCardLimitSplitFlag } = this.state;
    const selectedCard = getStore('selectedCard');
    dispatchTrackEvent({
      event: EventTypes.EV,
      beaconId: `${this.props.trackPrefix}.RepayButtonFinish`,
      beaconContent: { cus: { expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay, awardNo: (this.selectedCoupon || {}).awardNo, repayWay: (selectedCard || {}).bankCardId, repayAmount: this.actualRepayAmt } }
    });
    const { isCheckedContract } = this;
    if (alipayOnly) dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.SubmitAlipayOnly` });
    else dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.${showFPAYContract ? 'SubmitWithContract' : 'SubmitCommon'}` });
    dispatchTrackEvent({
      event: EventTypes.EV,
      beaconId: Number(this.courtCostAmt) > 0 || this.courtOnly === '1' ? `${this.props.trackPrefix}.SubmitWithCourtCost` : `${this.props.trackPrefix}.SubmitWithoutCourtCost`,
    });
    if (selectedCard.isWxPay) {
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.SubmitWxPay` });
    }
    if (showFPAYContract && !isCheckedContract) {
      Madp.showToast({
        title: '请勾选支付扣款协议',
        icon: 'none'
      });
      return;
    }
    if (!Object.keys(selectedCard).length) {
      Madp.showToast({
        title: '请选择支付方式',
        icon: 'none'
      });
      return;
    }
    if ((!this.selectedCoupon || !this.selectedCoupon.awardNo) && bankCardLimitSplitFlag === 'Y') {
      Madp.showModal({
        title: '提示',
        content: '由于您的银行卡设置了单笔支付限额，本次还款可能会分成多笔扣款，请保证银行卡余额充足，以免扣款不足导致还款失败',
        confirmText: '继续还款',
        confirmColor: themeColor,
        cancelText: '换一张卡',
        success: async (res) => {
          if (res.confirm) {
            await this.beforePayCheck();
          } else if (res.cancel) {
            if (!this.useTileShow) {
              this.setState({ showRepayWay: true });
            } else {
              this.setState({ bankcardOpen: true });
            }
          }
        }
      });
    } else {
      await this.beforePayCheck();
    }
  }

  /** 还款支付前置检查, 特殊接口把ret判断逻辑拿出fetch.js */
  async beforePayCheck() {
    const { ret, errMsg, errCode, data } = await Dispatch.repayment.RepayPretreatment();
    if (ret === '0') {
      if (data && data.refuseCodeList && data.refuseCodeList[0].desc) {
        Util.showErrorMsg(data.refuseCodeList[0].desc);
        return;
      }
      // 判断用户是否有待激活的息费减免券，用hasWaitOffersFlag标记
      let hasWaitOffersFlag = false;
      let canCancelAutoPendingOrder = false; // 在途批扣订单
      let canCancelActivePendingOrder = false; // 在途支付宝或微信主动还款订单
      let needBreakAutoPendingOrder = false; // 需要中断退出在途批扣订单
      let needBreakActivePendingOrder = false; // 需要中断退出在途批扣订单
      let needBreak = false;
      if (data && data.guideCodeList) {
        for (const item of data.guideCodeList) {
          const { code, value } = item;
          if (code === 'INACTIVATED_REPAY_REDUCE' && value === 'Y') {
            hasWaitOffersFlag = true;
          } else if (code === 'CAN_CANCEL_FLAG' && value === 'Y') { // 长时间在途批扣
            canCancelAutoPendingOrder = true;
          }
        }
      }
      const commonInfo = { ...this.commonRepayInfo() };
      // 存在需确认的中长时间在途批扣，且支付方式不同
      const pendingAutoRepayTrans = ((data || {}).pendingRepayTransInfo || {}).repayType === 'B' ? data.pendingRepayTransInfo : {};
      if (!canCancelAutoPendingOrder && pendingAutoRepayTrans && JSON.stringify(pendingAutoRepayTrans) !== '{}') {
        if ((pendingAutoRepayTrans || {}).canCancelPendingFlag === 'Y'
          && (pendingAutoRepayTrans || {}).repayWay && (commonInfo || {}).repayWay
            && (((pendingAutoRepayTrans || {}).repayWay.indexOf('ALIPAY') > -1 && (commonInfo || {}).repayWay.indexOf('ALIPAY') < 0)
              || ((pendingAutoRepayTrans || {}).repayWay === 'BANK' && (commonInfo || {}).repayWay !== 'BANK')
                || ((pendingAutoRepayTrans || {}).repayWay === (commonInfo || {}).repayWay && (pendingAutoRepayTrans || {}).repayWay === 'BANK' && (pendingAutoRepayTrans || {}).bankCardId !== (commonInfo || {}).bankCardId))
        ) {
          canCancelAutoPendingOrder = true;
        } else {
          needBreakAutoPendingOrder = true;
        }
      }
      // 存在需提示的在途支付宝微信主动还款
      const pendingActiveRepayTrans = ((data || {}).pendingRepayTransInfo || {}).repayType === 'O' ? data.pendingRepayTransInfo : {};
      if (pendingActiveRepayTrans && JSON.stringify(pendingActiveRepayTrans) !== '{}') {
        if ((pendingActiveRepayTrans || {}).canCancelPendingFlag === 'Y') {
          canCancelActivePendingOrder = true;
        } else {
          needBreakActivePendingOrder = true;
        }
      }
      if (hasWaitOffersFlag && !this.confirmedWaitOffersTip) {
        dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.offersPayTipModalShow` });
        needBreak = true;
        this.setState({
          openWaitOffersTipModal: true
        });
      } else if (canCancelAutoPendingOrder) {
        needBreak = true;
        this.setState({
          openAvoidDupPayModal: true
        });
      } else if (canCancelActivePendingOrder) {
        dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.CanCancelActivePendingOrder` });
        needBreak = true;
        Madp.showModal({
          title: '支付提示',
          content: `您已经发起一笔${((pendingActiveRepayTrans || {}).repayWay && (pendingActiveRepayTrans || {}).repayWay.indexOf('ALIPAY') > -1) ? '支付宝' : '微信'}支付还款，请确认是否完成支付？`,
          cancelText: '未完成',
          confirmText: '已完成',
          confirmColor: themeColor,
          success: async (res) => {
            if (res.cancel) {
              dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.ActivePendingOrderUnfinish` });
              await this.confirmTransRecordSingleQuery(pendingActiveRepayTrans);
            } else if (res.confirm) {
              dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.ActivePendingOrderFinished` });
              await this.confirmTransRecordSingleQuery(pendingActiveRepayTrans, true);
            }
          }
        });
      } else if (needBreakAutoPendingOrder) {
        dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.NeedBreakAutoPendingOrder` });
        needBreak = true;
        Madp.showModal({
          title: '支付提示',
          content: '您有一笔自动还款正在处理中，为避免重复还款，请耐心等待还款结果通知',
          confirmText: '返回首页',
          confirmColor: themeColor,
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              const { updateIgnoreLeave } = this.props;
              if (typeof updateIgnoreLeave === 'function') updateIgnoreLeave();
              const url = '%2Fpages%2Findex%2Findex';
              Madp.redirectTo({
                url: decodeURIComponent(url),
              });
            }
          }
        });
      } else if (needBreakActivePendingOrder) {
        dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.NeedBreakActivePendingOrder` });
        needBreak = true;
        Madp.showModal({
          title: '支付提示',
          content: '您有一笔还款正在处理中，为避免重复还款，请5分钟后再试',
          confirmText: '返回首页',
          confirmColor: themeColor,
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              const { updateIgnoreLeave } = this.props;
              if (typeof updateIgnoreLeave === 'function') updateIgnoreLeave();
              const url = '%2Fpages%2Findex%2Findex';
              Madp.redirectTo({
                url: decodeURIComponent(url),
              });
            }
          }
        });
      }
      if (!needBreak) {
        const selectedCard = getStore('selectedCard');
        this.jumpPay(selectedCard, this.actualRepayAmt);
      }
    } else {
      setTimeout(() => {
        Util.showErrorMsg(errMsg);
        dispatchTrackEvent({
          event: EventTypes.SO,
          beaconId: `${this.props.trackPrefix}.${errCode === 'UMDP00065' ? 'OtherRepaymentRunning' : 'RepayPretreatmentSystemError'}`
        });
      }, 50);
    }
  }

  // 关闭待激活的息费减免券提示框
  closeWaitOffersTipModal() {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.confirmOffersPayTipModal` });
    this.setState({
      openWaitOffersTipModal: false
    });
  }
  // 存在待激活的息费减免券任选择继续还款
  cancelWaitOffersPay() {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.cancelOffersPayTipModal` });
    this.confirmedWaitOffersTip = true;
    this.setState({
      openWaitOffersTipModal: false
    });
    this.beforePayCheck();
  }

  // 关闭长时间还款提示框
  closeAvoidDupPayModal() {
    this.setState({
      openAvoidDupPayModal: false
    });
    const { updateIgnoreLeave } = this.props;
    if (typeof updateIgnoreLeave === 'function') updateIgnoreLeave();
    const url = '%2Fpages%2Findex%2Findex';
    Madp.redirectTo({
      url: decodeURIComponent(url),
    });
  }
  // 取消自动还款，并手动还款
  async cancelAutoPayAndManualPay() {
    this.setState({
      openAvoidDupPayModal: false
    });
    const { ret, errCode, errMsg } = await Dispatch.repayment.orderClose({
      repayCloseMode: '02', // 01: 在线关单，02：批扣关单
    });
    if (ret === '0' && errCode === 'COM00000') {
      const selectedCard = getStore('selectedCard');
      this.jumpPay(selectedCard, this.actualRepayAmt);
    } else {
      Madp.showToast({ title: errMsg || '系统繁忙，请稍后再试', icon: 'none' });
    }
  }

  // 查询在途支付宝或微信主动还款交易结果
  async confirmTransRecordSingleQuery(pendingActiveRepayTrans, finished = false) {
    const { transRecord = {} } = (pendingActiveRepayTrans || {}).transSeqno
      ? await Dispatch.repayment.queryTransRecordSingle({ transSeqno: (pendingActiveRepayTrans || {}).transSeqno, querySubTransFlag: 'Y', queryOrderFlag: 'Y' })
      : {};
    const { transStatus = 'PROC', custOrderStatus, repaymentLogList } = transRecord || {};
    let settleQuery = {};
    if (custOrderStatus && repaymentLogList) {
      settleQuery = {
        custOrderStatus,
        repaymentLogList
      };
    }
    settleQuery.transSeqno = (pendingActiveRepayTrans || {}).transSeqno;
    switch (transStatus) {
      case 'SUC':
        dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.ActivePendingOrderPaySuccess` });
        if (!finished) {
          dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.ActivePendingOrder` });
          Madp.showModal({
            title: '支付成功',
            content: `您已使用${((pendingActiveRepayTrans || {}).repayWay || '').indexOf('ALIPAY') > -1 ? '支付宝' : '微信'}支付还款成功`,
            confirmText: '我知道了',
            confirmColor: themeColor,
            showCancel: false,
            success: async (res) => {
              if (res.confirm) {
                await this.toResultPage('success', settleQuery);
              }
            }
          });
        } else {
          await this.toResultPage('success', settleQuery);
        }
        break;
      case 'FAIL':
        dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.ActivePendingOrderPayFail` });
        if (!finished) {
          await this.closeWXOrZFBPay(pendingActiveRepayTrans || {}, ((pendingActiveRepayTrans || {}).repayWay || '').indexOf('ALIPAY') > -1 ? 'alipay' : ((pendingActiveRepayTrans || {}).repayWay ? 'wechat' : ''));
        } else {
          await this.toResultPage('fail', settleQuery);
        }
        break;
      case 'PROC':
        dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.ActivePendingOrderPayPending` });
        if (!finished) {
          await this.closeWXOrZFBPay(pendingActiveRepayTrans || {}, ((pendingActiveRepayTrans || {}).repayWay || '').indexOf('ALIPAY') > -1 ? 'alipay' : ((pendingActiveRepayTrans || {}).repayWay ? 'wechat' : ''));
        } else {
          this.setState({
            assignLoadingTitle: '正在查询支付结果...'
          });
          this.payResult = pendingActiveRepayTrans || {};
          this.onPayResultJump('success');
        }
        break;
      default:
        dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.ActivePendingOrderPayDefault` });
        if (!finished) {
          await this.closeWXOrZFBPay(pendingActiveRepayTrans || {}, ((pendingActiveRepayTrans || {}).repayWay || '').indexOf('ALIPAY') > -1 ? 'alipay' : ((pendingActiveRepayTrans || {}).repayWay ? 'wechat' : ''));
        } else {
          this.setState({
            assignLoadingTitle: '正在查询支付结果...'
          });
          this.payResult = pendingActiveRepayTrans || {};
          this.onPayResultJump('success');
        }
        break;
    }
  }

  // 跳转支付，支付宝、微信和银行卡还款
  async jumpPay(selectedCard, needPayAmt) {
    this.setState({
      setContractZIndex: false
    });
    // 若为支付宝和微信还款则直接跳支付宝，无需输入招联交易密码
    if (Number(needPayAmt) > 0 && (selectedCard.isAlipay || selectedCard.isWxPay)) {
      dispatchTrackEvent({
        event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.OpenPayModel`, beaconContent: { cus: { pay_type: selectedCard.isAlipay ? 'Alipay' : 'Wxpay' } }
      });
      await this.debounceApi();
      return;
    }
    this.normalPay();
  }

  async normalPay() { // 如果有补签豁免标识就不用二次验证动码
    const { showFPAYContract } = this.state;
    if (showFPAYContract) {
      await this.initToken('SCENE_SUPPLEMENT');// 如果要补签，先走补签验证动码
    } else if (CustomConfig.payBySms) {
      await this.initToken();
    } else if (this.useTranComponentSign) {
      dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.NeedPassword`, beaconContent: { cus: { expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay } } });
      this.biomtOrPasswOrLivenRef.startProcess();
      this.setState({
        curShowTradePwdDialog: true,
      });
    } else {
      dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.NeedPassword`, beaconContent: { cus: { expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay } } });
      this.setState({
        clearAndDisableInput: false,
        showTradePwdDialog: true,
        curShowTradePwdDialog: true,
      });
    }
  }

  /**
   * 启用新交易组件 渠道开关 不配置就会启用
   */
  get useTranComponentSign() {
    if (!this.repayConfig || process.env.TARO_ENV === 'alipay') return false;
    else if (this.repayConfig.useTranComponent === 'allOld') return false;
    else if (this.repayConfig.useTranComponent === 'allNew') return true;
    return this.repayConfig.useTranComponent && this.repayConfig.useTranComponent.indexOf(Madp.getChannel()) > -1;
  }

  /**
   * 立即支付
   */
  async immediatePayApi(noToken) {
    const selectedCard = getStore('selectedCard');
    const { showFPAYContract } = this.state;
    dispatchTrackEvent({
      event: EventTypes.SO,
      beaconId: `${this.props.trackPrefix}.PayLaunch`,
      beaconContent: { cus: { expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay, awardNo: (this.selectedCoupon || {}).awardNo, repayWay: (selectedCard || {}).bankCardId, repayAmount: this.actualRepayAmt, hasContract: showFPAYContract, courtCostAmt: this.courtCostAmt } }
    });
    if (this.billType === 'advanced-stage') {
      await this.advancedStagePayNew(noToken);
    } else if (this.expressScene !== '31' && this.billType === 'fee-reduce') {
      await this.feeReducePay(noToken);
    } else {
      await this.standardPay(noToken);
    }
  }

  // 再分期提交还款
  async advancedStagePayNew(noToken) {
    const { alipayOnly, showFPAYContract } = this.state;
    const selectedCard = getStore('selectedCard');
    const { isWxPay } = selectedCard || {};
    const commonInfo = { ...this.commonRepayInfo(noToken, true) };
    // repayMode不需要
    delete commonInfo.repayMode;
    let securityInfoList = [];
    if (commonInfo.passwordToken) {
      securityInfoList = [{
        securityType: '1',
        securityContent: commonInfo.passwordToken
      }];
    } else if (commonInfo.smsCodeToken) {
      securityInfoList = [{
        securityType: '0',
        securityContent: commonInfo.smsCodeToken
      }];
    }
    // 获取优惠券
    // awardType 217   specialScene SSA02   博弈催收省心分资格券  [展期 展示还款 展期试算 ----送省心分资格券]  还有作业和微光卡场景
    // awardInfoList = awardInfoList.filter((item) => item.awardType === '217');
    const advancedStageAwardInfoList = [];
    if (this.selectedCoupon.awardNo) {
      advancedStageAwardInfoList.push({ awardNo: this.selectedCoupon.awardNo });
    }
    if (this.extendRepayQualificationCoupon && this.extendRepayQualificationCoupon.awardNo) {
      advancedStageAwardInfoList.push({ awardNo: this.extendRepayQualificationCoupon.awardNo });
    }
    if (this.extendRepayFutureCoupon && this.extendRepayFutureCoupon.awardNo) {
      advancedStageAwardInfoList.push({ awardNo: this.extendRepayFutureCoupon.awardNo });
    }
    const { applyNo } = getStore('advancedStageInfo');
    const payResultPageUrl = `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=1&repaymentFlag=${this.repaymentFlag}`;
    const repayParams = {
      payResultPageUrl,
      awardInfoList: advancedStageAwardInfoList,
      ...commonInfo,
      ...this.getOverPayAmtRepayParams(),
      transPreRepayAmt: Util.numToStr(this.preRepayUsedAmount) || '0.00', // 预还款金额
      transCashAmt: Util.numToStr(this.actualRepayAmt), // 新还款金额参数
      transTotalAmt: this.transTotalAmt, // 新还款金额参数
      totalWaiveAmt: this.totalWaiveAmt || '0.00', // 新还款 减免总金额
      securityInfoList,
      applyNo,
    };
    const {
      data, ret, errMsg, errCode
    } = await Dispatch.repayment.advancedStagePayNew(repayParams);
    this.transRefNo = Util.getTransRefNo();
    if (ret === '1') {
      if (isWxPay) {
        dispatchTrackEvent({
          event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.gotoWxRepayFail`, beaconContent: { cus: {} }
        });
      }
      this.handleErrorCode(errMsg, errCode);
    } else {
      this.transSeqno = (data && data.transSeqno) || '';
      const orderNoList = repayParams && repayParams.repayTrialDetailList && repayParams.repayTrialDetailList.map((orderNo) => orderNo.orderNo);
      dispatchTrackEvent({
        event: EventTypes.BC,
        beaconId: `${this.props.trackPrefix}.gotoRepaySuccess`,
        beaconContent: { cus: { transSeqno: data ? data.transSeqno : '', orderNoList: orderNoList || [], expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay, awardNo: (this.selectedCoupon || {}).awardNo, repayWay: (selectedCard || {}).bankCardId, repayAmount: this.actualRepayAmt, hasContract: showFPAYContract, courtCostAmt: this.courtCostAmt } }
      });
      if (isWxPay) {
        dispatchTrackEvent({
          event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.gotoWxRepaySuccess`, beaconContent: { cus: {} }
        });
      }
      this.payResult = data;
      this.payResult.alipayOnly = alipayOnly ? '1' : '0';
      this.payResult.bankCardLimitSplit = ((repayParams.bankCardLimitSplit
        || (repayParams.bankCardInfo && repayParams.bankCardInfo.bankCardLimitSplit)) === 'Y') ? '1' : '0';
      this.payResult.randomReduceFlag = (this.props.randomReduceFlag === 'Y' && this.props.randomReduceSwitch === 'Y') ? 'Y' : 'N'; // 是否应享受随机立减标志
      this.payResult.overPayAmt = this.overPayAmt || ''; // 溢缴款金额传递
      // 准备支付结果页的参数
      this.handlePayResult(repayParams);
      // 溢缴款大于还款金额，应还金额为0，不用微信/支付宝扣款
      if (!Number(repayParams.transCashAmt)) {
        if (this.payResult.transStatus === 'PROC') {
          this.onPayResultJump('success');
        } else {
          this.onPayResultJump('fail');
        }
      } else if (!isWxPay && (this.payResult.alipayUrl || this.payResult.prepayId)) {
        this.alipaySDK(this.payResult);
      } else if (isWxPay && (this.payResult.nonceStr || this.payResult.prepayId)) {
        this.wxPaySDK(this.payResult);
      } else if (this.payResult.transStatus === 'PROC') {
        this.onPayResultJump('success');
      } else {
        this.onPayResultJump('fail');
      }
    }
  }

  // 息费减免还款
  async feeReducePay(noToken) {
    // 微光卡路由参数 1 显示
    const { alipayOnly, showFPAYContract } = this.state;
    const selectedCard = getStore('selectedCard');
    const { isWxPay } = selectedCard || {};
    const repayParams = this.prepareFeeReduceRepayParams(noToken);
    const {
      data, ret, errMsg, errCode
    } = await Dispatch.repayment.normalRepayApply(repayParams);

    this.transRefNo = Util.getTransRefNo();
    if (ret === '1') {
      dispatchTrackEvent({
        event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.gotoRepayFail`, beaconContent: { cus: { transSeqno: data ? data.transSeqno : '', errCode } }
      });
      if (isWxPay) {
        dispatchTrackEvent({
          event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.gotoWxRepayFail`, beaconContent: { cus: {} }
        });
      }
      this.handleErrorCode(errMsg, errCode);
    } else {
      this.transSeqno = (data && data.transSeqno) || '';
      const orderNoList = repayParams && repayParams.repayTrialDetailList && repayParams.repayTrialDetailList.map((orderNo) => orderNo.orderNo);
      dispatchTrackEvent({
        event: EventTypes.BC,
        beaconId: `${this.props.trackPrefix}.gotoRepaySuccess`,
        beaconContent: { cus: { transSeqno: data ? data.transSeqno : '', orderNoList: orderNoList || [], expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay, awardNo: (this.selectedCoupon || {}).awardNo, repayWay: (selectedCard || {}).bankCardId, repayAmount: this.actualRepayAmt, hasContract: showFPAYContract, courtCostAmt: this.courtCostAmt } }
      });
      if (isWxPay) {
        dispatchTrackEvent({
          event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.gotoWxRepaySuccess`, beaconContent: { cus: {} }
        });
      }
      this.payResult = data;
      this.payResult.alipayOnly = alipayOnly ? '1' : '0';
      this.payResult.bankCardLimitSplit = ((repayParams.bankCardLimitSplit
        || (repayParams.bankCardInfo && repayParams.bankCardInfo.bankCardLimitSplit)) === 'Y') ? '1' : '0';
      this.payResult.randomReduceFlag = (this.props.randomReduceFlag === 'Y' && this.props.randomReduceSwitch === 'Y') ? 'Y' : 'N'; // 是否应享受随机立减标志
      this.payResult.overPayAmt = this.overPayAmt || ''; // 溢缴款金额传递
      // 准备支付结果页的参数
      this.handlePayResult(repayParams);
      // 溢缴款大于还款金额，应还金额为0，不用微信/支付宝扣款
      if (!Number(repayParams.transCashAmt)) {
        if (this.payResult.transStatus === 'PROC') {
          this.onPayResultJump('success');
        } else {
          this.onPayResultJump('fail');
        }
      } else if (!isWxPay && (this.payResult.alipayUrl || this.payResult.prepayId)) {
        this.alipaySDK(this.payResult);
      } else if (isWxPay && (this.payResult.nonceStr || this.payResult.prepayId)) {
        this.wxPaySDK(this.payResult);
      } else if (this.payResult.transStatus === 'PROC') {
        this.onPayResultJump('success');
      } else {
        this.onPayResultJump('fail');
      }
    }
  }

  /**
   * 组装息费减免还款接口的请求参数
   */
  prepareFeeReduceRepayParams(noToken) {
    const {
      settleWaiveAmt,
      totalCanWaiveAmt,
      waiveAmount, // 息费减免优惠金额(包含减免息费和违约金)
      prepayFeeRightsObject
    } = getStore('feeReduceInfo') || {};
    const commonInfo = { ...this.commonRepayInfo(noToken, true) };
    // 新增参数
    const awardInfoList = getStore('awardInfoList'); // 获取优惠券
    let securityInfoList = [];
    if (commonInfo.passwordToken) {
      securityInfoList = [{
        securityType: '1',
        securityContent: commonInfo.passwordToken
      }];
    } else if (commonInfo.smsCodeToken) {
      securityInfoList = [{
        securityType: '0',
        securityContent: commonInfo.smsCodeToken
      }];
    }
    const payResultPageUrl = `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/repay-success/index?alipayUrl=1`;
    const reqBody = {
      payResultPageUrl,
      ...commonInfo,
      repayDetailList: this.getRepayDetailList(),
      isRandomReduce: 'N',
      ...this.getOverPayAmtRepayParams(),
      transCashAmt: Util.numToStr(this.actualRepayAmt), // 新还款金额参数
      repayTotalAmt: Util.numToStr(this.actualRepayAmt), // 新还款金额参数
      transTotalAmt: Util.floatAdd(Number(this.transTotalAmt || 0), Number(waiveAmount || 0)).toFixed(2), // 新还款金额参数
      securityInfoList, // 新还款 安全规则参数
      settleWaiveAmt: Number(settleWaiveAmt || 0).toFixed(2),
      totalWaiveAmt: Number(totalCanWaiveAmt || 0).toFixed(2), // 新还款 减免总金额
      totalAwardAmount: Number(totalCanWaiveAmt || 0).toFixed(2), // 新还款 减免总金额
      transPreRepayAmt: Util.numToStr(this.preRepayUsedAmount) || '0.00', // 预还款金额
      ...prepayFeeRightsObject, // 免提还权益信息
    };
    if (awardInfoList.length) {
      reqBody.feeInteRightList = [
        { ...getFeeInteRightInfo(awardInfoList[0] || {}) }
      ];
    }

    return reqBody;
  }

  // 正常还款
  async standardPay(noToken) {
    // 微光卡路由参数 1 显示
    const { alipayOnly, showFPAYContract } = this.state;
    const selectedCard = getStore('selectedCard');
    const { isWxPay } = selectedCard || {};
    const repayParams = this.prepareRepayParams(noToken);
    const {
      data, ret, errMsg, errCode
    } = await Dispatch.repayment.normalRepayApply(repayParams);

    this.transRefNo = Util.getTransRefNo();
    if (ret === '1') {
      dispatchTrackEvent({
        event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.gotoRepayFail`, beaconContent: { cus: { transSeqno: data ? data.transSeqno : '', errCode } }
      });
      if (isWxPay) {
        dispatchTrackEvent({
          event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.gotoWxRepayFail`, beaconContent: { cus: {} }
        });
      }
      this.handleErrorCode(errMsg, errCode);
    } else {
      this.transSeqno = (data && data.transSeqno) || '';
      const orderNoList = repayParams && repayParams.repayTrialDetailList && repayParams.repayTrialDetailList.map((orderNo) => orderNo.orderNo);
      dispatchTrackEvent({
        event: EventTypes.BC,
        beaconId: `${this.props.trackPrefix}.gotoRepaySuccess`,
        beaconContent: { cus: { transSeqno: data ? data.transSeqno : '', orderNoList: orderNoList || [], expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay, awardNo: (this.selectedCoupon || {}).awardNo, repayWay: (selectedCard || {}).bankCardId, repayAmount: this.actualRepayAmt, hasContract: showFPAYContract, courtCostAmt: this.courtCostAmt } }
      });
      if (isWxPay) {
        dispatchTrackEvent({
          event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.gotoWxRepaySuccess`, beaconContent: { cus: {} }
        });
      }
      this.payResult = data;
      this.payResult.alipayOnly = alipayOnly ? '1' : '0';
      this.payResult.bankCardLimitSplit = ((repayParams.bankCardLimitSplit
        || (repayParams.bankCardInfo && repayParams.bankCardInfo.bankCardLimitSplit)) === 'Y') ? '1' : '0';
      this.payResult.randomReduceFlag = (this.props.randomReduceFlag === 'Y' && this.props.randomReduceSwitch === 'Y') ? 'Y' : 'N'; // 是否应享受随机立减标志
      this.payResult.overPayAmt = this.overPayAmt || ''; // 溢缴款金额传递
      // 准备支付结果页的参数
      this.handlePayResult(repayParams);
      // 溢缴款大于还款金额，应还金额为0，不用微信/支付宝扣款
      if (!Number(repayParams.transCashAmt)) {
        if (this.payResult.transStatus === 'PROC') {
          this.onPayResultJump('success');
        } else {
          this.onPayResultJump('fail');
        }
      } else if (!isWxPay && (this.payResult.alipayUrl || this.payResult.prepayId)) {
        this.alipaySDK(this.payResult);
      } else if (isWxPay && (this.payResult.nonceStr || this.payResult.prepayId)) {
        this.wxPaySDK(this.payResult);
      } else if (this.payResult.transStatus === 'PROC') {
        this.onPayResultJump('success');
      } else {
        this.onPayResultJump('fail');
      }
    }
  }

  /**
   * 组装提交还款接口的请求参数
   */
  prepareRepayParams(noToken) {
    const { selectedCoupon } = this;
    const { prepayFeeRightsObject } = this;
    const { applyNo } = getStore('extendRepayInfo') || {};
    // 贷后业务办理前提交还款时需要入参案件号
    const needapplyNoServiceRepay = ['preConsultRepay', 'extend'];

    const commonInfo = { ...this.commonRepayInfo(noToken, true) };
    let securityInfoList = [];
    if (commonInfo.passwordToken) {
      securityInfoList = [{
        securityType: '1',
        securityContent: commonInfo.passwordToken
      }];
    } else if (commonInfo.smsCodeToken) {
      securityInfoList = [{
        securityType: '0',
        securityContent: commonInfo.smsCodeToken
      }];
    }
    const payResultPageUrl = `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/repay-success/index?alipayUrl=1`;
    const reqBody = {
      payResultPageUrl,
      ...commonInfo,
      repayDetailList: this.getRepayDetailList(),
      isRandomReduce: 'N',
      ...this.getOverPayAmtRepayParams(),
      transCashAmt: Util.numToStr(this.actualRepayAmt), // 新还款金额参数
      transTotalAmt: this.transTotalAmt, // 新还款金额参数
      securityInfoList, // 新还款 安全规则参数
      awardInfoList: [], // 新还款 优惠券参数
      settleWaiveAmt: this.settleWaiveAmt || '0.00',
      totalWaiveAmt: this.totalWaiveAmt || '0.00', // 新还款 减免总金额
      transPreRepayAmt: Util.numToStr(this.preRepayUsedAmount) || '0.00', // 预还款金额
      randomReduceFlag: (this.props.randomReduceFlag === 'Y' && this.props.randomReduceSwitch === 'Y' && (!selectedCoupon || !selectedCoupon.awardNo)) ? 'Y' : 'N', // 是否支持随机立减
      interestTotalAmt: Util.numToStr(this.props.interestTotalAmt) || '0.00', // 随机立减总利息
      costTotalAmt: Util.numToStr(this.props.costTotalAmt) || '0.00', // 随机立减总期费用
      postLoanApplyInfo: needapplyNoServiceRepay.includes(this.billType) ? { applyNo: applyNo || this.applyNo } : null,
      ...prepayFeeRightsObject, // 免提还权益信息
    };

    if (selectedCoupon.awardNo) {
      reqBody.feeInteRightList = [
        { ...getFeeInteRightInfo(selectedCoupon) }
      ];
    }
    if (this.billType === 'fee-reduce') {
      reqBody.totalAwardAmount = this.totalWaiveAmt || '0.00';
      reqBody.repayTotalAmt = Util.numToStr(this.actualRepayAmt);
      reqBody.feeInteRightList = [
        { ...getFeeInteRightInfo(selectedCoupon) }
      ];
    }

    return reqBody;
  }

  /**
   * 还款方式，用于作为提交还款的入参
   * @params: transRefNo, currency, repayMode, repayWay, appId, bankCardId, smsCodeToken, passwordToken
   */
  commonRepayInfo(noToken, needBankCardInfo = false) {
    const bankCard = getStore('selectedCard');
    const {
      smsCodeToken, bankCardLimitSplitFlag
    } = this.state;
    // 自助拉起会出现未能获取到流水号就提交的情况
    this.transRefNo = this.transRefNo || Util.getTransRefNo();
    const repayInfo = commonGetCommonRepayInfo(
      bankCard, noToken, needBankCardInfo,
      smsCodeToken, bankCardLimitSplitFlag, this.selectedCoupon,
      this.transRefNo, this.repayMode, this.is0100Bills, this.pwdResultToken
    );
    return repayInfo;
  }

  getOverPayAmtRepayParams() {
    const overPayAmtRepayParams = commonGetOverPayAmtRepayParams(this.overPayAmt, this.remitTotalAmount, this.overPayAmtRepayFlag, this.xiaozhaoAmount);

    return overPayAmtRepayParams;
  }

  // 补签接口参数
  get getsignContractParam() {
    const { capitalConstractNum } = this.state;
    const selectedCard = getStore('selectedCard');
    const signContractParam = {
      cardId: selectedCard.bankCardId,
      entrance: 'SCENE_REPAYMENT',
      doVerifyCard: '1',
      whetherSupplement: true,
      firstScene: 'SIGNATURE_SENDSMS',
      isOneminute: true,
      // forceSign: (selectedCard || {}).forceSignFlag === 'Y',
    };
    if (capitalConstractNum) {
      signContractParam.fundMerchantNo = capitalConstractNum;
    }
    return signContractParam;
  }

  handlePayResult(repayParams) {
    const {
      settleWaiveAmt, selectedCoupon, hasOverdueFlag,
      totalWaiveAmt, isYyjOrYjkLoan
    } = this;
    const bankCard = getStore('selectedCard');
    this.payResult.actualAmt = this.payResult.repayTotalAmt;
    this.payResult.repayMode = this.repayMode;
    this.payResult.isDueTagOrD07 = this.isDueTagCust === 'Y' || hasOverdueFlag || this.isD07Tag;

    if (!this.payResult.isDueTagOrD07 && this.accountInfoList && this.accountInfoList[0]) {
      this.payResult.isDueTagOrD07 = this.accountInfoList[0].acctControlStatus !== '1';
    }

    if (bankCard.bankOrgCode) {
      this.payResult.bankOrgCode = bankCard.bankOrgCode;
      this.payResult.cardNo = bankCard.cardNo;
    }
    // 用于结果展示的超限减免金额和优惠券金额
    const {
      settleWaiveAmt: feeReduceSettleWaiveAmt,
      totalCanWaiveAmt,
    } = getStore('feeReduceInfo') || {};
    const finalSettleWaiveAmt = (this.expressScene !== '31' && this.billType === 'fee-reduce') ? feeReduceSettleWaiveAmt : settleWaiveAmt;
    const finalTotalAwardAmt = (this.expressScene !== '31' && this.billType === 'fee-reduce') ? totalCanWaiveAmt : totalWaiveAmt;
    this.payResult.settleWaiveAmt = +this.payResult.settleWaiveAmt > 0 ? this.payResult.settleWaiveAmt : finalSettleWaiveAmt;
    this.payResult.totalAwardAmt = +this.payResult.totalWaiveAmt > 0 ? this.payResult.totalWaiveAmt : finalTotalAwardAmt;
    // 用于结果页判断是否存在一元借或者一口借借据
    this.payResult.isYyjOrYjkLoan = isYyjOrYjkLoan;
    // 用于结果页上报法诉费埋点
    setStore({ hasCourtCostFlag: Number(this.courtCostAmt) > 0 || this.courtOnly === '1' ? 'Y' : 'N' });
  }

  onPayResultJump(type) {
    this.toDetailResult(type);
  }

  toCommonResult(type) {
    if (this.expressScene === '31') {
      Util.router.replace(this.imCallbackUrl);
    } else {
      Util.router.replace({
        path: '/pages/common-result/result',
        query: {
          type: type === 'success' ? `${this.billType}-${type}` : `${this.billType}-fail`
        }
      });
    }
  }

  toDetailResult(type) {
    if (this.billType === 'fee-reduce' && this.expressScene === '31') {
      Util.router.replace(this.imCallbackUrl);
      return;
    }
    const { repayConfig } = this;
    const resultQuery = {
      type: this.billType, // 结清 total 时触发
      counterSeconds: Number(repayConfig.counterSeconds) || 5,
      result: JSON.stringify(this.payResult),
      expressScene: this.expressScene,
      scene: this.billType === 'advanced-stage' ? 'SCENE_SXF' : ''
    };
    if (this.showLoadingDialog && this.finishLoading) {
      // 重置轮循弹窗的状态
      this.showLoadingDialog = false;
      this.finishLoading = false;
    }
    if (type === 'success') {
      // todo: 支付宝和微信的轮询需要注意，小程序的支付同理
      if (!this.showLoadingDialog) {
        this.showLoadingDialog = true;
        this.queryDialog.show((finishLoading) => {
          this.finishLoading = finishLoading;
          clearTimeout(this.getResultTimer);
          if (this.expressScene === '31') {
            Util.router.replace(this.imCallbackUrl);
          } else if (this.billType === 'advanced-stage') {
            Util.router.replace({
              path: `/pages/bill-advanced-stage/result?status=2&repaymentFlag=${this.repaymentFlag}`,
              query: {
                type: this.billType,
                scene: 'SCENE_SXF',
              }
            });
          } else if (this.billType === 'preConsultRepay') {
            const urlPramas = `serviceType=${StandardService.ConsultRepayApply}&status=5`;
            this.toServicesReapyResult(urlPramas);
          } else {
            Util.router.replace({
              path: '/pages/repay-success/index',
              query: {
                type: this.billType,
                expressScene: this.expressScene,
                repaySence: this.repaySence,
                result: JSON.stringify(this.payResult),
              }
            });
          }
        });
      }
      if (!this.finishLoading) {
        this.getResultTimer = setTimeout(async () => {
          await this.transRecordSingleQuery();
        }, 1000);
      }
    } else {
      if (this.expressScene === '31') {
        Util.router.replace(this.imCallbackUrl);
      } else if (this.billType === 'advanced-stage') {
        Util.router.replace({
          path: `/pages/bill-advanced-stage/result?status=3&subStatus=3&repaymentFlag=${this.repaymentFlag}`,
          query: {
            type: this.billType,
            scene: 'SCENE_SXF',
          }
        });
      } else {
        Util.router.replace({
          path: '/pages/repay-fail/index',
          query: {
            repaySence: this.repaySence,
            ...resultQuery
          }
        });
      }
    }
  }

  /** 立即还款错误码处理 */
  async handleErrorCode(errMsg, errCode) {
    const { trackPrefix, updateIgnoreLeave } = this.props;
    this.setState({
      errCode
    });
    handleErrorCode(
      errMsg,
      errCode,
      (a, b, c) => this.refreshOverPayAoumnt(a, b, c),
      trackPrefix,
      () => { this.setState({ showContractMUModal: true }); },
      this.setIsCantAffordDialogOpened.bind(this),
      themeColor,
      updateIgnoreLeave,
      this.handleRepayErrorDailog, // 错误码弹窗
      this.closeRepayErrorDailog, // 关闭错误码弹窗
      this.bankCardErrorToastFn // 用户绑卡状态
    );
  }

  // 透传弹窗信息
  handleRepayErrorDailog = async (param) => {
    let RepayErrorDailogParam = param;
    this.setState({ showRepayErrorDailog: true, RepayErrorDailogParam });
  }

  /* 银行卡异常、持卡人信息异常
   1、有其他卡或支付方式，提示引导更换其他支付方式,展开绑定银行卡
   2、只有一张银行卡且无其他支付方式，提示使用新银行卡，展开绑定银行卡
   */
  bankCardErrorToastFn = async (errorMsg, setSelectCardsStatusMsg, showCeilingIcon) => {
    const selectedCard = getStore('selectedCard');
    // 在错误码处理前，处理卡状态；此时不需要刷新选卡
    await this.setSelectCardsStatus(selectedCard.bankCardId, setSelectCardsStatusMsg, true, false, 'error', showCeilingIcon);
    // 判断是否有其他支付方式
    this.justNeedInitCard = true;
    const initCard = await this.initSelectCard();
    let RepayErrorDailogParam = {};
    if (!initCard) {
      RepayErrorDailogParam = {
        title: '温馨提示',
        contentText: errorMsg,
        confirmText: '使用新银行卡',
        confirmFn: () => { this.closeRepayErrorDailog(setSelectCardsStatusMsg, true, showCeilingIcon); this.onAddBankCard(); },
        closeDailogText: '关闭',
        closeDailogFn: () => this.closeRepayErrorDailog(setSelectCardsStatusMsg, true, showCeilingIcon)
      };
    } else {
      RepayErrorDailogParam = {
        title: '温馨提示',
        contentText: errorMsg,
        confirmText: '更换其他支付方式',
        confirmFn: () => this.closeRepayErrorDailog(setSelectCardsStatusMsg, true, showCeilingIcon),
      };
    }
    this.setState({ showRepayErrorDailog: true, RepayErrorDailogParam });
  }


  /* 关闭错误码弹窗
   * setSelectCardsStatusMsg-银行卡栏错误提示
   * bankcardOpen-是否展开换卡支付
  */
  closeRepayErrorDailog = (setSelectCardsStatusMsg = '', bankcardOpen = false, showCeilingIcon) => {
    this.setState({ showRepayErrorDailog: false });
    // 弹窗关闭后，银行卡置灰，自动切换下一支付方式（如有），并自动展开“换卡支付”
    if (bankcardOpen) {
      this.setState({ bankcardOpen: true });
      this.refreshOverPayAoumnt(setSelectCardsStatusMsg, showCeilingIcon, 0, true);
    }
  }

  showCeilingDialogFn = (singleDeductionLimit, deductionLimit) => {
    const ceilingDialogParam = {
      title: '温馨提示',
      contentText: `单笔还款限额：${Number(singleDeductionLimit) / 10000}万<br/>日累计还款限额：${Number(deductionLimit) / 10000}万`,
      confirmText: '我知道了',
      confirmFn: this.closCeilingDialog
    };
    // 打开弹窗且传参
    this.setState({ showCeilingDialog: true, ceilingDialogParam });
  }

  closCeilingDialog = () => {
    this.setState({ showCeilingDialog: false });
  }

  // 还款接口报错，自动刷新溢缴款接口
  refreshOverPayAoumnt(setSelectCardsStatusMsg, showCeilingIcon, setTimeoutNum = 2000, needReChooseCard) {
    // 延迟2000ms让showToast能够显示出来
    const selectedCard = getStore('selectedCard');
    const { errCode } = this.state || {};
    // 是否可用卡
    const support = errCode === 'UMDP01156';
    setTimeout(() => {
      if (setSelectCardsStatusMsg || needReChooseCard) {
        this.needReChooseCard = true;
        this.setSelectCardsStatus(selectedCard.bankCardId, setSelectCardsStatusMsg, true, support, 'error', showCeilingIcon);
        // this.overpayAccountInfo();
        return;
      }
      // this.overpayAccountInfo();
      // 重新为用户选卡
      this.initSelectCard();
      this.initPageShow();
    }, setTimeoutNum);
  }

  // 参数处理，需要增加属性
  getRepayDetailList() {
    const result = [];
    this.repayTrialDetailList.forEach((bill) => {
      const temp = {};
      temp.orderNo = bill.orderNo;
      const repayAmt = (Util.floatAdd(Number(bill.canPayAmt), Number(bill.canWaiveAmt))).toFixed(2);
      // 0:100类型repayAmt需要加上超限金额limitWaiveAmt
      temp.repayAmt = (Util.floatAdd(Number(repayAmt), Number(bill.limitWaiveAmt || 0))).toFixed(2);

      temp.debtorSplitDetailList = bill.debtorSplitDetailList;
      if (this.billType === '7days' || this.billType === 'extend') {
        const orderList = this.repayDetailList.filter((item) => item.orderNo === bill.orderNo);
        // eslint-disable-next-line no-confusing-arrow
        temp.instCnt = orderList.reduce((max, i) => (i.installCnt > max ? i.installCnt : max), 0);
      } else {
        temp.instCnt = bill.instCnt || 0;
      }
      result.push(temp);
    });
    return result;
  }

  /**
   * 支付宝还款
   */
  async alipaySDK(r) {
    if (r.alipayUrl) {
      // 支付宝中采用路由跳转的形式进行支付，结果页展示信息保留在缓存中
      // 另支付宝会清session，所以存到local
      const payResult = {
        type: this.billType,
        result: JSON.stringify(this.payResult),
      };
      dispatchTrackEvent({
        event: EventTypes.EV,
        beaconId: `${this.props.trackPrefix}.alipayUrlPayResult`,
        beaconContent: { cus: payResult }
      });
      Madp.setStorageSync('payResult', payResult, 'LOCAL');
    }
    if (process.env.TARO_ENV === 'alipay') {
      // 支付宝小程序 原生支付
      const self = this;
      const zfbOrderId = r.prepayId; // 订单号
      if (zfbOrderId) {
        // eslint-disable-next-line no-undef
        my.tradePay({
          tradeNO: zfbOrderId,
          success: async (res) => {
            if (res.resultCode === '9000') {
              self.onPayResultJump('success');
              dispatchTrackEvent({
                event: EventTypes.EV,
                beaconId: `${self.props.trackPrefix}.AliPayMiniSuccess`,
                beaconContent: { cus: { transRefNo: self.transRefNo, infor: JSON.stringify(res) } }
              });
            } else if (res.resultCode === '6001') {
              self.closeWXOrZFBPay(r, 'alipay'); // 预支付模式->支付宝关单
              dispatchTrackEvent({
                event: EventTypes.CK,
                beaconId: `${self.props.trackPrefix}.zfbAliPayCancelOrderMini`,
              });
            } else {
              self.onPayResultJump('fail');
              dispatchTrackEvent({
                event: EventTypes.EV,
                beaconId: `${self.props.trackPrefix}.AliPayMiniFail`,
                beaconContent: { cus: { transRefNo: self.transRefNo, infor: JSON.stringify(res) } }
              });
            }
          },
          fail: (res) => {
            self.onPayResultJump('fail');
            dispatchTrackEvent({
              event: EventTypes.EV,
              beaconId: `${self.props.trackPrefix}.AliPayMiniFail`,
              beaconContent: { cus: { transRefNo: self.transRefNo, infor: JSON.stringify(res) } }
            });
          }
        });
      } else {
        Madp.showToast({
          title: '暂不支持支付宝支付，请稍选择其他还款方式',
          icon: 'error',
          duration: 2000
        });
      }
    } else if (isMuapp()) { // 招联自营APP
      const self = this;
      window.muapp.AliPayPlugin.alipayAction(r.alipayUrl || r.prepayId, async (params) => {
        if (params && params.resultStatus === '6001') { // 用户手动取消
          self.closeWXOrZFBPay(r, 'alipay'); // 非预支付模式->支付宝关单
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${self.props.trackPrefix}.MuappAliPaySDKCancel`,
            beaconContent: { cus: self.transRefNo }
          });
          return;
        }
        const res = await Dispatch.repayment.alipayCallback(params);
        if (res && res.result === 'SUC') {
          self.onPayResultJump('success');
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${self.props.trackPrefix}.MuappAliPaySDKSuccess`,
            beaconContent: { cus: self.transRefNo }
          });
        } else {
          self.onPayResultJump('fail');
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${self.props.trackPrefix}.MuappAliPaySDKFail`,
            beaconContent: { cus: self.transRefNo }
          });
        }
      });
      return;
    } else if (isAlipay() && !isMuapp() && process.env.TARO_ENV !== 'alipay') {
      // 是否在 支付宝 h5页面内生活号渠道还款
      this.zfbPayResult(r);
      return;
    }
    // h5页面唤起支付宝支付
    if (r.alipayUrl) {
      this.parseToDOM(r.alipayUrl);
    }
  }

  /**
     * 微信还款
     */
  wxPaySDK(r) {
    const self = this;
    if (process.env.TARO_ENV === 'weapp') { // 微信小程序
      Madp.requestPayment({
        timeStamp: r.timestamp,
        nonceStr: r.nonceStr,
        package: r.packageValue,
        signType: r.signType,
        paySign: r.sign,
        success: (res) => {
          self.onPayResultJump('success');
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${self.props.trackPrefix}.WechatWxPaySDKSuccess`,
            beaconContent: { cus: { transRefNo: self.transRefNo, infor: JSON.stringify(res) } }
          });
        },
        fail: async (res) => {
          if (res && res.errMsg === 'requestPayment:fail cancel') { // 用户取消
            self.closeWXOrZFBPay(r, 'wechat'); // 微信关单
          } else {
            self.onPayResultJump('fail');
          }
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${self.props.trackPrefix}.WechatWxPaySDKFail`,
            beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(res) } }
          });
        }
      });
    } else if (isWechat()) { // 微信公众号
      Madp.chooseWXPay({
        timeStamp: r.timestamp,
        nonceStr: r.nonceStr,
        package: r.packageValue,
        signType: r.signType,
        paySign: r.sign,
        success: (res) => {
          self.onPayResultJump('success');
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${self.props.trackPrefix}.WechatWxPaySDKSuccess`,
            beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(res) } }
          });
        },
        fail: (res) => {
          if (res === 'cancel') {
            self.closeWXOrZFBPay(r, 'wechat'); // 微信关单
            return;
          }
          self.onPayResultJump('fail');
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${self.props.trackPrefix}.WechatWxPaySDKFail`,
            beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(res) } }
          });
        }
      });
    } else if (isMuapp()) { // 招联自营APP
      try {
        window.muapp.WeChatPayPlugin.wechatpayAction({
          appId: r.appId,
          nonceStr: r.nonceStr,
          packageValue: r.packageValue,
          // 如果后端接口没送这个partnerId，无法调起微信支付
          partnerId: r.partnerId,
          prepayId: r.prepayId,
          sign: r.sign,
          timeStamp: r.timestamp,
        }, (result) => {
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${self.props.trackPrefix}.MuappWxPaySDKTest`,
            beaconContent: { cus: { resultStatus: result.resultStatus } }
          });
          if (result.resultStatus === '9000') {
            self.onPayResultJump('success');
            dispatchTrackEvent({
              event: EventTypes.EV,
              beaconId: `${self.props.trackPrefix}.MuappWxPaySDKSuccess`,
              beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(result) } }
            });
          } else if (result.resultStatus === '8000') { // 支付取消
            self.closeWXOrZFBPay(r, 'wechat'); // 微信关单
            dispatchTrackEvent({
              event: EventTypes.EV,
              beaconId: `${self.props.trackPrefix}.MuappWxPaySDKCancel`,
              beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(result) } }
            });
          } else {
            Util.toast('微信支付失败');
            dispatchTrackEvent({
              event: EventTypes.EV,
              beaconId: `${self.props.trackPrefix}.MuappWxPaySDKFail`,
              beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(result) } }
            });
          }
        }, (res) => {
          Util.toast('微信支付失败');
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${self.props.trackPrefix}.MuappWxPaySDKError`,
            beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(res) } }
          });
        });
      } catch (error) {
        // error
      }
    } else if (r.prepayId && process.env.TARO_ENV === 'h5') {
      // const url = `${r.prepayId}&redirect_url=${encodeURIComponent(`${window.location.origin + window.location.pathname}#/pages/qrcode-repay/success`)}`;
      // 这里存储payResult是为了微信h5支付完回来能出轮循，因为页面可能会刷新
      Madp.setStorageSync('payResult', this.payResult, 'LOCAL');
      Madp.setStorageSync('wxNeedToLoading', '1', 'LOCAL');
      this.wxPayGoBackNeedToResult = true;
      window.location.href = r.prepayId;
    } else {
      Util.toast('只有APP或Wechat渠道才可以使用微信还款！');
      dispatchTrackEvent({
        event: EventTypes.EV,
        beaconId: `${this.props.trackPrefix}.WxPayChannelError`,
        beaconContent: { cus: self.transRefNo }
      });
    }
  }

  // 微信或支付宝关单（包括预支付和非预支付模式）
  closeWXOrZFBPay = async (r, mode) => {
    await Dispatch.repayment.orderClose({
      originalTransRefNo: r.transSeqno, // 还款流水号
      repayCloseMode: '01', // 关单模式，01-在线关单(预下单、非预下单模式关单)；02-批扣关单
    });
    Madp.showToast({
      title: mode === 'wechat' ? '微信取消' : '支付宝取消',
      icon: 'none',
      duration: 2000
    });
    if (mode === 'wechat') {
      dispatchTrackEvent({
        event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.WxRepayOrderClose`, beaconContent: { cus: {} }
      });
    }
  }

  parseToDOM(str) {
    // 这里存储payResult是为了支付宝h5支付完回来能出轮循，因为页面可能会刷新
    Madp.setStorageSync('payResult', this.payResult, 'LOCAL');
    Madp.setStorageSync('zfbNeedToLoading', '1', 'LOCAL');
    this.zfbPayGoBackNeedToResult = true;
    const div = document.createElement('div');
    if (typeof str === 'string') {
      div.innerHTML = str;
    }
    document.body.appendChild(div);
    div.getElementsByTagName('form')[0].submit();
  }

  // 支付宝h5 支付【生活号渠道还款 与支付宝支付对接的模式调整成“先下单后支付”模式】
  zfbPayResult(r) {
    const self = this;
    // eslint-disable-next-line no-undef
    if (ap) {
      // eslint-disable-next-line no-undef
      ap.tradePay({
        tradeNO: r.prepayId // 订单号,
      }, async (res) => {
        if (res.resultCode === '9000') {
          self.onPayResultJump('success');
          dispatchTrackEvent({
            event: EventTypes.CK,
            beaconId: `${this.props.trackPrefix}.zfbAliPaySuccess`,
          });
        } else if (res.resultCode === '6001') {
          // 如果是走这里就调用后台取消订单 接口 李志恒
          this.closeWXOrZFBPay(r, 'alipay'); // 预支付模式->支付宝关单
          dispatchTrackEvent({
            event: EventTypes.CK,
            beaconId: `${this.props.trackPrefix}.zfbAliPayCancelOrder`,
          });
        } else {
          self.onPayResultJump('fail');
          dispatchTrackEvent({
            event: EventTypes.CK,
            beaconId: `${this.props.trackPrefix}.zfbAliPayFail`,
          });
        }
      });
    }
  }

  onRepayWayClick() {
    dispatchTrackEvent({
      event: EventTypes.EV,
      beaconId: `${this.props.trackPrefix}.clickCheckOutRepayWayButton`,
      beaconContent: { cus: { expressScene: this.expressScene, scene: this.billType === 'advanced-stage' ? 'SCENE_SXF' : '' } },
    });
    if (!this.useTileShow) {
      this.setState({ showRepayWay: true });
    } else {
      // 关闭密码组件
      if (this.useTranComponentSign) {
        this.biomtOrPasswOrLivenRef.handleTpClose();
        this.setState({
          curShowTradePwdDialog: false,
        });
      } else {
        this.setState({
          showTradePwdDialog: false,
          clearAndDisableInput: true, // 清除密码残留
          curShowTradePwdDialog: false,
        });
      }
      this.setState({
        setContractZIndex: true,
        bankcardOpen: true,
      });
    }
  }

  // 查询交易结果
  async transRecordSingleQuery() {
    const { transSeqno, bankCardLimitSplit } = this.payResult;
    const { transRecord = {} } = transSeqno
      ? await Dispatch.repayment.queryTransRecordSingle({ transSeqno, querySubTransFlag: 'Y', queryOrderFlag: 'Y' })
      : {};
    const { transStatus = 'PROC', custOrderStatus, repaymentLogList, overRepayAmt } = transRecord || {};
    setStore({ overRepayAmt });
    let settleQuery = {};
    if (custOrderStatus && repaymentLogList) {
      settleQuery = {
        custOrderStatus,
        repaymentLogList
      };
    }
    settleQuery.transSeqno = transSeqno;
    switch (transStatus) {
      case 'SUC':
        await this.toResultPage(`${bankCardLimitSplit}` === '1' ? '' : 'success', settleQuery);
        break;
      case 'FAIL':
        await this.toResultPage('fail', settleQuery);
        break;
      case 'PROC':
        await this.toResultPage();
        break;
      default:
        await this.toResultPage();
        break;
    }
  }

  async toResultPage(type, settleQuery) {
    if (type) {
      this.setState({
        assignLoadingTitle: ''
      });
      clearTimeout(this.getResultTimer);
      this.queryDialog.hide();
      this.finishLoading = true;
    } else {
      // 处理中，但是截止时间还没到
      if (!this.finishLoading) {
        this.getResultTimer = setTimeout(async () => {
          await this.transRecordSingleQuery();
        }, 1000);
      }
      return;
    }
    const selectedCard = getStore('selectedCard');
    dispatchTrackEvent({
      event: EventTypes.BC,
      beaconId: `${this.props.trackPrefix}.PayQueryFinished`,
      beaconContent: { cus: { expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay, awardNo: (this.selectedCoupon || {}).awardNo, repayWay: (selectedCard || {}).bankCardId, repayAmount: this.actualRepayAmt, transStatus: type || 'proc' } }
    });
    if (this.expressScene === '31') {
      Util.router.replace(this.imCallbackUrl);
      return;
    }
    if (type === 'success') {
      if (this.billType === 'advanced-stage') {
        Util.router.replace({
          path: `/pages/bill-advanced-stage/result?status=1&repaymentFlag=${this.repaymentFlag}`,
          query: {
            type: this.billType,
            scene: 'SCENE_SXF',
          }
        });
      } else if (this.billType === 'extend') {
        await this.queryCustCaseDetail();
      } else if (this.billType === 'preConsultRepay') {
        await this.consultRepayCaseDetail();
      } else {
        Util.router.replace({
          path: '/pages/repay-success/index',
          query: {
            isReapySuccess: '1',
            repaySence: this.repaySence,
            type: this.billType,
            expressScene: this.expressScene,
            specialRepayScene: this.specialRepayScene,
            result: JSON.stringify(this.payResult),
            settleQuery: encodeURIComponent(JSON.stringify(settleQuery)),
          }
        });
      }
    } else if (type === 'fail') {
      if (this.billType === 'advanced-stage') {
        Util.router.replace({
          path: `/pages/bill-advanced-stage/result?status=3&subStatus=3&repaymentFlag=${this.repaymentFlag}`,
          query: {
            type: this.billType,
            scene: 'SCENE_SXF',
          }
        });
      } else if (this.billType === 'preConsultRepay') {
        await this.consultRepayCaseDetail();
      } else {
        Util.router.replace({
          path: '/pages/repay-fail/index',
          query: {
            expressScene: this.expressScene,
            repaySence: this.repaySence,
            transSeqno: settleQuery && settleQuery.transSeqno || '',
          }
        });
      }
    }
  }

  // 下月还案件详情查询
  async queryCustCaseDetail() {
    const { applyNo } = getStore('extendRepayInfo');
    const { ret, data } = await Dispatch.repayment.queryCustCaseDetail({
      applyNo: applyNo || this.applyNo,
      ignoreLoading: true
    });
    const { applyStatus } = data || {};
    if (ret === '0') {
      this.applyStatusNext(applyStatus);
    } else {
      Util.router.replace({
        path: `/pages/service-result/index?_windowSecureFlag=1&serviceType=extend&status=3&repaymentFlag=${this.repaymentFlag}`,
      });
    }
  }

  // 协商还案件详情查询
  async consultRepayCaseDetail() {
    // 1.获取案件申请号
    const applyNo = Url.getParam('applyNo') || '';

    // 2.获取协商还案件详情
    const { ret, data } = await Dispatch.repayment.postLoanQueryCaseDetail({
      applyNo: applyNo,
      ...this.loginInfoHash
    });
    const { applyStatus } = data || {};
    if (ret === '0') {
      this.standardApplyStatusNext(applyStatus, StandardService.ConsultRepayApply);
    } else {
      this.toServicesReapyResult();
    }
  }

  standardApplyStatusNext(applyStatus, serviceType) {
    let urlPramas = `serviceType=${serviceType}`;
    switch (applyStatus) {
      case '210': // 案件审核通过，业务办理成功
        urlPramas = `${urlPramas}&status=1`;
        this.toServicesReapyResult(urlPramas);
        break;
      case '220': // 案件审核通过，业务办理失败
        urlPramas = `${urlPramas}&status=6`;
        this.toServicesReapyResult(urlPramas);
        break;
      default: // 异常情况
        urlPramas = `${urlPramas}&status=6`;
        this.toServicesReapyResult(urlPramas);
        break;
    }
  }


  // 贷后服务标准化结果页
  toServicesReapyResult(urlpramas) {
    Madp.redirectTo({ url: `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/standard-service-result/index?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}&${urlpramas}` });
  }

  // 根据案件结果进行处理
  applyStatusNext(status) {
    switch (status) {
      case '8': // 提交完成，审核中
        Util.router.replace({
          path: `/pages/service-result/index?serviceType=extend&status=2&repaymentFlag=${this.repaymentFlag}`,
          query: {
            type: this.billType,
          }
        });
        break;
      case '9': // 提交完成，办理成功
        Util.router.replace({
          path: `/pages/service-result/index?serviceType=extend&status=1&repaymentFlag=${this.repaymentFlag}`,
          query: {
            type: this.billType,
          }
        });
        break;
      case '10': // 提交完成，办理失败
        Util.router.replace({
          path: `/pages/service-result/index?serviceType=extend&status=3&subStatus=3&repaymentFlag=${this.repaymentFlag}`,
          query: {
            type: this.billType,
          }
        });
        break;
      default: // 异常情况
        Util.router.replace({
          path: '/pages/repay-success/index',
          query: {
            type: this.billType,
            expressScene: this.expressScene,
            result: JSON.stringify(this.payResult),
          }
        });
        break;
    }
  }

  /**
   * @description: 改变选择的银行卡状态
   * @param { String } errorMsg 错误提示
   * @param { Boolean } needInit 是否重新选卡，默认 true
   * @param { Boolean } support 是否正常显示，默认 true
   * @param { String } reason 不可用原因，默认 error
   */
  setSelectCardsStatus(bankCardId, errorMsg, needInit = true, support = true, reason = 'error', showCeilingIcon = false) {
    // 微信和支付宝具有豁免，支付错了也可以继续选
    if (bankCardId === 'alipay' || bankCardId === 'wxpay') {
      return null;
    }
    const { bankCardErrorStatusList, errCode } = this.state;
    const StatusList = bankCardErrorStatusList;
    if (StatusList[bankCardId]) {
      // 如果已存在
      if (StatusList[bankCardId].reasonList.indexOf(reason) <= -1) {
        // 新的错误原因
        StatusList[bankCardId].reasonList.push(reason);
      }
    } else {
      // 卡不存在
      StatusList[bankCardId] = {
        errMsg: errorMsg, // 异常银行卡的提示语
        support, // 不支持的银行卡特殊展示
        reasonList: [reason]
      };
    }
    StatusList[bankCardId].showCeilingIcon = showCeilingIcon;
    StatusList[bankCardId].errCode = errCode; // 错误码
    this.setState({
      bankCardErrorStatusList: StatusList
    }, () => {
      if (this.needReChooseCard) {
        // 重新为用户选卡
        this.initSelectCard();
        this.initPageShow();
        this.needReChooseCard = false;
      }
    });
  }

  // 获取动码，默认获取还款动码，也支持传参，例如补签流程
  async initToken() {
    const selectedCard = getStore('selectedCard');
    const { capitalConstractNum } = this.state;
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.NeedSmsCode`, beaconContent: { cus: { scene: (selectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER'), expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay } } });
    // 新交易组件不需要自行获取token
    if (this.useTranComponentSign && this.onePassOrSmsCodeRef && typeof this.onePassOrSmsCodeRef.startProcess === 'function') {
      this.onePassOrSmsCodeRef.startProcess();
      this.setState({
        curShowSmsCodeDialog: true,
      });
      return;
    }
    this.setState({ // 20221129,旧动码也无需自行获取token
      showSmsCodeDialog: true,
      curShowSmsCodeDialog: true,
    });
  }

  /**
  * 0ZFBMNPJD H5 暂时要关闭支付宝，打开银行卡, 后续会下线这个设定
  */
  get closeWxAlipayFromCC() {
    const { repayConfig } = this;
    if (process.env.TARO_ENV === 'alipay') {
      return Boolean(repayConfig && repayConfig.closeAlipayMNP);
    } else {
      return repayConfig && repayConfig.closeAlipayChannel && repayConfig.closeAlipayChannel.indexOf(Madp.getChannel()) > -1;
    }
  }

  get transferAmt() {
    if (this.selectedCoupon) {
      if (this.selectedCoupon.awardType === '216') {
        return this.totalCanPayAmt;
      } else {
        // 不是216卷的情况下需要把优惠的金额加回来
        return (parseFloat(this.totalCanPayAmt) + parseFloat(this.totalWaiveAmt)).toFixed(2);
      }
    } else {
      return this.totalCanPayAmt;
    }
  }

  /**
   * 查询BOS获取当前渠道支持的还款支付方式和优先展示的支付方式
   * @returns
   * 返回处理后的支付方式数组，形如['2','1','3','4']，顺序在前优先展示
   */
  async getModeOfPayment() {
    const { overdueDay: realOverDueDays } = this.props;
    const res = await Dispatch.repayment.getChannelParams({
      channelParamKeyList: ['modeOfPayment', 'preferentialDisplayPaymentMethod', 'overDueDay', 'overDueRepaymentMethod', 'showWxPayOnBcCantAfford', 'showPaymentOnUserCommand', 'guideAlipaySelectCard', 'oneBindCard'],
      paramTypeList: ['CHANNEL_PARAM']
    });

    // modeOfPayment 返回值为字符串 "1,2,3,4"，1: "银行卡支付"，2: "支付宝支付"，3: "微信支付"，4: "银行卡转账还款"
    let { modeOfPayment, preferentialDisplayPaymentMethod, overDueDay, overDueRepaymentMethod, showWxPayOnBcCantAfford, showPaymentOnUserCommand, guideAlipaySelectCard, oneBindCard }
      = res && res.data && res.data.channelParamMap || {};
    // showWxPayOnBcCantAfford = ['0APP', '2APP', '3CMBAPP', '3CUAPP'].indexOf(Madp.getChannel()) >= 0 || isWechat() || process.env.TARO_ENV === 'weapp'; // 先写死，等配置
    showWxPayOnBcCantAfford = showWxPayOnBcCantAfford === 'Y';
    showPaymentOnUserCommand = showPaymentOnUserCommand === 'Y';
    // 支持引导从支付宝选卡渠道技参配置
    this.supportZFBCardChannelFlag = guideAlipaySelectCard === 'Y';
    // 支持引导一键绑卡渠道技参配置
    this.supportOneBindCardChannelFlag = oneBindCard === 'Y';
    // 当用户逾期天数realOverDueDays大于阈值overDueDay时，支付方式列表使用对应的overDueRepaymentMethod
    if (typeof realOverDueDays === 'number' && +overDueDay >= 1 && realOverDueDays >= +overDueDay) {
      modeOfPayment = overDueRepaymentMethod || [];
    }

    let modeOfPaymentArray = [];
    // 微信、支付宝渠道互斥展示
    if (isWechat() || process.env.TARO_ENV === 'weapp') {
      modeOfPaymentArray = modeOfPayment ? modeOfPayment.split(',').filter((i) => i !== '2') : [];
    } else if (isAlipay() || process.env.TARO_ENV === 'alipay') {
      modeOfPaymentArray = modeOfPayment ? modeOfPayment.split(',').filter((i) => i !== '3') : [];
    } else {
      modeOfPaymentArray = modeOfPayment ? modeOfPayment.split(',') : [];
    }

    // 检查之前是否有“银行卡余额不足而允许微信支付方式”的标记，有则添加微信支付方式(提前还款场景除外)
    if (this.billType !== 'total' && showWxPayOnBcCantAfford && modeOfPaymentArray.indexOf('3') < 0) {
      if (this.userId) {
        const idTimeObj = Madp.getStorageSync('BANKCARD_CANT_AFFORD_ALLOW_WECHAT_REPAY', 'LOCAL') || {};
        if (idTimeObj[this.userId] && Date.now() - idTimeObj[this.userId] < BANKCARD_CANT_AFFORD_ALLOW_WECHAT_REPAY_INTERVAL) {
          modeOfPaymentArray.push('3');
        }
      }
    }

    // 如果允许在用户通过客服进行指定时展示某种支付方式，则调用还款检查接口，检查当前用户是否有指定、指定了何种
    if (showPaymentOnUserCommand) {
      const { ret, data = {} } = await Dispatch.repayment.RepayPretreatment() || {};
      const { paymentMethodList = [] } = data || {};
      if (ret === '0' && Array.isArray(paymentMethodList)) {
        paymentMethodList.forEach(payment => {
          // payment: 1微信；2支付宝
          if (typeof payment === 'string' && typeof (+payment) === 'number') {
            if (payment === '1') {
              payment = '3';
            }
            if (modeOfPaymentArray.indexOf(payment) < 0) {
              modeOfPaymentArray.push(payment);
            }
          }
        });
      }
    }

    // 先排序，排序完即为默认的展示顺序（因为cc配置时参数可能不是按序选择的）
    modeOfPaymentArray = modeOfPaymentArray.sort();
    this.setState({
      modeOfPayment: modeOfPaymentArray,
      firstRepayWay: preferentialDisplayPaymentMethod,
      showWxPayOnBcCantAfford
    });
  }

  addModeOfPayment(paymentList = []) {
    let { modeOfPayment: modeOfPaymentArray } = this.state;
    paymentList.forEach((payment) => {
      const paymentStr = `${payment}`;
      if (modeOfPaymentArray.indexOf(paymentStr) < 0) {
        modeOfPaymentArray.push(paymentStr);
      }
    });
    modeOfPaymentArray = modeOfPaymentArray.sort();
    this.setState({
      modeOfPayment: modeOfPaymentArray,
    });
  }

  get imCallbackUrl() {
    const param = {
      expressScene: this.expressScene,
    };
    if (this.transSeqno) {
      param.outFlowNo = this.transSeqno;
    }
    const url = Url.addParam(decodeURIComponent(this.callbackUrl), param);
    return url;
  }

  // 返回回跳地址。交易组件在招行h5，活体完成之后要根据回跳地址继续流程
  get bioRedirectUrl() {
    if (Madp.getChannel !== '3CMBAPP' || process.env.TARO_ENV !== 'h5') {
      return undefined;
    } else {
      const CurrentUrl = window.location.href;
      const resultToken = Url.getParam('token');
      const bioResult = Url.getParam('bioResult');
      if (resultToken || bioResult) {
        // eslint-disable-next-line no-useless-escape
        const params = CurrentUrl.split(/\?|\&/);
        const filterparams = params.filter((item) => item.indexOf('token=') === -1 && item.indexOf('bioResult=') === -1);
        let newUrl = '';
        // eslint-disable-next-line array-callback-return
        filterparams.map((item, index) => {
          if (index === 0) {
            newUrl += `${item}?`;
          } else if (index < filterparams.length - 1) {
            newUrl += `${item}&`;
          } else {
            newUrl += `${item}`;
          }
        });
        return newUrl;
      } else {
        return CurrentUrl;
      }
    }
  }

  setIsCantAffordDialogOpened(isOpened = true) {
    const { showWxPayOnBcCantAfford } = this.state;
    if (isOpened) {
      let cantAffordDialogHintWxRepay = false;
      // 提前还款时银行卡余额不足且客户没有微信支付时，不增加微信支付展示
      if (this.billType !== 'total' && showWxPayOnBcCantAfford) {
        const { modeOfPayment } = this.state;
        // 余额不足时允许微信支付方式
        if (modeOfPayment.indexOf('3') < 0 && !(isAlipay() || process.env.TARO_ENV === 'alipay')) {
          this.addModeOfPayment(['3']);
          if (!this.hasSentWxPayAvailablebeacon) {
            // this.hasSentWxPayAvailablebeacon为true意味着modeOfPayment在临时增加之前就有微信支付（例如因为逾期打标）
            // 此时就不能展示新增加了微信支付的文案
            cantAffordDialogHintWxRepay = true;
          }
        }
        // 无论临时增加之前是否有微信支付，都添加“银行卡余额不足而允许微信支付方式”的标记
        if (this.userId) {
          let idTimeObj = Madp.getStorageSync('BANKCARD_CANT_AFFORD_ALLOW_WECHAT_REPAY', 'LOCAL') || {};
          if (!idTimeObj[this.userId] || Date.now() - idTimeObj[this.userId] >= BANKCARD_CANT_AFFORD_ALLOW_WECHAT_REPAY_INTERVAL) {
            idTimeObj[this.userId] = Date.now();
            Madp.setStorageSync('BANKCARD_CANT_AFFORD_ALLOW_WECHAT_REPAY', idTimeObj, 'LOCAL');
          }
        }
      }
      const selectedCard = getStore('selectedCard');
      const { bankOrgShortName, bankCardNoMask = '', bankName = '' } = selectedCard || {};
      this.setState({
        isCantAffordDialogOpened: true,
        cantAffordDialogCountdown: 3,
        cantAffordDialogBankcardName: bankOrgShortName ? `${bankOrgShortName} (${bankCardNoMask.slice(-4)})` : `${bankName} (${bankCardNoMask.slice(-4)})`,
        cantAffordDialogHintWxRepay
      }, () => {
        const timer = setInterval(() => {
          const { cantAffordDialogCountdown } = this.state;
          const newCantAffordDialogCountdown = cantAffordDialogCountdown - 1;
          this.setState({
            cantAffordDialogCountdown: newCantAffordDialogCountdown
          }, () => {
            if (newCantAffordDialogCountdown <= 0) {
              this.setIsCantAffordDialogOpened(false);
              if (timer) {
                clearInterval(timer);
              }
            }
          });
        }, 1000);
      });
    } else {
      this.setState({
        isCantAffordDialogOpened: false,
      }, () => { // 确保关掉对话框后再清空银行卡名
        this.setState({
          cantAffordDialogBankcardName: ''
        });
      });
    }
  }

  // 剪切板
  setClipboardTextCall = async () => {
    if (Madp.getChannel() !== '0APP') {
      await Madp.setClipboardText({
        text: `${clipboardUrl}&clickPosition=repayCheckOutRandomReduce`,
        success: async () => {
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${this.props.trackPrefix}.RandomReduceClipSuccess`,
          });
        },
        fail: () => {
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: `${this.props.trackPrefix}.RandomReduceClipFail`,
          });
        }
      });
    }
  }

  render() {
    const {
      show
    } = this.state;
    const {
      className, randomReduceFlag, randomReduceText, interestTotalAmt, costTotalAmt, afterEditClick
    } = this.props;

    const {
      openPwdTip, openSmsCodeTip, bankCards,
      showSmsCodeDialog, showTradePwdDialog, clearAndDisableInput, alipayOnly, repayWayNoticeConfig,
      showRepayCardContent, showFPAYContract, showContractMUModal, SignContract, bankCardErrorStatusList,
      capitalConstractNum, showTranseferModal,
      selectBankTransferFlag, hasOverdueFlag,
      showErrorTipModal, maskRealName, openTransferCouponTip, showRepayWay,
      setContractZIndex, fullName, modeOfPayment, firstRepayWay,
      curShowSmsCodeDialog, curShowTradePwdDialog, bankcardOpen,
      isCantAffordDialogOpened, cantAffordDialogCountdown, cantAffordDialogBankcardName = '', cantAffordDialogHintWxRepay,
      openWaitOffersTipModal, openAvoidDupPayModal, assignLoadingTitle, showRepayErrorDailog, RepayErrorDailogParam, showCeilingDialog, ceilingDialogParam
    } = this.state;

    const countTime = this.repayConfig && this.repayConfig.counterSeconds ? Number(this.repayConfig.counterSeconds) : 5;
    const isShowOverRepayRow = (this.overPayAmtRepayFlag || Number(this.preRepayAmt) > 0) && !selectBankTransferFlag;
    const { isOverDueUserFlag } = this;
    const selectedCard = getStore('selectedCard');

    let selectButtonText = '';
    let bankImageUrl = '';
    if (Object.keys(selectedCard).length !== 0) {
      if (selectedCard.isAlipay || selectedCard.isWxPay) {
        selectButtonText = selectedCard.isAlipay ? '支付宝' : (selectedCard.isWxPay ? '微信' : '');
        const { bankOrg: { bankImage } } = selectedCard || {};
        bankImageUrl = bankImage;
      } else {
        const { bankOrgShortName, bankCardNoMask = '', bankImage = '', bankName = '' } = selectedCard || {};
        bankImageUrl = bankImage;
        selectButtonText = bankOrgShortName ? `${bankOrgShortName} (${bankCardNoMask.slice(-4)})` : `${bankName} (${bankCardNoMask.slice(-4)})`;
      }
    }

    const showButtonText = selectButtonText;
    const showBankImageUrl = bankImageUrl;

    // 处理微信的兼容性问题，需要给drawer-content设置高度支付方式才能滚动
    const repayWayDrawerHeightClass = 'repay-check-out-repay-way-drawer-normal-height';

    // 有协议、预约还款、支付宝微信的时候样式会有区别
    const isNeedMaginBottom = showFPAYContract || selectedCard.isAlipay || selectedCard.isWxPay || selectBankTransferFlag;
    // 最上方展示金额需要区分预约还款
    const topShowAmt = selectBankTransferFlag ? this.transferAmt : this.actualRepayAmt;
    // 随机立减展示
    const showRandomReduce = randomReduceFlag === 'Y' && (interestTotalAmt > 0 || costTotalAmt > 0);

    if (showRandomReduce) dispatchTrackEvent({
      event: EventTypes.SO,
      beaconId: `${this.props.trackPrefix}.ShowRandomReduce`,
      beaconContent: { cus: { channel: Madp.getChannel() } }
    });

    // 有随机立减、法诉费时需要高度适配，随机立减和法诉费不会同时存在
    let drawerHeightClass = showRandomReduce ? 'repay-check-out-random-height' : 'repay-check-out-normal-height';
    if (!showRandomReduce) {
      drawerHeightClass = (Number(this.courtCostAmt || 0) > 0) ? 'repay-check-out-court-height' : 'repay-check-out-normal-height';
    }

    return (
      <MUView className={`repay-check-out-warper ${drawerHeightClass}`}>

        <MUDrawer
          beaconId="RepayCheckOutDrawer"
          show={show}
          placement="bottom"
          onClose={() => { this.hide(); }}
        >
          <MUView className="repay-check-out">
            <MUView className={`repay-check-out-top ${isNeedMaginBottom ? 'repay-check-out-top-margin-bottom' : ''} ${showRandomReduce ? 'repay-check-out-top-random-reduce' : ''} ${Number(this.courtCostAmt || 0) > 0 ? 'repay-check-out-top-court' : ''}`}>
              {/* <MUView className="repay-check-out-top-holder" /> */}
              <MUView className="repay-check-out-top-title">
                <MUView className="inline-block">
                  <MUView className="inline-block amount-symbol">¥</MUView>
                  <MUView className="inline-block title-amount">{topShowAmt}</MUView>
                  {(this.editable && !selectBankTransferFlag) && (<MUImage
                    className="inline-block edit-img"
                    src={editImg}
                    beaconId="CheckOutEditClick"
                    beaconContent={{ cus: { expressScene: this.expressScene, newScene: this.newScene } }}
                    onClick={() => {
                      this.hide();
                      if (afterEditClick && typeof afterEditClick === 'function') afterEditClick();
                    }}
                  />)}
                </MUView>
                {showRandomReduce ? (
                  <MUView
                    className="random-reduce"
                    beaconId="SetClipboardText"
                    onClick={() => {
                      this.setClipboardTextCall();
                    }}
                  >
                    <MUText className="random-reduce__text">{randomReduceText}</MUText>
                  </MUView>
                ) : null}
                {Number(this.courtCostAmt || 0 > 0) ? (
                  <MUView className="court-cost-amt">{`(含司法处置费${Number(this.courtCostAmt || 0).toFixed(2)}元)`}</MUView>
                ) : null}
                <MUView className="mask-name">
                  借款人：{maskRealName}
                </MUView>
              </MUView>
              <MUImage
                beaconId="RepayCheckOutDrawerClose"
                className="repay-check-out-top-close"
                src={icClose}
                onClick={() => { this.hide(); }}
              />
            </MUView>
            <MUView
              className={
                classNames(className, 'repay-check-out-content', { 'repay-check-out-content--random': showRandomReduce, 'repay-check-out-content--court': (Number(this.courtCostAmt || 0) > 0) })
              }
            >
              {!selectBankTransferFlag && (<MUView
                className="repay-check-out-select-repay-way"
                beaconId="changeRepayWay"
                onClick={() => this.onRepayWayClick()}
              >
                <MUView className="repay-check-out-select-text">
                  支付方式
                </MUView>
                <MUView className="repay-check-out-select-button">
                  {showBankImageUrl
                    ? <MUImage className="repay-check-out-select-button-bank-image" src={showBankImageUrl} /> : null}
                  <MUView className="repay-check-out-select-button-text">{showButtonText}</MUView>
                  {this.useTileShow && !curShowSmsCodeDialog && !curShowTradePwdDialog ? (
                    null
                  ) : (
                    <MUView className="mu-icon mu-icon-arrow-right repay-check-out-select-button-icon" />
                  )}
                </MUView>
              </MUView>)}

              {selectBankTransferFlag && !this.useTileShow && (<MUView
                className={`repay-check-out-select-repay-way ${this.ledaBankConfig['transfer'] ? 'transferGuide-content' : ''}`}
                beaconId="changeRepayWay"
                onClick={() => this.onRepayWayClick()}
              >
                <MUView className="repay-check-out-select-button">
                  <MUImage className="repay-check-out-select-button-bank-image" src={transferImg} />
                  <MUView className="transferGuide-text">
                    <MUView className="transferGuide-text-top">
                      银行卡转账还款
                    </MUView>
                    {this.ledaBankConfig['transfer'] ? (
                      <MUView
                        className="title-banner-content"
                      >
                        APP专享-最高可返30元
                      </MUView>
                    ) : null}
                    <MUView className="transferGuide-text-bottom">
                      推荐大额用户使用，单笔不限额
                    </MUView>
                  </MUView>
                </MUView>
                <MUView className="mu-icon mu-icon-arrow-right repay-check-out-select-button-icon transferGuide-icon" />
              </MUView>)}

              {selectBankTransferFlag && this.useTileShow && (<MUView
                className="repay-check-out-select-repay-way"
                beaconId="changeRepayWay"
                onClick={() => this.onRepayWayClick()}
              >
                <MUView className="repay-check-out-select-text">
                  支付方式
                </MUView>
                <MUView className="repay-check-out-select-button">
                  <MUImage className="repay-check-out-select-button-bank-image" src={transferImg} />
                  <MUView className="repay-check-out-select-button-text">银行卡转账还款</MUView>
                  {!curShowSmsCodeDialog && !curShowTradePwdDialog ? (
                    null
                  ) : (
                    <MUView className="mu-icon mu-icon-arrow-right repay-check-out-select-button-icon" />
                  )}
                </MUView>
              </MUView>)}
            </MUView>
            {this.useTileShow ? (
              <MUView
                className={
                  classNames(
                    className,
                    'repay-check-out__repay-way-tile',
                    { 'repay-check-out__repay-way-tile--random': showRandomReduce },
                    { 'repay-check-out__repay-way-tile--court': Number(this.courtCostAmt || 0) > 0 },
                    { 'repay-check-out__repay-way-tile--contract': showFPAYContract },
                    { 'repay-check-out__repay-way-tile--transfer': selectBankTransferFlag },
                  )
                }
              >
                {showRepayCardContent ? (
                  <RepayWay
                    trackPrefix="repayment.expressRepay"
                    isShow={showRepayCardContent}
                    bankCards={alipayOnly ? [] : bankCards}
                    closeWxAlipayFromCC={this.closeWxAlipayFromCC}
                    isAlipayOnly={alipayOnly}
                    payWayAlert={repayWayNoticeConfig}
                    bankConfig={this.ledaBankConfig}
                    isDueTagCust={this.isDueTagCust === 'Y'}
                    overDue={this.isDueTagCust === 'Y' || hasOverdueFlag || this.billType === 'fee-reduce'}
                    isOverDueUserFlag={isOverDueUserFlag || this.billType === 'fee-reduce'}
                    billType={this.billType}
                    select={(card, isUserClick = false) => this.userCardSelect(card, isUserClick)}
                    transferGuide={() => this.onTransferGuide()}
                    addBankCard={() => this.onAddBankCard()}
                    amount={this.totalCanPayAmt}
                    bankCardErrorStatusList={bankCardErrorStatusList}
                    overPayAmtRepay={this.overPayAmt}
                    remitTotalAmount={this.remitTotalAmount}
                    overPayAmtRepayFlag={isShowOverRepayRow}
                    availableAmount={this.xiaozhaoAmount}
                    closeAlipay0100BillFlag={this.closeAlipay0100BillFlag}
                    is0100Bills={() => this.is0100Bills}
                    firstRepayConfig={this.repayConfig && this.repayConfig.firstRepayConfig}
                    preRepayAmt={this.preRepayAmt}
                    preRepayUsedAmount={this.preRepayUsedAmount}
                    isRepayCheckout
                    isShowDescTitle={false}
                    modeOfPayment={modeOfPayment}
                    firstRepayWay={firstRepayWay}
                    expressScene={this.expressScene}
                    useTileShow={this.useTileShow}
                    bankcardOpen={bankcardOpen}
                    bankcardClose={() => this.setState({ bankcardOpen: false })}
                    guideZfbCard={this.supportZFBCardChannelFlag || this.supportOneBindCardChannelFlag}
                    showCeilingDialogFn={this.showCeilingDialogFn}
                  />
                ) : null}
              </MUView>
            ) : null}
            <MUView className={`repay-check-out-bottom-content ${setContractZIndex ? 'repay-check-out-bottom-content-z-index' : ''}`}>
              <Contract
                trackPrefix={this.props.trackPrefix}
                onChecked={(v) => { this.isCheckedContract = v; }}
                capitalConstractNum={capitalConstractNum}
                showFPAYContract={showFPAYContract}
                showContractMUModal={false}
                onModalClose={() => { }}
                onModalConfirm={() => { }}
                contractInfos={this.contractInfos}
              />
              {selectBankTransferFlag && (<MUView className="transfer-text">
                先预约，再转账
              </MUView>)}
              <MUView className="button-content">
                <MUButton
                  className="bottom-button"
                  type="primary"
                  beaconId="repayButton"
                  beaconContent={{ cus: { expressScene: this.expressScene, newScene: this.newScene, overdueDay: this.overdueDay, awardNo: (this.selectedCoupon || {}).awardNo, repayWay: (selectedCard || {}).bankCardId, repayAmount: this.actualRepayAmt } }}
                  onClick={() => {
                    if (selectBankTransferFlag) {
                      this.onTransferGuide();
                    } else {
                      this.preSubmit();
                    }
                  }}
                >
                  {selectBankTransferFlag ? '去预约' : '确认还款'}
                </MUButton>
              </MUView>

            </MUView>
            {(
              <MUSafeSmsCodeHalfWrap
                title="同意并输入验证码"
                isOpened={showSmsCodeDialog}
                scene={(showFPAYContract || SignContract) ? 'SCENE_SUPPLEMENT' : (CustomConfig.payBySms ? '' : 'SCENE_REPAYMENT')}
                withLoginStatus
                bizContent={this.transRefNo}
                replaceMsg={showFPAYContract || SignContract ? '手机号码已更换？' : ' '}
                needOnePass={false}
                needUseSendResponseMobile
                onOk={(token, signChannelSuccess) => {
                  console.log('ignore', signChannelSuccess);
                  // 强制补签签约失败，关闭动码，弹窗引导换卡
                  // const nearestSelectedCard = getStore('selectedCard');
                  // dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.SignAuthCodeSuccess`, beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                  // if (!signChannelSuccess && (nearestSelectedCard || {}).forceSignFlag === 'Y') {
                  //   dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.SignFail`, beaconContent: { cus: { scene: 'BAOFU' } } });
                  //   this.setState({ showSmsCodeDialog: false, curShowSmsCodeDialog: false }, () => {
                  //     const that = this;
                  //     Madp.showModal({
                  //       title: '验证失败',
                  //       content: '请选择其他银行卡或绑定新卡',
                  //       confirmText: '我知道了',
                  //       confirmColor: themeColor,
                  //       showCancel: false,
                  //       success(res) {
                  //         if (res.confirm) {
                  //           dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${that.props.trackPrefix}.VerifyFailConfirm`, beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                  //           that.needReChooseCard = true;
                  //           that.setSelectCardsStatus(nearestSelectedCard.bankCardId, '验证失败');
                  //         }
                  //       }
                  //     });
                  //   });
                  //   return;
                  // }
                  this.onSmsVerifyOk(token);
                }}
                onOposcExempt={(token) => { this.onSmsVerifyOk(token); }}
                onError={(errObj) => {
                  // 补签银行发送动码失败，弹窗
                  if ((showFPAYContract || SignContract) && ((errObj || {}).errCode === 105 || (errObj || {}).errCode === 106)) {
                    const nearestSelectedCard = getStore('selectedCard');
                    dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.SendCodeFail`, beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                    this.setState({ showSmsCodeDialog: false, curShowSmsCodeDialog: false }, () => {
                      const that = this;
                      Madp.showModal({
                        title: '动码发送失败',
                        content: '请选择其他银行卡或绑定新卡',
                        confirmText: '我知道了',
                        confirmColor: themeColor,
                        showCancel: false,
                        success(res) {
                          if (res.confirm) {
                            dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${that.props.trackPrefix}.VerifyFailConfirm`, beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                            that.needReChooseCard = true;
                            that.setSelectCardsStatus(nearestSelectedCard.bankCardId, '验证失败', true, false);
                          }
                        }
                      });
                    });
                  }
                }}
                onClose={() => this.setState({ showSmsCodeDialog: false, curShowSmsCodeDialog: false, setContractZIndex: true })}
                beaconId="repaySafeSmsCodePopup"
                onTitleIconClicked={this.openIconModal.bind(this)}
                onVerifyFail={() => {
                  const nearestSelectedCard = getStore('selectedCard');
                  dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.AuthCodeFail`, beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                  Madp.showToast({ title: '验证失败，请稍后重试或更换支付方式', icon: 'none' });
                }}
                onMobileReplaceClick={() => {
                  this.goChangePhoneNum();
                }}
                busiSendOpId={showFPAYContract || SignContract ? 'mucfc.user.verifyIdentify.commonSendSms' : ''}
                busiSendArgs={showFPAYContract || SignContract ? this.getsignContractParam : {}}
                busiVerifyOpId={showFPAYContract || SignContract ? 'mucfc.user.bankCard.addBankCard' : ''}
                busiVerifyArgs={showFPAYContract || SignContract ? this.getsignContractParam : {}}
              />
            )}
            {
              <MUTradePasswordEncryptedWrap
                onTitleIconClicked={this.openPwdIconModal.bind(this)}
                scene="SCENE_REPAYMENT"
                verifyType="TRA"
                isOpened={showTradePwdDialog}
                needFpPayment
                clearAndDisableInput={clearAndDisableInput}
                title="同意并输入6位数字交易密码"
                leftText=""
                rightText="忘记数字密码"
                onOk={(msg, token) => { this.onPasswordOk(token); }}
                onClose={() => this.onPasswordClose()}
                onOverPwAttemptNum={() => {
                  Madp.showModal({
                    title: '温馨提示',
                    content: '密码输入错误次数超限，请稍候再试',
                    confirmText: '确认',
                    confirmColor: themeColor,
                    showCancel: false
                  });
                  dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.PwdOverTimes` });
                }}
                onNeedModifyPass={() => this.onNeedModifyPass()}
                onForgotPass={() => this.onForgotPass()}
                onVerifyFailed={(num) => {
                  dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.PasswordFail` });
                  this.verifyFailedNum(num);
                }}
                userId="yourUserId"
                beaconId="tradePasswordEncrypted"
              />
            }
            {<OnePassOrSmsCodeWrap
              title="同意并输入验证码"
              onTitleIconClicked={this.openIconModal.bind(this)}
              needOnePass={false}
              onRef={(ref) => { this.onePassOrSmsCodeRef = ref; }}
              scene={showFPAYContract || SignContract ? 'SCENE_SUPPLEMENT' : 'SCENE_REPAYMENT'}
              onExitProcess={() => {
                this.setState({ showSmsCodeDialog: false, curShowSmsCodeDialog: false, setContractZIndex: true });
              }}
              onOposcError={(errObj) => {
                // 补签银行发送动码失败，弹窗
                if ((showFPAYContract || SignContract) && ((errObj || {}).errCode === 105 || (errObj || {}).errCode === 106)) {
                  const nearestSelectedCard = getStore('selectedCard');
                  dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.SendCodeFail`, beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                  this.onePassOrSmsCodeRef && this.onePassOrSmsCodeRef.closeOposc && this.onePassOrSmsCodeRef.closeOposc();
                  this.setState({ showSmsCodeDialog: false, curShowSmsCodeDialog: false }, () => {
                    const that = this;
                    Madp.showModal({
                      title: '动码发送失败',
                      content: '请选择其他银行卡或绑定新卡',
                      confirmText: '我知道了',
                      confirmColor: themeColor,
                      showCancel: false,
                      success(res) {
                        if (res.confirm) {
                          dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${that.props.trackPrefix}.VerifyFailConfirm`, beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                          that.needReChooseCard = true;
                          that.setSelectCardsStatus(nearestSelectedCard.bankCardId, '验证失败', true, false);
                        }
                      }
                    });
                  });
                }
              }}
              onOposcExempt={() => {
                // updateSmsToken(token);
                // this.payApply(false);
              }}
              bizcontent={this.transRefNo}
              onOposcOk={(token, signChannelSuccess) => {
                console.log('ignore', signChannelSuccess);
                // 强制补签签约失败，关闭动码，弹窗引导换卡
                // const nearestSelectedCard = getStore('selectedCard');
                // dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.SignAuthCodeSuccess`, beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                // if (!signChannelSuccess && (nearestSelectedCard || {}).forceSignFlag === 'Y') {
                //   dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${this.props.trackPrefix}.SignFail`, beaconContent: { cus: { scene: 'BAOFU' } } });
                //   this.setState({ showSmsCodeDialog: false, curShowSmsCodeDialog: false }, () => {
                //     const that = this;
                //     Madp.showModal({
                //       title: '验证失败',
                //       content: '请选择其他银行卡或绑定新卡',
                //       confirmText: '我知道了',
                //       confirmColor: themeColor,
                //       showCancel: false,
                //       success(res) {
                //         if (res.confirm) {
                //           dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${that.props.trackPrefix}.VerifyFailConfirm`, beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                //           that.needReChooseCard = true;
                //           that.setSelectCardsStatus(nearestSelectedCard.bankCardId, '验证失败');
                //         }
                //       }
                //     });
                //   });
                //   return;
                // }
                this.onSmsVerifyOk(token);
              }}
              busiSendOpId={showFPAYContract || SignContract ? 'mucfc.user.verifyIdentify.commonSendSms' : ''}
              busiSendArgs={showFPAYContract || SignContract ? this.getsignContractParam : {}}
              busiVerifyOpId={showFPAYContract || SignContract ? 'mucfc.user.bankCard.addBankCard' : ''}
              busiVerifyArgs={showFPAYContract || SignContract ? this.getsignContractParam : {}}
              onSmsVerifyFail={() => {
                const nearestSelectedCard = getStore('selectedCard');
                dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.AuthCodeFail`, beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                Madp.showToast({ title: '验证失败，请稍后重试或更换支付方式', icon: 'none' });
              }}
              onMobileReplace={() => {
                dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.ResetPhone` });
                Util.externalJump('RESET_PHONE_URL');
              }}
            />}
            {<BiomtOrPasswOrLivenWrap
              title="同意并输入6位交易密码"
              clearAndDisableInput={clearAndDisableInput}
              onTitleIconClicked={this.openPwdIconModal.bind(this)}
              onRef={(ref) => { this.biomtOrPasswOrLivenRef = ref; }}
              isLivenAllowed={Madp.getChannel() !== '3CMBAPP'}
              scene="SCENE_REPAYMENT"
              // isBiomtAllowed={false}
              onSetTradePw={this.onForgotPass.bind(this)}
              onForgetPw={this.onForgotPass.bind(this)}
              onBopolError={() => { }}
              onCancel={() => { }}
              onLivenOverNum={() => { }}
              onBopolOk={(token) => {
                this.onPasswordOk(token);
              }}
              onTpOverNum={() => {
                dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.PwdOverTimes` });
              }}
              onTpVerifyFail={() => {
                // 失败回调。可以带参数，为剩余的验证次数
                dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.PasswordFail` });
              }}
              redirectUrl={this.bioRedirectUrl}
              needToastAfterVerifyOk={false}
              bioVerifyMethod={getWebviewName && getWebviewName() === 'wx' ? 'H5' : ''}
            />}

          </MUView>

        </MUDrawer >
        <MUView className={`repay-check-out-repay-way-drawer ${process.env.TARO_ENV === 'weapp' ? repayWayDrawerHeightClass : ''}`}>
          <BottomDrawer
            isOpen={showRepayWay}
            title="选择支付方式"
            onClose={() => { this.setState({ showRepayWay: false }); }}
            onTopCloseClick={() => {
              this.initPageShow();
              this.setState({ showRepayWay: false });
            }}
          >
            <MUView className="repay-check-out-space-line" />
            {showRepayCardContent ? (
              <RepayWay
                trackPrefix="repayment.expressRepay"
                isShow={showRepayCardContent}
                bankCards={alipayOnly ? [] : bankCards}
                closeWxAlipayFromCC={this.closeWxAlipayFromCC}
                isAlipayOnly={alipayOnly}
                payWayAlert={repayWayNoticeConfig}
                bankConfig={this.ledaBankConfig}
                isDueTagCust={this.isDueTagCust === 'Y'}
                overDue={this.isDueTagCust === 'Y' || hasOverdueFlag || this.billType === 'fee-reduce'}
                isOverDueUserFlag={isOverDueUserFlag || this.billType === 'fee-reduce'}
                billType={this.billType}
                select={(card, isUserClick = false) => this.userCardSelect(card, isUserClick)}
                transferGuide={() => this.onTransferGuide()}
                addBankCard={() => this.onAddBankCard()}
                amount={this.totalCanPayAmt}
                bankCardErrorStatusList={bankCardErrorStatusList}
                overPayAmtRepay={this.overPayAmt}
                remitTotalAmount={this.remitTotalAmount}
                overPayAmtRepayFlag={isShowOverRepayRow}
                availableAmount={this.xiaozhaoAmount}
                closeAlipay0100BillFlag={this.closeAlipay0100BillFlag}
                is0100Bills={() => this.is0100Bills}
                firstRepayConfig={this.repayConfig && this.repayConfig.firstRepayConfig}
                preRepayAmt={this.preRepayAmt}
                preRepayUsedAmount={this.preRepayUsedAmount}
                isRepayCheckout
                isShowDescTitle={false}
                modeOfPayment={modeOfPayment}
                firstRepayWay={firstRepayWay}
                expressScene={this.expressScene}
                guideZfbCard={this.supportZFBCardChannelFlag || this.supportOneBindCardChannelFlag}
              />
            ) : null}
          </BottomDrawer>
        </MUView>
        <MUModal
          beaconId="tipModal"
          className="tipModal"
          type="text"
          isOpened={openSmsCodeTip}
          title="关于个人信息处理规则说明"
          content={`在以下场景中，${fullName}需要核实您的验证码，以验证您的手机号码是否真实、有效：<br>（1）注册、登录招联平台或您需要销户、挂失、解除挂失；<br>（2）您需要申请授信、借款或使用招联借款用于消费；<br>（3）您需要修改您向招联提供的信息（如姓名、手机号码）；<br>（4）您需要启用、修改手势密码，或设置、修改数字交易密码，或挂失、解挂失；<br>（5）其他需要验证您手机号码是否真实、有效的场景。<br>验证码是您的重要个人信息，一旦泄露或处理不当可能危害您的财产安全，招联将根据法律法规要求并参照行业最佳实践为您的个人信息安全提供保障。`}
          confirmText="我知道了"
          onClose={this.closeIconModal.bind(this)}
          closeOnClickOverlay={false}
          onConfirm={this.closeIconModal.bind(this)}
        />
        <MUModal
          beaconId="pwdTipModal"
          className="pwdTipModal"
          type="text"
          isOpened={openPwdTip}
          title="关于个人信息处理规则说明"
          content={`在以下场景中，${fullName}需要收集、验证您输入的交易密码：<br>(1)您在招联设置或修改您的交易密码时；<br>(2)您在招联进行借款、消费、红包提现等交易时；<br>(3)您在招联查看或获取重要信息时，如账户解挂、合同下载等。<br>交易密码是您的重要个人信息，一旦泄露或处理不当可能危害您的财产安全，招联将根据法律法规要求并参照行业最佳实践为您的个人信息安全提供保障。`}
          confirmText="我知道了"
          onClose={this.closePwdIconModal.bind(this)}
          closeOnClickOverlay={false}
          onConfirm={this.closePwdIconModal.bind(this)}
        />
        <MUModal
          beaconId="errorTipModal"
          isOpened={showErrorTipModal}
          content={this.errorTipModalcontent}
          confirmText="更换支付方式"
          cancelText="取消"
          onCancel={() => {
            this.errorTipModalCancelClick();
          }}
          closeOnClickOverlay={false}
          onConfirm={() => {
            this.errorTipModalConfirmClick();
          }}
        />
        <RepayModal
          className="repay-transefer-modal"
          beaconId="transeferModal"
          isOpened={showTranseferModal}
          closeOnClickOverlay
          onClose={() => this.setState({ showTranseferModal: false })}
          confirmText="去查看"
          onConfirm={() => this.onTransferGuide(1)}
        >
          <MUView className="modal-content">
            <MUText>
              您已有一笔生效中的预约，请勿重复预约
            </MUText>
          </MUView>
        </RepayModal>
        <RepayModal
          title="温馨提示"
          className="repay-transefer-modal"
          isOpened={openTransferCouponTip}
          closeOnClickOverlay
          onClose={() => this.setState({ openTransferCouponTip: false })}
          confirmText="我知道了"
          onConfirm={() => this.setState({ openTransferCouponTip: false })}
        >
          <MUView className="modal-content">
            <MUText>
              银行卡转账还款不支持使用优惠券，若需要使用优惠券请选择其他还款方式
            </MUText>
          </MUView>
        </RepayModal>
        <LoadingDialog
          onRef={(ref) => { this.queryDialog = ref; }}
          countTime={countTime}
          isRepayCheckOut
          assignLoadingTitle={assignLoadingTitle}
        />
        <RepayContractModal
          showContractMUModal={showContractMUModal}
          onModalClose={() => {
            this.setState({ showContractMUModal: false });
            this.closeRepayErrorDailog('', true);
          }}
          onModalConfirm={() => this.signInContract()}
          capitalConstractNum={capitalConstractNum}
          contractInfos={this.contractInfos}
        />

        <MUDialog
          beaconId={'CantAffordDialog'}
          isOpened={isCantAffordDialogOpened}
          className={'repay-check-out-wx-repay-dialog'}
        >
          <MUView className="repay-check-out-wx-repay-dialog-icon">
            <MUIcon value={'tip'} size={80} />
          </MUView>
          <MUView className="repay-check-out-wx-repay-dialog-title">
            温馨提示
          </MUView>
          <MUView className="repay-check-out-wx-repay-dialog-desc">
            您选择的<MUText className="repay-check-out-wx-repay-dialog-highlight">{cantAffordDialogBankcardName}</MUText> 余额不足。
            {cantAffordDialogHintWxRepay
              ? <MUText>
                考虑到您的还款诉求，现为您提供 <MUText className="repay-check-out-wx-repay-dialog-highlight">微信支付</MUText> 还款方式，如有需要可选择使用
              </MUText>
              : <MUText>
                您可使用其他支付方式完成还款哦
              </MUText>}
          </MUView>
          <MUButton
            className="repay-check-out-wx-repay-dialog-btn"
            beaconId="WxRepayConfirmBtn"
            type="primary"
            onClick={() => this.setIsCantAffordDialogOpened(false)}
          >
            我知道了
          </MUButton>
          <MUView className="repay-check-out-wx-repay-dialog-countdown">{cantAffordDialogCountdown}s自动关闭</MUView>
        </MUDialog>
        {/* 有待激活的息费减免券弹窗 */}
        <MUModal
          className="offersTipModal"
          beaconId="OffersTipModal"
          type="text"
          isOpened={openWaitOffersTipModal}
          title="温馨提示"
          content="您有一笔息费减免申请单正在审批中，若继续还款则无法享受此优惠。"
          confirmText="暂不还款"
          cancelText="继续还款"
          onCancel={this.cancelWaitOffersPay.bind(this)}
          closeOnClickOverlay={false}
          onConfirm={this.closeWaitOffersTipModal.bind(this)}
        />
        {/* 长时间还款批扣处理弹窗 */}
        <AutoRepayModal
          isOpened={openAvoidDupPayModal}
          onConfirm={this.closeAvoidDupPayModal.bind(this)}
          onCancel={this.cancelAutoPayAndManualPay.bind(this)}
        />
        {/* 错误码弹窗 */}
        <RepayErrorDailog
          isOpened={showRepayErrorDailog}
          RepayErrorDailogParam={RepayErrorDailogParam}
        />
        {/* 错误码弹窗-在还款提交后超限额，置灰银行卡后，点击icon提示的弹窗 */}
        <RepayErrorDailog
          isOpened={showCeilingDialog}
          RepayErrorDailogParam={ceilingDialogParam}
        />
      </MUView>
    );
  }
}
