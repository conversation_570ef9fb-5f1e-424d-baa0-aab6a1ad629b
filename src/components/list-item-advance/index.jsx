/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import {
  MUView, MUIcon,
} from '@mu/zui';
import classNames from 'classnames';

import './index.scss';

export default class BillListItemAdvance extends Component {
  static propTypes = {
    item: PropTypes.object,
    billType: PropTypes.string, // 以后可能有用，目前只有省心分用，该组件无法勾选，点击则进行下一步
    infoClick: PropTypes.func,
    disabled: PropTypes.bool,
  }

  static defaultProps = {
    item: {},
    billType: '',
    infoClick: () => { },
    disabled: false,
  }

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    "styleIsolation" : "shared"
  }

  getItemOption(item) {
    const { billType } = this.props;
    let option = {};
    if (!Object.keys(item).length) return option;
    if (billType === 'advance-stageable') {
      option = {
        leftTitle: `${item.extendedRepayTotalAmt || ''}元`,
        leftDesc: '全部应还',
        leftFooter: `${this.loanDate}借款${item.installTotalAmt}元`,
        rightTitle: `每期应还: ${item.exetendedInstallAmt}元`,
        rightDesc: `由${item.installTotalCnt}期分至${item.extendedInstallCnt}期`,
      };
    } else if (billType === 'advance-unstageable') {
      // 不可省心分的 展示展期前的详情
      option = {
        leftTitle: `${item.repayTotalAmt || ''}元`,
        leftDesc: '全部应还',
        leftFooter: `${this.loanDate}借款${item.installTotalAmt}元`,
        rightTitle: `每期应还: ${item.installAmt}元`,
        rightDesc: '未参与省心分',
      };
    }
    return option;
  }

  get loanDate() {
    // eslint-disable-next-line react/destructuring-assignment
    const { loanDate = '' } = this.props.item;
    const year = loanDate.toString().slice(0, 4);
    const month = loanDate.toString().slice(4, 6);
    const day = loanDate.toString().slice(6, 8);
    return `${year}年${month}月${day}日`;
  }

  render() {
    // eslint-disable-next-line object-curly-newline
    const { item = {}, infoClick, billType, disabled } = this.props;
    const itemOption = this.getItemOption(item);
    return !Object.keys(item).length && !billType ? <MUView /> : (
      <MUView className="list-item-advance">
        <MUView className="content-block">
          <MUView className="content-block-title">
            <MUView className={classNames('left-title', { 'disabled-color': disabled })}>
              {/* eslint-disable-next-line react/jsx-one-expression-per-line */}
              {itemOption.leftTitle}
              {!disabled ? (
                <MUView onClick={() => infoClick(item)} beaconId="InfoClick">
                  <MUIcon className="text-info brand-text" value="info" size="16" />
                </MUView>
              ) : null}
            </MUView>
            <MUView
              className={classNames('right-title', { 'disabled-color': disabled })}
            >
              {itemOption.rightTitle}
            </MUView>
          </MUView>
          <MUView className="content-block-desc">
            {itemOption.leftDesc ? (
              <MUView className={classNames('left-desc', { 'disabled-color': disabled })}>
                {itemOption.leftDesc}
              </MUView>
            ) : null}
            {itemOption.rightDesc ? (
              <MUView className={classNames('right-desc', { 'disabled-color': disabled })}>
                {itemOption.rightDesc}
              </MUView>
            ) : null}
          </MUView>
          <MUView className="content-block-footer">
            <MUView className={classNames('content-block-sub-left', { 'disabled-color': disabled })}>
              {itemOption.leftFooter}
            </MUView>
            <MUView className={classNames('content-block-sub-right', { 'disabled-color': disabled })}>
              {itemOption.rightFooter}
            </MUView>
          </MUView>
        </MUView>
        <MUView className="split" />
      </MUView>
    );
  }
}
