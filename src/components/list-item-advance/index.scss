@import '../../components/weapp/index.scss';

.list-item-advance {
  width: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  font-size: 25px;
  color: #adadad;
  position: relative;

  .content-block {
    box-sizing: border-box;
    padding: 30px 30px 20px;
    width: 100vw;

    &-title,
    &-desc,
    &-footer {
      display: flex;
      justify-content: space-between;
      position: relative;
    }

    &-title {
      align-items: baseline;

      .left-title {
        font-size: 36px;
        color: #333;
        display: flex;

        .text-info {
          margin-left: 5px;
          margin-top: -7px;
        }
      }

      .right-title {
        color: #333;
        font-size: 28px;
      }
    }

    &-desc {
      margin-top: 5px;
    }

    &-footer {
      margin-top: 12px;
    }
  }

  .split {
    width: 100%;
    height: 1px;
    border-top: 1px solid #e6e6e6;
    position: absolute;
    bottom: 0;
    right: 0;
    width: calc(100% - 30px);
  }

  &:last-child {
    .split {
      width: 0%;
    }
  }

  .disabled-color {
    color: #adadad !important;
  }
}
