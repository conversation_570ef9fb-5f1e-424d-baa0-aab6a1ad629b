/* eslint-disable react/no-unused-prop-types */
/* eslint-disable react/forbid-prop-types */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-param-reassign */
/* eslint-disable react/sort-comp */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUButton, MUModal, MUImage, MUText
} from '@mu/zui';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import infoIcon from '@components/assets/img/icon_info.png';
import couponIcon from '@components/assets/img/icon_coupon.png';
import PropTypes from 'prop-types';
import './index.scss';

export default class RetainDialog extends Component {
  static propTypes = {
    showDialog: PropTypes.bool,
    confirmText: PropTypes.string,
    cancelText: PropTypes.string,
    renderMainContent: PropTypes.element,
    subContent: PropTypes.string,
    type: PropTypes.string,
    beaconType: PropTypes.string,
    onConfirm: PropTypes.func,
    onCancel: PropTypes.func,
    billInfo: PropTypes.object
  }

  static defaultProps = {
    showDialog: false,
    confirmText: '返回',
    cancelText: '继续还款',
    renderMainContent: '',
    subContent: '',
    type: '',
    beaconType: '',
    onConfirm: () => { },
    onCancel: () => { },
    billInfo: {
      repayStayType: '',
      totalOrderCount: '',
      prepayFeeSum: '',
      interestDiscountAmtSum: ''
    }
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    "styleIsolation" : "shared"
  }

  componentWillReceiveProps(nextProps) {
    const currentShow = this.props.showDialog;
    if (nextProps.showDialog && nextProps.showDialog !== currentShow) {
      dispatchTrackEvent({ event: EventTypes.PO, beaconId: `Repayment.BillListAll.${nextProps.beaconType}Retain.Show` });
    }
  }

  onDialogConfirm() {
    const { onConfirm, beaconType } = this.props;
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `Repayment.BillListAll.${beaconType}Retain.Confirm` });
    if (typeof onConfirm === 'function') {
      onConfirm();
    }
  }

  onDialogCancel() {
    const { onCancel, beaconType } = this.props;
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `Repayment.BillListAll.${beaconType}Retain.Cancel` });
    if (typeof onCancel === 'function') {
      onCancel();
    }
  }

  get dialogHead() {
    const { type } = this.props;
    if (type === 'coupon') return couponIcon;
    return infoIcon;
  }

  render() {
    const {
      showDialog, confirmText, cancelText, subContent, billInfo
    } = this.props;
    const showType = String(billInfo.repayStayType);
    return (
      <MUModal
        beaconId="RetainDialog"
        isOpened={showDialog}
        closeOnClickOverlay={false}
      >
        <MUView className="retain-dialog">
          <MUImage src={this.dialogHead} className="retain-dialog-head">ICON</MUImage>
          <MUView className="retain-dialog-content">
            <MUView className="main">
              {
                showType === '1' && (
                  <MUView>
                    {`您有${billInfo.totalOrderCount}笔借款暂未到期，若提前还款，根据合同约定将收取`}
                    <MUText className="amount-color">
                      {billInfo.prepayFeeSum}
                      元
                    </MUText>
                    违约金，建议按期还款，减轻还款压力
                  </MUView>
                )
              }
              {
                showType === '2' && (
                  <MUView>
                    {`您有${billInfo.totalOrderCount}笔借款暂未到期，若提前还款，将无法继续享受利率折扣，建议按期还款，享受剩余`}
                    <MUText className="amount-color">
                      {billInfo.interestDiscountAmtSum}
                      元
                    </MUText>
                    利息优惠
                  </MUView>
                )
              }
              {
                showType === '3' && (
                  <MUView>{`您有${billInfo.totalOrderCount}笔借款暂未到期，若提前还款，将无法使用还款券，建议按期还款，享受最多${billInfo.awardTotalAmt}元的还款优惠（*还款券使用规则请以券面为准）`}</MUView>
                )
              }
              {
                (showType === '4' || showType === '5') && (
                  <MUView>{`您有${billInfo.totalOrderCount}笔借款暂未到期，建议按期还款，减轻还款压力`}</MUView>
                )
              }
            </MUView>
            <MUView className="sub">{subContent}</MUView>
          </MUView>
          <MUView className="retain-dialog-btn">
            <MUButton type="primary" className="retain-dialog-btn-confirm" onClick={() => this.onDialogConfirm()} beaconId="RetainConfirm">{confirmText}</MUButton>
            <MUButton className="retain-dialog-btn-cancel" onClick={() => this.onDialogCancel()} beaconId="RetainCancel">{cancelText}</MUButton>
          </MUView>
        </MUView>
      </MUModal>
    );
  }
}
