import { Component } from '@tarojs/taro';
import {
  MUView,
  MUDialog,
  MUButton,
  MURichText
} from '@mu/zui';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

export default class repayErrorDailog extends Component {
  config = {
    styleIsolation: 'shared'
  }


  render() {
    const {
      isOpened, RepayErrorDailogParam
    } = this.props;
    const { title, contentText, confirmText, confirmFn, closeDailogText, closeDailogFn } = RepayErrorDailogParam || {};
    return (
      <MUView className="repayErrorDailog">
        <MUDialog isOpened={isOpened}>
          <MUView className="repayErrorDailog-title">{title}</MUView>
          <MUView className="repayErrorDailog-content">
            <MURichText nodes={contentText} />
          </MUView>
          {confirmText
            && <MUButton className="repayErrorDailog-confirmText" onClick={confirmFn}>
              {confirmText}
            </MUButton>}
          {closeDailogText
            && <MUView className="repayErrorDailog-closeDailogText" onClick={closeDailogFn}>
              {closeDailogText}
            </MUView>}
        </MUDialog>
      </MUView>
    );
  }
}
