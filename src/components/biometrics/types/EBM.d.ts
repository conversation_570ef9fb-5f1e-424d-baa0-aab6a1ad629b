import { ComponentClass } from 'react'

import MUComponent from './base'

export interface EBMProps extends MUComponent {
  /* 生物识别类型。1登录 2交易 */
  biometricScene: number;
  /* 用户id */
  userId: string;
  /* 点击数字密码键盘右侧文本“忘记数字密码”回调 */
  onClickTpRightText: Function;
  /* 超过数字密码失败次数回调函数 */
  onTpOverNum?: Function;
  /* 输入交易密码错误回调 */
  onTpVerifyFailed?: Function;
  /* 关闭数字键盘回调函数 */
  onTpClose?: Function;
  /* 数字密码验证成功回调函数 */
  onTpOk?: Function;
  /* 活体次数超限回调函数 */
  onAisOverNum: Function;
  /* 活体用户取消回调函数 */
  onAisCancel: Function;
  /* 活体认证成功回调函数 */
  onAisOk?: Function;
  /* 开启生物识别过程用户取消回调函数 */
  onEnableCancel?: Function;
  /* 发现生物识别库发生变化回调函数 */
  onBioDbChanged?: Function;
  /* 发现机器无指纹或面容id回调函数 */
  onBioLacking?: Function;
  /* 验证生物识别超过次数回调函数 */
  onVerifyOverNum: Function;
  /* 数字密码验证或活体验证或开启生物识别过程异常回调函数 */
  onError: (err: ErrorInfo) => void;
  /* 开启生物识别过程成功回调函数 */
  onEnableOk: Function;
}

export interface EBMState {
  isTpOpened: boolean;
  tpToken: string;
  tpKey: number;
}

export interface IsSupportedFrontEndRes {
  fingerOrFace: number;
  isHacked: number;
  tech?: string;
}

export interface IsSupportedRes {
  fingerOrFace: number;
  isSupported: boolean;
  isHacked: number;
  isBlackList: boolean;
  tech?: string;
}

export interface VerifyProps {
  /* 生物识别类型。1登录 2交易 */
  biometricScene: number;
  /* 业务场景 */
  scene: string;
  /* 用户id */
  userId: string;
  /* 业务方指定的加签内容 */
  bizcontent?: object;
  /* 指纹验证提示语 */
  prompt?: string;
  /* 指纹验证框非首次失败时的按钮文案 */
  buttonPrompt?: string;
  /* 获取到是否支持生物识别回调函数 */
  onGetIsSupportedRes;
  /* 生物识别验证获取到结果回调函数 */
  onShowVerifyModal?: Function;
  /* 生物识别验证过程用户点击取消（即首次失败后的取消按钮）回调函数 */
  onVerifyCancel?: Function;
  /* 发现生物识别库发生变化回调函数 */
  onBioDbChanged?: Function;
  /* 发现机器无指纹或面容id回调函数 */
  onBioLacking?: Function;
  /* 生物识别验证过程用户点击启用数字密码（即非首次失败后的取消按钮）回调函数 */
  onAlternativeVerifyOption?: Function;
  /* 验证生物识别超过次数回调函数 */
  onVerifyOverNum: Function;
  /* 验证生物识别过程异常回调函数 */
  onError: (err: ErrorInfo) => void;
  /* 生物识别验证过程成功回调函数 */
  onVerifyOk: Function;
}

export interface onePassProps {
  /* 场景 */
  scene: string;
  /* 用户手机号。登录场景必传 */
  phoneNum?: string;
  /* 验签内容。例：`a=x&b=20` | string */
  bizContent: string;
  /* 本机验证异常回调函数 */
  onError: (err: ErrorInfo) => void;
  /* 本机验证成功回调函数 */
  onOk: Function;
}

declare const EBMComponent: ComponentClass<EBMProps>

export default EBMComponent

export interface OPMProps {
  
}

export interface OPMState {
  carrierUrl: string,
}

export interface ErrorInfo {
  errCode: number;
  errMsg: string;
}

export interface MsgInfo {
  type: string;
  args?: object;
}