// export { default as <PERSON><PERSON><PERSON>ie<PERSON> } from './demo';

import { VerifyProps, onePassProps } from './EBM'

declare namespace MUBioMetrics {
  class EnableBiometrics {}
  function checkIsSupported(biometricScene: number, needCheckMaxPayment?: boolean)
  function disableBiometrics(biometricScene: number)
  function checkIsEnabled(biometricScene: number)
  function verifyBiometrics(options: VerifyProps)
  // function onePassMuApp(onePassMuAppFuncs: object)
  // function onePassH5(onePassH5Funcs: object)
  // function onePassMnp {onePassMnpFuncs: object}
}
  
export default MUBioMetrics;

