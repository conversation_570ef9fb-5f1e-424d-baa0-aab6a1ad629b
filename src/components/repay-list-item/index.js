/* eslint-disable eqeqeq */
/* eslint-disable import/no-mutable-exports */
/* eslint-disable react/jsx-curly-brace-presence */
/* eslint-disable react/jsx-indent */
/* eslint-disable react/jsx-closing-tag-location */
/* eslint-disable react/jsx-wrap-multilines */
/* eslint-disable indent */
/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/no-unused-prop-types */
import { Component } from '@tarojs/taro';
import { __decorate } from 'tslib';
import { View, Image, Text } from '@tarojs/components';
import { track, EventTypes } from '@mu/madp-track';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import _isFunction from 'lodash/isFunction';
import {
  MUButton
} from '@mu/zui';
import './index.scss'

let RepayListItem = class RepayListItem extends Component {
  static propTypes = {
    renderNote: PropTypes.element,
    // disabled: PropTypes.bool,
    renderTitle: PropTypes.element,
    thumb: PropTypes.string,
    contentStyle: PropTypes.string,
    onClick: PropTypes.func,
    isSwitch: PropTypes.bool,
    hasBorder: PropTypes.bool,
    switchColor: PropTypes.string,
    trackedBeaconId: PropTypes.string,
    switchIsCheck: PropTypes.bool,
    disabledArrow: PropTypes.bool,
    extraText: PropTypes.string,
    extraThumb: PropTypes.string,
    onSwitchChange: PropTypes.func,
    arrow: PropTypes.string,
    thumbStyle: PropTypes.bool,
    content: PropTypes.string,
    renderContent: PropTypes.element,
    renderExtraText: PropTypes.element,
    extraType: PropTypes.oneOf(['dot', 'text', 'button']),
    iconInfo: PropTypes.shape({
      size: PropTypes.number,
      value: PropTypes.string,
      color: PropTypes.string,
      prefixClass: PropTypes.string,
      customStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),
      className: PropTypes.oneOfType([PropTypes.array, PropTypes.string])
    }),
    onBtnClick: PropTypes.func,
    cardSupport: PropTypes.bool
  }

  static defaultProps = {
    renderNote: '',
    // disabled: false,
    renderTitle: '',
    thumb: '',
    trackedBeaconId: '',
    isSwitch: false,
    hasBorder: true,
    switchColor: '#6190E8',
    switchIsCheck: false,
    extraType: 'text',
    extraText: '',
    extraThumb: '',
    iconInfo: { value: '' },
    arrow: 'mu-icon-chevron-right',
    disabledArrow: false,
    content: '',
    contentStyle: '',
    renderContent: null,
    renderExtraText: null,
    thumbStyle: false,
    onSwitchChange: () => { },
    onClick: () => { },
    onBtnClick: () => { },
    cardSupport: true
  }

  handleClick(evt) {
    const { onClick } = this.props;
    if (_isFunction(onClick)) {
      onClick(evt);
    }
  }

  // 仅对extraType为button时
  handleBtnClick(evt) {
    const { onBtnClick } = this.props;
    if (_isFunction(onBtnClick)) {
      onBtnClick(evt);
    }
  }

  render() {
    const {
      arrow, content, thumb, iconInfo,
      // disabled,
      cardSupport,
      extraType, extraText, hasBorder, disabledArrow, trackedBeaconId,
      contentStyle, thumbStyle
      // extraThumb,
      // switchColor,
    } = this.props;
    const rootClass = classNames('repay-list-list__item repay-list-list__item--multiple', {
      'repay-list-list__item--thumb': thumb,
      // 'repay-list-list__item--disabled': disabled,
      'repay-list-list__item--disabled': !cardSupport,
      'repay-list-list__item--no-border': !hasBorder
    }, this.props.className);
    const iconClass = classNames(iconInfo.prefixClass || 'mu-icon', {
      [`${iconInfo.prefixClass || 'mu-icon'}-${iconInfo.value}`]: iconInfo.value
    }, iconInfo.className);
    let extraContent = null;
    switch (extraType) {
      case 'text':
        {
          extraContent = <View className="item-extra__info">{extraText}</View>;
          break;
        }
      case 'dot':
        {
          extraContent = <View className="item-extra__info">
            <View className="item-extra__dot" />
          </View>;
          break;
        }
      case 'button':
        {
          extraContent = <View className="item-extra__info">
            <MUButton
              type="primary"
              circle
              className="mu-button--list"
              onClick={this.handleBtnClick.bind(this)}
              beaconId="MUButton"
              parentId={trackedBeaconId}
            >
              {extraText.slice(0, 4)}
            </MUButton>
          </View>;
          break;
        }
      default:
        {
          break;
        }
    }
    return <View className={`${rootClass} repay-list-item`} onClick={this.handleClick.bind(this)}>
      <View className="repay-list-list__item-container">
        {thumb && <View className={
          classNames(
            'repay-list-list__item--thumb',
            'item-thumb', 'item-thumb-lg',
            { 'add-thumb': !!thumbStyle }
          )
        }
        >
          <Image className="item-thumb__info" mode="scaleToFill" src={thumb} />
        </View>}
        {iconInfo.value && <View className="repay-list-list__item-icon item-icon">
          <View className={iconClass} />
        </View>}
        <View className="repay-list-list__item-content item-content" style={contentStyle || ''}>
          <View className="item-content__info">
            <View className="item-content__info-title">{this.props.renderTitle}</View>
            {<View className="item-content__info-note">{this.props.renderNote}</View>}
          </View>
        </View>
        {content && <View className="repay-list-list__item-mid item-mid">{content}</View>}
        {this.props.renderContent}
        <View className="repay-list-list__item-extra item-extra">
          {extraContent}
          {this.props.renderExtraText}

          {arrow && !disabledArrow && extraType !== 'button' ? <View className="item-extra__icon">
            <Text className={classNames('mu-icon', 'item-extra__icon-arrow', arrow)} />
          </View> : null}
        </View>
      </View>
    </View>;
  }
};
__decorate([track({ event: EventTypes.BC })], RepayListItem.prototype, 'handleClick', null);
__decorate([track({ event: EventTypes.BC })], RepayListItem.prototype, 'handleClick', null);
RepayListItem = __decorate([track((props) => {
  const { beaconId, content, beaconContent } = props;
  if (beaconContent && beaconContent.cus) {
    return {
      beaconId,
      beaconContent: { cus: { itemContent: content, ...beaconContent.cus } },
      uiType: 'RepayListItem'
    };
  }
  return {
    beaconId,
    beaconContent: { cus: content == '' ? {} : { itemContent: content } },
    uiType: 'RepayListItem'
  };
})], RepayListItem);
export default RepayListItem;
