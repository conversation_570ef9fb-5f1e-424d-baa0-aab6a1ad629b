@import '../../components/weapp/index.scss';

.repay-list-list {
  &__item-container {
    display: flex;
    align-items: center;
    -webkit-box-align: center;

    .item-icon {
      margin-right: $mu-list-item-horizontal-padding;
      font-size: $mu-list-icon-size;
      display: flex;
      align-items: center;
    }
  }

  &__item {
    padding: $spacing-v-lg $mu-list-spacing-h-lg;
    position: relative;
    box-sizing: border-box;
    color: $mu-list-text-color;
    font-size: $font-size-h2;
    transition: $color-grey-5 0.3s;

    &:active {
      background-color: $color-grey-5;
    }

    // &:not(:last-child) {
    //   position: relative;

    &::after {
      content: '';
      position: absolute;
      transform-origin: center;
      box-sizing: border-box;
      pointer-events: none;

      top: auto;
      left: $spacing-h-lg;
      right: 0;
      bottom: 0;
      transform: scaleY(0.5);
      /* stylelint-disable-next-line */
      border-bottom: 1PX solid $color-border-base;
    }

    // }

    &--thumb {
      .item-thumb {
        margin-right: $mu-list-item-horizontal-padding;
        width: $mu-list-thumb-size;
        height: $mu-list-thumb-size;

        &__info {
          width: 100%;
          height: 100%;
        }
      }

      .item-thumb-lg {
        width: $mu-list-thumb-size-lg;
        height: $mu-list-thumb-size-lg;

        &__info {
          width: 100%;
          height: 100%;
        }
      }

      .add-thumb {
        width: 48px;
        height: 48px;
        padding: 16px;
        margin-top: 7px;
        margin-bottom: 7px;
      }
    }


    &--no-border::after {
      content: initial;
      border: none !important;
    }

    .item-content {
      &__info {
        // margin-top: 5px;
        min-width: 140px;

        &-title {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          color: inherit;
          font-size: inherit;
          line-height: 1.6;
        }

        &-note {
          margin-top: 5px;
          color: $mu-list-content-note-color;
          font-size: $font-size-list;
          line-height: 1.6;
        }
      }
    }

    .item-mid {
      font-size: $font-size-h2;
      color: #333;
    }

    .item-extra {
      position: absolute;
      right: 0.64rem;
      // position: relative; lll
      display: flex;
      align-items: center;
      justify-content: flex-end;
      max-height: 50px;

      &__info,
      &__icon {
        display: inline-block;
      }

      &__info {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        // padding-top: 5px;
        max-width: 100%;
        color: $mu-list-extra-color;
        padding-right: 32px;
        font-size: $font-size-h2;
        vertical-align: middle;
        box-sizing: border-box;

        &:last-child {
          padding-right: 0;
        }
      }

      &__dot {
        // text-align: top;
        width: 16px;
        height: 16px;
        border-radius: 100%;
        background-color: #FE5A5F;
      }

      &__icon {
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        position: absolute;
        display: flex;
        align-items: center;

        &-arrow {
          color: $mu-list-arrow-color;
          font-size: $font-size-list;
          width: 30px;
          background-position-y: center;
        }
      }
    }


    &--disabled {
      color: #A6A6A6 !important;

      .item-content {
        &__info {
          &-note {
            color: #A6A6A6 !important;
          }
        }
      }

      .repay-list-list__item--thumb .item-thumb__info {
        opacity: 0.4;
      }

      .at-list__item-container {
        opacity: $opacity-disabled;
      }

      &:active {
        background-color: transparent !important;
      }
    }
  }

  &__item-content {
    min-width: 164px;
    width: 70%;
    margin-right: $spacing-h-md;
  }

  &__item-extra {
    // @include flex(0, 0, $mu-list-extra-width);

    // max-width: $mu-list-extra-width;
    flex: 1;

    .mu-icon {
      display: inline-block;
      /* stylelint-disable-next-line */
      font-family: iconfont;
      font-style: normal;
      font-weight: 400;
      font-variant: normal;
      text-transform: none;
      text-rendering: auto;
      line-height: 1;
      -webkit-font-smoothing: antialiased;
      vertical-align: middle;
    }

    .at-list__item .item-extra__icon-arrow {
      color: #ccc;
      font-size: 0.55467rem;
      width: 0.64rem;
      background-position-y: center;
    }

    .mu-icon-arrow-right::before {
      content: "\E603";
    }
  }
}

.repay-payway-list-transferGuide {
  margin-top: 20px;
  background: #fff;

  .item-content__info {
    width: 400px;
  }

  .repay-list-list__item-content {
    width: 50%;
  }

  .title-banner-content {
    margin-top: 10px;
    font-size: 22px;
    color: #FF8844;
    line-height: 1.2;
    /* stylelint-disable-next-line */
    border: 1PX solid #FF8844;
    border-radius: 2px;
    padding: 4px;
    display: inline-block;
  }
}
