import {
  MUView,
  MUModal,
} from '@mu/zui';

/**
 * 小招荷包内容详情弹窗
 */
export default function OverpayDetailModal({
  isOpened,
  preRepayAmt,
  remitTotalAmount,
  overPayAmtRepay,
  onConfirm,
  isRepayCheckOut = false,
}) {
  return (
    <MUModal
      beaconId="overPayAmtDetail-modal-show"
      isOpened={isOpened}
      closeOnClickOverlay={false}
    >
      <MUView className="overPayAmtDetail-modal">
        <MUView className="overPayAmtDetail-title">小招荷包</MUView>
        {Number(preRepayAmt) > 0 ? (
          <MUView className="overPayAmtDetail-item">
            <MUView>预还款金额</MUView>
            <MUView>
              {preRepayAmt}元
            </MUView>
          </MUView>
        ) : null}
        {Number(remitTotalAmount) > 0 ? (
          <MUView className="overPayAmtDetail-item">
            <MUView>转账处理中的金额</MUView>
            <MUView>
              {remitTotalAmount}元
            </MUView>
          </MUView>
        ) : null}
        {(isRepayCheckOut && Number(overPayAmtRepay) - Number(remitTotalAmount) > 0) || !isRepayCheckOut ? (
          <MUView className="overPayAmtDetail-item">
            <MUView>溢缴款余额</MUView>
            <MUView>
              {(Number(overPayAmtRepay) - Number(remitTotalAmount)).toFixed(2)}元
            </MUView>
          </MUView>
        ) : null}
        <MUView
          className="overPayAmtDetail-but"
          beaconId="overPayAmtDetail-modal-click"
          onClick={() => onConfirm && onConfirm()}
        >我知道了</MUView>
      </MUView>
    </MUModal>
  );
}