import {
  MUView,
  MUModal,
} from '@mu/zui';

/**
 * 小招荷包提示弹窗
 */
export default function OverpayTipsModal({
  isOpened,
  onConfirm,
}) {
  return (
    <MUModal
      beaconId="overPayAmtInfo-modal-show"
      isOpened={isOpened}
      closeOnClickOverlay={false}
    >
      <MUView className="overPayAmtDetail-modal">
        <MUView className="overPayAmtDetail-title">小招荷包</MUView>
        <MUView className="overPayAmtDetail-modal-text">
          荷包中的零钱是你向招联超额还款产生的溢缴款或使用招联贷款后的商家退款。在还款时自动为你优先抵扣。
        </MUView>
        <MUView
          className="overPayAmtDetail-but"
          beaconId="overPayAmtInfo-modal-click"
          onClick={() => onConfirm && onConfirm()}
        >我知道了</MUView>
      </MUView>
    </MUModal>
  );
}
