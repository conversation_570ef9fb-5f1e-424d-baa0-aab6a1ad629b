/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import { useState } from '@tarojs/taro';
import {
  MUView,
  MUText,
  MUIcon,
  MUImage,
} from '@mu/zui';
import RepayListItem from '@components/repay-list-item';
import OverpayTipsModal from '@components/overpay-modal/tips-modal';
import OverpayDetailModal from '@components/overpay-modal/detail-modal';
import './index.scss';

const tipImg = 'https://file.mucfc.com/zlh/3/0/202305/2023051820232141dca4.png';
const xiaozhao = 'https://file.mucfc.com/zlh/3/0/202305/2023051820232172f029.png';

/**
 * 小招荷包（溢缴款）
 */
export default function OverPayRow({
  uncheck,
  overPayAmtRepayFlag,
  overPayAmtRepay = '0.00',
  remitTotalAmount = '0.00',
  availableAmount = '0.00',
  preRepayAmt = '0.00',
  preRepayUsedAmount = '0.00',
  isRepayCheckOut = false,
  onTipImgClick = () => { },
}) {
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showInforModal, setShowInforModal] = useState(false);
  return (
    <MUView className={`overpay-row ${isRepayCheckOut ? 'repay-check-out-overpay-row' : ''}`}>
      {overPayAmtRepayFlag && (
        <RepayListItem
          renderTitle={(
            <MUView>
              <MUView className={`title-xiaozhao-tips ${isRepayCheckOut ? 'repay-check-out-tips' : ''}`}>
                小招荷包
                <MUImage
                  src={tipImg}
                  className="title-xiaozhao-tips-icon"
                  onClick={() => {
                    if (isRepayCheckOut) {
                      onTipImgClick && onTipImgClick();
                    } else {
                      (Number(remitTotalAmount) > 0 || Number(preRepayAmt) > 0) ? setShowDetailModal(true) : setShowInforModal(true);
                    }
                  }}
                />
              </MUView>
            </MUView>
          )}
          renderNote={<MUView className={`${isRepayCheckOut ? 'repay-check-out-note' : ''}`}>还款金额优先从荷包中扣除</MUView>}
          disabledArrow
          renderExtraText={!isRepayCheckOut ? (
            <MUView>
              <MUText className="title-xiaozhao-tips-overPayAmt">
                {(Number(availableAmount) + Number(preRepayUsedAmount)).toFixed(2)}元
              </MUText>
              <MUIcon
                className={uncheck ? 'title-xiaozhao-check' : 'brand-text title-xiaozhao-check'}
                value={uncheck ? 'unchecked' : 'checked'}
                size="20"
              />
            </MUView>
          ) : (
            <MUView className="repay-check-out-overPayAmt-text">
              抵扣
              <MUText className="title-xiaozhao-tips-overPayAmt repay-check-out-overPayAmt-amount">
                {(Number(availableAmount) + Number(preRepayUsedAmount)).toFixed(2)}
              </MUText>
              元
            </MUView>
          )}
          contentStyle="width: 50%"
          thumb={xiaozhao}
          beaconId="xiaozhaoWay"
        />
      )}
      <OverpayTipsModal
        isOpened={showInforModal}
        onConfirm={() => setShowInforModal(false)}
      />
      <OverpayDetailModal
        isOpened={showDetailModal}
        preRepayAmt={preRepayAmt}
        remitTotalAmount={remitTotalAmount}
        overPayAmtRepay={overPayAmtRepay}
        onConfirm={() => setShowDetailModal(false)}
      />
    </MUView>
  );
}
