/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-param-reassign */
/* eslint-disable react/sort-comp */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUIcon, MUButton
} from '@mu/zui';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import './index.scss';

const themeColor = Util.getThemeColor(channelConfig.theme);

export default class RepayFooter extends Component {
  static propTypes = {
    mainDesc: PropTypes.string,
    subDesc: PropTypes.string,
    className: PropTypes.string,
    onConfirm: PropTypes.func,
    btnText: PropTypes.string,
    showChecker: PropTypes.bool,
    isChecked: PropTypes.bool,
    btnDisabled: PropTypes.bool,
    onCheckerClicked: PropTypes.func,
  }

  static defaultProps = {
    mainDesc: '',
    subDesc: '',
    btnText: '',
    className: '',
    onConfirm: () => { },
    showChecker: true,
    isChecked: false,
    btnDisabled: false,
    onCheckerClicked: () => { },
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    "styleIsolation" : "shared"
  }

  render() {
    const {
      mainDesc, subDesc, onConfirm, btnText, showChecker, isChecked, onCheckerClicked, btnDisabled, className
    } = this.props;
    const margin = (process.env.TARO_ENV !== 'h5') ? '10px' : '';
    return (
      <MUView className={className ? `repay-footer ${className}` : 'repay-footer'}>
        {showChecker ? (
          <MUView
            className="repay-footer-checker"
            style={{ marginTop: margin }}
            beaconId="Checker"
            onClick={onCheckerClicked}
          >
            {isChecked ? <MUIcon value="checked" size={24} className="brand-text" /> : <MUIcon value="unchecked" size={24} color="#E5E5E5" />}
          </MUView>
        ) : null}
        <MUView className={subDesc ? 'repay-footer-desc-block' : 'repay-footer-desc-block single-line'}>
          <MUView className="repay-footer-main-desc">
            {mainDesc}
          </MUView>
          <MUView className="repay-footer-sub-desc">
            {subDesc}
          </MUView>
        </MUView>
        <MUView className="repay-footer-btn">
          <MUButton
            className="theme-background-color"
            customStyle={`background: ${themeColor}`}
            onClick={onConfirm}
            beaconId="Confirm"
            disabled={btnDisabled}
          >
            {btnText}
          </MUButton>
        </MUView>
      </MUView>
    );
  }
}
