@import '../../components/weapp/index.scss';

.repay-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  height: 120px;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);


  &-checker {
    //float: left;
    margin-right: 10px;
    margin-top: 2px;
    width: auto;
    padding-left: 20px;
  }

  &-desc-block {
    //float: left;
    margin-top: 20px;

    &.single-line {
      margin-top: 35px;

      .repay-footer-main-desc {
        font-size: 30px;
      }
    }
  }

  &-main-desc {
    font-size: 28px;
  }

  &-sub-desc {
    font-size: 28px;
    color: #adadad;
  }

  &-btn {
    //float: right;
    height: 120px;
    width: 220px;

    .at-button {
      height: 100%;
      color: #fff;
      border-radius: 0;
    }
  }

  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
