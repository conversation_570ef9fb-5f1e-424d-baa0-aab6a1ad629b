@import '~@mu/zui/dist/style/mixins/index.scss';
@import '~@mu/zui/dist/style/variables/default.scss';

.overpay-drawer {
  display: flex;
  flex: 1;
  height: 100%;
  flex-direction: column;
  background: #f3f3f3;
  &__top {
    position: fixed;
    top: 0;
    flex-direction: row;
    height: 100px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #FFFFFF;
    &__holder {
      padding-left: 36px;
      height: 32px;
      width: 32px;
    }
    &__title {
      font-size: $font-size-h2;
      color: #333333;
      font-weight: 700;
    }
    &__close {
      margin-right: 30px;
      height: 32px;
      width: 32px;
      color: #A6A6A6;
    }
  }
  &__content {
    margin-top: 100px;
    padding: 30px 40px 60px;
    height: 100%;
    background: #fff;
    .content {
      &__title {
        font-size: 28px;
        line-height: 36px;
        color: #3d3d3d;
      }
      &__amount {
        margin-top: 40px;
        font-size: 32px;
        line-height: 36px;
        color: #333;
        font-weight: 600;
      }
      &__detail {
        margin-top: 56px;
        padding: 30px;
        border-radius: 16px;
        background: #f3f3f3;
        font-size: 26px;
        line-height: 38px;
        color: #808080;
        &--multiple {
          margin-top: 36px;
        }
        &__item {
          padding: 0 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 26px;
          line-height: 52px;
          font-weight: 400;
          color: #333;
        }
      }
    }
  }
  &__point {
    color: #FF8844;
  }
}
.at-drawer__content {
  overflow: hidden;
  border-radius: 16px 16px 0 0;
  min-height: 40vh;
}
