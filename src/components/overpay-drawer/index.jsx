/**
 * @description: 小招荷包说明
 */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUDrawer, MUView, MUImage, MUText
} from '@mu/zui';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';

const icClose = 'https://file.mucfc.com/zlh/3/0/202305/202305182019591e0b87.png';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

export default class OverpayDrawer extends Component {
  static propTypes = {
    showOverpayDrawer: PropTypes.bool,
    title: PropTypes.string,
    overpayBalance: PropTypes.string,
    remitTotalAmount: PropTypes.string,
    preRepayAmt: PropTypes.string,
    onClose: PropTypes.func,
    onTopCloseClick: PropTypes.func,
  };

  static defaultProps = {
    showOverpayDrawer: false,
    title: '',
    overpayBalance: '',
    remitTotalAmount: '',
    preRepayAmt: '',
    onClose: () => {},
    onTopCloseClick: () => {},
  };

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  render() {
    const {
      showOverpayDrawer, title, overpayBalance, remitTotalAmount, preRepayAmt,
      onClose, onTopCloseClick
    } = this.props;
    let showType = '';
    let singleText = '';
    let singleAmt = '';
    if (Number(overpayBalance || '0.00') > 0 && Number(remitTotalAmount || '0.00') === 0 && Number(preRepayAmt || '0.00') === 0) {
      showType = 'single';
      singleText = '溢缴款余额：';
      singleAmt = Number(overpayBalance || '0.00').toFixed(2);
    } else if (Number(overpayBalance || '0.00') === 0 && Number(remitTotalAmount || '0.00') > 0 && Number(preRepayAmt || '0.00') === 0) {
      showType = 'single';
      singleText = '转账处理中的金额：';
      singleAmt = Number(remitTotalAmount || '0.00').toFixed(2);
    } else if (Number(overpayBalance || '0.00') === 0 && Number(remitTotalAmount || '0.00') === 0 && Number(preRepayAmt || '0.00') > 0) {
      showType = 'single';
      singleText = '预还款金额：';
      singleAmt = Number(preRepayAmt || '0.00').toFixed(2);
    } else {
      showType = 'multiple';
    }
    return (
      <MUDrawer
        beaconId="OverpayDrawer"
        show={showOverpayDrawer}
        placement="bottom"
        height="auto"
        onClose={() => { onClose && onClose(); }}
      >
        <MUView className="overpay-drawer">
          <MUView className="overpay-drawer__top">
            <MUView className="overpay-drawer__top__holder" />
            <MUText className="overpay-drawer__top__title">{title}</MUText>
            <MUImage
              beaconId="OverpayDrawerClose"
              className="overpay-drawer__top__close"
              src={icClose}
              onClick={() => { onTopCloseClick && onTopCloseClick(); }}
            />
          </MUView>
          {showType === 'single' ? (
            <MUView className="overpay-drawer__content">
              <MUView className="content__title">还款金额优先从小招荷包中扣除</MUView>
              <MUView className="content__amount">{singleText}
                <MUText className="overpay-drawer__point">{singleAmt}元</MUText>
              </MUView>
              {Number(overpayBalance || '0.00') > 0 ? (
                <MUView className="content__detail">
                  说明：小招荷包中的溢缴款是您向招联超额还款产生的余额或您使用招联贷款付款后发生退款时产生的余额款项（如有），在您还款时自动为您优先抵扣。
                </MUView>
              ) : null}
            </MUView>
          ) : (
            <MUView className="overpay-drawer__content">
              <MUView className="content__title">还款金额优先从小招荷包中扣除，可抵扣余额
                <MUText className="overpay-drawer__point">{Util.floatAdd(Number(preRepayAmt || '0.00'), Util.floatAdd(Number(remitTotalAmount || '0.00'), Number(overpayBalance || '0.00'))).toFixed(2)}元</MUText>
              </MUView>
              <MUView className="content__detail content__detail--multiple">
                {Number(preRepayAmt || '0.00') > 0 ? (
                  <MUView className="content__detail__item">
                    <MUView className="item__title">预还款金额</MUView>
                    <MUView className="item__amount">{preRepayAmt}元</MUView>
                  </MUView>
                ) : null}
                {Number(remitTotalAmount || '0.00') > 0 ? (
                  <MUView className="content__detail__item">
                    <MUView className="item__title">转账处理中的金额</MUView>
                    <MUView className="item__amount">{remitTotalAmount}元</MUView>
                  </MUView>
                ) : null}
                {Number(overpayBalance || '0.00') > 0 ? (
                  <MUView className="content__detail__item">
                    <MUView className="item__title">溢缴款余额</MUView>
                    <MUView className="item__amount">{overpayBalance}元</MUView>
                  </MUView>
                ) : null}
                <MUView className="content__detail__item">
                  <MUView className="item__title">合计</MUView>
                  <MUView className="item__amount overpay-drawer__point">{Util.floatAdd(Number(preRepayAmt || '0.00'), Util.floatAdd(Number(remitTotalAmount || '0.00'), Number(overpayBalance || '0.00'))).toFixed(2)}元</MUView>
                </MUView>
              </MUView>
            </MUView>
          )}
        </MUView>
      </MUDrawer>
    );
  }
}
