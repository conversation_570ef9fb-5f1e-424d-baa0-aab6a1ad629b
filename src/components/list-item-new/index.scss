@import '../../components/weapp/index.scss';

.list-item-wrapper {
  width: 100%;
  height: min-content;
  box-sizing: border-box;
  padding: 0 20px 20px 20px;

  .list-item {
    width: 100%;
    height: min-content;
    position: relative;
    padding: 30px 20px 20px;
    box-sizing: border-box;
    border-radius: 20px;
    background-color: #FFFFFF;

    &__amount {
      margin-left: 20px;
      line-height: 32px;
      display: flex;
      align-items: center;

      &--text {
        color: #333333;
        font-size: 32px;
        font-weight: 600;
      }
      &--icon {
        height: 32px;
        width: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    &__period {
      height: 38px;
      display: flex;
      align-items: center;
      margin-top: 16px;
      margin-left: 20px;
      
      &--cnt {
        color: #808080;
        font-size: 26px;
        font-weight: 400;
        line-height: 38px;
      }

      &--dueTag {
        height: 36px;
        padding: 0 10px;
        margin-left: 10px;
        border-radius: 4px;
        color: #FFFFFF;
        font-size: 22px;
        font-weight: 600;
        line-height: 36px;
        vertical-align: middle;
        background-color: #CC1F15;
      }
    }

    .big-margin {
      margin-top: 24px;
      margin-bottom: 20px;
    }

    &__prepayFee {
      width: max-content;
      height: 38px;
      margin-top: 16px;
      margin-left: 20px;
      color: #808080;
      font-size: 26px;
      font-weight: 400;
      line-height: 38px;
    }

    &__prepayDate {
      height: 38px;
      margin-top: 16px;
      margin-left: 20px;
      color: #FF8844;
      font-size: 26px;
      font-weight: 400;
      line-height: 38px;
    }

    &__desc {
      width: 100%;
      height: 56px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding-left: 20px;
      margin-top: 20px;
      border-radius: 8px;
      color: #808080;
      font-size: 24px;
      font-weight: 400;
      line-height: 40px;
      background-color: #F9F9F9;

      &--merchant {
        margin-left: 10px;
      }
    }

    &__interest {
      height: 36px;
      position: absolute;
      right: 20px;
      top: 20px;
      padding: 5px 20px;
      border-radius: 10px;
      color: #FF8844;
      font-size: 24px;
      font-weight: 400;
      line-height: 36px;
      background-color: #FFF3EC;

      &--arrow {
        height: 14px;
        width: 14px;
        position: absolute;
        bottom: -5px;
        right: 50px;
        transform: rotate(45deg);
        background: #FFF3EC;
      }
    }

    &__checker {
      width: 80px;
      height: 80px;
      position: absolute;
      right: 37px;
      top: 65px;
      display: flex;
      justify-content: center;
      align-items: center;

      &--img {
        width: 40px;
        height: 40px;
      }
    }

    &-near {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 15px;

      &__left {
        display: flex;
        align-items: center;

        &--checker {
          width: 40px;
          height: 40px;
          margin-left: 20px;
          margin-right: 10px;
    
          &--img {
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        &--text {
          height: 32px;
          margin-left: 20px;
          color: #333333;
          font-size: 32px;
          font-weight: 600;
          font-family: "PingFang SC";
          line-height: 32px;
        }

        &--icon {
          padding: 5px 10px;
          display: flex;
          align-items: center;
        }
      }

      &__right {
        height: 38px;
        display: flex;
        align-items: center;
        margin-right: 20px;
        
        &--dueTag {
          height: 36px;
          padding: 0 10px;
          border-radius: 4px;
          color: #FFFFFF;
          font-size: 22px;
          font-weight: 600;
          line-height: 36px;
          vertical-align: middle;
          background-color: #CC1F15;
        }

        &--creditTag,
        &--extend {
          height: 38px;
          padding: 0 10px;
          margin-left: 10px;
          border-radius: 5px;
          color: #3477FF;
          font-size: 22px;
          font-weight: 600;
          line-height: 38px;
          vertical-align: middle;
          background-color: #EBF1FF;
        }

        &--cnt {
          margin-left: 10px;
          height: 32px;
          color: #333333;
          font-size: 32px;
          font-weight: 600;
          font-family: "PingFang SC";
          line-height: 32px;
        }

        .red {
          color: #CC1F15;
        }
      }

      &__credit-product {
        display: flex;
        align-items: center;
        padding-left: 90px;
        margin-bottom: 15px;
        color: #808080;
        font-size: 26px;
        font-weight: 400;

        &-highlight {
          color: #FF8844;
        }
      }
    }

    .accordion-content {
      padding-left: 70px;
      margin-bottom: 20px;
  
      .repay-detail-item {
        display: flex;
        align-items: center;
        font-size: 26px;
        line-height: 36px;
        color: #808080;
        margin-bottom: 15px;

        .mu-text__default {
          margin-right: 30px;
        }

        .highlight {
          margin-left: 10px;
          color: #FF8844;
        }
      }
    }

    .accordion-content-near {
      padding-left: 90px;
    }

    .accordion-content-future {
      padding-left: 20px;
    }
  }
}

