/* eslint-disable react/jsx-closing-bracket-location */
/* eslint-disable max-len */
/* eslint-disable operator-linebreak */
/* eslint-disable no-nested-ternary */
import Madp from '@mu/madp';
import { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import ItemAccordionList from '@components/list-item-new/itemAccordionList';
import RepayModal from '@components/repay-modal/index';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import {
  MUView, MUIcon, MUText, MUImage, MURichText
} from '@mu/zui';
import WaRichtext from '@mu/wa-richtext';
import { merchantNoList } from '@utils/constants';
import { urlDomain } from '@utils/url_config';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

const themeColor = Util.getThemeColor(channelConfig.theme);
const checkCant = 'https://file.mucfc.com/zlh/3/0/202305/2023051820182812ffef.png';
const checkNo = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828a71df6.png';
const checkYes = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828e2a677.png';
const redCheckYes = 'https://file.mucfc.com/zlh/3/0/202305/20230518202125495a8a.png';

const tipsInfoDict = {
  IpayPrepayFee: { // 利率类提前还款违约金说明
    tipsTitle: '提前还款违约金说明',
    tipsConent: '提前还款违约金=提前还款本金*提前还款违约金比例（最低***元）',
    tipsExample: '举例：如果提前还款1000元本金，提前还款违约金比例为1%，则应还提前还款违约金为10元',
  },
  FpayPrepayFee: { // 费率类提前还款违约金说明
    tipsTitle: '提前还款违约金说明',
    tipsConent: '提前还款违约金=N*每期分期手续费',
    tipsExample: '举例：贷款10000元，每期分期手续费为50元，提前还款加收一期分期手续费，则应还提前还款违约金为50元',
  },
  ykjPayPrepayFee: { // 一口借提前还款违约金说明
    tipsTitle: '提前还款违约金说明',
    tipsConent: '提前还款违约金=提前还款本金*日利率*距离借款到期日的天数',
  }
};

export default class BillListItem extends Component {
  static propTypes = {
    item: PropTypes.object,
    billType: PropTypes.string, // 以后可能有用，目前只有随心分用，该组件无法勾选，点击则进行下一步
    checked: PropTypes.bool,
    onItemCheck: PropTypes.func,
    disabled: PropTypes.bool,
    checkedIcon: PropTypes.bool,
    isAllZL: PropTypes.bool, //  是否全部商户都是招联金融
    showCreditProductInfo: PropTypes.string, // 展示诚信保护信息
    showCreditProductFlag: PropTypes.string, // 展示诚信保护角标
    showSinglePrepayFeeDesc: PropTypes.bool, // 是否展示提前还款违约金减免描述
    hideInterestWaiveBubble: PropTypes.bool, // 是否隐藏借款利息优惠气泡
    custStatus: PropTypes.string, // 客户状态
  }

  static defaultProps = {
    item: {},
    billType: '', // 7days七天，total提前还款，extend还款日延长，
    checked: false,
    onItemCheck: () => { },
    disabled: false,
    checkedIcon: true,
    isAllZL: false,
    showCreditProductInfo: '',
    showCreditProductFlag: '',
    showSinglePrepayFeeDesc: true,
    hideInterestWaiveBubble: true,
    custStatus: '',
  }

  constructor(props) {
    super(props);
    this.state = {
      showAccordion: false,
      // 费用明细说明弹窗
      showTipsModal: false,
      tipsTitle: '',
      tipsConent: '',
      tipsExample: '',
    };
    this.accordionListRef = {
      show: () => { },
      hide: () => { },
    };
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  componentDidMount() {
    this.initSwitchAccordionStatus();
  }

  getItemOption(item) {
    const { billType } = this.props;
    let option = {};
    if (!Object.keys(item).length) return option;
    if (billType === 'extend') {
      option = {
        amount: item.surplusPayTotalAmt,
        businessType: item.businessType || '',
        merchantName: item.merchantName || '',
        merchantNo: item.merchantNo || '',
        subDesc: `${Util.getDateCollection(item.loanDate).join('-')} ${item.businessType || '借款'}${item.installTotalAmt}元`,
        lastCntsDesc: `第${item.installCnt}/${item.installTotalCnt}期`,
        tradeTips: `${Util.getDateCollection(item.payDate).splice(1).join('月')}日应还`,
        overDueDesc: '',
        extendDesc: item.isDelayPlan === 'Y' ? '参与延期' : '',
        unStageAble: false,
        creditProductInfo: '',
        creditProductFlag: '',
      };
    } else {
      let realOverDueFlag = false; // 是否真逾期(真逾期不展示诚信保护信息)
      if (item.overdueDays > 0 && Util.timeMinus(item.inteDate, item.payDate, 'day', 'date') < item.overdueDays) {
        realOverDueFlag = true;
      }
      option = {
        amount: billType === 'total' ? item.surplusPayPrincipalAmt : item.surplusPayTotalAmt, // 待还本金
        businessType: item.businessType || '',
        merchantName: item.merchantName || '',
        merchantNo: item.merchantNo || '',
        subDesc: `${Util.getDateCollection(item.loanDate).join('-')} ${item.businessType}${item.installTotalAmt}元`,
        lastCntsDesc: billType === 'total'
          ? `剩余期数 ${item.surplusInstallCnt || item.installTotalCnt - item.installCnt + 1 || item.installTotalCnt}期`
          : `第${item.installCnt}/${item.installTotalCnt}期`,
        tradeTips: billType === 'total' ? '剩余应还' : `${Util.getDateCollection(item.payDate).splice(1).join('月')}日应还`,
        overDueDesc: item.displayOverdueDays ? `已逾期${item.displayOverdueDays}天` : '',
        prepayDesc: item.payPrepayFee > 0 ? `提前还款收取<span style="color: #FF8844;">违约金${item.payPrepayFee}元</span>` : '',
        creditProductInfo: (!realOverDueFlag && item.canExtendInteDateFlag === 'Y' && item.inteDate) ? `${Util.getDateCollection(item.inteDate).splice(1).join('月')}日` : '',
        creditProductFlag: item.canExtendInteDateFlag === 'Y' ? '诚信保护' : '',
        canNotRepayDesc: this.getCanNotRepayDesc(item),
      };
    }
    return option;
  }

  getCanNotRepayDesc = (item) => {
    const { canPayFlag, canNotPayReasonCode } = item || {};
    if (canPayFlag === 'Y') return '';
    let reason = '';
    switch (canNotPayReasonCode) {
      case '01':
      case '02':
        reason = '不支持提前还款';
        break;
      case '03':
        reason = `${Util.dateFormatter(item.canPayDate, 'chinese')}后可提前还款`;
        break;
      case '04':
        reason = '借款当天不可提前还款';
        break;
      case '05':
        reason = '周周借不能进行提前还款';
        break;
      default:
        reason = '';
        break;
    }
    return reason;
  }

  initSwitchAccordionStatus = () => {
    const { item = {} } = this.props;
    const waiveSum = Number(item.waivePayInteAmt || '0.00') + Number(item.waivePayFineAmt || '0.00')
      + Number(item.waivePayPeriodFee || '0.00') + Number(item.waivePayOnetimeFee || '0.00')
      + Number(item.waivePayPrepayFee || '0.00');
    if (Number(waiveSum) > 0) {
      this.setState({ showAccordion: true }, () => this.accordionListRef.show());
    }
  }

  switchAccordion = () => {
    const { showAccordion } = this.state;
    if (showAccordion) {
      this.setState({ showAccordion: false }, () => this.accordionListRef.hide());
    } else {
      this.setState({ showAccordion: true }, () => this.accordionListRef.show());
    }
  }

  handleItemClick({
    showTipsModal, tipsTitle, tipsConent, tipsExample
  }) {
    this.setState({
      showTipsModal,
      tipsTitle,
      tipsConent,
      tipsExample,
    });
  }

  clearItemClick() {
    const { custStatus } = this.props;
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'repayment.BillListAll.CommonTipModal.ModalClickClose', beaconContent: { cus: { custStatus } } });
    this.setState({
      showTipsModal: false,
    });
  }

  toLoanInfo = () => {
    const { item, selectedBillList } = this.props;
    Madp.setStorageSync('selectedBillList', selectedBillList, 'SESSION');
    Madp.setStorageSync('hasJumpToLoanInfo', 'Y', 'SESSION');
    Madp.navigateTo({
      url: `${urlDomain}/${Madp.getChannel()}/traderecords/#/pages/ious-information/index?orderNo=${item.orderNo}&showDetails=1&needLogin=1`,
    });
  }

  showPrepayFeeModal = () => {
    const { item = {} } = this.props;
    // 违约金大于0时，点击才展示弹窗
    if (item && item.payPrepayFee > 0) {
      const type = (item.specialFlags && (item.specialFlags.indexOf('6') > -1)) ? 'ykjPayPrepayFee' : `${item.loanType || 'I'}payPrepayFee`;
      const tipsInfo = tipsInfoDict[type];
      // 利率类违约金说明的最小违约金需要动态取值
      if (type === 'IpayPrepayFee') {
        tipsInfo.tipsConent = Number(item.prepayFeeMinAmt || 0) > 0 ? tipsInfo.tipsConent.replace('***', item.prepayFeeMinAmt || 0) : '提前还款违约金=提前还款本金*提前还款违约金比例';
      }
      this.handleItemClick({
        showTipsModal: true,
        ...tipsInfo,
      });
    }
  }

  onItemClick = (item, hasInterestWaiveBubble) => {
    const { onItemCheck } = this.props;
    onItemCheck && onItemCheck(item);
  }

  render() {
    const {
      item = {},
      billType, checked,
      onItemCheck, disabled,
      checkedIcon, isAllZL,
      showSinglePrepayFeeDesc,
      showCreditProductInfo,
      showCreditProductFlag,
      custStatus,
      hideInterestWaiveBubble,
    } = this.props;
    const itemOption = this.getItemOption(item);
    const { useCouponFlag, useTempPriceFlag } = item;

    const {
      showAccordion,
      showTipsModal,
      tipsConent,
      tipsExample,
      tipsTitle,
    } = this.state;

    const showMerchant = !isAllZL && item.merchantName && merchantNoList.indexOf(item.merchantNo) <= -1;
    const hasInterestWaiveBubble = !hideInterestWaiveBubble && (useCouponFlag === 'Y' || useTempPriceFlag === 'Y');

    let interestWaiveBubbleText = '';
    if (hasInterestWaiveBubble && checked) {
      interestWaiveBubbleText = Util.getRepaymentLocale('HK02.HK02WA001');
    }

    return !Object.keys(item).length && !billType ? <MUView /> : (
      <MUView className="list-item-wrapper">
        {billType === 'total' ? (<MUView className="list-item">
          <MUView className="list-item__amount">
            <MUView className="list-item__amount--text">{itemOption.amount}元</MUView>
            <MUIcon className="list-item__amount--icon" value="arrow-right" size="14" color="#808080" beaconId="ToLoanInfo" beaconContent={{ cus: { custStatus } }} onClick={() => this.toLoanInfo(item)} />
          </MUView>
          <MUView className={`list-item__period ${!showSinglePrepayFeeDesc ? 'big-margin' : ''}`}>
            {itemOption.lastCntsDesc ? <MUView className="list-item__period--cnt">{itemOption.lastCntsDesc}</MUView> : null}
            {itemOption.overDueDesc ? <MUView className="list-item__period--dueTag">{itemOption.overDueDesc}</MUView> : null}
          </MUView>
          {itemOption.canNotRepayDesc ? <MUView className="list-item__prepayDate">{itemOption.canNotRepayDesc}</MUView> : null}
          {!itemOption.canNotRepayDesc && showSinglePrepayFeeDesc ? (
            <MUView className="list-item__prepayFee" beaconId="ShowPrepayFeeModal" beaconContent={{ cus: { custStatus } }} onClick={this.showPrepayFeeModal}>
              {itemOption.prepayDesc ? (<MURichText nodes={itemOption.prepayDesc} />) : (<MUView>提前还款免收违约金</MUView>)}
            </MUView>
          ) : null}
          <MUView className="list-item__desc">
            <MUView className="list-item__desc--text">{itemOption.subDesc}</MUView>
            {showMerchant && (
              <MUView className="list-item__desc--merchant">
                来自<MUText>{itemOption.merchantName && itemOption.merchantName.slice(0, 5)}</MUText>
              </MUView>
            )}
          </MUView>
          {interestWaiveBubbleText ? <MUView className="list-item__interest">
            <MUView className="list-item__interest--text"><WaRichtext beaconId="BubbleTextRichtext" content={interestWaiveBubbleText} /></MUView>
            <MUView className="list-item__interest--arrow" />
          </MUView> : null}
          {checkedIcon ? (
            <MUView
              className="list-item__checker"
              onClick={() => this.onItemClick(item)}
              beaconId="ItemCheck"
              beaconContent={{
                cus: {
                  type: checked ? 'unCheck' : 'check',
                  custStatus,
                  hasPrePayFee: !itemOption.canNotRepayDesc && showSinglePrepayFeeDesc && itemOption.prepayDesc ? 'Y' : 'N',
                  hasInterestWaiveBubble: hasInterestWaiveBubble ? 'Y' : 'N',
                }
              }}>
              <MUImage
                src={disabled ? checkCant : (checked ? (themeColor === '#E60027' ? redCheckYes : checkYes) : checkNo)}
                className="list-item__checker--img"
              />
            </MUView>
          ) : null}
        </MUView>) : (<MUView className="list-item">
          <MUView className="list-item-near">
            <MUView className="list-item-near__left">
              {(billType === '7days' || billType === 'bargaining') ? (<MUView className="list-item-near__left--checker">
                <MUImage
                  src={disabled ? checkCant : (checked ? (themeColor === '#E60027' ? redCheckYes : checkYes) : checkNo)}
                  className="list-item-near__left--checker--img"
                  beaconId="ItemCheck"
                  beaconContent={{ cus: { custStatus } }}
                  onClick={() => onItemCheck(item)}
                />
              </MUView>) : null}
              <MUView className="list-item-near__left--text">{itemOption.amount}元</MUView>
              {showAccordion ? (<MUIcon className="list-item-near__left--icon" value="arrow-up" size="14" color="#CACACA" beaconId="SwitchAccordion" beaconContent={{ cus: { custStatus } }} onClick={this.switchAccordion} />)
                : <MUIcon className="list-item-near__left--icon" value="arrow-down" size="14" color="#CACACA" beaconId="SwitchAccordion" beaconContent={{ cus: { custStatus } }} onClick={this.switchAccordion} />}
            </MUView>
            <MUView className="list-item-near__right">
              {itemOption.overDueDesc ? <MUView className="list-item-near__right--dueTag">{itemOption.overDueDesc}</MUView> : null}
              {itemOption.extendDesc ? <MUView className="list-item-near__right--extend">{itemOption.extendDesc}</MUView> : null}
              {!itemOption.overDueDesc && showCreditProductFlag === 'Y' && itemOption.creditProductFlag ? <MUView className="list-item-near__right--creditTag">{itemOption.creditProductFlag}</MUView> : null}
              {itemOption.lastCntsDesc ? <MUView className={`list-item-near__right--cnt ${itemOption.overDueDesc ? 'red' : ''}`}>{itemOption.lastCntsDesc}</MUView> : null}
            </MUView>
          </MUView>
          {showCreditProductInfo === 'Y' && itemOption.creditProductInfo && showAccordion ? <MUView className="list-item-near__credit-product">
            诚信保护期至
            <MUView
              className="list-item-near__credit-product-highlight"
              onClick={() => {
                Madp.showModal({
                  content: '每日按正常利率计息，诚信保护期内完成还款不影响个人征信。',
                  showCancel: false,
                  confirmText: '知道了',
                  confirmColor: themeColor,
                });
                dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'ShowCreditProductInfo', beaconContent: { cus: { custStatus } } });
              }}
              >
              {itemOption.creditProductInfo}
            </MUView>
          </MUView> : null}
          <ItemAccordionList ref={(ref) => { ref && (this.accordionListRef = ref); }} billDetail={item} billType={billType} />
          <MUView className="list-item__desc">
            <MUView className="list-item__desc--text">{itemOption.subDesc}</MUView>
            {showMerchant && (
              <MUView className="list-item__desc--merchant">
                来自<MUText>{itemOption.merchantName && itemOption.merchantName.slice(0, 5)}</MUText>
              </MUView>
            )}
          </MUView>
        </MUView>)}
        <RepayModal
          title={tipsTitle}
          beaconId="CommonTipModal"
          isOpened={showTipsModal}
          closeOnClickOverlay
          confirmText="我知道了"
          onClose={this.clearItemClick.bind(this)}
          onConfirm={this.clearItemClick.bind(this)}
        >
          <MUView className="modal-content">
            <MUView>
              {tipsConent}
            </MUView>
            <MUView className="tips-modal-example-line">{tipsExample}</MUView>
          </MUView>
        </RepayModal>
      </MUView >
    );
  }
}
