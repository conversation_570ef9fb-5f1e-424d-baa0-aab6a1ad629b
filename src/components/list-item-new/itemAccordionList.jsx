import { Component } from '@tarojs/taro';
import {
  MUView,
} from '@mu/zui';
import DetailItem from '@components/detail-item';
import PropTypes from 'prop-types';
import classNames from 'classnames';

export default class ItemAccordionList extends Component {
  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    billDetail: PropTypes.object,
    billType: PropTypes.string,
  }

  static defaultProps = {
    billDetail: {},
    billType: '',
  };

  constructor(props) {
    super(props);
    this.state = {
      show: false
    };
  }

  show = () => {
    this.setState({
      show: true
    });
  }

  hide = () => {
    this.setState({
      show: false
    });
  }

  render() {
    const { billDetail, billType } = this.props;
    const { show } = this.state;

    const { loanType } = billDetail;
    let originAmt = billDetail.surplusPayPrincipalAmt || '0.00'; // 待还本金
    let stagesFee = billDetail.surplusPayInteAmt || '0.00'; // 待还利息
    let periodFee = billDetail.surplusPayPeriodFeeAmt || '0.00'; // 待还期费用
    let fineAmt = billDetail.surplusPayFineAmt || '0.00'; // 待还罚息
    let oneTimeFee = billDetail.surplusPayOnetimeFeeAmt || '0.00'; // 剩余一次性手续费
    let payPrepayFee = billDetail.payPrepayFee || '0.00';
    let showPayFineAmt = Number(billDetail.surplusPayFineAmt || '0.00') + Number(billDetail.waivePayFineAmt || '0.00');
    let showPayOnetimeFeeAmt = Number(billDetail.surplusPayOnetimeFeeAmt || '0.00') + Number(billDetail.waivePayOnetimeFee || '0.00');
    let showPrepayFee = Number(billDetail.payPrepayFee || '0.00') + Number(billDetail.waivePayPrepayFee || '0.00');

    if (billType === 'extend') {
      periodFee = billDetail.surplusPayPeriodFee || '0.00'; // 待还期费用
      showPrepayFee = 0; // 延后还款日 不展示提前还款违约金
    }

    return (<MUView>
      {show ? (
        <MUView className={classNames(
          'accordion-content',
          { 'accordion-content-near': billType === '7days' || billType === 'bargaining' },
          { 'accordion-content-future': billType === 'future' || billType === 'extend' })}
        >
          {<DetailItem title="本金" value={`${originAmt}元`} />}
          {/* 七天待还、账单分期分期手续费 */}
          {loanType === 'I' && (
          <DetailItem beaconId="interest" title="利息" value={`${stagesFee}元`} waiveDiscount={billDetail.waivePayInteAmt} />
          )}
          {loanType !== 'I' && (
          <DetailItem beaconId="byStagesFee" title="分期手续费" value={`${periodFee}元`} waiveDiscount={billDetail.waivePayPeriodFee} />
          )}
          {showPayFineAmt || Number(billDetail.waivePayFineAmt || '0.00') > 0 ? (
            <DetailItem beaconId="exceedFee" title="罚息" value={`${fineAmt}元`} waiveDiscount={billDetail.waivePayFineAmt} />
          ) : ''}
          {showPayOnetimeFeeAmt ? (
            <DetailItem title="平台服务费" value={`${oneTimeFee}元`} waiveDiscount={billDetail.waivePayOnetimeFee} />
          ) : ''}
          {showPrepayFee ? (
            <DetailItem beaconId="repaymentInfo" title="提前还款违约金" value={`${payPrepayFee}元`} waiveDiscount={billDetail.waivePayPrepayFee} />
          ) : ''}
        </MUView>
      ) : null}
    </MUView>);
  }
}
