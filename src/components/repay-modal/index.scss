@import '../../components/weapp/index.scss';

.RepayModal-dialog {
  .title {
    font-size: 36px;
    font-weight: bold;
    text-align: center;
  }

  .content {
    margin-bottom: 30px;
    padding: 0 40px;
    font-size: 28px;
    color: #808080;
  }

  .btns {

    //   margin-top: 30px;
    .btn-confirm {
      font-size: 36px;

      &-no-radius {
        border-radius: 0;
      }

      &-image {
        margin: 0 40px 40px 40px;
      }
    }

    .btn-cancel {
      font-size: 28px;
      color: #A6A6A6;
      border: none;
    }
  }

  &-modal__header {
    padding: 0 0 $spacing-v-md;
    color: $mu-modal-header-text-color;
    font-size: $font-size-h1;
    line-height: $font-size-h1;
    text-align: center;

    .title {
      font-weight: bold;
    }

    .mu-icon {
      margin-bottom: $spacing-h-xxl;
      font-size: $icon-size-xxl;
    }

    &+.mu-modal__content {
      color: $color-text-title-secondary;
    }

    .taro-img {
      padding: 12px 12px 0;
      width: calc(100% - 24px);
      height: 220px;
      margin-bottom: 50px;
      background-color: $color-white;
      pointer-events: none;

      img {
        border-radius: 4px;
        pointer-events: none;
      }
    }
  }
}

.RepayModal-dialog-container {
  .mu-modal__container {
    overflow: visible;
  }

  .detain-dialog__infomation {
    width: calc(100% - 40px);
    position: absolute;
    display: flex;
    align-items: center;
    bottom: -48px;
    font-family: 'PingFangSC-Regular';
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
    text-align: center;
    //line-height: 32px;
    z-index: 2001;

    &--tag {
      width: 68px;
      min-width: 68px;
      height: 32px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      font-size: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
    }
  }

  // --兼容微信小程序
  >mu-view:nth-child(2) {
    flex: 1;
  }
}