/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
/**
 * @description: 小程序原生化页面，MUModal的参数content不能是jsx。
 * 重新包装一层MUModal已到达，将jsx作为MUModal的子元素传入。
 * @version: V1.0
 * @date: 2021-08-27
 * @author: ✨dlCai
 */

import { Component } from '@tarojs/taro';
import { Image } from '@tarojs/components';
import {
  MUView,
  MUModal,
  MUButton,
} from '@mu/zui';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import _isFunction from 'lodash/isFunction';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

export default class RepayModal extends Component {
  static propTypes = {
    type: PropTypes.oneOf(['success', 'warning', 'tip', 'image', 'text', 'activity', 'default']),
    src: PropTypes.string,
    title: PropTypes.string,
    cancelText: PropTypes.string,
    confirmText: PropTypes.string,
    closeOnClickOverlay: PropTypes.bool,
    isOpened: PropTypes.bool,
    isConfirmDisabled: PropTypes.bool,
    className: PropTypes.string,
    beaconId: PropTypes.string,
    onCancel: PropTypes.func,
    onConfirm: PropTypes.func,
    onClose: PropTypes.func,
  };

  static defaultProps = {
    type: 'default',
    src: '',
    title: '',
    cancelText: '',
    confirmText: '',
    closeOnClickOverlay: true,
    isOpened: false,
    isConfirmDisabled: false,
    className: '',
    beaconId: '',
    onCancel: () => { },
    onConfirm: () => { },
    onClose: () => { },
  };

  static options = {
    addGlobalClass: true
  }

  config = {
    "styleIsolation" : "shared"
  }

  handleCancelClick(evt) {
    const { onCancel } = this.props;
    if (_isFunction(onCancel)) {
      onCancel(evt);
    }
    this.handleCloseClick(evt);
  }

  handleConfirmClick(evt) {
    const { onConfirm } = this.props;
    if (_isFunction(onConfirm)) {
      onConfirm(evt);
    }
    this.handleCloseClick(evt);
  }

  handleCloseClick(evt) {
    const { onClose } = this.props;
    if (_isFunction(onClose)) {
      onClose(evt);
    }
  }

  render() {
    const {
      type, title, cancelText, confirmText, src, beaconId, isOpened, closeOnClickOverlay, onClose, className,
      isConfirmDisabled, isShowInformation
    } = this.props;
    return (
      <MUView className="RepayModal-dialog-container">
        <MUModal
          type={type}
          beaconId={beaconId}
          isOpened={isOpened}
          closeOnClickOverlay={closeOnClickOverlay}
          onClose={onClose}
          isConfirmDisabled={isConfirmDisabled}
          className={className}
        >

          <MUView className="RepayModal-dialog">
            {type === 'image' && (
              <MUView className="RepayModal-dialog-modal__header">
                {type === 'image' && src && <Image src={src} className="taro-img" />}
                <MUView className="title">{title}</MUView>
              </MUView>
            )}
            {type !== 'image' && (
              <MUView className="RepayModal-dialog-modal__header title">{title}</MUView>
            )}
            <MUView className="content">
              {this.props.children}
            </MUView>
            {type !== 'text' && (
              <MUView className="btns">
                {cancelText && (
                  <MUButton
                    className="btn-cancel"
                    beaconId="ConCancel"
                    onClick={this.handleCancelClick.bind(this)}
                  >
                    {cancelText}
                  </MUButton>
                )}
                {confirmText && (
                  <MUButton
                    className={
                      classNames(
                        'btn-confirm',
                        { 'brand-text': type === 'default' },
                        { 'btn-confirm-image': type === 'image' },
                        { 'btn-confirm-no-radius': type === 'default' }
                      )}
                    type={type && type !== 'image' ? 'text' : 'primary'}
                    beaconId="ConConfirm"
                    onClick={this.handleConfirmClick.bind(this)}
                  >
                    {confirmText}
                  </MUButton>
                )}
              </MUView>
            )}
            {type === 'text' && (
              <MUView className="btns">
                {confirmText && (
                  <MUButton
                    className="btn-confirm"
                    type="primary"
                    beaconId="ConConfirm"
                    onClick={this.handleConfirmClick.bind(this)}
                  >
                    {confirmText}
                  </MUButton>
                )}
                {cancelText && (
                  <MUButton
                    className="btn-cancel"
                    beaconId="ConCancel"
                    onClick={this.handleCancelClick.bind(this)}
                  >
                    {cancelText}
                  </MUButton>
                )}
              </MUView>
            )}
          </MUView>
          {isShowInformation && <MUView className="detain-dialog__infomation">
            <MUView className="detain-dialog__infomation--tag">服务</MUView>
          </MUView>
          }
        </MUModal>
      </MUView>
    );
  }
}
