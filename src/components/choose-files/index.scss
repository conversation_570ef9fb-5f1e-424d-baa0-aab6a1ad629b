.choose-file {
  &__header {
    padding: 40px 0 40px 20px;
    .header-line {
      float: left;
      margin-right: 12px;
      width: 8px;
      height: 38px;
      background: #3477FF;
      border-radius: 4px;
    }
    .header-title {
      height: 36px;
      font-size: 32px;
      line-height: 36px;
      color: #333;
      font-weight: 600;
    }
    .header-desc {
      margin-top: 12px;
      font-size: 24px;
      line-height: 26px;
      color: #808080;
      font-weight: 400;
    }
  }
  &__content {
    background: #fff;
    padding: 20px 20px 40px 10px;
    .content-step {
      margin-bottom: 6px;
      width: 100%;
      text-align: right;
      font-size: 24px;
      line-height: 1;
      color: #808080;
      font-weight: 400;
    }
    .content-files {
      padding-right: 20px;
      display: flex;
      justify-content: space-around;
      &__all {
        // width: 560px;
        display: flex;
        flex: 1;
        flex-wrap: wrap;
      }
      &__item {
        margin: 20px 0 0 20px;
        width: 116px;
        height: 116px;
        border: 1px solid rgba(152, 161, 168, 0.3);
        border-radius: 8px;
        position: relative;
        &__image {
          width: 100%;
          height: 100%;
        }
        .image-special {
          padding: 30px 24px 30px 20px;
          width: 58px;
          height: 58px;
        }
        &__text {
          padding: 25px 0;
          width: 130px;
          height: 68px;
          font-size: 24px;
          line-height: 34px;
          color: #1D1D1D;
          font-weight: 400;
          word-break: break-all;
        }
        &__delete {
          width: 32px;
          height: 32px;
          position: absolute;
          top: 10px;
          right: 10px;
        }
      }
      .item-special {
        width: 254px;
        display: flex;
      }
      &__add {
        width: 120px;
        height: 120px;
        margin: 20px 0 0 20px;
        position: relative;
        &__image {
          width: 100%;
          height: 100%;
        }
        &__input {
          width: 120px;
          height: 120px;
          position: absolute;
          top: 0;
          left: 0;
          opacity: 0;
          z-index: 9;
        }
      }
    }
  }
  .mu-modal__container {
    width: 600px;
    // height: 600px;
    .taro-img {
      width: 90%;
      height: 90%;
    }
  }
}