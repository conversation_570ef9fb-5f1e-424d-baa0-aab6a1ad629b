import { Component } from '@tarojs/taro';
import {
  MUView, MUImage, MUModal
} from '@mu/zui';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import Madp from '@mu/madp';
import fileAdd from './imgs/file-add.png';
import docxExample from './imgs/docx-example.png';
import excelExample from './imgs/excel-example.png';
import pdfExample from './imgs/pdf-example.png';
import audioExample from './imgs/audio-example.png';
import deleteImg from './imgs/delete.png';
import './index.scss';

export default class ChooseFiles extends Component {
  static propTypes = {
    chooseFilesParam: PropTypes.object,
    hideInput: PropTypes.bool,
  }

  static defaultProps = {
    chooseFilesParam: {},
    hideInput: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      showItems: [],
      showPreview: false,
      previewImg: '',
    };
    this.maxUploadNum = '5';
  }

  getTitleText = (type) => {
    let titleObj = {};
    switch (type) {
      case '100':
      case 'C03':
        titleObj.title = '贫困证明';
        titleObj.subTitle = '公共机构、社区开具的贫困证明等';
        break;
      case '101':
      case 'C04':
        titleObj.title = '疾病证明';
        titleObj.subTitle = '正规医疗机构出具的疾病证明、收据等';
        break;
      case '102':
      case 'C05':
        titleObj.title = '多方欠款证明';
        titleObj.subTitle = '征信报告，在其他金融机构、借贷平台的欠款证明等';
        break;
      case '199':
      case '999':
        titleObj.title = '其他材料';
        titleObj.subTitle = '死亡证明、拘留证明等其他材料';
        break;
      default:
        break;
    }
    return titleObj;
  }

  getExampleImg = (baseUrl) => {
    let exampleImg = '';
    const fileType = baseUrl.split('.');
    const formatType = fileType[fileType.length - 1].toLowerCase();
    if (formatType.indexOf('doc') > -1
      || formatType.indexOf('docx') > -1) {
      exampleImg = docxExample;
    } else if (formatType.indexOf('xls') > -1
    || formatType.indexOf('xlsx') > -1) {
      exampleImg = excelExample;
    } else if (formatType.indexOf('pdf') > -1) {
      exampleImg = pdfExample;
    } else if (formatType.indexOf('mp3') > -1
      || formatType.indexOf('wav') > -1
      || formatType.indexOf('m4a') > -1
      || formatType.indexOf('amr') > -1) {
      exampleImg = audioExample;
    }
    return exampleImg;
  }

  addFile = async (file, type, opr) => {
    const oriFile = file.target.files[0];
    // 文件类型限制
    if (file.target.value && !/\.(png|PNG|jpg|JPG|jpeg|gif|doc|docx|xls|xlsx|pdf|PDF|mp3|wav|m4a|amr|mp4)$/i.test(file.target.value)) {
      Madp.showToast({
        title: '请上传满足格式要求的文件',
        icon: 'none'
      });
      return;
    }
    if (oriFile.size / 1024 > 10 * 1024) {
      Madp.showToast({
        title: '请上传不超过10M的文件',
        icon: 'none'
      });
      return;
    }
    const fileResolve = await Util.getBase64(oriFile);
    this.setState((prevState) => prevState.showItems.push({ baseUrl: fileResolve, name: oriFile.name }));
    const { chooseFilesParam: { chooseFileFinish } } = this.props || {};
    if (typeof chooseFileFinish === 'function') chooseFileFinish(type, opr, fileResolve);
  }

  deleteFile = (i, type, opr) => {
    this.setState((prevState) => prevState.showItems.splice(i, 1));
    const { chooseFilesParam: { chooseFileFinish } } = this.props || {};
    if (typeof chooseFileFinish === 'function') chooseFileFinish(type, opr, i);
  }

  toPreview = (item) => {
    const fileType = item.name.split('.');
    const formatType = fileType[fileType.length - 1].toLowerCase();
    if (formatType.indexOf('png') > -1
      || formatType.indexOf('jpg') > -1
      || formatType.indexOf('jpeg') > -1
      || formatType.indexOf('gif') > -1) {
      this.setState({
        showPreview: true,
        previewImg: item.baseUrl
      });
    } else {
      Madp.showToast({
        title: '本文件不支持预览，请上源文件打开查看',
        icon: 'none'
      });
    }
  }

  render() {
    const {
      chooseFilesParam, hideInput
    } = this.props || {};
    const { materialType } = chooseFilesParam;
    const {
      showItems, showPreview, previewImg
    } = this.state;

    return (
      <MUView className="choose-file">
        <MUView className="choose-file__header">
          <MUView className="header-line" />
          <MUView className="header-title">{this.getTitleText(materialType).title}</MUView>
          <MUView className="header-desc">{this.getTitleText(materialType).subTitle}</MUView>
        </MUView>
        <MUView className="choose-file__content">
          <MUView className="content-step">{`${showItems.length}/${this.maxUploadNum}`}</MUView>
          <MUView className="content-files">
            <MUView className="content-files__all">
              {showItems.map((item, i) => (
                <MUView
                  className={`content-files__item${this.getExampleImg(item.name) ? ' item-special' : ''}`}
                >
                  <MUImage
                    className={`content-files__item__image${this.getExampleImg(item.name) ? ' image-special' : ''}`}
                    src={this.getExampleImg(item.name) ? this.getExampleImg(item.name) : item.baseUrl}
                    beaconId="FilePreview"
                    onClick={() => this.toPreview(item)}
                  />
                  {this.getExampleImg(item.name) ? (
                    <MUView
                      className="content-files__item__text"
                      beaconId="FilePreview"
                      onClick={() => this.toPreview(item)}
                    >
                      {item.name.length > 16 ? `${item.name.substr(0, 2)}******${item.name.substr(-7)}` : item.name}
                    </MUView>
                  ) : null}
                  <MUImage
                    className="content-files__item__delete"
                    src={deleteImg}
                    beaconId="ChooseFileDelete"
                    onClick={() => this.deleteFile(i, materialType, 'sub')}
                  />
                </MUView>
              ))}
            </MUView>
            {showItems.length < 5 ? (
              <MUView className="content-files__add">
                <input
                  type="file"
                  className="content-files__add__input"
                  // accept=".png,.jpg,.jpeg,.gif,.doc,.docx,.xls,.xlsx,.pdf,.mp3,.wav,.m4a,.amr"
                  onChange={(e) => this.addFile(e, materialType, 'add')}
                  style={{ visibility: hideInput ? 'hidden' : 'visible' }}
                />
                <MUImage className="content-files__add__image" src={fileAdd} />
              </MUView>
            ) : null}
          </MUView>
        </MUView>
        <MUModal
          type="activity"
          beaconId="PreviewModal"
          isOpened={showPreview}
          src={previewImg}
          onImageClick={() => {}}
          onClose={() => this.setState({ showPreview: false })}
        />
      </MUView>
    );
  }
}
