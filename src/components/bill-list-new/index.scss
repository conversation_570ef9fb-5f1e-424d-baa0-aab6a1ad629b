@import '../../components/weapp/index.scss';

.bill-list-comp-new {
  padding: 20px 0 200px;
  background-color: #F3F3F3;

  // position: relative;
  .divider {
    height: 20px;
    background-color: #f3f3f3;
  }

  .bill-list-tip {
    width: 100%;
    height: 26px;
    margin-top: 40px;
    color: #808080;
    text-align: center;
    font-size: 24px;
    font-weight: 400;
    line-height: 26px;
  }

  .bill-list-section-desc {
    margin: 10px 20px 20px;
    height: 26px;
    color: #808080;
    font-size: 24px;
    font-weight: 400;
    text-align: left;
    background-color: #F3F3F3;
    line-height: 26px;
  }

  .bill-list-plan {
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    color: #808080;
    font-size: 24px;
    font-weight: 400;
    line-height: 26px;
  }

  .bill-list-footer {
    position: fixed;
    border-top: 1px solid #f8f8f8;
    border-bottom: none;
    bottom: 0;
    height: 100px;
    width: 100%;
    background-color: #FFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &-checker {
      height: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 30px;

      .check-img {
        width: 40px;
        height: 40px;
      }
    }

    &-bubble {
      width: 180px;
      position: fixed;
      right: 20px;
      bottom: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);

      &-text {
        height: 48px;
        width: 180px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #FFFFFF;
        white-space: nowrap;
        border-radius: 24px;
        background: #FF8844;
        z-index: 1;
      }

      &-arrow {
        width: 14px;
        height: 14px;
        margin-top: -8px;
        transform: rotate(45deg);
        background: #FF8844;
      }
    }

    &-status {
      display: flex;
      align-items: center;
    }

    &-counter {
      display: flex;
      align-items: center;
      height: 100%;
      margin-left: 20px;
      color: #333333;
      font-size: 28px;
      font-weight: 600;

      &-num-highlight {
        color: #3477ff;
        font-weight: bold;
      }

      &-num-counter {
        margin-left: 10px;
        color: #333333;
        font-size: 24px;
        font-weight: 400;
        line-height: 40px;
      }

      .small-size {
        font-size: 20px;
      }

      &-desc {
        height: 32px;
        display: flex;
        align-items: center;
        color: #808080;
        font-size: 20px;
        font-weight: 400;
        line-height: 32px;
      }

      &-detail {
        display: flex;
        align-items: center;
        margin-left: 15px;
        color: #3477FF;
      }
    }

    &-btn {
      position: absolute;
      top: 10px;
      right: 20px;
      border-radius: 40px;
      height: 80px;
      width: 180px;
      text-align: center;
      line-height: 80px;
      font-size: 32px;
      font-weight: 600;
      color: #FFF;
      background: $color-brand;

      &.disabled {
        opacity: 0.3;
      }
    }
  }

  .bill-list-footer-extend {
    position: fixed;
    border-top: 1px solid #f8f8f8;
    border-bottom: none;
    bottom: 0;
    height: 120px;
    line-height: 120px;
    width: 100%;
    color: #FFFFFF;
    background: #FFFFFF;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  
    &-btn {
      margin: 16px 30px;
      width: calc(100% - 60px);
      height: 88px;
      line-height: 88px;
      font-size: 36px;
      font-weight: 600;
      color: #FFFFFF;
      text-align: center;
      border-radius: 8px;
    }
  }

  .at-drawer__content {
    border-radius: 20px 20px 0 0;
  }

  .repay-detail {
    position: relative;
    padding: 40px;

    &__title {
      width: 100%;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333333;
      font-size: 36px;
      font-weight: 600;
      line-height: 36px;
    }

    &__close {
      width: 40px;
      height: 40px;
      position: absolute;
      right: 30px;
      top: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &__sum {
      height: 38px;
      margin-top: 58px;
      color: #333333;
      font-size: 26px;
      font-weight: 400;
      line-height: 38px;

      &--highlight {
        color: #FF8844;
      }
    }

    &__content {
      width: 100%;
      margin-top: 30px;
      box-sizing: border-box;
      border-radius: 15px;
      padding: 40px;
      background-color: #F9F9F9;

      .repay-detail-item {
        height: 52px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #333333;
        font-size: 26px;
        font-weight: 400;
        font-family: "PingFang SC";

        &-title {
          font-weight: 600;
        }
      }
    }
  }
}

@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .bill-list-comp-new {
    padding-bottom: calc(200px + constant(safe-area-inset-bottom));
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .bill-list-comp-new {
    padding-bottom: calc(200px + env(safe-area-inset-bottom));
  }
}

.at-checkbox__option--disabled {
  .debt-top-container {
    background-image: url('https://file.mucfc.com/ebn/3/18/2023010/202310121438284a6b5a.png');
    background-repeat: no-repeat;
    background-size: 140px;
    background-position-x: 61%;
  }

  .at-checkbox__option-cnt {
    opacity: 0.6 !important;
  }
}