/* eslint-disable no-confusing-arrow */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/forbid-prop-types */
/* eslint-disable no-plusplus */
/* eslint-disable taro/manipulate-jsx-as-array */
/* eslint-disable object-curly-newline */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-param-reassign */
/* eslint-disable react/sort-comp */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUText, MUImage, MUDrawer, MUIcon
} from '@mu/zui';
import WaRichtext from '@mu/wa-richtext';
import PropTypes from 'prop-types';
import Madp from '@mu/madp';
import { debounce, Url } from '@mu/madp-utils';
import ListItemNew from '@components/list-item-new';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import DetailItem from '@components/detail-item';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

const themeColor = Util.getThemeColor(channelConfig.theme);
const checkCant = 'https://file.mucfc.com/zlh/3/0/202305/2023051820182812ffef.png';
const checkNo = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828a71df6.png';
const checkYes = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828e2a677.png';
const redCheckYes = 'https://file.mucfc.com/zlh/3/0/202305/20230518202125495a8a.png';

export default class BillList extends Component {
  static propTypes = {
    selectedBillList: PropTypes.array,
    billList: PropTypes.array,
    readOnlyBillList: PropTypes.array,
    billType: PropTypes.string,
    onSelect: PropTypes.func,
    selectAll: PropTypes.func,
    cancelAll: PropTypes.func,
    submitRepayment: PropTypes.func,
    showOtherChannelRepayTips: PropTypes.bool,
    canAllSelect: PropTypes.bool, // 为什么加多一个字段判断，因为这个组件其他地方也用到了，而我不想改其他的
    canSelectLength: PropTypes.number,
    disabledSelect: PropTypes.bool,
    disabledBtn: PropTypes.bool,
    btnContext: PropTypes.string,
    isDueTagCust: PropTypes.bool,
    earliestRepayDate: PropTypes.string, // 最早可还款日期
    showCreditProductInfo: PropTypes.string, // 是否展示诚信保护期信息
    showCreditProductFlag: PropTypes.string, // 是否展示诚信保护期角标
    checkedIcon: PropTypes.bool, // 是否有可选中借据的按钮
    unicomBillList: PropTypes.array, // 联通合约机借据
    sevenDayBillList: PropTypes.array, // 7日内到期借据
    hideInterestWaiveBubble: PropTypes.bool, // 隐藏借款优惠气泡
    bubbleInfo: PropTypes.object, // 去还款按钮气泡信息
    custStatus: PropTypes.string, // 客户状态
    courtCostBalance: PropTypes.object, // 法诉费
    showSevenDayBillTip: PropTypes.string, // 展示7天待还说明
  }

  static defaultProps = {
    selectedBillList: [],
    billList: [],
    readOnlyBillList: [],
    billType: 'total',
    onSelect: () => { },
    selectAll: () => { },
    cancelAll: () => { },
    submitRepayment: () => { },
    showOtherChannelRepayTips: true,
    canAllSelect: true,
    canSelectLength: 0,
    disabledSelect: false,
    disabledBtn: false,
    btnContext: '',
    isDueTagCust: false,
    earliestRepayDate: '',
    showCreditProductInfo: '',
    showCreditProductFlag: '',
    checkedIcon: true,
    unicomBillList: [],
    sevenDayBillList: [],
    hideInterestWaiveBubble: false,
    bubbleInfo: {},
    custStatus: '',
    courtCostBalance: '',
    showSevenDayBillTip: false,
  }

  constructor(props) {
    super(props);
    this.state = {
      showRepayDetail: false
    };
    this.doSubmitRepay = debounce((btn) => {
      this.submit(btn);
    }, 1000, {
      leading: true,
      trailing: false
    });
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  componentWillReceiveProps(nextProps) {
    // 虽然写了，但暂时没用
    const { isDueTagCust } = nextProps;
    if (isDueTagCust) {
      // 若为打标用户，则跳转到首页
      Util.router.replace('/pages/index/index');
    }
  }

  componentDidMount() {
    // 虽然写了，但暂时没用
    const { isDueTagCust } = this.props;
    if (isDueTagCust) {
      // 若为打标用户，则跳转到首页
      Util.router.replace('/pages/index/index');
    }
  }

  onItemSelect = debounce((item) => {
    const { billType } = this.props;
    if (billType === 'extend') return; // 延期还款仅展示 无需点击
    const { onSelect, disabledSelect, earliestRepayDate } = this.props;
    // 七天外账单的情况，设置不可选中，不可还款
    if (disabledSelect) return earliestRepayDate && Util.toast(`${earliestRepayDate}后才可还款哦`);
    onSelect(item);
  }, 400, { leading: true, trailing: false })


  showRepayDetail = () => {
    this.setState({
      showRepayDetail: true
    });
  };

  repaySelectClicked = debounce(() => {
    // eslint-disable-next-line object-curly-newline
    const { selectAll, cancelAll, disabledSelect } = this.props;
    if (disabledSelect) return;
    if (this.sumCheckerStatus) {
      // 当前全选了，再点取消所有选中
      cancelAll();
    } else {
      // 当前没全选，再点全选
      selectAll();
      const { billType } = this.props;
      if (billType === 'stages') {
        Util.toast('已为您勾选可分期账单（不可分期账单未被选中）');
      }
    }
  }, 400, { leading: true, trailing: false });

  submit(disabled = false) {
    if (disabled) return;
    const {
      selectedBillList,
      submitRepayment,
      billType,
    } = this.props;
    const { totalRepaySum, courtCostBalance } = this.totalRepayDetail || {};
    if (typeof submitRepayment === 'function') {
      submitRepayment({
        amount: (Number(totalRepaySum || '0.00') - Number(courtCostBalance || '0.00')).toFixed(2),
        stages: selectedBillList.length,
        billType,
      });
    }
  }

  // 延后还（延期还款）按钮返回
  backHandle = () => {
    Madp.navigateBack();
  }

  goRepayPlan = () => {
    if (Url.getParam('fromIndex') === '1') {
      Madp.navigateBack();
    } else {
      Util.router.replace('/pages/index/index?showRepayPlan=1');
    }
  }

  get nextBtnContent() {
    const { billType, btnContext } = this.props;
    if (btnContext) return btnContext;
    if (billType === 'total') {
      return '提前还款';
    }
    return '立即还款';
  }

  /**
   *  还款合计金额
   */
  get totalRepayDetail() {
    const { selectedBillList, disabledSelect, courtCostBalance } = this.props;
    let [
      originRepaySum,
      totalRepaySum,
      waiveSum,
      totalInterest,
      periodFee,
      fineAmt,
      payPrepayFee,
    ] = [0, 0, 0, 0, 0, 0, 0];
    // disabledSelect为true，七天外账单的情况，设置不可选中，不可还款，不计算底部金额
    !disabledSelect && selectedBillList.forEach((bill) => {
      originRepaySum += +bill.surplusPayPrincipalAmt || Number('0.00'); // 选中账单的总本金
      totalRepaySum += +bill.surplusPayTotalAmt || +bill.shouldReturnAmt || Number('0.00');
      const waiveTotal = Number(bill.waivePayInteAmt || '0.00') + Number(bill.waivePayFineAmt || '0.00')
        + Number(bill.waivePayPeriodFee || '0.00') + Number(bill.waivePayOnetimeFee || '0.00')
        + Number(bill.waivePayPrepayFee || '0.00');
      waiveSum += waiveTotal;
      totalInterest += Number(bill.surplusPayInteAmt || '0.00'); // 一期账单总利息
      periodFee += Number(bill.surplusPayPeriodFeeAmt || '0.00'); // 待还期费用
      fineAmt += Number(bill.surplusPayFineAmt || '0.00'); // 待还罚息
      payPrepayFee += Number(bill.payPrepayFee || '0.00'); // 提前还款违约金
    });
    return {
      originRepaySum: originRepaySum.toFixed(2),
      // 全部待还页存在法诉费，且当前选择了借据时，合计金额加上法诉费
      totalRepaySum: (selectedBillList || []).length > 0 ? (Number(totalRepaySum || '0.00') + Number(courtCostBalance || '0.00')).toFixed(2) : totalRepaySum.toFixed(2),
      waiveSum: waiveSum.toFixed(2),
      totalInterest: totalInterest.toFixed(2),
      periodFee: periodFee.toFixed(2),
      fineAmt: fineAmt.toFixed(2),
      payPrepayFee: payPrepayFee.toFixed(2),
      courtCostBalance,
      totalFee: (Number(totalInterest) + Number(periodFee) + Number(fineAmt) + Number(payPrepayFee) + Number(courtCostBalance || '0.00')).toFixed(2),
    };
  }

  get sumCheckerStatus() {
    const { billList, selectedBillList, canAllSelect, canSelectLength } = this.props;
    if (canAllSelect) {
      if (!selectedBillList.length && !billList.length) return false;
      return selectedBillList.length === billList.length;
    } else {
      if (!selectedBillList.length && !canSelectLength) return false;
      return selectedBillList.length === canSelectLength;
    }
  }

  // 判断借据是否全部是招联的借据，如果是的，则不展示“来自XXXX”
  get isAllZL() {
    let isAllZL = true;
    const zlMerchantNo = ['10000', '10001'];
    const { billList } = this.props;
    const flattedBillList = (billList || []).flat(2);
    flattedBillList.forEach((item) => {
      if (zlMerchantNo.indexOf(item.merchantNo) <= -1) {
        isAllZL = false;
      }
    });
    return isAllZL;
  }

  get normalBillList() {
    const { billType, billList, sevenDayBillList } = this.props;
    // 普通借据需要过滤掉联通合约机借据、保留逾期借据
    let normalBill = (billList || []).filter((bill) => bill.belongUnicomContractOrder !== 'Y' || bill.displayOverdueDays);

    // 7日内到期借据需单独展示，所以需过滤出来，否则会重复
    if (billType === '7days' || billType === 'bargaining') {
      // normalBill = normalBill.filter(item1 => !sevenDayBillList.some((item2) => item1.orderNo === item2.orderNo));
      normalBill = normalBill.filter(item1 => !(sevenDayBillList || []).includes(item1));
    }
    return normalBill;
  }

  render() {
    const {
      selectedBillList,
      readOnlyBillList,
      billType,
      showOtherChannelRepayTips,
      disabledSelect,
      disabledBtn,
      billList,
      showCreditProductInfo,
      showCreditProductFlag,
      checkedIcon,
      unicomBillList,
      sevenDayBillList,
      hideInterestWaiveBubble,
      bubbleInfo,
      custStatus,
      showSevenDayBillTip,
    } = this.props;
    const { showRepayDetail } = this.state;
    const isBtnDisabled = disabledBtn || !selectedBillList.length;
    const totalRepayNum = disabledBtn ? 0 : selectedBillList.length;
    const {
      totalRepaySum,
      waiveSum,
      totalInterest,
      originRepaySum,
      periodFee,
      fineAmt,
      payPrepayFee,
      courtCostBalance,
      totalFee, // 待还总息费（含利息、分期手续费、罚息、提前还违约金、司法处置费）
    } = this.totalRepayDetail;
    let sumCounterDesc = `共${totalRepayNum}笔`;
    if (Number(waiveSum)) sumCounterDesc = `${sumCounterDesc}(已减免${waiveSum}元)`;
    let showCannotExtendBillListFlag = true;

    const showNormalBillList = this.normalBillList && this.normalBillList.length > 0;
    const showSevenDayBillList = sevenDayBillList && sevenDayBillList.length > 0;
    const showUnicomBillList = unicomBillList && unicomBillList.length > 0;
    const showReadOnlyBillList = readOnlyBillList && readOnlyBillList.length > 0;

    // 若有一笔借据违约金大于0，则所有借据各自展示违约金说明，否则底部统一展示免收违约金（联通合约机、可见不可还借据各自展示）
    const showSinglePrepayFeeDesc = ((billList || []).filter((item) => Number(item.payPrepayFee) > 0) || []).length > 0;

    const repayDetailContent = [
      { title: '利息', value: totalInterest },
      { title: '分期手续费', value: periodFee },
      { title: '提前还款违约金', value: payPrepayFee },
      { title: '罚息', value: fineAmt },
      { title: '司法处置费', value: courtCostBalance },
    ];

    let buttonBubbleText = '';
    if (bubbleInfo && bubbleInfo.type && bubbleInfo.title) {
      buttonBubbleText = Util.getRepaymentLocale(bubbleInfo.title);
    }

    return (
      <MUView className="bill-list-comp-new">
        {/* 普通借据 */}
        {showNormalBillList ? (
          <MUView>
            {this.normalBillList.map((bill) => (
              <ListItemNew
                item={bill || {}}
                billType={billType}
                onItemCheck={(item) => this.onItemSelect(item)}
                disabled={bill.canPayFlag === 'N' || disabledSelect}
                checked={selectedBillList.findIndex((selectedBill) => JSON.stringify(selectedBill) === JSON.stringify(bill)) > -1}
                isAllZL={this.isAllZL}
                showCreditProductInfo={showCreditProductInfo}
                showCreditProductFlag={showCreditProductFlag}
                checkedIcon={checkedIcon}
                showSinglePrepayFeeDesc={showSinglePrepayFeeDesc || showUnicomBillList || showReadOnlyBillList}
                hideInterestWaiveBubble={hideInterestWaiveBubble}
                custStatus={custStatus}
                selectedBillList={selectedBillList}
              />
            ))}
            {/* 当且仅当客户全部待还借据都免违约金，且没有联通合约机借据，也没有可见可还借据时展示在底部 */}
            {billType === 'total' && !showSinglePrepayFeeDesc && !showUnicomBillList && !showReadOnlyBillList ? (<MUView className="bill-list-tip">以上借据提前还款免收违约金</MUView>) : null}
          </MUView>
        ) : null}
        {/* 7日内到期借据（仅近7天待还页面用到） */}
        {showSevenDayBillList ? (
          <MUView>
            {showSevenDayBillTip ? (<MUView className="bill-list-section-desc">以下为7日内到期账单，可一并勾选还款</MUView>) : null}
            {sevenDayBillList.map((bill) => (
              <ListItemNew
                item={bill || {}}
                billType={billType}
                onItemCheck={(item) => this.onItemSelect(item)}
                disabled={bill.canPayFlag === 'N' || disabledSelect}
                checked={selectedBillList.findIndex((selectedBill) => JSON.stringify(selectedBill) === JSON.stringify(bill)) > -1}
                isAllZL={this.isAllZL}
                showCreditProductInfo={showCreditProductInfo}
                showCreditProductFlag={showCreditProductFlag}
                checkedIcon={checkedIcon}
                custStatus={custStatus}
                selectedBillList={selectedBillList}
              />
            ))}
          </MUView>
        ) : null}
        {/* 联通合约机借据 */}
        {showUnicomBillList ? (
          <MUView>
            <MUView className="bill-list-section-desc">以下为联通合约机消费（按时缴纳话费，无需提前还款）</MUView>
            {unicomBillList.map((bill) => (
              <ListItemNew
                item={bill || {}}
                billType={billType}
                onItemCheck={(item) => this.onItemSelect(item)}
                disabled={bill.canPayFlag === 'N' || disabledSelect}
                checked={selectedBillList.findIndex((selectedBill) => JSON.stringify(selectedBill) === JSON.stringify(bill)) > -1}
                isAllZL={this.isAllZL}
                showCreditProductInfo={showCreditProductInfo}
                showCreditProductFlag={showCreditProductFlag}
                checkedIcon={checkedIcon}
                showSinglePrepayFeeDesc
                hideInterestWaiveBubble={hideInterestWaiveBubble}
                custStatus={custStatus}
                selectedBillList={selectedBillList}
              />
            ))}
          </MUView>
        ) : null}
        {/* 可见不可还借据 */}
        {showReadOnlyBillList ? (
          <MUView>
            {showOtherChannelRepayTips ? (
              <MUView className="bill-list-section-desc">以下借据请到借款渠道还款（账单是否还清，以借款渠道为准）</MUView>
            ) : null}
            {showCannotExtendBillListFlag && readOnlyBillList.map((bill) => (
              <ListItemNew
                item={bill}
                billType={billType}
                checked
                disabled
                readOnly
                isAllZL={this.isAllZL}
                checkedIcon={checkedIcon}
                showSinglePrepayFeeDesc
                custStatus={custStatus}
                selectedBillList={selectedBillList}
              />
            ))}
          </MUView>
        ) : null}
        {billType === 'future' ? (<MUView className="bill-list-plan" beaconId="GoRepayPlan" beaconContent={{ cus: { custStatus } }} onClick={this.goRepayPlan}>
          查看还款计划 <MUIcon value="arrow-right" size="12" color="#808080" />
        </MUView>) : null}
        {billType !== 'extend' && billType !== 'future' && billList.length ? (
          billType === 'total' ? (<MUView className="bill-list-footer">
            <MUView className="bill-list-footer-status">
              <MUView className="bill-list-footer-counter">
                <MUView>
                  <MUView className="bill-list-footer-counter-num">
                    合计：<MUText className="bill-list-footer-counter-num-highlight">{`${Number(totalRepaySum) > 0 ? totalRepaySum : '0'}元`}</MUText>
                    <MUText className={`bill-list-footer-counter-num-counter ${(Number(totalRepaySum) >= 10000 && Number(waiveSum) >= 10000) ? 'small-size' : ''}`}>{totalRepayNum > 0 ? sumCounterDesc : ''}</MUText>
                  </MUView>
                  {totalRepayNum > 0 ? (<MUView className="bill-list-footer-counter-desc">
                    {`含本金：${Number(originRepaySum) > 0 ? originRepaySum : '0'}元`}
                    {`，息费${Number(totalFee) > 0 ? totalFee : '0.00'}元`}
                    {Number(totalFee) > 0 ? (<MUView className="bill-list-footer-counter-detail" beaconId="ShowRepayDetail" beaconContent={{ cus: { custStatus } }} onClick={this.showRepayDetail}>
                      <MUView className="bill-list-footer-counter-detail-text">查看明细</MUView>
                      <MUIcon className="bill-list-footer-counter-detail-icon" value="arrow-right" size="12" color="#3477FF" />
                    </MUView>) : null}
                  </MUView>) : null}
                </MUView>
              </MUView>
            </MUView>
            {buttonBubbleText ? <MUView className="bill-list-footer-bubble">
              <MUView className="bill-list-footer-bubble-text"><WaRichtext beaconId="BubbleTextRichtext" content={buttonBubbleText} /></MUView>
              <MUView className="bill-list-footer-bubble-arrow" />
            </MUView> : null}
            <MUView
              className={`bill-list-footer-btn ${isBtnDisabled ? 'disabled' : ''}`}
              style={process.env.TARO_ENV !== 'swan' ? { background: themeColor } : {}}
              beaconId="ToSubmit"
              beaconContent={{ cus: { custStatus, text: this.nextBtnContent } }}
              onClick={() => this.doSubmitRepay(isBtnDisabled)}
            >
              {this.nextBtnContent}
            </MUView>
          </MUView>) : (<MUView className="bill-list-footer">
            <MUView className="bill-list-footer-status">
              <MUView className="bill-list-footer-checker" beaconId="repaySumChecker" beaconContent={{ cus: { custStatus } }} onClick={this.repaySelectClicked}>
                <MUImage
                  src={this.sumCheckerStatus ? (disabledSelect ? checkCant : (themeColor === '#E60027' ? redCheckYes : checkYes)) : checkNo}
                  className="check-img"
                />
              </MUView>
              <MUView className="bill-list-footer-counter">
                <MUView>
                  <MUView className="bill-list-footer-counter-num">
                    合计：
                    <MUText className="bill-list-footer-counter-num-highlight">{`${Number(totalRepaySum) > 0 ? totalRepaySum : '0'}元`}</MUText>
                  </MUView>
                  {totalRepayNum > 0 ? (<MUView className="bill-list-footer-counter-desc">
                    {sumCounterDesc}，含本金：{originRepaySum}元
                  </MUView>) : null}
                </MUView>
              </MUView>
            </MUView>
            <MUView
              className={`bill-list-footer-btn ${isBtnDisabled ? 'disabled' : ''}`}
              style={process.env.TARO_ENV !== 'swan' ? { background: themeColor } : {}}
              beaconId="ToSubmit"
              beaconContent={{ cus: { custStatus, text: this.nextBtnContent } }}
              onClick={() => this.doSubmitRepay(isBtnDisabled)}
            >
              {this.nextBtnContent}
            </MUView>
          </MUView>)
        ) : null}
        {billType === 'extend' ? (<MUView className="bill-list-footer-extend">
          <MUView className="bill-list-footer-extend-btn" beaconId="BackToExtend" style={process.env.TARO_ENV !== 'swan' ? { background: themeColor } : {}} onClick={this.backHandle}>返回</MUView>
        </MUView>) : null}
        <MUDrawer
          show={showRepayDetail}
          beaconId="RepayDetailDrawer"
          beaconContent={{ cus: { custStatus } }}
          placement="bottom"
          height="45%"
          mask
          onClose={() => this.setState({ showRepayDetail: false })}
        >
          <MUView className="repay-detail">
            <MUText className="repay-detail__title">还款明细</MUText>
            <MUView className="repay-detail__close" onClick={() => this.setState({ showRepayDetail: false })}>
              <MUIcon value="close2" size="20" color="#CACACA" />
            </MUView>
            <MUView className="repay-detail__sum">
              共计
              <MUText className="repay-detail__sum--highlight">{totalRepayNum}</MUText>
              笔，合计
            </MUView>
            <MUView className="repay-detail__content">
              <DetailItem title="本金" value={`${originRepaySum}元`} />
              {repayDetailContent.map((item) => (Number(item.value) > 0 ? <DetailItem title={item.title} value={`${item.value}元`} /> : null))}
            </MUView>
          </MUView>
        </MUDrawer>
      </MUView>
    );
  }
}
