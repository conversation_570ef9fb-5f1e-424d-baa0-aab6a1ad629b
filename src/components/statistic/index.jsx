/* eslint-disable react/forbid-prop-types */
import { Component } from '@tarojs/taro';
import {
  MU<PERSON>iew, MUText, MUButton, MUIcon, MUCurtain, MUImage
} from '@mu/zui';
import PropTypes from 'prop-types';
import { debounce, Url } from '@mu/madp-utils';
import Util from '@utils/maxin-util';
import ChannelConfig from '@config/index';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}
const themeColor = Util.getThemeColor(ChannelConfig.theme);
const icClose = 'https://file.mucfc.com/zlh/3/0/202305/202305182019591e0b87.png';

export default class Sum extends Component {
  static propTypes = {
    trackPrefix: PropTypes.string,
    disabled: PropTypes.bool,
    amount: PropTypes.number,
    BenefitCardAmt: PropTypes.string,
    couponUsable: PropTypes.bool,
    noInterestOrFee: PropTypes.bool,
    bankTransferFlag: PropTypes.bool,
    isTotal: PropTypes.bool,
    submitRepay: PropTypes.func,
    transferGuide: PropTypes.func,
    isPreRepay: PropTypes.func,
    specialRepayScene: PropTypes.string,
  }

  static defaultProps = {
    trackPrefix: 'repayment.Repay',
    disabled: false,
    amount: 0,
    BenefitCardAmt: '',
    couponUsable: true,
    noInterestOrFee: false,
    bankTransferFlag: false,
    isTotal: false,
    submitRepay: () => { },
    transferGuide: () => { },
    isPreRepay: false,
    specialRepayScene: '',
  }

  constructor(props) {
    super(props);
    this.state = {
      showAmtInfo: false
    };
    this.billType = Url.getParam('billType') || '';
    this.doSubmitRepay = debounce(() => {
      this.onSubmitRepay();
    }, 1000, {
      leading: true, // 指定调用在节流开始前
      trailing: false
    });
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  onCouponUsed() {
    this.myCouponPopup.show();
    const {
      couponUsable,
      noInterestOrFee,
      isTotal,
    } = this.props;
    if (!couponUsable) {
      // 本身禁止使用优惠券
      dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.MyCouponUnableClick` });
    }
    if (couponUsable && (noInterestOrFee && !isTotal)) {
      // 非提前还款有优惠券无利息或费用
      dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.MyCouponNoneClick` });
    }
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.trackPrefix}.MyCouponAbleClick` });
  }

  onSubmitRepay() {
    const { submitRepay, bankTransferFlag, transferGuide } = this.props;
    if (bankTransferFlag) {
      transferGuide();
    } else {
      submitRepay();
    }
  }

  setAmtInfo() {
    const { showAmtInfo } = this.state;
    if (!showAmtInfo) dispatchTrackEvent({ event: EventTypes.PO, beaconId: `${this.props.trackPrefix}.ShowBenifitInfo` });
    this.setState({
      showAmtInfo: !showAmtInfo
    });
  }

  render() {
    const {
      amount, bankTransferFlag, BenefitCardAmt, isPreRepay, disabled, courtCostBalance, specialRepayScene
    } = this.props;
    const { showAmtInfo } = this.state;
    let transDesc = '';
    if (bankTransferFlag) {
      transDesc = '转账金额：';
    } else {
      transDesc = (isPreRepay ? '实际支付：' : '还款实付：');
    }

    return (
      <MUView className={`repay-statistic-comp ${showAmtInfo ? 'repay-statistic-show-info' : ''}`}>
        <MUView className="repay-statistic-comp-detail">
          <MUView className="detail-title">
            <MUView className="title-name">
              金额明细
            </MUView>
            <MUView className="title-icon">
              <MUImage beaconId="DrawerClose" className="close" src={icClose} onClick={() => { this.setAmtInfo(); }} />
            </MUView>
          </MUView>
          <MUView className="content">
            <MUView className="amount">
              <MUView className="amount-key">
                还款金额
              </MUView>
              <MUView className="amount-value">
                ¥
                {amount}
                元
              </MUView>
            </MUView>
            <MUView className="amount">
              <MUView className="amount-key">
                微光卡开卡费
              </MUView>
              <MUView className="amount-value">
                ¥
                {BenefitCardAmt}
                元
              </MUView>
            </MUView>
          </MUView>

        </MUView>
        <MUView className="repay-statistic">
          <MUView className="repay-statistic-sum">
            <MUText className={`repay-statistic-sum-amount${bankTransferFlag ? 'Transfer' : ''}`}>
              {transDesc}
              <MUText className={`repay-statistic-sum-amount-num ${bankTransferFlag ? 'black' : ''} ${themeColor === '#E60027' ? 'repay-statistic-sum-amount-num--theme' : ''}`}>
                {(Util.floatAdd(Number(amount), Number(BenefitCardAmt))).toFixed(2)}元
              </MUText>
            </MUText>
            <MUView className="repay-statistic-sum-amount-sub-title">
              {bankTransferFlag ? (
                <MUView className="repay-statistic-sum-amount-transfer">
                  {bankTransferFlag ? '先预约，后转账' : ''}
                </MUView>
              ) : null}
              {BenefitCardAmt ? (
                <MUView
                  className="repay-statistic-sum-amount-benefit"
                  beaconId="ShowBenifitInfoText"
                  onClick={() => this.setAmtInfo()}
                >
                  {BenefitCardAmt ? '查看明细' : ''}
                </MUView>
              ) : null}
              {Number(courtCostBalance) > 0 ? (
                <MUView className="repay-statistic-sum-amount-benefit">
                  (含司法处置费{courtCostBalance}元)
                </MUView>
              ) : null}
            </MUView>
          </MUView>
          {BenefitCardAmt ? (
            <MUIcon
              className="repay-statistic-icon"
              value={showAmtInfo ? 'arrow-down' : 'arrow-up'}
              size="16"
              color="#a6a6a6"
              beaconId="ShowBenifitInfoIcon"
              onClick={() => this.setAmtInfo()}
            />
          ) : <MUView />}
          <MUView className={bankTransferFlag ? 'repay-statistic-transfer' : 'repay-statistic-btn'}>
            {this.billType === 'preConsultRepay' ? <MUView className="preConsultRepay">还款成功，即可办理协商还</MUView> : null}
            <MUButton
              disabled={disabled}
              className="theme-background-color"
              customStyle={`background: ${themeColor}`}
              onClick={this.doSubmitRepay}
              beaconId="SubmitRepay"
              beaconContent={{ cus: { repayMode: specialRepayScene === 'consult' ? 'consultRepay' : '' } }}
            >
              {bankTransferFlag ? '去预约' : '立即还款'}
            </MUButton>
          </MUView>
          <MUCurtain
            isOpened={showAmtInfo}
            onClick={() => this.setAmtInfo()}
            closeBtnPosition="top-right"
          >
            <MUView
              className="repay-statistic-curtain"
              onClick={() => this.setAmtInfo()}
              beaconId="OnClickMUCurtain"
            />
          </MUCurtain>

        </MUView>
      </MUView>
    );
  }
}
