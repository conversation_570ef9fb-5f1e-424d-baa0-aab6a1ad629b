@import '../../components/weapp/index.scss';

.repay-statistic-comp {
  position: fixed;
  bottom: 0;
  left: 10px;
  right: 0;
  background: #fff;
  height: 100px;
  z-index: 800;

  .preConsultRepay {
    position: absolute;
    top: -65px;
    right: 0;
    width: 264px;
    height: 48px;
    opacity: 1;
    color: #ff8844;
    text-align: right;
    font-size: 22px;
    font-weight: 400;
    line-height: 48px;
    background: #fff3ec;
    padding-right: 20px;
    padding-left: 20px;

    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      right: 60px;
      width: 0;
      height: 0;
      border-left: 8PX solid transparent;
      border-right: 8PX solid transparent;
      border-top: 8PX solid #fff3ec;
    }
  }

  .repay-statistic {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    height: 100px;
    padding-left: 30px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &-sum {
      float: left;
      height: 100%;
      display: flex;
      justify-content: center;
      flex-direction: column;
      font-size: 32px;

      &-amount {
        &-num {
          color: #3477FF;
          font-weight: 600;

          &--theme {
            color: #E60027;
          }
        }

        &-sub-title {
          display: flex;

          .repay-statistic-sum-amount-benefit:nth-child(2) {
            margin-left: 10px;
          }
        }

        &-transfer {
          height: 22px;
          margin-top: 16px;
          font-size: 22px;
          color: #FF8800;
          line-height: 22px;
          font-weight: 400;
        }

        &-benefit {
          height: 22px;
          margin-top: 16px;
          font-size: 22px;
          color: #A6A6A6;
          line-height: 22px;
        }
      }

      &-amountTransfer {
        font-size: 32px;
        color: #333333;
      }

      &-count {
        font-size: 22px;
        color: #a6a6a6;
      }

      .black {
        font-size: 32px;
        color: #333333;
        font-weight: 600;
      }
    }

    &-icon {
      position: absolute;
      padding: 33px 27px 33px 100px;
      right: 220px;
    }

    &-transfer {
      margin: 10px;
      float: right;
      height: 80px;
      width: 180px;
      color: #FFFFFF;
      line-height: 36px;
      font-weight: 500;

      .at-button {
        height: 100%;
        font-size: 28px;
        color: #fff;
        border-radius: 50px;
      }
    }

    &-btn {
      margin: 10px;
      float: right;
      height: 80px;
      width: 180px;
      position: relative;

      .at-button {
        height: 100%;
        font-size: 28px;
        color: #fff;
        border-radius: 50px;
      }
    }

    .black {
      font-size: 32px;
      color: #F95F52;
      font-weight: 600;
    }

    .theme-background-color {
      background: $color-brand;
    }

    &-curtain {
      width: 100%;
      height: 100%;
      display: flex;
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
    }

    .at-curtain__body {
      position: unset;
    }

    .at-curtain {
      bottom: 444px;
    }
  }

  &-detail {
    width: 100%;
    width: 100%;
    height: 324px;
    border-radius: 16px 16px 0 0;

    .detail-title {
      height: 100px;
      width: 100%;
      position: relative;
      display: flex;

      /* stylelint-disable-next-line */
      &>mu-view:first-of-type {
        width: 100%;
      }
    }

    .title-name {
      width: 100%;
      height: 100%;
      font-size: 36px;
      color: #333333;
      text-align: center;
      line-height: 100px;
    }

    .title-icon {
      position: absolute;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);

      .close {
        height: 25px;
        width: 25px;
      }
    }

    .content {
      margin-top: 20px;
      padding: 40px 36px 0;

      // height: 184px;
      .amount {
        display: flex;
        height: 32px;
        justify-content: space-between;
        font-size: 32px;
        color: #1B1B1B;
        margin-bottom: 40px;

        &-key {
          line-height: 32px;
        }

        &-value {
          line-height: 32px;
        }
      }
    }
  }

  .theme-background-color {
    border: 0;
    background: $color-brand;
  }
}

.repay-statistic-show-info {
  height: 444px;
  z-index: 802;
}