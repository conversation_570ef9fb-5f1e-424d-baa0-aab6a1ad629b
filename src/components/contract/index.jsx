/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
/* eslint-disable max-len */
/* eslint-disable no-nested-ternary */
import {
  useEffect, useState, useMemo, useCallback
} from '@tarojs/taro';
import Madp from '@mu/madp';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { getStore, setStore } from '@api/store';
import RepayModal from '@components/repay-modal/index';
import Util from '@utils/maxin-util';
import CustomConfig from '@config/index';
import Dispatch from '@api/actions';
import {
  MUView,
  MUContractChecker,
  MUIcon
} from '@mu/zui';
import { AgreementDrawer } from '@mu/agreement';
import { getLoginInfo } from '@mu/business-basic';

const themeColor = Util.getThemeColor(CustomConfig.theme);

function contractIconClick() {
  Madp.showModal({
    content: '您的单笔还款限额不足，请签署支付扣款协议提升单笔还款限额',
    showCancel: false,
    confirmColor: themeColor,
    confirmText: '我知道了'
  });
}

let channelParams;
async function getChannelParams() {
  if (channelParams) return channelParams;
  const data = {
    catalogueCodeList: ['13D'],
    paramTypeList: ['AGREEMENT']
  };
  channelParams = await Dispatch.repayment.getChannelParams(data);
  return channelParams;
}

/**
 * 协议相关组件
 */
export default function Contract({
  onChecked,
  showFPAYContract,
  showPREREPAYContract,
  capitalConstractNum,
  showContractMUModal, onModalClose, onModalConfirm, // 补签弹窗
  showDelayRepayConstract, // 展示延期还款协议
  billExtendInfo, // 延期还款协议信息
  trackPrefix, // 埋点页面前缀
}) {
  const contractApplyList = getStore('contractApplyList') || [];
  const selectedCard = getStore('selectedCard');
  const [channelAgreementPlanCfgDtoList, setChannelAgreementPlanCfgDtoList] = useState([]);
  const [readOnce, setReadOnce] = useState(false);
  const [checkBoxChecked, setCheckBoxChecked] = useState(false);
  const storageType = Madp.getChannel() === '3CMBAPP' ? 'LOCAL' : 'SESSION';

  useMemo(() => {
    const contractText = [];
    const contracts = [];
    if (showFPAYContract) {
      if (!capitalConstractNum) {
        contractText.push('银行卡信息授权书');
        contracts.push({
          id: 1,
          text: '银行卡信息授权书',
          contractType: contractApplyList.length ? 'BANK_CARD_AUTH' : 'BANKCARDAUTH'
        });
        contracts.push({
          id: 2,
          text: '支付扣款协议',
          contractType: capitalConstractNum ? 'WITHHOLDUN' : contractApplyList.length ? 'PAY_DEDUCT' : 'PAYWITHHOLD'
        });
      } else {
        contracts.push({
          id: 1,
          text: '支付扣款协议',
          contractType: capitalConstractNum ? 'WITHHOLDUN' : contractApplyList.length ? 'PAY_DEDUCT' : 'PAYWITHHOLD'
        });
      }
      contractText.push('支付扣款协议');
      dispatchTrackEvent({
        event: EventTypes.SO,
        beaconId: `${trackPrefix}.ShowFPAYContract`,
        beaconContent: { cus: { scene: (selectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } },
      });
    }
    if (showDelayRepayConstract) {
      contracts.push({
        id: 1,
        text: '延期还款服务单',
        contractType: 'DELAY_REPAY'
      });
    }
    if (showPREREPAYContract) {
      contracts.push({
        id: 1,
        text: '预还款须知',
        contractType: 'PREPAY_NOTICE'
      });
    }
    channelAgreementPlanCfgDtoList.forEach((item) => {
      item.contracts = contracts;
      item.contractText = contracts.map((contract) => contract.text).join('、');
    });
  }, [channelAgreementPlanCfgDtoList, showFPAYContract, capitalConstractNum, showPREREPAYContract, showDelayRepayConstract, trackPrefix]);

  const hasContract = channelAgreementPlanCfgDtoList.length > 0 && channelAgreementPlanCfgDtoList[0].contracts && channelAgreementPlanCfgDtoList[0].contracts.length > 0;

  const onContractClick = useCallback(async ({
    url, text, formData
  } = {}) => {
    const bankCard = getStore('selectedCard');
    if (url) {
      // 点协议的时候存储一下当前选择的卡
      bankCard && bankCard.bankCardId && setStore({ shouldReselectCard: '1' });
      Madp.navigateTo({ url: `/pages/web-view/index?pageUrl=${encodeURIComponent(url)}` });
      return;
    }
    const { custName = '', idNo = '', mobile = '' } = await getLoginInfo() || {};
    const date = new Date();
    let contractParam = {
      contractType: contractApplyList.some((item) => item.contractType === 'PAY_DEDUCT') ? 'PAY_DEDUCT' : 'PAYWITHHOLD',
      // todo
      bankName: bankCard.bankName,
      accountName: bankCard.bankCustName,
      accountNo: bankCard.bankCardNoMask,
      name: custName,
      certId: idNo,
      yearNow: date.getFullYear(),
      monthNow: date.getMonth() + 1,
      dayNow: date.getDate(),
    };
    // console.log(capitalConstractNum, 'capitalConstractNum');
    if (capitalConstractNum) {
      const { merchantInfo = {} } = await Dispatch.repayment.queryMerchantInfo({
        queryMerchantNo: capitalConstractNum
      });
      contractParam = {
        contractType: 'WITHHOLDUN',
        name: custName,
        certId: idNo,
        partnerId: capitalConstractNum,
        partner: merchantInfo.merchantName,
        accountName: bankCard.bankCustName,
        bankName: bankCard.bankName,
        accountNo: bankCard.bankCardNoMask,
        yearNow: date.getFullYear(),
        monthNow: date.getMonth() + 1,
        dayNow: date.getDate(),
      };
    }
    if (text === '银行卡信息授权书') {
      contractParam.contractType = contractApplyList.some((item) => item.contractType === 'BANK_CARD_AUTH') ? 'BANK_CARD_AUTH' : 'BANKCARDAUTH';
    }
    if (text === '延期还款服务单') {
      const extendContract = contractApplyList.find((item) => item.contractType === 'DELAY_REPAY') || {};
      contractParam = {
        ...CustomConfig.contractParams,
        contractCategory: 'DELAY_REPAY',
        contractEdition: 'COMMON',
        bankName: bankCard.bankName,
        accountName: bankCard.bankCustName,
        accountNo: bankCard.bankCardNoMask,
        name: custName,
        certId: idNo,
        mobile: mobile,
        yearNow: date.getFullYear(),
        monthNow: date.getMonth() + 1,
        dayNow: date.getDate(),
        bringParam: 1,
        ...extendContract,
        ...billExtendInfo
      };
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'repayment.ExtendList.ClickContractLink', target: this });
    }

    if (text === '预还款须知') {
      contractParam = {
        contractCategory: 'PREPAY_NOTICE',
        contractEdition: 'COMMON',
        bankName: bankCard.bankName,
        accountName: bankCard.bankCustName,
        accountNo: bankCard.bankCardNoMask,
        name: custName,
        certId: idNo,
        yearNow: date.getFullYear(),
        monthNow: date.getMonth() + 1,
        dayNow: date.getDate(),
      };
    }
    contractApplyList.forEach((item) => {
      if (item.contractType === contractParam.contractType) {
        contractParam = { ...contractParam, ...item, contractCategory: item.contractType };
      }
    });
    // console.log(contractParam, 'contractParam');
    if (formData) return contractParam;
    // 跳出页面前存储下
    bankCard && bankCard.bankCardId && setStore({ shouldReselectCard: '1' });
    Util.viewContract(contractParam);
  }, [capitalConstractNum, billExtendInfo]);

  const transChannelParams = async (channels) => {
    if (!channels) return;
    const newChannels = [];
    const channelList = [{ channelCatalogueAgreementDtoList: [] }];
    let newChannelList = [];
    const viewContract0 = await onContractClick({ url: '', text: '银行卡信息授权书', formData: true });
    const viewContract1 = await onContractClick({ url: '', text: '', formData: true });
    if (channels.length > 1) {
      channelList[0].channelCatalogueAgreementDtoList = channels.reduce((total, num) => (total.channelCatalogueAgreementDtoList || []).concat(num.channelCatalogueAgreementDtoList));
      newChannelList = [channelList[0]];
    } else {
      newChannelList = channels;
    }
    newChannelList.forEach((channel, index) => {
      newChannels.push({
        catalogueCode: channel.catalogueCode,
        list: [],
        contractList: [],
        contracts: [],
        showContract: false,
        readDuration: 5,
        contractText: ''
      });

      channel.channelCatalogueAgreementDtoList.forEach((item, key) => {
        if (item.forceReadFlag === 'Y') {
          if (contractApplyList.length && item.contractTypeFlag) {
            contractApplyList.forEach((contract) => {
              if (item.contractType === 'BANK_CARD_AUTH' && contract.contractType === 'BANK_CARD_AUTH') {
                item.custShowName = '银行卡信息授权书';
                item = { ...viewContract0, ...item, ...contract };
              } else if (item.contractType === 'PAY_DEDUCT' && contract.contractType === 'PAY_DEDUCT') {
                item.custShowName = '支付扣款协议';
                item = { ...viewContract1, ...item, ...contract };
              } else if (item.contractType === 'WITHHOLDUN' && contract.contractType === 'WITHHOLDUN') {
                item.custShowName = '支付扣款协议';
                item = { ...viewContract1, ...item, ...contract };
              }
            });

            if ((item.contractType === 'BANK_CARD_AUTH' || item.contractType === 'PAY_DEDUCT' || item.contractType === 'WITHHOLDUN'
              || (item.contractType === 'SAVE_MONEY_CARD' && item.serviceName)) && !readOnce) {
              if (item.custShowName) {
                newChannels[index].list.push({
                  title: item.custShowName,
                  params: item
                });
              }
            }
            newChannels[index].readDuration = item.readDuration;
          } else if (!item.contractTypeFlag) {
            if (item.contractType === 'BANKCARDAUTH') {
              item.custShowName = '银行卡信息授权书';
              item = { ...viewContract0, ...item };
            } else if (item.contractType === 'PAYWITHHOLD') {
              item.custShowName = '支付扣款协议';
              item = { ...viewContract1, ...item };
            } else if (item.contractType === 'WITHHOLDUN') {
              item.custShowName = '支付扣款协议';
              item = { ...viewContract1, ...item };
            }
            if ((item.contractType === 'BANKCARDAUTH' || item.contractType === 'PAYWITHHOLD' || item.contractType === 'WITHHOLDUN'
              || (item.contractType === 'DIMLIGHT' && item.serviceName)) && !readOnce) {
              newChannels[index].list.push({
                title: item.custShowName,
                params: item
              });
            }
            newChannels[index].readDuration = item.readDuration;
          }
        }
      });
    });
    return newChannels;
  };

  const handleSubmitClick = useCallback((value, item, index) => {
    if (value) {
      Madp.setStorageSync(
        'expressRepayContracts',
        JSON.stringify(item && item.contracts),
        storageType
      );
      setReadOnce(true);
      setCheckBoxChecked(true);
      channelAgreementPlanCfgDtoList[index].showContract = false;
      channelAgreementPlanCfgDtoList[index].list = [];
      onChecked(value);
    } else {
      channelAgreementPlanCfgDtoList[index].showContract = false;
    }
    setChannelAgreementPlanCfgDtoList([...channelAgreementPlanCfgDtoList]);
  }, [channelAgreementPlanCfgDtoList, readOnce]);

  const handlerCheckboxClick = async (value, item) => {
    const cps = await getChannelParams();
    const capcdl = await transChannelParams(cps.data.channelAgreementPlanCfgDtoList, false);

    const newArrList = [];
    capcdl[0].list.forEach((data) => {
      item.contracts.forEach((contract) => {
        if (contract.contractType === data.params.contractType || (!contract.contractType && contract.contractCategory === data.params.contractCategory && contract.contractEdition === data.params.contractEdition)) {
          newArrList.push(data);
        }
      });
    });
    const haveForceFlag = newArrList.some((one) => one.params.forceReadFlag === 'Y');

    // 需强读未强读
    if (haveForceFlag && !readOnce) {
      capcdl[0].list = newArrList;
      if (value) { // 当前未选中，要选中，先去强读
        capcdl[0].showContract = true;
      } else { // 当前已选中，置为未选中
        setCheckBoxChecked(value);
        onChecked(value);
        Madp.setStorageSync(
          'expressRepayContracts',
          '',
          storageType
        );
      }
    } else { // 不需强读，仅更新协议状态
      if (value) { // 当前未选中，需选中
        Madp.setStorageSync(
          'expressRepayContracts',
          JSON.stringify(item && item.contracts),
          storageType
        );
      } else { // 当前已选中，置为未选中
        Madp.setStorageSync(
          'expressRepayContracts',
          '',
          storageType
        );
      }
      setCheckBoxChecked(value);
      onChecked(value);
    }
    setChannelAgreementPlanCfgDtoList(capcdl);
  };

  useEffect(() => {
    getChannelParams()
      .then((cps) => cps.data && transChannelParams(cps.data.channelAgreementPlanCfgDtoList))
      .then((capcdl) => {
        setChannelAgreementPlanCfgDtoList(capcdl || []);
      });
  }, []);

  return (
    <MUView>
      {
        hasContract ? (
          <MUView className="repay-contract">
            {
              channelAgreementPlanCfgDtoList.map((item, index) => (
                <MUView className="repay-contract-container">
                  <MUContractChecker
                    beaconId="ContractChecker"
                    outerControl
                    checkboxValue={checkBoxChecked}
                    onCheckboxClick={(value) => handlerCheckboxClick(value, item)}
                    contractText={item.contractText}
                    contracts={item.contracts}
                    onContractClick={onContractClick}
                  />
                  {!capitalConstractNum && showFPAYContract && (selectedCard || {}).forceSignFlag !== 'Y'
                    && <MUIcon beaconId="ContractIcon" className="repay-contract-icon" size="16" value="info" onClick={contractIconClick} />}
                  {
                    item.showContract && (
                      <AgreementDrawer
                        agreementViewProps={{
                          type: (item.contracts && item.contracts.length > 1) ? 1 : 2,
                          list: item.list,
                          current: 0,
                        }}
                        show={item.showContract}
                        close={() => handleSubmitClick(false, item, index)}
                        submit={() => handleSubmitClick(true, item, index)}
                        totalCount={item.readDuration}
                      />
                    )
                  }
                </MUView>
              ))
            }
          </MUView>
        ) : null
      }

      <RepayModal
        className="repay-contractModal"
        beaconId="FPAYContractModal"
        isOpened={showContractMUModal}
        closeOnClickOverlay
        onClose={onModalClose}
        confirmText="我已阅读并同意"
        onConfirm={onModalConfirm}
      >
        <MUView className="modal-content">
          <MUView>{`储蓄卡(${selectedCard.bankCardNoMask && selectedCard.bankCardNoMask.slice(-4)})代扣协议已失效，需重新签署`}</MUView>
          <MUView
            onClick={onContractClick}
            beaconId="MUModalContract"
            className="brand-text"
          >
            支付扣款协议
          </MUView>
        </MUView>
      </RepayModal>
    </MUView>
  );
}
