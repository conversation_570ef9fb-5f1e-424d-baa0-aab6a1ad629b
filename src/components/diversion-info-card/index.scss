@import '../../components/weapp/index.scss';
@mixin std-text($size, $lineHeight, $weight, $color, $textAlign) {
  font-size: $size;
  line-height: $lineHeight;
  font-weight: $weight;
  color: $color;
  text-align: $textAlign;
}

.diversion-info-card {
  margin: 20px 20px 0;
  padding: 26px 30px 40px 34px;
  border-radius: 16px;
  background: #fff;
  display: flex;
  align-items: center;
  &__left {
    width: 56px;
    height: 56px;
    font-size: 0;
    .taro-img, image {
      width: 100%;
      height: 100%;
    }
  }
  // --兼容微信小程序
  >mu-view:nth-child(2) {
    flex: 1;
  }
  &__center {
    margin-left: 30px;
    flex: 1;
    &__title {
      @include std-text(32px, 48px, 600, #333, left);
    }
    &__guide {
      @include std-text(24px, 36px, 400, #808080, left);
      &--overdue {
        color: #CC1F15;
      }
    }
  }
  &__right {
    width: 148px;
    height: 60px;
    border-radius: 30px;
    background: #3477ff;
    @include std-text(26px, 60px, 600, #fff, center);
    &--unicom {
      background: #E60027;
    }
    &--overdue {
      background: #CC1F15;
    }
  }
}