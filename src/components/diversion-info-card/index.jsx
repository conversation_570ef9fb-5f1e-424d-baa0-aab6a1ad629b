/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUImage,
} from '@mu/zui';
import PropTypes from 'prop-types';
import Madp from '@mu/madp';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { miniProgramChannel } from '@utils/constants';
import { urlDomain } from '@utils/url_config';

import diversionCardImg from '@components/assets/img/diversion-card-img.png';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

export default class DiversionInfoCard extends Component {
  static propTypes = {
    diversionInfo: PropTypes.object,
    themeColor: PropTypes.string,
    trackPage: PropTypes.string,
  }

  static defaultProps = {
    diversionInfo: {},
    themeColor: '',
    trackPage: '',
  }

  constructor(props) {
    super(props);
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  componentDidMount() {
    const { trackPage } = this.props;
    dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPage}ShowDiversionInfoCard` });
  }

  render() {
    const { diversionInfo, themeColor } = this.props;
    const {
      diversionDueBillAmt, diversionOverdueDays,
    } = diversionInfo || {};

    return (
      <MUView className="diversion-info-card">
        <MUView className="diversion-info-card__left"><MUImage src={diversionCardImg} /></MUView>
        <MUView className="diversion-info-card__center">
          <MUView className="diversion-info-card__center__title">{Number(diversionOverdueDays || 0) > 0 ? '您的第三方借据已逾期' : '您有待还的第三方借据'}</MUView>
          <MUView className={`diversion-info-card__center__guide${Number(diversionOverdueDays || 0) > 0 ? ' diversion-info-card__center__guide--overdue' : ''}`}>前往招联金融-智选专区查账还款</MUView>
        </MUView>
        <MUView
          className={`diversion-info-card__right${themeColor === '#E60027' ? ' diversion-info-card__right--unicom' : ''}${Number(diversionOverdueDays || 0) > 0 ? ' diversion-info-card__right--overdue' : ''}`}
          beaconId="JumpDiversionGuide"
          onClick={() => {
            if (miniProgramChannel.indexOf(Madp.getChannel()) > -1) {
              Madp.navigateTo({ url: `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/diversion-guide/index?repayAmt=${diversionDueBillAmt}&overdueDays=${diversionOverdueDays}&needLogin=1` });
            } else {
              Madp.navigateTo({ url: `/pages/diversion-guide/index?repayAmt=${diversionDueBillAmt}&overdueDays=${diversionOverdueDays}` });
            }
          }}
        >去查看</MUView>
      </MUView>
    );
  }
}
