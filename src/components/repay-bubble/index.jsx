/**
 * @description: 还款通用气泡，包含上箭头带背景带主题色、下箭头带背景带主题色
 */

import { Component } from '@tarojs/taro';
import {
  MUView, MUImage, MUText
} from '@mu/zui';
import PropTypes from 'prop-types';

import fillTopTriangle from '@components/assets/img/fill-top-triangle.png';
import fillBottomTriangle from '@components/assets/img/fill-bottom-triangle.png';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

export default class RepayBubble extends Component {
  static propTypes = {
    repayBubbleParam: PropTypes.object,
  };

  static defaultProps = {
    repayBubbleParam: {}
  };

  static options = {
    addGlobalClass: true
  }

  config = {
    "styleIsolation" : "shared"
  }

  render() {
    const {
      repayBubbleParam
    } = this.props;
    const { type, contentArr } = repayBubbleParam || {};
    return (
      <MUView className="repay-bubble">
        {type === 'fillTop' ? (
          <MUView className="repay-bubble__arrow-top">
            <MUImage src={fillTopTriangle} />
          </MUView>
        ) : null}
        {contentArr && contentArr.length > 0 ? (
          <MUView className="repay-bubble__text--wrap">
            <MUView className="repay-bubble__text">
              {contentArr.map((item) => (
                <MUText
                  className={item.contentColor ? `repay-bubble__text--${item.contentColor}` : ''}
                >{item.contentText}</MUText>
              ))}
            </MUView>
          </MUView>
        ) : null}
        {type === 'fillBottom' ? (
          <MUView className="repay-bubble__arrow-bottom">
            <MUImage src={fillBottomTriangle} />
          </MUView>
        ) : null}
      </MUView>
    );
  }
}
