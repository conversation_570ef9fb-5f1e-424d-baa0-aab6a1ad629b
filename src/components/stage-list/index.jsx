import { Component } from '@tarojs/taro';
import {
  MUView,
  MUText,
  MUIcon,
} from '@mu/zui';
import PropTypes from 'prop-types';
import { getStore, setStore } from '@api/store';
import './index.scss';

export default class StageList extends Component {
  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    calculateList: PropTypes.array,
  }

  static defaultProps = {
    calculateList: []
  }

  state = {
    selectedStage: {},
  }

  async componentWillReceiveProps(newProps) {
    const { calculateList } = this.props;
    if (JSON.stringify(newProps.calculateList) !== JSON.stringify(calculateList)) {
      this.initDefaultStage(newProps.calculateList);
    }
  }

  getStageListNodes() {
    const {
      calculateList
    } = this.props;
    const {
      selectedStage
    } = this.state;
    const stageListNodes = calculateList.map((item) => {
      const checkedValue = selectedStage.loanPeriod === item.loanPeriod ? 'checked' : 'unchecked';
      const checkedColor = selectedStage.loanPeriod === item.loanPeriod ? '#488FF0' : '#cacaca';
      return (
        <MUView className="stage-list-item" onClick={() => this.selectStage(item)} beaconId="stageListItem">
          <MUView className="item-count">{`${item.loanPeriod}期`}</MUView>
          <MUView className="item-line" />
          <MUView className="item-content">
            <MUView className="main">
              每期应还
              <MUText className="highlight">{`￥${item.repayPlanList[0].repayAmt}`}</MUText>
            </MUView>
            <MUView className="sub">
              {`本金${item.repayPlanList[0].repayPrincipalAmt}，分期费${item.repayPlanList[0].installFee}`}
            </MUView>
          </MUView>
          <MUView className="item-selector">
            <MUIcon className="" value={checkedValue} size="20" color={checkedColor} />
          </MUView>
        </MUView>
      );
    });
    return stageListNodes;
  }

  /**
   *  初始化默认选择的分期数
   */
  initDefaultStage(calculateList) {
    console.log(71, calculateList);
    // 读取缓存的已选分期数，没有就默认分 6 期
    const selectedLoanPeriod = getStore('selectedLoanPeriod') || 6;
    const selectedStage = calculateList.filter((item) => item.loanPeriod === selectedLoanPeriod)[0]
      || calculateList.filter((item) => item.loanPeriod === 6)[0];
    this.setState({
      selectedStage
    });
    setStore({
      selectedLoanPeriod: selectedStage.loanPeriod
    });
  }

  /**
   * 选择分期数
   */
  selectStage(stage) {
    this.setState({
      selectedStage: stage,
    });
    setStore({
      selectedLoanPeriod: stage.loanPeriod
    });
    console.log(105, stage);
  }

  render() {
    const stageList = this.getStageListNodes();
    return (
      <MUView className="stage-list">
        <MUView className="stage-list-desc">
          <MUText className="stage-list-desc-title">选择分期期数</MUText>
        </MUView>
        {stageList}
      </MUView>
    );
  }
}
