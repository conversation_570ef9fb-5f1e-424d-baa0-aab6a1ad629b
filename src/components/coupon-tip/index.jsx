/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import { useEffect, useState, useMemo, useCallback } from '@tarojs/taro';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import {
  MUView,
  MUImage,
  MUText,
  MUIcon,
} from '@mu/zui';
import RepayModal from '@components/repay-modal/index';
import Dispatch from '@api/actions';
import ClearCouponModal from '@components/modals/ClearCouponModal';
import { getFeeInteRightInfo } from '@utils/repay-util';

const guideCouponPicture = 'https://file.mucfc.com/ebn/3/0/202404/20240425205237efd25b.png';
let guideCoupon = {};

/**
 * 优惠券提示组件
 */
export default function CouponTip({
  couponObject = {},
  couponCalculate = {},
  waiveTotalAmt,
  onClick,
  getRrialParam,
  selectBankTransferFlag,
  selectedCoupon,
  isSpecialCouponScene,
  isOverdue,
  isCannotCoupon,
  trackPrefix,
  settleWaiveAmt,
  specialRepayScene,
  ignoreTrial,
  isHideCouponColumn,
  hasPrepayFeeRightsCoupon,
  guideCouponChoose,
  getVirtualRrialParam = () => {},
  guideCouponChooseCallBack = () => {},
  expressScene,
  newScene,
  overdueDay,
}) {
  const {
    availableCouponDetailList,
    appExclusiveCouponDetailList,
    unavailableCouponDetailList
  } = couponObject;
  const firstCoupon = availableCouponDetailList && availableCouponDetailList[0];
  guideCoupon = Object.keys(firstCoupon || {}).length ? firstCoupon : guideCoupon;
  const [reduceAmt, setReduceAmt] = useState(0);
  const [openTransferCouponTip, setOpenTransferCouponTip] = useState(false);
  const [guideWaiveAmt, setGuideWaiveAmt] = useState(0);
  const [showGuideCheck, setShowGuideCheck] = useState(false);
  const guideInputAmt = (getVirtualRrialParam() || {}).transTotalAmt;

  const hideTransferCouponTip = useCallback(() => {
    setOpenTransferCouponTip(false);
  }, []);

  const couponTipType = useMemo(() => {
    let couponTip = '';
    if (availableCouponDetailList.length) {
      couponTip = 1;
      dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${trackPrefix}.AvailableCoupons`, beaconContent: { cus: { expressScene, newScene, overdueDay } } });
    } else if (appExclusiveCouponDetailList.length || unavailableCouponDetailList.length) {
      dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${trackPrefix}.ConditionalCoupons` });
      couponTip = 2;
    }
    /** specialScenehasCoupon: 仅能使用216券的特殊场景下，是否有216券 */
    let specialScenehasCoupon = isSpecialCouponScene
      && [...availableCouponDetailList, ...appExclusiveCouponDetailList, ...unavailableCouponDetailList]
        .findIndex((i) => i.awardType === '216') > -1;

    if (couponCalculate.waiveTotalAmt || Number(couponCalculate.waiveTotalAmt) === 0 || waiveTotalAmt) {
      couponTip = 3;
    }
    // 无特殊优惠券,而且逾期
    if (!specialScenehasCoupon && isOverdue) {
      couponTip = 4;
    }
    // 无特殊优惠券, 而且部分提前还款、息费减免
    if (!specialScenehasCoupon && isCannotCoupon) {
      couponTip = 5;
    }
    // 息费减免券可在银行转账展示，有可用优惠券时，展示不可选文案
    if (selectBankTransferFlag) {
      if (Object.keys(selectedCoupon || {}).length) {
        if (selectedCoupon.awardType === '216') {
          couponTip = 3;
        } else {
          setOpenTransferCouponTip(true);
          couponTip = 6;
        }
      } else if (availableCouponDetailList.length) {
        couponTip = 6;
      }
    }
    // 快捷还款：无超限金额时，一定展示优惠栏（避免页面元素太少太空）
    if (!couponTip && trackPrefix === 'repayment.expressRepay' && Number(settleWaiveAmt) === 0) {
      couponTip = 7;
    }
    // 协商还不支持使用优惠券
    if (specialRepayScene === 'consult') {
      couponTip = 8;
    }
    // 有免提还违约金资格券的时候，联动展示“暂无可用优惠”
    if (hasPrepayFeeRightsCoupon) {
      couponTip = 7;
    }
    // 博弈还款流程，不展示优惠券栏位
    if (isHideCouponColumn) {
      couponTip = '';
    }
    return couponTip;
  }, [availableCouponDetailList, availableCouponDetailList.length, couponCalculate, waiveTotalAmt, selectBankTransferFlag, isSpecialCouponScene, isOverdue, isCannotCoupon, specialRepayScene, isHideCouponColumn, hasPrepayFeeRightsCoupon, guideCouponChoose, getVirtualRrialParam, guideCouponChooseCallBack, expressScene, newScene, overdueDay]);

  useEffect(() => {
    if (couponTipType === 1 && firstCoupon) {
      if (!ignoreTrial) {
        const params = getRrialParam();
        if (trackPrefix === 'repayment.expressRepay') {
          params.feeInteRightList = [{
            ...getFeeInteRightInfo(firstCoupon)
          }];
        } else {
          params.awardInfoList = [{
            ...firstCoupon,
            awardOrderList: firstCoupon.orderNoList
          }];
        }
        Dispatch.repayment.repayTransTrial(params)
          .then((res) => {
            setReduceAmt(res.totalCanWaiveAmt);
          })
          .catch(() => { });
      }
    }
  }, [couponTipType, firstCoupon, ignoreTrial]);

  useEffect(() => {
    if (guideCouponChoose) {
      const params = getVirtualRrialParam();
      if ((guideCoupon || {}).awardAmtType !== '4') {
        params.transTotalAmt = String(parseFloat((guideCoupon || {}).transAmt || '0.00').toFixed(2));
      }
      params.feeInteRightList = [{
        ...getFeeInteRightInfo(guideCoupon)
      }];
      Dispatch.repayment.repayTransTrial(params)
        .then((res) => {
          setGuideWaiveAmt(res.totalCanWaiveAmt);
        })
        .catch(() => { });
    }
  }, [guideCouponChoose, getVirtualRrialParam]);

  return (
    <MUView className="repay-wrapper-coupon-tip">
      {
        couponTipType
        && (
          <MUView
            className="repay-wrapper-coupon"
            beaconId="CouponMUListItem"
            onClick={() => {
              if (availableCouponDetailList.length) {
                dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${trackPrefix}.ClickAvailableCoupons`, beaconContent: { cus: { expressScene, newScene, overdueDay } } });
              } else if (appExclusiveCouponDetailList.length || unavailableCouponDetailList.length) {
                dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${trackPrefix}.ClickConditionalCoupons` });
              }
              // 选中银行卡转账时，优惠券栏不可选（仅会在息费减免券时展示）
              if (couponTipType > 3 || selectBankTransferFlag) { return; }
              if (onClick) onClick();
            }}
          >
            <MUView className="repay-wrapper-coupon-title">优惠券</MUView>
            <MUView className="repay-wrapper-coupon-extra">
              <MUView className="repay-wrapper-coupon-extra-info">

                {couponTipType === 1 && (
                  reduceAmt
                    ? (
                      <MUView className="text-color">
                        未选优惠，预计省
                        <MUText className="number-color">
                          {reduceAmt}
                        </MUText>
                        元
                      </MUView>
                    )
                    : (
                      <MUView className="text-color">
                        未选优惠
                      </MUView>
                    )
                )}
                {couponTipType === 2 && (
                  <MUView className="text-color">
                    <MUText className="number-color">
                      {appExclusiveCouponDetailList.length + unavailableCouponDetailList.length}
                    </MUText>
                    张还款券，查看使用条件
                  </MUView>
                )}
                {couponTipType === 3 && (
                  <MUView className="text-color icon-container">
                    已省
                    <MUText className="number-color">
                      {couponCalculate.waiveTotalAmt || waiveTotalAmt}
                    </MUText>
                    元
                  </MUView>
                )}
                {couponTipType === 4 && (<MUView className="useless-color">逾期不可使用还款券</MUView>)}
                {couponTipType === 5 && (<MUView className="useless-color">不可使用还款券</MUView>)}
                {couponTipType === 6 && (<MUView className="useless-color">所选还款方式不可使用优惠券</MUView>)}
                {couponTipType === 7 && (<MUView className="useless-color">暂无可用优惠</MUView>)}
                {couponTipType === 8 && (<MUView className="useless-color">协商还不支持使用</MUView>)}
              </MUView>
              {
                couponTipType < 4 ? (
                  <MUView className="repay-wrapper-coupon-extra-icon">
                    <MUIcon value="arrow-right" size={14} color="#CACACA" />
                  </MUView>
                ) : null
              }
            </MUView>
          </MUView>
        )
      }
      {(guideCouponChoose && Number(guideWaiveAmt || '0.00') > 0) ? (
        <MUView className="guide-coupon-choose">
          <MUView className="guide-coupon-choose__left">
            <MUView className="guide-coupon-choose__left__picture">
              <MUImage src={guideCouponPicture} />
            </MUView>
            {Number((guideCoupon || {}).transAmt) > 0 ? (
              <MUView className="guide-coupon-choose__left__text">
                还{(guideCoupon || {}).awardAmtType === '4' ? Number(guideInputAmt || '0.00').toFixed(2) : Number((guideCoupon || {}).transAmt).toFixed(2)}元可<MUText className="guide-coupon-choose__left__text--special">减息费{Number(guideWaiveAmt || '0.00').toFixed(2)}元</MUText>
              </MUView>
            ) : null}
          </MUView>
          <MUView
            className="guide-coupon-choose__right"
            beaconId="GuideCouponChooseClick"
            beaconContent={{ cus: { expressScene, newScene, overdueDay } }}
            onClick={() => {
              setShowGuideCheck(true);
              dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPrefix}.GuideCouponChooseModal`, beaconContent: { cus: { expressScene, newScene, overdueDay } } });
            }}
          >
            <MUView className="guide-coupon-choose__right__text">
              使用优惠
            </MUView>
            <MUView className="guide-coupon-choose__right__picture">
              <MUIcon value="jump-cicle" size="14" color="#808080" />
            </MUView>
          </MUView>
        </MUView>
      ) : null}
      {couponTipType
        && Number(settleWaiveAmt) > 0
        && trackPrefix === 'repayment.expressRepay'
        && <MUView className="repayinfo-split" />}
      <RepayModal
        title="温馨提示"
        className="repay-transefer-modal"
        isOpened={openTransferCouponTip}
        closeOnClickOverlay
        onClose={hideTransferCouponTip}
        confirmText="我知道了"
        onConfirm={hideTransferCouponTip}
      >
        <MUView className="modal-content">
          <MUText>
            银行卡转账还款不支持使用优惠券，若需要使用优惠券请选择其他还款方式
          </MUText>
        </MUView>
      </RepayModal>
      <ClearCouponModal
        isOpened={showGuideCheck}
        onConfirm={() => {
          setShowGuideCheck(false);
          guideCouponChooseCallBack((guideCoupon || {}).awardAmtType === '4' ? Number(guideInputAmt || '0.00').toFixed(2) : Number((guideCoupon || {}).transAmt || '0.00').toFixed(2), true);
          dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPrefix}.GuideCouponChooseModalConfirm`, beaconContent: { cus: { expressScene, newScene, overdueDay } } });
        }}
        onCancel={() => {
          setShowGuideCheck(false);
          dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPrefix}.GuideCouponChooseModalCancel`, beaconContent: { cus: { expressScene, newScene, overdueDay } } });
        }}
        newVersion
        minTransAmt={(guideCoupon || {}).awardAmtType === '4' ? Number(guideInputAmt || '0.00').toFixed(2) : Number((guideCoupon || {}).transAmt || '0.00').toFixed(2)}
        guideWaiveAmt={Number(guideWaiveAmt || '0.00').toFixed(2)}
      />
    </MUView>
  );
}
