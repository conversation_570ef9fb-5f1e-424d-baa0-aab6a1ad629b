.picker {

  .at-drawer__content {
    box-sizing: border-box;
    padding: 30px;
    border-radius: 20px 20px 0 0;
  }

  &__head {
    width: 100%;
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &--cancel {
      height: 42px;
      color: #333333;
      text-align: left;
      font-size: 28px;
      font-weight: 400;
      line-height: 42px;
    }

    &--title {
      height: 54px;
      opacity: 1;
      color: #333333;
      text-align: center;
      font-size: 36px;
      font-weight: 600;
      font-family: "PingFang SC";
      line-height: 54px;
    }

    &--confirm {
      height: 42px;
      color: #3477ff;
      text-align: right;
      font-size: 28px;
      font-weight: 600;
      line-height: 42px;
    }
  }

  &__roller {
    height: 300px;
    overflow: hidden;
    margin: 50px 0;
    position: relative;

    &--list {
      // padding: 60px 0;

      &--item {
        height: 60px;
        color: #333333;
        text-align: center;
        font-size: 36px;
        font-weight: 400;
        line-height: 60px;
      }
    }

    &--mask {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 50px 0;
      margin: 0 auto;
      z-index: 3;
      transform: translateZ(0px);
      background-image:
          linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6)),
          linear-gradient(to top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6));
      background-position: top, bottom;
      background-size: 100% 120px;
      background-repeat: no-repeat;
    }

    &--indicator {
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      height: 60px;
      width: 100%;
      border: 1px solid #c7c7c7;
      border-left: 0;
      border-right: 0;
      color: #333333;
      font-size: 40px;
      z-index: 3;
    }
  }
}