import { Component } from '@tarojs/taro';
import { <PERSON>U<PERSON><PERSON>er, MUView } from '@mu/zui';
import PropTypes from 'prop-types';
import { EventTypes, dispatchTrackEvent } from '@mu/madp-track';

import './index.scss';

/**
 * 普通选择器（支持自定义标题、支持点击触发）
 * 注意：目前仅在再分期使用，其他地方使用请做好充分测试
 */
export default class MyPicker extends Component {
  static propTypes = {
    trackPrefix: PropTypes.string,
    show: PropTypes.bool,
    title: PropTypes.string,
    beaconId: PropTypes.string,
    value: PropTypes.string,
    options: PropTypes.object,
    onCancel: PropTypes.func,
    onConfirm: PropTypes.func,
  }

  static defaultProps = {
    trackPrefix: 'repayment.ReInstallmentApply',
    show: false,
    title: '',
    beaconId: '',
    value: '',
    options: [],
    onCancel: PropTypes.func,
    onConfirm: PropTypes.func,
  }

  constructor(props) {
    super(props);
    this.state = {
    };
    this.startTouchTop = 0; // 记录开始滚动的位置
    this.scrollTop = 0; // 记录最终滚动的位置
    this.maxScrollTop = 0; // 滚动最大值
    this.minScrollTop = 0; // 滚动最小值
    this.listItemHeight = 0; // 记录每一项到高度
  }

  componentDidMount() {
    this.initEventListener();
    this.initSelectedValue();
  }

  initEventListener = () => {
    // 特别注意：需要确保父组件中show为真时再渲染此组件，否则无法拿到DOM，影响组件交互
    const roller = document && document.querySelector('.picker__roller');
    const rollerList = document && document.querySelector('.picker__roller--list');
    const listItem = document && document.querySelector('.picker__roller--list--item');
    const listItemHeight = listItem ? parseFloat(window.getComputedStyle(listItem).height) : 0;
    this.listItemHeight = listItemHeight;
    this.maxScrollTop = roller ? ((roller.clientHeight - listItemHeight) / 2) : 0; // 用户下拉会产生一个最大值，而最大值应该是第一个元素下拉到中间的位置
    this.minScrollTop = rollerList ? (-(rollerList.offsetHeight - listItemHeight - this.maxScrollTop)) : 0; // 最小值应该是用户上拉时最后一个元素达到中间的位置，因此应该是内容容器-最大值
    roller && roller.addEventListener('touchstart', this.touchStartHandler);
    roller && roller.addEventListener('touchmove', this.touchMoveHandler);
    roller && roller.addEventListener('touchend', this.touchEndHandler);
  }

  // 初始化选中状态
  initSelectedValue = () => {
    const { value, options } = this.props;
    if (!value) return;
    const initSelectedIndex = options && options.findIndex((item) => item.value === value);
    const indicator = document && document.querySelector('.picker__roller--indicator');
    const itemHeight = indicator && parseFloat(window.getComputedStyle(indicator).height);
    let scrollTop = 0;
    if (initSelectedIndex === 0) {
      scrollTop = 2 * itemHeight;
    } else if (initSelectedIndex === 1) {
      scrollTop = 1 * itemHeight;
    } else if (initSelectedIndex === 0) {
      scrollTop = 0;
    } else {
      scrollTop = -(initSelectedIndex - 2) * itemHeight;
    }
    this.setScrollDistance(scrollTop);
  }

  touchStartHandler = (e) => {
    e.preventDefault();
    const target = e.touches ? e.touches[0] : e;
    this.startTouchTop = target.pageY;
  }

  touchMoveHandler = (e) => {
    const target = e.touches ? e.touches[0] : e;
    const currentTouchTop = target.pageY;
    const moveY = currentTouchTop - this.startTouchTop;
    let scrollTop = this.scrollTop + moveY;

    // 约束滚动边界
    if (scrollTop > this.maxScrollTop || scrollTop < this.minScrollTop) {
      if (scrollTop > this.maxScrollTop) {
        scrollTop = this.maxScrollTop;
      } else {
        scrollTop = this.minScrollTop;
      }
    }
    this.setScrollDistance(scrollTop);
    this.startTouchTop = currentTouchTop;
  }

  touchEndHandler = (e) => {
    // 元素的位置准确卡在选中实线中
    const indicator = document && document.querySelector('.picker__roller--indicator');
    const itemHeight = indicator && parseFloat(window.getComputedStyle(indicator).height);
    const scrollTop = Math.round(this.scrollTop / itemHeight).toFixed(5) * itemHeight;
    this.setScrollDistance(scrollTop);
  }

  setScrollDistance = (top) => {
    this.scrollTop = top;
    const distance = top;
    const rollerList = document && document.querySelector('.picker__roller--list');
    rollerList.style.transform = `translate3d(0, ${distance}px, 0)`;
  }

  /**
   * 根据scrollTop来确认最终选择的是哪一项
   * 初始化，组件展示前5期，scrollTop = 0， 默认选中第3期，
   * 滚动后，若向下滚动，scrollTop 为 (itemHight * 当前项到 index = 2 的距离)
   * 滚动后，若向上滚动，scrollTop 为 -(itemHight * 当前项到 index = 2 的距离)
   * 例如：
   *  期数为 ['1', '2', '3', '4', '5', '6', '7']，item高度为 30
   *  对应scrollTop为 ['60', '30', '0', '-30', '-60', '-90', '-120']
   */
  getSelectItemIndex = () => {
    const { listItemHeight, scrollTop } = this;
    // 初始化时默认展示5项可选项。所以索引是固定的
    if ((scrollTop / listItemHeight) === 2) {
      return 0;
    } else if ((scrollTop / listItemHeight) === 1) {
      return 1;
    } else if ((scrollTop / listItemHeight) === 0) {
      return 2;
    } else {
      return Math.round(2 + ((scrollTop * -1) / listItemHeight)); // 理论上都是整数，这里兜底取整一下
    }
  }

  onCancel = () => {
    const { trackPrefix, beaconId, onCancel } = this.props;
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `${trackPrefix}.${beaconId}.Cancel` });
    onCancel && onCancel();
  }

  onConfirm = () => {
    const { trackPrefix, beaconId, onConfirm } = this.props;
    const selectedIndex = this.getSelectItemIndex();
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `${trackPrefix}.${beaconId}.Confirm`, beaconContent: { cus: { selectedIndex } } });
    onConfirm && onConfirm(selectedIndex);
  }

  render() {
    const {
      show, title, options, beaconId,
    } = this.props;

    return (
      <MUView className="picker">
        <MUDrawer show={show} beaconId={beaconId} placement="bottom" height="37%" onClose={this.onCancel}>
          <MUView className="picker__head">
            <MUView className="picker__head--cancel" onClick={this.onCancel}>取消</MUView>
            <MUView className="picker__head--title">{title}</MUView>
            <MUView className="picker__head--confirm" onClick={this.onConfirm}>确认</MUView>
          </MUView>
          <MUView className="picker__roller">
            <MUView className="picker__roller--list">
              {options.map((item, index) => (
                <MUView
                  className="picker__roller--list--item"
                  onClick={() => { }}
                >
                  {item.text}
                </MUView>
              ))}
            </MUView>
            <MUView className="picker__roller--mask" />
            <MUView className="picker__roller--indicator" />
          </MUView>
        </MUDrawer>
      </MUView>
    );
  }
}
