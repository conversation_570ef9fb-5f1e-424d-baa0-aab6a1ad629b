@import '../../components/weapp/index.scss';

.wait-pay-plan {
  &__scroll {
    height: calc(66vh - 100px);
    background: #FFFFFF;
    &__content {
      padding: 46px 30px constant(safe-area-inset-bottom) 40px;
      padding: 46px 30px env(safe-area-inset-bottom) 40px;
      .plan-row {
        display: flex;
        align-items: center;
        font-size: 24px;
        line-height: 28px;
        color: #333333;
        font-weight: normal;
        position: relative;
  
        .on-click-list {
          display: flex;
          justify-content: space-between;
          font-size: 28px;
          font-weight: 600;
          .plan-amount {
            &--red {
              color: #CC1F15;
            }
            &--green {
              color: #03D560;
            }
            &--gray {
              color: #a6a6a6;
            }
            &--through, .plan-amount--part {
              font-weight: normal;
              padding-left: 10px;
              font-size: 24px;
            }
            &--through {
              color: #a6a6a6;
              text-decoration: line-through;
            }
            &--part {
              color: #03D560;
            }
            &--break {
              font-weight: normal;
              padding-left: 0 !important;
              margin-top: 10px;
            }
            &__info {
              margin-left: 10px;
            }
            &--last {
              font-size: 24px;
            }
          }
          .next-bg {
            right: 2px;
            top: -14px;
            position: absolute;
            width: 56px;
            height: 56px;
            &-btn {
              margin: 14px 0 0 30px;
              width: 26px;
            }
          }
        }
  
        .plan-date {
          width: 140px;
          white-space: nowrap;
          text-align: right;
        }
  
        .dot {
          margin: 0 35px;
          width: 10px;
          height: 10px;
          border: 4px solid $color-brand;
          /* stylelint-disable-next-line */
          border-radius: 100PX; // 大写px别改，大写taro不会转成rem，转成rem在某些安卓机型上针对小数点的圆角会有问题
        }
  
        .plan-status {
          margin: 0 35px;
          width: 17px;
          height: 17px;
          font-size: 0;
          line-height: 18px;
          image, .taro-img {
            width: 100%;
            height: 100%;
          }
        }
  
        .highlight {
          margin-left: 10px;
          padding: 6px 10px;
          border-radius: 4px;
          font-size: 22px;
          line-height: 24px;
        }

        .wait-pay-now {
          background: #ff88441a;
          color: #ff8844;
        }

        .wait-pay-overdue {
          background: #CC1F15;
          color: #fff;
        }

        .wait-pay-due {
          background: #E1EBFF;
          color: #3477FF;
        }
      }
  
      .no-first {
        margin-top: 62px;
  
        .split {
          border-left: 2px solid #CACACA;
          border-radius: 2px;
          position: absolute;
          top: -62px;
          left: 183px;
          height: 60px;
        }
      }
    }
  }

  .brand-border-weapp {
    border-color: $color-brand;
  }
}
