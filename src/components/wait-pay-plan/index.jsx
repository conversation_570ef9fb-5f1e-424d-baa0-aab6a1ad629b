/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUScrollView, MUIcon, MUText, MUImage, MUModal
} from '@mu/zui';
import PropTypes from 'prop-types';
import { EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { setStore } from '@api/store';
import Util from '@utils/maxin-util';
import BottomDrawer from '@components/bottom-drawer/new-index';
import statusOverdue from '@components/assets/img/status-overdue.png';
import statusSuccess from '@components/assets/img/status-success.png';
import Madp from '@mu/madp';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}
// const arrowIcon = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828e80449.png';

export default class WaitPayPlan extends Component {
  static propTypes = {
    planList: PropTypes.array,
    scene: PropTypes.string,
    waiveRatio: PropTypes.number,
    showWaitPayPlanDefault: PropTypes.bool,
    trackPage: PropTypes.string,
    custStatus: PropTypes.string,
    deductTime: PropTypes.string,
  }

  static defaultProps = {
    planList: [], // 账单列表
    scene: '', // 场景，协商还；
    waiveRatio: 0, // 减免比例
    showWaitPayPlanDefault: false, // 展开待还计划默认值
    trackPage: '',
    custStatus: '',
    deductTime: '',
  }

  constructor(props) {
    super(props);
    this.state = {
      showWaitPayPlan: false,
      showSettleReduce: false, // 展示协商还息费减免说明
    };
  }

  componentDidMount() {
    const { showWaitPayPlanDefault } = this.props;
    if (showWaitPayPlanDefault) {
      this.show();
    } else {
      Madp.removeStorageSync('hideRepayPlan', 'SESSION');
    }
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  // 打开待还计划
  show = () => this.setState({ showWaitPayPlan: true });

  // 关闭待还计划
  close = () => {
    const { trackPage, custStatus } = this.props;
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPage}WaitPayPlanClose`, beaconContent: { cus: { custStatus } } });
    this.setState({ showWaitPayPlan: false });
  };

  // 跳转X期待还详情
  toRepayList(plan) {
    const { trackPage, custStatus, deductTime } = this.props;
    let addrLink;
    if (plan.waitPayStatus !== 'undue') {
      addrLink = `/pages/bill-list-near/index?showNotice=1&custStatus=${custStatus}${plan.waitPayStatus === 'due' ? '&selectAll=1' : ''}`;
    } else {
      setStore({ futureBillList: plan.billList });
      addrLink = `/pages/bill-list-future/index?fromIndex=1&repayDate=${plan.date.join('-')}&custStatus=${custStatus}${deductTime ? `&deductTime=${deductTime}` : ''}`;
    }
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPage}FutureToDetail`, beaconContent: { cus: { custStatus } } });
    Util.router.push(addrLink);
  }

  dateFormat(date) {
    let showDate = '';
    if (!date) return showDate;
    showDate = `${Number(date[0]).toString()}-${(Number(date[1]) >= 10 ? Number(date[1]) : `0${Number(date[1])}`).toString()}-${(Number(date[2]) >= 10 ? Number(date[2]) : `0${Number(date[2])}`).toString()}`;
    return showDate;
  }

  getPlanView(plan, index, lastCnt, waiveRatio) {
    return (
      <MUView className={index ? 'plan-row no-first' : 'plan-row'}>
        <MUView className="plan-date">{this.dateFormat(plan.date)}</MUView>
        {plan.repayStatus === '2' || plan.repayStatus === '5' ? <MUView className="plan-status">
          <MUImage src={plan.repayStatus === '2' ? statusOverdue : statusSuccess} />
        </MUView> : <MUView className="brand-border-weapp brand-border dot" />}
        {index === lastCnt
          ? <MUView
              className="on-click-list"
              beaconId="SettleReduceTip"
              onClick={() => this.setState({
                showSettleReduce: true
              })}
          >
            <MUView className="plan-amount plan-amount--last">
              <MUText className={waiveRatio && Number(waiveRatio) > 0 ? 'plan-amount--red' : ''}>
                结清剩余金额{waiveRatio && Number(waiveRatio) > 0 ? `，息费减${waiveRatio}%` : ''}
              </MUText>
              <MUIcon className="plan-amount__info" value="info" size={13} color="#a6a6a6" />
            </MUView>
          </MUView>
          : <MUView className="on-click-list">
            {plan.repayStatus === '4' ? <MUView className="plan-amount--gray">
              <MUText>已提前完成，无需还款</MUText>
            </MUView> : <MUView>
              {waiveRatio && Number(waiveRatio) > 0 ? <MUView className="plan-amount">
                <MUText className={`plan-amount--${plan.repayStatus === '5' ? 'green' : 'red'}`}>{plan.repayStatus === '5' ? plan.realPaidAmt : plan.taskThresholdAmt}元</MUText>
                {/* 协商还计划的时候，无息费减免情况下不展示划线价 */}
                {Number(plan.expectWaiveAmt) > 0 ? <MUText className="plan-amount--through">{plan.repayStatus === '5' ? Util.floatAdd(Number(plan.realPaidAmt), Number(plan.realWaivedAmt)) : plan.taskAmt}元</MUText> : null}
                {Number(plan.realPaidAmt) > 0 && Util.floatMinus(Number(plan.taskThresholdAmt), Number(plan.realPaidAmt)) > 0 && Number(plan.taskThresholdAmt) < 1000 ? <MUText className="plan-amount--part">已还{plan.realPaidAmt}元</MUText> : null}
                {Number(plan.realPaidAmt) > 0 && Util.floatMinus(Number(plan.taskThresholdAmt), Number(plan.realPaidAmt)) > 0 && Number(plan.taskThresholdAmt) >= 1000 ? <MUView className="plan-amount--part plan-amount--break">已还{plan.realPaidAmt}元</MUView> : null}
              </MUView> : <MUView className="plan-amount">
                <MUText className={`plan-amount${plan.repayStatus === '5' ? '--green' : ''}`}>{plan.repayStatus === '5' ? plan.realPaidAmt : plan.taskThresholdAmt}元</MUText>
                {Number(plan.realPaidAmt) > 0 && Util.floatMinus(Number(plan.taskThresholdAmt), Number(plan.realPaidAmt)) > 0 ? <MUText className="plan-amount--part">已还{plan.realPaidAmt}元</MUText> : null}
              </MUView>}
            </MUView>}
          </MUView>}
        <MUView className="split" />
      </MUView>
    );
  }

  render() {
    const { showWaitPayPlan, showSettleReduce } = this.state;
    const { planList, scene, waiveRatio } = this.props;
    let lastCnt = 0;
    if (scene === 'consult') {
      lastCnt = planList.filter((item) => (item.repayStatus !== '3' && item.repayStatus !== '4')).length - 1;
    }
    return (
      <MUView className="wait-pay-plan">
        <BottomDrawer
          isOpen={showWaitPayPlan}
          title={scene === 'consult' ? '协商还计划' : '还款计划'}
          onClose={this.close}
          onTopCloseClick={this.close}
        >
          <MUScrollView
            className="wait-pay-plan__scroll"
            scrollY
          >
            {scene === 'consult' ? (
              <MUView className="wait-pay-plan__scroll__content">
                {planList.map((plan, index) => this.getPlanView(plan, index, lastCnt, waiveRatio))}
              </MUView>
            ) : (
              <MUView className="wait-pay-plan__scroll__content">
                {planList.map((plan, index) => (
                  <MUView className={index ? 'plan-row no-first' : 'plan-row'}>
                    <MUView className="plan-date">{this.dateFormat(plan.date)}</MUView>
                    <MUView className="brand-border-weapp brand-border dot" />
                    <MUView className="on-click-list" beaconId="ToRepayList" onClick={() => this.toRepayList(plan)}>
                      <MUView className="plan-amount">
                        {plan.amount}元
                        {plan.waitPayStatus === 'due' ? <MUText className="highlight wait-pay-due">可还款</MUText> : null}
                        {plan.waitPayStatus === 'now' ? <MUText className="highlight wait-pay-now">今日到期</MUText> : null}
                        {plan.waitPayStatus === 'overdue' ? <MUText className="highlight wait-pay-overdue">已逾期</MUText> : null}
                      </MUView>
                      <MUView className="next-bg">
                        <MUIcon className="next-bg-btn" value="arrow-right" size={13} color="#CACACA" />
                      </MUView>
                    </MUView>
                    <MUView className="split" />
                  </MUView>
                ))}
              </MUView>
            )}
          </MUScrollView>
        </BottomDrawer>
        <MUModal
          isOpened={showSettleReduce}
          content="分期期间仍然会产生新的息费，具体情况以您借款时签署的合同为准"
          confirmText="好的"
          onConfirm={() => this.setState({ showSettleReduce: false })}
          onClose={() => this.setState({ showSettleReduce: false })}
        />
      </MUView>
    );
  }
}
