/* eslint-disable react/destructuring-assignment */
import { Component } from '@tarojs/taro';
import {
  MUDialog, MUImage, MUView
} from '@mu/zui';
import PropTypes from 'prop-types';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

const ANIMATION_IMG = 'https://file.mucfc.com/ebn/2/10/202212/20221222142648ccc917.png';

export default class LoadingDialog extends Component {
  static propTypes = {
    countTime: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    onRef: PropTypes.func,
    isRepayCheckOut: PropTypes.bool, // 是否是还款收银台
    serviceType: PropTypes.string, // 服务类型
    isNewStyle: PropTypes.bool, // 是否是新的样式
  }

  static defaultProps = {
    countTime: 0,
    onRef: () => {},
    isRepayCheckOut: false,
    serviceType: '',
    isNewStyle: false,
  }

  constructor(props) {
    super(props);
    this.state = {
      isOpened: false,
      time: props.countTime,
    };
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  componentDidMount() {
    this.props.onRef(this);
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.countTime !== this.props.countTime) {
      this.setState({
        time: nextProps.countTime,
      });
    }
  }

  show(callback) {
    const { time } = this.state;
    if (!this.timer && time && time > 0) {
      this.setState({
        isOpened: true
      }, () => {
        this.timer = setInterval(() => {
          this.setState((preState) => ({
            time: preState.time - 1
          }), () => {
            const { time } = this.state;
            if (time < 1) {
              this.hide();
              if (typeof callback === 'function') {
                callback(true);
              }
            }
          });
        }, 1000);
      });
    }
  }

  hide() {
    this.setState({
      isOpened: false,
    }, () => {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        // 重置轮循弹窗状态
        const { countTime } = this.props;
        this.setState({ time: countTime });
      }
    });
  }

  render() {
    const { isOpened, time } = this.state;
    const { isNewStyle, isRepayCheckOut, serviceType, assignLoadingTitle } = this.props;
    let contentTitle = '正在审批中...';
    if (assignLoadingTitle) {
      contentTitle = assignLoadingTitle;
    } else if (isRepayCheckOut) {
      contentTitle = '正在处理中...';
    } else if (serviceType === 'advanced-stage') {
      contentTitle = '再分期申请处理中...';
    } else if (serviceType === 'bills-staging') {
      contentTitle = '账单分期申请正在审批中...';
    } else if (serviceType === 'renew-loans') {
      contentTitle = '申请审批中';
    }

    if (!isNewStyle) return (<MUDialog
      beaconId="LoadingDialog"
      isOpened={isOpened}
      className="loading-dialog"
    >
      <MUView className="loading-dialog-content">
        <MUView className="loading-dialog-content-animation">
          <MUImage src={ANIMATION_IMG} className="loading-dialog-content-animation-img" />
          <MUView className="loading-dialog-content-animation-count">{time}</MUView>
        </MUView>
        <MUView className="loading-dialog-content-title">{contentTitle}</MUView>
        <MUView className="loading-dialog-content-sub">请耐心等待</MUView>
      </MUView>
    </MUDialog>);

    // 新的底部loading弹窗，目前仅用在 “分期还本“ 办理页
    return (isOpened ? (<MUView
      className="loading-new-dialog"
      beaconId="LoadingDialog"
      onTouchMove={(e) => { e.preventDefault(); e.stopPropagation(); }}
    >
      <MUView className="loading-content">
        <MUView className="loading-content-animation">
          <MUImage className="loading-content-animation__img" src={ANIMATION_IMG} />
          <MUView className="loading-content-animation__count">{time}</MUView>
        </MUView>
        <MUView className="loading-content__title">{contentTitle}</MUView>
        <MUView className="loading-content__sub">正在处理中，请稍加等待</MUView>
      </MUView>
    </MUView>) : <MUView />);
  }
}
