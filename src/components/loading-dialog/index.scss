.loading-dialog {
  &-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 90px 0 60px;

    &-animation {
      position: relative;
      height: 140px;
      width: 140px;
      display: flex;
      align-items: center;
      justify-content: center;

      &-img {
        position: absolute;
        height: 140px;
        width: 140px;
        left: 50%;
        top: 50%;
        animation: loan-loading 1s linear none 0s infinite;
      }

      &-count {
        font-size: 28px;
      }
    }

    &-title {
      padding: 40px 0 12px;
      font-size: 32px;
    }

    &-sub {
      font-size: 26px;
      color: #808080;
    }
  }

  .mu-dialog__overlay {
    background-color: #F3F3F3;
  }
}

.loading-new-dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1000;

  .loading-content {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 750px;
    height: 55%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 30px 30px 0 0;
    padding-bottom: 40px;
    box-sizing: border-box;
    background-image: url('https://file.mucfc.com/zlh/0/0/202404/20240402114413e5e609.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;

    &-animation {
      position: relative;
      height: 120px;
      width: 120px;
      display: flex;
      align-items: center;
      justify-content: center;

      &__img {
        position: absolute;
        height: 120px;
        width: 120px;
        left: 50%;
        top: 50%;
        animation: loan-loading 1s linear none 0s infinite;
      }

      &__count {
        font-size: 28px;
      }
    }

    &__title {
      padding: 40px 0 15px;
      font-size: 40px;
      font-weight: 600;
      color: #333333;
    }

    &__sub {
      font-size: 28px;
      color: #808080;
    }
  }
}

@keyframes loan-loading {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
