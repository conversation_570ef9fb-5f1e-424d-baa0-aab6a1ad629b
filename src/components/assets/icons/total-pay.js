/* eslint-disable max-len */
export const ToalPayImg = {
  base64: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTRweCIgaGVpZ2h0PSI1NHB4IiB2aWV3Qm94PSIwIDAgNTQgNTQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuc1hsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4NCiAgICAgIDx0aXRsZT7lhajpg6jlvoXov5g8L3RpdGxlPg0KICAgICAgPGRlZnM+DQogICAgICAgIDxjaXJjbGUgaWQ9InBhdGgtMSIgY3g9IjM5IiBjeT0iMjciIHI9IjE1Ij48L2NpcmNsZT4NCiAgICAgIDwvZGVmcz4NCiAgICAgIDxnIGlkPSLliIflm74iIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPg0KICAgICAgICA8ZyBpZD0i5YWo6YOo5b6F6L+YIj4NCiAgICAgICAgICA8ZyBpZD0i5YWo6YOo5b6F6L+YaWNvbiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4wMDAwMDAsIDYuMDAwMDAwKSI+DQogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTQiIGZpbGw9IiM1RjlGRjYiIHg9IjAiIHk9IjAiIHdpZHRoPSI0OCIgaGVpZ2h0PSI0MCIgcng9IjYiPjwvcmVjdD4NCiAgICAgICAgICAgIDxnIGlkPSJPdmFsIj4NCiAgICAgICAgICAgICAgPHVzZSBmaWxsPSIjNUY5RkY2IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIHhsaW5rSHJlZj0iI3BhdGgtMSIgLz4NCiAgICAgICAgICAgICAgPGNpcmNsZSBzdHJva2U9IiNGRkZGRkYiIHN0cm9rZS13aWR0aD0iMyIgY3g9IjM5IiBjeT0iMjciIHI9IjE2LjUiPjwvY2lyY2xlPg0KICAgICAgICAgICAgPC9nPg0KICAgICAgICAgICAgPGcgaWQ9Ikdyb3VwIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgzNS4wMDAwMDAsIDIwLjAwMDAwMCkiIGZpbGw9IiNGRkZGRkYiPg0KICAgICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTUiIHg9IjAiIHk9IjgiIHdpZHRoPSIxMiIgaGVpZ2h0PSI0Ij48L3JlY3Q+DQogICAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtNS1Db3B5IiB4PSIwIiB5PSIwIiB3aWR0aD0iNCIgaGVpZ2h0PSIxMiI+PC9yZWN0Pg0KICAgICAgICAgICAgPC9nPg0KICAgICAgICAgIDwvZz4NCiAgICAgICAgPC9nPg0KICAgICAgPC9nPg0KPC9zdmc+DQo='
};
