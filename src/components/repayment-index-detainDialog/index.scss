.repayment-index {
  &-detainDialog {
    &-content {
      display: flex;
      flex-direction: column;
      align-items: center;

      &-icon {
        margin: auto;
        margin-top: 36px;
      }

      &-text {
        margin: 20px auto;
        font-size: 36px;
        line-height: 60px;
        margin: 40px auto;
        width: 450px;
        padding-left: 50px;
      }

      &-confirm {
        font-size: 36px;
        width: 400px;
      }

      &-cancel {
        margin: 0 auto;
        border: none;
        background-color: #FFF !important;
        color: #808080;
        font-size: 36px;
        width: 400px;
        margin-bottom: 20px;
        border-color: #FFF !important;
      }
    }

    .mu-modal__container {
      overflow: visible;
    }

    .dialog__infomation {
      width: calc(100% - 40px);
      position: absolute;
      display: flex;
      align-items: center;
      bottom: -48px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 20px;
      color: #ffffff;
      text-align: center;
      //line-height: 32px;
      z-index: 2001;

      &--tag {
        width: 68px;
        min-width: 68px;
        height: 32px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        font-size: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
      }

      // --兼容微信小程序
      >mu-view:nth-child(2) {
        flex: 1;
      }

      &--text {
        flex: 1;
        text-align: center;
        margin-right: 68px;
        // 保证当文字接近22字时一行显示完，优先级大于居中
        white-space: nowrap;
        line-height: 32px;
      }
    }
  }
}