/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable max-len */
/* eslint-disable react/sort-comp */

import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView,
  MUModal,
  MUButton,
  MUIcon
} from '@mu/zui';
import PropTypes from 'prop-types';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}
export default class RepaymentDetainDialog extends Component {
  static propTypes = {
    configData: PropTypes.object,
    isOpened: PropTypes.bool,
    contentText: PropTypes.string,
    onConfirm: PropTypes.func,
    onCancel: PropTypes.func,
  };

  static defaultProps = {
    configData: {},
    isOpened: false,
    contentText: '你的借款已经逾期，还差一步即可完成还款啦！',
    onCancel: () => { },
    onConfirm: () => { },
  };

  constructor(props) {
    super(props);
    this.confirmOpen = true;
  }
  static options = {
    addGlobalClass: true
  }

  config = {
    "styleIsolation" : "shared"
  }

  componentDidMount() {
    const { isOpened, configData } = this.props;
    if (isOpened) {
      if (this.returnFrequencyControl(configData)) {
        this.confirmOpen = true;
        Madp.setStorageSync('repaymentIndexDetainDialogShowData', {
          lastRenderData: configData,
          lastShowTime: new Date().getTime(),
        });
      } else {
        this.confirmOpen = false;
      }
    }
  }

  returnFrequencyControl(configData) {
    const {
      lastRenderData = {},
      lastShowTime = 0,
    } = Madp.getStorageSync('repaymentIndexDetainDialogShowData');
    const { frequency = '3' } = configData || {};
    console.log('--------------lastRenderData', lastRenderData);
    console.log('--------------lastShowTime', lastShowTime);
    console.log('--------------frequency', frequency);
    const nowTime = new Date().getTime();
    if (lastRenderData === '{}') {
      return true;
    } else if (frequency === '0' && !lastShowTime) {
      // 只弹一次
      return true;
    } else if (frequency === '1' && (nowTime - lastShowTime) >= 86400000) {
      // 一天弹一次
      return true;
    } else if (frequency === '2' && (nowTime - lastShowTime) >= 604800000) {
      // 一周弹一次
      return true;
    } else if (frequency === '3') {
      // 每次都弹
      return true;
    } else {
      this.confirmOpen = false;
      return false;
    }
  }

  render() {
    const {
      isOpened, contentText, onCancel, onConfirm, configData
    } = this.props;
    if (isOpened) {
      if (this.returnFrequencyControl(configData)) {
        this.confirmOpen = true;
        Madp.setStorageSync('repaymentIndexDetainDialogShowData', {
          lastRenderData: configData,
          lastShowTime: new Date().getTime(),
        });
      } else {
        this.confirmOpen = false;
        if (window && window.history && window.history.length <= 2) {
          Madp.closeWebView();
        } else {
          Madp.navigateBack({ delta: 1 });
        }
      }
    }
    return (
      <MUModal
        closeOnClickOverlay={false}
        beaconId="RepaymentDetainDialog"
        isOpened={isOpened && this.confirmOpen}
        className="repayment-index-detainDialog"
      >
        <MUView className="repayment-index-detainDialog-content">
          <MUIcon
            value="tip"
            size={72}
            className="repayment-index-detainDialog-content-icon"
          />
          <MUView className="repayment-index-detainDialog-content-text">
            {contentText}
            {/* 你的借款已经逾期4天，还差一步即可完成还款啦！ */}
          </MUView>
          <MUButton
            type="primary"
            className="repayment-index-detainDialog-content-confirm"
            beaconId="RepaymentDetainDialogConfirm"
            onClick={onConfirm}
          >
            继续还款
          </MUButton>
          <MUButton
            type="secondary"
            className="repayment-index-detainDialog-content-cancel"
            beaconId="RepaymentDetainDialogCancel"
            onClick={onCancel}
          >
            放弃还款
          </MUButton>
        </MUView>
        <MUView className="dialog__infomation">
          <MUView className="dialog__infomation--tag">服务</MUView>
          <MUView className="dialog__infomation--text">本弹窗由招联金融推送</MUView>
        </MUView>
      </MUModal>
    );
  }
}
