/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import {
  MUView,
  MUModal,
  MUIcon,
  MUButton,
  MURichText
} from '@mu/zui';

/**
 * 全部待还-有博弈梯度减免券-未逾期特享还款场景
 * 取消借据勾选提示，并引导进入息费减免流程
 */
export default function LeadToFeeReduceModal({
  isOpened,
  onConfirm,
  onCancel,
  themeColor
}) {
  return (
    <MUModal
      closeOnClickOverlay={false}
      beaconId="LeadToFeeReduceModal"
      isOpened={isOpened}
      className="lead-dialog"
    >
      <MUView className="lead-dialog-content">
        <MUIcon
          value="tip"
          size={72}
          color={themeColor}
          className="lead-dialog-content-icon"
        />
        <MUView className="lead-dialog-content-title">
          取消勾选不可享优惠
        </MUView>
        <MURichText
          className="lead-dialog-content-text"
          nodes={'全选借据还款，可享受<span style="color: #FE870C;">息费减免优惠</span>，也可以部分还款哦~'}
        />
        <MUButton
          type="primary"
          className="lead-dialog-content-confirm"
          beaconId="LeadToFeeReduceModalConfirm"
          onClick={() => {
            onConfirm && onConfirm();
          }}
        >
          去优惠还款
        </MUButton>
        <MUButton
          type="secondary"
          className="lead-dialog-content-cancel"
          beaconId="LeadToFeeReduceModalCancel"
          onClick={() => {
            onCancel && onCancel();
          }}
        >
          仍然取消勾选
        </MUButton>
      </MUView>
    </MUModal>
  );
}
