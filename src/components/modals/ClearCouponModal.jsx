/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import {
  MUView,
  MUModal,
  MUIcon,
  MUButton,
  MUImage,
  MUText,
} from '@mu/zui';

const guideCouponCheck = 'https://file.mucfc.com/ebn/3/0/202404/20240425205237fce723.png';

/**
 * 结清减免券/固额减免券卷弹窗
 */
export default function ClearCouponModal({
  isOpened,
  onConfirm,
  onCancel,
  newVersion,
  minTransAmt,
  guideWaiveAmt,
}) {
  return (
    <MUModal
      closeOnClickOverlay={false}
      beaconId="RepayCouponDialog"
      isOpened={isOpened}
      className="repay-coupon-dialog"
    >
      <MUView className="repay-coupon-dialog-content">
        {newVersion ? (
          <MUView className="repay-coupon-dialog-content__wrap">
            <MUView className="repay-coupon-dialog-content__picture"><MUImage src={guideCouponCheck} /></MUView>
            <MUView className="repay-coupon-dialog-content__title">息费优惠</MUView>
            <MUView className="repay-coupon-dialog-content__explain">
              现在还款{minTransAmt}元，可<MUText className="repay-coupon-dialog-content__explain--special">减免{guideWaiveAmt}元息费</MUText>，是否享受优惠？
            </MUView>
          </MUView>
        ) : (
          <MUView className="repay-coupon-dialog-content__wrap">
            <MUIcon
              value="tip"
              size={72}
              className="repay-coupon-dialog-content-icon"
            />
            <MUView className="repay-coupon-dialog-content-tip">
              温馨提示
            </MUView>
            <MUView className="repay-coupon-dialog-content-text">
              您本次还款金额未达到使用门槛，若多次还款可能无法享受减免优惠，请确认是否继续？
            </MUView>
          </MUView>
        )}
        <MUButton
          type="primary"
          className="repay-coupon-dialog-content-confirm"
          beaconId="RepayCouponDialogConfirm"
          onClick={() => {
            onConfirm && onConfirm();
          }}
        >
          {newVersion ? `还${minTransAmt}元  享优惠` : '继续还款'}
        </MUButton>
        <MUButton
          type="secondary"
          className="repay-coupon-dialog-content-cancel"
          beaconId="RepayCouponDialogCancel"
          onClick={() => {
            onCancel && onCancel();
          }}
        >
          {newVersion ? '不使用优惠' : '暂不还款'}
        </MUButton>
      </MUView>
    </MUModal>
  );
}
