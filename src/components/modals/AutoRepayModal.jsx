/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import {
  MUView,
  MUModal,
  MUButton,
  MURichText,
} from '@mu/zui';

/**
 * 微光卡开通额度不足临时提额开卡弹窗
 */
export default function AutoRepayModal({
  isOpened,
  onConfirm,
  onCancel,
}) {
  return (
    <MUModal
      beaconId="AvoidDupPaymentModal"
      className="avoidDupPayModal"
      type="text"
      isOpened={isOpened}
      closeOnClickOverlay={false}
      onClose={() => { onCancel && onCancel(); }}
    >
      <MUView className="at-modal__header mu-modal__header">
        <MUView className="title">温馨提示</MUView>
      </MUView>
      <MUView className="taro-scroll taro-scroll-view__scroll-y at-modal__content mu-modal__content">
        <MUView className="content-text">
          <MURichText className="content-text__container" nodes="您有1笔自动还款正在处理中，为避免重复还款，请稍后再试。<br />您也可取消自动还款并手动还款，自动还款取消后，若已自动扣款成功，会于24小时内将您的资金退回至您的银行卡或小招荷包中，请留意后续通知。" />
        </MUView>
      </MUView>
      <MUView className="mu-modal__footer">
        <MUView className="at-modal__action">
          <MUButton
            className="button-1"
            beaconId="ConConfirm"
            onClick={() => { onConfirm && onConfirm(); }}
          >
            再等等
          </MUButton>

          <MUButton
            className="button-2"
            beaconId="ConCancel"
            onClick={() => { onCancel && onCancel(); }}
          >
            取消自动还款，并手动还款
          </MUButton>
        </MUView>
      </MUView>
    </MUModal>
  );
}
