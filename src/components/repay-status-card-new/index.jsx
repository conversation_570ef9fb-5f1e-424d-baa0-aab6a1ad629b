/* eslint-disable react/no-unused-prop-types */
/* eslint-disable object-curly-newline */
/* eslint-disable react/jsx-closing-bracket-location */
/* eslint-disable quote-props */
/* eslint-disable indent */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-param-reassign */
/* eslint-disable react/sort-comp */
/* eslint-disable max-len */
/* eslint-disable no-nested-ternary */
import { Component } from '@tarojs/taro';
import {
  MUView, MUImage, MUIcon, MURichText, MUButton
} from '@mu/zui';
import PropTypes from 'prop-types';
import { repayStatusType, loanUrl } from '@utils/constants';
import LoadingGif from '@components/assets/img/loanding.gif';
import Util from '@utils/maxin-util';
import RepayBubble from '@components/repay-bubble/index';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import WaRichtext from '@mu/wa-richtext';
import FingerGif from '@components/assets/img/finger-confirm.gif';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

const billImg = 'https://file.mucfc.com/ebn/3/0/202404/20240425205237193381.png';
const deductImg = 'https://file.mucfc.com/ebn/3/0/202404/202404252052373794ba.png';
const noticeImg = 'https://file.mucfc.com/ebn/3/0/202404/2024042520523746949c.png';
const prepayImg = 'https://file.mucfc.com/ebn/3/0/202404/202404252052379b4df6.png';

// 气泡文案取运营配置
const serviceBubble = {
  'RenewLoans': 'HK01.HK01WA001', // 分期还本
  'ConsultRepay': 'HK01.HK01WA002', // 协商还
  'BillAdvancedStage': 'HK01.HK01WA003', // 再分期
  'FeeReduce': 'HK01.HK01WA004', // 还的多减得多
  'Bargain': 'HK01.HK01WA005', // 讨价还价
  'BillExtend': 'HK01.HK01WA006', // 延后还
  'Promise': 'HK01.HK01WA007', // 缓催
  'BillsStaging': 'HK01.HK01WA008', // 账单分期
};

export default class RepayStatusCardNew extends Component {
  static propTypes = {
    statusType: PropTypes.string,
    availLoanFlag: PropTypes.bool,
    availLoanWaiveFlag: PropTypes.bool,
    statusTitle: PropTypes.string,
    titleCall: PropTypes.func,
    noTitleGuide: PropTypes.bool,
    statusAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    courtCostBalance: PropTypes.string,
    warningIcon: PropTypes.string,
    warningTextObj: PropTypes.array,
    overdueFlag: PropTypes.bool, // 逾期打标或逾期天数大于0
    warningGuideText: PropTypes.string,
    warningGuideCall: PropTypes.func,
    showVPlusTips: PropTypes.bool,
    disabledBtn: PropTypes.bool,
    btnText: PropTypes.string,
    nextStep: PropTypes.func,
    availRepayWaiveFlag: PropTypes.bool,
    showRandomReduce: PropTypes.bool,
    randomReduceText: PropTypes.string,
    showServiceBtn: PropTypes.bool,
    recommendService: PropTypes.object,
    serviceStep: PropTypes.func,
    negotiateRepayTaskBill: PropTypes.object, // 协商还数据
    consultStep: PropTypes.func,
    trackPage: PropTypes.string,
    custStatus: PropTypes.string,
    availShowButtons: PropTypes.bool,
    setClipboardTextCall: PropTypes.func,
  }

  static defaultProps = {
    statusType: '', // [loading/加载中, empty/无待还, clear/本期还清, detail/展示详情]
    availLoanFlag: false,
    availLoanWaiveFlag: false,
    statusTitle: '',
    titleCall: () => {}, // 跳转待还
    noTitleGuide: false,
    statusAmount: '',
    courtCostBalance: '0.00',
    warningIcon: '',
    warningTextObj: [],
    overdueFlag: false,
    warningGuideText: '',
    warningGuideCall: () => {},
    showVPlusTips: false,
    disabledBtn: false, // 禁止按钮点击
    btnText: '',
    nextStep: () => { },
    availRepayWaiveFlag: false,
    showRandomReduce: false,
    randomReduceText: '',
    showServiceBtn: false, // 还款服务入口
    recommendService: {},
    serviceStep: () => { },
    negotiateRepayTaskBill: {},
    consultStep: () => { },
    trackPage: '',
    custStatus: '',
    availShowButtons: false,
    setClipboardTextCall: () => { },
  }

  constructor(props) {
    super(props);
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    'styleIsolation': 'shared'
  }

  componentWillReceiveProps(nextProps) {
    const {
      availRepayWaiveFlag, trackPage, showServiceBtn, availLoanFlag, availLoanWaiveFlag,
    } = this.props;
    const { custStatus, recommendService } = nextProps || {};
    if (availRepayWaiveFlag !== (nextProps || {}).availRepayWaiveFlag && (nextProps || {}).availRepayWaiveFlag) {
      dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPage}AvailRepayWaive`, beaconContent: { cus: { custStatus } } });
    }
    if (showServiceBtn !== (nextProps || {}).showServiceBtn && (nextProps || {}).showServiceBtn) {
      dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPage}ShowServiceBtn`, beaconContent: { cus: { custStatus, serviceName: (recommendService || {}).title } } });
    }
    if (availLoanFlag !== (nextProps || {}).availLoanFlag && (nextProps || {}).availLoanFlag) {
      dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPage}AvailLoan`, beaconContent: { cus: { custStatus } } });
    }
    if (availLoanWaiveFlag !== (nextProps || {}).availLoanWaiveFlag && (nextProps || {}).availLoanWaiveFlag) {
      dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPage}AvailLoanWaive`, beaconContent: { cus: { custStatus } } });
    }
  }

  jumpWaitPay() {
    const { noTitleGuide, titleCall } = this.props;
    if (noTitleGuide) return;
    if (titleCall && typeof titleCall === 'function') {
      titleCall();
    }
  }

  warningGuideCall() {
    const { warningGuideCall } = this.props;
    if (warningGuideCall && typeof warningGuideCall === 'function') {
      warningGuideCall();
    }
  }

  mainButtonCall(type) {
    const { consultStep, nextStep } = this.props;
    if (type === 'loan') {
      Util.router.replace({
        path: loanUrl,
        query: {
          mtagc: '31031.01.01'
        }
      });
    } else if (type === 'consult') {
      if (consultStep && typeof consultStep === 'function') {
        consultStep(type);
      }
    } else {
      if (nextStep && typeof nextStep === 'function') {
        nextStep();
      }
    }
  }

  subButtonCall() {
    const {
      recommendService, trackPage, custStatus
    } = this.props;
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPage}ServiceBtnClick`, beaconContent: { cus: { custStatus, serviceName: (recommendService || {}).title } } });
    const { uniqueName, targetUrl } = recommendService || {};
    let finalTargetUrl = targetUrl;
    if (uniqueName === 'BillAdvancedStage') {
      finalTargetUrl = finalTargetUrl.indexOf('?') > -1 ? `${targetUrl}&mtago=31025.01.09` : `${targetUrl}?mtago=31025.01.09`;
    }
    if (uniqueName === 'BillExtend') {
      finalTargetUrl = finalTargetUrl.indexOf('?') > -1 ? `${targetUrl}&mtago=31025.01.01` : `${targetUrl}?mtago=31025.01.01`;
    }
    Util.configUrlJump(finalTargetUrl, 'homeIndex');
  }

  getRepayBubbleParam = () => {
    let repayBubbleParam = {
      type: 'fillBottom',
    };
    const {
      lastCntFlag, repayStatus, overDueDate, repayDate, taskShouldAmt, expectWaiveAmt,
      nextRepayDate, nextTaskShouldAmt, waiveRatio
    } = this.props.negotiateRepayTaskBill || {};
    const overDueDateCollection = Util.getDateCollection(overDueDate);
    const repayDateCollection = Util.getDateCollection(repayDate);
    if ((expectWaiveAmt && Number(expectWaiveAmt) > 0) && (waiveRatio && Number(waiveRatio) > 0)) {
      if (lastCntFlag !== 'Y') {
        // 逾期
        if (repayStatus === '2') {
          repayBubbleParam.contentArr = [
            { contentText: '协商还已超期，', contentColor: '' },
            { contentText: `${Number(overDueDateCollection[1]) >= 10 ? Number(overDueDateCollection[1]) : `0${Number(overDueDateCollection[1])}`}月${overDueDateCollection[2]}日`, contentColor: 'red' },
            { contentText: '前还款，可继续享受', contentColor: '' },
            { contentText: '息费减免、缓催', contentColor: 'red' },
            { contentText: '福利', contentColor: '' },
          ];
        } else {
          // 当期未还或还部分
          if (taskShouldAmt && Number(taskShouldAmt) > 0) {
            repayBubbleParam.contentArr = [
              { contentText: `${Number(repayDateCollection[1]) >= 10 ? Number(repayDateCollection[1]) : `0${Number(repayDateCollection[1])}`}月${repayDateCollection[2]}日前`, contentColor: 'red' },
              { contentText: '完成还款，享', contentColor: '' },
              { contentText: `${expectWaiveAmt}元`, contentColor: 'red' },
              { contentText: '息费减免，多还多减', contentColor: '' },
            ];
          } else {
            // 当期结清
            const nextRepayDateCollection = Util.getDateCollection(nextRepayDate);
            repayBubbleParam.contentArr = [
              { contentText: '本期已还款，下期还款日', contentColor: '' },
              { contentText: `${Number(nextRepayDateCollection[1]) >= 10 ? Number(nextRepayDateCollection[1]) : `0${Number(nextRepayDateCollection[1])}`}月${nextRepayDateCollection[2]}日`, contentColor: 'red' },
              { contentText: '，金额', contentColor: '' },
              { contentText: `${nextTaskShouldAmt}元`, contentColor: 'red' },
            ];
          }
        }
      } else {
        // 逾期取任务失效日期
        if (repayStatus === '2') {
          repayBubbleParam.contentArr = [
            { contentText: `${Number(overDueDateCollection[1]) >= 10 ? Number(overDueDateCollection[1]) : `0${Number(overDueDateCollection[1])}`}月${overDueDateCollection[2]}日前`, contentColor: 'red' },
            { contentText: '完成还款，享', contentColor: '' },
            { contentText: `${waiveRatio}%`, contentColor: 'red' },
            { contentText: '息费减免', contentColor: 'red' },
          ];
        } else {
          repayBubbleParam.contentArr = [
            { contentText: `${Number(repayDateCollection[1]) >= 10 ? Number(repayDateCollection[1]) : `0${Number(repayDateCollection[1])}`}月${repayDateCollection[2]}日前`, contentColor: 'red' },
            { contentText: '完成还款，享', contentColor: '' },
            { contentText: `${waiveRatio}%`, contentColor: 'red' },
            { contentText: '息费减免', contentColor: 'red' },
          ];
        }
      }
    } else {
      if (lastCntFlag !== 'Y') {
        // 逾期
        if (repayStatus === '2') {
          repayBubbleParam.contentArr = [
            { contentText: '协商还已超期，', contentColor: '' },
            { contentText: `${Number(overDueDateCollection[1]) >= 10 ? Number(overDueDateCollection[1]) : `0${Number(overDueDateCollection[1])}`}月${overDueDateCollection[2]}日`, contentColor: 'red' },
            { contentText: '前还款，可继续享受', contentColor: '' },
            { contentText: '缓催', contentColor: 'red' },
            { contentText: '福利', contentColor: '' },
          ];
        } else {
          // 当期未还或还部分
          if (taskShouldAmt && Number(taskShouldAmt) > 0) {
            repayBubbleParam.contentArr = [
              { contentText: '请在', contentColor: '' },
              { contentText: `${Number(repayDateCollection[1]) >= 10 ? Number(repayDateCollection[1]) : `0${Number(repayDateCollection[1])}`}月${repayDateCollection[2]}日前`, contentColor: 'red' },
              { contentText: '完成还款', contentColor: '' },
            ];
          } else {
            // 当期结清
            const nextRepayDateCollection = Util.getDateCollection(nextRepayDate);
            repayBubbleParam.contentArr = [
              { contentText: '本期已还款，下期还款日', contentColor: '' },
              { contentText: `${Number(nextRepayDateCollection[1]) >= 10 ? Number(nextRepayDateCollection[1]) : `0${Number(nextRepayDateCollection[1])}`}月${nextRepayDateCollection[2]}日`, contentColor: 'red' },
              { contentText: '，金额', contentColor: '' },
              { contentText: `${nextTaskShouldAmt}元`, contentColor: 'red' },
            ];
          }
        }
      } else {
        // 逾期取任务失效日期
        if (repayStatus === '2') {
          repayBubbleParam.contentArr = [
            { contentText: '请在', contentColor: '' },
            { contentText: `${Number(overDueDateCollection[1]) >= 10 ? Number(overDueDateCollection[1]) : `0${Number(overDueDateCollection[1])}`}月${overDueDateCollection[2]}日前`, contentColor: 'red' },
            { contentText: '完成还款，超期则无法享受', contentColor: '' },
            { contentText: '缓催', contentColor: 'red' },
          ];
        } else {
          repayBubbleParam.contentArr = [
            { contentText: '请在', contentColor: '' },
            { contentText: `${Number(repayDateCollection[1]) >= 10 ? Number(repayDateCollection[1]) : `0${Number(repayDateCollection[1])}`}月${repayDateCollection[2]}日前`, contentColor: 'red' },
            { contentText: '完成还款', contentColor: '' },
          ];
        }
      }
    }
    return repayBubbleParam;
  }

  render() {
    const {
      statusType, availLoanFlag, availLoanWaiveFlag, statusTitle, noTitleGuide, statusAmount, courtCostBalance, warningIcon, warningTextObj = [], overdueFlag, warningGuideText, showVPlusTips,
      showServiceBtn, recommendService, availRepayWaiveFlag, showRandomReduce, randomReduceText, btnText, disabledBtn, negotiateRepayTaskBill, availShowButtons, setClipboardTextCall,
    } = this.props;
    const needCenter = !availRepayWaiveFlag && showRandomReduce && (randomReduceText || '').length >= 14;
    const needWithBtn = availRepayWaiveFlag || (showRandomReduce && (randomReduceText || '').length < 14);
    const statusTypeObj = {
      loading: statusType === repayStatusType.loading,
      empty: statusType === repayStatusType.empty && Number(courtCostBalance) === 0,
      clear: statusType === repayStatusType.clear,
      detail: statusType === repayStatusType.overDue
        || statusType === repayStatusType.inSeven
        || statusType === repayStatusType.today
        || statusType === repayStatusType.inSevenMutil
        || statusType === repayStatusType.outSeven
        || statusType === repayStatusType.dueTagCust
        || (Number(courtCostBalance) > 0 && statusType !== 'consult'),
      consult: statusType === 'consult',
    };
    const {
      taskShouldAmt, billOverdueDays, nearBillsTotalAmount
    } = negotiateRepayTaskBill || {};
    const repayBubbleParam = this.getRepayBubbleParam();

    const bubbleText = Util.getRepaymentLocale(serviceBubble[(recommendService || {}).uniqueName]);
    const warningText = Util.getRepaymentLocale(...warningTextObj);
    const availRepayWaiveText = Util.getRepaymentLocale('HK03.HK03WA005');
    const showServiceBtnPoint = showServiceBtn && (recommendService || {}).uniqueName === 'RenewLoans';

    return (
      <MUView className="repay-status-card">
        {statusTypeObj.loading && (
          <MUView className="status-loading">
            <MUView><MUImage src={LoadingGif} className="repayment-sum-loading-gif" /></MUView>
          </MUView>
        )}

        {statusTypeObj.empty && (
          <MUView className="status-empty">
            <MUView className="status-empty__icon">
              <MUImage className="status-empty__icon--img" src={billImg} />
            </MUView>
            <MUView className="status-empty__text">暂无待还账单</MUView>
            <MUView className="status-empty__repay-service">
              <MUView className="status-empty__repay-service__title">
                - 借款后可享以下还款服务 -
              </MUView>
              <MUView className="status-empty__repay-service__content">
                <MUView className="service-item">
                  <MUView className="service-item__icon">
                    <MUImage className="service-item__icon--img" src={noticeImg} />
                  </MUView>
                  <MUView className="service-item__info">还款日有提醒</MUView>
                </MUView>
                <MUView className="service-item">
                  <MUView className="service-item__icon">
                    <MUImage className="service-item__icon--img" src={deductImg} />
                  </MUView>
                  <MUView className="service-item__info">到期自动扣款</MUView>
                </MUView>
                <MUView className="service-item">
                  <MUView className="service-item__icon">
                    <MUImage className="service-item__icon--img" src={prepayImg} />
                  </MUView>
                  <MUView className="service-item__info">支持提前还款</MUView>
                </MUView>
              </MUView>
            </MUView>
            {availLoanFlag ? (
              <MUView className="status__buttons status__buttons--wrap">
                <MUView className="status__buttons__main-wrap">
                  {availLoanWaiveFlag ? (
                    <MUView className="bubble-text">
                      借款有限时优惠，赶紧去看看~
                    </MUView>
                  ) : null}
                  <MUView className="bubble-text--block-wrap">
                    {availLoanWaiveFlag ? (
                      <MUView className="bubble-text--block" />
                    ) : null}
                    <MUButton
                      className="mu-button--list status__buttons__main"
                      type="primary"
                      circle
                      beaconId="ToLoan"
                      onClick={() => this.mainButtonCall('loan')}
                    >借一笔</MUButton>
                  </MUView>
                </MUView>
              </MUView>
            ) : null}
          </MUView>
        )}

        {statusTypeObj.clear && (
          <MUView className="status-clear">
            <MUIcon value="success" size={60} className="clear-icon" />
            <MUView className="clear-desc">本期已还清</MUView>
          </MUView>
        )}

        {statusTypeObj.detail && (
          <MUView className="status-detail">
            {statusTitle ? (
              <MUView
                className="status-detail__title"
                beaconId="ToNearList"
                onClick={() => this.jumpWaitPay()}
              >
                <MUView className="status-detail__title__text">{statusTitle}</MUView>
                {!noTitleGuide ? (
                  <MUView className="status-detail__title__guide">
                    <MUIcon value="arrow-right" size={14} />
                  </MUView>
                ) : null}
              </MUView>
            ) : null}
            <MUView className="status-detail__amount">{statusAmount || '0.00'}</MUView>
            {Number(courtCostBalance) > 0 ? <MUView className="status-detail__court-cost">（含司法处置费{courtCostBalance}元）</MUView> : null}
            {(warningIcon && warningText) ? (
              <MUView className="status-detail__supply-info">
                <MUView className="supply-info__left">
                  <MUView className="supply-info__left__icon"><MUImage src={warningIcon} /></MUView>
                  <MUView
                    className={`supply-info__left__text${((statusType === repayStatusType.dueTagCust) || overdueFlag) ? ' supply-info__left__text--over-due' : ''}`}
                  ><WaRichtext beaconId="StatusBubbleRichtext" content={warningText} /></MUView>
                </MUView>
                <MUView
                  className="supply-info__right"
                  beaconId={warningGuideText === '去开通' ? 'OpenBankDeduct' : 'OverDueEffect'}
                  onClick={() => this.warningGuideCall()}
                >
                  <MUView
                    className={`supply-info__right__text${((statusType === repayStatusType.dueTagCust) || overdueFlag) ? ' supply-info__right__text--over-due' : ''}`}
                    >{warningGuideText}</MUView>
                  <MUView className="supply-info__right__icon">
                    <MUIcon value="arrow-right" size={13} color={((statusType === repayStatusType.dueTagCust) || overdueFlag) ? '#CC1F15' : '#3477FF'} />
                  </MUView>
                </MUView>
              </MUView>
            ) : null}
            {(!warningIcon && (warningText || showVPlusTips)) ? (
              <MUView className="status-detail__tip-info">
                {warningText ? (
                  <MUView className="tip-info__main"><WaRichtext beaconId="StatusBubbleRichtext" content={warningText} /></MUView>
                ) : null}
                {showVPlusTips ? (
                  <MUView>
                    <MUView className="tip-info__sub">
                      {/* 诚信保护文案：写死10天内 */}
                      <WaRichtext beaconId="StatusBubbleRichtext" content={Util.getRepaymentLocale('HK01.HK01WA011')} />
                    </MUView>
                    <MUView className="tip-info__corner">V+会员</MUView>
                  </MUView>
                ) : null}
              </MUView>
            ) : null}
            {(availShowButtons && showServiceBtnPoint) ? (
              <MUView className="status__buttons--wrap status__buttons--wrap--special">
                <MUView className="status__buttons__point">
                  <MUView className="status__buttons__point__main-wrap">
                    {bubbleText ? (
                      <MUView className="bubble-text">
                        <WaRichtext beaconId="StatusBubbleRichtext" content={bubbleText} />
                      </MUView>
                    ) : null}
                    <MUView className="bubble-text--block-wrap">
                      {bubbleText ? (
                        <MUView className="bubble-text--block" />
                      ) : null}
                      <MUButton
                        className={`mu-button--list status__buttons__point__main${overdueFlag ? ' status__buttons__point__main--special' : ''}${statusType === repayStatusType.outSeven ? ' status__buttons__point__main--scale' : ''}`}
                        type="primary"
                        circle
                        beaconId="ToService"
                        onClick={() => this.subButtonCall()}
                      >{(recommendService || {}).title}</MUButton>
                      <MUView className="point-gif"><MUImage src={FingerGif} /></MUView>
                    </MUView>
                  </MUView>
                  {showServiceBtn ? (
                    <MUView className="status__buttons__point__sub-wrap">
                      <MUView className="bubble-text--block-wrap">
                        <MUButton
                          className={`mu-button--list--secondary status__buttons__point__sub${overdueFlag ? ' status__buttons__point__sub--special' : ''}${statusType === repayStatusType.outSeven ? ' status__buttons__point__sub--big' : ''}${disabledBtn ? ' status__buttons__point__sub--disabledBtn' : ''}`}
                          circle
                          disabled={disabledBtn}
                          beaconId="ToNearPay"
                          onClick={() => this.mainButtonCall('repay')}
                        >{btnText}</MUButton>
                      </MUView>
                    </MUView>
                  ) : null}
                </MUView>
              </MUView>
            ) : null}
            {(availShowButtons && (!showServiceBtnPoint)) ? (
              <MUView className={`status__buttons--wrap${(availRepayWaiveFlag || showRandomReduce || showServiceBtn) ? ' status__buttons--wrap--special' : ''}`}>
                {needCenter ? (
                  <MUView
                    className="bubble-text"
                    beaconId="SetClipboardText"
                    onClick={() => {
                      if (!availRepayWaiveFlag && showRandomReduce && randomReduceText) {
                        if (setClipboardTextCall && typeof setClipboardTextCall === 'function') setClipboardTextCall();
                      }
                    }}
                  >
                    {(randomReduceText || '').length >= 30 ? `${(randomReduceText || '').substring(0, 27)}...` : randomReduceText}
                  </MUView>
                ) : null}
                <MUView className="status__buttons">
                  <MUView className="status__buttons__main-wrap">
                    {needWithBtn ? (
                      <MUView
                        className="bubble-text"
                        beaconId="SetClipboardText"
                        onClick={() => {
                          if (!availRepayWaiveFlag && showRandomReduce && randomReduceText) {
                            if (setClipboardTextCall && typeof setClipboardTextCall === 'function') setClipboardTextCall();
                          }
                        }}
                      >
                        {availRepayWaiveFlag ? <WaRichtext beaconId="StatusBubbleRichtext" content={availRepayWaiveText} /> : randomReduceText}
                      </MUView>
                    ) : null}
                    <MUView className="bubble-text--block-wrap">
                      {(availRepayWaiveFlag || showRandomReduce) ? (
                        <MUView className="bubble-text--block" />
                      ) : null}
                      <MUButton
                        className={`mu-button--list status__buttons__main${overdueFlag ? ' status__buttons__main--special' : ''}`}
                        type="primary"
                        circle
                        disabled={disabledBtn}
                        beaconId="ToNearPay"
                        onClick={() => this.mainButtonCall('repay')}
                      >{btnText}</MUButton>
                    </MUView>
                  </MUView>
                  {showServiceBtn ? (
                    <MUView className="status__buttons__sub-wrap">
                      {!(availRepayWaiveFlag || showRandomReduce) && bubbleText ? (
                        <MUView className="bubble-text">
                          <WaRichtext beaconId="StatusBubbleRichtext" content={bubbleText} />
                        </MUView>
                      ) : null}
                      <MUView className="bubble-text--block-wrap">
                        {!(availRepayWaiveFlag || showRandomReduce) && bubbleText ? (
                          <MUView className="bubble-text--block" />
                        ) : null}
                        <MUButton
                          className={`mu-button--list--secondary status__buttons__sub${overdueFlag ? ' status__buttons__sub--special' : ''}`}
                          circle
                          beaconId="ToService"
                          onClick={() => this.subButtonCall()}
                        >{(recommendService || {}).title}</MUButton>
                      </MUView>
                    </MUView>
                  ) : null}
                </MUView>
              </MUView>
            ) : null}
          </MUView>
        )}

        {statusTypeObj.consult && (
          <MUView className="status-detail">
            <MUView className="status-detail__title">
              <MUView className="status-detail__title__text">月最低还款金额(元)</MUView>
            </MUView>
            <MUView className="status-detail__amount">{taskShouldAmt}</MUView>
            {(Number(billOverdueDays || 0) > 0) ? (
              <MUView className="status-detail__tip-info">
                <MUView className="tip-info__main">
                  <MURichText
                    nodes={`您已<span style="color: #CC1F15;">逾期${Number(billOverdueDays || 0)}天</span>，待还金额：${nearBillsTotalAmount}元`}
                    />
                </MUView>
              </MUView>
            ) : null}
            <MUView className="status__buttons status__buttons--wrap status__buttons--wrap--special">
              <MUView className="status__buttons__main-wrap">
                {repayBubbleParam && JSON.stringify(repayBubbleParam) !== '{}' ? (
                  <MUView className="status-consult-bubble">
                    <RepayBubble
                      repayBubbleParam={repayBubbleParam}
                  />
                  </MUView>
                ) : null}
                <MUButton
                  className={`mu-button--list status__buttons__main${overdueFlag ? ' status__buttons__main--special' : ''}`}
                  type="primary"
                  circle
                  beaconId="ToNearPay"
                  beaconContent={{ cus: { repayMode: 'consultRepay' } }}
                  onClick={() => this.mainButtonCall('consult')}
                >去还款</MUButton>
              </MUView>
            </MUView>
          </MUView>
        )}
      </MUView>
    );
  }
}
