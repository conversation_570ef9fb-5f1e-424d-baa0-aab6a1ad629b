.repay-status-card {
  padding-bottom: 60px;
  background: #FFFFFF;
  text-align: center;
  position: relative;
  overflow: hidden;

  .status-loading {
    margin-top: 200px;
  }

  .status-empty {
    margin-top: 40px;
    &__icon {
      width: 104px;
      height: 120px;
      margin: 0 auto;
      font-size: 0;
      &--img {
        width: 100%;
        height: 100%;
      }
    }
    &__text {
      margin: 40px 0 48px;
      font-size: 40px;
      line-height: 60px;
      color: #333333;
      font-weight: 600;
    }
    &__repay-service {
      margin: 0 40px;
      padding: 50px 0 48px;
      background: #F3F3F380;
      border-radius: 16px;
      text-align: center;
      &__title {
        font-size: 28px;
        line-height: 42px;
        color: #333;
      }
      &__content {
        margin-top: 40px;
        display: flex;
        justify-content: space-around;
        .service-item {
          flex: 1;
          &__icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            overflow: hidden;
            &--img {
              width: 100%;
              height: 100%;
            }
          }
          &__info {
            font-size: 24px;
            line-height: 36px;
            color: #808080;
          }
        }
      }
    }
  }

  .status-clear {
    .clear-icon {
      margin: 80px auto 50px;
    }

    .clear-desc {
      margin-bottom: 26px;
      font-size: 36px;
      line-height: 54px;
      font-weight: 600;
      color: #3D3D3D;
    }
  }

  .status-detail {
    &__title {
      margin-top: 58px;
      display: flex;
      justify-content: center;
      align-items: center;
      &__text {
        font-size: 32px;
        line-height: 32px;
        color: #333;
      }
      &__guide {
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        font-size: 0;
      }
    }

    &__amount {
      margin-top: 26px;
      text-align: center;
      line-height: 100px;
      color: #333333;
      font-size: 100px;
      font-weight: bold;
      font-family: "DIN Alternate";
    }
    .status-amount--special {
      color: #CC1F15;
    }

    &__court-cost {
      margin-top: 20px;
      font-size: 26px;
      line-height: 26px;
      color: #808080;
    }

    &__supply-info {
      margin: 24px 40px 0;
      padding: 16px 30px 16px 20px;
      border-radius: 16px;
      background: #F3F3F380;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .supply-info__left {
        display: flex;
        align-items: center;
        &__icon {
          width: 48px;
          height: 48px;
          font-size: 0;
          .taro-img, image {
            width: 100%;
            height: 100%;
          }
        }
        &__text {
          margin-left: 16px;
          font-size: 26px;
          line-height: 40px;
          color: #808080;
          &--over-due {
            color: #CC1F15;

            .i18n-color-red {
              color: #CC1F15;
            }
          }
        }
      }
      .supply-info__right {
        display: flex;
        align-items: center;
        &__text {
          font-size: 26px;
          line-height: 40px;
          color: #3477FF;
          &--over-due {
            color: #CC1F15;
          }
        }
        &__icon {
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          font-size: 0;
        }
      }
    }
    
    &__tip-info {
      margin: 24px 40px 0;
      padding: 20px 30px;
      border-radius: 16px;
      background: #F3F3F380;
      font-size: 26px;
      line-height: 40px;
      color: #808080;
      text-align: center;
      position: relative;

      .i18n-color-red {
        color: #CC1F15;
      }
      
      .tip-info__corner {
        height: 40px;
        width: 86px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        line-height: 30px;
        font-weight: 600;
        color: #fff;
        border-radius: 20px 0 20px 0;
        background: linear-gradient(115deg, #E2C8BF 0%, #C19788 49%);
        position: absolute;
        left: 0;
        top: 0;
        transform: translateY(-50%);
      }
    }

    .status-desc-block {
      display: flex;
      justify-content: center;
      margin-top: 30px;

      .status-desc {
        font-size: 32px;
        line-height: 32px;
        color: #808080;
      }

      .status-go-to {
        width: 32px;
        height: 32px;
        margin-left: 10px;
        display: flex;

        .img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .status-warning-text {
      padding: 0 30px;
      margin-top: 30px;
      font-size: 32px;
      line-height: 40px;
      color: #CC1F15;
    }
    .status-warning-text--special {
      color: #A6A6A6;
    }

    .status-tips-text {
      margin-top: 30px;
      font-size: 32px;
      line-height: 32px;
      color: #808080;
      display: flex;
      justify-content: center;
      align-items: center;

      .deduct-tips-icon {
        margin-left: 5px;
      }
    }

    .status-warning-text--suit, .status-tips-text--suit {
      margin-top: 40px;
    }

    .status-btns--wrap {
      margin-top: 40px;
    }

    .status-random-reduce {
      margin: -20px 0 -10px;
      &__text {
        font-size: 20px;
        line-height: 20px;
        padding: 5px 20px;
        font-weight: 400;
        color: #FF8844;
        border: 1PX solid #FF8844;
        border-radius: 34px;
        position: relative;
      }
      &__text::after {
        content: "";
        position: absolute;
        width: 12px;
        height: 12px;
        border-right: 1PX solid #FF8844;
        border-bottom: 1PX solid #FF8844;
        border-left: 1PX solid #fff;
        border-top: 1PX solid #fff;
        left: 50%;
        margin-left: -8px;
        top: 27px;
        transform: rotate(45deg);
        background-color: #fff;
        border-bottom-right-radius: 1PX;
        overflow: hidden;
      }
    }

    .status-random-reduce-mini {
      margin: -10px 0 0;
      .status-random-reduce-mini::after {
        content: "";
        position: absolute;
        width: 12px;
        height: 12px;
        border-right: 1PX solid #FF8844;
        border-bottom: 1PX solid #FF8844;
        border-left: 1PX solid #fff;
        border-top: 1PX solid #fff;
        left: 50%;
        margin-left: -8px;
        top: 29px;
        transform: rotate(45deg);
        background-color: #fff;
        border-bottom-right-radius: 1PX;
        overflow: hidden;
      }
    }

    .status-repay-bubble {
      margin: -20px 0 -10px;
    }

    .status-btns {
      display: flex;
      justify-content: space-around;
    }

    .status-btn {
      width: 360px;
      font-size: 36px;
      font-weight: 600;
    }
    .status-btn--suit {
      margin-top: 20px;
      border-radius: 88px;
    }
    .status-btn--special {
      background-color: #CC1F15 !important;
      border-color: #CC1F15 !important;
    }

    &-overPayAmtBorder {
      width: 100%;
      margin-top: 15px;
      // padding: 19.4px 0 40.1px 0;
    }

    .alipayMargin {
      margin-top: 20px;
    }

    &-overPayAmt {
      display: inline;
      font-size: 24px;
      color: #808080;
      margin: 0 auto;
      // display: flex;
      // justify-content: center;
      // text-align: center;
      padding: 8px 12px;
      // line-height: 42px;
      // width: 458px;
      // height: 69.5px;
      background: #F4F4F4;
      border: 1px solid #E5E5E5;
      position: relative;
      border-radius: 8px;

      &-num {
        color: #FF8844;
      }
    }

    &-overPayAmt:after,
    &-overPayAmt:before {
      content: "";
      position: absolute;
      width: 0;
      height: 0;
      border: 14px solid transparent;
      border-bottom-color: #E5E5E5;
      left: 50%;
      margin-left: -14px;
      top: -28px;
    }

    &-overPayAmt:after {
      border-bottom-color: #F4F4F4;
      top: -25px;
    }

    &-vPlus {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 42px auto -22px;
      font-family: PingFangSC-Regular, sans-serif;
      font-weight: 400;
      font-size: 26px;
      line-height: 37px;
      color: #808080;
      &-icon {
        margin-right: 4px;
        width: 84px;
        height: 33px;
      }
    }
  }

  .status-showServiceBtn {
    width: 360px;
    font-size: 36px;
    position: relative;

    &-content {
      display: flex;
      justify-content: center;
      position: relative;
      // height: 100%;

      &-text {
        font-size: 36px;
        color: #333333;
        text-align: center;
        line-height: 64px;
        position: relative;
      }

      &-image {
        width: 56px;
        height: 32px;
        margin-left: 10px;
        position: absolute;
        right: -56px;
        display: flex;

        .img {
          line-height: 64px;
          position: absolute;
          width: 56px;
          height: 32px;
          overflow: inheri;
        }
      }
    }

  }

  .status-showStagesBtn {
    width: 360px;
    font-size: 36px;
    // margin-right: 30px;
  }

  .repayment-sum-loading-gif {
    width: 200px;
    height: 40px;

    img {
      height: 100%;
      width: 100%;
    }
  }

  .status__buttons--wrap {
    margin: 48px 40px 0 40px;
    &--special {
      margin-top: 36px 40px 0 40px;
    }
    .bubble-text {
      margin: 0 auto 20px;
      padding: 12px 20px;
      width: max-content;
      font-size: 24px;
      line-height: 24px;
      color: #FF8844;
      background: #FFF3EC;
      border-radius: 8px;
      &--main {
        margin: 0 0 20px auto;
      }
    }
  }

  .status__buttons {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
    align-items: flex-end;
    &__main-wrap, .status__buttons__sub-wrap {
      font-size: 0;
      .bubble-text {
        margin: 0 auto 20px;
        padding: 12px 20px;
        width: max-content;
        font-size: 24px;
        line-height: 24px;
        color: #FF8844;
        background: #FFF3EC;
        border-radius: 8px;
        &--main {
          margin: 0 0 20px auto;
        }
      }
      .bubble-text--block-wrap {
        position: relative;
        .bubble-text--block {
          width: 14px;
          height: 14px;
          background: #FFF3EC;
          border-radius: 0 0 4px 0;
          position: absolute;
          left: calc(50% - 7px);
          top: -27px;
          transform: rotate(45deg);
          &--special {
            top: 65px;
          }
        }
      }
    }
    &__sub-wrap {
      margin-right: 30px;
      font-size: 0;
    }
    &__main {
      width: 360px;
      height: 100px;
      font-size: 36px;
      line-height: 100px;
      font-weight: 600;
      border-radius: 50px;
      &--special {
        background: #CC1F15 !important;
        border-color: #CC1F15;
      }
    }
    &__sub {
      width: 280px;
      height: 100px;
      font-size: 36px;
      line-height: 100px;
      font-weight: 600;
      border-radius: 50px;
      &--special {
        border-color: #CC1F15;
        .at-button__text {
          color: #CC1F15;
        }
      }
    }
    .status-consult-bubble {
      margin-bottom: 10px;
    }
  }

  .status__buttons__point {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
    align-items: flex-end;
    &__main-wrap, .status__buttons__point__sub-wrap {
      font-size: 0;
      .bubble-text {
        margin: 0 auto 20px;
        padding: 12px 20px;
        width: max-content;
        font-size: 24px;
        line-height: 24px;
        color: #FF8844;
        background: #FFF3EC;
        border-radius: 8px;
        &--main {
          margin: 0 0 20px auto;
        }
      }
      .bubble-text--block-wrap {
        position: relative;
        .bubble-text--block {
          width: 14px;
          height: 14px;
          background: #FFF3EC;
          border-radius: 0 0 4px 0;
          position: absolute;
          left: calc(50% - 7px);
          top: -27px;
          transform: rotate(45deg);
          &--special {
            top: 65px;
          }
        }
        .point-gif {
          width: 110px;
          height: 110px;
          font-size: 0;
          position: absolute;
          right: -22px;
          top: 45px;
          .taro-img, image {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    &__sub-wrap {
      margin-right: 30px;
      font-size: 0;
    }
    &__main {
      width: 320px;
      height: 100px;
      font-size: 36px;
      line-height: 100px;
      font-weight: 600;
      border-radius: 50px;
      &--scale {
        width: 280px;
      }
      &--special {
        background: #CC1F15 !important;
        border-color: #CC1F15;
      }
    }
    &__sub {
      width: 320px;
      height: 100px;
      font-size: 36px;
      line-height: 100px;
      font-weight: 600;
      border-radius: 50px;
      &--big {
        width: 360px;
      }
      &--special {
        border-color: #CC1F15;
        .at-button__text {
          color: #CC1F15;
        }
      }
      &--disabledBtn {
        border-color: #3477FF;
        opacity: 0.3;
        .at-button__text {
          color: #3477FF;
        }
      }
    }
  }
}
