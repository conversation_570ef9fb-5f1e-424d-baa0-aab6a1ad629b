import { Component } from '@tarojs/taro';
import { MUView, MURadio } from '@mu/zui';
import PropTypes from 'prop-types';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { AgreementDrawer } from '@mu/agreement';
import Util from '@utils/maxin-util';
import Dispatch from '@api/actions';
import { getStore } from '@api/store';
import { getLoginInfo } from '@mu/business-basic';
import './index.scss';

export default class Protocol extends Component {
  static propTypes = {
    contractApplyList: PropTypes.arrayOf([PropTypes.object]), // 2.0协议信息列表
    contractInfoList: PropTypes.arrayOf([PropTypes.object]), // 3.0协议信息列表
    billExtendInfo: PropTypes.oneOfType([PropTypes.object, PropTypes.array]), // 协议内容额外需填充的参数
    className: PropTypes.string, // 样式
    onChecked: PropTypes.func, // 给组件外部传递是否选中协议的状态
    trackPrefix: PropTypes.string, // 埋点
  }

  static defaultProps = {
    contractApplyList: [],
    contractInfoList: [],
    billExtendInfo: null,
    className: '',
    onChecked: () => { },
    trackPrefix: '',
  };

  constructor(props) {
    super(props);
    this.state = {
      channelAgreementPlanCfgDtoList: [{}], // 合同数组
      isCheckedContract: '', // 表示合同组件是否同意勾选合同, 值为hasCheckedContract标识选中
      alreadyForceFlag: false, // 表示合同是否已经强读过
    };
    this.mobile = ''; // 掩码手机号
    this.custName = ''; // 掩码客户姓名
    this.idNo = ''; // 掩码身份证号，待中台添加
  }

  async componentDidMount() {
    // 获取用户信息
    const result = await getLoginInfo();
    const { mobile, custName, idNo } = result || {};
    this.mobile = mobile;
    this.custName = custName;
    this.idNo = idNo;
    const { contractApplyList = [], contractInfoList, billExtendInfo } = this.props;
    if (contractApplyList && contractApplyList.length > 0) {
      this.initProtocalData(contractApplyList, billExtendInfo);
    } else {
      this.initProtocalNewData(contractInfoList, billExtendInfo);
    }
  }

  componentWillReceiveProps(nextProps) {
    const { contractApplyList = [], contractInfoList, billExtendInfo } = nextProps;
    const { billExtendInfo: billExtendInfoBefore } = this.props;
    if (JSON.stringify(billExtendInfoBefore) !== JSON.stringify(billExtendInfo)) {
      if (contractApplyList && contractApplyList.length > 0) {
        this.initProtocalData(contractApplyList, billExtendInfo);
      } else {
        this.initProtocalNewData(contractInfoList, billExtendInfo);
      }
    }
  }

  // 初始化合同数据
  async initProtocalData(contractApplyList, billExtendInfo) {
    const { channelAgreementPlanCfgDtoList } = this.state;
    if (contractApplyList && contractApplyList.length > 0) {
      channelAgreementPlanCfgDtoList.forEach((item) => {
        const contractList = [];
        if (contractApplyList && contractApplyList.length > 0) {
          contractApplyList.forEach((contractItem) => {
            const { contractType, contractEdition, contractVersion } = contractItem || {};
            const formatItem = {
              contractType,
              contractEdition,
              contractVersion,
              contractCategory: contractType
            };

            if (contractType === 'POSTLOAN_CHANGE') {
              const contractParam = this.getPostloanChangeParam(contractItem);
              contractList.push({
                title: '贷后服务变更协议',
                params: {
                  ...contractParam,
                  ...formatItem,
                }
              });
            }

            if (contractType === 'DELAY_REPAY') {
              const contractParam = this.getDelayRepayParam(contractItem, billExtendInfo);
              contractList.push({
                title: '延期还款服务单',
                params: {
                  ...contractParam,
                  ...formatItem,
                }
              });
            }

            if (contractType === 'REPAY_DATE_CHANGE') {
              const contractParam = this.getChangeRepayDayParam(contractItem, billExtendInfo);
              contractList.push({
                title: '还款日变更服务单',
                params: {
                  ...contractParam,
                  ...formatItem,
                }
              });
            }

            if (contractType === 'LOAN_PRICE_CHANGE') {
              const contractParam = this.getLoanPriceChangeParam(contractItem, billExtendInfo);
              contractList.push({
                title: '借款价格变更服务单',
                params: {
                  ...contractParam,
                  ...formatItem,
                }
              });
            }

            if (contractType === 'NEGOTIATE_REPAY') {
              const contractParam = this.getNegotiateRepayParam(contractItem, billExtendInfo);
              contractList.push({
                title: '月最低还款承诺书',
                params: {
                  ...contractParam,
                  ...formatItem,
                }
              });
            }

            if (contractType === 'POST_INFO_AUTH') {
              const contractParam = this.getInfoAuthParam(contractItem);
              contractList.push({
                title: '资料授权书',
                params: {
                  ...contractParam,
                  ...formatItem,
                }
              });
            }

            if (contractType === 'LOAN_ELE_CHANGE') {
              const contractParam = this.getLoanChangeParam(contractItem, billExtendInfo);
              contractList.push({
                title: '借款要素变更服务单',
                params: {
                  ...contractParam,
                  ...formatItem,
                }
              });
            }

            if (contractType === 'BILL_REPAY') {
              const contractParam = this.getbillStagingParam(contractItem, billExtendInfo);
              contractList.push({
                title: '账单分期还款服务单',
                params: {
                  ...contractParam,
                  ...formatItem,
                }
              });
            }
          });
        }
        item.list = contractList;
        item.contracts = contractApplyList;
        item.contractText = contractApplyList.map((contract) => {
          const { contractType } = contract || {};
          if (contractType === 'POSTLOAN_CHANGE') {
            return '贷后服务变更协议';
          } else if (contractType === 'DELAY_REPAY') {
            return '延期还款服务单';
          } else if (contractType === 'REPAY_DATE_CHANGE') {
            return '还款日变更服务单';
          } else if (contractType === 'LOAN_PRICE_CHANGE') {
            return '借款价格变更服务单';
          } else if (contractType === 'NEGOTIATE_REPAY') {
            return '月最低还款承诺书';
          } else if (contractType === 'POST_INFO_AUTH') {
            return '资料授权书';
          } else if (contractType === 'LOAN_ELE_CHANGE') {
            return '借款要素变更服务单';
          } else if (contractType === 'BILL_REPAY') {
            return '账单分期还款服务单';
          }
        }).join('、');
      });
    }
    this.setState({
      channelAgreementPlanCfgDtoList
    });
  }

  // 初始化合同3.0数据
  async initProtocalNewData(contractInfoList, billExtendInfo) {
    const { channelAgreementPlanCfgDtoList } = this.state;

    if (contractInfoList && contractInfoList.length > 0) {
      for (const item of channelAgreementPlanCfgDtoList) {
        const contractList = [];
        if (contractInfoList && contractInfoList.length > 0) {
          for (const contractItem of contractInfoList) {
            const { contractCode, contractName } = contractItem || {};

            // 贷后服务变更协议
            if (contractCode === 'DHBGXY') {
              const contractParam = this.getPostloanChangeParam(contractItem, '3.0');
              const htmlFile = await this.htmlFilePrivew(contractCode, contractParam);
              contractList.push({
                title: contractName,
                htmlFile,
              });
            }
            // 延期还款服务单
            if (contractCode === 'DHYWBG_YQHK') {
              const contractParam = this.getDelayRepayParam(contractItem, billExtendInfo, '3.0');
              const htmlFile = await this.htmlFilePrivew(contractCode, contractParam);
              contractList.push({
                title: contractName,
                htmlFile
              });
            }

            // 还款日变更服务单
            if (contractCode === 'DHYWBG_BGHKR') {
              const contractParam = this.getChangeRepayDayParam(contractItem, billExtendInfo, '3.0');
              const htmlFile = await this.htmlFilePrivew(contractCode, contractParam);
              contractList.push({
                title: contractName,
                htmlFile
              });
            }

            // 借款价格变更服务单
            if (contractCode === 'DHYWBG_JKJGBG') {
              const contractParam = this.getLoanPriceChangeParam(contractItem, billExtendInfo, '3.0');
              const htmlFile = await this.htmlFilePrivew(contractCode, contractParam);
              contractList.push({
                title: contractName,
                htmlFile
              });
            }
            // 新版还款承诺函
            if (contractCode === 'KHCNS_HKCN') {
              const contractParam = this.getRepayPromiseParam(billExtendInfo);
              const htmlFile = await this.htmlFilePrivew(contractCode, contractParam);
              contractList.push({
                title: contractName,
                htmlFile
              });
            }

            // 月最低还款承诺书
            if (contractCode === 'DHYWBG_XSMYZDHK') {
              const contractParam = this.getNegotiateRepayParam(contractItem, billExtendInfo, '3.0');
              const htmlFile = await this.htmlFilePrivew(contractCode, contractParam);
              contractList.push({
                title: contractName,
                htmlFile
              });
            }

            // '资料授权书' TODO: 未联调
            if (contractCode === 'GRXXSQ_DHZLSQ') {
              const contractParam = this.getInfoAuthParam(contractItem, billExtendInfo, '3.0');
              const htmlFile = await this.htmlFilePrivew(contractCode, contractParam);
              contractList.push({
                title: contractName,
                htmlFile
              });
            }

            // '借款要素变更服务单'
            if (contractCode === 'DHYWBG_DKYSBG') {
              const contractParam = this.getLoanChangeParam(contractItem, billExtendInfo, '3.0');
              const htmlFile = await this.htmlFilePrivew(contractCode, contractParam);
              contractList.push({
                title: contractName,
                htmlFile
              });
            }

            // 账单分期还款服务单
            if (contractCode === 'DHYWBG_ZDFQ') {
              const contractParam = this.getbillStagingParam(contractItem, billExtendInfo, '3.0');
              const htmlFile = await this.htmlFilePrivew(contractCode, contractParam);
              contractList.push({
                title: contractName,
                htmlFile
              });
            }

            // 账单分期还款服务单
            if (contractCode === 'DHYWBG_FQHB') {
              const contractParam = this.getRenewLoansParam(contractItem, billExtendInfo);
              const htmlFile = await this.htmlFilePrivew(contractCode, contractParam);
              contractList.push({
                title: contractName,
                htmlFile
              });
            }
          }
        }
        item.list = contractList;
        item.contracts = contractInfoList;
        item.contractText = contractInfoList.map((contract) => {
          const { contractName } = contract || {};
          return contractName;
        }).join('、');
      }
    }
    this.setState({
      channelAgreementPlanCfgDtoList
    });
  }

  // 获取还款承诺的参数
  getRepayPromiseParam = (billExtendInfo = {}) => {
      
    const contractData = {
      name: this.custName || '',
    }
    contractData.baseContractInfo = {
      baseDate: billExtendInfo.baseDate || '',
    };

    contractData.loanTransInfo = {
      reason: billExtendInfo.reason || '',
    };
    return contractData;
  }

  // 请求预览html
  htmlFilePrivew = (contractCode, contractPreviewData) => {
    return Dispatch.repayment.queryContractInfo({
      scene: 'PREVIEW',
      interfaceVersion: '3.0',
      contractCode,
      contractPreviewData,
    }).then(res => {
      const { data } = res || {};
      const { contractList } = data || {};
      return contractList && contractList[0] && contractList[0].htmlFile;
    });
  };

  // 获取贷后服务协议合同需要的参数
  getPostloanChangeParam = (contractParam, newContract = '') => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const nowDate = new Date();

    const contractData = {
      name: this.custName || '',
      mobile: this.mobile || '',
      certId: this.idNo || '',
      certName: '身份证',
      yearNow: nowDate.getFullYear(),
      monthNow: nowDate.getMonth() + 1,
      dayNow: nowDate.getDate(),
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      bringParam: 1
    };
    if (newContract === '3.0') {
      const { dateFormat } = Util.getCurrentDateTimeInFormat();
      contractData.baseContractInfo = {
        signDate: dateFormat,
        certType: '身份证'
      };
    }


    return contractData;
  }

  // 获取延期还款服务单合同需要的参数，
  getDelayRepayParam = (contractParam, billExtendInfo = {}, newContract = '') => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const nowDate = new Date();
    const { dateFormat } = Util.getCurrentDateTimeInFormat();

    const contractData = {
      name: this.custName || '',
      mobile: this.mobile || '',
      certName: '身份证',
      certId: this.idNo || '',
      yearNow: nowDate.getFullYear(),
      monthNow: nowDate.getMonth() + 1,
      dayNow: nowDate.getDate(),
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      ...billExtendInfo, // 借据变更信息
      bringParam: 1,
    };
    if (newContract === '3.0') {
      contractData.baseContractInfo = {
        signDate: dateFormat,
        certType: '身份证'
      };
      contractData.markedPriceInfo = {
        orderNos: billExtendInfo && billExtendInfo.loanNo // 借据编号列表
      };
    }
    return contractData;
  }

  // 获取还款日变更服务单合同需要的参数，
  getChangeRepayDayParam = (contractParam, billExtendInfo = {}, newContract = '') => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const nowDate = new Date();

    const elements = billExtendInfo && billExtendInfo.billList && billExtendInfo.billList.map((bill) => ({
      elements_loanNo: bill.orderNo,
      elements_dueDate: Util.dateFormatter(String(bill.maturityDate || '') || bill.nextDueDate) // 借款到期日
    }));

    const elementsThree = billExtendInfo && billExtendInfo.billList && billExtendInfo.billList.map((bill) => ({
      orderNo: bill.orderNo,
      changeDueDate: String(bill.maturityDate || '') || bill.nextDueDate // 借款到期日 YYMMDD
    }));
    const contractData = {
      name: this.custName || '',
      mobile: this.mobile || '',
      certName: '身份证',
      certId: this.idNo || '',
      dueDate: billExtendInfo && billExtendInfo.payDayNumber,
      yearNow: nowDate.getFullYear(),
      monthNow: nowDate.getMonth() + 1,
      dayNow: nowDate.getDate(),
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      elements, // 借据变更信息
      bringParam: 1
    };

    if (newContract === '3.0') {
      const { dateFormat } = Util.getCurrentDateTimeInFormat();
      contractData.baseContractInfo = {
        signDate: dateFormat,
        certType: '身份证'
      };
      contractData.markedPriceInfo = {
        repayDay: billExtendInfo && billExtendInfo.payDayNumber && billExtendInfo.payDayNumber.toString(),
        elements: elementsThree
      };
    }

    return contractData;
  }

  // 获取借款价格变更服务单合同需要的参数
  getLoanPriceChangeParam = (contractParam, billPriceCutResult = {}, newContract = '') => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const nowDate = new Date();
    const {
      dayInterest,
      penaltyRate,
      lprFloatRate,
      annualInteRate,
      adjustOrderDetails = [],
    } = billPriceCutResult;

    const order = adjustOrderDetails.filter((i) => i.adjustResult === 'Y').map((o) => ({
      order_no: o.orderNo,
      order_date: o.loanDate,
      order_amount: o.loanAmt
    }));

    const orderThree = adjustOrderDetails.filter(i => i.adjustResult === 'Y').map(o => ({
      loanNo: o.orderNo,
      loanDate: o.loanDate,
      amount: o.loanAmt
    }));

    const contractData = {
      name: this.custName || '',
      mobile: this.mobile || '',
      certName: '身份证',
      certId: this.idNo || '',
      dueDate: '',
      yearNow: nowDate.getFullYear(),
      monthNow: nowDate.getMonth() + 1,
      dayNow: nowDate.getDate(),
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      rate: Number(dayInterest), // 利率 rate
      yearRate: Number(annualInteRate), // 年化利率 annualInteRate
      totalDayInterest: '365', // 年计息天数 totalDayInterest
      lprRate: Number(lprFloatRate), // LPR加点 lprRate
      lateCharge: Number(penaltyRate), // 罚息利率  lateCharge
      order, // 借款价格服务变更信息
      bringParam: 1
    };

    if (newContract === '3.0') {
      const { dateFormat } = Util.getCurrentDateTimeInFormat();
      contractData.baseContractInfo = {
        signDate: dateFormat,
        certType: '身份证'
      };
      contractData.markedPriceInfo = {
        afterRate: `${Number(dayInterest)}`, // 折后日利率
        afterYearRate: `${Number(annualInteRate)}`, // 折后年化利率
        totalDayInterest: '365', // 利率计算天数
        lprRateInterest: `${Number(lprFloatRate)}`, // LPR加点
        loanList: orderThree
      };
    }

    return contractData;
  }

  // 获取月最低还款承诺书合同需要的参数
  getNegotiateRepayParam = (contractParam, selectNegotiateRepay, newContract = '') => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const { taskTotalAmt, surplusPayPrincipalAmt, surplusPayInteFeeAmt, surplusPayFineAmt, taskThresholdAmt, lastRepayDate } = selectNegotiateRepay || {};
    const endYear = lastRepayDate && lastRepayDate.substring(0, 4);
    const endMonth = lastRepayDate && lastRepayDate.substring(4, 6);
    const endDay = lastRepayDate && lastRepayDate.substring(6, 8);
    const nowDate = new Date();
    const contractData = {
      name: this.custName || '',
      mobile: this.mobile || '',
      certId: this.idNo || '',
      yearNow: nowDate.getFullYear(),
      monthNow: nowDate.getMonth() + 1,
      dayNow: nowDate.getDate(),
      totalAmt: taskTotalAmt,
      principal: surplusPayPrincipalAmt,
      inteFee: surplusPayInteFeeAmt,
      fineFee: surplusPayFineAmt,
      periodAmt: taskThresholdAmt,
      periodEndYear: endYear,
      periodEndMonth: endMonth,
      periodEndDay: endDay,
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      bringParam: 1
    };

    if (newContract === '3.0') {
      const { dateFormat } = Util.getCurrentDateTimeInFormat();
      contractData.baseContractInfo = {
        signDate: dateFormat,
        certType: '身份证'
      };
      // 贷款交易信息
      contractData.loanTransInfo = {
        residueRepayAmt: taskTotalAmt, // 剩余应还款总额
        cashAmt: surplusPayPrincipalAmt, // 借款金额
        inteFee: surplusPayInteFeeAmt, // 利息
        fineFee: surplusPayFineAmt, // 罚息
        periodAmt: taskThresholdAmt, // 每期还款金额
        periodEndDate: lastRepayDate // 借款到期日期
      };
    }

    return contractData;
  }

  // 获取贷后资料授权合同需要的参数
  getInfoAuthParam = (contractParam, newContract = '') => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const nowDate = new Date();

    const contractData = {
      name: this.custName || '',
      mobile: this.mobile || '',
      certId: this.idNo || '',
      yearNow: nowDate.getFullYear(),
      monthNow: nowDate.getMonth() + 1,
      dayNow: nowDate.getDate(),
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      bringParam: 1
    };

    if (newContract === '3.0') {
      const { dateFormat } = Util.getCurrentDateTimeInFormat();
      contractData.baseContractInfo = {
        signDate: dateFormat,
        certType: '身份证'
      };
    }

    return contractData;
  }

  // 获取借款要求变更服务单合同需要的参数，
  getLoanChangeParam = (contractParam, advancedStageInfo, newContract = '') => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const nowDate = new Date();
    const { mergeOrderFlag, orderInfoListAfterMerge, originalOrderInfoListCanExtend, orderInfoListCanExtend } = advancedStageInfo;

    let elements = [];
    let mergeOrderElements = [];
    if (newContract === '3.0') {
      if (mergeOrderFlag === 'Y') {
        mergeOrderElements = (orderInfoListAfterMerge || []).map((bill) => ({
          loanType: '合并再分期', // 借据类型
          loanNo: ((originalOrderInfoListCanExtend || []).map((item) => item.orderNo) || []).join('、'), // 原借据编号
          amount: bill.extensionPayTotalPrincipalAmt, // 再分期金额
          loanDate: bill.loanDate, // 借款日期
          loanEndDate: bill.extensionMaturityDate, // 贷款期限结束日期
          dueDate: bill.extensionMaturityDate, // 变更后的借款到期日
          period: bill.extensionInstallTotalCnt, // 再分期期数
          chargeType: '按利率计息', // 息费计收方式
          originalIrrAnnualRate: bill.extensionYearRate ? `${(parseFloat(bill.extensionYearRate) * 100).toFixed(4)}` : '', // 年利率（单利）
          actualIrrAnnualRate: bill.extensionWaivedYearRate ? `${(parseFloat(bill.extensionWaivedYearRate) * 100).toFixed(4)}` : '', // 变更后折合年利率（单利）
          penaltyRate: bill.extensionPenaltyRate ? `${(parseFloat(bill.extensionPenaltyRate) * 100).toFixed(4)}` : '', // 日罚息利率
          repayType: bill.extensionPrincipalType, // 变更后还款方式
          advanceDesc: '提前还款不收取违约金', // 提前还款违约金描述
        }));
      } else {
        elements = (orderInfoListCanExtend || []).map((bill) => ({
          loanType: '单笔再分期', // 借据类型
          loanNo: bill.orderNo, // 借据编号
          amount: bill.extensionPayTotalPrincipalAmt, // 再分期金额
          period: bill.extensionInstallTotalCnt, // 再分期期数
          loanDate: bill.loanDate, // 借款日期
          loanEndDate: bill.extensionMaturityDate, // 贷款期限结束日期
          chargeType: bill.loanType === 'F' ? '按费率计息' : '按利率计息', // 息费计收方式
          originalIrrAnnualRate: bill.extensionYearRate ? `${(parseFloat(bill.extensionYearRate) * 100).toFixed(4)}` : '', // 变更前折合年利率（单利）
          actualIrrAnnualRate: bill.extensionWaivedYearRate ? `${(parseFloat(bill.extensionWaivedYearRate) * 100).toFixed(4)}` : '', // 变更后折合年利率（单利）
          penaltyRate: bill.extensionPenaltyRate ? `${(parseFloat(bill.extensionPenaltyRate) * 100).toFixed(4)}` : '', // 日罚息利率
          repayType: bill.extensionPrincipalType, // 变更后还款方式待确认
          advanceDesc: '提前还款不收取违约金', // 提前还款违约金
        }));
      }
    } else {
      if (mergeOrderFlag === 'Y') {
        mergeOrderElements = (orderInfoListAfterMerge || []).map((bill) => ({
          oldLoanNos: ((originalOrderInfoListCanExtend || []).map((item) => item.orderNo) || []).join(), // 原借据编号
          cashAmt: bill.extensionPayTotalPrincipalAmt, // 再分期金额
          loanDate: Util.dateFormatter(bill.loanDate), // 借款日期
          dueDate: Util.dateFormatter(bill.extensionMaturityDate), // 变更后的借款到期日
          period: bill.extensionInstallTotalCnt, // 再分期期数
          chargeType: '按利率计息', // 息费计收方式
          yearRate: Util.stringToPersent(bill.extensionYearRate, 4), // 年利率（单利）
          penaltyRate: Util.stringToPersent(bill.extensionPenaltyRate, 4), // 日罚息利率
          repayType: bill.extensionPrincipalType, // 变更后还款方式待确认
          advanceMoney: '提前还款不收取违约金', // 提前还款违约金
        }));
      } else {
        elements = (orderInfoListCanExtend || []).map((bill) => ({
          elements_loanNo: bill.orderNo, // 借据编号
          elements_cashAmt: bill.extensionPayTotalPrincipalAmt, // 再分期金额
          elements_period: bill.extensionInstallTotalCnt, // 再分期期数
          elements_periodRate: bill.loanType === 'F' ? Util.stringToPersent(bill.extensionPeriodFeeRate, 4) : null, // 分期手续费期费率（利率借据无）
          elements_loanDate: Util.dateFormatter(bill.loanDate), // 借款日期
          elements_dueDate: Util.dateFormatter(bill.extensionMaturityDate), // 变更后的借款到期日
          elements_chargeType: bill.loanType === 'F' ? '按费率计息' : '按利率计息', // 息费计收方式
          elements_rate: Util.stringToPersent(bill.extensionYearRate, 4), // 变更后折合年利率（单利）
          elements_penaltyRate: Util.stringToPersent(bill.extensionPenaltyRate, 4), // 日罚息利率
          elements_repayType: bill.extensionPrincipalType, // 变更后还款方式待确认
          elements_advanceMoney: '提前还款不收取违约金', // 提前还款违约金
          elements_rateBefore: Util.stringToPersent(bill.origYearRate, 4), // 变更前折合年利率（单利）
          elements_repayTypeBefore: bill.origPrincipalType, // 变更前还款方式待后台更新字段
        }));
      }
    }


    const contractData = {
      name: this.custName || '',
      mobile: this.mobile || '',
      certName: '身份证',
      certId: this.idNo || '',
      yearNow: nowDate.getFullYear(),
      monthNow: nowDate.getMonth() + 1,
      dayNow: nowDate.getDate(),
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      elements, // 借据变更信息
      ...mergeOrderElements[0],
      serviceName: mergeOrderFlag === 'Y' ? '合并再分期' : '单笔再分期',
      bringParam: 1
    };

    if (newContract === '3.0') {
      const { dateFormat } = Util.getCurrentDateTimeInFormat();
      contractData.baseContractInfo = {
        signDate: dateFormat,
        certType: '身份证'
      };
      // 贷款交易信息
      contractData.loanTransInfo = {
        productName: mergeOrderFlag === 'Y' ? '合并再分期' : '单笔再分期', // 分期商品名
      };
      const loanList = mergeOrderFlag === 'Y' ? mergeOrderElements : elements;
      contractData.markedPriceInfo = {
        loanList
      };
    }

    return contractData;
  }

  // 获取账单分期还款服务单需要的参数
  getbillStagingParam = (contractParam, billExtendInfo, newContract = '') => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const { totalPrincipal, totalInterest, selectedCntInfo, selectedBillList, repayWayDes } = billExtendInfo || {};

    const {
      cnt, feeRate,
      repayPlanList, overDueRate,
      firstRepayDate, repayName,
      yearInterestRate, lprRate,
      prepayFeeLower, contractAdvanceData
    } = selectedCntInfo || {};
    const dateStart = new Date();
    const dateEnd = new Date((([...(repayPlanList || [])].pop() || {}).repayDate || '').replace(/(\d{4})(\d{2})(\d{2})/, '$1/$2/$3'));

    if (!cnt) return;
    const contractData = {
      name: this.custName,
      mobile: this.mobile,
      certName: '身份证',
      certId: this.idNo,
      yearNow: dateStart.getFullYear(),
      monthNow: dateStart.getMonth() + 1,
      dayNow: dateStart.getDate(),
      yearStart: dateStart.getFullYear(),
      monthStart: dateStart.getMonth() + 1,
      dayStart: dateStart.getDate(),
      yearEnd: dateEnd.getFullYear(),
      monthEnd: dateEnd.getMonth() + 1,
      dayEnd: dateEnd.getDate(),
      totalCnt: cnt,
      lprRateInterest: lprRate,
      yearRate: yearInterestRate,
      backType: repayName || repayWayDes.name || '等额偿还本金及分期手续费',
      overRate: overDueRate,
      repayDay: +((firstRepayDate || '').substr(6, 2)),
      rate: feeRate,
      repayTotalAmt: totalPrincipal,
      interestFeeTotalAmt: totalInterest,
      remark: (selectedBillList || []).map(({ orderNo }) => orderNo).join(','),
      prepayFeeLower,
      bringParam: 1,
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      ...contractAdvanceData
    };

    if (newContract === '3.0') {
      const startYear = dateStart.getFullYear();
      const startMonth = String(dateStart.getMonth() + 1).padStart(2, '0');
      const startDay = String(dateStart.getDate()).padStart(2, '0');
      const startFormattedDate = `${startYear}${startMonth}${startDay}`;

      const endYear = dateEnd.getFullYear();
      const endMonth = String(dateEnd.getMonth() + 1).padStart(2, '0');
      const endDay = String(dateEnd.getDate()).padStart(2, '0');
      const endformattedDate = `${endYear}${endMonth}${endDay}`;

      const { dateFormat } = Util.getCurrentDateTimeInFormat();
      contractData.baseContractInfo = {
        signDate: dateFormat,
        certType: '身份证'
      };
      contractData.loanTransInfo = {
        interestFeeTotalAmt: totalInterest, // 账单分期总金额
        repayTotalAmt: totalPrincipal, // 初始应还款总额
        periodStartDate: startFormattedDate, // 借款起始日期
        periodEndDate: endformattedDate, // 借款到期日期
      };
      contractData.markedPriceInfo = {
        totalCnt: cnt, // 借款期数
        orderNos: (selectedBillList || []).map(({ orderNo }) => orderNo).join(','), // 借据编号列表
        periodRate: feeRate && feeRate.toString(), // 分期手续费期费率
        periodYearRate: yearInterestRate && yearInterestRate.toString(), // 分期手续费期费率的年化利率(%)
        lprRatePeriod: lprRate && lprRate.toString(), // 按分期手续费期费率的最新1年期贷款市场报价利率(LPR)上浮百分点(%)
        repayDay: `${+((firstRepayDate || '').substr(6, 2))}`, // 还款日
        penaltyRate: overDueRate && overDueRate.toString()// 日罚息利率
      };
    }
    return contractData;
  }

  // 获取分期还本服务单需要的参数
  getRenewLoansParam = (contractParam, billExtendInfo) => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const { origBillInfo, currBillInfo } = billExtendInfo || {};
    const { orderNo, loanDate, installTotalAmt, installTotalCnt } = getStore('renewLoansInfo') || {};
    const { yearRate: origYearRate, originalPenaltyRate, lastRepayDate: origLastRepayDate } = origBillInfo || {};
    const { paymentTypeDesc, endInstallCnt: currEndInstallCnt, yearRate: currYearRate, penaltyRate, lastRepayDate: currLastRepayDate } = currBillInfo || {};

    const dateNow = new Date();
    const loanDateStart = new Date((loanDate || '').replace(/(\d{4})(\d{2})(\d{2})/, '$1/$2/$3'));
    const dateStart = new Date((Util.addOneDayToDate(origLastRepayDate) || '').replace(/(\d{4})(\d{2})(\d{2})/, '$1/$2/$3'));
    const dateEnd = new Date((currLastRepayDate || '').replace(/(\d{4})(\d{2})(\d{2})/, '$1/$2/$3'));

    const contractData = {
      name: this.custName || '',
      mobile: this.mobile || '',
      certName: '身份证',
      certId: this.idNo || '',
      yearNow: dateNow.getFullYear(),
      monthNow: dateNow.getMonth() + 1,
      dayNow: dateNow.getDate(),
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      bringParam: 1,
    };

    // 原借据借款日期
    const loanStartYear = loanDateStart.getFullYear();
    const loanStartMonth = String(loanDateStart.getMonth() + 1).padStart(2, '0');
    const loanStartDay = String(loanDateStart.getDate()).padStart(2, '0');
    const loanStartFormattedDate = `${loanStartYear}${loanStartMonth}${loanStartDay}`;

    // 分期还本办理日期
    const nowYear = dateNow.getFullYear();
    const nowMonth = String(dateNow.getMonth() + 1).padStart(2, '0');
    const nowDay = String(dateNow.getDate()).padStart(2, '0');
    const nowFormattedDate = `${nowYear}${nowMonth}${nowDay}`;

    // 分期还本生效日期：即续贷新增期次的第一日，即借据原截止日期+1日
    const startYear = dateStart.getFullYear();
    const startMonth = String(dateStart.getMonth() + 1).padStart(2, '0');
    const startDay = String(dateStart.getDate()).padStart(2, '0');
    const startFormattedDate = `${startYear}${startMonth}${startDay}`;

    // 分期还本借款到期日期（最后一个还款日）
    const endYear = dateEnd.getFullYear();
    const endMonth = String(dateEnd.getMonth() + 1).padStart(2, '0');
    const endDay = String(dateEnd.getDate()).padStart(2, '0');
    const endformattedDate = `${endYear}${endMonth}${endDay}`;

    contractData.baseContractInfo = {
      signDate: nowFormattedDate, // 分期还本办理日期
      baseDate: startFormattedDate, // 分期还本生效日期
    };
    contractData.loanTransInfo = {
      cashAmt: installTotalAmt, // 分期还本金额
      periodStartDate: loanStartFormattedDate, // 借款起始日期
      periodEndDate: endformattedDate, // 借款到期日期
      byMonthInterestPreviousCnt: installTotalCnt, // 分期还本前借据的 原分期期数
      byMonthInterestAfterCnt: Number(currEndInstallCnt - installTotalCnt), // 分期还本后增加的期数：即办理后总期数-办理前的原分期期数
    };
    contractData.markedPriceInfo = {
      orderNos: orderNo, // 借据编号列表
      totalCnt: currEndInstallCnt, // 分期还本，总期数（即最后一期期数）
      originalIrrAnnualRate: origYearRate ? `${(parseFloat(origYearRate) * 100).toFixed(4)}` : '', // 分期还本前实际利率
      actualIrrAnnualRate: currYearRate ? `${(parseFloat(currYearRate) * 100).toFixed(4)}` : '', // 分期还本后实际利率
      originalPenaltyRate: originalPenaltyRate ? `${(parseFloat(originalPenaltyRate) * 100).toFixed(4)}` : '', // 分期还本前日罚息利率
      penaltyRate: penaltyRate ? `${(parseFloat(penaltyRate) * 100).toFixed(4)}` : '', // 分期还本后日罚息利率
      repayType: paymentTypeDesc, // 分期还本还款方式
      advanceDesc: '提前还款免收违约金', // 分期还本提前还款说明
    };
    return contractData;
  }

  // 点击合同Radio选中的事件
  protocalCheckBoxHandler = (index, item) => {
    const { isCheckedContract, alreadyForceFlag, channelAgreementPlanCfgDtoList } = this.state || {};
    const { contracts } = item || {};
    const { onChecked } = this.props;
    const haveForceFlag = (contracts || []).some((listItem) => {
      const { forceReadFlag } = listItem || {};
      return forceReadFlag === 'Y';
    });
    let readDuration = 0;

    if (haveForceFlag && !alreadyForceFlag) {
      for (const contractItem of contracts) {
        const { forceReadDuration = 0, forceReadFlag } = contractItem || {};
        if (forceReadDuration) { // 接口有返回强制阅读时间，取需强读协议的最大强制阅读时间
          if (forceReadFlag === 'Y' && forceReadDuration > readDuration) {
            readDuration = forceReadDuration;
          }
        } else { // 默认强制阅读5s
          readDuration = 5;
        }
      }
    }

    if (isCheckedContract) {
      // 当前为选中状态
      this.setState({
        isCheckedContract: ''
      });
      onChecked(false);
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.CancelProtocalCheckBox`, beaconContent: { cus: { isChecked: false } } });
    } else if (haveForceFlag && !alreadyForceFlag) {
      // 需要强读，但是还未强读
      channelAgreementPlanCfgDtoList[index].showContract = true;
      channelAgreementPlanCfgDtoList[index].readDuration = readDuration;
      this.setState({
        channelAgreementPlanCfgDtoList
      });
    } else {
      // 当前为非选中状态，无需强读
      this.setState({
        isCheckedContract: 'hasCheckedContract'
      });
      onChecked(true);
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.ProtocalCheckBox`, beaconContent: { cus: { isChecked: true } } });
    }
  }

  // 点击协议文字事件
  protocalActionHandler = (index, item) => {
    const { channelAgreementPlanCfgDtoList, alreadyForceFlag } = this.state || {};
    const { contracts } = item || {};
    const haveForceFlag = (contracts || []).some((listItem) => {
      const { forceReadFlag } = listItem || {};
      return forceReadFlag === 'Y';
    });

    let readDuration = 0;
    if (haveForceFlag && !alreadyForceFlag) {
      for (const contractItem of contracts) {
        const { forceReadDuration = 0, forceReadFlag } = contractItem || {};
        if (forceReadDuration) { // 接口有返回强制阅读时间，取需强读协议的最大强制阅读时间
          if (forceReadFlag === 'Y' && forceReadDuration > readDuration) {
            readDuration = forceReadDuration;
          }
        } else { // 默认强制阅读5s
          readDuration = 5;
        }
      }
    }

    channelAgreementPlanCfgDtoList.forEach((contract, indexItem) => {
      channelAgreementPlanCfgDtoList[indexItem].showContract = true;
      channelAgreementPlanCfgDtoList[indexItem].readDuration = readDuration;
    });

    this.setState({
      channelAgreementPlanCfgDtoList
    });

    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.ProtocalPreview` });
  }

  // 强读点击确认或者取消回调
  contractSubmitClickHandler = (value, item, index) => {
    const { channelAgreementPlanCfgDtoList } = this.state || {};
    const { onChecked } = this.props;
    if (value) {
      channelAgreementPlanCfgDtoList[index].showContract = false;
      this.setState({
        channelAgreementPlanCfgDtoList,
        alreadyForceFlag: true,
        isCheckedContract: 'hasCheckedContract'
      });
      onChecked(true);
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.ProtocalCheckBox`, beaconContent: { cus: { isChecked: true } } });
    } else {
      channelAgreementPlanCfgDtoList[index].showContract = false;
      this.setState({
        channelAgreementPlanCfgDtoList
      });
    }
  }

  // 提供给父组件取消勾选的情况
  isCheckedContractToParent = (v) => {
    this.setState({ isCheckedContract: v });
    this.props.onChecked(v);
  }

  render() {
    const { channelAgreementPlanCfgDtoList, isCheckedContract } = this.state;
    const { contractApplyList = [], contractInfoList = [], className } = this.props;

    // 获取合同名称拼接信息，用于合同部分文字展示
    const { contractText } = channelAgreementPlanCfgDtoList && channelAgreementPlanCfgDtoList[0] || {};
    return (
      <MUView className="protocol">
        {
          channelAgreementPlanCfgDtoList.map((item, index) => {
            return (contractApplyList && contractApplyList.length > 0 || contractInfoList && contractInfoList.length > 0) ? (
              <MUView className="protocol-container">
                <MURadio
                  beaconId="ProtocalCheckRadio"
                  type="row"
                  className={className}
                  onClick={() => {
                    this.protocalCheckBoxHandler(index, item);
                  }}
                  options={[{
                    type: 'protocal',
                    labelLeft: '我已阅读并同意',
                    linkText: contractText,
                    onLink: () => {
                      this.protocalActionHandler(index, item);
                    },
                    value: 'hasCheckedContract'
                  }]}
                  value={isCheckedContract}
                />
                {
                  item.showContract && (
                    <AgreementDrawer
                      agreementViewProps={{
                        type: (item.contracts && item.contracts.length > 1) ? 1 : 2,
                        list: item.list,
                        current: 0,
                      }}
                      show={item.showContract}
                      close={() => this.contractSubmitClickHandler(false, item, index)}
                      submit={() => this.contractSubmitClickHandler(true, item, index)}
                      totalCount={item.readDuration}
                    />
                  )
                }
              </MUView>) : null;
          })
        }
      </MUView>
    );
  }
}
