/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import {
  MUView,
  MUImage,
} from '@mu/zui';
import Util from '@utils/maxin-util';
import { isMiniProgram } from '@utils/repay-util';
// import './index.scss';

const addBlockImage = 'https://file.mucfc.com/ebn/21/0/2023010/20231010145348cfe84f.png';
const deleteIcon = 'https://file.mucfc.com/ebn/21/0/2023010/20231010145305b82321.png';

/**
 * 图片选择组件，ZUI的组件兼容性堪忧
 */
export default function ImagePicker({
  count = 1,
  onChange,
  files = [],
}) {
  const onChooseImage = async () => {
    const newFileList = [...files];
    const imgObj = await Util.chooseImage({
      sizeThreshold: 0.3 * 1024 * 1024
    });
    if (!imgObj || !imgObj.src) return;
    const { src, path } = imgObj;
    newFileList.push({ index: +new Date(), src, path: isMiniProgram() ? path : src });
    onChange && onChange(newFileList);
  };

  const handleDelete = (index) => {
    const newFileList = files.filter((i) => i.index !== index);
    onChange && onChange(newFileList);
  };

  return (
    <MUView className="repay-img-picker">
      {files.map((item) => (
        <MUView className="img-picker-preview-block">
          <MUImage className="img-picker-preview" src={item.path} />
          <MUImage beaconId="imgPickerDelete" onClick={() => { handleDelete(item.index); }} className="img-picker-delete-icon" src={deleteIcon} />
        </MUView>
      ))}
      {files.length < count && <MUImage beaconId="imgPickerAdd" onClick={onChooseImage} src={addBlockImage} className="img-picker-add-block" />}
    </MUView>
  );
}
