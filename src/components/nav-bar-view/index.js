/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/prop-types */
import {
  Component
} from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  getCurrentPageUrlWithArgs
} from '@mu/madp-utils';
import {
  View
} from '@tarojs/components';
import {
  MUNavBar
} from '@mu/zui';
import {
  getStore
} from '@api/store';
import channelConfig from '@config/index';
import Util from '@utils/maxin-util';
import {
  getMUBarRightAreaConfig
} from '@mu/chat-entry-component';

export default class NavBarView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      config: {}
    };
  }

  componentDidMount() {
    if (channelConfig.showMuNavBar && window.history.length === 1) {
      // window.history.length为整个webview的历史栈长度，非当前页面index
      // 记录它等于1的时候，即之前没有任何页面，用于后面点击返回时做关闭页面判断
      // window.sessionStorage.setItem(`repayment_ENTRANCE`, );
      Madp.setStorageSync('repayment_ENTRANCE', getCurrentPageUrlWithArgs(), 'SESSION');
    }
    // 需要限制h5, 不然小程序编译报错
    if (process.env.TARO_ENV === 'h5' && Util.showChatEntry()) {
      const that = this;
      // 监听客服参数改变的消息并重置
      Madp.eventCenter.on('CHAT_ENTRY_EXTRAPARAM', () => {
        that.setRightAreaConfig();
      });
      const extraParam = getStore('chatEntryExtraParam');
      getMUBarRightAreaConfig({
        busiEntrance: Util.getBusiEntrance(),
        extraParam
      }).then(
        (config) => {
          // 目前只是客服组件需要用到这个，招行导航栏的右边部分内容
          // 判断一下是否需要展示客服导航栏，展示则设置config
          this.setState({
            config
          });
        }
      );
    }
  }

  setRightAreaConfig() {
    if (process.env.TARO_ENV === 'h5' && Util.showChatEntry()) {
      const extraParam = getStore('chatEntryExtraParam');
      getMUBarRightAreaConfig({
        busiEntrance: Util.getBusiEntrance(),
        extraParam
      }).then(
        (config) => {
          // 目前只是客服组件需要用到这个，招行导航栏的右边部分内容
          // 判断一下是否需要展示客服导航栏，展示则设置config
          this.setState({
            config
          });
        }
      );
    }
  }

  backClick() {
    try {
      const entrance = Madp.getStorageSync('repayment_ENTRANCE', 'SESSION');
      if (entrance && entrance === getCurrentPageUrlWithArgs()) {
        Madp.setStorageSync('repayment_ENTRANCE', '', 'SESSION');
        Madp.closeWebView();
      } else {
        Madp.navigateBack({
          delta: 1
        });
      }
    } catch (e) {
      // Do something when catch error
    }
  }


  render() {
    const {
      className,
      hoverClass,
      hoverStartTime,
      hoverStayTime,
      style,
      title,
      onClick,
    } = this.props;
    const {
      config
    } = this.state;

    const navTitle = title || '还款';

    return (
      <View
        className={className}
        hoverClass={hoverClass}
        hoverStartTime={hoverStartTime}
        hoverStayTime={hoverStayTime}
        style={style}
      >
        {channelConfig.showMuNavBar ? (
          <MUNavBar
            beaconId="NavBar"
            title={navTitle}
            fixed
            leftArea={[
              {
                type: 'icon',
                value: 'back',
                onClick: () => {
                  if (onClick) {
                    onClick();
                  } else {
                    this.backClick();
                  }
                }
              }
            ]}
            rightArea={[
              config
            ]}
          />
        ) : null}
        {this.props.children}
      </View>
    );
  }
}
