/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUIcon, MUImage
} from '@mu/zui';
import PropTypes from 'prop-types';
import { EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import Util from '@utils/maxin-util';
import {
  chatPageUrl
} from '@utils/constants';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}
const custServicePicture = 'https://file.mucfc.com/ebn/3/0/202404/20240425205237c9dcac.png';

export default class RepayGuide extends Component {
  static propTypes = {
    repayGuideInfo: PropTypes.object,
    entrance: PropTypes.string,
    themeColor: PropTypes.string,
    trackPage: PropTypes.string,
    custStatus: PropTypes.string,
  }

  static defaultProps = {
    repayGuideInfo: {}, // 还款指南信息
    entrance: '', // 客服入口
    themeColor: '', // 主题色
    trackPage: '',
    custStatus: '',
  }

  constructor(props) {
    super(props);
  }

  componentDidMount() {
    const { trackPage, custStatus, repayGuideInfo } = this.props;
    dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPage}ShowRepayGuide`, beaconContent: { cus: { custStatus } } });
    const {
      guideList
    } = repayGuideInfo;
    if ((guideList || []).length) {
      guideList.forEach((item) => {
        dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPage}ShowGuide${(item || {}).type}`, beaconContent: { cus: { custStatus } } });
      });
    }
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  // 跳转客服
  jumpToChatPage = (param = {}, type) => {
    const { trackPage, custStatus } = this.props;
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPage}JumpChat${type}`, beaconContent: { cus: { faqSeq: (param || {}).faqSeq, custStatus } } });
    Util.router.push({
      path: chatPageUrl,
      query: {
        ...param,
      },
      useAppRouter: true,
    });
  }

  render() {
    const {
      repayGuideInfo, entrance, themeColor, custStatus
    } = this.props;
    const {
      title, recommendFaqs, engineTitle, guideList
    } = repayGuideInfo;
    const guideListFlag = (guideList || []).length > 0;

    return (
      <MUView className="repay-guide">
        {title ? (
          <MUView className="repay-guide__title">{title}</MUView>
        ) : null}
        <MUView className="repay-guide__faqs">
          <MUView className="repay-guide__faqs__recommend">
            {(recommendFaqs || []).slice(0, 3).map((faqItem, i) => (
              <MUView
                className="faq-item"
                beaconId="FaqItemClick"
                onClick={() => this.jumpToChatPage({ busiEntrance: entrance, initQuestion: faqItem, faqSeq: i + 1 }, 'Faq')}
              >
                <MUView className="faq-item__dot" style={{ background: i === 0 ? themeColor : '#000' }} />
                <MUView className="faq-item__text" style={{ color: i === 0 ? themeColor : '#000' }}>
                  {(faqItem || '').length >= 15 ? `${faqItem.substring(0, 12)}...` : faqItem}
                </MUView>
                <MUView className="faq-item__arrow">
                  <MUIcon value="arrow-right" size={12} color={i === 0 ? themeColor : '#000'} />
                </MUView>
              </MUView>
            ))}
          </MUView>
          <MUView
            className="repay-guide__faqs__engine"
            beaconId="EngineClick"
            onClick={() => this.jumpToChatPage({ busiEntrance: entrance }, 'Engine')}
          >
            <MUView className="engine-picture">
              <MUImage src={custServicePicture} />
            </MUView>
            <MUView className="engine-text">{engineTitle}</MUView>
          </MUView>
        </MUView>
        {guideListFlag ? (
          <MUView className="repay-guide__supplys">
            {guideList.map((item, i) => (
              <MUView
                className={`${i !== 0 ? 'supply-item supply-item--special' : 'supply-item'}`}
                beaconId={`RepayGuide${(item || {}).type}`}
                beaconContent={{ cus: { custStatus } }}
                onClick={() => (item || {}).guideCall()}
              >{item.guideTitle}</MUView>
            ))}
          </MUView>
        ) : null}
      </MUView>
    );
  }
}
