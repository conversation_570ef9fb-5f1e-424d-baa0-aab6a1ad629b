@import '../../components/weapp/index.scss';

.repay-guide {
  margin: 20px 20px 0;
  padding: 24px 30px 30px;
  border-radius: 16px;
  background: #fff;
  &__title {
    margin-bottom: 20px;
    font-size: 32px;;
    line-height: 48px;
    color: #000;
    font-weight: 600;
  }
  &__faqs {
    padding: 24px 70px 24px 32px;
    width: calc(100% - 100px);
    height: 152px;
    border-radius: 12px;
    background: linear-gradient(0deg, #EEF3FC 50%, #DAE7FF 98%);
    display: flex;
    justify-content: space-between;
    &__recommend {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .faq-item {
        display: flex;
        align-items: center;
        &__dot {
          width: 10px;
          height: 10px;
          background: #000;
          border-radius: 50%;
        }
        &__text {
          margin-left: 20px;
          font-size: 24px;
          line-height: 36px;
          color: #000;
        }
        &__arrow {
          width: 24px;
          height: 26px;
          font-size: 0;
          .mu-icon {
            margin-top: 1px;
          }
        }
      }
    }
    &__engine {
      height: 152px;
      position: relative;
      .engine-picture {
        width: 124px;
        height: 124px;
        .taro-img, image {
          width: 100%;
          height: 100%;
        }
      }
      .engine-text {
        position: absolute;
        left: -17px;
        bottom: 0px;
        padding: 6px 19px;
        border-radius: 30px;
        background: #fff;
        box-shadow: 0px 0px 8px 0px rgba(14, 78, 209, 0.12);
        font-size: 20px;
        line-height: 36px;
        color: #3477FF;
        text-align: center;
        white-space: nowrap;
      }
    }
  }
  &__supplys {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;

    // --兼容微信小程序
    mu-view {
      flex: 1;
    }
    .supply-item {
      flex: 1;
      padding: 12px 0;
      border-radius: 30px;
      background: #F3F3F3;
      font-size: 22px;
      line-height: 36px;
      color: #000;
      text-align: center;
      &--special {
        margin-left: 16px;
      }
    }
  }
}
