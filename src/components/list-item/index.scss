@import '../../components/weapp/index.scss';

.repay-bill-list-item {
  margin-bottom: 20px;
  width: 100;
  min-height: 182px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
  font-size: 25px;
  color: #adadad;

  .main-block {
    display: flex;
    align-items: stretch;
  }

  .checker-block {
    width: 14vw;
    display: flex;
    justify-content: center;
    margin-top: 38px;

    .check-img {
      width: 40px;
      height: 40px;
    }

    &-display {
      width: 30px;
    }
  }

  .content-block {
    flex: 1;
    width: 86vw;

    .unstageable {
      background-image: url('https://file.mucfc.com/ebn/3/18/2023010/202310121438284a6b5a.png');
      background-repeat: no-repeat;
      background-size: 140px;
      background-position-x: 61%;
    }

    &-main {
      display: flex;
      height: 116px;
      justify-content: space-between;
      position: relative;

      &-left {
        position: absolute;
        top: 34px;

        .trade-tips {
          height: 48px;
          color: #333;
          font-size: 32px;
          line-height: 48px;
          // margin-bottom: 22px;
        }

        .trade-source {
          //overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 24px;
          line-height: 24px;

          .source {
            margin-left: 10px;
            color: #1a18188f;
          }
        }

        .over-due-label, .credit-product-flag {
          margin-left: 16px;
          height: 24px;
          font-size: 24px;
          line-height: 1;
          color: #FFF;
          padding: 8px 13px;
          border-radius: 5px;
          background-color: #FE5A5F;
          // line-height: initial;
        }

        .extend-label {
          margin-left: 16px;
          height: 24px;
          font-size: 24px;
          line-height: 1;
          color: #3477ff;
          padding: 8px 13px;
          border-radius: 5px;
          background-color: #ECF2FF;
        }
      }

      .credit-product-info, .section-rate-info {
        position: absolute;
        bottom: 20px;
        height: 36px;
        display: flex;
        font-size: 24px;
        line-height: 36px;
        color: #808080;
      }

      .section-rate-info {
        color: #FE5A5F;
        &__icon {
          margin-left: 10px;
        }
      }

      &-right {
        position: absolute;
        right: 0;
        top: 40px;
        max-width: 60%;
        text-align: right;

        .amount {
          height: 36px;
          font-size: 36px;
          line-height: 36px;
          color: #333;
          display: flex;
          align-items: center;

          .main-text-info {
            margin-left: 10px;
            margin-top: -5px;
          }
        }

        .waive-tips-info {
          height: 24px;
          position: absolute;
          right: 30px;
          top: 48px;
          font-size: 24px;
          line-height: 24px;
          color: #808080;
          white-space: nowrap;
          &--amount {
            color: #FE5A5F;
          }
        }

        .icon-accordion {
          margin-left: 8px;
          color: #A6A6A6;
          font-size: 26px !important;
        }

        .icon-accordion-up {
          transform: rotate(90deg);
        }

        .icon-accordion-down {
          transform: rotate(-90deg);
        }
      }
    }
    .has-credit-product {
      height: 148px;
    }
  }

  .single-line {
    display: flex;
  }

  .content-block-sub {
    position: relative;
    margin-left: 30px;
    margin-right: 30px;
    display: flex;
    height: 60px;
    line-height: 60px;
    justify-content: space-between;
    align-content: center;
    color: #808080;

    &-left {
      display: flex;
      align-items: center;
      font-size: 24px;

      .src-text {
        margin-left: 12px;
        display: flex;
        align-items: center;
        white-space: nowrap;

        .src-text-name {
          font-size: 24px;
          display: block;
          max-width: 120px;
          overflow: hidden;
          // text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    &-right {
      display: flex;
      align-items: center;
      font-size: 24px;

      .detail-icon {
        margin-top: -2px;
        margin-left: 8px;
        color: #A6A6A6;
      }
    }
  }

  .content-block-sub::before {
    content: "";
    position: absolute;
    /* stylelint-disable-next-line */
    height: 1PX;
    top: 0;
    left: 0;
    right: 0;
    background-color: #e6e6e6;
    transform: scaleY(0.5);
  }

  .accordion-content {
    // margin-top: -20px;
    padding-right: 68px;
    padding-bottom: 4px;

    .repay-detail-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 28px;
      line-height: 36px;
      color: #808080;
      margin-bottom: 20px;
    }
  }
}

.repay-bill-list-item-wrapper {
  .tips-modal-example-line {
    margin-top: 20px;
  }
  .section-rate-modal {
    text-align: center;
    &__title {
      font-size: 36px;
      line-height: 1;
      color: #333;
      font-weight: 500;
    }
    &__content {
      padding: 36px 40px 0;
      font-size: 32px;
      line-height: 45px;
      color: #808080;
      font-weight: 400;
    }
    &__content--special {
      color: #FE5A5F;
    }
    &__desc {
      padding: 20px 40px 36px;
      font-size: 26px;
      line-height: 40px;
      color: #888888;
      font-weight: 400;
    }
  }
}
