import { Component } from '@tarojs/taro';
import {
  MUView,
} from '@mu/zui';
import DetailItem from '@components/detail-item';
import PropTypes from 'prop-types';

const tipsInfoDict = {
  stagesFee: {
    tipsTitle: '利息说明',
    tipsConent: '应还利息=未还本金*日利率*计息天数-已优惠利息',
    tipsExample: '举例：如果贷款10000元，日利率为0.04%，则每日利息为4元，如果使用3天，则应还利息共12元',
  },
  periodFee: {
    tipsTitle: '分期手续费说明',
    tipsConent: '每期应还分期手续费=借据总本金*分期手续费期费率-已优惠分期手续费',
    tipsExample: '举例：如果贷款10000元，分期手续费期费率为0.5%，则每期分期手续费为50元',
  },
  IfineAmt: { // 利率类罚息说明
    tipsTitle: '罚息说明',
    tipsConent: '应还罚息= 逾期金额*罚息日利率*逾期天数',
    tipsExample: '举例：如果逾期金额1000元，罚息日利率为0.06%，逾期5天，则应还罚息3元',
  },
  FfineAmt: { // 费率类罚息说明
    tipsTitle: '罚息说明',
    tipsConent: '应还罚息=逾期金额*罚息日利率*逾期天数',
    tipsExample: '举例：如果逾期金额1000，罚息日利率为0.075%，逾期5天，则应还罚息3.75元',
  },
  IpayPrepayFee: { // 利率类提前还款违约金说明
    tipsTitle: '提前还款违约金说明',
    tipsConent: '提前还款违约金=提前还款本金*提前还款违约金比例（最低***元）',
    tipsExample: '举例：如果提前还款1000元本金，提前还款违约金比例为1%，则应还提前还款违约金为10元',
  },
  FpayPrepayFee: { // 费率类提前还款违约金说明
    tipsTitle: '提前还款违约金说明',
    tipsConent: '提前还款违约金=N*每期分期手续费',
    tipsExample: '举例：贷款10000元，每期分期手续费为50元，提前还款加收一期分期手续费，则应还提前还款违约金为50元',
  },
};

export default class ItemAccordionList extends Component {
  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    billDetail: PropTypes.object,
    showContent: PropTypes.bool,
    billType: PropTypes.string,
    itemClick: PropTypes.func,
  }

  static defaultProps = {
    billDetail: {},
    showContent: false,
    billType: '',
    itemClick: () => { }
  };

  constructor(props) {
    super(props);
    this.state = {

    };
  }

  handleItemClick(type) {
    const { itemClick, billDetail, billType } = this.props;
    if (billType !== 'total' || (billDetail && billDetail.displayStatus === '0')) return; // 非全部待还和可见不可还均不展示说明弹窗
    const tipsInfo = tipsInfoDict[type];
    // 利率类违约金说明的最小违约金需要动态取值
    if (type === 'IpayPrepayFee') {
      tipsInfo.tipsConent = tipsInfo.tipsConent.replace('***', billDetail.prepayFeeMinAmt || 0);
    }
    itemClick({
      showTipsModal: true,
      ...tipsInfo,
    });
  }

  render() {
    const {
      billDetail,
      showContent,
      billType,
    } = this.props;
    const { loanType } = billDetail;
    let originAmt = billDetail.surplusPayPrincipalAmt || '0.00'; // 待还本金
    let stagesFee = billDetail.surplusPayInteAmt || '0.00'; // 待还利息
    let periodFee = billDetail.surplusPayPeriodFeeAmt || '0.00'; // 待还期费用
    let fineAmt = billDetail.surplusPayFineAmt || '0.00'; // 待还罚息
    let oneTimeFee = billDetail.surplusPayOnetimeFeeAmt || '0.00'; // 剩余一次性手续费
    let payPrepayFee = billDetail.payPrepayFee || '0.00';
    let showPayFineAmt = Number(billDetail.surplusPayFineAmt || '0.00') + Number(billDetail.waivePayFineAmt || '0.00');
    let showPayOnetimeFeeAmt
      = Number(billDetail.surplusPayOnetimeFeeAmt || '0.00') + Number(billDetail.waivePayOnetimeFee || '0.00');
    let showPrepayFee = Number(billDetail.payPrepayFee || '0.00') + Number(billDetail.waivePayPrepayFee || '0.00');
    if (billType === 'advanced-stage') {
      // 省心分不展示息费减免的东西
      stagesFee = billDetail.extendedRepayInteAmt || '0.00';
      originAmt = billDetail.extendedRepayCapitalAmt || '0.00';
      periodFee = billDetail.extendedRepayFee || '0.00';
      fineAmt = billDetail.extendedRepayFineAmt || '0.00';
      oneTimeFee = billDetail.extendedRepayOnetimeFee || '0.00';
      showPayFineAmt = Number(billDetail.extendedRepayFineAmt || '0.00');
      showPayOnetimeFeeAmt = Number(billDetail.extendedRepayOnetimeFee || '0.00');
      payPrepayFee = '0.00'; // 省心分不展示提前还款手续费
      showPrepayFee = 0; // 省心分不展示提前还款手续费
    } else if (billType === 'extend') {
      periodFee = billDetail.surplusPayPeriodFee || '0.00'; // 待还期费用
      showPrepayFee = 0; // 延后还款日 不展示提前还款违约金
    }
    // eslint-disable-next-line max-len
    return (
      showContent && (
        <MUView className="accordion-content">
          {<DetailItem title="本金" value={`${originAmt}元`} />}
          {/* 七天待还、账单分期分期手续费 */}
          {loanType === 'I' && (
            <DetailItem beaconId="interest" handleClick={this.handleItemClick.bind(this, 'stagesFee')} title="利息" value={`${stagesFee}元`} waiveDiscount={billDetail.waivePayInteAmt} />
          )}
          {loanType !== 'I' && (
            <DetailItem beaconId="byStagesFee" handleClick={this.handleItemClick.bind(this, 'periodFee')} title="分期手续费" value={`${periodFee}元`} waiveDiscount={billDetail.waivePayPeriodFee} />
          )}
          {showPayFineAmt || Number(billDetail.waivePayFineAmt || '0.00') > 0 ? (
            <DetailItem beaconId="exceedFee" handleClick={this.handleItemClick.bind(this, `${loanType || 'I'}fineAmt`)} title="罚息" value={`${fineAmt}元`} waiveDiscount={billDetail.waivePayFineAmt} />
          ) : ''}
          {showPayOnetimeFeeAmt ? (
            <DetailItem title="平台服务费" value={`${oneTimeFee}元`} waiveDiscount={billDetail.waivePayOnetimeFee} />
          ) : ''}
          {showPrepayFee ? (
            <DetailItem beaconId="repaymentInfo" handleClick={this.handleItemClick.bind(this, `${loanType || 'I'}payPrepayFee`)} title="提前还款违约金" value={`${payPrepayFee}元`} waiveDiscount={billDetail.waivePayPrepayFee} />
          ) : ''}
        </MUView>
      )
    );
  }
}
