/* eslint-disable react/jsx-closing-bracket-location */
/* eslint-disable max-len */
/* eslint-disable operator-linebreak */
/* eslint-disable no-nested-ternary */
import Madp from '@mu/madp';
import { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import ItemAccordionList from '@components/list-item/itemAccordionList';
import RepayModal from '@components/repay-modal/index';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import {
  MUView, MUIcon, MUText, MUImage, MUModal, MUButton
} from '@mu/zui';
import { merchantNoList } from '@utils/constants';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

const themeColor = Util.getThemeColor(channelConfig.theme);
const checkCant = 'https://file.mucfc.com/zlh/3/0/202305/2023051820182812ffef.png';
const checkNo = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828a71df6.png';
const checkYes = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828e2a677.png';
const redCheckYes = 'https://file.mucfc.com/zlh/3/0/202305/20230518202125495a8a.png';

export default class BillListItem extends Component {
  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    item: PropTypes.object,
    billType: PropTypes.string, // 以后可能有用，目前只有随心分用，该组件无法勾选，点击则进行下一步
    infoClick: PropTypes.func,
    checked: PropTypes.bool,
    onItemCheck: PropTypes.func,
    disabled: PropTypes.bool,
    checkedIcon: PropTypes.bool,
    readOnly: PropTypes.bool,
    isAllZL: PropTypes.bool, //  是否全部商户都是招联金融
    showCreditProductInfo: PropTypes.string, // 展示诚信保护信息
    showCreditProductFlag: PropTypes.string, // 展示诚信保护角标
    showSectionRateFlag: PropTypes.string, // 是否展示分段利率提示
  }

  static defaultProps = {
    item: {},
    billType: '', // 7days七天，total提前还款，adjust宽限期延长，extend还款日延长，reserve-near预约还款，等同7天，reserve-total，等同全部
    infoClick: () => { },
    checked: false,
    onItemCheck: () => { },
    disabled: false,
    checkedIcon: true,
    readOnly: false,
    isAllZL: false,
    showCreditProductInfo: '',
    showCreditProductFlag: '',
    showSectionRateFlag: '',
  }

  constructor(props) {
    super(props);
    this.state = {
      showAccordion: false,
      // 费用明细说明弹窗
      showTipsModal: false,
      tipsTitle: '',
      tipsConent: '',
      tipsExample: '',
      showSectionRateModal: false,
      preventDefault: false,
    };
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  getItemOption(item) {
    const { billType, readOnly } = this.props;
    let option = {};
    if (!Object.keys(item).length) return option;
    if (billType === 'extend') {
      option = {
        amount: item.surplusPayTotalAmt,
        businessType: item.businessType || '',
        merchantName: item.merchantName || '',
        merchantNo: item.merchantNo || '',
        subDesc: `${Util.getDateCollection(item.loanDate).join('-')} ${item.businessType || '借款'}${item.installTotalAmt}元`,
        lastCntsDesc: `第${item.installCnt}/${item.installTotalCnt}期`,
        tradeTips: `${Util.getDateCollection(item.payDate).splice(1).join('月')}日应还`,
        overDueDesc: '',
        extendDesc: item.isDelayPlan === 'Y' ? '参与延期' : '',
        unStageAble: false,
        creditProductInfo: '',
        creditProductFlag: '',
      };
    } else {
      // 其实只有两种展示，一种是近期待还的展示，一种是全部待还的
      const itemType = (billType === 'total' || billType === 'reserve-total') ? 'total' : 'near';
      option = {
        // surplusPayTotalAmt七天
        amount: item.surplusPayTotalAmt,
        businessType: item.businessType || '',
        merchantName: item.merchantName || '',
        merchantNo: item.merchantNo || '',
        subDesc: `${Util.getDateCollection(item.loanDate).join('-')} ${item.businessType}${item.installTotalAmt}元`,
        lastCntsDesc: itemType === 'total'
          ? `剩余${item.surplusInstallCnt || item.installTotalCnt - item.installCnt + 1 || item.installTotalCnt}期`
          : `第${item.installCnt}/${item.installTotalCnt}期`,
        tradeTips: itemType === 'total'
          ? '剩余应还'
          : `${Util.getDateCollection(item.payDate).splice(1).join('月')}日应还`,
        overDueDesc: item.displayOverdueDays
          ? `逾期${item.displayOverdueDays}天`
          : '',
        unStageAble: billType === 'stages' && readOnly,
        creditProductInfo: (item.canExtendInteDateFlag === 'Y' && item.inteDate) ? `${Util.getDateCollection(item.inteDate).splice(1).join('月')}日` : '',
        creditProductFlag: item.canExtendInteDateFlag === 'Y' ? '诚信保护' : '',
      };
    }
    return option;
  }

  get waivePayTotal() {
    const { item = {} } = this.props;
    const waiveSum = Number(item.waivePayInteAmt || '0.00') + Number(item.waivePayFineAmt || '0.00')
      + Number(item.waivePayPeriodFee || '0.00') + Number(item.waivePayOnetimeFee || '0.00')
      + Number(item.waivePayPrepayFee || '0.00');
    return waiveSum.toFixed(2);
  }

  switchAccordion = (e) => {
    e.stopPropagation();
    console.log('switchAccordion');
    const { showAccordion } = this.state;
    this.setState({ showAccordion: !showAccordion });
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'switchAccordion' });
  }

  handleItemClick({
    showTipsModal, tipsTitle, tipsConent, tipsExample
  }) {
    this.setState({
      showTipsModal,
      tipsTitle,
      tipsConent,
      tipsExample,
    });
  }

  clearItemClick() {
    this.setState({
      showTipsModal: false,
      // 将内容设置为空后会闪现空白弹框
      // tipsTitle: '',
      // tipsConent: '',
      // tipsExample: ''
    });
  }

  render() {
    // eslint-disable-next-line object-curly-newline
    const { item = {}, infoClick, billType, checked, onItemCheck, disabled, checkedIcon, isAllZL, showCreditProductInfo, showCreditProductFlag, showSectionRateFlag } = this.props;
    const itemOption = this.getItemOption(item);
    const {
      showAccordion,
      showTipsModal,
      tipsConent,
      tipsExample,
      tipsTitle,
      showSectionRateModal
    } = this.state;

    const showMerchant = !isAllZL && item.merchantName && merchantNoList.indexOf(item.merchantNo) <= -1;

    return !Object.keys(item).length && !billType ? <MUView /> : (
      <MUView className="repay-bill-list-item-wrapper">
        <MUView
          className="repay-bill-list-item"
          beaconId="ItemCheck"
          onClick={() => {
            if (this.state.preventDefault) {
              return;
            }
            onItemCheck(item);
          }}
        >
          <MUView className="main-block">
            {checkedIcon ? (
              <MUView className="checker-block">
                <MUImage
                  src={disabled ? checkCant : (checked ? (themeColor === '#E60027' ? redCheckYes : checkYes) : checkNo)}
                  className="check-img"
                />
              </MUView>
            ) : (<MUView className="checker-block-display" />)}
            <MUView className="content-block">
              {/* 空格不要删 */}
              <MUView className={`content-block-main${itemOption.unStageAble ? ' unstageable' : ''}${(showCreditProductInfo === 'Y' || showCreditProductFlag === 'Y') ? ' has-credit-product' : ''}`}>
                <MUView>
                  <MUView className="content-block-main-left single-line">
                    <MUView className="trade-tips">{itemOption.tradeTips}</MUView>
                    {itemOption.overDueDesc ? <MUView className="over-due-label">{itemOption.overDueDesc}</MUView> : null}
                    {itemOption.extendDesc ? <MUView className="extend-label">{itemOption.extendDesc}</MUView> : null}
                    {!itemOption.overDueDesc && showCreditProductFlag === 'Y' && itemOption.creditProductFlag ? <MUView className="credit-product-flag" style={`background-color:${themeColor}`}>{itemOption.creditProductFlag}</MUView> : null}
                  </MUView>
                  {showCreditProductInfo === 'Y' && itemOption.creditProductInfo ? <MUView className="credit-product-info">
                    诚信保护期至
                    <View
                      style={`color:${themeColor}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        Madp.showModal({
                          content: '每日按正常利率计息，诚信保护期内完成还款不影响个人征信。',
                          showCancel: false,
                          confirmText: '知道了',
                          confirmColor: themeColor,
                        });
                        dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'ShowCreditProductInfo' });
                      }}
                    >
                      {itemOption.creditProductInfo}
                    </View>
                  </MUView> : null}
                  {showSectionRateFlag === 'Y' && item.preSettledPayRecallInteAmt && Number(item.preSettledPayRecallInteAmt) > 0 ? <MUView
                    className="section-rate-info"
                    beaconId="showSectionRateModal"
                    onClick={(e) => {
                      if (e && e.stopPropagation) {
                        e.stopPropagation();
                      }
                      this.setState({
                        showSectionRateModal: true,
                        preventDefault: true,
                      });
                    }}>
                    提前还款需退还已优惠利息
                    <MUIcon
                      color="#808080"
                      className="section-rate-info__icon"
                      value="info"
                      size="16"
                    />
                  </MUView> : null}
                </MUView>
                <MUView className="content-block-main-right single-line">
                  <MUView className="amount">
                    {/* eslint-disable-next-line react/jsx-one-expression-per-line */}
                    {itemOption.amount}元
                    <View style="padding-right: 15px;" onClick={this.switchAccordion}>
                      <MUIcon className={`icon-accordion ${showAccordion ? 'icon-accordion-up' : 'icon-accordion-down'}`} value="arrow-left" />
                    </View>
                  </MUView>
                  {this.waivePayTotal && Number(this.waivePayTotal) > 0 ? (
                    <MUView className="waive-tips-info">
                      (已减免
                      <MUText className="waive-tips-info--amount">{this.waivePayTotal}</MUText>
                      元)
                    </MUView>
                  ) : null}
                </MUView>
              </MUView>
              <ItemAccordionList showContent={showAccordion} billDetail={item} billType={billType} itemClick={this.handleItemClick.bind(this)} />
            </MUView>
          </MUView>
          <MUView className="content-block-sub">
            <MUView className="content-block-sub-left">
              {itemOption.subDesc}
              {showMerchant && (
                <MUView className="src-text">
                  来自
                  <MUText className="src-text-name">{itemOption.merchantName && itemOption.merchantName.slice(0, 5)}</MUText>
                </MUView>
              )}
            </MUView>
            <MUView className="content-block-sub-right">
              {/* eslint-disable-next-line react/jsx-one-expression-per-line */}
              {itemOption.lastCntsDesc}
              {(billType === 'total') && (
                <View
                  onClick={(e) => {
                    e.stopPropagation();
                    infoClick(item);
                    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'ToDetail' });
                  }}
                >
                  <MUIcon className="detail-icon" value="jump-cicle" size="16" />
                </View>
              )}
            </MUView>
          </MUView>
        </MUView>
        <RepayModal
          title={tipsTitle}
          beaconId="transeferModal"
          isOpened={showTipsModal}
          closeOnClickOverlay
          onClose={this.clearItemClick.bind(this)}
          confirmText="我知道了"
          onConfirm={this.clearItemClick.bind(this)}
        >
          <MUView className="modal-content">
            <MUView>
              {tipsConent}
            </MUView>
            <MUView className="tips-modal-example-line">{tipsExample}</MUView>
          </MUView>
        </RepayModal>
        <MUModal
          beaconId="SectionRateModal"
          isOpened={showSectionRateModal}
          className="section-rate-modal"
          onClose={() => this.setState({ showSectionRateModal: false, preventDefault: false })}
        >
          <MUView>
            <MUView className="section-rate-modal__title">说明</MUView>
            <MUView className="section-rate-modal__content">
              提前还款需退还已优惠利息
              <MUText className="section-rate-modal__content--special">{Number(item.preSettledPayRecallInteAmt).toFixed(2)}元</MUText>（此借据使用了分段利率，累计已优惠
              <MUText className="section-rate-modal__content--special">{Number(item.accumulatedLoanWaiveInteAmt).toFixed(2)}元</MUText>）
            </MUView>
            <MUView className="section-rate-modal__desc">
              *退还的已优惠利息=提前还款本金*优惠天数*(优惠前年利率-优惠后年利率)/365
            </MUView>
            <MUButton
              customStyle={`color: ${themeColor}`}
              beaconId="SectionRateModalButton"
              onClick={() => this.setState({ showSectionRateModal: false, preventDefault: false })}
            >我知道了</MUButton>
          </MUView>
        </MUModal>
      </MUView>
    );
  }
}
