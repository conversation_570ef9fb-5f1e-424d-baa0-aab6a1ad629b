/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  M<PERSON><PERSON><PERSON><PERSON>,
  MUTex<PERSON>,
  M<PERSON><PERSON>,
  MUI<PERSON>
} from '@mu/zui';
import PropTypes from 'prop-types';

const iconTicked = 'https://file.mucfc.com/mms/0/0/202404/20240425161756f888e8.png';
const alipayIcon = 'https://file.mucfc.com/mms/0/0/202404/20240425161756e1323a.png';
const bankCardIcon = 'https://file.mucfc.com/mms/0/0/202404/20240425161756b0b2c3.png';
const closeIcon = 'https://file.mucfc.com/mms/0/0/202404/20240425161756c0e2f9.png';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

export default class RepayModal extends Component {
  static propTypes = {
    onlyBindAlipay: PropTypes.bool,
    showAutoRepayDrawer: PropTypes.bool,
    agreeBindBank: PropTypes.func,
    agreeBindAlipay: PropTypes.func,
    contractInfo: PropTypes.object,
    cancelBind: PropTypes.func,
    viewContract: PropTypes.func,
  };

  static defaultProps = {
    onlyBindAlipay: false,
    showAutoRepayDrawer: false,
    contractInfo: {},
    agreeBindBank: () => { },
    agreeBindAlipay: () => { },
    cancelBind: () => { },
    viewContract: () => { },
  };

  constructor(props) {
    super(props);
    this.state = {
      selectAutoRepayType: 'bind-card',
    };
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  onItemSelect = (type) => {
    const { selectAutoRepayType } = this.state;
    if (selectAutoRepayType === type) return;
    this.setState({ selectAutoRepayType: type });
  }

  render() {
    const {
      onlyBindAlipay,
      showAutoRepayDrawer,
      viewContract,
      agreeBindBank,
      agreeBindAlipay,
      cancelBind,
      contractInfo,
    } = this.props;
    const { selectAutoRepayType } = this.state;

    const autoRepayType = onlyBindAlipay ? 'bind-alipay' : selectAutoRepayType;
    const contractName = (contractInfo && contractInfo.contractText) || '支付授权协议';

    return showAutoRepayDrawer ? (<MUDrawer
      beaconId="AutoRepayTypeDrawer"
      show={showAutoRepayDrawer}
      placement="bottom"
      height={onlyBindAlipay ? '53%' : '67%'}
      mask
      onClose={cancelBind}
    >
      <MUView className="repay-drawer">
        <MUView className="repay-drawer__top">
          <MUText className="repay-drawer__top--title">{onlyBindAlipay ? '开通支付宝自动还款' : '开通自动还款'}</MUText>
          <MUImage className="repay-drawer__top--close" beaconId="DrawerClose" src={closeIcon} onClick={cancelBind} />
        </MUView>
        {autoRepayType === 'bind-card' && !onlyBindAlipay ? (<MUView className="repay-drawer__cardTip">
          <MUView className="repay-drawer__cardTip--text">绑定指定银行卡，管理自动扣款顺序</MUView>
          <MUView className="repay-drawer__cardTip--arrow" />
        </MUView>) : null}
        {autoRepayType === 'bind-alipay' && !onlyBindAlipay ? (<MUView className="repay-drawer__alipayTip">
          <MUView className="repay-drawer__alipayTip--text">一键绑定支付宝账户，自动还款省心方便</MUView>
          <MUView className="repay-drawer__alipayTip--arrow" /></MUView>) : null}
        {onlyBindAlipay ? (<MUView className="repay-drawer__alipay"><MUImage className="repay-drawer__alipay--icon" src={alipayIcon} /></MUView>) : (<MUView className="repay-drawer__choose">
          <MUView className="repay-drawer__choose--card" beaconId="SelectBindCard" onClick={() => this.onItemSelect('bind-card')}>
            <MUView className="repay-drawer__choose--card--icon"><MUImage className="bankCardIcon" src={bankCardIcon} /></MUView>
            <MUText className="repay-drawer__choose--card--text">银行卡自动还款</MUText>
            {autoRepayType === 'bind-card' ? <MUImage className="repay-drawer__choose--card--check" src={iconTicked} /> : null}
          </MUView>
          <MUView className="repay-drawer__choose--alipay" beaconId="SelectBindAlipay" onClick={() => this.onItemSelect('bind-alipay')}>
            <MUView className="repay-drawer__choose--alipay--icon"><MUImage className="alipayIcon" src={alipayIcon} /></MUView>
            <MUText className="repay-drawer__choose--alipay--text">支付宝自动还款</MUText>
            {autoRepayType === 'bind-alipay' ? <MUImage className="repay-drawer__choose--alipay--check" src={iconTicked} /> : null}
          </MUView>
        </MUView>)}
        {autoRepayType === 'bind-alipay' ? (<MUView className="repay-drawer__contract">
          将跳转至支付宝，请阅读并同意 <MUText className="repay-drawer__contract--blue" onClick={viewContract} beaconId="ViewContract">{contractName}</MUText>
        </MUView>) : null}
        {autoRepayType === 'bind-card' ? (<MUButton className="repay-drawer__button" type="primary" beaconId="AgreeBindBank" onClick={agreeBindBank}>
          <MUView className="repay-drawer__button--icon"><MUIcon color="#3477FF" value="plus" size={15} /></MUView>
          <MUText className="repay-drawer__button--text">添加银行卡</MUText>
        </MUButton>) : (<MUButton className="repay-drawer__button" type="primary" beaconId="AgreeBindAlipay" onClick={agreeBindAlipay}>同意协议并开通</MUButton>)}
        <MUView className="repay-drawer__cancel" beaconId="NoNeedBind" onClick={cancelBind}>暂不需要</MUView>
        <MUView className="repay-drawer__desc">
          <MUView>还款日当天，自动划扣当期应还金额，请保证余额充足</MUView>
          <MUView>扣款结果将以短信形式告知您</MUView>
        </MUView>
      </MUView>
    </MUDrawer>) : null;
  }
}
