
.repay-drawer {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 30px;

  &__top {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &--title {
      color: #333333;
      font-size: 36px;
      font-weight: 600;
      line-height: 36px;
    }

    &--close {
      height: 36px;
      width: 36px;
      position: absolute;
      top: 0;
      right: 0;
      box-sizing: border-box;
      padding: 5px;
    }
  }

  &__cardTip {
    position: relative;
    margin-top: 64px;

    &--text {
      height: 48px;
      width: max-content;
      line-height: 48px;
      box-sizing: border-box;
      padding: 0 20px;
      font-size: 24px;
      font-weight: normal;
      color: #FF8844;
      border-radius: 8px;
      background: #FFF3EC;
      z-index: 1;
    }

    &--arrow {
      width: 14px;
      height: 14px;
      position: absolute;
      left: 155px;
      margin-top: -8px;
      transform: rotate(45deg);
      background: #FFF3EC;
    }
  }

  &__alipayTip {
    position: relative;
    display: flex;
    justify-content: flex-end;
    margin-top: 64px;

    &--text {
      height: 48px;
      width: max-content;
      line-height: 48px;
      box-sizing: border-box;
      padding: 0 20px;
      font-size: 24px;
      font-weight: normal;
      color: #FF8844;
      border-radius: 8px;
      background: #FFF3EC;
      z-index: 1;
    }

    &--arrow {
      width: 14px;
      height: 14px;
      position: absolute;
      bottom: -5px;
      right: 155px;
      margin-top: -8px;
      transform: rotate(45deg);
      background: #FFF3EC;
    }
  }

  &__choose {
    margin: 15px 0 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &--card,
    &--alipay {
      width: 330px;
      height: 218px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 8px;
      background: #F3F3F3;

      &--icon {
        width: 72px;
        height: 72px;
        margin: 48px 0 22px;
        display: flex;
        align-items: center;
        justify-content: center;

        .bankCardIcon {
          height: 72px;
          width: 72px;
        }
  
        .alipayIcon {
          height: 52px;
          width: 52px;
        }
      }

      &--text {
        height: 36px;
        font-size: 28px;
        font-weight: 600;
        line-height: 36px;
        color: #333333;
      }

      &--check {
        width: 56px;
        height: 56px;
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }

  &__alipay {
    margin: 80px 0 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    &--icon {
      height: 100px;
      width: 100px;
    }
  }

  &__contract {
    height: 40px;
    margin-bottom: 40px;
    font-size: 26px;
    line-height: 40px;
    color: #979797;

    &--blue {
      color: #3477FF
    }
  }

  &__button {
    width: 100%;
    height: 100px;

    .at-button__text {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    &--icon {
      width: 40px;
      height: 40px;
      line-height: 40px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 20px;
      background-color: #FFFFFF;

      .mu-icon {
        font-weight: 800;
      }
    }

    &--text {
      font-size: 36px;
      font-weight: 600;
      line-height: 40px;
      margin-left: 20px;
      color: #FFFFFF;
    }
  }

  &__cancel {
    height: 40px;
    margin-top: 30px;
    font-size: 26px;
    font-weight: normal;
    line-height: 40px;
    color: #808080;
    text-align: center;
  }

  &__desc {
    height: 72px;
    width: 92%;
    position: fixed;
    bottom: 40px;
    font-size: 24px;
    line-height: 36px;
    text-align: center;
    color: #979797;
  }
}
