import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import PropTypes from 'prop-types';
import { MUView, MUIcon } from '@mu/zui';
import { track, EventTypes } from '@mu/madp-track';
import './index.scss';

@track((props) => ({
  beaconId: props.beaconId,
  business: props.business,
  uiType: 'TradeChecker'
}))
export default class <PERSON><PERSON>hecker extends Component {
  static propTypes = {
    size: PropTypes.string,
    color: PropTypes.string,
    uncheckedColor: PropTypes.string,
    circleWeight: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
    value: PropTypes.bool,
    disabled: PropTypes.bool,
    onClick: PropTypes.func,
    className: PropTypes.oneOfType([PropTypes.array, PropTypes.string]),
  }

  static defaultProps = {
    size: 24,
    color: undefined,
    uncheckedColor: '#E5E5E5',
    circleWeight: false,
    value: false,
    disabled: false,
    className: '',
    onClick: () => { },
  }

  @track((props) => ({ event: EventTypes.SC, content: { c: 'mu-trade-checker', bqt: !props.value } }))
  handleClick = () => {
    const { disabled, onClick, value } = this.props;
    if (disabled) return;
    onClick(!value);
  }

  render() {
    const {
      value, size, color, className, uncheckedColor, circleWeight
    } = this.props;

    const remSize = Taro.pxTransform(parseInt(size) * 2);
    const custSizeStyle = `width: ${remSize}; height: ${remSize}`;
    const custBorderStyle = `border: ${circleWeight}px solid ${uncheckedColor};`;

    const custCircle = (
      <View className="cust-checker" style={custSizeStyle}>
        <View className="cust-checker-circle" style={custBorderStyle} />
      </View>
    );

    return (
      <MUView className={className}>
        <MUView
          className="mu-trade-checker"
          style={`line-height: ${remSize}`}
          beaconId="tradeChecker"
          onClick={this.handleClick}
        >
          {
            value ? (<MUIcon value="checked" size={size} color={color} />)
              : (circleWeight ? custCircle
                : (<MUIcon value="unchecked" size={size} color={uncheckedColor} />))
          }
        </MUView>
      </MUView>
    );
  }
}
