.business-columns {
  margin: 20px 20px 0;
  display: flex;
  background: #fff;
  border-radius: 16px;
  >mu-view {
    flex: 0.25;
  }
  &__item {
    flex: 0.25;
    text-align: center;
    padding: 34px 0 40px;
    position: relative;
    &__mark {
      width: 84px;
      height: 30px;
      // background-color: pink;
      position: absolute;
      right: 1%;
      top: 15%;
      z-index: 99;
    }
    &__icon {
      margin: 0 auto;
      width: 72px;
      height: 72px;
      font-size: 0;
      .taro-img, image {
        width: 100%;
        height: 100%;
      }
    }
    &__title {
      margin-top: 14px;
      font-size: 24px;
      line-height: 24px;
      color: #000;
      font-weight: 400;
    }
  }
}