import { Component } from '@tarojs/taro';
import {
  MUView, MUImage,
} from '@mu/zui';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

const mark = 'https://file.mucfc.com/ebn/3/0/202401/2024010511322006c69a.png';

export default class BusinessColumns extends Component {
  static propTypes = {
    businessColumns: PropTypes.array,
    havePrepayFeeRightsCoupon: PropTypes.bool, // 是否有免提还违约金资格券
    custStatus: PropTypes.string, // 客户状态
  }

  static defaultProps = {
    businessColumns: [],
    havePrepayFeeRightsCoupon: false,
    custStatus: '',
  };

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  handleServiceJump = (item) => {
    const { custStatus } = this.props;
    const { uniqueName, targetUrl } = item || {};
    let finalTargetUrl = targetUrl;
    if (uniqueName === 'BillAdvancedStage') {
      finalTargetUrl = finalTargetUrl.indexOf('?') > -1 ? `${targetUrl}&mtago=31025.01.09` : `${targetUrl}?mtago=31025.01.09`;
    } else if (uniqueName === 'BillExtend') {
      finalTargetUrl = finalTargetUrl.indexOf('?') > -1 ? `${targetUrl}&mtago=31025.01.01` : `${targetUrl}?mtago=31025.01.01`;
    } else if (uniqueName === 'BillListAll' || uniqueName === 'More') {
      finalTargetUrl = finalTargetUrl.indexOf('?') > -1 ? `${targetUrl}&custStatus=${custStatus}` : `${targetUrl}?custStatus=${custStatus}`;
    }
    Util.configUrlJump(finalTargetUrl, 'homeIndex');
  }

  render() {
    const {
      businessColumns, havePrepayFeeRightsCoupon
    } = this.props;

    return (
      <MUView className="business-columns">
        {businessColumns.map((item) => (
          <MUView
            className="business-columns__item"
            beaconId={`${item.uniqueName}Click`}
            onClick={() => this.handleServiceJump(item)}
          >
            {havePrepayFeeRightsCoupon && item.uniqueName === 'BillListAll' && <MUImage className="business-columns__item__mark" src={mark} />}
            <MUView className="business-columns__item__icon"><MUImage src={item.showImgUrl} /></MUView>
            <MUView className="business-columns__item__title">{item.title}</MUView>
          </MUView>
        ))}
      </MUView>
    );
  }
}
