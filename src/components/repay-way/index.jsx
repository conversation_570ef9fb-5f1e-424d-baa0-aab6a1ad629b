/* eslint-disable no-extra-boolean-cast */
/* eslint-disable taro/manipulate-jsx-as-array */
/* eslint-disable array-callback-return */
/* eslint-disable react/forbid-prop-types */
/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
/* eslint-disable no-nested-ternary */
import { Component } from '@tarojs/taro';
import {
  MUView,
  MUText,
  MUList,
  MUIcon,
  MUModal,
  MUImage,
  MUButton,
} from '@mu/zui';
import WaRichtext from '@mu/wa-richtext';
import Madp from '@mu/madp';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import { getStore, setStore } from '@api/store';
import CustomConfig from '@config/index';
import appInfos from '@utils/app-config-infos';
import {
  getWebViewName, isAlipay, isWechat, isQQEnv,
} from '@mu/madp-utils';
import { repayCheckOutInitDefaultCard } from '@utils/repay-util';
import OverPayRow from '@components/overpay-row';
import RepayListItem from '@components/repay-list-item';
import './index.scss';

const addCardsImg = 'https://file.mucfc.com/zlh/3/0/202305/202305182014221029da.png';
const transferImg = 'https://file.mucfc.com/zlh/3/0/202305/202305182014223a4b9a.png';
const moreImg = 'https://file.mucfc.com/zlh/3/0/202305/2023051820212575a65b.png';

let Rhyke;
if (process.env.TARO_ENV === 'h5') {
  Rhyke = require('rhyke');
}
export default class RepayWay extends Component {
  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    trackPrefix: PropTypes.string,
    isShow: PropTypes.bool, // 是否展示
    bankCards: PropTypes.array, // 银行卡列表
    select: PropTypes.func, // 选择函数
    closeWxAlipayFromCC: PropTypes.bool, // cc参数，是否关闭支付宝支付和微信支付
    overDue: PropTypes.bool, // 是否逾期
    isOverDueUserFlag: PropTypes.bool, // 是否逾期&已过宽限期
    transferGuide: PropTypes.func, // 跳转线下转账引导页
    addBankCard: PropTypes.func, // 添加银行卡
    isAlipayOnly: PropTypes.bool, // 是否只能用支付宝，支付宝内且cc配置
    payWayAlert: PropTypes.object, // 支付宝解释弹窗
    billType: PropTypes.string, // 账单类型
    isDueTagCust: PropTypes.bool, // 逾期打标，严重逾期
    bankConfig: PropTypes.object, // 展位系统支付方式优惠信息
    amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), // 实际支付金额
    bankCardErrorStatusList: PropTypes.object, // 错误银行卡列表
    overPayAmtRepay: PropTypes.number, // 溢缴款金额
    overPayAmtRepayFlag: PropTypes.bool, // 溢缴款金额是否展示
    remitTotalAmount: PropTypes.number, // 线下转账已识别未入账金额
    availableAmount: PropTypes.number, // 可用余额（展示用）
    closeAlipay0100BillFlag: PropTypes.bool, // 0:100借据是否显示 支付宝还款
    is0100Bills: PropTypes.bool, // 借据类型
    firstRepayConfig: PropTypes.object, // 优先还款类型,影响显示位置和默认选中
    preRepayAmt: PropTypes.number, // 预还款金额
    isShowDescTitle: PropTypes.bool, // 是否展示选择支付方式那一行,也用来控制预还款的微信和支付宝展示
    preRepayUsedAmount: PropTypes.number, // 预还款展示金额
    isRepayCheckout: PropTypes.bool, // 是否是还款收银台
    isFirstEnter: PropTypes.bool, // 还款收银台参数，是否首次加载，用于支付方式初始化
    modeOfPayment: PropTypes.array, // 当前渠道支持的还款方式数组，['1','2','3','4']，1: "银行卡支付"，2: "支付宝支付"，3: "微信支付"，4: "银行卡转账还款"
    firstRepayWay: PropTypes.string, // 优先展示的支付方式
    expressScene: PropTypes.string, // 快捷还款场景参数
    useTileShow: PropTypes.bool, // 平铺展示
    bankcardOpen: PropTypes.bool, // 银行卡展开
    bankcardClose: PropTypes.func, // 银行卡收起
    guideZfbCard: PropTypes.bool, // 引导支付宝选卡
  }

  static defaultProps = {
    trackPrefix: 'repayment.Repay',
    isShow: false,
    bankCards: [],
    select: () => { },
    closeWxAlipayFromCC: false,
    overDue: false,
    isOverDueUserFlag: false,
    transferGuide: () => { },
    addBankCard: () => { },
    isAlipayOnly: false,
    payWayAlert: {},
    billType: '',
    isDueTagCust: false,
    bankConfig: {},
    amount: 0,
    bankCardErrorStatusList: {},
    overPayAmtRepay: 0,
    overPayAmtRepayFlag: false,
    remitTotalAmount: 0,
    availableAmount: 0, // 可用余额（展示用）
    closeAlipay0100BillFlag: false,
    is0100Bills: () => { }, // 借据类型
    firstRepayConfig: {},
    preRepayAmt: 0,
    isShowDescTitle: true,
    preRepayUsedAmount: 0,
    isRepayCheckout: false,
    isFirstEnter: false,
    modeOfPayment: [],
    firstRepayWay: '',
    expressScene: '',
    useTileShow: false,
    bankcardOpen: false,
    bankcardClose: () => { },
    guideZfbCard: false,
  }

  constructor(props) {
    super(props);
    this.state = {
      forceWxPay: false,
      showPayAlert: false,
      selectedCard: {},
      moreWays: false,
      isCollapse: true, // 支付方式折叠
      tileBankHide: true, // 平铺银行卡折叠
    };
    this.alipay = {
      isAlipay: true,
      bankCardId: 'alipay',
      bankOrg: {
        bankName: '支付宝还款',
        bankImage: 'https://file.mucfc.com/ebn/3/18/2023010/202310121438282ab9c7.png',
        bankCode: '2088' // 交易给出
      }
    };
    this.wxPay = {
      isWxPay: true,
      bankCardId: 'wxpay',
      bankOrg: {
        bankName: '微信还款',
        bankImage: 'https://file.mucfc.com/ebn/3/18/2023010/202310121438282331af.png',
        bankCode: '2089' // 交易给出
      }
    };
    this.transferGuideInfo = {
      istTransferGuide: true,
      noteInfo: '推荐大额用户使用，单笔不限额',
      bankCardId: 'transferGuide',
      bankOrg: {
        bankName: '银行卡转账还款',
        bankCode: 'transfer' // 自定义
      }
    };
    this.isInited = false;
    // 预还款支付页和还款收银台使用的参数，是否是来自用户的点击
    this.repayWayUserClick = false;
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  componentDidMount() {
    const { isAlipayOnly, isRepayCheckout } = this.props;
    // console.log(getStore('selectedBillList'), 'selectedBillList-componentDidMount', is0100Bills());
    // 防止还款收银台拉起支付方式选择组件在仅可使用支付宝时支付组件不能被正常拉起
    if (isAlipayOnly && !isRepayCheckout) {
      // 只支持支付宝只能选支付宝，系统的初始化不需要弹窗，给false
      // didmount中添加这段逻辑是因为父组件是在设置了isAlipayOnly的prop之后才执行的子组件渲染
      // 且只支持支付宝时不会去获取银行卡，所以这种情况下初始化时WillReceiveProps是不会被触发
      // initDefaultCard不需要在这里执行是因为，银行卡的获取是在组件渲染之后进行的，获取了就会触发
      // 要是哪天银行卡获取放到了渲染前面，那也是要把逻辑挪到这的
      // anyway，这段逻辑也不是我写的，bless you
      this.selectCard(this.alipay, false);
    }
    const self = this;
    // 直接启用微信支付
    /* eslint-disable no-new */
    if (process.env.TARO_ENV === 'h5') {
      new Rhyke({
        rhythm: CustomConfig.labFunc.wxPay,
        tabEvent: true,
        matched() {
          if (window.location.href.indexOf('repay/index') > -1) {
            // 彩蛋注册了全局生效，根据路由限定一下范围
            CustomConfig.wxPay = true;
            self.setState({ forceWxPay: true });
            CustomConfig.labFunc.wxPayCallback();
          }
        }
      });
    }
  }

  componentWillReceiveProps(newProps) {
    if ((newProps || {}).bankcardOpen) this.setState({ tileBankHide: false });
  }

  // 绑新卡
  setNewCards(newPropsBankCards) {
    const { isAlipayOnly } = this.props;
    if (isAlipayOnly) return;
    // 如果是从绑卡成功页面跳转回来
    try {
      const cardInfo = Madp.getStorageSync('BANK_CARD_INFO_DATA', 'SESSION') || {};
      if (Object.keys(cardInfo).length && cardInfo.bankCard) {
        const cardData = newPropsBankCards.filter((card) => card.bankCardId === cardInfo.bankCard.cardId);
        if (!cardData.length) return;
        this.selectCard(cardData[0]);
        this.setState({ moreWays: true });
        // 只用一次
        // Madp.removeStorageSync('BANK_CARD_INFO_DATA', 'SESSION');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('error :>> ', error);
    }
  }

  /**
   *  初始化默认选择的银行卡,当上一张银行卡有异常，也会触发这个函数，再选择下一张银行卡/微信/支付宝
   */
  async initDefaultCard(cardsList, bankCardErrorStatusList) {
    const { isRepayCheckout, isFirstEnter, isAlipayOnly, is0100Bills } = this.props;
    if (isRepayCheckout) {
      const initCard = repayCheckOutInitDefaultCard(
        cardsList, bankCardErrorStatusList, isRepayCheckout, isAlipayOnly, is0100Bills(), isFirstEnter, this.aliRepayAvailable, this.wxRepayAvailable
      );
      if (initCard) {
        if (initCard.needSelectCardType === 'newBindCard') {
          this.selectCard(initCard.needSelectCard);
          this.setState({ moreWays: true });
        } else if (initCard.needSelectCardType === 'aliPay') {
          this.selectCard(this.alipay, initCard.isManual);
        } else if (initCard.needSelectCardType === 'wxpay') {
          this.selectCard(this.wxPay, initCard.isManual);
        } else if (initCard.needSelectCardType === 'bankCard') {
          this.selectCard(initCard.needSelectCard, initCard.isManual);
        }
      }
      return null;
    }
    // console.log('initDefaultCard :>> ', cardsList, bankCardErrorStatusList);
    // 绑卡回来默认选中刚绑的卡
    this.setNewCards(cardsList);
    if (isAlipayOnly) {
      // 只支持支付宝只能选支付宝，系统的初始化不需要弹窗，给false
      this.selectCard(this.alipay, false);
      return;
    }
    // 本次自动选择的银行卡
    let chosenCard = {};
    // 上一次选择的银行卡
    const tempCard = getStore('selectedCard') || {};
    // 筛选自动选的目标银行卡 有银行卡时
    if (cardsList && cardsList.length) {
      const defaultCards = []; // 默认卡，
      const availableCards = cardsList.filter((card) => {
        const {
          bankCardId,
          isDefault,
          needSignFundList = [], // 需要补签的资方
          // satisfyLimitAmtStatus = 'Y', // 是否满足限额
          // supplementSignFlag = 'N' // 招联是否需要补签
        } = card;
        // 可以选的卡，可以自动选择。
        const canDefaultSelect = (!needSignFundList || needSignFundList.length === 0);
        // canDefaultSelect && isDefault === 'Y' 初始条件
        if (canDefaultSelect && isDefault === 'Y') {
          defaultCards.push(card);// 默认扣款卡。（现在isDefault都是null, 后台的锅）
        } else if (canDefaultSelect && (!bankCardErrorStatusList || Object.keys(bankCardErrorStatusList).length === 0 || !bankCardErrorStatusList[bankCardId])) {
          return card;
        }
      });

      // 选择不置灰的默认银行卡或卡列表第一张卡
      const cardInfo = Madp.getStorageSync('BANK_CARD_INFO_DATA', 'SESSION') || {};
      const isNewBackCardFlag = cardInfo && cardInfo.bankCard && cardInfo.bankCard.cardId;
      const isDefaultCard = cardsList.find((item) => item.isDefault === 'Y'); // 是否是默认卡
      const newBindBank = cardsList.find((item) => item.bankCardId === isNewBackCardFlag); // 新绑卡
      const reNewSelectedCard = cardsList.find((item) => item.bankCardId === (tempCard && tempCard.bankCardId)); // 已选中的银行卡数据可能更新，使用接口返回的最新数据
      if (isNewBackCardFlag) {
        chosenCard = newBindBank || cardsList[0] || {};
        // console.log(chosenCard, '新绑定的chosenCard');
      } else if (bankCardErrorStatusList && JSON.stringify(bankCardErrorStatusList) !== '{}') {
        // 有错误卡的情况 判断是否是0:100 or 非0:100
        if (is0100Bills() === 'only0100Bill' || is0100Bills() === 'minxBill') {
          chosenCard = cardsList[0] || {};
        } else {
          const coupon = getStore('selectedCoupon') || {};
          // 勾选 支付宝或者微信支付时候 处理下刷新后还是当前支付方式
          if (tempCard.bankCardId && (tempCard.bankCardId === 'alipay' || tempCard.bankCardId === 'wxpay')) {
            chosenCard = tempCard;
            if (tempCard.bankCardId === 'alipay') {
              this.selectCard(this.alipay);
            } else if (tempCard.bankCardId === 'wxpay') {
              this.selectCard(this.wxPay);
            } else {
              this.selectCard(chosenCard, false);
            }
            return;
          }
          if (isDefaultCard && isDefaultCard.satisfyLimitAmtStatus === 'N' && isDefaultCard && isDefaultCard.supplementSignFlag === 'N' && coupon.awardNo) {
            chosenCard = cardsList[0] || {};
          } else {
            chosenCard = reNewSelectedCard && Object.keys(reNewSelectedCard).length > 0 ? reNewSelectedCard : isDefaultCard || cardsList[0] || {}; // 处理因为默认选中支付宝导致的银行卡列表选择有问题
          }
          // 不能选的卡
          const errorList = Object.keys(bankCardErrorStatusList);
          if (errorList.length === cardsList.length) {
            // 全部卡不能用 没有可用银行卡时，自动选一个支付宝/微信
            if (this.aliRepayAvailable) {
              this.selectCard(this.alipay);
            } else if (this.wxRepayAvailable) {
              this.selectCard(this.wxPay);
            } else {
              // 支付宝/微信也不可用时，清空state和store中已选择的卡，避免使用异常的卡重复提交
              this.setState({ selectedCard: {} });
              setStore({ selectedCard: {} });
            }
          }
        }
        // console.log(chosenCard, '其他的chosenCard1');
      } else {
        chosenCard = defaultCards[0] || availableCards[0] || {};
        // console.log(chosenCard, '其他的chosenCard2');
      }
    }

    // 初始化时或者自动选的银行卡变更时，走这一步
    if (!this.isInited || JSON.stringify(tempCard) !== JSON.stringify(chosenCard)) {
      this.isInited = true;
      if (chosenCard.bankCardId) { // 当自动选的银行卡存在时
        // console.log(*********, chosenCard);
        this.selectCard(chosenCard, false);
      } else if (tempCard && (tempCard.isWxPay || tempCard.isAlipay)) {
        // 有微信或者支付宝且已选中，取重复值
        if (tempCard.isWxPay) this.selectCard(this.wxPay);
        if (tempCard.isAlipay) this.selectCard(this.alipay);
      } else if (this.aliRepayAvailable) { // 没有可用银行卡时，自动选一个支付宝/微信
        this.selectCard(this.alipay);
      } else if (this.wxRepayAvailable) {
        this.selectCard(this.wxPay);
      } else {
        this.selectCard(chosenCard, false);
      }
    }
  }

  /**
   * 选择银行卡
   */
  selectCard(card, isManual = true) {
    // console.log('===selectCard', card, isManual);
    const { select, isAlipayOnly, bankCardErrorStatusList, isRepayCheckout } = this.props;
    let { selectedCard } = this.state;
    if (isRepayCheckout) {
      selectedCard = getStore('selectedCard');
    }
    // 用户主动选中卡，且仅支持支付宝，且点击支付宝时，才弹窗
    if (isManual && isAlipayOnly && card.bankCardId === 'alipay') {
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.AlipayOnlyCancelSelect` });
      this.gotoPayWayAlert();
    }
    // 重复选中一张卡无视即可, 银行卡状态有问题不可选中, 送了一张空卡过来也无视
    if (JSON.stringify(selectedCard) === JSON.stringify(card)
      // 协议异常的卡，可以再次选中
      || (bankCardErrorStatusList[card.bankCardId] && bankCardErrorStatusList[card.bankCardId].errCode !== 'UMDP01156')
      || !card.bankCardId) {
      return;
      // } else if (card.satisfyLimitAmtStatus === 'N') {
      //   this.setState({ moreWays: true });
      //   return;
    }
    this.setState({
      selectedCard: card,
    });
    if (typeof select === 'function') {
      select(card, this.repayWayUserClick);
    }
    setStore({
      selectedCard: card
    });
  }

  /** 展示银行卡列表的筛选以及调整。筛选储蓄卡，可用的首选卡放第一，不然就是一张可用卡  */
  filterBankCards(showAll) {
    const { bankCards: originalCards, bankCardErrorStatusList, is0100Bills } = this.props;
    let cardList = [];
    // 多张卡的情况下，第一张卡是默认卡，或者是可用卡
    let firsrCard = {};
    if (originalCards && originalCards.length) { // 有银行卡时
      const defaultCards = []; // 默认卡，
      const availableCards = originalCards.filter((card) => {
        if (card.cardType !== '2') { /** 不展示信用卡 */
          // 可用的卡
          // const available = card && card.satisfyLimitAmtStatus === 'Y' && !bankCardErrorStatusList[card.bankCardId];
          const available = card && !bankCardErrorStatusList[card.bankCardId];
          // 是否有新绑卡
          const cardInfo = Madp.getStorageSync('BANK_CARD_INFO_DATA', 'SESSION') || {};
          const isNewBackCardFlag = cardInfo && cardInfo.bankCard && cardInfo.bankCard.cardId;
          // 0:100 借据判断条件
          const selectBillFlag = is0100Bills() === 'only0100Bill' || is0100Bills() === 'minxBill';
          if (selectBillFlag) {
            if (isNewBackCardFlag && card.cardId === cardInfo.bankCard.cardId) {
              defaultCards.push(card);
            } else {
              return card;
            }
          } else if (is0100Bills() === 'normalBill') {
            // 非 0:100
            if (available && card.isDefault === 'Y') { // 筛选默认卡
              defaultCards.push(card);
            } else if (available) {
              return card;
            }
          }
        } else {
          dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.CreditCard` });
        }
      });
      // 选择不置灰的默认银行卡或卡列表第一张卡
      firsrCard = defaultCards[0] || availableCards[0] || {};
    }
    if (Object.keys(firsrCard).length) {
      const filterfirsrCardList = originalCards.filter((card) => card.bankCardId !== firsrCard.bankCardId);
      cardList = [firsrCard, ...filterfirsrCardList];
      // console.log(cardList, 'cardList-filterBankCards');
    } else {
      cardList = originalCards;
    }
    if (showAll && cardList.length) {
      return cardList;
    } else if (cardList.length) {
      return [cardList[0]];
    } else {
      return [];
    }
  }

  // 只支持支付宝只能选支付宝
  setAlipayOnlyCard(alipayOnly) {
    if (alipayOnly) {
      // 只支持支付宝只能选支付宝，系统的初始化不需要弹窗，给false
      this.selectCard(this.alipay, false);
    }
  }

  get isHideWxAliRepay() {
    const uniconHideWxAli = getStore('uniconHideWxAli');
    if (uniconHideWxAli) {
      // sydicatedLoanTag，01联合贷，00普通
      const unionList = getStore('selectedBillList').filter((list) => list.sydicatedLoanTag === '01');
      if (unionList.length) {
        return true;
      }
    }
    return false;
  }

  /**
   * 支付宝展示条件
   * 1、不能是微信环境；
   * 2、没有关闭微信支付宝标志；（选择的账单中有联合贷账单，联合贷账单会关闭）
   * 3、关闭的渠道：3CEBAPP、3CEZXAPP、3CMBAPP、3HSTRCBAPP、3MGAPP、3TXSPAPP。
   * 4、// 0ZFBMNPJD 暂时要关闭支付宝，打开银行卡, 后续会下线这个设定，打开银行卡
   * 特殊能够开放的情况：支付宝渠道会请求ABtest来判断是否仅支持支付宝还款，开放支付宝支付
   */
  get aliRepayAvailable() {
    const {
      isAlipayOnly, closeWxAlipayFromCC, closeAlipay0100BillFlag, isOverDueUserFlag, isDueTagCust, isShowDescTitle, isRepayCheckout, modeOfPayment
    } = this.props;
    // 新收银台才先走渠道参数的判断，旧的继续走旧逻辑
    if (isRepayCheckout) {
      if (modeOfPayment && modeOfPayment.indexOf('2') !== -1) {
        // 渠道开关打开，在该渠道下对所有客群都开放，但支付宝渠道不展示微信支付
        if (isWechat() || process.env.TARO_ENV === 'weapp') {
          return false;
        }
        return true;
      } else if (!isDueTagCust) {
        // 渠道开关关闭，在该渠道下对所有客群都关闭（除了逾期客户）
        return false;
      }
    }

    if (!isShowDescTitle && !isRepayCheckout) {
      // 预还款要展示支付宝，参数isShowDescTitle暂时仅有预支付使用
      return true;
    }
    if (appInfos.repayment && appInfos.repayment.isHideWxZfbRepayWay) {
      // 1、app参数控制关闭
      return false;
    } else if (isAlipayOnly) {
      // 2、ABtest来判断仅支持支付宝还款
      return true;
    } else if (CustomConfig.alipayOnOverdue && ((isRepayCheckout && isDueTagCust) || (!isRepayCheckout && (isOverDueUserFlag || isDueTagCust)))) {
      // 逾期时显示支付宝
      return true;
    } else if (!CustomConfig.alipay || this.isHideWxAliRepay || closeWxAlipayFromCC || closeAlipay0100BillFlag) {
      // 3、默认配置不支持支付宝支付的关闭，有联合带账单的关闭，cc配置的特殊渠道关闭, app参数控制关闭, 有0：100借据的关闭
      return false;
    } else {
      return true;
    }
  }

  /**
  * 微信展示条件
  * 特殊展示方式（暗门）：页面空白处，两短一长再两短。
  */
  get wxRepayAvailable() {
    // 手Q不展示微信支付
    const qqEnv = (typeof isQQEnv === 'function') ? isQQEnv() : false;
    if (qqEnv) {
      return false;
    }
    
    const { closeWxAlipayFromCC, isOverDueUserFlag, isDueTagCust, isShowDescTitle, isRepayCheckout, modeOfPayment } = this.props;
    const { forceWxPay } = this.state;
    // 新收银台才先走渠道参数的判断，旧的继续走旧逻辑
    if (isRepayCheckout) {
      if (modeOfPayment && modeOfPayment.indexOf('3') !== -1) {
        // 渠道开关打开，在该渠道下对所有客群都开放，但微信渠道不展示支付宝支付
        if (isAlipay() || process.env.TARO_ENV === 'alipay') {
          return false;
        }
        return true;
      } else if (!isDueTagCust) {
        // 渠道开关关闭，在该渠道下对所有客群都关闭（除了逾期客户）
        return false;
      }
    }

    if (!isRepayCheckout && !isShowDescTitle) {
      // 预还款要展示微信，参数isShowDescTitle暂时仅有预支付使用
      return true;
    }

    if (appInfos.repayment && appInfos.repayment.isHideWxZfbRepayWay) {
      // 1、app参数控制关闭
      return false;
    } else if (forceWxPay) {
      // 2、开启微信支付显示的暗门
      dispatchTrackEvent({ event: EventTypes.SO, beaconId: 'forceWxPayDisplay', target: this });
      return true;
    } else if (this.isHideWxAliRepay || closeWxAlipayFromCC || getWebViewName() === 'wx' || appInfos.repayment.isHideWxZfbRepayWay) {
      // 3、联合带账单关闭、cc配置的特殊渠道关闭、微信小程序里面的h5要关闭, app参数控制关闭， 有0：100借据的关闭
      return false;
    } else if (CustomConfig.wxPay && ((isRepayCheckout && isDueTagCust) || (!isRepayCheckout && (isOverDueUserFlag || isDueTagCust)))) {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'wxPayOverDueCust',
        beaconContent: { cus: { wxPay: CustomConfig.wxPay, isOverDueUserFlag, isDueTagCust } }
      });
      // 4、 微信环境或者app环境，而且是逾期用户,开启
      return true;
    } else {
      return false;
    }
  }

  async gotoPayWayAlert(tabClick = false) {
    const { payWayAlert } = this.props;
    if (tabClick) dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.AlipayOnlyPaywayAlert` });
    if (payWayAlert.dialog) {
      this.setState({ showPayAlert: true });
    }
  }

  async payWayAlertClick(configInfo) {
    // 为避免新旧还款跳转不一致，跳转链接针对taro有单独的设置，优先取new
    const jumpUrl = configInfo.newUrl || configInfo.defaultUrl;
    const { transferGuide } = this.props;
    if (!jumpUrl) return;
    if (jumpUrl.indexOf('@transfer-guide@') > -1) {
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.AlipayOnlyTransferAlert` });
      await transferGuide();
    } else if (jumpUrl.indexOf('http') > -1 && process.env.TARO_ENV === 'h5') {
      window.location.href = jumpUrl;
    } else {
      Util.router.push(jumpUrl);
    }
    this.setState({ showPayAlert: false });
  }

  // 绑卡
  gotoBindCard() {
    const { addBankCard } = this.props;
    addBankCard();
  }

  changMoreStatus(param) {
    if (param) {
      this.setState({ moreWays: param });
    } else {
      const { moreWays } = this.state;
      this.setState({ moreWays: !moreWays });
    }
  }

  checkFirstPay() {
    if (this.isWxZfbPayFirst && this.aliRepayAvailable) {
      this.selectCard(this.alipay, false);
    }
  }

  get showTransferGuide() {
    const { isAlipayOnly, billType, isDueTagCust, expressScene } = this.props;
    return (!isAlipayOnly && (billType === '7days' || billType === 'total' || isDueTagCust)) && (!expressScene || expressScene !== '31');
  }

  /*
   * 客户端参数firstRepayConfig说明：
   * @firstRepayWay: (string)优先展示的还款方式，配置'alipay'将支付宝前置且默认选中
   * @enableChannel: (array)上述配置的生效渠道
   * */
  get isWxZfbPayFirst() {
    const { firstRepayConfig = {} } = this.props;
    const { enableChannel = [], firstRepayWay } = firstRepayConfig;
    return enableChannel.indexOf(Madp.getChannel()) > -1 && (firstRepayWay === 'alipay');
  }

  get isCollapse() {
    const { firstRepayConfig = {}, isAlipayOnly } = this.props;
    const { collapseOtherChannel = [] } = firstRepayConfig;
    const { isCollapse } = this.state;
    return !isAlipayOnly && this.isWxZfbPayFirst && this.aliRepayAvailable && collapseOtherChannel.indexOf(Madp.getChannel()) > -1 && isCollapse;
  }

  changePreRepayWayUserClick(val) {
    this.repayWayUserClick = val;
  }

  getPreRepayWayUserClick() {
    return this.repayWayUserClick;
  }

  changeTileStatus() {
    const { bankcardClose } = this.props;
    const { tileBankHide } = this.state;
    if (typeof bankcardClose === 'function') {
      bankcardClose();
    }
    this.setState({ tileBankHide: !tileBankHide });
  }


  render() {
    const {
      isShow, isAlipayOnly, payWayAlert, overPayAmtRepay, bankConfig,
      bankCardErrorStatusList, overPayAmtRepayFlag, availableAmount, remitTotalAmount, preRepayAmt,
      isShowDescTitle, preRepayUsedAmount, isRepayCheckout, modeOfPayment, firstRepayWay, useTileShow, guideZfbCard, showCeilingDialogFn
    } = this.props;
    let {
      selectedCard, showPayAlert, moreWays, tileBankHide
    } = this.state;
    const { isCollapse } = this;
    // console.log(overPayAmtRepay, 'render-overPayAmtRepay', amount,'availableAmount',availableAmount);
    /**
     * 以下关于支付方式的jsx数组，最好放在render中执行，避免小程序原生化报错
     * */
    // 如果有溢缴款余额，需要在第一条加上
    if (Number(overPayAmtRepay) > 0) {
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${this.props.trackPrefix}.OverPayAmtShow` });
    }

    if (isRepayCheckout) {
      selectedCard = getStore('selectedCard');
    }

    let filterCards = this.filterBankCards(moreWays);
    if (isRepayCheckout) {
      if (useTileShow) {
        filterCards = this.filterBankCards(!tileBankHide);
      } else {
        filterCards = this.filterBankCards(true);
      }
    }
    // 遍历银行卡，展示还款方式。（为啥放在render里面呢？因为放到其他函数中，小程序原生展示不出来，暂无其他方案）
    const cardsListNodes = filterCards.map((item) => {
      const {
        singleDeductionLimit, bankCardNoMask,
        bankOrgShortName, bankCardId, bankOrgCode,
        bankImage, deductionLimit, bankName
      } = item;
      // 添加兜底：如未返回银行卡简称，按照原来的展示
      const title = bankOrgShortName ? `${bankOrgShortName} (${bankCardNoMask.slice(-4)})` : `${bankName} (${bankCardNoMask.slice(-4)})`;
      let checkedValue = selectedCard.bankCardId === bankCardId ? 'checked' : 'unchecked';
      let checkedStyle = selectedCard.bankCardId === bankCardId ? 'brand-text' : 'unchecked';
      const bannerContent = bankOrgCode && bankConfig[bankOrgCode];
      // 展示银行卡异常文案
      let ErrorText = '';
      // 是否展示单笔最高限额icon（超限额还款提交后展示）
      let showCeilingIcon = false;
      // 银行卡是否支持
      let cardSupport = true;
      // if (satisfyLimitAmtStatus === 'N') {
      //   checkedValue = 'close-plain';
      //   checkedStyle = 'unchecked';
      //   ErrorText = '还款金额超限额';
      //   cardSupport = false;
      //   dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'repayment.Repay.RepayOverLimit', beaconContent: { cus: { bankCardId } } });
      // }
      if (bankCardErrorStatusList[bankCardId]) {
        // checkedValue = 'close-plain';
        // checkedStyle = 'unchecked';
        ErrorText = bankCardErrorStatusList[bankCardId].errMsg;
        showCeilingIcon = bankCardErrorStatusList[bankCardId].showCeilingIcon;
        cardSupport = bankCardErrorStatusList[bankCardId].support;
      }
      // 有ErrorText则不展示单笔最高支持
      const desc = (singleDeductionLimit && !ErrorText)
        ? `单笔最高支持${Number(singleDeductionLimit) / 10000}万元` : '';
      return (
        <RepayListItem
          renderTitle={(
            <MUView>
              <MUView>{title}</MUView>
              <MUView className={`${bannerContent ? 'title-banner-content' : ''}`}>
                {bannerContent}
              </MUView>
            </MUView>
          )}
          renderNote={( // 注意，百度小程序不支持动态传入dom，如 showCeilingDesc && <MUText>{desc}</MUText>
            <MUView>
              <MUText>{desc}</MUText>
              <MUText>
                {ErrorText}
              </MUText>
              <MUIcon size="14" value={showCeilingIcon ? 'info' : ''} className="repay-payway-ceilingIcon" onClick={() => showCeilingDialogFn(singleDeductionLimit, deductionLimit)} />
            </MUView>
          )}
          disabledArrow
          renderExtraText={(bankCardErrorStatusList[bankCardId] && bankCardErrorStatusList[bankCardId].errCode !== 'UMDP01156') ? <MUView className="renderExtraText-bankCardError" /> : <MUIcon className={checkedStyle} value={checkedValue} size="20" />} // 卡item最右边的按钮样式或文案
          thumb={bankImage}
          beaconId="repayWayCard"
          onClick={() => this.selectCard(item)}
          cardSupport={cardSupport} // 不支持的银行卡
        />
      );
    });
    // 展示【换卡支付】条件
    const showPaywayMore = !isAlipayOnly && (Number(overPayAmtRepay) > 0 || filterCards.length);
    // 银行卡列表下面展示【添加银行卡】条件
    const bankCardListWithAddCard = moreWays && showPaywayMore;
    // 微信支付列表展示【添加银行卡】条件
    const wxAliWayListWithAddCard = !isAlipayOnly && !filterCards.length && Number(overPayAmtRepay) <= 0;
    const dialogConfig = payWayAlert.dialog || [];

    const addBankCardListItem = (
      <RepayListItem
        className="repay-payway-list-add"
        thumb={addCardsImg}
        thumbStyle
        renderTitle={(
          <MUView>
            使用新银行卡
          </MUView>
        )}
        renderNote={guideZfbCard ? (
          <MUView style="color:#f84;">
            支持免输卡号快速添加
          </MUView>
        ) : ''}
        // eslint-disable-next-line taro/render-props
        extraText="仅支持储蓄卡"
        // renderExtraText={
        //   process.env.TARO_ENV === 'weapp'
        //     ? (<MUIcon value="arrow-right" color="#CCC" size="15" />) : <MUView>仅支持储蓄卡</MUView>
        // }
        arrow="mu-icon-arrow-right"
        beaconId={`${filterCards.length ? 'repayWay' : 'BindBankcard'}`}
        onClick={() => this.gotoBindCard()}
      />
    );

    const addBankCardListItemSwan = (
      <RepayListItem
        className="repay-payway-list-add"
        renderTitle={(
          <MUView>
            添加银行卡
            <MUText className="repay-payway-list-add-text">
              （仅支持储蓄卡）
            </MUText>
          </MUView>
        )}
        thumb={addCardsImg}
        beaconId={`${filterCards.length ? 'repayWay' : 'BindBankcard'}`}
        // eslint-disable-next-line taro/render-props
        renderExtraText={
          process.env.TARO_ENV === 'weapp'
            ? (<MUIcon value="arrow-right" color="#CCC" size="15" />) : <MUView />
        }
        onClick={() => this.gotoBindCard()}
        thumbStyle
      />
    );

    const zfbPayContent = (
      <MUList>
        {this.aliRepayAvailable ? (
          <MUView>
            <RepayListItem
              className="hide-note" // CSS控制了隐藏下面的note，因为直接删除样式会对不齐
              renderTitle={(
                <MUView>
                  <MUView>支付宝支付</MUView>
                  {Boolean(this.alipay.bankOrg && bankConfig[this.alipay.bankOrg.bankCode]) ? (
                    <MUView
                      className="title-banner-content"
                    >
                      {this.alipay.bankOrg && bankConfig[this.alipay.bankOrg.bankCode]}
                    </MUView>
                  ) : null}
                </MUView>
              )}
              renderNote={<MUView>推荐支付宝用户使用</MUView>}
              disabledArrow
              renderExtraText={(
                <MUIcon
                  className={isAlipayOnly || selectedCard.bankCardId === this.alipay.bankCardId ? 'brand-text' : 'unchecked'}
                  value={isAlipayOnly || selectedCard.bankCardId === this.alipay.bankCardId ? 'checked' : 'unchecked'}
                  size="20"
                />
              )}
              thumb={this.alipay.bankOrg && this.alipay.bankOrg.bankImage}
              beaconId="repayWayAlipay"
              onClick={() => this.selectCard(this.alipay)}
            />
            {useTileShow ? (<MUView className="repay-payway-space" />) : null}
          </MUView>
        ) : null}
      </MUList>
    );

    const wxPayContent = (
      <MUList>
        {!isCollapse && this.wxRepayAvailable ? (
          <MUView>
            <RepayListItem
              renderTitle={(
                <MUView>
                  <MUView>微信还款</MUView>
                  {Boolean(this.wxPay.bankOrg && bankConfig[this.wxPay.bankOrg.bankCode]) ? (
                    <MUView
                      className="title-banner-content"
                    >
                      {this.wxPay.bankOrg && bankConfig[this.wxPay.bankOrg.bankCode]}
                    </MUView>
                  ) : null}
                </MUView>
              )}
              renderNote={<MUView>推荐微信用户使用</MUView>}
              disabledArrow
              renderExtraText={(
                <MUIcon
                  className={selectedCard.bankCardId === this.wxPay.bankCardId ? 'brand-text' : 'unchecked'}
                  value={selectedCard.bankCardId === this.wxPay.bankCardId ? 'checked' : 'unchecked'}
                  size="20"
                />
              )}
              thumb={this.wxPay.bankOrg && this.wxPay.bankOrg.bankImage}
              beaconId="repayWayWxPay"
              onClick={() => this.selectCard(this.wxPay)}
            />
            {useTileShow ? (<MUView className="repay-payway-space" />) : null}
          </MUView>
        ) : null}
      </MUList>
    );

    const transferPay = (
      <MUList>
        {!isCollapse && this.showTransferGuide ? (
          <MUView>
            <RepayListItem
              className="repay-payway-list-transferGuide"
              renderTitle={(
                <MUView>
                  <MUView>银行卡转账还款</MUView>
                  {Boolean(this.transferGuideInfo.bankOrg && bankConfig[this.transferGuideInfo.bankOrg.bankCode]) ? (
                    <MUView
                      className="title-banner-content"
                    >
                      APP专享-最高可返30元
                    </MUView>
                  ) : null}
                </MUView>
              )}
              renderNote={(
                <MUView>
                  <MUText>{this.transferGuideInfo.noteInfo}</MUText>
                </MUView>
              )}
              renderExtraText={(
                <MUIcon
                  className={selectedCard.bankCardId === this.transferGuideInfo.bankCardId ? 'brand-text' : 'unchecked'}
                  value={selectedCard.bankCardId === this.transferGuideInfo.bankCardId ? 'checked' : 'unchecked'}
                  size="20"
                />
              )}
              disabledArrow
              thumb={transferImg}
              beaconId="BankCardTransfer"
              onClick={() => this.selectCard(this.transferGuideInfo)}
            />
            {useTileShow ? (<MUView className="repay-payway-space" />) : null}
          </MUView>
        ) : null}
      </MUList>
    );

    const WxZfbPay = (
      <MUList className="repay-payway-wxAliList">
        {wxPayContent}
        {zfbPayContent}
        {!isCollapse && wxAliWayListWithAddCard && !isRepayCheckout ? addBankCardListItem : null}
      </MUList>
    );

    let tileBankContent = null;
    if (tileBankHide && filterCards && filterCards.length) {
      tileBankContent = <MUView>{cardsListNodes}</MUView>;
    }
    if (tileBankHide && (!filterCards || filterCards.length === 0) && !isAlipayOnly) {
      // 在百度中当原本tileBankContent进入该if分支，然后tileBankHide变了又进入下方的if分支，此时tileBankContent的结构会有错误
      // 推测是相似结构的替换在百度有问题，所以改成addBankCardListItemSwan，它和addBankCardListItem是完全一样的
      tileBankContent = process.env.TARO_ENV === 'swan' ? <MUView>{addBankCardListItemSwan}</MUView> : <MUView>{addBankCardListItem}</MUView>;
      // tileBankContent = <MUView>{addBankCardListItem}</MUView>;
    }
    if (!tileBankHide) {
      tileBankContent = (
        <MUView>
          {cardsListNodes}
          <MUView>{addBankCardListItem}</MUView>
        </MUView>
      );
    }

    return (
      <MUView className={isShow ? 'repay-payway' : 'display-none'}>
        {isShowDescTitle ? (<MUView className="repay-payway-desc">
          <MUText className="repay-payway-desc-title">选择支付方式</MUText>
          {isAlipayOnly ? (
            <MUText
              className="repay-payway-desc-link"
              onClick={() => this.gotoPayWayAlert(true)}
              beaconId="repayWayNotice"
            >
              {payWayAlert.content || ''}
              <MUImage className="repay-payway-desc-link-img" src={moreImg} />
            </MUText>
          ) : (
            <MUText
              className="repay-payway-desc-link"
              beaconId="repayLimitList"
            >
              支付限额说明
            </MUText>
          )}
        </MUView>) : null}
        {/* 新旧支付方式 */}
        {isRepayCheckout ? (
          useTileShow ? (
            <MUView>
              {/* 新的支付方式：都统一用MUView包一下，不然在小程序中被当作普通变量编译会报错 */}
              <MUList>
                {/* 优先展示的支付方式，当且仅当cc配置的优先展示支付方式包含在当前渠道支付的还款支付方式内才生效（管理端已做限制，这里在兜底判断一下） */}
                {firstRepayWay === '1' && modeOfPayment.indexOf(firstRepayWay) !== -1 && (<MUView>
                  {tileBankContent}
                  {
                    (filterCards && filterCards.length) ? (
                      <MUView className="repay-payway-more repay-payway-more--tile-show" beaconId="ChangeCard" onClick={() => this.changeTileStatus()}>
                        <MUText className="repay-payway-more-text repay-payway-more-text--tile-show">
                          {tileBankHide ? '换卡支付' : '收起'}
                        </MUText>
                        <MUImage className="repay-payway-more-img repay-payway-more-img--tile-show" style={`transform : rotate(${!tileBankHide ? '270' : '90'}deg)`} src={moreImg} />
                      </MUView>
                    ) : null
                  }
                  <MUView className="repay-payway-space" />
                </MUView>)}
                {firstRepayWay === '2' && modeOfPayment.indexOf(firstRepayWay) !== -1 && (<MUView>
                  {zfbPayContent}
                </MUView>)}
                {firstRepayWay === '3' && modeOfPayment.indexOf(firstRepayWay) !== -1 && (<MUView>
                  {wxPayContent}
                </MUView>)}
                {firstRepayWay === '4' && modeOfPayment.indexOf(firstRepayWay) !== -1 && (<MUView>
                  {transferPay}
                </MUView>)}

                {/* 下面主要是展示优先展示的支付方式之外的其他支付方式（目的是为了去重）
              1、没有在上面优先展示了的支付方式，且是当前渠道支持的支付方式，就下面就展示
              2、微信、支付宝存在逾期客户的展示问题，没有在上面优先展示了的支付方式，或不在当前渠道支持的支付方式内，要进一步判断是否满足逾期客户展示条件 */}
                {firstRepayWay !== '1' && modeOfPayment && modeOfPayment.indexOf('1') !== -1 && (<MUView>
                  {tileBankContent}
                  {
                    (filterCards && filterCards.length) ? (
                      <MUView className="repay-payway-more repay-payway-more--tile-show" beaconId="ChangeCard" onClick={() => this.changeTileStatus()}>
                        <MUText className="repay-payway-more-text repay-payway-more-text--tile-show">
                          {tileBankHide ? '换卡支付' : '收起'}
                        </MUText>
                        <MUImage className="repay-payway-more-img repay-payway-more-img--tile-show" style={`transform : rotate(${!tileBankHide ? '270' : '90'}deg)`} src={moreImg} />
                      </MUView>
                    ) : null
                  }
                  <MUView className="repay-payway-space" />
                </MUView>)}
                {(firstRepayWay !== '2' || modeOfPayment.indexOf(firstRepayWay) === -1) && (<MUView>
                  {zfbPayContent}
                </MUView>)}
                {(firstRepayWay !== '3' || modeOfPayment.indexOf(firstRepayWay) === -1) && (<MUView>
                  {wxPayContent}
                </MUView>)}
                {firstRepayWay !== '4' && modeOfPayment && modeOfPayment.indexOf('4') !== -1 && (<MUView>
                  {transferPay}
                </MUView>)}
              </MUList>
            </MUView>
          ) : (
            <MUView>
              {/* 新的支付方式：都统一用MUView包一下，不然在小程序中被当作普通变量编译会报错 */}
              <MUList>
                {/* 优先展示的支付方式，当且仅当cc配置的优先展示支付方式包含在当前渠道支付的还款支付方式内才生效（管理端已做限制，这里在兜底判断一下） */}
                {firstRepayWay === '1' && modeOfPayment.indexOf(firstRepayWay) !== -1 && (<MUView>{cardsListNodes}</MUView>)}
                {firstRepayWay === '2' && modeOfPayment.indexOf(firstRepayWay) !== -1 && (<MUView>{zfbPayContent}</MUView>)}
                {firstRepayWay === '3' && modeOfPayment.indexOf(firstRepayWay) !== -1 && (<MUView>{wxPayContent}</MUView>)}
                {firstRepayWay === '4' && modeOfPayment.indexOf(firstRepayWay) !== -1 && (<MUView>{transferPay}</MUView>)}

                {/* 下面主要是展示优先展示的支付方式之外的其他支付方式（目的是为了去重）
            1、没有在上面优先展示了的支付方式，且是当前渠道支持的支付方式，就下面就展示
            2、微信、支付宝存在逾期客户的展示问题，没有在上面优先展示了的支付方式，或不在当前渠道支持的支付方式内，要进一步判断是否满足逾期客户展示条件 */}
                {firstRepayWay !== '1' && modeOfPayment && modeOfPayment.indexOf('1') !== -1 && (<MUView>{cardsListNodes}</MUView>)}
                {(firstRepayWay !== '2' || modeOfPayment.indexOf(firstRepayWay) === -1) && (<MUView>{zfbPayContent}</MUView>)}
                {(firstRepayWay !== '3' || modeOfPayment.indexOf(firstRepayWay) === -1) && (<MUView>{wxPayContent}</MUView>)}
                {firstRepayWay !== '4' && modeOfPayment && modeOfPayment.indexOf('4') !== -1 && (<MUView>{transferPay}</MUView>)}
              </MUList>
              {!isAlipayOnly && modeOfPayment && modeOfPayment.indexOf('1') !== -1 ? (<MUView>{addBankCardListItem}</MUView>) : null}
            </MUView>
          )
        ) : (
          <MUView>
            {/* 溢缴款 */}
            <MUList className="repay-payway-list">
              <OverPayRow
                uncheck={selectedCard.bankCardId === 'transferGuide'}
                overPayAmtRepayFlag={overPayAmtRepayFlag}
                overPayAmtRepay={overPayAmtRepay}
                remitTotalAmount={remitTotalAmount}
                availableAmount={availableAmount}
                preRepayAmt={preRepayAmt}
                preRepayUsedAmount={preRepayUsedAmount}
              />
              {this.isWxZfbPayFirst && WxZfbPay}
              {isCollapse && (
                <MUView className="repay-payway-collapse" beaconId="collapse" onClick={() => this.setState({ isCollapse: false })}>
                  <MUText>其他支付方式</MUText>
                  <MUImage className="repay-payway-collapse-img" src={moreImg} />
                </MUView>
              )}
              {!isCollapse && cardsListNodes}
              {!isCollapse && bankCardListWithAddCard && !isRepayCheckout ? addBankCardListItem : null}

            </MUList>
            {
              !isCollapse && showPaywayMore && !isRepayCheckout ? (
                <MUView className="repay-payway-more" beaconId="ChangeCard" onClick={() => this.changMoreStatus()}>
                  <MUText className="repay-payway-more-text">
                    换卡支付
                  </MUText>
                  <MUImage className="repay-payway-more-img " style={`transform : rotate(${moreWays ? '270' : '90'}deg)`} src={moreImg} />
                </MUView>
              ) : null
            }
            {!this.isWxZfbPayFirst && WxZfbPay}
            {transferPay}
          </MUView>
        )}
        <MUModal
          className="payway-notice-dialog"
          beaconId="RepayNotice"
          isOpened={showPayAlert}
          closeOnClickOverlay={false}
          onClose={() => this.setState({ showPayAlert: false })}
        >
          <MUView className="title">{payWayAlert.title}</MUView>
          <MUView className="content" beaconId="RepayNoticeContent" onClick={() => this.payWayAlertClick(dialogConfig[0])}>
            <WaRichtext beaconId="RepayNoticeRichtext" content={Util.getRepaymentLocale('HK01.HK01WA013')} />
          </MUView>
          <MUButton
            className="btn"
            beaconId="RepayNoticeComfirm"
            onClick={() => this.setState({ showPayAlert: false })}
          >
            {payWayAlert.btnContent}
          </MUButton>
        </MUModal>
      </MUView>
    );
  }
}
