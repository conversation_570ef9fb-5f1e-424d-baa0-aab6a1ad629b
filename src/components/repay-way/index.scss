@import '../../components/weapp/index.scss';
@import '../overpay-row/index.scss';

.repay-payway {
  &-desc {
    display: flex;
    padding: 40px 30px 20px;
    font-size: 26px;
    line-height: 26px;
    justify-content: space-between;

    &-title {
      color: #808080;
    }

    &-link {
      color: #808080;

      &-img {
        width: 26px;
        height: 26px;
        vertical-align: -4px;
      }
    }
  }

  &-wxAliList {
    background: #fff;
    margin-top: 20px;

    .at-list__item-content {
      width: 70%;
      position: relative;
    }

    .title-banner-content {
      margin-top: 10px;
      font-size: 22px;
      color: #FF8844;
      line-height: 1.2;
      /* stylelint-disable-next-line */
      border: 1PX solid #FF8844;
      border-radius: 2px;
      padding: 4px;
      display: inline-block;
    }

    .at-list__item .item-content__info-title {
      line-height: 1.2;
    }
  }

  &-collapse {
    text-align: center;
    font-size: 28px;
    color: #808080;
    height: 80px;
    line-height: 80px;

    &-img {
      width: 24px;
      height: 24px;
      line-height: 28px;
      margin-left: 10px;
      transform: rotate(90deg);
      display: inline-block;
      vertical-align: middle;
      transform-origin: 80% 50%;
    }
  }

  &-list {
    background: #fff;
    overflow: hidden;

    // .repay-list-list__item-content {
    //   width: 70%;
    //   position: relative;
    // }
    &-add {
      .repay-list-list__item--thumb {
        width: 48px;
        height: 48px;
        padding: 16px;
      }

      .at-list__item--thumb {
        padding: 0 16px;
        margin-top: 7px;
        margin-bottom: 7px;
      }

      &-text {
        font-size: 26px;
        color: #808080;
        line-height: 26px;
        font-weight: 400;
      }
    }

    &-guide {
      margin-top: 20px;
      background: #fff;

      &-link {
        font-size: 28px;

      }

      .item-content__info {
        width: 400px;
      }

      .repay-list-list__item-content {
        width: 50%;
      }
    }

    &-transferGuide {
      margin-top: 20px;
      background: #fff;

      .item-content__info {
        width: 400px;
      }

      .repay-list-list__item-content {
        width: 50%;
      }

      .title-banner-content {
        margin-top: 10px;
        font-size: 22px;
        color: #FF8844;
        line-height: 1.2;
        /* stylelint-disable-next-line */
        border: 1PX solid #FF8844;
        border-radius: 2px;
        padding: 4px;
        display: inline-block;
      }
    }

    &-close {
      height: 0;
      transition: height 0.8s;
    }

    &-open {
      height: 283px;
      transition: height 0.8s;
    }

    &-openSmall {
      height: 143px;
      transition: height 0.8s;
    }

    .at-list__item .item-content__info-title {
      line-height: 1.2;
    }

    .title-banner-content {
      margin-top: 10px;
      font-size: 22px;
      color: #FF8844;
      line-height: 1.2;
      /* stylelint-disable-next-line */
      border: 1PX solid #FF8844;
      border-radius: 2px;
      padding: 4px;
      display: inline-block;
    }
  }


  &-more {
    font-size: 28px;
    color: #808080;
    font-weight: 400;
    line-height: 80px;
    padding-left: 140px;
    background-color: #fff;
    position: relative;

    &-text {
      display: inline-block;
    }

    &-img {
      width: 24px;
      height: 24px;
      line-height: 26px;
      margin-left: 10px;
      transform: rotate(90deg);
      display: inline-block;
      vertical-align: middle;
      transform-origin: 70% 50%;
    }
  }

  &-ceilingIcon {
    margin-left: 10px;
    color: #a6a6a6;
  }

  &-more::before {
    content: "";
    position: absolute;
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    pointer-events: none;
    bottom: auto;
    left: 24px;
    right: 0;
    top: 0;
    -webkit-transform: scaleY(0.5);
    -ms-transform: scaleY(0.5);
    transform: scaleY(0.5);
    //border-top: 1PX solid #E5E5E5;
  }

  &-more--tile-show {
    font-size: 26px;
    color: #A6A6A6;
    padding-left: 0;
    text-align: center;
  }

  .payway-notice-dialog {
    .mu-modal__container {
      top: 55%;
    }

    .title {
      text-align: center;
      margin-bottom: 20px;
      font-size: 45px;
      line-height: 45px;
    }

    .content {
      padding: 0 30px 20px;
    }

    .img {
      width: 100%;
      height: auto;
    }
  }

  .hide-note {
    .item-content__info-note {
      display: none;
    }

    .item-thumb {
      // 让zfb方式高度一致
      margin-top: 7px;
      margin-bottom: 7px;
    }
  }

  .unchecked {
    color: #cacaca;
  }

  &-tooMore {
    // color: #e00b0b;
    margin-left: 1em;

    &-deleteMargin {
      margin-left: 0;
    }
  }

  .item-extra__info {
    font-size: 28px;
    color: #A6A6A6;
  }

  &-space {
    height: 20px;
    background: #f3f3f3;
  }

  .renderExtraText-bankCardError {
    width: 40px;
    height: 40px;
    border: 0 solid #E5E5E5;
    background: #F3F3F3;
    border-radius: 50%;
  }
}

.display-none {
  display: none;
}

.title-banner-content {
  margin-top: 10px;
  font-size: 22px;
  color: #FF8844;
  line-height: 1.2;
  /* stylelint-disable-next-line */
  border: 1PX solid #FF8844;
  border-radius: 2px;
  padding: 4px;
  display: inline-block;
}