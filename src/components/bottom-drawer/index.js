/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
import {
  Component
} from '@tarojs/taro';
import {
  MUDrawer,
  MUView,
  MUIcon,
  MUText
} from '@mu/zui';
import PropTypes from 'prop-types';
import classNames from 'classnames';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

export default class BottomDrawer extends Component {
  static propTypes = {
    title: PropTypes.string,
    onClose: PropTypes.func,
  }

  static defaultProps = {
    title: '',
    onClose: () => {},
  }

  constructor(props) {
    super(props);
    this.state = {
      show: false,
    };
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  show(func) {
    this.setState({
      show: true,
    }, () => {
      if (func) {
        func();
      }
    });
  }

  hide() {
    const {
      onClose
    } = this.props;
    onClose();
    this.setState({
      show: false,
    });
  }

  render() {
    const {
      show
    } = this.state;
    const {
      title,
      className,
    } = this.props;
    return (
      <MUDrawer
        beaconId="MUDrawer"
        show={show}
        placement="bottom"
        height="66%"
        onClose={() => { this.hide(); }}
      >
        <MUView className="drawer">
          <MUView className="drawer-top">
            <MUView className="drawer-top-holder" />
            <MUText className="drawer-top-title">{title}</MUText>
            <MUIcon
              beaconId="DrawerClose"
              value="close2"
              className="drawer-top-close"
              size="16"
              onClick={() => { this.hide(); }}
            />
          </MUView>
          <MUView className={
              classNames(className, 'drawer-content')
            }
          >
            {this.props.children}
          </MUView>
        </MUView>
      </MUDrawer>
    );
  }
}
