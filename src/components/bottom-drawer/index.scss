@import '~@mu/zui/dist/style/mixins/index.scss';
@import '~@mu/zui/dist/style/variables/default.scss';

.drawer {
  display: flex;
  flex: 1;
  height: 100%;
  flex-direction: column;
  background: #f3f3f3;

  &-top {
    position: fixed;
    top: 0;
    flex-direction: row;
    height: 100px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #FFFFFF;

    &-holder {
      padding-left: 36px;
      height: 32px;
      width: 32px;
    }

    &-title {
      font-size: $font-size-h1;
      color: #333333;
      font-weight: 700;
    }

    &-close {
      margin-right: 30px;
      height: 32px;
      width: 32px;
      color: #A6A6A6;
    }
  }

  &-content {
    overflow: scroll;
    margin-top: 100px;
  }

}

.at-drawer__content {
  overflow: hidden;
  border-radius: 16px 16px 0 0;
}

.bottom-height {
  margin-bottom: 100px;
}
