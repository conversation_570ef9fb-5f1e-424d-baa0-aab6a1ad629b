/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
import {
  Component
} from '@tarojs/taro';
import {
  MUDrawer,
  MUView,
  MUImage,
  MUText
} from '@mu/zui';
import PropTypes from 'prop-types';
import classNames from 'classnames';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

const icClose = 'https://file.mucfc.com/zlh/3/0/202305/202305182019591e0b87.png';

/** BottomDrawer简化版：使用isOpen控制Drawer显示隐藏
 *  原因是支付宝小程序父子嵌套组件同时需要获取ref时，会出现ref获取不到的情况
 *  所以改造了原来使用ref.show/hide的方式
 *  onTopCloseClick的存在原因：onClose会在drawer关闭的时候调用一次，如果onTopCloseClick
 *  里面直接调onClose()，那么onClose会被调用两次，为了防止一些意外的情况发生，
 *  把onTopCloseClick单独提出来处理
 */
export default class BottomDrawer extends Component {
  static propTypes = {
    title: PropTypes.string,
    isOpen: PropTypes.bool,
    onClose: PropTypes.func,
    onTopCloseClick: PropTypes.func,
  }

  static defaultProps = {
    title: '',
    isOpen: false,
    onClose: () => {},
    onTopCloseClick: () => {},
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    "styleIsolation" : "shared"
  }

  render() {
    const {
      title,
      className,
      isOpen,
      onClose,
      onTopCloseClick
    } = this.props;
    return (
      <MUDrawer
        beaconId="MUDrawer"
        show={isOpen}
        placement="bottom"
        height="66%"
        onClose={() => { onClose && onClose(); }}
      >
        <MUView className="drawer">
          <MUView className="drawer-top">
            <MUView className="drawer-top-holder" />
            <MUText className="drawer-top-title">{title}</MUText>
            <MUImage
              beaconId="DrawerClose"
              className="drawer-top-close"
              src={icClose}
              onClick={() => { onTopCloseClick && onTopCloseClick(); }}
            />
          </MUView>
          <MUView className={
              classNames(className, 'drawer-content')
            }
          >
            {this.props.children}
          </MUView>
        </MUView>
      </MUDrawer>
    );
  }
}
