.cert-form {
  .date-picker {
    position: relative;

    &:after {
      position: absolute;
      z-index: 99;
      content: '';
      left: 33px;
      width: calc(100% - 33px);
      border-bottom: 1px solid rgba(229, 229, 229, 0.5);
    }
  }

  .amount-input {
    position: relative;

    .at-input.input {
      padding-right: 80px;
    }

    .unit {
      position: absolute;
      right: 30px;
      top: 18px;
      font-size: 35px;
    }
  }

  .cert-block {
    padding: 15px 33px;
    font-size: 35px;

    .cert-sub-title {
      color: #ccc;
      font-size: 32px;
      margin-left: 35px;
    }

    .picker {
      margin: 15px 0 8px -28px;
    }
  }

  .tipModal-text {
    text-align: left;
  }

  .pay-mode {
    &::after {
      position: absolute;
      z-index: 99;
      content: '';
      left: 33px;
      width: calc(100% - 33px);
      /* stylelint-disable-next-line */
      border-bottom: 1PX solid rgba(229, 229, 229, 0.5);
    }
    &-item {
      display: flex;
      flex-direction: row;
      &-title {
        display: inline-block;
      }
      &-arrow {
        display: inline-block;
        justify-content: center;
        color: #CACACA;
        align-items: center;
        justify-content: center;
        margin: auto 15px;
      }
    }
  }
}
