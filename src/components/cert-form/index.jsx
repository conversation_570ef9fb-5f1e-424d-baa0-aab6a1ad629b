/* eslint-disable object-curly-newline */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-param-reassign */
/* eslint-disable react/sort-comp */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUForm, MUInput, MUView, MUPicker, MUText, MUImagePicker,
  MUActionSheet, MUActionSheetItem, MUModal, MUIcon
} from '@mu/zui';
import { isHWAtomic, isMuapp } from '@mu/madp-utils';
import Util from '@utils/maxin-util';
import Dispatch from '@api/actions';
import PropTypes from 'prop-types';
// import './index.scss';

export default class CertForm extends Component {
  static propTypes = {
    title: PropTypes.string,
    className: PropTypes.string,
    fsizeThreshold: PropTypes.number,
    type: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    onFormChanged: PropTypes.func,
  }

  static defaultProps = {
    title: '',
    className: '',
    fsizeThreshold: 1048576,
    type: '',
    onFormChanged: () => { }
  }

  constructor(props) {
    super(props);
    this.state = {
      account: '',
      displayDate: '',
      displayBank: '',
      transferAmount: '',
      certUrls: [],
      showBankList: false,
      payWaysIndex: 0,
      payWays: ['银行卡转账', '现金汇款'],
      voucherNo: '',
      showModal: false,
      inputBank: false
    };
    this.result = {
      type: props.type,
      payWaysIndex: 0,

      voucherNo: '',

      account: '',
      bankOrg: '',

      date: '',
      amount: '',
      certData: ''
    };
    this.bankOrgList = [];
  }

  async componentDidMount() {
    const bankOrgs = await Dispatch.repayment.getBankOrgList();
    this.bankOrgList = bankOrgs.map(({ bankCode, bankName }) => ({
      bankCode, bankName
    }));
    this.bankOrgList.push({ bankCode: 'noBank', bankName: '付款方银行不存在？点击此处输入全名' });
  }

  async onImgPicked(files) {
    // let certUrl = '';
    let certData = '';
    if (files.length) {
      const { file } = files[0];
      const { fsizeThreshold } = this.props;
      certData = await this.urlToBase64(file, fsizeThreshold);
      // const { remitImg } = await Dispatch.repayment.uploadCertificate(certData);
      // certUrl = remitImg;
    }
    this.setState({ certUrls: files });
    // this.result.certUrl = certUrl;
    this.result.certData = certData;
    const { onFormChanged } = this.props;
    onFormChanged({ ...this.result });
  }

  urlToBase64 = (file, sizeThreshold) => new Promise((resolve, reject) => {
    const image = new Image();
    image.onload = () => {
      const canvas = document.createElement('canvas');
      let canvasWidth = image.width;
      let canvasHeight = image.height;
      if (file.size > sizeThreshold) {
        // 大于阈值的压缩
        const ratio = Math.sqrt(sizeThreshold / file.size * 1.0);
        canvasWidth = image.width * ratio;
        canvasHeight = image.height * ratio;
      }
      // 将图片插入画布并开始绘制
      canvas.width = canvasWidth;
      canvas.height = canvasHeight;
      canvas.getContext('2d').drawImage(image, 0, 0, canvasWidth, canvasHeight);
      const result = canvas.toDataURL(file.type);
      resolve(result);
    };
    image.src = file.path;
    // 图片加载失败的错误处理
    image.onerror = () => {
      reject(new Error('urlToBase64 error'));
    };
  });

  onAccountChange(val) {
    this.setState({ account: val });
    this.result.account = val;
    const { onFormChanged } = this.props;
    onFormChanged({ ...this.result });
  }

  onAmountChange(val) {
    if (!val) return;
    this.setState({ transferAmount: Number(val).toFixed(2) });
    this.result.amount = Number(val).toFixed(2);
    const { onFormChanged } = this.props;
    onFormChanged({ ...this.result });
  }

  // 转账凭证
  onVoucherNoChange(val) {
    this.setState({ voucherNo: val });
    this.result.voucherNo = val;
    const { onFormChanged } = this.props;
    onFormChanged({ ...this.result });
  }

  onDateChange(event) {
    const { value } = event.detail;
    if (!value) return;
    const [displayDate, formedValue] = Util.dateFormatter(value, ['chinese', 'yyyymmdd']);
    this.setState({ displayDate });
    this.result.date = formedValue;
    const { onFormChanged } = this.props;
    onFormChanged({ ...this.result });
  }

  onBankSelect(bank) {
    const { bankName, bankCode } = bank;
    if (bankCode === 'noBank') {
      this.setState({
        showBankList: false,
        inputBank: true
      });
      return;
    }
    let { displayBank } = this.state;
    const { onFormChanged } = this.props;
    if (displayBank !== bankName) {
      this.result.bankOrg = bankName;
      displayBank = bankName;
      onFormChanged({ ...this.result });
    }
    this.setState({
      showBankList: false,
      displayBank,
    });
  }

  onBankChange(val) {
    const { inputBank } = this.state;
    if (inputBank) {
      let { displayBank } = this.state;
      const { onFormChanged } = this.props;
      if (displayBank !== val) {
        this.result.bankOrg = val;
        displayBank = val;
        onFormChanged({ ...this.result });
      }
      this.setState({
        displayBank,
      });
    } else {
      this.setState({ showBankList: true });
    }
  }

  clickBankInput() {
    const { inputBank } = this.state;
    this.setState({ showBankList: !inputBank });
  }

  payWaysChange(event) {
    this.setState({
      payWaysIndex: event.detail.value,
      showModal: Boolean(event.detail.value)
    });
    this.result.payWaysIndex = event.detail.value;
    const { onFormChanged } = this.props;
    onFormChanged({ ...this.result });
  }

  get modalContent() {
    return (
      <MUView>
        <MUView className="tipModal-text">1、若您是通过手机银行/网银/银行柜台等方式从您的银行卡/账户进行的转账还款，付款方式请选择“银行卡转账”；</MUView>
        <MUView className="tipModal-text">2、若您是通过银行柜台直接使用现金进行的汇款，付款方式请选择“现金汇款”，现金汇款申诉预计1-3个工作日进行处理，请您耐心等待，谢谢!</MUView>
      </MUView>
    );
  }

  render() {
    const {
      title, className, type
    } = this.props;
    const { account, transferAmount, displayDate, certUrls,
      showBankList, displayBank, payWays, payWaysIndex, voucherNo,
      showModal, inputBank } = this.state;
    const fromContent = payWaysIndex
      ? {
        dateItem: '汇款日期',
        monneyItem: '汇款金额',
        title: ''
      }
      : {
        dateItem: '转账日期',
        monneyItem: '转账金额',
        title
      };

    return (
      <MUView className={className ? `cert-form ${className}` : 'cert-form'}>
        <MUForm title={fromContent.title} className="form">
          <MUPicker mode="selector" className={type ? 'pay-mode unfirst-WayPicker' : 'pay-mode'} disabled={Boolean(type)} beaconId="payWaysPicker" range={payWays} onChange={(event) => this.payWaysChange(event)}>
            <MUView className="pay-mode-item">
              <MUInput
                className="pay-mode-item-title"
                title="付款方式"
                type="text"
                postfixIcon="chevron-right"
                beaconId="TransferDate"
                postfixIconSize="16"
                value={payWays[payWaysIndex]}
                color="#CACACA"
                editable={false}
                border={false}
              />
              <MUIcon className="pay-mode-item-arrow" value="arrow-right" size={15} />
            </MUView>
          </MUPicker>
          {
            payWaysIndex ? null
              : (
                <MUInput
                  title="付款方名称"
                  beaconId="Account"
                  placeholder="请输入付款方名称，例如: 张三"
                  value={account}
                  onChange={(val) => this.onAccountChange(val)}
                />
              )
          }
          {
            payWaysIndex ? null
              : (
                <MUInput
                  title="付款方银行"
                  type="text"
                  placeholder={`请${inputBank ? '输入' : '选择'}付款方银行`}
                  postfixIcon="chevron-right"
                  onPostFixIconClick={() => { this.setState({ showBankList: true, inputBank: false }); }}
                  beaconId="BankOrg"
                  postfixIconSize="16"
                  value={displayBank}
                  color="#CACACA"
                  onClick={() => this.clickBankInput()}
                  onChange={(val) => this.onBankChange(val)}
                  editable={inputBank}
                />
              )
          }
          <MUPicker mode="date" beaconId="DatePicker" className="date-picker" onChange={(event) => this.onDateChange(event)}>
            <MUInput
              title={fromContent.dateItem}
              type="text"
              placeholder={`请选择${fromContent.dateItem}`}
              postfixIcon="chevron-right"
              beaconId="TransferDate"
              postfixIconSize="16"
              value={displayDate}
              color="#CACACA"
              editable={false}
            />
          </MUPicker>
          <MUView className="amount-input">
            <MUInput
              title={fromContent.monneyItem}
              className="input"
              type="money"
              beaconId="TransferAmount"
              placeholder={`输入的金额需与${fromContent.monneyItem}一致`}
              value={transferAmount}
              onChange={(val) => this.onAmountChange(val)}
              enableFormative
            />
            <MUText className="unit">元</MUText>
          </MUView>
          {
            payWaysIndex
              ? (
                <MUInput
                  title="凭证号"
                  beaconId="voucherNo"
                  placeholder="请输入您的汇款凭证号/交易号"
                  value={voucherNo}
                  onChange={(val) => this.onVoucherNoChange(val)}
                />
              ) : null
          }
          <MUView className="cert-block">
            <MUText className="cert-title">上传凭证</MUText>
            <MUText className="cert-sub-title">请上传转账回执或记录</MUText>
            <MUImagePicker
              className="picker"
              beaconId="ImgPicker"
              files={certUrls}
              eventType={(isMuapp() || isHWAtomic()) ? '13D' : undefined} // 鸿蒙APP元服务需走Madp.chooseimage, Taro.chooseimage不支持
              count={1}
              onChange={(files) => this.onImgPicked(files)}
            />
          </MUView>
        </MUForm>
        <MUActionSheet
          cancelText="取消"
          isOpened={showBankList}
          beaconId="BankSheet"
          onClose={() => this.setState({ showBankList: false })}
        >
          {this.bankOrgList.map((bank) => (
            <MUActionSheetItem onClick={() => this.onBankSelect(bank)} beaconId={`BankSelect${bank.bankCode}`}>
              {bank.bankName}
            </MUActionSheetItem>
          ))}
        </MUActionSheet>
        <MUModal
          type="tip"
          beaconId="tipModal"
          isOpened={showModal}
          title="温馨提示"
          content={this.modalContent}
          confirmText="我知道了"
          onConfirm={() => { this.setState({ showModal: false }); }}
        />
      </MUView>
    );
  }
}
