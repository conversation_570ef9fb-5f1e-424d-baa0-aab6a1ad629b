import {
  useCallback
} from '@tarojs/taro';
import Madp from '@mu/madp';
import { getStore, setStore } from '@api/store';
import Util from '@utils/maxin-util';
import Dispatch from '@api/actions';
import {
  MUDialog,
  MUButton,
  MUText,
  MUView,
} from '@mu/zui';

/**
 * 由于样式问题，将协议的补签弹窗从原组件中提出
 * 在上线前一天才发现快捷还款这个弹窗没提出来，暂时将快捷还款的补签弹窗改成这个
 */
export default function RepayContractModal({
  showContractMUModal,
  onModalClose,
  onModalConfirm,
  capitalConstractNum,
  contractInfos
}) {
  const selectedCard = getStore('selectedCard');
  const contractApplyList = getStore('contractApplyList') || [];

  // 点击预览协议
  const onContractClick = useCallback(async ({
    url, text, formData
  } = {}, onClickContractInfo) => {
    const bankCard = getStore('selectedCard');
    if (url) {
      // 点协议的时候存储一下当前选择的卡
      bankCard && bankCard.bankCardId && setStore({ shouldReselectCard: '1' });
      Madp.navigateTo({ url: `/pages/web-view/index?pageUrl=${encodeURIComponent(url)}` });
      return;
    }
    const { dateFormat } = Util.getCurrentDateTimeInFormat();
    let contractParam = {
      contractType: contractApplyList.some((item) => item.contractType === 'PAY_DEDUCT') ? 'PAY_DEDUCT' : 'PAYWITHHOLD',
      // bankName: bankCard.bankName,
      // accountName: bankCard.bankCustName,
      // accountNo: bankCard.bankCardNoMask,
      // name: maskRealName,
      // certId: maskIdNo,
      // yearNow: date.getFullYear(),
      // monthNow: date.getMonth() + 1,
      // dayNow: date.getDate(),
      // 合同3.0参数
      interfaceVersion: '3.0',
      contractCategory: onClickContractInfo.contractType, // 新合同类型
      contractCode: onClickContractInfo.contractCode, // 合同模板编码
      bankCardInfo: {
        accountName: bankCard.bankCustName, // 账户名
        bankName: bankCard.bankName, // 开户银行名称
        accountNo: bankCard.bankCardNoMask, // 账号
      },
      // 基础合同信息
      baseContractInfo: {
        signDate: dateFormat // 签署时间-yyyyMMdd
      },
    };
    // 0：100 补签资方
    if (capitalConstractNum) {
      const { merchantInfo = {} } = await Dispatch.repayment.queryMerchantInfo({
        queryMerchantNo: capitalConstractNum
      });
      const partnerInfoList = [{// 机构信息
        partnerId: capitalConstractNum,
        partner: merchantInfo.merchantName,
        partnerType: '02'
      }];
      contractParam.contractType = 'WITHHOLDUN';
      contractParam.baseContractInfo.partnerInfoList = partnerInfoList;
      //  contractParam = {
      //   contractType: 'WITHHOLDUN',
      //   name: maskRealName,
      //   certId: maskIdNo,
      //   partnerId: capitalConstractNum,
      //   partner: merchantInfo.merchantName,
      //   accountName: bankCard.bankCustName,
      //   bankName: bankCard.bankName,
      //   accountNo: bankCard.bankCardNoMask,
      //   yearNow: date.getFullYear(),
      //   monthNow: date.getMonth() + 1,
      //   dayNow: date.getDate(),
      // };
    }
    // 银行卡授权协议书
    if (onClickContractInfo.contractType === 'YHKXXSQ_BZ') {
      contractParam.contractType = contractApplyList.some((item) => item.contractType === 'BANK_CARD_AUTH') ? 'BANK_CARD_AUTH' : 'BANKCARDAUTH';
    }
    // contractApplyList作用？
    contractApplyList.forEach((item) => {
      if (item.contractType === contractParam.contractType) {
        contractParam = { ...contractParam, ...item, contractCategory: item.contractType };
      }
    });
    console.log(contractParam, 'contractParam');
    if (formData) return contractParam;
    // 跳出页面前存储下
    bankCard && bankCard.bankCardId && setStore({ shouldReselectCard: '1' });
    Util.viewContract(contractParam);
  }, [capitalConstractNum]);

  const contentText = (<MUView>
    <MUText>{`您尾号(${selectedCard.bankCardNoMask && selectedCard.bankCardNoMask.slice(-4)})的储蓄卡的扣款协议已失效，请阅读并同意`}</MUText>
    {/* 《银行卡信息授权_标准》 */}
    {!capitalConstractNum && <MUText
      onClick={() => onContractClick({}, contractInfos.YHKXXSQ_BZ)}
      beaconId="MUModalContract"
      className="brand-text"
    >
      {contractInfos.YHKXXSQ_BZ && contractInfos.YHKXXSQ_BZ.contractName}
    </MUText>}
    {/* 添加0：100合同包配置《客户委托支付扣款_标准》、《客户委托支付扣款_联合贷0比100》 */}
    <MUText
      onClick={() => onContractClick({}, capitalConstractNum ? contractInfos.KHWTZFKK_LHD0B100 : contractInfos.KHWTZFKK_BZ)}
      beaconId="MUModalContract"
      className="brand-text"
    >
      {capitalConstractNum
        ? contractInfos.KHWTZFKK_LHD0B100 && contractInfos.KHWTZFKK_LHD0B100.contractName
        : `、${contractInfos.KHWTZFKK_BZ && contractInfos.KHWTZFKK_BZ.contractName}`}
    </MUText>
  </MUView>);

  return (
    <MUView className="repayErrorDailog">
      <MUDialog isOpened={showContractMUModal}>
        <MUView className="repayErrorDailog-title">温馨提示</MUView>
        <MUView className="repayErrorDailog-content">
          {contentText}
        </MUView>
        <MUButton className="repayErrorDailog-confirmText" onClick={onModalConfirm}>
          同意协议，去还款
        </MUButton>
        <MUView className="repayErrorDailog-closeDailogText" onClick={onModalClose}>
          暂不同意
        </MUView>
      </MUDialog>
    </MUView>
  );
}