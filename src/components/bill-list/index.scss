@import '../../components/weapp/index.scss';

.bill-list-comp {
  padding: 20px 0 244px;
  background-color: #F3F3F3;

  // position: relative;
  .divider {
    height: 20px;
    background-color: #f3f3f3;
  }

  .icon-modal-content {
    text-align: left;
    padding: 30px;
    padding-top: 0;
    font-size: 32px;
    color: #333333;
    line-height: 1.6;
  }

  .icon-modal-btn {
    height: 100px;
    line-height: 100px;
    text-align: center;
    color: #3477FF;
    font-size: 36px;
    border-top: 1PX solid #E5E5E5;
    cursor: pointer;
  }
}

@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .bill-list-comp {
    padding-bottom: calc(244px + constant(safe-area-inset-bottom));
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .bill-list-comp {
    padding-bottom: calc(244px + env(safe-area-inset-bottom));
  }
}

.at-checkbox__option--disabled {
  .debt-top-container {
    background-image: url('https://file.mucfc.com/ebn/3/18/2023010/202310121438284a6b5a.png');
    background-repeat: no-repeat;
    background-size: 140px;
    background-position-x: 61%;
  }

  .at-checkbox__option-cnt {
    opacity: 0.6 !important;
  }
}

.main-text-block {
  display: flex;
  align-items: center;

  .over-due-label {
    margin-left: 10px;
    font-size: 24px;
    color: #FFF;
    padding: 5px 12px;
    border-radius: 5px;
    background-color: #FE5A5F;
  }

  .main-text-info {
    margin-left: 10px;
    margin-top: -5px;
  }
}

.type-text-block {
  text-align: right;

  &-main {
    margin-top: 35px;
    font-size: 32px;
    line-height: 1.5;
  }

  &-sub {
    font-size: 24px;
    color: #CACACA;
    line-height: 1.5;

    &-text {
      color: #A6A6A6;
    }
  }
}

.repay-section-desc {
  margin-top: 20px;
  height: 60px;
  font-size: 26px;
  line-height: 60px;
  text-align: center;
  color: #3477FF;
  background-color: #E1EBFF;
}

.repay-in-other-channel-tips {
  margin-top: 20px;
  position: relative;
  width: 100%;
  color: #3477FF;
  background-color: #E1EBFF;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;

  &-a {
    font-size: 26px;
  }

  &-b {
    height: 60px;
    line-height: 60px;
    margin-left: 5px;
    font-size: 22px;
  }
}

.repay-cannot-extend-bill {
  padding-left: 30px;

  &-text {
    font-weight: 400;
    font-size: 26px;
    color: #808080;
    line-height: 26px;
  }
}

.repay-sum {
  // z-index: 1;
  position: fixed;
  border-top: 1px solid #f8f8f8;
  border-bottom: none;
  bottom: 0;
  height: 120px;
  width: 100%;
  background-color: #FFF;
  display: flex;
  justify-content: space-between;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  &-checker {
    height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 14vw;

    // .mu-radio__icon {
    //   height: 40px;
    //   width: 40px;
    //   border-color: #3477FF;
    // }
    // .mu-icon-checked {
    //   height: 48px;
    //   width: 48px;
    //   margin-left: -4px;
    //   margin-bottom: -2px;
    //   font-size: 48px;
    //   border-color: #3477FF;
    // }
    // .mu-radio__option-group {
    //   width: 60px;
    // }
    .check-img {
      width: 40px;
      height: 40px;
    }
  }

  &-status {
    display: flex;
  }

  &-counter {
    display: flex;
    align-items: center;
    color: black;
    height: 100%;
    font-size: 29px;

    // margin-left: 10px;
    &-num-heighling {
      color: #FE5A5F;
      font-weight: bold;
    }

    &-desc {
      font-size: 24px;
      color: #A6A6A6;
    }
  }

  &-next-step {
    margin-top: 20px;
    margin-right: 20px;
    border-radius: 40px;
    height: 80px;
    width: 180px;
    text-align: center;
    line-height: 80px;
    font-size: 32px;
    font-weight: 600;
    color: #FFF;
    background: $color-brand;

    // background: linear-gradient(90deg, #3477FF 10%, #0459FA 90%);
    &.disabled {
      opacity: 0.3;
    }
  }

  &-next-step-extend {
    height: 120px;
    width: 200px;
    text-align: center;
    line-height: 120px;
    font-size: 34px;
    color: #FFF;
    background: $color-brand;

    &.disabled {
      opacity: 0.3;
    }
  }

  &-origin-sum {
    margin-left: 8px;
    font-size: 24px;
    color: #808080;
  }
}

.repay-sum-full-btn-bg {
  z-index: 1;
  position: fixed;
  border-top: 1px solid #f8f8f8;
  border-bottom: none;
  bottom: 0;
  height: 120px;
  line-height: 120px;
  width: 100%;
  color: #FFF;
  background: #FFFFFF;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .repay-sum-full-btn {
    margin: 16px 30px;
    width: calc(100% - 60px);
    height: 88px;
    line-height: 88px;
    font-size: 36px;
    font-weight: 600;
    color: #FFF;
    opacity: 0.3;
    text-align: center;
    border-radius: 8px;
  }
  
  .visibled {
    opacity: 1;
  }
}

.repay-extend-bill-title {
  font-size: 26px;
  height: 70px;
  line-height: 70px;
  font-weight: 400;
  color: #808080;
  padding-left: 30px;
  padding-right: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .repay-cannot-extend-bill-text {
    display: flex;
    align-items: center;

    .repay-cannot-extend-tips-icon {
      margin-left: 10px;
      margin-top: 5px;
    }
  }

  .repay-cannot-extend-bill-icon {
    color: #CACACA;
  }

}

.repay-extend-bill-checker {
  display: flex;
  align-items: center;

  .check-img {
    display: block;
    margin-left: 12px;
    width: 34px;
    height: 34px;
  }
}
