/* eslint-disable no-nested-ternary */
/* eslint-disable react/forbid-prop-types */
/* eslint-disable no-plusplus */
/* eslint-disable taro/manipulate-jsx-as-array */
/* eslint-disable object-curly-newline */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-param-reassign */
/* eslint-disable react/sort-comp */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUText, MUImage, MUModal, MUIcon
} from '@mu/zui';
import PropTypes from 'prop-types';
import Madp from '@mu/madp';
import { debounce } from '@mu/madp-utils';
import ListItem from '@components/list-item';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import { setStore } from '@api/store';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

const themeColor = Util.getThemeColor(channelConfig.theme);
const checkCant = 'https://file.mucfc.com/zlh/3/0/202305/2023051820182812ffef.png';
const checkNo = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828a71df6.png';
const checkYes = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828e2a677.png';
const redCheckYes = 'https://file.mucfc.com/zlh/3/0/202305/20230518202125495a8a.png';

export default class BillList extends Component {
  static propTypes = {
    selectedBillList: PropTypes.array,
    billList: PropTypes.array,
    readOnlyBillList: PropTypes.array,
    billType: PropTypes.string,
    onSelect: PropTypes.func,
    selectAll: PropTypes.func,
    cancelAll: PropTypes.func,
    submitRepayment: PropTypes.func,
    showOtherChannelRepayTips: PropTypes.bool,
    showCannotExtendBillText: PropTypes.bool,
    canAllSelect: PropTypes.bool, // 为什么加多一个字段判断，因为这个组件其他地方也用到了，而我不想改其他的
    canSelectLength: PropTypes.number,
    disabledSelect: PropTypes.bool,
    disabledBtn: PropTypes.bool,
    btnContext: PropTypes.string,
    isDueTagCust: PropTypes.bool,
    needConcat: PropTypes.bool, // 需要拼接数组，配合sectionTitleList使用，在分组上面显示标题
    sectionTitleList: PropTypes.array,
    earliestRepayDate: PropTypes.string, // 最早可还款日期
    showCreditProductInfo: PropTypes.string, // 是否展示诚信保护期信息
    showCreditProductFlag: PropTypes.string, // 是否展示诚信保护期角标
    showSectionRateFlag: PropTypes.string, // 是否展示分段利率提示
    isBillExtend: PropTypes.bool, // 延期还款页标志
    checkedIcon: PropTypes.bool, // 是否有可选中借据的按钮
  }

  static defaultProps = {
    selectedBillList: [],
    billList: [],
    readOnlyBillList: [],
    billType: 'total',
    onSelect: () => { },
    selectAll: () => { },
    cancelAll: () => { },
    submitRepayment: () => { },
    showOtherChannelRepayTips: true,
    showCannotExtendBillText: false,
    canAllSelect: true,
    canSelectLength: 0,
    disabledSelect: false,
    disabledBtn: false,
    btnContext: '',
    isDueTagCust: false,
    needConcat: false,
    sectionTitleList: [],
    earliestRepayDate: '',
    showCreditProductInfo: '',
    showCreditProductFlag: '',
    showSectionRateFlag: '',
    isBillExtend: false,
    checkedIcon: true,
  }

  constructor(props) {
    super(props);
    this.state = {
      openIconModal: false,
      showCannotExtendBillList: false
    };
    this.doSubmitRepay = debounce((btn) => {
      this.submit(btn);
    }, 1000, {
      leading: true,
      trailing: false
    });
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    "styleIsolation" : "shared"
  }
  componentWillReceiveProps(nextProps) {
    // 虽然写了，但暂时没用
    const { isDueTagCust } = nextProps;
    if (isDueTagCust) {
      // 若为打标用户，则跳转到首页
      Util.router.replace('/pages/index/index');
    }
  }

  componentDidMount() {
    // 虽然写了，但暂时没用
    const { isDueTagCust } = this.props;
    if (isDueTagCust) {
      // 若为打标用户，则跳转到首页
      Util.router.replace('/pages/index/index');
    }
  }

  onItemSelect = debounce((item) => {
    const { billType } = this.props;
    if (billType === 'extend') return; // 延期还款仅展示 无需点击
    const { onSelect, disabledSelect, earliestRepayDate } = this.props;
    if (disabledSelect) return earliestRepayDate && Util.toast(`${earliestRepayDate}后才可还款哦`);
    onSelect(item);
  }, 400, { leading: true, trailing: false })


  infoClick = (item) => {
    const {
      billType
    } = this.props;
    setStore({
      currentBillDetail: item
    });
    Util.router.push({
      path: '/pages/repay-detail/index',
      query: {
        billType
      }
    });
  };

  repaySelectClicked = debounce(() => {
    // eslint-disable-next-line object-curly-newline
    const { selectAll, cancelAll } = this.props;
    if (this.sumCheckerStatus) {
      // 当前全选了，再点取消所有选中
      cancelAll();
    } else {
      // 当前没全选，再点全选
      selectAll();
      const { billType } = this.props;
      if (billType === 'stages') {
        Util.toast('已为您勾选可分期账单（不可分期账单未被选中）');
      }
    }
  }, 400, { leading: true, trailing: false });

  submit(disabled = false) {
    if (disabled) return;
    const {
      selectedBillList,
      submitRepayment,
      billType,
    } = this.props;
    if (typeof submitRepayment === 'function') {
      submitRepayment({
        amount: this.totalRepayDetail.totalRepaySum,
        stages: selectedBillList.length,
        billType,
      });
    }
  }

  get nextBtnContent() {
    const { billType, btnContext } = this.props;
    if (btnContext) return btnContext;
    if (billType === 'stages') {
      return '确认';
    } else if (billType === 'adjust') {
      return '申请调整';
    } else if (billType === 'reserve-near') {
      return '立即预约';
    } else if (billType === 'reserve-total') {
      return '立即预约';
    } else if (billType === 'bargaining') {
      return '去还价';
    }
    return '去还款';
  }

  /**
   *  还款合计金额
   */
  get totalRepayDetail() {
    const { selectedBillList } = this.props;
    let originRepaySum = 0;
    let totalRepaySum = 0;
    let waiveSum = 0;
    let [totalPrincipal, totalInterest] = [0, 0];
    selectedBillList.forEach((bill) => {
      originRepaySum += +bill.surplusPayPrincipalAmt || Number('0.00'); // 选中账单的总本金
      totalRepaySum += +bill.surplusPayTotalAmt || +bill.shouldReturnAmt || Number('0.00');
      const waiveTotal = Number(bill.waivePayInteAmt || '0.00') + Number(bill.waivePayFineAmt || '0.00')
        + Number(bill.waivePayPeriodFee || '0.00') + Number(bill.waivePayOnetimeFee || '0.00')
        + Number(bill.waivePayPrepayFee || '0.00');
      waiveSum += waiveTotal;
      totalPrincipal += Number(bill.installTotalAmt || '0.00'); // 一期账单总本金
      totalInterest += Number(bill.surplusPayInteAmt || '0.00'); // 一期账单总利息
    });
    return {
      originRepaySum: originRepaySum.toFixed(2),
      totalRepaySum: totalRepaySum.toFixed(2),
      waiveSum: waiveSum.toFixed(2),
      totalPrincipal: totalPrincipal.toFixed(2),
      totalInterest: totalInterest.toFixed(2),
    };
  }

  get sumCheckerStatus() {
    const { selectedBillList, canAllSelect, canSelectLength } = this.props;
    if (canAllSelect) {
      if (!selectedBillList.length && !this.concatBillList.length) return false;
      return selectedBillList.length === this.concatBillList.length;
    } else {
      if (!selectedBillList.length && !canSelectLength) return false;
      return selectedBillList.length === canSelectLength;
    }
  }

  get concatBillList() {
    const { billList, needConcat } = this.props;
    let list = billList;
    if (needConcat && billList instanceof Array) {
      list = [].concat(...billList);
    }
    return list;
  }

  // 判断借据是否全部是招联的借据，如果是的，则不展示“来自XXXX”
  get isAllZL() {
    let isAllZL = true;
    const zlMerchantNo = ['10000', '10001'];
    const { billList } = this.props;
    const flattedBillList = (billList || []).flat(2);
    flattedBillList.forEach((item) => {
      if (zlMerchantNo.indexOf(item.merchantNo) <= -1) {
        isAllZL = false;
      }
    });
    return isAllZL;
  }

  // 延后还（延期还款）按钮返回
  backHandle = () => {
    Madp.navigateBack();
  }

  render() {
    const {
      selectedBillList,
      readOnlyBillList,
      billType,
      showOtherChannelRepayTips,
      showCannotExtendBillText,
      disabledSelect,
      disabledBtn,
      billList, sectionTitleList, needConcat,
      earliestRepayDate,
      showCreditProductInfo,
      showCreditProductFlag,
      showSectionRateFlag,
      isBillExtend,
      checkedIcon,
    } = this.props;
    const { openIconModal, showCannotExtendBillList } = this.state;
    const isBtnDisabled = disabledBtn || !selectedBillList.length;
    const totalRepayNum = selectedBillList.length;
    const { totalRepaySum, waiveSum, totalPrincipal, totalInterest, originRepaySum } = this.totalRepayDetail;
    // const billNewList = billList && billList.map((list) => list.filter((item, index) => list.findIndex((e) => (e.orderNo === item.orderNo)) === index))
    let sumCounterDesc = `共${totalRepayNum}笔`;
    if (Number(waiveSum)) sumCounterDesc = `${sumCounterDesc}(已减免${waiveSum}元)`;
    let showCannotExtendBillListFlag = true;
    if (isBillExtend) {
      showCannotExtendBillListFlag = showCannotExtendBillList;
    }
    return (
      <MUView className="bill-list-comp" beaconId="billListComponent">
        {
          needConcat ? (
            billList.map((Abill, i) => (
              <MUView>
                {
                  sectionTitleList[i] && Abill.length
                    ? (
                      <MUView className="repay-section-desc" style={themeColor === '#E60027' ? 'color: #E60027;background-color: rgba(230, 0, 39, 0.15);' : ''}>
                        <MUText className="desc-context">{sectionTitleList[i]}</MUText>
                      </MUView>
                    ) : null
                }
                {
                  Abill.map((bill) => (
                    <ListItem
                      item={bill || {}}
                      billType={billType}
                      infoClick={(item) => this.infoClick(item)}
                      onItemCheck={(item) => this.onItemSelect(item)}
                      disabled={bill.canPayFlag === 'N' || disabledSelect}
                      checked={selectedBillList.indexOf(bill) > -1}
                      isAllZL={this.isAllZL}
                      showCreditProductInfo={showCreditProductInfo}
                      showCreditProductFlag={showCreditProductFlag}
                      showSectionRateFlag={showSectionRateFlag}
                      checkedIcon={checkedIcon}
                    />
                  ))
                }
              </MUView>
            ))
          )
            : (
              <MUView>
                {
                  isBillExtend && (
                    <MUView className="repay-extend-bill-title">
                      <MUView className="repay-extend-bill-title-text">
                        请选择办理借据
                      </MUView>
                      <MUView className="repay-extend-bill-checker" beaconId="repaySumChecker" onClick={this.repaySelectClicked}>
                        全选
                        <MUImage
                          src={this.sumCheckerStatus ? (disabledSelect ? checkCant : (themeColor === '#E60027' ? redCheckYes : checkYes)) : checkNo}
                          className="check-img"
                        />
                      </MUView>
                    </MUView>
                  )
                }
                {
                  billList.map((bill) => (
                    <ListItem
                      item={bill || {}}
                      billType={billType}
                      infoClick={(item) => this.infoClick(item)}
                      onItemCheck={(item) => this.onItemSelect(item)}
                      disabled={bill.canPayFlag === 'N' || disabledSelect}
                      checked={selectedBillList.indexOf(bill) > -1}
                      isAllZL={this.isAllZL}
                      showCreditProductInfo={showCreditProductInfo}
                      showCreditProductFlag={showCreditProductFlag}
                      showSectionRateFlag={showSectionRateFlag}
                      checkedIcon={checkedIcon}
                    />
                  ))
                }
              </MUView>
            )
        }
        {
          readOnlyBillList.length ? (
            <MUView className="repay-in-other-channel">
              {showOtherChannelRepayTips ? (
                <MUView className="repay-in-other-channel-tips" style={themeColor === '#E60027' ? 'color: #E60027;background-color: rgba(230, 0, 39, 0.15);' : ''} beaconId="readOnlyBillTip">
                  <MUText className="repay-in-other-channel-tips-a">以下请到借款渠道还款</MUText>
                  <MUText className="repay-in-other-channel-tips-b">(账单是否还清，以借款渠道为准)</MUText>
                </MUView>
              ) : null}
              {showCannotExtendBillText ? (
                <MUView className="repay-extend-bill-title" beaconId="cannotExtendBillText">
                  <MUView className="repay-cannot-extend-bill-text">
                    不可延期借据(共{readOnlyBillList.length}笔)
                    <MUView onClick={() => this.setState({ openIconModal: true })} beaconId="clickIcon">
                      <MUIcon className="repay-cannot-extend-tips-icon" value="info" size="15" />
                    </MUView>
                  </MUView>
                  <MUView beaconId="showCannotExtendBill" onClick={() => { this.setState({ showCannotExtendBillList: !showCannotExtendBillList }); }}>
                    <MUView>
                      {showCannotExtendBillList ? '收起' : '展开'}
                      <MUIcon className={`repay-cannot-extend-bill-icon ${showCannotExtendBillList ? 'mu-icon-arrow-up' : 'mu-icon-arrow-down'}`} size="13" />
                    </MUView>
                  </MUView>
                </MUView>
              ) : null}
              {showCannotExtendBillListFlag && readOnlyBillList.map((bill) => (
                <ListItem item={bill} billType={billType} infoClick={(item) => this.infoClick(item)} checked disabled readOnly isAllZL={this.isAllZL} checkedIcon={checkedIcon} />
              ))}
            </MUView>
          ) : null
        }
        <MUView className="bill-list-children">
          {/* eslint-disable-next-line react/prop-types */}
          {this.props.children}
        </MUView>
        {
          this.concatBillList.length ? (
            (!isBtnDisabled || !earliestRepayDate) ? (
              <MUView className="repay-sum" beaconId="billListSum">
                <MUView className="repay-sum-status">
                  <MUView className="repay-sum-checker" beaconId="repaySumChecker" onClick={this.repaySelectClicked}>
                    <MUImage
                      src={this.sumCheckerStatus ? (disabledSelect ? checkCant : (themeColor === '#E60027' ? redCheckYes : checkYes)) : checkNo}
                      className="check-img"
                    />
                  </MUView>
                  <MUView className="repay-sum-counter">
                    {billType === 'stages'
                      ? (
                        <MUView>
                          <MUView className="repay-sum-counter-num">
                            分期本金合计：
                            <MUText className="repay-sum-counter-num-heighling">{`${totalPrincipal}`}</MUText>
                            元
                          </MUView>
                          <MUView className="repay-sum-counter-desc">{`当期利息合计：${totalInterest}元`}</MUView>
                        </MUView>
                      ) : (
                        <MUView>
                          <MUView className="repay-sum-counter-num">
                            合计:
                            <MUText className="repay-sum-counter-num-heighling">{` ${totalRepaySum}`}</MUText>
                            元
                            <MUText className="repay-sum-origin-sum">
                              含本金
                              {originRepaySum}
                            </MUText>
                          </MUView>
                          <MUView className="repay-sum-counter-desc">{sumCounterDesc}</MUView>
                        </MUView>
                      )}
                  </MUView>
                </MUView>
                <MUView
                  className={`${isBillExtend ? 'repay-sum-next-step-extend' : 'repay-sum-next-step'} ${isBtnDisabled ? 'disabled' : ''}`}
                  style={process.env.TARO_ENV !== 'swan' ? { background: themeColor } : {}}
                  beaconId="ToSubmit"
                  onClick={() => this.doSubmitRepay(isBtnDisabled)}
                >
                  {this.nextBtnContent}
                </MUView>
              </MUView>
            ) : (
              <MUView className="repay-sum-full-btn-bg">
                {
                  billType === 'extend'
                    ? (<MUView className="repay-sum-full-btn theme-background-color visibled" onClick={this.backHandle}>返回</MUView>)
                    : (<MUView className="repay-sum-full-btn theme-background-color" style={`background: ${themeColor}`}>{earliestRepayDate}后可还</MUView>)
                }
              </MUView>
            )
          ) : null
        }
        <MUModal
          beaconId="cannotExtendBillModal"
          isOpened={openIconModal}
        >
          <MUView className="icon-modal-content">
            <MUView>借据不可办理延期的可能原因如下：</MUView>
            <MUView>1、借款逾期天数超过办理限制</MUView>
            <MUView>2、按月计息方式的借款不支持延期还款</MUView>
            <MUView>3、借款已过最后一期应还款日</MUView>
            <MUView>4、借款已办理过相关延期服务</MUView>
          </MUView>
          <MUView
            className="icon-modal-btn"
            beaconId="cannotExtendBillModalConfirm"
            onClick={() => { this.setState({ openIconModal: false }); }}
          >
            我知道了
          </MUView>
        </MUModal>
      </MUView>
    );
  }
}
