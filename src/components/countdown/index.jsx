/* eslint-disable quote-props */
import { Component } from '@tarojs/taro';
import { MUText, MUView } from '@mu/zui';
import PropTypes from 'prop-types';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

export default class CountDown extends Component {
  static propTypes = {
    time: PropTypes.number,
    isCard: PropTypes.bool,
    isShowDay: PropTypes.bool,
    isShowSencond: PropTypes.bool,
    onTimeUpClose: PropTypes.bool,
    format: PropTypes.object,
    onTimeUp: PropTypes.func
  }

  static defaultProps = {
    time: 0,
    isCard: true,
    isShowDay: false,
    isShowSencond: true,
    onTimeUpClose: false,
    format: {
      day: '天',
      hours: ':',
      minutes: ':',
      seconds: ''
    },
    onTimeUp() {}
  }

  constructor(props) {
    super(props);
    const { time = 0 } = this.props;
    this.seconds = time;
    this.state = {
      day: 0,
      hours: 0,
      minutes: 0,
      seconds: 0
    };
    this.timer = null;
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    'styleIsolation': 'shared'
  }

  componentDidMount() {
    this.setTimer();
  }

  componentWillReceiveProps(nextProps) {
    if (JSON.stringify(this.props) === JSON.stringify(nextProps)) return;
    const { time } = nextProps;
    this.seconds = time;
    this.clearTimer();
    this.setTimer();
  }

  componentWillUnmount() {
    this.clearTimer();
  }

  componentDidShow() {
    this.setTimer();
  }

  setTimer() {
    if (!this.timer) this.countdonwn();
  }
  clearTimer() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }
  countdonwn() {
    let [tempDay, tempHours, tempMinutes, tempSeconds] = [0, 0, 0, 0];
    if (this.seconds > 0) {
      tempDay = this.props.isShowDay ? Math.floor(this.seconds / 86400) : 0;
      tempHours = Math.floor(this.seconds / 3600) - tempDay * 24;
      tempMinutes = Math.floor(this.seconds / 60) - tempDay * 24 * 60 - tempHours * 60;
      tempSeconds = Math.floor(this.seconds) - tempDay * 24 * 60 * 60 - tempHours * 60 * 60 - tempMinutes * 60;
    }
    this.setState({
      day: tempDay,
      hours: tempHours,
      minutes: tempMinutes,
      seconds: tempSeconds
    });
    this.seconds--;
    if (this.seconds < 0) {
      this.clearTimer();
      this.props.onTimeUp && this.props.onTimeUp();
      return;
    }
    this.timer = setTimeout(() => {
      this.countdonwn();
    }, 1000);
  }

  formatNum(num) {
    return num <= 9 ? `0${num}` : `${num}`;
  }

  render() {
    const { time = 0, isShowDay, isShowSencond, onTimeUpClose, format } = this.props;
    const { day, hours, minutes, seconds } = this.state;
    if (onTimeUpClose && time === 0) return null;
    return (
      <MUView className="countdown">
        <MUText className="countdown__title">限时剩余</MUText>
        {
          isShowDay && format.day && (
            <MUView className="countdown__item">
              <MUView className="countdown__item--number">{day.toString()}</MUView>{format.day}
            </MUView>
          )
        }
        <MUView className="countdown__item">
          <MUView className="countdown__item--number">{this.formatNum(hours)}</MUView>{format.hours}
        </MUView>
        <MUView className="countdown__item">
          <MUView className="countdown__item--number">{this.formatNum(minutes)}</MUView>{format.minutes}
        </MUView>
        {
          isShowSencond && (
            <MUView className="countdown__item">
              <MUView className="countdown__item--number">{this.formatNum(seconds)}</MUView>{format.seconds}
            </MUView>
          )
        }
      </MUView>
    );
  }
}
