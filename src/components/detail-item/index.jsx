/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-one-expression-per-line */
import Madp from '@mu/madp';
import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import {
  MUView,
  MUText
} from '@mu/zui';
import { EventTypes, dispatchTrackEvent, track } from '@mu/madp-track';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import RepayModal from '@components/repay-modal/index';
// import '@components/repay-modal/index.scss';

const themeColor = Util.getThemeColor(channelConfig.theme);

export default class Sum extends Component {
  static propTypes = {
    title: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    value: PropTypes.oneOfType([PropTypes.array, PropTypes.string]),
    plan: PropTypes.oneOfType([PropTypes.object, PropTypes.bool]),
    overdue: PropTypes.oneOfType([PropTypes.number, PropTypes.bool]),
    index: PropTypes.number,
    listLength: PropTypes.number,
    loanType: PropTypes.string,
    billType: PropTypes.string,
    waiveDiscount: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    noIcon: PropTypes.bool,
    handleClick: PropTypes.func,
    showCreditProductInfo: PropTypes.string
  }

  static defaultProps = {
    title: '',
    value: '',
    plan: false,
    overdue: false,
    index: 0,
    listLength: 0,
    loanType: 'I',
    waiveDiscount: '',
    billType: '',
    noIcon: false,
    handleClick: () => { },
    showCreditProductInfo: ''
  }

  state = {
    showDetailModal: false,
  }

  onDetailClick() {
    console.log('onDetailClick');
    this.setState({ showDetailModal: true });
  }

  getTitleMsg() {
    const {
      index,
      listLength,
    } = this.props;
    return `第${index}/${listLength}期`;
  }

  handleRowClick = (e) => {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `repayment.BillListAll.modal.${this.props.beaconId}` });
    e.stopPropagation();
    const { handleClick } = this.props;
    if (handleClick) handleClick(e);
  }

  formatter(val) {
    let formattedDate = val;
    const dateString = val && val.toString();
    if (!val) {
      formattedDate = '';
    } else if (/\d{4}\.\d{2}\.\d{2}/.test(dateString)) {
      const yy = dateString.split('.')[0];
      const mm = dateString.split('.')[1];
      const dd = dateString.split('.')[2];
      formattedDate = `${yy}/${mm}/${dd}`;
      // if (Number(yy) !== (new Date().getFullYear())) {
      //   formattedDate = `${yy}/${mm}/${dd}`;
      // }
    } else if (dateString.length === 8 && (/^\d+$/).test(dateString)) {
      const yy = String(dateString).slice(0, 4);
      const mm = String(dateString).slice(4, 6);
      const dd = String(dateString).slice(6, 8);
      formattedDate = `${yy}/${mm}/${dd}`;
      // if (Number(yy) !== (new Date().getFullYear())) {
      //   formattedDate = `${yy}/${mm}/${dd}`;
      // }
    }
    return formattedDate;
  }


  render() {
    const {
      title,
      value,
      plan,
      overdue,
      loanType,
      waiveDiscount,
      billType,
      noIcon,
      showCreditProductInfo
    } = this.props;
    const {
      showDetailModal,
    } = this.state;
    const titleMsg = this.getTitleMsg();
    let datailElement = null;
    // TODO0823 逻辑验证
    if (plan && plan.planStatus === '01') {
      datailElement = <MUView />;
    } else if (plan) {
      if (plan.planStatus !== '01' && noIcon) {
        datailElement = (
          <MUView className="repay-detail-info">
            <MUView className="repay-detail-info-plan">
              <MUView>
                {overdue
                  ? <MUText className="item-label warn">逾期</MUText>
                  : <MUText className="item-label">待还</MUText>}
              </MUView>
              <MUView>
                <MUText className={overdue ? 'warn' : ''}>{value}</MUText>
              </MUView>
            </MUView>
            <MUView className="repay-detail-info-plan">
              <MUView className="date">
                <MUText className={overdue ? 'warn item-date' : 'item-date'}>{this.formatter(title)}</MUText>
                {showCreditProductInfo === 'Y' && plan.extendInteDateFlag === 'Y' && plan.inteDate ? (
                  <MUView className="detail-credit-product-info">
                    诚信保护期至
                    <MUView
                      beaconId="clickCreditDate"
                      style={`color:${themeColor}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        Madp.showModal({
                          content: '每日按正常利率计息，诚信保护期内完成还款不影响个人征信。',
                          showCancel: false,
                          confirmText: '知道了',
                          confirmColor: themeColor,
                        });
                      }}
                    >
                      {`${Util.getDateCollection(plan.inteDate).splice(1).join('月')}日`}
                    </MUView>
                  </MUView>) : null}
              </MUView>
              <MUView className={overdue ? 'detailInfo warn' : 'detailInfo'}>
                <MUText>本金{`${plan.surplusCapitalAmt}元，`}</MUText>
                <MUText>{loanType === 'I' ? '利息' : '分期手续费'} {`${loanType === 'I' ? plan.surplusInterestAmt : plan.surplusPeriodFee}元`}</MUText>
                {Number(plan.payOneTimeFeeAmt) > 0 && (<MUText>，平台服务费{`${plan.payOneTimeFeeAmt}元`}</MUText>)}
                {Number(plan.surDefaultInterestAmt) > 0 && (<MUText>，罚息{`${plan.surDefaultInterestAmt}元`}</MUText>)}
              </MUView>
            </MUView>
          </MUView>
        );
      } else if (plan.planStatus !== '01') {
        datailElement = (
          <MUView className="repay-detail-item plan">
            <MUView>
              {overdue
                ? <MUText className="item-label warn">逾期</MUText>
                : <MUText className="item-label">未还</MUText>}
              <MUText className={overdue ? 'warn item-date' : 'item-date'}>{this.formatter(title)}</MUText>
              {showCreditProductInfo === 'Y' && plan.extendInteDateFlag === 'Y' && plan.inteDate ? (
                <MUView className="detail-credit-product-info">
                  诚信保护期至
                  <MUView
                    beaconId="clickCreditDate"
                    style={`color:${themeColor}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      Madp.showModal({
                        content: '每日按正常利率计息，诚信保护期内完成还款不影响个人征信。',
                        showCancel: false,
                        confirmText: '知道了',
                        confirmColor: themeColor,
                      });
                    }}
                  >
                    {`${Util.getDateCollection(plan.inteDate).splice(1).join('月')}日`}
                  </MUView>
                </MUView>) : null}
            </MUView>
            <MUView className="item-info">
              <MUText className={overdue ? 'warn' : ''}>{value}</MUText>
              <MUText
                className="item-info-icon"
                onClick={() => this.onDetailClick()}
                beaconId="repayDetailItem"
              />
            </MUView>
            <RepayModal
              title={titleMsg.toString()}
              beaconId="RepayPlanDetail"
              isOpened={showDetailModal}
              confirmText="我知道了"
              onConfirm={() => this.setState({ showDetailModal: false })}
            >
              <MUView className="modal-content">
                <MUView className="modal-content-item">
                  <MUText>本金</MUText>
                  <MUText>{`${plan.surplusCapitalAmt}元`}</MUText>
                </MUView>
                <MUView className="modal-content-item">
                  <MUText>{loanType === 'I' ? '利息' : '分期手续费'}</MUText>
                  <MUText>{`${loanType === 'I' ? plan.surplusInterestAmt : plan.surplusPeriodFee}元`}</MUText>
                </MUView>
                {Number(plan.payOneTimeFeeAmt) > 0 && (
                  <MUView className="modal-content-item">
                    <MUText>平台服务费</MUText>
                    <MUText>{`${plan.payOneTimeFeeAmt}元`}</MUText>
                  </MUView>
                )}
                {Number(plan.surDefaultInterestAmt) > 0 && (
                  <MUView className="modal-content-item">
                    <MUText>罚息</MUText>
                    <MUText>{`${plan.surDefaultInterestAmt}元`}</MUText>
                  </MUView>
                )}
              </MUView>
            </RepayModal>
          </MUView>
        );
      }
    } else {
      datailElement = (
        <MUView className="repay-detail-item">
          <MUText className="repay-detail-item-title">{title}</MUText>
          <MUText>
            {value}
            <MUText className="highlight">{waiveDiscount && Number(waiveDiscount) ? `(已优惠${Number(waiveDiscount).toFixed(2)}元)` : ''}</MUText>
          </MUText>
        </MUView>
      );
    }

    return <View onClick={this.handleRowClick}>{datailElement}</View>;
  }
}
