@import '../../components/weapp/index.scss';

.repay-detail-item {
  position: relative;
  display: flex;
  color: #808080;
  justify-content: space-between;
  align-items: center;

  &.plan {
    padding: 33px 70px 33px 30px;
    font-size: 32px;
    /* stylelint-disable-next-line */
    border-bottom: 1PX solid #F1F1F1;
  }

  &:first-child {
    color: #333;
  }

  .modal-content {
    &-item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      color: #333333;
      font-size: 32px;
      line-height: 2em;
    }
  }

  .warn {
    color: #FE5A5F;
  }

  .item-label {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 80px;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    text-align: center;
    margin-right: 20px;
    font-size: 28px;
    border-radius: 6px;
    /* stylelint-disable-next-line */
    border: 1PX solid #808080;

    &.warn {
      color: #FE5A5F;
      /* stylelint-disable-next-line */
      border: 1PX solid #FE5A5F;
    }

    &.sxfBorder {
      color: #577BFF;
      /* stylelint-disable-next-line */
      border: 1PX solid #577BFF;
    }
  }

  .item-date {
    margin-left: 100px;
  }

  .item-info {
    position: absolute;
    top: 50%;
    right: 68px;
    transform: translateY(-50%);
  }

  .item-info-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: inline-block;
    width: 30px;
    height: 30px;
    margin-left: 10px;
    background-image: url('../assets/img/gray_info.png');
    background-size: contain;
    background-repeat: no-repeat;
  }

  .sxfHasRepay {
    color: #577BFF;
  }

  .RepayModal-dialog {
    .title {
      font-weight: 700;
      padding: 0 0 $spacing-v-md;
      color: $mu-modal-header-text-color;
      font-size: $font-size-h1;
      line-height: $font-size-h1;
      text-align: center;
      color: #353535;
      font-size: $font-size-h2;
      line-height: 45px;
      box-sizing: border-box;
    }
  }

  .modal-title {
    font-weight: 700;
    padding: 0 0 $spacing-v-md;
    color: $mu-modal-header-text-color;
    font-size: $font-size-h1;
    line-height: $font-size-h1;
    text-align: center;
    color: #353535;
    font-size: $font-size-h2;
    line-height: 45px;
    box-sizing: border-box;
  }
}

.detail-credit-product-info {
  margin: 12px 0 0 100px;
  height: 28px;
  display: flex;
  font-size: 28px;
  line-height: 1;
  color: #808080;
}
