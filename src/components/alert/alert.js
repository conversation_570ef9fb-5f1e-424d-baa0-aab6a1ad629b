import {
  Component
} from '@tarojs/taro';
import {
  MUModal
} from '@mu/zui';

export default class InstallItro extends Component {
  constructor(props) {
    super(props);
    this.defaultState = {
      title: '温馨提示',
      content: '',
      confirmText: '确定',
      cancelText: '',
      isOpened: false,
      type: 'default',
      closeOnClickOverlay: false,
      onCancel: () => {},
      onConfirm: () => {},
    };
    this.state = {
      ...this.defaultState
    };
  }

  showAlert(opt) {
    const option = {
      ...this.defaultState,
      title: opt.title,
      content: opt.content,
      type: opt.type || 'default',
      confirmText: opt.confirmText || '确定',
      cancelText: opt.cancelText,
      isOpened: true,
      closeOnClickOverlay: false,
    };
    option.onConfirm = async () => {
      await opt.onConfirm();
      this.setState({
        isOpened: false
      });
    };
    option.onCancel = async () => {
      await opt.onCancel();
      this.setState({
        isOpened: false
      });
    };
    this.setState({
      ...option
    });
  }

  hideAlert() {
    this.setState({
      isOpened: false
    });
  }

  isShowing() {
    const {
      isOpened
    } = this.state;
    return isOpened;
  }

  render() {
    const properties = {
      ...this.state
    };
    const {
      content
    } = properties;
    if (content && typeof content === 'string') {
      // eslint-disable-next-line react/no-danger
      properties.content = (<div className="self-alert-content" dangerouslySetInnerHTML={{ __html: content }} />);
    }
    return (
      <MUModal {...properties} beaconId="commonAlert" />
    );
  }
}
