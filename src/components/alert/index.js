import Nerv from 'nervjs';
import Alert from './alert';

const createAlert = async () => {
  const div = document.createElement('div');
  document.body.appendChild(div);
  const alert = await Nerv.render(<Alert />, div);
  return {
    showAlert: (options) => alert.showAlert(options),
    hideAlert: () => alert.hideAlert(),
    isShowing: () => alert.isShowing(),
  };
};

let alert;

const showAlert = async (options) => {
  if (!options) return;
  if (!alert || alert.isShowing()) alert = await createAlert();
  let opt = {};
  if (typeof options === 'string') {
    opt = {
      content: options,
      onCancel: () => {},
      onConfirm: () => {},
    };
  } else if (options.onClose && typeof options.onClose === 'function') {
    opt = {
      title: options.title,
      type: options.type,
      content: options.content,
      confirmText: options.confirmText,
      onCancel: () => {},
      onConfirm: options.onClose,
    };
  } else {
    opt = {
      title: options.title,
      content: options.content,
      type: options.type,
      confirmText: options.confirmText,
      cancelText: options.cancelText,
      onCancel: options.onCancel || (() => {}),
      onConfirm: options.onConfirm || (() => {}),
    };
  }
  return alert.showAlert({ ...opt });
};

showAlert.close = () => {
  if (alert) {
    alert.hideAlert();
  }
};

export default showAlert;
