@import '../../components/weapp/index.scss';

.collect {
  padding-top: 20px;
  padding-bottom: 330px;
  &__explain {
    padding: 30px;
    font-size: 28px;
    line-height: 42px;
    color: #333;
    font-weight: 400;
    background-color: #fff;
    &__material {
      margin-top: 28px;
      font-size: 24px;
      line-height: 26px;
      color: #a6a6a6;
    }
  }

  &__cnt {
    margin-top: 20px;
    .item-extra__info {
      color: #333 !important;
    }
    &__reason, &__amount {
      background-color: #fff;
      .item-content__info-title {
        width: 220px;
      }
    }
    &__reason--selected, &__amount {
      .item-extra__info {
        color: #333;
      }
    }
    &__amount03 {
      .at-list__item-content {
        min-width: auto !important;
        width: auto !important;
      }
    }
    &__repayplan {
      &-details {
        position: relative;
        margin-right: 32px;
        &__title {
          height: 28px;
          font-weight: 400;
          font-size: 28px;
          text-align: right;
          line-height: 28px;
          margin-bottom: 16px;
        }
        &__subtitle {
          height: 24px;
          font-weight: 400;
          font-size: 24px;
          color: #A6A6A6;
          text-align: right;
          line-height: 24px;
        }
      }
    }
    &__plan {
      margin-top: 20px;
      padding: 32px 30px 30px;
      background-color: #fff;
      display: flex;
      .plan-title, .plan-content__period {
        height: 42px;
        font-size: 28px;
        line-height: 42px;
        color: #333;
        font-weight: 400;
      }
      .plan-content {
        flex: 1;
        text-align: right;
        &__date {
          margin-top: 10px;
          height: 26px;
          font-size: 24px;
          line-height: 26px;
          color: #a6a6a6;
          font-weight: 400;
        }
      }
    }
    &__reduce {
      .reduce-promise {
        font-size: 24px;
        line-height: 42px;
        color: #808080;
        font-weight: 400;
      }
    }
  }
  .reason-divide-area {
    width: 100%;
    height: 20px;
    background: #f3f3f3;
  }
  .reason-last-item {
    position: relative;
    &__submit {
      position: absolute;
      top: 20px;
      right: 20px;
    }
    &__text {
      background: #f3f3f3;
      .at-textarea__textarea {
        background: #f3f3f3;
      }
    }
  }
  .drawer {
    background: #fff;
    .consultRepay-explanation {
      width: 100%;
      height: 64px;
      background: #E1EBFF;
      opacity: 0.8;
      font-weight: 400;
      font-size: 26px;
      color: #3477FF;
      line-height: 64px;
      background-color: #E1EBFF;
      text-align: center;
    }
    .consultRepay-bottom-plan {
      margin-bottom: 50px;
      padding: 0 40px;
      &__content {
        padding-top: 60px;
        &__item {
          display: flex;
          font-size: 30px;
          line-height: 30px;
          color: #333;
          font-weight: 400;
          .item__date {
            width: 170px;
          }
          .item__circle {
            margin: 10px 33px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 4px solid #3477FF;
          }
          .item__line {
            margin: 0 auto;
            width: 2px;
            height: 62px;
            background-color: #cacaca;
            border-radius: 2.1px;
          }
          .item__amt {
            display: flex;
            height: 30px;
            color: #cc1f15;
            &--threshold {
              font-size: 30px;
              font-weight: 400;
              line-height: 30px;
              letter-spacing: 0;
              text-align: left;
              .unit {
                color: #333333;
              }
            }
            &--task {
              height: 30px;
              margin-left: 9px;
              padding-top: 2px;
              font-weight: 400;
              font-size: 26px;
              color: #A6A6A6;
              text-decoration: line-through;
            }
          }
          .last {
            font-weight: 400;
            font-size: 30px;
            color: #CC1F15;
            line-height: 30px;
          }
        }
      }
    }
    .bottom-plan {
      margin-bottom: 50px;
      padding: 0 30px;
      &__desc {
        font-size: 28px;
        line-height: 36px;
        color: #a6a6a6;
        font-weight: 400;
      }
      &__content {
        padding-top: 60px;
        &__item {
          display: flex;
          font-size: 28px;
          line-height: 32px;
          color: #333;
          font-weight: 400;
          .item__circle {
            margin: 10px 20px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            border: 1PX solid #3477FF;
          }
          .item__line {
            margin: 0 auto;
            width: 1PX;
            height: 60px;
            background-color: #cacaca;
          }
        }
      }
    }
  }
  &-contract-sumbit {
    width: 100%;
    position: fixed;
    left: 0;
    bottom: 0;
    background: #FFFFFF;
    .collect-contract-container {
      padding: 20px 30px 0px 30px;
      .mu-radio__option-container {
        align-items: flex-start !important;
      }
      border-bottom: 1px solid #f3f3f3;
    }
    .collect__submit {
      margin: 30px 30px 0;
      padding-bottom: calc(30px + constant(safe-area-inset-bottom));
      padding-bottom: calc(30px + env(safe-area-inset-bottom));
    }
  }

}