import { Component } from '@tarojs/taro';
import {
  MUView, MUListItem, MUActionSheet, MUActionSheetItem, MUText, MUTextarea, MUIcon, MURadio, MUButton, MUList, MUModal
} from '@mu/zui';
import pageHoc from '@utils/pageHoc';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import Madp from '@mu/madp';
import {
  Url, isMuapp, isAlipay, isWechat, isUnicom
} from '@mu/madp-utils';
import BottomDrawer from '@components/bottom-drawer';
import ChooseFiles from '@components/choose-files';
import { AgreementDrawer } from '@mu/agreement';
import Dispatch from '@api/actions';
import { platLogin } from '@utils/login';
import { getLoginInfo } from '@mu/business-basic';

import './index.scss';

const themeColor = Util.getThemeColor(channelConfig.theme);
// 协议类型
const CONTRACT_MAP = {
  NEGOTIATE_REPAY: '月最低还款承诺书',
  POSTLOAN_CHANGE: '贷后服务变更协议',
  POST_INFO_AUTH: '贷后资料授权书',
  BILL_REPAY: '账单分期还款协议',
  DELAY_REPAY: '延期还款协议',
  REPAY_DATE_CHANGE: '还款日变更协议'
};

const serviceTypeParam = {
  stopUrging: '007', // 缓催
  interestFreeTorepay: '009', // 息费减免-待还
  interestFreeRepaid: '011', // 息费减免-已还
  letOff: '008' // 免上浮罚息
};

// 服务申请状态
// const SERVICE_APPLY_STATUS = {
//   0: '初始化',
//   1: '完成（首次）待办',
//   2: '成（首次）待办，待提交审核',
//   3: '待风控结果',
//   4: '完成（风控后）补充待办',
//   5: '等待人工审核',
//   6: '审核拒绝',
//   7: '审核通过等待业务提交',
//   8: '审核通过且业务处理中',
//   9: '审核通过且业务办理成功',
//   10: '审批通过且业务办理失败'
// };

@track({ event: EventTypes.PO }, {
  pageId: 'CollectPage',
  dispatchOnMount: true,
})
@pageHoc({ title: '贷后服务关怀申请' })
export default class CollectPage extends Component {
  constructor(props) {
    super(props);
    this.param = Url.getParam('param');
    // 停上浮罚息到期日
    this.stopFloatFineRateEndDate = '';
    if (typeof this.param === 'string') {
      this.param = JSON.parse(this.param);
    }
    const {
      applyId, serviceType, todoId
    } = this.param || {};
    this.state = {
      showPage: false,
      reason: '请选择',
      showReason: false,
      otherReason: '',
      showOtherReason: false,
      channelAgreementPlanCfgDtoList: [{}],
      showAllRepaidAmountExplainModal: false,
      returnedPlanTerm: {}, // 待还计划
      scene: Url.getParam('scene') || '', // 场景
    };
    this.applyId = `${applyId}`; // 贷后信息查询流水号
    this.serviceType = serviceType; // 03：协商分期；02：息费减免；04：停催；009：息费减免-待还；011：息费减免-已还；008：免上浮罚息
    this.todoId = todoId; // 待办任务id
    this.unpaidTotalAmt = ''; // 待还总金额
    this.promiseAmt = ''; // 承诺总金额
    this.waiveAmt = ''; // 减免总金额
    this.installTotalAmt = ''; // 分期总金额
    this.principal = ''; // 本金
    this.inteFee = ''; // 利息
    this.fineFee = ''; // 罚息
    this.firstPeriodRepayDay = ''; // 第一期还款日 2022-12-7 格式，为空取当月
    this.periodRepayDay = ''; // 每月还款日
    this.periodAmt = ''; // 每期还款金额
    this.period = ''; // 总期数
    this.periodList = [];
    this.lastPeriodRepayAmt = ''; // 最后一期还款金额
    this.lastPeriodRepayDay = ''; // 最后一期还款日
    this.multipleArrearsList = []; // 多方欠款文件列表
    this.diseaseProofList = []; // 疾病证明文件列表
    this.povertyProofList = []; // 文件证明款文件列表
    this.otherMaterialList = []; // 其他材料证明文件列表
    this.needSignContractList = []; // 需要签署的协议列表
    this.periodEndYear = ''; // 协商分期结清年
    this.periodEndMonth = ''; // 协商分期结清月
    this.periodEndDay = ''; // 协商分期结清日
    this.reasonList = [
      '失业',
      '创业失败',
      '重大疾病',
      '家庭变故',
      '向多家金融机构贷款',
    ];
    this.consultReplayCase = Madp.getStorageSync('consultReplayCase', 'LOCAL') || {}; // 获取前置页面存进去的随心还案件数据
    let consultReplayCaseNeedFileList = (this.consultReplayCase.needFileList || []).map(item => item.fileType);
    /* eslint-disable-next-line max-len */
    this.needUploadMaterialList = Url.getParam('scene') === 'consultRepay' ? consultReplayCaseNeedFileList.filter((item) => item !== '999')
    /* eslint-disable-next-line max-len */
      .concat(consultReplayCaseNeedFileList.filter((item) => item === '999')) : []; // 需要上传的材料列表；100-贫困证明，101-疾病证明，102-多方欠款证明，999-其他资料
    this.contractApplyList = this.consultReplayCase.contractApplyList || []; // 需要签收的合同列表
    this.contractInfos = this.consultReplayCase.contractInfos || []; // 需要签收的合同列表3.0
    this.negotiateRepayPackageList = this.consultReplayCase.negotiateRepayPackageList || []; // 协商还套餐
    this.applySourceType = this.consultReplayCase.applySourceType || '0', // 建案类型，1-自主建案，2-人工建案
    this.applyStatus = this.consultReplayCase.applyStatus || '0', // 建案状态，1-提交审核，4-资料补充
    this.applyNo = this.consultReplayCase.applyNo || '', // 案件号
    this.installCnt = this.negotiateRepayPackageList[0]
      && this.negotiateRepayPackageList[0].installCnt || 0, // 分期期数
    this.waiveRatio = this.negotiateRepayPackageList[0]
      && this.negotiateRepayPackageList[0].waiveRatio || 0, // 减免比例
    this.repayDay = this.negotiateRepayPackageList[0]
      && this.negotiateRepayPackageList[0].repayDay || '23', // 还款日
    this.taskAmt = this.negotiateRepayPackageList[0]
    && this.negotiateRepayPackageList[0].taskAmt || '0', // 任务金额
    this.negotiateRepayTrial = []; // 还款计划
    this.suspendCollectionCollectDate = ''; // 缓催日期，yyyy-mm-dd
    this.repaymentFlag = Url.getParam('repaymentFlag') || ''; // 是否还款模块跳入的标识
    this.mobile = ''; // 掩码手机号
    this.custName = ''; // 掩码客户姓名
    this.idNo = ''; // 掩码身份证号
  }

  async componentDidMount() {
    const { loginSuccess = false, mobile, custName, idNo } = await getLoginInfo() || {};
    if (!loginSuccess) {
      platLogin();
      return;
    }
    this.mobile = mobile;
    this.custName = custName;
    this.idNo = idNo;

    const { scene } = this.state;
    // 依次为停缓催、息费减免、免上浮罚息
    if (scene === 'stopUrging'
      || scene === 'interestFreeTorepay'
      || scene === 'interestFreeRepaid'
      || scene === 'letOff') {
      switch (scene) {
        case 'stopUrging':
          this.serviceType = '04';
          break;
        case 'interestFreeTorepay':
          this.serviceType = '009';
          break;
        case 'interestFreeRepaid':
          this.serviceType = '011';
          break;
        case 'letOff':
          this.serviceType = '008';
          break;
        default: break;
      }
      await this.initStopUrgingData();
    } else if (scene === 'consultRepay') {
      // 随心还场景
      this.serviceType = '03';

      // 获取月最低还款承诺书合同需要的参数，兼容旧模式
      this.installTotalAmt = this.negotiateRepayPackageList[0]
        && this.negotiateRepayPackageList[0].taskTotalAmt || 0;
      this.principal = this.negotiateRepayPackageList[0]
        && this.negotiateRepayPackageList[0].surplusPayPrincipalAmt || 0;
      this.inteFee = this.negotiateRepayPackageList[0]
        && this.negotiateRepayPackageList[0].surplusPayInteFeeAmt || 0;
      this.fineFee = this.negotiateRepayPackageList[0]
        && this.negotiateRepayPackageList[0].surplusPayFineAmt || 0;
      this.periodAmt = this.negotiateRepayPackageList[0]
        && this.negotiateRepayPackageList[0].taskThresholdAmt || 0;
      const lastRepayDate = this.negotiateRepayPackageList[0]
        && this.negotiateRepayPackageList[0].lastRepayDate || '';
      this.lastRepayDate = lastRepayDate;
      this.periodEndYear = lastRepayDate && lastRepayDate.substring(0, 4);
      this.periodEndMonth = lastRepayDate && lastRepayDate.substring(4, 6);
      this.periodEndDay = lastRepayDate && lastRepayDate.substring(6, 8);

      this.setState({ showPage: true });
      // 随心还试算
      await this.queryNegotiateRepayTrial();
      // 合同数据处理
      this.operationContractList();
    } else {
      await this.initData();
    }
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '贷后服务关怀申请' });
  }

  /**
   * 处理合同数据，新接口兼容老场景
   */
  operationContractList = () => {
    const { channelAgreementPlanCfgDtoList } = this.state;
    if (this.contractApplyList && this.contractApplyList.length > 0) {
      // 按旧模式兼容contractName和contractCategory
      this.contractApplyList.forEach(item => {
        item.contractName = CONTRACT_MAP[item.contractType];
        item.contractCategory = item.contractType;
      });

      channelAgreementPlanCfgDtoList[0].checkBoxChecked = false;
      channelAgreementPlanCfgDtoList[0].contractList = this.contractApplyList;
      channelAgreementPlanCfgDtoList[0].contractText = '';
      channelAgreementPlanCfgDtoList[0].contractList.forEach((item) => {
        channelAgreementPlanCfgDtoList[0].contractText += `、${CONTRACT_MAP[item.contractType]}`;
      });
      channelAgreementPlanCfgDtoList[0].contractText = channelAgreementPlanCfgDtoList[0].contractText.substring(1);
      /* eslint-disable-next-line max-len */
      channelAgreementPlanCfgDtoList[0].forceReadFlag = channelAgreementPlanCfgDtoList[0].contractList.filter((filterItem) => filterItem.forceReadFlag === 'Y').length > 0; // 标记是否需要强读
      channelAgreementPlanCfgDtoList[0].show = false;
      channelAgreementPlanCfgDtoList[0].readDuration = 5;
      /* eslint-disable-next-line max-len */
      channelAgreementPlanCfgDtoList[0].forceReadProtocalList = channelAgreementPlanCfgDtoList[0].contractList.filter((filterItem) => filterItem.forceReadFlag === 'Y');
      channelAgreementPlanCfgDtoList[0].hasForceRead = false; // 标记是否有强读完成
    } else if (this.contractInfos && this.contractInfos.length > 0) { // 3.0合同
      this.contractInfos.forEach(item => {
        item.contractName = item.contractName; // 合同名称
        item.contractCategory = item.contractCode;
      });

      channelAgreementPlanCfgDtoList[0].checkBoxChecked = false;
      channelAgreementPlanCfgDtoList[0].contractList = this.contractInfos;
      channelAgreementPlanCfgDtoList[0].contractText = '';
      channelAgreementPlanCfgDtoList[0].contractList.forEach((item) => {
        channelAgreementPlanCfgDtoList[0].contractText += `、${item.contractName}`;
      });
      channelAgreementPlanCfgDtoList[0].contractText = channelAgreementPlanCfgDtoList[0].contractText.substring(1);
      /* eslint-disable-next-line max-len */
      channelAgreementPlanCfgDtoList[0].forceReadFlag = channelAgreementPlanCfgDtoList[0].contractList.filter((filterItem) => filterItem.forceReadFlag === 'Y').length > 0; // 标记是否需要强读
      channelAgreementPlanCfgDtoList[0].show = false;
      channelAgreementPlanCfgDtoList[0].readDuration = 5;
      /* eslint-disable-next-line max-len */
      channelAgreementPlanCfgDtoList[0].forceReadProtocalList = channelAgreementPlanCfgDtoList[0].contractList.filter((filterItem) => filterItem.forceReadFlag === 'Y');
      channelAgreementPlanCfgDtoList[0].hasForceRead = false; // 标记是否有强读完成
    }
    this.setState({
      showPage: true,
      channelAgreementPlanCfgDtoList
    });
  }

  initData = async () => {
    const userTodoInfo = await Dispatch.repayment.userTodoInfo({
      sceneFuction: 'QUERY_USER_TODO',
      userTodoParam: {
        todoId: this.todoId,
        serviceType: this.serviceType,
        applyId: this.applyId
      }
    });
    if (userTodoInfo && userTodoInfo.length > 0) {
      if (userTodoInfo[0].todoStatus === '3') {
        // 跳转成功结果页
        Util.router.replace('/pages/common-result/result?type=collect-success');
      } else if (userTodoInfo[0].todoStatus === '2' || userTodoInfo[0].todoStatus === '4') {
        // 已取消或已过期
        Madp.showModal({
          content: '您好，您的链接已过期，请拨打95786联系您的信用管家重新获取',
          confirmText: '我知道了',
          confirmColor: themeColor,
          showCancel: false,
          success(res) {
            if (res.confirm) {
              dispatchTrackEvent({ target: this, event: EventTypes.CK, beaconId: 'CollectEnterCancel' });
              if (process.env.TARO_ENV === 'h5') {
                if (isMuapp() || isAlipay() || isWechat() || isUnicom()) {
                  Madp.closeWebView();
                } else {
                  Madp.navigateBack({
                    delta: 1
                  });
                }
              }
            }
          }
        });
      } else if (userTodoInfo[0].todoStatus === '1') {
        // 进行中
        const { postLoanServiceData } = userTodoInfo[0] || {};
        const {
          unpaidTotalAmt, promiseAmt, waiveAmt, installTotalAmt, principal, inteFee, fineFee,
          firstPeriodRepayDay, periodRepayDay, periodAmt, period, lastPeriodRepayAmt,
          needUploadMaterialList, needSignContractList, periodEndYear, periodEndMonth, periodEndDay
        } = postLoanServiceData;
        this.unpaidTotalAmt = unpaidTotalAmt;
        this.promiseAmt = promiseAmt;
        this.waiveAmt = waiveAmt;
        this.installTotalAmt = installTotalAmt;
        this.principal = principal;
        this.inteFee = inteFee;
        this.fineFee = fineFee;
        this.firstPeriodRepayDay = firstPeriodRepayDay;
        this.getFirstPeriodRepayDay();
        this.periodRepayDay = periodRepayDay;
        this.periodAmt = periodAmt;
        this.period = period;
        for (let i = 0; i < period && Number(period);) {
          this.periodList.push(`a${i}`);
          i += 1;
        }
        this.lastPeriodRepayAmt = lastPeriodRepayAmt;
        /* eslint-disable-next-line max-len */
        this.needUploadMaterialList = needUploadMaterialList.filter((item) => item !== '199').concat(needUploadMaterialList.filter((item) => item === '199'));
        this.periodEndYear = periodEndYear;
        this.periodEndMonth = periodEndMonth;
        this.periodEndDay = periodEndDay;
        this.lastPeriodRepayDay = this.getCntPlanDate(this.firstPeriodRepayDay, Number(this.period) - 1);
        this.needSignContractList = needSignContractList;
        const { channelAgreementPlanCfgDtoList } = this.state;
        if (needSignContractList && needSignContractList.length > 0) {
          channelAgreementPlanCfgDtoList[0].checkBoxChecked = false;
          channelAgreementPlanCfgDtoList[0].contractList = needSignContractList;
          channelAgreementPlanCfgDtoList[0].contractText = '';
          channelAgreementPlanCfgDtoList[0].contractList.forEach((item) => {
            channelAgreementPlanCfgDtoList[0].contractText += `、${item.contractName ? item.contractName : '个人敏感信息授权书'}`;
          });
          channelAgreementPlanCfgDtoList[0].contractText = channelAgreementPlanCfgDtoList[0].contractText.substring(1);
          /* eslint-disable-next-line max-len */
          channelAgreementPlanCfgDtoList[0].forceReadFlag = channelAgreementPlanCfgDtoList[0].contractList.filter((filterItem) => filterItem.contractCategory === 'NEGOTIATE_REPAY').length > 0; // 标记是否需要强读
          channelAgreementPlanCfgDtoList[0].show = false;
          channelAgreementPlanCfgDtoList[0].readDuration = 5;
          /* eslint-disable-next-line max-len */
          channelAgreementPlanCfgDtoList[0].forceReadProtocalList = channelAgreementPlanCfgDtoList[0].contractList.filter((filterItem) => filterItem.contractCategory === 'NEGOTIATE_REPAY');
          channelAgreementPlanCfgDtoList[0].hasForceRead = false; // 标记是否有强读完成
        }
        this.setState({
          showPage: true,
          channelAgreementPlanCfgDtoList
        });
      }
    } else {
      // 无待办处理
      Madp.showModal({
        content: '您好，您的链接已过期，请拨打95786联系您的信用管家重新获取',
        confirmText: '我知道了',
        confirmColor: themeColor,
        showCancel: false,
        success(res) {
          if (res.confirm) {
            dispatchTrackEvent({ target: this, event: EventTypes.CK, beaconId: 'CollectEnterNone' });
            if (process.env.TARO_ENV === 'h5') {
              if (isMuapp() || isAlipay() || isWechat() || isUnicom()) {
                Madp.closeWebView();
              } else {
                Madp.navigateBack({
                  delta: 1
                });
              }
            }
          }
        }
      });
    }
  }
  /**
   * 随心还试算，获取还款计划
   */
  queryNegotiateRepayTrial = async () => {
    const { ret, data } = await Dispatch.repayment.queryNegotiateRepayTrial({
      installTermCount: this.installCnt,
      waiveRatio: this.waiveRatio,
      installType: 1,
      appointAmt: this.taskAmt,
      repayDay: this.repayDay
    });
    if (ret === '0') {
      this.negotiateRepayTrial = data.repayPlanList || [];
      this.negotiateRepayTrial.some(planTerm => {
        if (planTerm.repayStatus === '0') {
          // 获取第一期待还计划
          this.setState({
            returnedPlanTerm: planTerm
          });
          return true;
        }
        return false;
      });
    }
  }

  getExplainTop = () => {
    let explainTop = '您正在申请催收相关业务，请根据实际情况，上传相关证明材料，并提交申请，可隐藏资料中与申请无关的个人信息。招联承诺对您提供的资料和信息严格保密。';
    switch (this.serviceType) {
      // 息费减免-旧
      case '02':
        explainTop = '您好，为缓解您的资金压力，特为您申请了“息费减免”，请上传相关材料提交审核。可隐藏资料中与申请无关的个人信息。招联承诺对您提供的资料和信息严格保密。';
        break;
      case '03': /* eslint-disable-next-line max-len */
        explainTop = '您好，为缓解您的资金压力，特为您申请“协商还”，请上传相关材料提交审核（无要求材料可忽略）。可隐藏资料中与申请无关的个人信息。招联承诺对您提供的资料和信息严格保密。审批通过后请按协商方式还款，分期后未按约定还款计划还款，并且超过一个月，即恢复全额催收。';
        break;
      case '04': {
        const suspendCollectDateFormat = Util.getDateCollection(this.suspendCollectionCollectDate);
        /* eslint-disable-next-line max-len */
        const suspendCollectDate = (suspendCollectDateFormat && JSON.stringify(suspendCollectDateFormat) !== '[]') ? `${suspendCollectDateFormat[0]}年${Number(suspendCollectDateFormat[1]) >= 10 ? Number(suspendCollectDateFormat[1]) : `0${Number(suspendCollectDateFormat[1])}`}月${suspendCollectDateFormat[2]}日` : '';
        /* eslint-disable-next-line max-len */
        explainTop = `您好，您正在申请“暂缓催收”业务${suspendCollectDate && suspendCollectDate.indexOf('2099') === -1 ? `，在${suspendCollectDate}前减少催收频次` : ''}。请您根据实际情况，上传业务相关证明材料（无要求材料可忽略），并提交申请，请勿将本申请内容披露给任何第三方。您可以隐藏材料中与申请无关的个人信息，招联承诺对您提供的资料和信息严格保密。提交后将进入后台审批，签署协议和提供资料可提升审批通过的概率。`;
        break;
      }
      // 息费减免-待还/已还
      case '009':
      case '011':
        explainTop = '您好，您正在申请“息费减免”业务。请您根据实际情况，上传业务相关证明材料（无要求材料可忽略），并提交申请，请勿将本申请内容披露给任何第三方。您可以隐藏材料中与申请无关的个人信息，招联承诺对您提供的资料和信息严格保密。提交后将进入后台审批，签署协议和提供资料可提升审批通过的效率。';
        break;
      // 免上浮罚息
      case '008':
        explainTop = `您好，您正在申请“免上浮罚息”业务，在${this.stopFloatFineRateEndDate}前对办理借据逾期后免收取罚息上浮超出正常利息的部分。请您根据实际情况，上传业务相关证明材料（无要求材料可忽略），并提交申请，请勿将本申请内容披露给任何第三方。您可以隐藏材料中与申请无关的个人信息，招联承诺对您提供的资料和信息严格保密。提交后将进入后台审批，签署协议和提供资料可提升审批通过的效率。`;
        break;
      default:
        break;
    }
    return explainTop;
  }

  getFirstPeriodRepayDay = () => {
    if (!this.firstPeriodRepayDay) {
      const nowDate = new Date();
      this.firstPeriodRepayDay = `${nowDate.getFullYear()}-${nowDate.getMonth() + 1}-${this.periodRepayDay}`;
    }
  }

  getCntPlanDate = (firstDate, i) => {
    let cntPlanDate = '';
    const year = firstDate.split('-')[0];
    const month = firstDate.split('-')[1];
    const day = firstDate.split('-')[2] < 10 ? `0${firstDate.split('-')[2]}` : firstDate.split('-')[2];
    if ((Number(month) + i) % 12 !== 0) {
      cntPlanDate = `${Number(year) + Math.floor((Number(month) + i) / 12)}-${((Number(month) + i) % 12) < 10 ? `0${(Number(month) + i) % 12}` : (Number(month) + i) % 12}-${day}`;
    } else {
      cntPlanDate = `${Number(year) + Math.floor((Number(month) + i) / 12) - 1}-12-${day}`;
    }
    return cntPlanDate;
  }

  handleInput = (e) => {
    this.setState({
      otherReason: e.target.value
    });
  }

  chooseFileFinish = (type, opr, file) => {
    if (opr === 'add') {
      switch (type) {
        case '100':
        case 'C03':
          this.povertyProofList.push(file);
          break;
        case '101':
        case 'C04':
          this.diseaseProofList.push(file);
          break;
        case '102':
        case 'C05':
          this.multipleArrearsList.push(file);
          break;
        case '199':
        case '999':
          this.otherMaterialList.push(file);
          break;
        default:
          break;
      }
    } else {
      switch (type) {
        case '100':
        case 'C03':
          this.povertyProofList.splice(file, 1);
          break;
        case '101':
        case 'C04':
          this.diseaseProofList.splice(file, 1);
          break;
        case '102':
        case 'C05':
          this.multipleArrearsList.splice(file, 1);
          break;
        case '199':
        case '999':
          this.otherMaterialList.splice(file, 1);
          break;
        default:
          break;
      }
    }
  }

  handlerCheckboxClick = (i) => {
    this.setState((prevState) => prevState.channelAgreementPlanCfgDtoList.map((item, index) => {
      if (index === i) {
        // 需要强度未强读
        if (item.forceReadFlag && !item.hasForceRead) {
          this.toViewContract(i);
          Object.assign(item, { show: true });
          return;
        }
        // 不需要强读
        Object.assign(item, { checkBoxChecked: !item.checkBoxChecked });
        return;
      }
      return item;
    }), () => {
      if (Util.isH5Env()) {
        Madp.showToast({
          title: '',
          icon: 'none',
          duration: 0
        });
      }
    });
  }

  handleSubmitClick = (i, scene) => {
    this.setState((prevState) => prevState.channelAgreementPlanCfgDtoList.map((item, index) => {
      if (index === i) {
        // 完成强读确认
        if (scene === '1') {
          Object.assign(item, {
            show: false,
            checkBoxChecked: true,
            hasForceRead: true,
          });
          return;
        }
        // 未完成强读确认
        Object.assign(item, { show: false });
        return;
      }
      return item;
    }));
  }

  /**
   * 弹出协议阅读
   * @param {*} i
   */
  toViewContract = async (i) => {
    const { channelAgreementPlanCfgDtoList } = this.state;
    const previewProtocalList = channelAgreementPlanCfgDtoList[i].contractList;
    for (let index = 0; index < previewProtocalList.length; index++) {
      const item = previewProtocalList[index];
      if (item.contractCategory === 'POST_INFO_AUTH' || item.contractCategory === 'GRXXSQ_DHZLSQ') { // 贷后资料授权书
        previewProtocalList[index].title = item.contractName;
        previewProtocalList[index].params = { ...item, ...this.getInfoAuthParam(item) };
        previewProtocalList[index].title = item.contractName;
        // 3.0的合同
        if (item.contractCategory === 'GRXXSQ_DHZLSQ') {
          const htmlFilePrivew = await this.htmlFilePrivew(item.contractCode, this.getInfoAuthParam(item));
          previewProtocalList[index].htmlFile = htmlFilePrivew;
        }
      } else if (item.contractCategory === 'NEGOTIATE_REPAY' || item.contractCategory === 'DHYWBG_XSMYZDHK') { // 月最低还款承诺书
        previewProtocalList[index].title = item.contractName;
        previewProtocalList[index].params = { ...item, ...this.getNegotiateRepayParam(item) };
        previewProtocalList[index].title = item.contractName;
        // 3.0的合同
        if (item.contractCategory === 'DHYWBG_XSMYZDHK') {
          const htmlFilePrivew = await this.htmlFilePrivew(item.contractCode, this.getNegotiateRepayParam(item));
          previewProtocalList[index].htmlFile = htmlFilePrivew;
        }
      } else if (item.contractCategory === 'POSTLOAN_CHANGE' || item.contractCategory === 'DHBGXY') { // 贷后服务变更协议
        previewProtocalList[index].title = item.contractName;
        previewProtocalList[index].params = { ...item, ...this.getPostLoanChangeParam(item) };
        previewProtocalList[index].title = item.contractName;
        // 3.0的合同
        if (item.contractCategory === 'DHBGXY') {
          const htmlFilePrivew = await this.htmlFilePrivew(item.contractCode, this.getPostLoanChangeParam(item));
          previewProtocalList[index].htmlFile = htmlFilePrivew;
        }
      }
    }
    this.setState((prevState) => prevState.channelAgreementPlanCfgDtoList.map((item, index) => {
      if (index === i) {
        // 完成强读确认
        Object.assign(item, { forceReadProtocalList: previewProtocalList });
        return;
      }
      return item;
    }));
  }

  // 请求预览html
  htmlFilePrivew = (contractCode, contractPreviewData) => {
    return Dispatch.repayment.queryContractInfo({
      scene: 'PREVIEW',
      interfaceVersion: '3.0',
      contractCode,
      contractPreviewData,
    }).then(res => {
      const { data } = res || {};
      const { contractList } = data || {};
      return contractList && contractList[0] && contractList[0].htmlFile;
    });
  };

  getInfoAuthParam = (contractParam) => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const nowDate = new Date();
    const contractData = {
      name: this.custName || '',
      certId: this.idNo || '',
      yearNow: nowDate.getFullYear(),
      monthNow: nowDate.getMonth() + 1,
      dayNow: nowDate.getDate(),
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      bringParam: 1
    };
    const { dateFormat } = Util.getCurrentDateTimeInFormat();
    contractData.baseContractInfo = {
      signDate: dateFormat,
      certType: '身份证'
    };
    return contractData;
  }

  getNegotiateRepayParam = (contractParam) => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const nowDate = new Date();
    const {
      installTotalAmt, principal, inteFee, fineFee, periodAmt, periodEndYear, periodEndMonth, periodEndDay, lastRepayDate
    } = this;
    const contractData = {
      name: this.custName || '',
      mobile: this.mobile || '',
      certId: this.idNo || '',
      yearNow: nowDate.getFullYear(),
      monthNow: nowDate.getMonth() + 1,
      dayNow: nowDate.getDate(),
      totalAmt: installTotalAmt,
      principal,
      inteFee,
      fineFee,
      periodAmt,
      periodEndYear,
      periodEndMonth,
      periodEndDay,
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      bringParam: 1
    };
    const { dateFormat } = Util.getCurrentDateTimeInFormat();
    contractData.baseContractInfo = {
      signDate: dateFormat,
      certType: '身份证'
    };
    // 贷款交易信息
    contractData.loanTransInfo = {
      residueRepayAmt: installTotalAmt, // 剩余应还款总额
      cashAmt: principal, // 借款金额
      inteFee: inteFee, // 利息
      fineFee: fineFee, // 罚息
      periodAmt: periodAmt, // 每期还款金额
      periodEndDate: lastRepayDate // 借款到期日期
    };
    return contractData;
  }

  getPostLoanChangeParam = (contractParam) => {
    const {
      needCompanySignatureFlag, needCustSignatureFlag
    } = contractParam || {};
    const nowDate = new Date();
    const contractData = {
      yearNow: nowDate.getFullYear(),
      monthNow: nowDate.getMonth() + 1,
      dayNow: nowDate.getDate(),
      name: this.custName || '',
      certName: '身份证',
      mobile: this.mobile || '',
      certId: this.idNo || '',
      isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
      isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
      needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
      needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
      bringParam: 1
    };
    const { dateFormat } = Util.getCurrentDateTimeInFormat();
    contractData.baseContractInfo = {
      signDate: dateFormat,
      certType: '身份证'
    };
    return contractData;
  }

  submitCheck = async () => {
    const {
      serviceType, povertyProofList, diseaseProofList, multipleArrearsList, otherMaterialList
    } = this;

    // 资料合集
    const fileParam = {
      'img.C03': povertyProofList,
      'img.C04': diseaseProofList,
      'img.C05': multipleArrearsList,
      'img.999': otherMaterialList
    };

    const {
      reason, channelAgreementPlanCfgDtoList, scene
    } = this.state;
    if (serviceType === '03' && reason === '请选择') {
      Madp.showToast({
        title: '请选择或填写您的延期还款原因',
        icon: 'none'
      });
      return;
    }
    if (this.needUploadMaterialList.length > 0 && povertyProofList.length < 1 && diseaseProofList.length < 1
      && multipleArrearsList.length < 1 && otherMaterialList.length < 1) {
      Madp.showToast({
        title: '请至少上传一份资料',
        icon: 'none'
      });
      return;
    }
    /* eslint-disable-next-line max-len */
    if (channelAgreementPlanCfgDtoList[0] && channelAgreementPlanCfgDtoList[0].contractList && channelAgreementPlanCfgDtoList[0].contractList.length && !channelAgreementPlanCfgDtoList[0].checkBoxChecked) {
      Madp.showToast({
        title: '请您阅读和勾选协议',
        icon: 'none'
      });
      return;
    }
    // 停缓催、息费减免（待还/已还）、免上浮罚息
    if (scene === 'stopUrging'
      || scene === 'interestFreeTorepay'
      || scene === 'interestFreeRepaid'
      || scene === 'letOff') {
      if (this.applyStatus === '1') {
        const {
          ret, errMsg, data
        } = await Dispatch.repayment.postLoanSubmitCase({
          applyNo: this.applyNo,
          applyFiles: {
            'img.C03': '',
            'img.C04': '',
            'img.C05': '',
            'img.999': ''
          }
        }, fileParam);
        if (ret === '0') {
          const { applyStatus } = data || {};
          // 办理成功
          if (applyStatus === '9') {
            Madp.redirectTo({ url: `/pages/service-result/index?serviceType=stopUrging&status=1&repaymentFlag=${this.repaymentFlag}` });
          } else if (applyStatus === '10') { // 办理失败
            Madp.redirectTo({ url: `/pages/service-result/index?serviceType=stopUrging&status=3&repaymentFlag=${this.repaymentFlag}` });
          } else { // 办理已提交
            Madp.redirectTo({ url: `/pages/service-result/index?serviceType=stopUrging&status=2&repaymentFlag=${this.repaymentFlag}` });
          }
        } else {
          Madp.showToast({
            title: errMsg,
            icon: 'none',
            duration: 2000
          });
        }
        return;
      }
      if (this.applyStatus === '4') {
        const {
          ret, errMsg, data
        } = await Dispatch.repayment.postLoanAddData({
          applyNo: this.applyNo,
          applyFiles: {
            'img.C03': '',
            'img.C04': '',
            'img.C05': '',
            'img.999': ''
          }
        }, fileParam);
        if (ret === '0') {
          const { applyStatus } = data || {};
          // 办理成功
          if (applyStatus === '9') {
            Madp.redirectTo({ url: `/pages/service-result/index?serviceType=stopUrging&status=1&repaymentFlag=${this.repaymentFlag}` });
          } else if (applyStatus === '10') { // 办理失败
            Madp.redirectTo({ url: `/pages/service-result/index?serviceType=stopUrging&status=3&repaymentFlag=${this.repaymentFlag}` });
          } else { // 办理已提交
            Madp.redirectTo({ url: `/pages/service-result/index?serviceType=stopUrging&status=2&repaymentFlag=${this.repaymentFlag}` });
          }
        } else {
          Madp.showToast({
            title: errMsg,
            icon: 'none',
            duration: 2000
          });
        }
        return;
      }
    }
    // 旧版本逻辑
    if (this.applyStatus === '0') {
      const userTodoInfo = await Dispatch.repayment.userTodoInfo({
        sceneFuction: 'QUERY_USER_TODO',
        userTodoParam: {
          todoId: this.todoId,
          serviceType,
          applyId: this.applyId
        }
      });
      if (userTodoInfo && userTodoInfo.length > 0) {
        if (userTodoInfo[0].todoStatus !== '1') {
          // 跳转失败结果页
          Util.router.replace('/pages/common-result/result?type=collect-fail');
        } else {
          // 进行中
          this.submit();
        }
      } else {
        // 无待办处理
        Util.router.replace('/pages/common-result/result?type=collect-fail');
      }
    } else if (this.applyStatus === '1') {
      // applyStatus为1提交案件审核
      let paramOther = {};
      if (this.applySourceType === '1') {
        // 自主建案1要传资料，人工建案2不需要传协商还服务申请信息negotiateRepayApplyInfo
        paramOther = {
          negotiateRepayApplyInfo: {
            installCnt: this.installCnt, // 分期期数
            waiveRatio: this.waiveRatio, // 减免比例
            repayDay: this.repayDay, // 还款日
            installType: 0, // 分期模式
          }
        };
      }
      const {
        ret, errMsg
      } = await Dispatch.repayment.postLoanSubmitCase({
        applyNo: this.applyNo,
        ...paramOther,
        applyFiles: {
          'img.C03': '',
          'img.C04': '',
          'img.C05': '',
          'img.999': ''
        }
      }, fileParam);
      if (ret === '0') {
        const urlParam = `applyNo=${this.applyNo}&status=2&repaymentFlag=${this.repaymentFlag}`;
        Madp.redirectTo({ url: `/pages/consult-repay-result/index?${urlParam}` });
      } else {
        Madp.showToast({
          title: errMsg,
          icon: 'none',
          duration: 2000
        });
      }
    } else if (this.applyStatus === '4') {
      // applyStatus为4需要资料补充
      const { ret, errMsg } = await Dispatch.repayment.postLoanAddData({
        applyNo: this.applyNo,
        applyFiles: {
          'img.C03': '',
          'img.C04': '',
          'img.C05': '',
          'img.999': ''
        }
      }, fileParam);

      if (ret === '0') {
        Util.router.replace('/pages/common-result/result?type=collect-success');
      } else {
        Madp.showToast({
          title: errMsg,
          icon: 'none'
        });
      }
    }
  }

  submit = async () => {
    const { reason } = this.state;
    const {
      povertyProofList, diseaseProofList, multipleArrearsList, otherMaterialList
    } = this;
    const fileParam = {
      povertyProofList,
      diseaseProofList,
      multipleArrearsList,
      otherMaterialList,
    };
    try {
      const {
        ret, errMsg
      } = await Dispatch.repayment.submitServiceMaterial({
        scene: 'POST_LOAN_SERVICE',
        postLoanServiceParam: {
          todoId: this.todoId,
          serviceType: this.serviceType,
          applyId: this.applyId,
          delayRepayReason: reason,
          povertyProofList: '',
          diseaseProofList: '',
          multipleArrearsList: '',
          otherMaterialList: '',
        }
      }, fileParam);
      if (ret === '0') {
        Util.router.replace('/pages/common-result/result?type=collect-success');
      } else {
        Madp.showToast({
          title: errMsg,
          icon: 'none'
        });
      }
    } catch (e) {
      Util.router.replace('/pages/common-result/result?type=collect-fail');
    }
  }

  // 20240730 改成 2024年07月30日
  dateFormat = (date) => {
    if (!date) return '';
    let dateString = date.toString();
    let [year, month, day] = [dateString.substring(0, 4), dateString.substring(4, 6), dateString.substring(6, 8)];
    return `${year}年${month}月${day}日`;
  }

  // 停缓催初始化信息，资料列表、协议
  initStopUrgingData = async () => {
    const { scene } = this.state;
    // 获取停缓催服务建案信息
    const param = {
      serviceType: serviceTypeParam[scene],
    };
    const { ret, errCode, errMsg, data } = await Dispatch.repayment.applyConsultRepayCase(param);
    // 接口报错，建案失败
    if (ret !== '0') {
      let urlParam = 'serviceType=stopUrging&status=3';
      // 准入拒绝使用返回码方式
      if (errCode === 'UMDP02724') { // UMDP02724 不符合办理条件；需前端特殊处理展示
        urlParam = `${urlParam}&subStatus=1`;
      } else if (errMsg) {
        urlParam = `${urlParam}&subStatus=1&errMsg=${errMsg}`;
      }
      Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
      return;
    }
    const {
      applyNo, applyStatus, applySourceType, suspendCollectionPackageInfo, needFileList, contractApplyList, contractInfos, stopFloatFineRateApplyInfo
    } = data || {};
    // 停上浮罚息到期日
    const { endDate } = stopFloatFineRateApplyInfo || {};
    this.stopFloatFineRateEndDate = this.dateFormat(endDate);
    if ((applyStatus === '1' || applyStatus === '4') && applySourceType === '2') { // 未提交或未补充资料提交
      this.applyNo = applyNo;
      this.applyStatus = applyStatus;
      this.suspendCollectionCollectDate = (suspendCollectionPackageInfo || {}).suspendCollectionCollectDate || '';
      /* eslint-disable-next-line max-len */
      this.needUploadMaterialList = (needFileList || []).filter((item) => (item || {}).fileType !== '999').concat((needFileList || []).filter((item) => (item || {}).fileType === '999')).map(item => (item || {}).fileType) || [];
      this.contractApplyList = contractApplyList || [];
      this.contractInfos = contractInfos || [];
      this.operationContractList();
      let showContent = '';
      if (this.contractApplyList.length === 2 || this.contractInfos.length === 2) {
        showContent = '1';
        /* eslint-disable-next-line max-len */
      } else if ((this.contractApplyList.length === 1 && this.contractApplyList[0] && this.contractApplyList[0].contractType === 'POSTLOAN_CHANGE')
        || (this.contractInfos.length === 1 && this.contractInfos[0] && this.contractInfos[0].contractCode === 'DHBGXY')) {
        showContent = '2';
        /* eslint-disable-next-line max-len */
      } else if ((this.contractApplyList.length === 1 && this.contractApplyList[0] && this.contractApplyList[0].contractType === 'POST_INFO_AUTH')
      || (this.contractInfos.length === 1 && this.contractInfos[0] && this.contractInfos[0].contractCode === 'GRXXSQ_DHZLSQ')) {
        showContent = '4';
      } else {
        showContent = '3';
      }
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'CollectEnterStopUrging',
        beaconContent: { cus: { showContent } },
      });
    } else if (applyStatus === '5' || applyStatus === '8') { // 跳转至分停缓催审核页
      Madp.redirectTo({ url: `/pages/service-result/index?serviceType=stopUrging&status=2&repaymentFlag=${this.repaymentFlag}` });
    } else if (applyStatus === '9') { // 跳转至分停缓催成功页
      Madp.redirectTo({ url: `/pages/service-result/index?serviceType=stopUrging&status=1&repaymentFlag=${this.repaymentFlag}` });
    } else { // 其它场景
      Madp.redirectTo({ url: `/pages/service-result/index?serviceType=stopUrging&status=3&repaymentFlag=${this.repaymentFlag}` });
    }
  }

  render() {
    const {
      showPage, reason, showReason, otherReason, showOtherReason, channelAgreementPlanCfgDtoList,
      showAllRepaidAmountExplainModal, returnedPlanTerm, scene
    } = this.state;
    const {
      serviceType, unpaidTotalAmt, promiseAmt, waiveAmt, periodList, firstPeriodRepayDay,
      periodAmt, lastPeriodRepayAmt, lastPeriodRepayDay, needUploadMaterialList
    } = this;

    if (!showPage) {
      return <MUView />;
    }
    return (
      <MUView className="collect">
        <MUView className="collect__explain">
          {scene === 'stopUrging' ? (
            <MUView>尊敬的客户：</MUView>
          ) : null}
          {this.getExplainTop()}
          {needUploadMaterialList.length ? (
            <MUView className="collect__explain__material">主要支持材料类型：图片、word、excel、pdf</MUView>
          ) : null}
        </MUView>
        <MUView className="collect__cnt">
          {serviceType === '03' ? (
            <MUListItem
              className={reason === '请选择'
                ? 'collect__cnt__reason'
                : 'collect__cnt__reason collect__cnt__reason--selected'}
              title="还款困难原因"
              extraText={reason.length > 12 ? `${reason.substring(0, 12)}...` : reason}
              arrow="mu-icon-arrow-right"
              hasBorder
              beaconId="CollectReasonOpen"
              onClick={() => this.setState({ showReason: true })}
            />
          ) : null}
          {(serviceType === '02' || (serviceType === '03' && scene !== 'consultRepay')) && unpaidTotalAmt && <MUListItem className="collect__cnt__amount" title="全部待还金额" extraText={`${unpaidTotalAmt}元`} hasBorder />}
          {serviceType === '03' && scene === 'consultRepay' ? (
            <MUList>
              {this.negotiateRepayPackageList[0]
                && this.negotiateRepayPackageList[0].taskTotalAmt
                && <MUListItem
                  className={'collect__cnt__amount03'}
                  title="全部待还金额"
                  extraText={`${this.negotiateRepayPackageList[0].taskTotalAmt}元`}
                  arrow="mu-icon-arrow-right"
                  postFixIcon="info"
                  beaconId="ConsultRepayTaskTotalAmt"
                  disabledArrow
                  onPostFixIconClick={() => this.setState({
                    showAllRepaidAmountExplainModal: true
                  })}
                />}
              <MUListItem
                className={'collect__cnt__repayplan'}
                title="协商还计划"
                arrow="mu-icon-chevron-right"
                extraType="text"
                beaconId="ConsultRepayRepayplan"
                onClick={() => this.BottomDrawer.show()}
                renderExtraText={
                  <MUView className="collect__cnt__repayplan-details">
                    <MUView className="collect__cnt__repayplan-details__title">
                      {`${Number((returnedPlanTerm.repayDate || '').slice(4, 6))}月${Number((returnedPlanTerm.repayDate || '').slice(6, 8))}日还款${returnedPlanTerm.taskThresholdAmt || 0}元`}
                    </MUView>
                    <MUView className="collect__cnt__repayplan-details__subtitle">
                      最后一月会包含分期期间新增息费
                    </MUView>
                  </MUView>
                }
              />
            </MUList>
          ) : serviceType === '03' && (<MUView
            className="collect__cnt__plan"
            beaconId="CollectPlanShow"
            onClick={() => this.BottomDrawer.show()}
          >
            <MUView className="plan-title">协商还款</MUView>
            <MUView className="plan-content">
              <MUView className="plan-content__period">{`每月还款金额低至${periodAmt}元`}</MUView>
              <MUView className="plan-content__date">{`宽限至${lastPeriodRepayDay}结清全部本息`}</MUView>
            </MUView>
            <MUView className="plan-arrow">
              <MUIcon value="arrow-right" size={15} color="#cacaca" />
            </MUView>
          </MUView>)}
          <MUModal
            isOpened={showAllRepaidAmountExplainModal}
            content="该金额为当前待还金额，分期期间仍然会产生新的息费，具体情况以您借款时签署的合同为准。"
            confirmText="知道了"
            onConfirm={() => this.setState({
              showAllRepaidAmountExplainModal: false
            })}
            onClose={() => this.setState({
              showAllRepaidAmountExplainModal: false
            })}
          />
          {serviceType === '02' && waiveAmt && (
            <MUView className="collect__cnt__reduce collect__cnt__plan">
              <MUView className="plan-title">申请优惠</MUView>
              <MUView className="plan-content">
                <MUView className="plan-content__period">
                  {(promiseAmt && Number(promiseAmt) > 0) && <MUText className="reduce-promise">{`还款${promiseAmt}元可结清`}</MUText>}
                  {(promiseAmt && Number(promiseAmt) > 0) ? `(预计减免${waiveAmt}元)` : `预计减免${waiveAmt}元`}
                </MUView>
                <MUView className="plan-content__date">最终减免金额以审批通过后还款页面展示结果为准</MUView>
              </MUView>
            </MUView>
          )}
        </MUView>
        {needUploadMaterialList.map((item) => (
          <ChooseFiles
            chooseFilesParam={{
              materialType: item,
              chooseFileFinish: this.chooseFileFinish,
            }}
            hideInput={channelAgreementPlanCfgDtoList[0] && channelAgreementPlanCfgDtoList[0].show}
          />
        ))}
        <MUView className="collect-contract-sumbit">
          {channelAgreementPlanCfgDtoList.map((item, index) => item.contractList && item.contractList.length > 0 && (
            <MUView className="collect-contract-container">
              <MURadio
                type="row"
                className="extend-contract-checker"
                beaconId="ExtendContractChecker"
                options={[{
                  type: 'protocal',
                  labelLeft: '我已阅读并同意',
                  linkText: item.contractText,
                  onLink: () => {
                    this.toViewContract(index);
                    /* eslint-disable-next-line max-len */
                    Object.assign(item, { show: true, readDuration: (item.forceReadFlag && !item.hasForceRead) ? 5 : 0 });
                    setTimeout(() => {
                      if (Util.isH5Env()) {
                        Madp.showToast({
                          title: '',
                          icon: 'none',
                          duration: 0
                        });
                      }
                    }, 0);
                  },
                  value: 'checked'
                }]}
                value={item.checkBoxChecked ? 'checked' : ''}
                onClick={() => this.handlerCheckboxClick(index)}
              />
              <AgreementDrawer
                agreementViewProps={{
                  type: (item.forceReadProtocalList && item.forceReadProtocalList.length > 1) ? 1 : 2,
                  list: item.forceReadProtocalList,
                  current: 0,
                }}
                show={item.show}
                close={() => this.handleSubmitClick(index, '0')}
                submit={() => this.handleSubmitClick(index, '1')}
                totalCount={item.readDuration}
              />
            </MUView>
          ))}
          <MUView className="collect__submit">
            <MUButton type="primary" onClick={this.submitCheck}>提交</MUButton>
          </MUView>
        </MUView>
        <MUActionSheet
          isOpened={showReason}
          cancelText="取消"
          onClose={() => this.setState({ showReason: false })}
        >
          {this.reasonList.map((item) => (
            <MUActionSheetItem
              beaconId="CollectReasonSelect"
              onClick={() => this.setState({ reason: item, showReason: false, showOtherReason: false })}
            >{item}</MUActionSheetItem>
          ))}
          <MUView className="reason-divide-area" />
          <MUActionSheetItem
            className="reason-last-item"
            beaconId="CollectReasonOther"
            onClick={() => this.setState({ showOtherReason: true })}
          >
            其他
            {showOtherReason && (
              <MUView>
                <MUView
                  className="reason-last-item__submit"
                  style={`color: ${themeColor}`}
                  beaconId="CollectReasonFinish"
                  onClick={() => {
                    if (otherReason.trim().length < 1) {
                      Madp.showToast({
                        title: '请填写您的困难原因',
                        icon: 'none'
                      });
                      return;
                    }
                    this.setState({
                      reason: otherReason,
                      showReason: false,
                    });
                  }}
                > 提交</MUView>
                <MUTextarea
                  className="reason-last-item__text"
                  value={otherReason}
                  beaconId="CollectReasonInput"
                  onChange={(e) => this.handleInput(e)}
                  maxLength={50}
                />
              </MUView>
            )}
          </MUActionSheetItem>
        </MUActionSheet>
        {
          serviceType === '03' && scene === 'consultRepay' ? (<BottomDrawer
            ref={(ref) => { this.BottomDrawer = ref; }}
            title="还款计划"
            height="60%"
          >
            <MUView className="consultRepay-explanation">分期期间，新产生的息费{this.waiveRatio !== 0 && `减${this.waiveRatio}%，`}累计至最后一个月还款</MUView>
            <MUView className="consultRepay-bottom-plan">
              <MUView className="consultRepay-bottom-plan__content">
                {this.negotiateRepayTrial.map((item, i) => (
                  <MUView className="consultRepay-bottom-plan__content__item">
                    <MUView className="item__date">{`${item.repayDate.slice(0, 4)}/${item.repayDate.slice(4, 6)}/${item.repayDate.slice(6, 8)}`}</MUView>
                    <MUView>
                      <MUView className="item__circle" style={`border-color: ${themeColor}`} />
                      {i < (this.negotiateRepayTrial.length - 1) ? (
                        <MUView className="item__line" />
                      ) : null}
                    </MUView>
                    <MUView>{i < (this.negotiateRepayTrial.length - 1) ? (
                      <MUView className="item__amt">
                        <MUView className="item__amt--threshold">
                          {item.taskThresholdAmt}<MUText className="unit">元</MUText>
                        </MUView>
                        {this.waiveRatio !== 0 && <MUView className="item__amt--task">{item.taskAmt}元</MUView>}
                      </MUView>
                    ) : <MUText className="last">结清剩余金额{this.waiveRatio !== 0 && `，息费减免${this.waiveRatio}%`}</MUText>}</MUView>
                  </MUView>
                ))}
              </MUView>
            </MUView>
          </BottomDrawer>) : (<BottomDrawer
            ref={(ref) => { this.BottomDrawer = ref; }}
            title="协商还款"
            height="60%"
          >
            <MUView className="bottom-plan">
              <MUView className="bottom-plan__desc">{`每月还款金额不低于${periodAmt}元，最后一期还款金额以结清当日累计欠款为准；`}</MUView>
              <MUView className="bottom-plan__desc">{`${lastPeriodRepayDay}前结清名下全部欠款本金及息费。`}</MUView>
              <MUView className="bottom-plan__content">
                {periodList.map((item, i) => (
                  <MUView className="bottom-plan__content__item">
                    <MUView>{this.getCntPlanDate(firstPeriodRepayDay, i)}</MUView>
                    <MUView>
                      <MUView className="item__circle" style={`border-color: ${themeColor}`} />
                      {i < (periodList.length - 1) ? (
                        <MUView className="item__line" />
                      ) : null}
                    </MUView>
                    <MUView>{i < (periodList.length - 1) ? periodAmt : lastPeriodRepayAmt}</MUView>
                  </MUView>
                ))}
              </MUView>
            </MUView>
          </BottomDrawer>)
        }
      </MUView>
    );
  }
}
