.credit-product {
  &__header {
    width: 100%;
    height: 200px;
    image,
    .taro-img {
      width: 100%;
      height: 100%;
    }
  }

  &__date {
    padding: 30px 30px 40px;
    background: #fff;
    &__tip {
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 40px;
      color: #FF8900;
      font-weight: 400;
      .tip__text {
        height: 24px;
        font-size: 22px;
        line-height: 24px;
        padding: 8px 8px 8px 28px;
        border-radius: 20px 0 0 20px;
        background: #FFF5E9;
      }
      .tip__icon {
        width: 28px;
        height: 28px;
        padding: 6px 28px 6px 0;
        border-radius: 0 20px 20px 0;
        background: #FFF5E9;
        font-size: 0;
      }
    }

    &__grace {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      .grace__pay-date {
        font-weight: 400;
        &__text {
          font-size: 24px;
          line-height: 24px;
          color: #333;
        }
        &__date {
          margin-top: 10px;
          font-size: 40px;
          line-height: 48px;
          color: #333;
        }
      }
      .grace__arrow {
        margin-bottom: 6px;
        width: 170px;
        height: 36px;
        font-size: 0;
        image,
        .taro-img {
          width: 100%;
          height: 100%;
        }
      }
      .grace__extend-date {
        text-align: right;
        font-weight: 400;
        &__text {
          font-size: 24px;
          line-height: 24px;
          color: #333;
          font-weight: 400;
        }
        &__date {
          margin-top: 10px;
          font-size: 40px;
          line-height: 48px;
          color: #FF8844;
          font-weight: 400;
        }
      }
    }
  }

  &__amt {
    margin-top: 20px;
    padding: 30px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &__text {
      font-size: 32px;
      line-height: 38px;
      color: #333;
      font-weight: 400;
    }
    &__total {
      text-align: right;
      font-weight: 400;
      .total__money {
        font-size: 40px;
        line-height: 40px;
        color: #333;
      }
      .total__count {
        margin-top: 20px;
        font-size: 24px;
        line-height: 24px;
        color: #a6a6a6;
      }
    }
  }

  &__button {
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 30px;
    width: calc(100% - 60px);
    background: #fff;
  }

  .credit-product-dialog {
    .RepayModal-dialog-modal__header {
      margin: 50px auto 10px;
    }
  }
}