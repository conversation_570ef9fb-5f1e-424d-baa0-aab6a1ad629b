/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUImage, MUText, MUIcon, MUButton
} from '@mu/zui';
import Madp from '@mu/madp';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { Url } from '@mu/madp-utils';
import { setStore } from '@api/store';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import CustomConfig from '@config/index';
import pageHoc from '@utils/pageHoc';
import RepayModal from '@components/repay-modal/index';
import { miniProgramChannel } from '@utils/constants';

import './index.scss';

// import '@components/repay-modal/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/repay-modal/index.scss')
}

const themeColor = Util.getThemeColor(CustomConfig.theme);
const creditProductHeader = 'https://file.mucfc.com/zlh/3/0/202304/20230423153242d36101.png';
const creditProductArrow = 'https://file.mucfc.com/zlh/3/0/202304/20230423153303b4bd75.png';

@track({ event: EventTypes.PO }, {
  pageId: 'CreditProduct',
  dispatchOnMount: true,
})
@pageHoc({ title: '诚信保护' })
export default class CreditProduct extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showPage: false, // 页面是否展示
      showCreditProductDialog: false, // 诚信保护说明弹窗
    };
    this.creditProductList = []; // 诚信保护账单列表
    this.extendGracePeriodCount = 3; // 延长诚信保护期天数
    this.payDate = ''; // 应还款日
    this.extendGraceDate = ''; // 延长诚信保护期后的还款日
    this.creditProductTotalAmt = 0; // 延长诚信保护期的总金额
    this.creditProductTotalCount = 0; // 延长诚信保护期的总笔数
    this.supplySuccess = Url.getParam('supplySuccess') || '';
  }

  componentDidMount() {
    // 若是补充身份信息且成功回来的，则跳转到补充联系人页面
    if (this.supplySuccess === '1') {
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'UpdateIDCardSuccess' });
      Util.router.replace({
        path: '/pages/identity/contact',
        query: {
          billType: 'credit-product'
        }
      });
      return;
    }

    this.initEvent();
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '诚信保护' });
  }

  config = {
    navigationBarTitleText: '诚信保护'
  }

  // 初始化事件 获取初始化数据
  // eslint-disable-next-line react/sort-comp
  async initEvent() {
    // 准入处理
    const { errCode } = await Dispatch.repayment.adjustAccessCheck({ adjustType: '5' });
    if (errCode !== 'COM00000') {
      Madp.showModal({
        content: '很抱歉，您暂不符合办理条件，请继续累计信用。',
        showCancel: false,
        confirmText: '返回',
        confirmColor: themeColor,
        success: (res) => {
          if (res.confirm) {
            this.backOrClose();
          }
        }
      });
      return;
    }

    // 诚信保护账单查询
    try {
      this.creditProductList = await Dispatch.repayment.getCreditProductList();
      if (this.creditProductList && this.creditProductList.length > 0) {
        let minPayDate = this.creditProductList[0].payDate;
        let maxOverDueDay = 0;
        this.creditProductList.forEach((billItem = {}, i) => {
          if (i > 0 && Util.timeMinus(minPayDate, billItem.payDate, 'day', 'date') > 0) {
            minPayDate = billItem.payDate;
          }
          if (i >= 0 && billItem.overdueDays && Number(billItem.overdueDays) > maxOverDueDay) {
            maxOverDueDay = Number(billItem.overdueDays);
          }
          this.creditProductTotalAmt = billItem.surplusPayTotalAmt ? Util.floatAdd(Number(billItem.surplusPayTotalAmt), Number(this.creditProductTotalAmt)) : 0;
          this.creditProductTotalCount += 1;
        });
        dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'MaxOverDueDay', beaconContent: { cus: { maxOverDueDay } } });
        this.payDate = minPayDate;
        this.extendGraceDate = this.creditProductList.filter((listItem) => listItem.payDate === this.payDate)[0].inteDate;
        this.extendGracePeriodCount = Util.timeMinus(this.extendGraceDate, this.payDate, 'day', 'date') || 3;
        this.setState({ showPage: true });
      } else {
        Madp.showModal({
          content: '很抱歉，您暂不符合办理条件，请继续累计信用。',
          showCancel: false,
          confirmText: '返回',
          confirmColor: themeColor,
          success: (res) => {
            if (res.confirm) {
              this.backOrClose();
            }
          }
        });
      }
    } catch (err) {
      Madp.showModal({
        content: '很抱歉，您暂不符合办理条件，请继续累计信用。',
        showCancel: false,
        confirmText: '返回',
        confirmColor: themeColor,
        success: (res) => {
          if (res.confirm) {
            this.backOrClose();
          }
        }
      });
    }
  }

  // 诚信保护提交
  async submitCreditProduct() {
    const orderList = [];
    if (this.creditProductList && this.creditProductList.length > 0) {
      this.creditProductList.forEach((item) => {
        if (item.orderNo && item.installCnt) {
          orderList.push(`${item.orderNo}_${item.installCnt}`);
        }
      });
    }
    let creditProductRepayInfo = {
      adjustType: '5',
      applyScene: '6',
      extendGracePeriodAdjustInfo: {
        adjustCnt: this.extendGracePeriodCount ? this.extendGracePeriodCount.toString() : '3',
        orderList,
      }
    };
    setStore({ creditProductRepayInfo });
    const { authInfoDetails } = await Dispatch.repayment.checkSupplyInfo('delay_repay');
    const isNeedSupplyID = !!authInfoDetails && authInfoDetails.filter((process) => process.authParamType === 'COMPENSATE_ID_INFO').length;
    if (isNeedSupplyID) {
      const supplyParams = {
        scene: 'SCENE_PACK_DELAY_GRACE',
        billType: 'credit-product',
      };
      Util.gotoSupplyInfo(supplyParams);
    } else {
      // 不做身份证做补充联系人
      Util.router.replace({
        path: '/pages/identity/contact',
        query: {
          type: 'credit-product',
          billType: 'credit-product'
        }
      });
    }
  }

  // 通用返回处理
  backOrClose = () => {
    if (Util.isH5Env()) {
      Madp.closeWebView().then().catch(() => {
        if (miniProgramChannel.indexOf(Madp.getChannel()) > -1) {
          Madp.miniProgram.navigateBack({ delta: 1 });
        } else {
          Madp.navigateBack({ delta: 1 });
        }
      });
    } else {
      Madp.navigateBack({ delta: 1 });
    }
  }

  render() {
    const { showPage, showCreditProductDialog } = this.state;
    if (!showPage) {
      return <MUView />;
    }
    const {
      extendGracePeriodCount, payDate, extendGraceDate, creditProductTotalAmt, creditProductTotalCount
    } = this;
    return (
      <MUView className="pages-bg credit-product">
        <MUView style={`min-height: ${CustomConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
          <MUView className="credit-product__header">
            <MUImage src={creditProductHeader} />
          </MUView>
          <MUView className="credit-product__date">
            <MUView
              className="credit-product__date__tip"
              beaconId="CreditProductDateTip"
              onClick={() => this.setState({ showCreditProductDialog: true })}
            >
              <MUView className="tip__text">
                即将开启{extendGracePeriodCount}天诚信保护
              </MUView>
              <MUView className="tip__icon">
                <MUIcon value="info" size="14" color="#FF8900" />
              </MUView>
            </MUView>
            <MUView className="credit-product__date__grace">
              <MUView className="grace__pay-date">
                <MUView className="grace__pay-date__text">应还款日</MUView>
                <MUView className="grace__pay-date__date">{`${Util.getDateCollection(payDate).splice(1).join('月')}日`}</MUView>
              </MUView>
              <MUView className="grace__arrow">
                <MUImage src={creditProductArrow} />
              </MUView>
              <MUView className="grace__extend-date">
                <MUView className="grace__extend-date__text">延后还款日</MUView>
                <MUView className="grace__extend-date__date">{`${Util.getDateCollection(extendGraceDate).splice(1).join('月')}日`}</MUView>
              </MUView>
            </MUView>
          </MUView>
          <MUView className="credit-product__amt">
            <MUView className="credit-product__amt__text">可延后还款金额</MUView>
            <MUView
              className="credit-product__amt__total"
              beaconId="CreditProductAmtTotal"
              onClick={() => {
                Util.router.push('/pages/credit-product/list');
              }}
            >
              <MUView className="total__money">{creditProductTotalAmt}</MUView>
              <MUView className="total__count">
                <MUText>共{creditProductTotalCount}笔</MUText>
                <MUIcon value="arrow-right" size="12" color="#A6A6A6" />
              </MUView>
            </MUView>
          </MUView>
          <MUView className="credit-product__button">
            <MUButton
              type="primary"
              beaconId="CreditProductButton"
              onClick={() => this.submitCreditProduct()}
            >立即申请</MUButton>
          </MUView>
        </MUView>
        <RepayModal
          type="image"
          title="诚信保护期规则说明"
          className="credit-product-dialog"
          isOpened={showCreditProductDialog}
          closeOnClickOverlay={false}
          beaconId="CreditProductDialog"
          confirmText="我知道了"
          onConfirm={() => this.setState({ showCreditProductDialog: false })}
        >
          <MUView>
            <MUView className="credit-product-dialog__content">
              <MUView>1、诚信保护期内按正常利率计息；罚息不上浮，减轻你的还款负担。</MUView>
              <MUView>2、诚信保护期内不会将逾期信息上报人行，保护你的信用。</MUView>
              <MUView>请抓紧时间在诚信保护期内及时还款，有借有还，再借不难。</MUView>
            </MUView>
          </MUView>
        </RepayModal>
      </MUView>
    );
  }
}
