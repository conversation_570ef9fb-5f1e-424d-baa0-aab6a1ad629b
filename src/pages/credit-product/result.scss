.result-page {
  height: 100vh;
  width: 100vw;
  padding-bottom: 176px;
  box-sizing: border-box;
  background-color: #fff;

  &__header {
    display: flex;
    flex-direction: column;
    align-items: center;

    &--slogan {
      width: 172px;
      height: auto;
      margin-bottom: 12px;
      margin-top: 42px;
    }
    
    &--img {
      height: 228px;
      width: 200px;
    }

    &--icon {
      margin-top: 90px;
    }

    &--title {
      height: 40px;
      width: auto;
      margin-top: 42px;
      opacity: 1;
      color: #333333ff;
      text-align: left;
      font-size: 40px;
      font-weight: 400;
      font-family: "PingFang SC";
      line-height: 40px;
    } 

    &--dec {
      width: 652px;
      height: 126px;
      margin-top: 36px;
      opacity: 1;
      color: #888888;
      text-align: center;
      font-size: 28px;
      font-weight: 400;
      font-family: "PingFang SC";
      line-height: 42px;

      .highlight {
        color: #FF8200;
      }
    }
  }

  .at-button {
    width: 330px;
    height: 88px;
    margin-top: 50px;
    opacity: 1;
  }
}

.result-slogan {
  margin-top: -96px !important;
}