import { Component } from '@tarojs/taro';
import {
  MUView, MUImage, MUButton, MUText, MUSlogan, MUIcon
} from '@mu/zui';
import Madp from '@mu/madp';
import { track } from '@mu/madp-track';
import Util from '@utils/maxin-util';
import { isMiniProgram } from '@utils/repay-util';
import { getStore } from '@api/store';
import pageHoc from '@utils/pageHoc';
import CustomConfig from '@config/index';
import './result.scss';

const sloganUrl = 'https://file.mucfc.com/bos/3/0/202304/20230427114002edca83.png';
const successIconUrl = 'https://file.mucfc.com/ebn/3/0/202304/20230421165019bcb2ae.png';

@track({}, { // 不需提供beaconId,第一个参数为空
  pageId: 'CreditProductResult',
  dispatchOnMount: true, // 页面埋点需要这样写, 表示当页面组件挂载时立刻触发埋点事件
})
@pageHoc({ title: '结果' })
export default class CreditProductResult extends Component {
  componentDidMount() {
    Madp.setNavigationBarTitle({ title: '结果' });
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '结果' });
  }

  goBackHome() {
    if (isMiniProgram()) {
      Madp.reLaunch({
        url: decodeURIComponent('%2Fpages%2Findex%2Findex'),
      });
      return;
    }
    Madp.closeWebView().then().catch(() => {
      Madp.navigateBack({ delta: window.history.length - 1 });
    });
  }

  render() {
    const { status, graceDate } = getStore('creditProductResultInfo') || {};
    if (status === 'SUC' && graceDate) return (
      <MUView className="pages-bg">
        <MUView style={`min-height: ${CustomConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
          <MUView className="result-page">
            <MUView className="result-page__header">
              <MUImage mode="widthFix" className="result-page__header--slogan" src={sloganUrl} />
              <MUImage className="result-page__header--img" src={successIconUrl} />
              <MUView className="result-page__header--title">已为您开启诚信保护</MUView>
              <MUView className="result-page__header--dec">
                有效期至 {Util.dateFormatter(graceDate, 'dateline')}，请抓紧时间在诚信保护期内及时还款，有借有还，再借不难；
                <MUText className="highlight">诚信保护期内仅对申请办理的借据生效</MUText>
                ，详情请查询待还账单
              </MUView>
            </MUView>
            <MUButton beaconId="BackBtn" type="secondary" className="result-page__button" onClick={this.goBackHome.bind(this)}>
              返回
            </MUButton>
          </MUView>
          <MUSlogan className="result-slogan" onlyLogo />
        </MUView>
      </MUView>
    );
    return (
      <MUView className="pages-bg result-page">
        <MUView style={`min-height: ${CustomConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
          <MUView className="result-page__header">
            <MUIcon className="result-page__header--icon" value="warning" size="80" />
            <MUView className="result-page__header--title">申请失败</MUView>
          </MUView>
          <MUButton beaconId="BackBtn" type="secondary" className="result-page__button" onClick={this.goBackHome.bind(this)}>
            返回
          </MUButton>
        </MUView>
      </MUView>
    );
  }
}
