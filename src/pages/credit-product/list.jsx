/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView,
} from '@mu/zui';
import Madp from '@mu/madp';
import { track, EventTypes } from '@mu/madp-track';
import { getStore } from '@api/store';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import CustomConfig from '@config/index';
import pageHoc from '@utils/pageHoc';
import ListItem from '@components/list-item';
import { miniProgramChannel } from '@utils/constants';

// import '@components/list-item/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/list-item/index.scss');
}

const themeColor = Util.getThemeColor(CustomConfig.theme);

@track({ event: EventTypes.PO }, {
  pageId: 'CreditProductList',
  dispatchOnMount: true,
})
@pageHoc({ title: '待还账单' })
export default class CreditProductList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      creditProductList: [], // 诚信保护账单列表
    };
  }

  componentDidMount() {
    this.initEvent();
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '待还账单' });
  }

  config = {
    navigationBarTitleText: '待还账单'
  }

  // 初始化事件 获取初始化数据
  // eslint-disable-next-line react/sort-comp
  async initEvent() {
    const creditProductList = getStore('creditProductList');
    if (!this.creditProductList || (this.creditProductList && this.creditProductList.length <= 0)) {
      try {
        this.creditProductList = await Dispatch.repayment.getAdjustBills();
        if (!this.creditProductList || (this.creditProductList && this.creditProductList.length <= 0)) {
          Madp.showModal({
            content: '抱歉，您暂无待还账单信息，请过段时间后再查看。',
            showCancel: false,
            confirmText: '返回',
            confirmColor: themeColor,
            success: (res) => {
              if (res.confirm) {
                this.backOrClose();
              }
            }
          });
        } else {
          this.setState({
            creditProductList
          });
        }
      } catch (err) {
        Madp.showModal({
          content: '抱歉，您暂无待还账单信息，请过段时间后再查看。',
          showCancel: false,
          confirmText: '返回',
          confirmColor: themeColor,
          success: (res) => {
            if (res.confirm) {
              this.backOrClose();
            }
          }
        });
      }
    } else {
      this.setState({
        creditProductList
      });
    }
  }

  // 通用返回处理
  backOrClose = () => {
    if (Util.isH5Env()) {
      Madp.closeWebView().then().catch(() => {
        if (miniProgramChannel.indexOf(Madp.getChannel()) > -1) {
          Madp.miniProgram.navigateBack({ delta: 1 });
        } else {
          Madp.navigateBack({ delta: 1 });
        }
      });
    } else {
      Madp.navigateBack({ delta: 1 });
    }
  }

  render() {
    const { creditProductList } = this.state;
    const showCreditProductList = creditProductList && creditProductList.length > 0;
    if (!showCreditProductList) {
      return <MUView />;
    }
    return (
      <MUView className="pages-bg credit-product-list">
        <MUView style={`min-height: ${CustomConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
          {creditProductList.map((bill) => (
            <ListItem item={bill} billType="credt-product" infoClick={(item) => this.infoClick(item)} checked disabled readOnly isAllZL />
          ))}
        </MUView>
      </MUView>
    );
  }
}
