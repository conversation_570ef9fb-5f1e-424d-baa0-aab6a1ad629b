/* eslint-disable prefer-rest-params */
/* eslint-disable no-unused-expressions */
/* eslint-disable max-len */
/* eslint-disable object-curly-newline */
/* eslint-disable arrow-body-style */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable react/sort-comp */
/* eslint-disable prefer-destructuring */
import Taro, { Component } from '@tarojs/taro';
import {
  MUModal, MUView, MUImage, MUText, MUIcon, MUInput, MUButton, MUNavBarWeapp,
} from '@mu/zui';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import pageHoc from '@utils/pageHoc';
import { injectState, refresh } from '@mu/leda';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import Madp from '@mu/madp';
import { filterQualCoupons, getFeeInteRightInfo } from '@utils/repay-util';
import { setStore } from '@api/store';
import {
  activityIds, repayServiceColumnsUrls, miniProgramChannel, EVENT_CODE_MAP
} from '@utils/constants';
import { debounce, getEnv, isMuapp, Url } from '@mu/madp-utils';
import CountDown from '@components/countdown';
import CustomConfig from '@config/index';
import { RepaymentPlugin as BusinessPlugin } from '@mu/business-plugin';
import { repaymentFn } from '@mu/business-plugin-utils';
import { OpRepayment } from '@mu/op-comp';
import { opService, getPageConf } from '@mu/business-basic';

import './index.scss';

// 引用一次

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  // 引用一次
  require('@components/countdown/index.scss');
}

if (process.env.TARO_ENV === 'swan') {
  require('./index_swan.scss');
}

const themeColor = Util.getThemeColor(CustomConfig.theme);
const rulePicture = 'https://file.mucfc.com/ebn/3/0/202406/20240612143536d69909.png';

let { feeReduceFn } = repaymentFn || {};
let ledaNameList = feeReduceFn.getLedaNameList();
// 默认页面展位id
const feeReduceLedaId = '5a814254-d293-4dcd-b65e-419ef675eee8';

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: feeReduceFn.getPageId() || feeReduceLedaId
    }
  }
}, {
  pageId: 'ReduceFee',
  dispatchOnMount: true,
})
@pageHoc({ title: '还款优惠' })
@injectState({
  pageId() {
    return feeReduceFn.getPageId() || feeReduceLedaId;
  },
  getPageConf: () => getPageConf(feeReduceLedaId, true),
  stateKeys: [
    ...ledaNameList
  ]
})
export default class ReduceFee extends Component {
  config = {
    navigationBarTitleText: '还款优惠',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  };
  constructor() {
    super(...arguments);
    this.state = {
      showNoFeeAmtModal: false, // 是否展示无待还息费弹窗
      showRefuseModal: false, // 不符合办理条件弹窗（无可用资格券）
      showPage: false,
      showErrorModal: false, // 还款试算失败引导退出弹窗
      showLimitModal: false, // 是否展示还款金额不足弹窗
      thresholdAmount: '', // 最低门槛金额
      payoffWaiveAmt: '-', // 结清档位优惠
      rulesList: [],
      payoffAmt: '-', // 结清金额
      time: 0, // 倒计时初始时间
      totalPay: '-',
      remainAmt: '-',
      inputAmt: '',
      feeReduce: '-',
      lateFee: '-',
      canSubmit: true, // 可以提交
      showDetailModal: false,
      overdueFlag: false, // 是否逾期
      capitalAmt: '-',
      feeDetail: {},
      courtCostBalance: '0.00', // 总法诉费
      inputting: false,
      showGuideUse: false, // 引导用券弹窗
      settleWaiveAmt: '-',
      totalCanWaiveAmt: '-',
      reduceFeeCoupon: null, // 当前使用的息费减免资格券
      totalFee: '-',
    };
    this.relatedOrderList = [];
    // fix: 用户输入框失焦和按钮点击同时发生导致返回值顺序异常问题
    this.changeAmt = debounce((val, keep) => {
      this.changeAmtEvent(val, keep);
    }, 100, {
      leading: false, // 指定调用在节流开始前
      trailing: true // 指定调用在节流结束后
    });
    this.needRefreshData = false;
    this.awardInfoList = []; // 优惠券
    this.hasPrepayFeeRightsCoupon = false, // 标识是否有免提还资格券
    this.prepayFeeRightsCouponList = [], // 免提还资格券
    this.prepayFeeRightsObject = {}; // 免提还权益信息（用于传到支付页作为提交接口入参）
    this.clickCleared = false; // 点击了清除按钮
    this.hoursLeft = 0; // 优惠券剩余Xh
    this.questionPopupRef = {};
    this.isQuestionPopupShow = false; // 问券打开
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1; // 小程序渠道标识
    this.opRetainOpened = ''; // 是否已经成功展示过挽留弹窗
    this.isNewOverdueCoupon = false; // 是否新券
  }

  async componentDidMount() {
    await this.initData();
  }

  async componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '还款优惠' });
    // 二次进入该页面，需重新获取数据
    if (this.needRefreshData) {
      await this.initData();
      const { inputAmt } = this.state;
      if (inputAmt) {
        this.repayTransTrialApi(inputAmt);
      }
    }
  }

  async initData() {
    await this.getRepayData();
  }

  /**
   * 获取全部待还账单，累加计算各种费用。居然让前端自己算 无语 总在 近期待还 全部待还切来切去的
   */

  async getRepayData() {
    try {
      const [
        allBills,
        { awardDetailList = [] },
      ] = await Promise.all([
        Dispatch.repayment.getReduceFeeAllBills({ additionalQueryList: ['001'] }),
        Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }),
      ]);
      const {
        negotiateRepayTaskBill, // 在途协商还信息
        // surplusTotalAmt = 0,
        advanceBillList = [],
        isDueTagCust,
        dueRepayInfo,
        overdueDays, // 客户在途自营借据的最大逾期天数，若客户在途自营借据均未逾期（过还款日）则逾期天数为0
        surplusPayTotalPrincipalAmt, // 剩余总本金
        surplusPayTotalInteAmt, // 剩余总利息
        surplusPayTotalPeriodFeeAmt, // 剩余总分期手续费
        surplusPayTotalPrepayFeeAmt, // 提前还款总手续费(违约金)
        surplusPayDueTotalInteFeeAmt, // 到期总息费
        surplusPayDueTotalPrincipalAmt, // 到期总本金
        surplusPayDueTotalInteAmt, // 到期总利息
        surplusPayDueTotalPeriodFee, // 到期总费用
        surplusPayTotalFineAmt, // 到期总罚息
        surplusPayTotalOnetimeFee, // 到期总一次性费用
        courtCostBalance, // 总法诉费
        repayControlDetailList, // 管控信息
      } = allBills;
      if (negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}') {
        Madp.showModal({
          content: '当前服务与协商还不可同享，您有正在办理或成功办理的协商还服务',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: themeColor,
          success: (res) => {
            if (res.confirm) {
              if (window && (window.history.length === 1)) {
                Madp.closeWebView();
              } else {
                Madp.navigateBack();
              }
            }
          }
        });
        return;
      }
      // 息费减免场景只需要传可见可还借据
      this.allBillList = (advanceBillList || []).filter((bill) => bill.displayStatus === '2') || [];
      // 涉及到的借据号
      const waiveOrderNos = [];
      this.relatedOrderList = [];
      const sumAllAmt = {
        oneTimeFee: isDueTagCust === 'Y' ? dueRepayInfo && dueRepayInfo.duePayTotalOnetimeFeeAmt : surplusPayTotalOnetimeFee, // 一次性手续费
        fineAmt: isDueTagCust === 'Y' ? dueRepayInfo && dueRepayInfo.duePayTotalFineAmt : surplusPayTotalFineAmt, // 罚息
        interest: isDueTagCust === 'Y' ? dueRepayInfo && dueRepayInfo.duePayTotalInteAmt : surplusPayDueTotalInteAmt, // 利息
        // prepayFee: 0, // 提前还款手续费 (默认就是0 近期还款接口没有返回)
        periodFee: isDueTagCust === 'Y' ? dueRepayInfo && dueRepayInfo.duePayTotalPeriodFeeAmt : surplusPayDueTotalPeriodFee, // 分期手续费
        surplusPayTotalPrincipalAmt,
        surplusPayTotalInteAmt,
        surplusPayTotalPeriodFeeAmt,
        surplusPayTotalPrepayFeeAmt
      };

      this.allBillList && this.allBillList.forEach((bill) => {
        waiveOrderNos.push(bill.orderNo);
      });

      // 待还息费
      const totalFeeAmt = (isDueTagCust === 'Y' && dueRepayInfo)
        ? (Number(dueRepayInfo.duePayTotalInteAmt) + Number(dueRepayInfo.duePayTotalPeriodFeeAmt) + Number(dueRepayInfo.duePayTotalFineAmt) + Number(dueRepayInfo.duePayTotalOnetimeFeeAmt)).toFixed(2)
        : (Number(surplusPayTotalInteAmt) + Number(surplusPayTotalPeriodFeeAmt) + Number(surplusPayTotalFineAmt) + Number(surplusPayTotalOnetimeFee)).toFixed(2);

      // 逾期打标
      let overDueTagFlag = false;
      if (isDueTagCust === 'Y') {
        (repayControlDetailList || []).forEach((item = {}) => {
          if (item.controlCode === 'C401' && (item.eventCode === 'GKSJ032' || item.eventCode === 'GKSJ033' || item.eventCode === 'GKSJ003')) overDueTagFlag = true;
        });
      }

      this.setState({
        overdueFlag: Number(overdueDays) >= 1 || overDueTagFlag,
        isDueTagCust,
        courtCostBalance,
        capitalAmt: isDueTagCust === 'Y' ? dueRepayInfo && dueRepayInfo.duePayTotalPrincipalAmt : surplusPayDueTotalPrincipalAmt, // 本金
        totalFee: isDueTagCust === 'Y' ? dueRepayInfo && dueRepayInfo.duePayTotalInteFeeAmt : surplusPayDueTotalInteFeeAmt, // 总息费
        feeDetail: { ...sumAllAmt }
      }, async () => {
        // 总息费为0，引导去全部待还
        if (Number(totalFeeAmt) === 0) {
          this.setState({
            showNoFeeAmtModal: true
          });
          return;
        }
        const showRefuseModal = await this.getCoupon(awardDetailList);
        // 拒绝弹窗弹出时就不再执行后续逻辑，直接引导退出
        if (showRefuseModal) return;
        await this.getRepayWaiveTrialTrans();
        this.calcCountDown();
        this.setState({ showPage: true }, () => {
          dispatchTrackEvent({
            event: EventTypes.SO,
            target: this,
            beaconId: 'EnterPage',
          });
        });
        // if (process.env.TARO_ENV === 'h5' && getWebViewName() !== 'ks') {
        if (process.env.TARO_ENV === 'h5') {
          Util.pushUrlState('keepState');
        }
      });
    } catch (err) {
      this.setState({ canSubmit: false });
    }
  }

  // 获取优惠券
  async getCoupon(awardDetailList) {
    try {
      // 如果缓存没有就去查接口获取
      let { usableQualConponList: couponList = [] } = filterQualCoupons(awardDetailList, this.allBillList) || {};
      let prepayFeeRightsCouponList = [];
      const { overdueFlag } = this.state;
      if (overdueFlag) {
        // 逾期还款场景：SSA02
        couponList = (couponList || []).filter((item) => (item.awardType === '216' && item.subUseSceneCode === 'SSA02'));
        dispatchTrackEvent({
          event: EventTypes.EV,
          beaconId: 'repayment.ReduceFee.CouponScene',
          beaconContent: { cus: { couponScene: 'SSA02' } },
        });
      } else {
        // 提前还款场景：SSA08
        prepayFeeRightsCouponList = (couponList || []).filter((item) => (item.awardType === '306'));
        couponList = (couponList || []).filter((item) => (item.awardType === '216' && item.subUseSceneCode === 'SSA08'));
        dispatchTrackEvent({
          event: EventTypes.EV,
          beaconId: 'repayment.ReduceFee.CouponScene',
          beaconContent: { cus: { couponScene: 'SSA08' } },
        });
      }
      if (couponList && couponList.length <= 0) {
        this.setState({
          showRefuseModal: true
        });
        return true;
      }
      this.awardInfoList = couponList || [];
      // 旧券没有返回梯度比例，以此区分新旧券
      this.isNewOverdueCoupon = couponList[0] ? !!((couponList[0].discountGradientList || []).length) : false;
      this.prepayFeeRightsCouponList = prepayFeeRightsCouponList;
      this.hasPrepayFeeRightsCoupon = prepayFeeRightsCouponList.length > 0;
      setStore({ awardInfoList: (couponList || []).slice(0, 1) });// 缓存待使用的博弈减免券信息
    } catch (err) {
      this.setState({ canSubmit: false });
    }
  }

  // 获取客群接口数据
  async getRepayWaiveTrialTrans() {
    try {
      const { overdueFlag } = this.state;
      // 客群接口获取三个梯度
      const params = {
        waiveCustGroup: this.awardInfoList && this.awardInfoList.length && this.awardInfoList[0].waiveRatioTag || '',
        orderNos: this.allBillList && this.allBillList.length ? this.allBillList.map((item) => item.orderNo) : [],
        canWaiveOrderNos: this.allBillList && this.allBillList.length ? this.allBillList.filter((item) => item.canUseManualCoupon === 'Y').map((e) => e.orderNo) : [],
        prepayFeeRightsFlag: this.hasPrepayFeeRightsCoupon ? 'Y' : 'N',
        waiveScene: overdueFlag ? '1' : '2',
        discountGradientList: ((this.awardInfoList || [])[0] || {}).discountGradientList || [],
      };
      const { ret, errCode, errMsg, data } = await Dispatch.repayment.repayWaiveTrialTrans({ ...params }, true);
      if (ret === '1') {
        if (errCode === 'NCR10904') {
          this.setState({
            showNoFeeAmtModal: true
          });
          return;
        }
        Madp.showModal({
          content: '系统繁忙，请稍后再试',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: themeColor,
          success: (res) => {
            if (res.confirm) {
              if (window && (window.history.length === 1)) {
                Madp.closeWebView();
              } else {
                Madp.navigateBack();
              }
            }
          }
        });
        return;
      } else {
        const { actualRepayWaiveDetails, payoffAmt, payoffWaiveAmt } = data || {};
        const formatWaiveList = (actualRepayWaiveDetails || []).filter((item = {}) => Number(item.waiveAmt || 0) !== 0);
        this.setState({
          totalPay: Util.floatAdd(Number(payoffAmt), Number(payoffWaiveAmt)).toFixed(2),
          rulesList: formatWaiveList,
          payoffAmt: payoffAmt || '-',
          payoffWaiveAmt: payoffWaiveAmt || '-',
          reduceFeeCoupon: this.awardInfoList && this.awardInfoList.length && this.awardInfoList[0],
          thresholdAmount: ((formatWaiveList && formatWaiveList.length && formatWaiveList[formatWaiveList.length - 1]) || {}).repayAmt, // 最低门槛金额
        }, () => {
          const { rulesList } = this.state;
          if (payoffWaiveAmt && Number(payoffWaiveAmt) > 0 && !Number.isNaN(Number(payoffWaiveAmt)) && rulesList && rulesList.length > 0 && payoffAmt && Number(payoffAmt) > 0 && !Number.isNaN(Number(payoffAmt))) {
            if (!this.needRefreshData) this.toPayAll();
            this.setState({ canSubmit: true });
          } else {
            this.setState({ canSubmit: false });
          }
        });
      }
    } catch (err) {
      this.setState({ canSubmit: false });
    }
  }

  // 全部还清
  toPayAll() {
    const { payoffAmt } = this.state;
    this.changeAmt(payoffAmt);
    this.setState({ inputAmt: payoffAmt });
  }

  changeAmtEvent(value, keep) {
    this.repayTransTrialApi(value, keep);
  }

  /**
   * 计算并设置倒计时的时间
   */
  calcCountDown() {
    const currentEnv = getEnv();
    // 获取当前的券ID
    const { taskId, expireDate } = this.awardInfoList && this.awardInfoList[0] || {};

    // 判断券ID是否在玩法里面，在就展示倒计时，否则不展示倒计时
    if (activityIds[currentEnv].indexOf(taskId) === -1) return;

    // 计算剩余时间，动派发的券都是1-3小时失效的，不用展示天，只展示时、分
    const currentDateTimestamp = Date.now();
    const hoursLeft = Util.timeMinus(expireDate, currentDateTimestamp, 'hours');
    const secondsLeft = Util.timeMinus(expireDate, currentDateTimestamp, 'seconds');

    // 剩余时间小于0或大于24小时都不展示
    if (secondsLeft <= 0 || hoursLeft > 24) return;

    this.setState({
      time: secondsLeft
    });
  }

  // 倒计时结束的回调
  onCountDownTimeUp = () => {
    // 标记倒计时结束，关闭倒计时且清除上边距
    this.setState({
      time: 0
    });
  }

  onInputAmtFocus() {
    // change在blur之后执行
    // 如果你在blur做了校验，并重置数据，会被change覆盖
    // inputting要放在最后执行，保证之前已经试算完毕
    this.setState({ inputting: true });
  }

  onInputAmtChange(val) {
    if (process.env.TARO_ENV !== 'h5' && this.clickCleared) {
      return;
    }
    const { inputting, payoffAmt } = this.state;
    if (!inputting) return;
    const inputNum = Number(val);
    if (Number.isNaN(inputNum)) {
      this.setState({ inputAmt: val }, () => {
        this.setState({ inputAmt: (val || '').slice(0, (val || '').length - 1) });
      });
      Madp.showToast({ title: '请输入正确的数字', icon: 'none' });
      return;
    }
    if (Number(val) > Number(payoffAmt)) {
      Madp.showToast({
        title: `只需要${payoffAmt}即可还清`,
        icon: 'none',
        duration: 2000,
      });
      this.setState({ inputAmt: val }, () => {
        this.setState({ inputAmt: payoffAmt });
        // 解决showToast自动动画导致的页面滑动问题
        Madp.pageScrollTo({
          scrollTop: 'auto',
          duration: 0,
        });
        setTimeout(() => {
          this.afterClickCleared();
        }, 0);
      });
      return;
    }
    this.setState({ inputAmt: val });
  }

  // 失焦
  async onInputAmtBlur(val) {
    if (this.clickCleared) {
      this.clickCleared = false;
      return;
    }
    // 失焦置顶
    Madp.pageScrollTo({
      scrollTop: 0,
      duration: 50,
    });
    const { payoffAmt } = this.state;
    // 输入非数，就不应该继续往下走，否则传入非数试算必然有问题
    if (Number.isNaN(Number(val))) {
      Madp.showToast({ title: '请输入正确的数字', icon: 'none' });
      this.setState({
        inputAmt: '',
        settleWaiveAmt: '-',
        totalCanWaiveAmt: '-',
        remainAmt: '-',
        lateFee: '-',
        feeReduce: '-',
      });
      return;
    }
    if (Number(val) > Number(payoffAmt) || val <= 0) {
      // 输入金额不符合条件的，失去焦点后先toast后自动填充结清金额，等于0只toast不自动填充
      if (Number(val) !== 0) {
        setTimeout(() => {
          this.toPayAll();
        }, 1800);
      }
      Madp.showToast({
        title: `您仅需还${payoffAmt || '-'}元即可结清，请调整还款金额`,
        icon: 'none',
        duration: 2000
      });
      // 失焦置顶
      setTimeout(() => {
        Madp.pageScrollTo({
          scrollTop: 0,
          duration: 50,
        });
      }, 2000);
    } else if (!val) {
      this.setState({
        settleWaiveAmt: '-',
        totalCanWaiveAmt: '-',
        remainAmt: '-',
        lateFee: '-',
      });
    } else {
      this.changeAmt(val);
    }
  }

  // 点击清除按钮
  afterClickCleared = () => {
    if (process.env.TARO_ENV === 'h5') {
      try {
        const aimEl = (document.querySelectorAll('.input-area-input > .at-input__container > .at-input__input') || [])[0] || {};
        if (aimEl.setSelectionRange) {
          aimEl.focus();
          const { inputAmt } = this.state;
          aimEl.setSelectionRange((inputAmt || '').length, (inputAmt || '').length);
        } else {
          const range = aimEl.createTextRange() || {};
          range.collapse(false);
          range.select();
        }
      } catch (error) {
        console.log(error);
      }
    }
  }

  // 资格券模式试算接口
  async repayTransTrialApi(amtInput, keep = false) {
    try {
      const { reduceFeeCoupon, rulesList, totalPay, thresholdAmount, canSubmit, overdueFlag } = this.state;
      const repaymentAmount = Util.numToStr(amtInput); // 输入的还款金额
      this.setState({ inputAmt: repaymentAmount });
      let waiveAmt = '0';
      // eslint-disable-next-line array-callback-return
      rulesList.some((item) => {
        if (repaymentAmount >= Number(item.repayAmt)) {
          waiveAmt = item.waiveAmt;
          return item;
        }
      });
      const prepayFeeRightsObject = {
        prepayFeeRightsFlag: this.hasPrepayFeeRightsCoupon ? 'Y' : 'N',
        prepayFeeRightDetail: this.hasPrepayFeeRightsCoupon ? {
          ...getFeeInteRightInfo((this.prepayFeeRightsCouponList || [])[0] || {})
        } : null
      };
      this.prepayFeeRightsObject = prepayFeeRightsObject;
      const param = {
        waiveAmt,
        transTotalAmt: repaymentAmount,
        repayMode: overdueFlag ? 'AMT' : 'ADVANCE', // 后台说跟旧息费减免特权的 INTEFEE_WAIVE 区分开,
        // awardInfoList: ((Number(repaymentAmount) < Number(thresholdAmount)) || !((reduceFeeCoupon || {}).awardNo)) ? [] : [reduceFeeCoupon], // 若不满足梯度档位的最低还款门槛，则不带息费减免券试算
        feeInteRightList: ((Number(repaymentAmount) < Number(thresholdAmount)) || !((reduceFeeCoupon || {}).awardNo)) ? [] : [{
          ...getFeeInteRightInfo(reduceFeeCoupon),
        }],
        repayDetailList: this.allBillList && this.allBillList.length ? this.allBillList.map((i) => ({
          orderNo: i.orderNo,
          debtorSplitDetailList: i.debtorSplitDetails,
          repayAmt: i.surplusPayTotalAmt,
          instCnt: i.installTotalCnt
        })) : [],
        ...prepayFeeRightsObject
      };
      const {
        result, repayTrialDetailList = [], settleWaiveAmt, totalCanWaiveAmt, shouldRepayAmt, trialWaiveResultList, totalCanPayAmt
      } = await Dispatch.repayment.repayTransTrial(param);
      // if (result === 'SUC' && Number(totalCanWaiveAmt) > 0) {
      if (result === 'SUC') {
        // 设置提交按钮状态
        if (!canSubmit) {
          this.setState({
            canSubmit: true
          });
        }
        // 输入金额与入账金额不一致，引导填充最低门槛金额（这里不拦截，还款提交会有问题）
        if (Number(totalCanPayAmt) !== Number(repaymentAmount)) {
          this.setState({
            showLimitModal: true,
            canSubmit: false
          });
          return;
        }
        // 可免违约金
        const lateFee = Number((trialWaiveResultList && trialWaiveResultList.filter((item) => item.waiveScene === '04')[0] || {}).actualWaiveAmt || 0).toFixed(2);
        let feeReduce = (Util.floatAdd(Number(settleWaiveAmt || 0), Number(totalCanWaiveAmt || 0))).toFixed(2);
        // 如有免收提前还款违约金资格券，这里的息费不含违约金，需要减掉
        feeReduce = this.hasPrepayFeeRightsCoupon ? (Util.numToStr(feeReduce) - Util.numToStr(lateFee)).toFixed(2) : feeReduce;
        this.setState({
          settleWaiveAmt, // 超限减金额
          totalCanWaiveAmt, // 可减免总额，包括优惠券减免、息费减免金额，不包括超限减免
          lateFee, // 可免违约金
          feeReduce, // 可减免息费
          remainAmt: (Util.numToStr(totalPay) - Util.numToStr(shouldRepayAmt)).toFixed(2) // 剩余待还金额 总代还 - 应还金额
        }, () => {
          if (keep) this.nextStep();
        });
        const newRepayTrialDetailList = repayTrialDetailList.map((i) => {
          const target = this.allBillList && this.allBillList.find((j) => j.orderNo === i.orderNo);
          return {
            ...i,
            instCnt: (target && target.installTotalCnt) || 0
          };
        });
        this.relatedOrderList = newRepayTrialDetailList;
        setStore({ repayTrialDetailList: newRepayTrialDetailList });// 缓存试算后账单明细 针对0:100需求使用
      } else {
        this.setState({
          showErrorModal: true
        });
      }
      // 可还款限制解除
      this.setState({ inputting: false }, () => {
        const { payoffAmt } = this.state;
        if (Number(amtInput || 0) !== Number(payoffAmt || 0)) {
          dispatchTrackEvent({
            event: EventTypes.SO,
            target: this,
            beaconId: 'ShowPayAll',
            beaconContent: { cus: { amount: amtInput } },
          });
        }
      });
    } catch (err) {
      this.setState({ canSubmit: false });
    }
  }

  nextStep(ignoreGuideUse = false) {
    // 由于试算发生在blur阶段，防止试算还未接触就进行下一步，通过inputting判断是否已经试算完毕
    if (this.state.inputting) return;
    const {
      settleWaiveAmt, totalCanWaiveAmt, feeReduce, lateFee, inputAmt, thresholdAmount, overdueFlag
    } = this.state;

    // 这些还是初始值的话，说明试算有问题，不能跳转支付页
    if (settleWaiveAmt === '-' || totalCanWaiveAmt === '-' || feeReduce === '-' || lateFee === '-') return;

    // 未达减免门槛，弹窗提示
    if (!ignoreGuideUse && Number(inputAmt || 0) < Number(thresholdAmount || 0)) {
      this.setState({
        showGuideUse: true,
      }, () => {
        dispatchTrackEvent({
          event: EventTypes.SO,
          target: this,
          beaconId: 'ShowGuideUse',
        });
      });
      return;
    }
    // // 试算后，免提还减免金额小于等于0，则提交接口不传免提还权益信息
    // if (this.hasPrepayFeeRightsCoupon && Number(lateFee || 0) <= 0) {
    //   this.prepayFeeRightsObject = {};
    // }
    // 试算后，优惠券减免金额小于等于0，则提交接口不传优惠券信息
    if ((Number(totalCanWaiveAmt || 0) - Number(lateFee || 0)) <= 0) {
      setStore({ awardInfoList: [] });
    }

    setStore({
      feeReduceInfo: {
        settleWaiveAmt,
        totalCanWaiveAmt,
        feeReduce,
        feeReducedOrderList: this.relatedOrderList,
        prepayFeeRightsObject: this.prepayFeeRightsObject,
        waiveAmount: this.hasPrepayFeeRightsCoupon ? (Util.floatAdd(Number(feeReduce || 0), Number(lateFee || 0))).toFixed(2) : feeReduce,
      },
      selectedBillList: this.allBillList
    });
    this.needRefreshData = true;
    dispatchTrackEvent({
      event: EventTypes.EV,
      target: this,
      beaconId: 'ToRepay',
      beaconContent: { cus: { confirmAmt: Number(inputAmt || 0).toFixed(2) } },
    });
    const url = `/pages/express-repay/index?_windowSecureFlag=1&amount=${Number(inputAmt).toFixed(2)}&billType=fee-reduce&feeReduceOldMode=${overdueFlag ? '1' : '0'}`;
    // H5的小程序渠道页面强制走H5的支付页
    if (process.env.TARO_ENV === 'h5' && this.miniChannelFlag) {
      Taro.navigateTo({
        url,
      });
      return;
    }
    Util.router.push(url);
  }

  // 提供放置挽留弹窗的位置
  async beforeRouteLeave(from, to, next, options) {
    // 不能办理业务时不进入挽留逻辑
    const { showNoFeeAmtModal, showRefuseModal, showPage } = this.state;
    try {
      // 返回还款首页时，触发op交互事件：挽留弹窗+kyc+加码派券
      if ((from.path === to.path) && (this.opRetainOpened !== 'SUCCESS') && !(showNoFeeAmtModal || showRefuseModal || !showPage)) {
        const opRes = await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.feeReduce, () => { this.closeOrBack(); });
        // 标记是否打开过，如选择优惠还款留在当前页，再次进入挽留逻辑，直接next(true),不再进入op
        this.opRetainOpened = opRes;
        // 如未正确打开op，也进行返回兜底
        if (opRes !== 'SUCCESS') {
          this.closeOrBack();
          next(true);
        } else {
          // 如果成功打开op，则不继续导航,取消路由变化；解决二次进入后无法返回的问题
          next(false);
          return;
        }
      } else {
        if (from.path === to.path) {
          this.closeOrBack();
        } else {
          next(true);
        }
      }
    } catch (error) {
      console.log('error:', error);
      if (from.path === to.path) {
        this.closeOrBack();
      } else {
        next(true);
      }
    }
  }

  async opOnPageEvent(eventName, interactionEventCode, closeFn) {
    const { overdueFlag, payoffWaiveAmt } = this.state;
    const { expireDate } = this.awardInfoList && this.awardInfoList[0] || {};
    const currentDateTimestamp = Date.now();
    const hoursLeft = Util.timeMinus(expireDate, currentDateTimestamp, 'hours');
    this.hoursLeft = hoursLeft;
    const { inputAmt } = this.state;
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: feeReduceLedaId,
            overdueFlag: overdueFlag, // 是否逾期
            discountRate: this.getMaxDiscountRate(), // 当前最高减免比例
            couponInfo: { // 现有优惠券信息
              payoffWaiveAmt, // 最高档还清的减免金额
              dayLeft: Math.floor(Util.divide(this.hoursLeft, 24)), // 有效剩余天数
              isNewOverdueCoupon: this.isNewOverdueCoupon // 是否新券
            },
            opOverchargeSuccess: async () => {
              await this.initData();
              await this.repayTransTrialApi(inputAmt);
            }, // 加码成功后，页面刷新展示
            opRetainClose: closeFn // 退出方法
          },
          // 正确打开op后，op组件调用该方法返回‘SUCCESS’
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve('ERROR');
      }
    });
  }

  // 最高优惠减免比例
  getMaxDiscountRate = () => {
    const discountGradientList = ((this.awardInfoList || [])[0] || {}).discountGradientList || [];
    let maxRate = '';
    discountGradientList.map((couponItem) => {
      if (Number(couponItem.discountRate) > Number(maxRate)) {
        maxRate = couponItem.discountRate;
      }
    });
    return maxRate;
  }

  detainPopWindowHandler(type, targetUrl, next) {
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: `Dialog${type}` });
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'DetainDialogGoNext' });
    switch (type) {
      case 'continue': {
        targetUrl && Madp.navigateTo({ url: targetUrl });
        break; // 点击确定按钮
      }
      case 'cancel': {
        typeof next === 'function' && next(true);
        break; // 点击取消回调
      }
      case 'close': {
        typeof next === 'function' && next(false);
        break; // 点击X 关闭弹窗的回调
      }
      case 'none': {
        typeof next === 'function' && next(false);
        break;// 没有配置内容时的回调
      }
      default: {
        typeof next === 'function' && next(true);
        break;
      }
    }
    // 设置一次publicDetainDialog展位关闭，避免后续页面render导致重复弹窗
    refresh({
      pageId: feeReduceLedaId,
      publicDetainDialog: {
        isOpen: false,
        handler: () => { },
      },
      isRequest: false,
    });
    this.closeOrBack();
  }

  closeOrBack = () => {
    if (process.env.TARO_ENV === 'h5') {
      if (isMuapp() && Url.getParam('repaymentFlag') !== 'Y') {
        Madp.closeWebView();
      } else {
        Madp.navigateBack({ delta: 1 }).then().catch(() => {
          Madp.closeWebView();
        });
      }
    } else {
      Madp.navigateBack({ delta: 1 }).then().catch(() => {
        const launchUrl = '%2Fpages%2Findex%2Findex';
        Madp.reLaunch({
          url: decodeURIComponent(launchUrl),
        });
      });
    }
  }


  // 微信自定义导航栏挽留弹框
  beforeMiniRouteLeave = async () => {
    // 不能办理业务时不进入挽留逻辑
    const { showNoFeeAmtModal, showRefuseModal, showPage } = this.state;
    try {
      // 返回还款首页时，触发op交互事件：挽留弹窗+kyc+加码派券
      if ((this.opRetainOpened !== 'SUCCESS') && !(showNoFeeAmtModal || showRefuseModal || !showPage)) {
        const opRes = await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.feeReduce, () => { this.closeOrBack(); });
        // 标记是否打开过，如选择优惠还款留在当前页，再次进入挽留逻辑，直接next(true),不再进入op
        this.opRetainOpened = opRes;
        // 如未正确打开op，也进行返回兜底
        if (opRes !== 'SUCCESS') {
          this.closeOrBack();
        } else {
          // 如果成功打开op，则不继续导航,取消路由变化；解决二次进入后无法返回的问题
          return;
        }
      } else {
        this.closeOrBack();
      }
    } catch (error) {
      console.log('error:', error);
      this.closeOrBack();
    }
  }

  get totalFee() {
    const { feeDetail, totalFee } = this.state;
    if (feeDetail && feeDetail.fineAmt && Number(feeDetail.fineAmt) > 0) {
      return Util.floatAdd(Number(feeDetail.fineAmt), Number(totalFee)).toFixed(2);
    } else {
      return totalFee;
    }
  }

  get totalRepayAndWaiveAmt() {
    const repayAndWaiveAmt = {
      totalRepayAmt: '-',
      totalWaiveAmt: '-',
    };
    const { inputAmt, feeReduce, lateFee } = this.state;
    if (feeReduce === '-' || lateFee === '-') {
      return repayAndWaiveAmt;
    }
    repayAndWaiveAmt.totalRepayAmt = Util.floatAdd(Number(inputAmt || 0), Util.floatAdd(Number(feeReduce || 0), Number(lateFee || 0))).toFixed(2);
    repayAndWaiveAmt.totalWaiveAmt = Util.floatAdd(Number(feeReduce || 0), Number(lateFee || 0)).toFixed(2);
    return repayAndWaiveAmt;
  }

  get feeReduceTip() {
    const {
      inputting, inputAmt, payoffAmt, rulesList, thresholdAmount,
    } = this.state;
    let tipParam = {
      showTip: false,
    };
    if (inputting) return tipParam;
    if (Number(inputAmt || 0) < Number(payoffAmt || 0)) {
      let nearestCeilAmt = '';
      if (Number(inputAmt || 0) < Number(thresholdAmount || 0)) {
        nearestCeilAmt = thresholdAmount;
      } else {
        nearestCeilAmt = ((rulesList || []).filter((item = {}, i) => ((Number(item.repayAmt || 0) > Number(inputAmt || 0)) && Number((rulesList[i + 1] || {}).repayAmt || 0) && (Number(inputAmt || 0)) >= Number((rulesList[i + 1] || {}).repayAmt || 0)))[0] || {}).repayAmt || '';
      }
      const nearestCeilWaive = ((rulesList || []).filter((item = {}) => (item.repayAmt === nearestCeilAmt))[0] || {}).waiveAmt || '';
      if (Number(nearestCeilAmt || 0) > Number(inputAmt || 0) && Number(nearestCeilWaive || 0)) {
        tipParam = {
          showTip: true,
          supplyPayAmt: Util.floatMinus(Number(nearestCeilAmt || 0), Number(inputAmt || 0)),
          nearestCeilWaive: Number(nearestCeilWaive || 0)
        };
      }
    }
    return tipParam;
  }

  // 渲染待还金额说明弹窗项
  renderModalItem = (title, param) => {
    return (Number(param) && Number(param) > 0 ? (
      <MUView className={title === '总待还' ? 'Detail-modal-content-item Detail-modal-content-item--special' : 'Detail-modal-content-item'}>
        <MUText>{title}</MUText>
        <MUText>{Util.numToStr(param) || 0}元</MUText>
      </MUView>
    ) : null
    );
  }

  render() {
    const {
      showNoFeeAmtModal, showRefuseModal, showPage, showErrorModal, showLimitModal, thresholdAmount,
      payoffWaiveAmt, rulesList, payoffAmt,
      time, totalPay, remainAmt, inputAmt, canSubmit,
      showDetailModal, overdueFlag, isDueTagCust, capitalAmt, feeDetail, courtCostBalance, inputting, showGuideUse
    } = this.state;
    const formatRuleList = (rulesList || []).filter((item = {}) => Number(item.waiveAmt) > 0);

    const businessPluginFooterData = {
      stateData: this.state,
    };

    // 写在这里是为了不渲染页面内容，只展示弹窗
    if (showNoFeeAmtModal) {
      return (<MUModal
        type="tip"
        beaconId="NoFeeAmtModal"
        className="nofeeAmt-modal"
        title="暂无待还息费"
        isOpened={showNoFeeAmtModal}
        content="您暂无待还息费，可前往“全部待还”页面查账还款"
        confirmText="全部待还"
        cancelText="我知道了"
        closeOnClickOverlay={false}
        onConfirm={() => {
          // 跳转全部待还页
          Util.router.replace(repayServiceColumnsUrls.BillListAll);
        }}
        onCancel={() => {
          if (window && (window.history.length === 1)) {
            Madp.closeWebView();
          } else {
            Madp.navigateBack();
          }
        }}
      />);
    }
    if (showRefuseModal) {
      return (<MUModal
        type="tip"
        title="温馨提示"
        beaconId="RefuseModal"
        className="refuse-modal"
        isOpened={showRefuseModal}
        content="您当前暂不符合办理条件，请前往“还款”页面完成还款"
        confirmText="知道了"
        closeOnClickOverlay={false}
        onConfirm={() => {
          if (window && (window.history.length === 1)) {
            Madp.closeWebView();
          } else {
            Madp.navigateBack();
          }
        }}
      />);
    }
    if (!showPage) {
      return <MUView className="pages-bg reduce-fee" />;
    }
    return (
      <MUView>
        <MUNavBarWeapp
          className="fee-reduce-navbar"
          title="还款优惠"
          leftArea={[
            {
              type: 'icon',
              value: 'back',
              onClick: this.beforeMiniRouteLeave
            }
          ]}
        />
        <MUView className={(this.feeReduceTip || {}).showTip ? 'pages-bg reduce-fee reduce-fee--more' : 'pages-bg reduce-fee'}>
          <MUView className="reduce-fee__rules">
            <MUView className="reduce-fee__rules__point">
              <MUView className="point__text">还得多，减的多{this.hasPrepayFeeRightsCoupon ? '，免收违约金' : ''}</MUView>
              <MUView className="point__max-deduct">还款最高优惠{payoffWaiveAmt}元</MUView>
            </MUView>
            <MUView className="reduce-fee__rules__content">
              <MUView className="content__picture">
                <MUImage src={rulePicture} />
              </MUView>
              <MUView className="content__title">
                <MUView className="content__title__left">支付金额</MUView>
                <MUView className="content__title__right">优惠金额</MUView>
              </MUView>
              <MUView className="content__detail">
                {
                  formatRuleList.map((rule, i) => (
                    <MUView className={(i === formatRuleList.length - 1) ? 'content__detail__rule content__detail__rule--final' : 'content__detail__rule'}>
                      <MUView>{rule.repayAmt}元{Number(rule.repayAmt) === Number(payoffAmt) ? ' (还清)' : '以上'}</MUView>
                      <MUView>
                        <MUText className={i === 0 ? 'content__detail__rule--special' : ''}>{rule.waiveAmt}</MUText>元
                      </MUView>
                    </MUView>
                  ))
                }
              </MUView>
            </MUView>
          </MUView>
          <MUView className="reduce-fee__amount">
            <MUView className="reduce-fee__amount__wait" style={{ marginTop: time > 0 ? `${10}px` : 0 }}>
              <MUView>总待还 {totalPay}元丨剩余待还 {remainAmt}元</MUView>
              <MUView
                className="wait__info"
                beaconId="DetailClick"
                onClick={() => setTimeout(() => this.setState({ showDetailModal: true }), 100)}
              >
                <MUIcon value="info" size="15" color="#808080" />
              </MUView>
            </MUView>
            <MUView className="reduce-fee__amount__edit">
              <MUView className="edit__symbol">￥</MUView>
              <MUView className="edit__num">
                <MUInput
                  className={inputAmt ? 'input-area-input' : 'input-area-input input-area-input--empty'}
                  beaconId="AmtInput"
                  value={inputAmt}
                  type="digit"
                  placeholder="请输入还款金额"
                  onFocus={() => this.onInputAmtFocus()}
                  onChange={(val) => this.onInputAmtChange(val)}
                  onBlur={(val) => {
                    setTimeout(() => {
                      this.onInputAmtBlur(val);
                    }, process.env.TARO_ENV === 'h5' ? 10 : 100);
                  }}
                  onConfirm={(val) => {
                    setTimeout(() => {
                      this.onInputAmtBlur(val);
                    }, 10);
                  }}
                  maxLength={9}
                />
                {(!inputting && Number(inputAmt) === Number(payoffAmt)) ? (
                  <MUImage
                    className="edit-img"
                    src="https://file.mucfc.com/zlh/3/0/202305/20230518201828520342.png"
                  />
                ) : null}
              </MUView>
              {(inputting && inputAmt) ? (
                <MUView
                  className="edit__operate"
                  beaconId="ClickClear"
                  onClick={() => {
                    this.clickCleared = true;
                    this.setState({ inputAmt: '' }, () => {
                      this.afterClickCleared();
                    });
                  }}
                >
                  <MUIcon value="close" size="14" color="#A6A6A6" />
                </MUView>
              ) : null}
              {(!inputting && Number(inputAmt) !== Number(payoffAmt)) ? (
                <MUView
                  className="edit__operate edit__operate--text edit__operate--special"
                  beaconId="ClickPayAll"
                  onClick={() => {
                    this.toPayAll();
                  }}
                > 全部还清</MUView>
              ) : null}
            </MUView>
            {!inputting ? (
              <MUView className="reduce-fee__amount__final">
                <MUView className="final__item">
                  <MUView className="final__item__left">总还款金额</MUView>
                  <MUView className="final__item__right">{(this.totalRepayAndWaiveAmt || {}).totalRepayAmt}元</MUView>
                </MUView>
                <MUView className="final__item">
                  <MUView className="final__item__left">优惠（不含本金）</MUView>
                  <MUView className="final__item__right final__item__right--special">-{(this.totalRepayAndWaiveAmt || {}).totalWaiveAmt}元</MUView>
                </MUView>
              </MUView>
            ) : null}
            <MUView className="reduce-fee__amount__count-down">
              <CountDown
                time={time}
                format={{
                  hours: '时',
                  minutes: '分',
                  seconds: '秒'
                }}
                onTimeUpClose
                onTimeUp={this.onCountDownTimeUp}
              />
            </MUView>
          </MUView>
          <MUView className="reduce-fee__submit">
            {(this.feeReduceTip || {}).showTip ? (
              <MUView className="reduce-fee__submit__tip">
                再还<MUText className="reduce-fee__submit__tip--special">{(this.feeReduceTip || {}).supplyPayAmt}</MUText>可减<MUText className="reduce-fee__submit__tip--special">{(this.feeReduceTip || {}).nearestCeilWaive}</MUText>元，仅可享受一次优惠
              </MUView>
            ) : null}
            <MUView className="reduce-fee__submit__button">
              <MUButton
                type="primary"
                beaconId="Submit"
                disabled={!inputAmt || !canSubmit || Number(inputAmt) === 0}
                onClick={() => this.nextStep()}
              >立即支付</MUButton>
            </MUView>
          </MUView>
          <BusinessPlugin
            moduleName="repayment"
            pageType="feeReduce"
            type="footer"
            data={businessPluginFooterData}
          />
          <MUModal
            type="warning"
            beaconId="RepayTrialErrorModal"
            className="trialError-modal"
            isOpened={showErrorModal}
            content="还款信息已过期，请关闭当前页面后重新提交还款"
            confirmText="我知道了"
            closeOnClickOverlay={false}
            onConfirm={() => {
              if (window && (window.history.length === 1)) {
                Madp.closeWebView();
              } else {
                Madp.navigateBack();
              }
            }}
          />
          <MUModal
            type="tip"
            beaconId="RepayLimitModal"
            className="repayLimit-modal"
            title="还款金额不足"
            isOpened={showLimitModal}
            content={`请修改还款金额，需还款${thresholdAmount}元以上`}
            confirmText="一键修改"
            cancelText="放弃优惠"
            closeOnClickOverlay={false}
            onConfirm={() => {
              this.setState({
                showLimitModal: false
              }, () => {
              // 自动修改为最低门槛金额
                this.changeAmt(thresholdAmount);
                this.setState({ inputAmt: thresholdAmount });
              });
            }}
            onCancel={() => {
              if (window && (window.history.length === 1)) {
                Madp.closeWebView();
              } else {
                Madp.navigateBack();
              }
            }}
          />
          <MUModal
            beaconId="Detail"
            className="Detail"
            isOpened={showDetailModal}
            closeOnClickOverlay={false}
          >
            <MUView className="Detail-title">总待还金额说明</MUView>
            <MUView className="Detail-modal-content">
              {
              (overdueFlag || isDueTagCust === 'Y') ? (
                <MUView>
                  {this.renderModalItem('总待还本金', capitalAmt)}
                  {this.renderModalItem('利息', feeDetail.interest)}
                  {this.renderModalItem('分期手续费', feeDetail.periodFee)}
                  {this.renderModalItem('罚息', feeDetail.fineAmt)}
                  {this.renderModalItem('平台服务费', feeDetail.oneTimeFee)}
                  {this.renderModalItem('司法处置费', courtCostBalance)}
                  {this.renderModalItem('总待还', totalPay)}
                </MUView>
              ) : (
                <MUView>
                  {this.renderModalItem('总待还本金', feeDetail.surplusPayTotalPrincipalAmt)}
                  {this.renderModalItem('利息', feeDetail.surplusPayTotalInteAmt)}
                  {this.renderModalItem('分期手续费', feeDetail.surplusPayTotalPeriodFeeAmt)}
                  {this.renderModalItem('罚息', feeDetail.fineAmt)}
                  {this.renderModalItem('提前还违约金', feeDetail.surplusPayTotalPrepayFeeAmt)}
                  {this.renderModalItem('平台服务费', feeDetail.oneTimeFee)}
                  {this.renderModalItem('司法处置费', courtCostBalance)}
                  {this.renderModalItem('总待还', totalPay)}
                </MUView>
              )
            }
            </MUView>
            <MUView className="Detail-btnContent">
              <MUButton
                className="Detail-btn brand-text"
                beaconId="DetailConfirm"
                type="text"
                onClick={() => this.setState({ showDetailModal: false })}
              >
                我知道了
              </MUButton>
            </MUView>
          </MUModal>
          <MUModal
            beaconId="GuideUseModal"
            className="reduce-fee__guide-use"
            isOpened={showGuideUse}
            closeOnClickOverlay={false}
          >
            <MUView className="reduce-fee__guide-use__title">
              有<MUText className="reduce-fee__guide-use__title--special">1</MUText>次优惠机会未使用
            </MUView>
            <MUView className="reduce-fee__guide-use__desc">
              再还<MUText className="reduce-fee__guide-use__desc--special">{(this.feeReduceTip || {}).supplyPayAmt}元</MUText>可优惠<MUText className="reduce-fee__guide-use__desc--special">{(this.feeReduceTip || {}).nearestCeilWaive}元</MUText>
            </MUView>
            <MUButton
              type="primary"
              className="reduce-fee__guide-use__confirm"
              beaconId="GuideUseModalConfirm"
              onClick={() => {
              // 修改为最低门槛金额
                this.changeAmt(thresholdAmount, true);
                this.setState({ inputAmt: thresholdAmount, showGuideUse: false });
              }}
            >
              优惠还款{Number(thresholdAmount || 0)}元
            </MUButton>
            <MUButton
              type="secondary"
              className="reduce-fee__guide-use__cancel"
              beaconId="GuideUseModalCancel"
              onClick={() => {
                this.setState({ showGuideUse: false });
                this.nextStep(true);
              }}
            >
              放弃优惠，继续还款
            </MUButton>
            <MUView
              className="reduce-fee__guide-use__close"
              beaconId="GuideUseModalClose"
              onClick={() => {
                this.setState({ showGuideUse: false });
              }}
            >
              <MUIcon value="close2" size="16" color="#808080" />
            </MUView>
          </MUModal>
        </MUView>
        {/* 交互式运营组件 */}
        <OpRepayment pageId={feeReduceLedaId} opEventKey="opPageLeave" />
      </MUView>
    );
  }
}
