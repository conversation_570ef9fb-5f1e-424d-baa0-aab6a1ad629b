@import '../../components/weapp/index.scss';

.reduce-fee {
  min-height: 100vh;
  background-image: url('https://file.mucfc.com/ebn/3/0/202406/202406191014094f133d.png');
  background-size: contain;
  background-repeat: no-repeat;
  padding-bottom: 160px;
  &--more {
    padding-bottom: 220px;
  }
  &__rules {
    overflow: hidden;
    &__point {
      margin: 40px 0 48px;
      text-align: center;
      color: #fff;
      .point {
        &__text {
          font-size: 30px;
          line-height: 30px;
          font-weight: 400;
          opacity: 0.8;
        }
        &__max-deduct {
          margin-top: 24px;
          font-size: 44px;
          line-height: 44px;
          font-weight: 900;
        }
      }
    }
    &__content {
      margin: 0 20px 0;
      background-image: linear-gradient(180deg, #EEF3FC 0%, #FFFFFF 21%);
      border-radius: 26px;
      overflow: hidden;
      .content {
        &__picture {
          margin: 0 auto;
          width: 264px;
          height: 60px;
          .taro-img, image {
            width: 100%;
            height: 100%;
          }
        }
        &__title {
          margin: 30px 60px 0 60px;
          display: flex;
          justify-content: space-between;
          font-size: 32px;
          line-height: 32px;
          color: #333;
          font-weight: 500;
        }
        &__detail {
          margin: 24px 30px 30px;
          border: 1PX solid #D8D8D8;
          border-radius: 16px;
          background: #FCFCFC;
          &__rule {
            padding: 22px 30px;
            display: flex;
            justify-content: space-between;
            font-size: 26px;
            line-height: 26px;
            color: #333;
            font-weight: 400;
            border-bottom: 1PX solid #D8D8D8;
            &--final {
              border-bottom: none;
            }
            &--special {
              color: #FF8844;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
  &__amount {
    margin: 20px;
    padding: 50px 30px 30px;
    background: #fff;
    border-radius: 26px;
    position: relative;
    &__wait {
      display: flex;
      align-items: center;
      font-size: 28px;
      line-height: 30px;
      color: #333;
      font-weight: 400;
      .wait__info {
        margin-left: 10px;
        width: 30px;
        height: 30px;
        font-size: 0;
      }
    }
    &__edit {
      padding: 33px 0 20px;
      display: flex;
      align-items: center;
      border-bottom: 1PX solid #D8D8D8;
      .edit {
        &__symbol {
          font-size: 54px;
          line-height: 54px;
          color: #333;
          font-weight: 600;
        }
        &__num {
          margin-left: 8px;
          // max-width: 55%;
          position: relative;
          .at-input {
            padding: 0;
            input {
              min-width: 468px;
              height: 80px;
              padding: 0;
              font-size: 80px;
              line-height: 80px;
              font-family: DINAlternate-Bold;
              color: #333;
              font-weight: 700;
              border: none;
              &::-moz-placeholder {
                font-size: 54px;
                color: #A6A6A6;
              }
    
              &::-ms-input-placeholder {
                font-size: 54px;
                color: #A6A6A6;
              }
    
              &::-webkit-input-placeholder {
                font-size: 54px;
                color: #A6A6A6;
              }
            }
            &:after {
              border: none !important;
            }
          }
          .edit-img {
            position: absolute;
            width: 36px;
            height: 36px;
            top: 30px;
            right: 0;
            pointer-events: none;
          }
        }
        &__operate {
          min-width: 120px;
          font-size: 0;
          text-align: right;
          &--text {
            font-size: 28px;
            line-height: 28px;
            color: #808080;
            font-weight: 400;
          }
          &--special {
            color: #3477FF;
          }
        }
      }
    }
    &__final {
      .final__item {
        margin-top: 30px;
        display: flex;
        justify-content: space-between;
        font-size: 28px;
        line-height: 30px;
        color: #333;
        font-weight: 400;
        &__left--special {
          color: #FF8844;
        }
        &__right--special {
          color: #FF8844;
        }
      }
    }
    &__count-down {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  &__submit {
    position: fixed;
    width: 100%;
    bottom: 0;
    &__tip {
      padding: 16px 0;
      background: #FFEDE3;
      font-size: 28px;
      line-height: 28px;
      color: #333;
      font-weight: 400;
      text-align: center;
      &--special {
        color: #FF8844
      }
    }
    &__button {
      padding: 30px;
      background: #fff;
    }
  }

  .Detail {
    .mu-modal__container {
      padding-top: 40px;
    }
    &-title {
      margin-bottom: 10px;
      font-size: 36px;
      line-height: 36px;
      color: #333;
      font-weight: 500;
      text-align: center;
    }
  
    &-modal-content {
      margin-bottom: 40px;
      padding: 0 30px;
      font-size: 30px;
      color: #333333;
      overflow: hidden;
  
      &-item {
        margin-top: 30px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        font-size: 32px;
        line-height: 32px;
        color: #333;
        font-weight: 400;
        &--special {
          color: #FF8844;
        }
      }
    }
  
    &-btnContent {
      display: flex;
      justify-content: space-evenly;
      // margin-bottom: 40px;
      height: 96px; // 之前没设置
    }
  
    &-btn {
      width: 240px;
      position: absolute;
      border-radius: 0;
      border: none;
      border-top: 1PX solid #E5E5E5;
      left: 0;
      bottom: 0;
      width: 100%;
    }
  }

  &__guide-use {
    .mu-modal__container {
      padding: 40px 10px 16px;
    }
    &__title {
      font-size: 36px;
      line-height: 54px;
      color: #333;
      font-weight: 600;
      text-align: center;
      &--special {
        color: #FF8844;
      }
    }
    &__desc {
      margin: 12px 0 40px;
      font-size: 28px;
      line-height: 42px;
      color: #808080;
      font-weight: 400;
      text-align: center;
      &--special {
        color: #FF8844;
      }
    }

    &__confirm {
      height: 88px;
      width: 480px;
      .at-button__text {
        padding: 17px 0;
        font-size: 36px;
        line-height: 54px;
      }
    }

    &__cancel {
      margin: 2px auto;
      border: none;
      background-color: #FFF !important;
      color: #808080 !important;
      font-size: 32px;
      border-color: #FFF !important;
      .at-button__text {
        font-size: 32px;
      }
    }

    .at-button {
      height: 88px;
    }

    &__close {
      position: absolute;
      top: 30px;
      right: 30px;
      width: 32px;
      height: 32px;
      font-size: 0;
    }
  }

  &__leave-stay {
    .mu-modal__container {
      width: 480px;
      padding: 40px;
    }
    &__title {
      font-size: 36px;
      line-height: 54px;
      color: #333;
      font-weight: 600;
      text-align: center;
      &--special {
        color: #FF8844;
      }
    }
    &__desc {
      margin: 12px 0 40px;
      font-size: 28px;
      line-height: 42px;
      color: #808080;
      font-weight: 400;
      text-align: center;
      &--special {
        color: #FF8844;
      }
    }

    &__buttons {
      display: flex;
      justify-content: space-between;
      .at-button {
        padding: 0;
        margin: 0;
      }
      .buttons__confirm,
      .buttons__cancel {
        height: 88px;
        width: 230px;
        .at-button__text {
          padding: 17px 30px;
          font-size: 36px;
          line-height: 54px;
        }
      }
    }

    &__close {
      position: absolute;
      top: 30px;
      right: 30px;
      width: 32px;
      height: 32px;
      font-size: 0;
    }
  }
}

.fee-reduce-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}

