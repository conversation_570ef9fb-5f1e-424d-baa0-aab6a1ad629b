import {
  MUVie<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Form,
  <PERSON>UInput,
  MURadio
} from '@mu/zui';

const Test2 = () => {
  return (
    <MUView className="test2-page" style={{ backgroundColor: '#F3F3F3' }}>
      {/* 返回按钮和标题 */}
      <MUView
        className="header"
        style={{ padding: '64px 30px 0' }}
      >
        <MUView className="back-button">
          {/* 返回箭头图标 */}
        </MUView>
        <MUText
          className="title"
          style={{
            fontSize: 36,
            fontWeight: 'bold',
            color: '#333333',
            textAlign: 'center',
            marginLeft: 120
          }}
        >
          身份认证
        </MUText>
      </MUView>

      {/* 提示文字 */}
      <MUView
        className="prompt"
        style={{ padding: '0 30px', marginTop: 40 }}
      >
        <MUText
          className="step"
          style={{
            fontSize: 40,
            fontWeight: 'bold',
            color: '#000000'
          }}
        >
          只需2步
        </MUText>
        <MUText
          className="time"
          style={{
            fontSize: 40,
            fontWeight: 'bold',
            color: '#3477FF',
            marginLeft: 20
          }}
        >
          最快1分钟获得额度
        </MUText>
      </MUView>

      {/* 安全提示 */}
      <MUView
        className="safety-tip"
        style={{
          backgroundColor: '#FFFFFF',
          padding: '30px',
          marginTop: 40,
          flexDirection: 'row',
          alignItems: 'center'
        }}
      >
        <MUText
          style={{
            fontSize: 24,
            color: '#808080'
          }}
        >
          招联全力保障您的个人信息安全，请放心填写
        </MUText>
      </MUView>

      {/* 个人信息表单 */}
      <MUForm
        className="info-form"
        style={{
          backgroundColor: '#FFFFFF',
          marginTop: 20,
          padding: '30px'
        }}
      >
        <MUInput
          name="realName"
          label="真实姓名"
          value="周星驰"
          placeholder=""
          labelStyle={{ fontSize: 32, fontWeight: 'bold', color: '#333333' }}
          inputStyle={{ fontSize: 28, color: '#333333' }}
        />
        <MUInput
          name="idNumber"
          label="身份证号"
          value="340939303930303049"
          placeholder=""
          rightIcon="delete"
          labelStyle={{ fontSize: 32, fontWeight: 'bold', color: '#333333' }}
          inputStyle={{ fontSize: 28, color: '#333333' }}
          style={{ marginTop: 40 }}
        />
        <MUText
          className="forgot-id"
          style={{
            fontSize: 24,
            color: '#808080',
            textAlign: 'right',
            marginTop: 20
          }}
        >
          忘记身份证号
        </MUText>
      </MUForm>

      {/* 身份选择 */}
      <MUView
        className="identity-selection"
        style={{
          backgroundColor: '#FFFFFF',
          marginTop: 20,
          padding: '30px'
        }}
      >
        <MUText
          className="section-title"
          style={{
            fontSize: 32,
            fontWeight: 'bold',
            color: '#333333'
          }}
        >
          身份选择
        </MUText>
        <MURadio
          name="identity"
          options={[
            { label: '我是学生', value: 'student' },
            { label: '我已工作', value: 'worker' }
          ]}
          value="worker"
          style={{ marginTop: 30 }}
          selectedStyle={{ backgroundColor: '#3477FF', borderColor: '#3477FF' }}
          labelStyle={{ fontSize: 28, color: '#808080' }}
          selectedLabelStyle={{ color: '#FFFFFF' }}
        />
      </MUView>

      {/* 月收入选择 */}
      <MUView
        className="income-selection"
        style={{
          backgroundColor: '#FFFFFF',
          marginTop: 20,
          padding: '30px'
        }}
      >
        <MUText
          className="section-title"
          style={{
            fontSize: 32,
            fontWeight: 'bold',
            color: '#333333'
          }}
        >
          月收入
        </MUText>
        <MURadio
          name="income"
          options={[
            { label: '3千以下', value: 'under3k' },
            { label: '3千-5千', value: '3k-5k' },
            { label: '5千-8千', value: '5k-8k' },
            { label: '8千-1万', value: '8k-10k' },
            { label: '1万-2万', value: '10k-20k' },
            { label: '2万以上', value: 'over20k' }
          ]}
          value="5k-8k"
          multiple={false}
          style={{ marginTop: 30 }}
          selectedStyle={{ backgroundColor: '#3477FF', borderColor: '#3477FF' }}
          labelStyle={{ fontSize: 28, color: '#808080' }}
          selectedLabelStyle={{ color: '#FFFFFF' }}
        />
      </MUView>

      {/* 底部按钮 */}
      <MUButton
        className="next-button"
        type="primary"
        style={{
          backgroundColor: '#3477FF',
          borderRadius: 100,
          height: 100,
          margin: '40px 30px',
          justifyContent: 'center'
        }}
      >
        <MUText
          style={{
            fontSize: 36,
            fontWeight: 'bold',
            color: '#FFFFFF'
          }}
        >
          下一步
        </MUText>
      </MUButton>
    </MUView>
  );
};

export default Test2;
