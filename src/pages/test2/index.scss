.test2-page {
  padding: 0 16px;
  background-color: #fff;
  min-height: 100vh;

  .status-bar {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    color: #999;
  }

  .header {
    position: relative;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;

    .back-button {
      position: absolute;
      left: 0;
      width: 24px;
      height: 24px;
      // 这里应该添加返回箭头图标样式
    }

    .title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
  }

  .prompt {
    text-align: center;
    margin-bottom: 24px;

    .step {
      font-size: 16px;
      color: #666;
    }

    .time {
      font-size: 14px;
      color: #999;
      margin-top: 4px;
    }
  }

  .info-form {
    margin-bottom: 24px;

    .forgot-id {
      font-size: 12px;
      color: #1890FF;
      text-align: right;
      margin-top: 8px;
    }
  }

  .section-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 12px;
    font-weight: bold;
  }

  .identity-selection,
  .income-selection {
    margin-bottom: 24px;
  }

  .next-button {
    position: fixed;
    bottom: 32px;
    left: 16px;
    right: 16px;
    height: 44px;
    background-color: #1890FF;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

    .mu-text {
      color: #fff;
      font-size: 16px;
    }
  }
}
