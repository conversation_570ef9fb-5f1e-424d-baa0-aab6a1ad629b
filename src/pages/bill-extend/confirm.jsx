import Taro, { Component } from '@tarojs/taro';
import {
  MUView, MUButton, MUImage, MUText
} from '@mu/zui';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import classNames from 'classnames';
import pageHoc from '@utils/pageHoc';
import ExtendListPlan from './components/extend-list-plan';
import Dispatch from '@api/actions';
import { getStore } from '@api/store';
import Madp from '@mu/madp';
import Util from '@utils/maxin-util';
import { sloganUrl, miniProgramChannel } from '@utils/constants';
import { Url } from '@mu/madp-utils';

function formatPrice(price) {
  return (Number(price)).toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

function isValidPrice(price) {
  const strippedPrice = typeof price === 'string' ? price.trim() : price;
  return strippedPrice !== '' && !Number.isNaN(+strippedPrice) && +strippedPrice >= 0;
}

import './confirm.scss';

import specialCard from '@components/assets/img/xyh_special_card.png';
import normalCard from '@components/assets/img/xyh_normal_card.png';
import privilege from '@components/assets/img/xyh_privilege.png';
import subcard1 from '@components/assets/img/xyh_subcard1.png';
import subcard2 from '@components/assets/img/xyh_subcard2.png';
import arrow from '@components/assets/img/xyh_arrow.png';

const ConfirmState = {
  remainTheSame: 0,
  noNeedToRepayNow: 1,
  repayless: 2,
  repaymore: 3
};

@track({ event: EventTypes.PO }, {
  pageId: 'ExtendConfirm',
  dispatchOnMount: true,
})
@pageHoc({ title: '延后还' })
export default class ExtendConfirm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showPage: false,
      isSpecialCard: false,
      confirmState: ConfirmState.remainTheSame,
      duePayTotalAmt: '',
      totalDelayAmt: '',
      beforeNeedRepayAmt: '',
      extendListPlanData: null
    };
    this.dispatchFlag = false;
    this.repaymentFlag = Url.getParam('repaymentFlag') || ''; // 是否从还款模块跳入标识
  }

  async componentDidMount() {
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
    this.extendRepayInfo = getStore('extendRepayInfo');
    this.isVplus = getStore('isVplus') || false; // 是否为v+会员

    Promise.all([
      this.delayRepayCal(),
      this.queryCustCaseDetail()
    ]).then(([delayRepayCalStates, queryCustCaseDetailStates]) => {
      this.setState({
        ...delayRepayCalStates,
        ...queryCustCaseDetailStates
      }, () => {
        this.compareNowAndBefore();
      });
    }).catch((ignore) => {});
  }

  config = {
    navigationBarTitleText: '延后还',
  }

  // 下月还试算
  delayRepayCal() {
    return new Promise(async (resolve, reject) => {
      const { delayRepayAdjustInfo } = this.extendRepayInfo || {};
      const {
        adjustCnt,
        orderNoList,
        needRepayType
      } = delayRepayAdjustInfo || {};

      const { ret, errCode, errMsg, data } = await Dispatch.repayment.delayRepayCal({
        orderNoList,
        adjustCnt,
        needRepayType,
      });
      if (ret === '0') {
        const {
          duePayTotalAmt, firstPayDate, repayPlanAfterDelay, totalDelayAmt
        } = data || {};
        if (isValidPrice(duePayTotalAmt)) {
          resolve({
            duePayTotalAmt,
            totalDelayAmt,
            extendListPlanData: {
              title: '还款计划变更为',
              firstPayDate,
              repayPlanAfterDelay
            }
          });
        } else {
          this.dispatchFlag = true;
          const urlParam = `serviceType=extend&status=3&subStatus=3&repaymentFlag=${this.repaymentFlag}`;
          Madp.redirectTo({
            url: `/pages/service-result/index?${urlParam}`
          });
          reject(new Error('duePayTotalAmt值不合法'));
        }
      } else {
        this.dispatchFlag = true;
        let urlParam = `serviceType=extend&status=3&repaymentFlag=${this.repaymentFlag}`;
        // 准入拒绝使用返回码方式
        if (errCode === 'UMDP02724') { // UMDP02724 不符合办理条件；需前端特殊处理展示
          urlParam = `${urlParam}&subStatus=1`;
        } else if (errMsg) {
          urlParam = `${urlParam}&subStatus=1&errMsg=${errMsg}`;
        }
        Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}` });
        reject(new Error(errMsg));
      }
    });
  }

  // 再分期案件详情查询
  queryCustCaseDetail() {
    return new Promise(async (resolve, reject) => {
      const { applyNo = '' } = this.extendRepayInfo || {};
      const { ret, errMsg, data } = await Dispatch.repayment.queryCustCaseDetail({
        applyNo,
        ignoreLoading: true
      });
      if (ret === '0') {
        const { delayRepayPackageInfo } = data || {};
        const { isMajorEvent, delayRepayApplyInfo } = delayRepayPackageInfo || {};
        const { beforeNeedRepayAmt } = delayRepayApplyInfo || {};

        if (isValidPrice(beforeNeedRepayAmt)) {
          resolve({
            beforeNeedRepayAmt,
            isSpecialCard: isMajorEvent === 'Y'
          });
        } else {
          this.dispatchFlag = true;
          const urlParam = `serviceType=extend&status=3&subStatus=3&repaymentFlag=${this.repaymentFlag}`;
          Madp.redirectTo({
            url: `/pages/service-result/index?${urlParam}`
          });
          reject(new Error('beforeNeedRepayAmt值不合法'));
        }
      } else {
        this.dispatchFlag = true;
        const urlParam = `serviceType=extend&status=3&subStatus=1&errMsg=${errMsg}&repaymentFlag=${this.repaymentFlag}`;
        Madp.redirectTo({
          url: `/pages/service-result/index?${urlParam}`
        });
        reject(new Error(errMsg));
      }
    });
  }

  compareNowAndBefore = () => {
    const { duePayTotalAmt, beforeNeedRepayAmt, isSpecialCard } = this.state;
    const now = +duePayTotalAmt * 100;
    const before = +beforeNeedRepayAmt * 100;

    if (now === 0) {
      if (before === 0) { // 出现异常
        const urlParam = 'serviceType=extend';
        Madp.redirectTo({
          url: `/pages/service-result/index?${urlParam}&status=3&subStatus=3&repaymentFlag=${this.repaymentFlag}`
        });
      } else { // 还变不还
        this.setState({
          confirmState: ConfirmState.noNeedToRepayNow,
          showPage: true,
        });
        dispatchTrackEvent({
          target: this,
          event: EventTypes.PO,
          beaconId: isSpecialCard ? 'RepayNoneMjevShow' : 'RepayNoneNotMjevShow'
        });
      }
    } else if (now < before) { // 还变少还
      this.setState({
        confirmState: ConfirmState.repayless,
        showPage: true,
      });
      dispatchTrackEvent({
        target: this,
        event: EventTypes.PO,
        beaconId: isSpecialCard ? 'RepayLessMjevShow' : 'RepayLessNotMjevShow'
      });
    } else if (now > before) { // 还变多还
      this.setState({
        confirmState: ConfirmState.repaymore,
        showPage: true,
      });
      dispatchTrackEvent({
        target: this,
        event: EventTypes.PO,
        beaconId: 'RepayMoreShow'
      });
    } else { // remainTheSame：不变
      this.goToRepay();
    }
  }

  goToRepay() {
    const { applyNo = '' } = this.extendRepayInfo || {};
    const url = `/pages/express-repay/index?_windowSecureFlag=1&billType=extend&applyNo=${applyNo}`;
    if (this.miniChannelFlag) {
      Taro.redirectTo({ url });
    } else {
      Madp.redirectTo({ url });
    }
  }

  async submit(isCancel = false) {
    const { applyNo = '' } = this.extendRepayInfo || {};
    const { ret, errMsg, data } = await Dispatch.repayment.postLoanAddData({
      applyNo,
      addInfoScene: '01',
      updateScene: isCancel ? '01' : '00'
    }, {});
    const { applyStatus } = data || {};
    let urlParam = 'serviceType=extend';

    if (ret === '0') {
      if (isCancel && applyStatus === '6') { // 取消办理且成功
        urlParam = `${urlParam}&status=1&isCancel=true`;
      } else if (!isCancel && applyStatus === '9') { // 同意办理且成功
        urlParam = `${urlParam}&status=1&isCancel=false&applyNo=${applyNo}`;
      } else {
        urlParam = `${urlParam}&status=3&subStatus=3`;
      }
    } else {
      urlParam = `${urlParam}&status=3&subStatus=1&errMsg=${errMsg}`;
    }
    Madp.redirectTo({
      url: `/pages/service-result/index?${urlParam}&repaymentFlag=${this.repaymentFlag}`
    });
  }

  getBeaconPrefix() {
    const { confirmState, isSpecialCard } = this.state;
    if (confirmState === ConfirmState.noNeedToRepayNow) {
      return `RepayNone${isSpecialCard ? '' : 'Not'}Mjev`;
    } else if (confirmState === ConfirmState.repayless) {
      return `RepayLess${isSpecialCard ? '' : 'Not'}Mjev`;
    } else {
      return 'RepayMore';
    }
  }

  render() {
    const { showPage, isSpecialCard, confirmState,
      duePayTotalAmt, totalDelayAmt, beforeNeedRepayAmt, extendListPlanData } = this.state;
    const footerHeight = (confirmState === ConfirmState.noNeedToRepayNow ? 160 : 120) / 2;
    const mainPartHeight = window.innerHeight - footerHeight;

    return showPage ? (
      <MUView className="extend-confirm">
        <MUView className="extend-confirm__main" style={{ height: `${mainPartHeight}px` }}>
          {isSpecialCard
            ? (<MUView className="extend-confirm__special-card">
              <MUImage
                className={
                  classNames('extend-confirm__special-card-slogan',
                    { 'extend-confirm__special-card-slogan-plus': this.isVplus })
                }
                src={this.isVplus ? sloganUrl.upperRightVplus : sloganUrl.upperRight}
              />
              <MUImage src={specialCard} className="extend-confirm__special-card-img" />
              <MUView className="extend-confirm__special-card-content" >
                <MUView className="extend-confirm__special-card-content-title" >
                  恭喜，您已获得办理资格
                </MUView>
                <MUView className="extend-confirm__special-card-content-subtitle" >
                  考虑到您的特殊情况，为您提供以下特权
                </MUView>
                <MUView className="extend-confirm__special-card-content-tips" >
                  <MUImage src={privilege} className="extend-confirm__special-card-content-tips-img" />
                  <MUText className="extend-confirm__special-card-content-tips-text">
                    {confirmState === ConfirmState.noNeedToRepayNow
                      ? (<MUText>无需立即还款即可办理</MUText>)
                      : (<MUText>还清到期金额
                        <MUText className="extend-confirm__special-card-content-tips-text-highlight">{`${formatPrice(duePayTotalAmt)}元`}</MUText>
                        即可办理成功</MUText>
                      )}
                  </MUText>
                </MUView>

                <MUView className="extend-confirm__special-card-content-subcards" >
                  <MUView className="extend-confirm__special-card-content-subcards-1" >
                    <MUImage src={subcard1} className="extend-confirm__special-card-content-subcards-1-img" />
                    <MUView className="extend-confirm__special-card-content-subcards-1-text" >
                      <MUView className="extend-confirm__special-card-content-subcards-1-text-1" >到期应还金额</MUView>
                      <MUView className="extend-confirm__special-card-content-subcards-1-text-2" >{`¥${formatPrice(beforeNeedRepayAmt)}`}</MUView>
                    </MUView>
                    <MUImage src={arrow} className="extend-confirm__special-card-content-subcards-arrow" />
                  </MUView>
                  <MUView className="extend-confirm__special-card-content-subcards-2" >
                    <MUImage src={subcard2} className="extend-confirm__special-card-content-subcards-2-img" />
                    <MUView className="extend-confirm__special-card-content-subcards-2-text" >
                      {confirmState === ConfirmState.noNeedToRepayNow
                        ? (<MUView className="extend-confirm__special-card-content-subcards-2-text-1" >无需立即还款</MUView>)
                        : (<MUView>
                          <MUView className="extend-confirm__special-card-content-subcards-2-text-2" >到期应还金额</MUView>
                          <MUView className="extend-confirm__special-card-content-subcards-2-text-3" >{`¥${formatPrice(duePayTotalAmt)}`}</MUView>
                        </MUView>
                        )}
                    </MUView>
                  </MUView>
                </MUView>
              </MUView>
            </MUView>
            ) : (
              <MUView className="extend-confirm__normal-card">
                <MUImage
                  className={
                    classNames('extend-confirm__normal-card-slogan',
                      { 'extend-confirm__normal-card-slogan-plus': this.isVplus })
                  }
                  src={this.isVplus ? sloganUrl.upperRightVplus : sloganUrl.upperRight}
                />
                <MUImage src={normalCard} className="extend-confirm__normal-card-img" />
                <MUView className="extend-confirm__normal-card-content" >
                  <MUView className="extend-confirm__normal-card-content-text" >
                    <MUView className="extend-confirm__normal-card-content-text-title" >
                      恭喜，您已获得办理资格
                    </MUView>
                    <MUView className="extend-confirm__normal-card-content-text-subtitle" >
                      {confirmState === ConfirmState.noNeedToRepayNow ? (
                        <MUText>请确认是否同意办理</MUText>
                      ) : (
                        <MUText>还<MUText className="extend-confirm__special-card-content-tips-text-highlight">{`${formatPrice(duePayTotalAmt)}元`}</MUText>
                          后，即可完成办理</MUText>
                      )}
                    </MUView>
                  </MUView>
                </MUView>
              </MUView>
            )}

          <MUView className="extend-confirm__banner">
            <MUView className="extend-confirm__banner-text">
              延后还借据本金
            </MUView>
            <MUView className="extend-confirm__banner-amount">
              {`${formatPrice(totalDelayAmt)}元`}
            </MUView>
          </MUView>

          <MUView className="extend-confirm__plan">
            <ExtendListPlan
              isVplus={this.isVplus}
              extendListPlanData={extendListPlanData}
              beaconPrefix={this.getBeaconPrefix()}
            />
          </MUView>
        </MUView>

        <MUView className={`extend-confirm__footer extend-confirm__footer-${
              confirmState === ConfirmState.noNeedToRepayNow ? 'confirm' : 'repay'}`}
        >
          {confirmState === ConfirmState.noNeedToRepayNow ? (
            <MUView>
              <MUView className="extend-confirm__footer-confirm-btnbox">
                <MUButton
                  beaconId={`${isSpecialCard ? '' : 'Not'}MjevCancelSubmit`}
                  onClick={() => this.submit(true)}
                >
                  取消办理
                </MUButton>
              </MUView>
              <MUView className="extend-confirm__footer-confirm-btnbox">
                <MUButton
                  beaconId={`${isSpecialCard ? '' : 'Not'}MjevConfirmSubmit`}
                  type={'primary'}
                  onClick={() => this.submit(false)}
                >
                  同意办理
                </MUButton>
              </MUView>
            </MUView>
          ) : (
            <MUView>
              <MUView className="extend-confirm__footer-text">
                <MUView className="extend-confirm__footer-text-1">
                  还<MUText className="extend-confirm__footer-text-highlight">{`${formatPrice(duePayTotalAmt)}元`}</MUText>后
                </MUView>
                <MUView className="extend-confirm__footer-text-2">即可完成最终办理</MUView>
              </MUView>
              <MUView className="extend-confirm__footer-repay-btnbox">
                <MUButton
                  beaconId={`${this.getBeaconPrefix()}GoToRepay`}
                  type={'primary'}
                  className="extend-confirm__footer-repay-btn"
                  onClick={() => this.goToRepay()}
                >
                  去还款
                </MUButton>
              </MUView>
            </MUView>
          )}
        </MUView>

      </MUView>
    ) : null;
  }
}
