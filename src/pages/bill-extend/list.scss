@import '../../components/weapp/index.scss';

.extend-list {
  overflow: hidden;

  &__bills {
    margin: 20px;

    &__wrapper {
      padding-left: 30px;
      border-radius: 16px;
      background: #fff;
      position: relative;
      overflow: hidden;

      .bills__top {
        margin-top: 70px;
        display: flex;
        align-items: center;

        &__slogan {
          width: 206px;
          height: 44px;
          position: absolute;
          left: 0;
          top: 0;

          &-plus {
            width: 249px;
          }
        }

        &__title {
          font-size: 32px;
          line-height: 32px;
          color: #333;
          font-weight: 500;
        }

        &__cnts {
          margin-left: 10px;
          padding-top: 2px;
          font-size: 26px;
          line-height: 26px;
          color: #808080;
          font-weight: 400;
        }

        &__explain {
          margin-left: 8px;
          font-size: 0;
        }
      }

      .bills__middle {
        margin: 20px 30px 20px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &__amount {
          font-size: 36px;
          line-height: 36px;
          color: #333;
          font-weight: 500;

          &__text {
            padding-left: 6px;
            font-size: 44px;
            line-height: 44px;
          }
        }

        &__modify {
          font-size: 32px;
          line-height: 32px;
          font-weight: 400;
        }
      }

      .bills__line {
        width: 100%;
        border-bottom: 1PX solid #E6E6E6;
      }

      .bills__bottom {
        margin: 28px 0;
        font-size: 26px;
        line-height: 26px;
        color: #808080;
        font-weight: 400;

        &__point {
          color: #cc1f15;
        }
      }

      .bills__bubble {
        position: absolute;
        top: 0;
        right: 0;
        border-radius: 0 16px 0 16px;
        display: flex;
        align-items: flex-end;
        padding: 9px 10px;

        &__text {
          font-size: 26px;
          line-height: 26px;
          color: #fff;
          font-weight: 400;
        }

        &__guide {
          margin-left: 10px;
          font-size: 0;
        }
      }
    }
  }

  &__period-reason {
    margin: 0 20px;

    &__wrapper {
      border-radius: 16px;
      background: #fff;

      .period-reason-item {
        padding: 34px 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 32px;
        line-height: 32px;
        color: #333;
        font-weight: 500;

        &__selected {
          display: flex;
          align-items: center;

          &__guide {
            margin-left: 10px;
            font-size: 0;

            .icon-accordion-down {
              transform: rotate(90deg);
            }
          }

          .select-place-holder {
            color: #cacaca;
          }
        }
      }

      .period-area {
        padding: 0 30px 30px;
        overflow: hidden;

        &__scroll {
          display: flex;
        }

        &__item {
          flex: 0 0 auto;
          width: 180px;
          height: 72px;
          margin-right: 20px;
          border-radius: 8px;
          background: #f3f3f3;
          text-align: center;
          font-size: 32px;
          line-height: 72px;
          color: #333;
          font-weight: 400;
          position: relative;

          &__ticked {
            width: 56px;
            height: 56px;
            position: absolute;
            right: 0;
            bottom: 0;
          }
        }

        &__item:last-child {
          margin-right: 0;
        }
      }
    }
  }

  &__footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background: #fff;

    .footer {
      &__contract {
        border-bottom: 1PX solid #f3f3f3;
        .mu-radio__option {
          margin: 29px 30px 29px 20px;
        }
      }
      &__button {
        margin: 30px;
      }
    }
  }

  .dialog-standard-modify {
    .mu-dialog__content {
      padding: 0;
    }
  }

  .cannot-extend-explain {
    &__bg {
      height: 165px;
      background-image: linear-gradient(180deg, #3477FF 0%, #FFFFFF 100%);
      opacity: 0.3;
    }

    &__wrap {
      position: relative;
      margin-top: -125px;

      &__title {
        margin-bottom: 40px;
        font-size: 36px;
        line-height: 54px;
        color: #333;
        font-weight: 600;
        text-align: center;
      }

      &__content {
        padding: 0 40px;
        font-size: 28px;
        line-height: 42px;
        color: #333;
        font-weight: 400;
        text-align: left;
      }

      &__confirm {
        margin: 40px 40px 30px;
      }

      &__slogan {
        width: 206px;
        height: 44px;
        margin: 0 auto;
        padding-bottom: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
  
        &-plus {
          width: 249px;
        }
      }
    }
  }

  .reason-select-drawer {
    position: relative;
    padding-left: 20px;
    overflow: hidden;

    &__title {
      margin: 30px 0 72px;
      font-size: 36px;
      line-height: 36px;
      color: #333;
      font-weight: 500;
      text-align: center;
    }

    &__content {
      display: flex;
      flex-wrap: wrap;

      &__item {
        margin: 0 20px 20px 0;
        width: 345px;
        height: 100px;
        background: #f3f3f3;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        text-align: center;
        position: relative;

        .item {
          &__main {
            font-size: 32px;
            line-height: 54px;
            color: #333;

            &__supply {
              font-size: 24px;
              line-height: 24px;
              color: #808080;
            }
          }

          &__ticked {
            width: 56px;
            height: 56px;
            position: absolute;
            right: 0;
            bottom: 0;
          }
        }
      }
    }

    &__close {
      width: 32px;
      height: 32px;
      font-size: 0;
      position: absolute;
      top: 30px;
      right: 30px;

      image,
      .taro-img {
        width: 100%;
        height: 100%;
      }
    }
  }
}