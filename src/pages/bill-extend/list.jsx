/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
import Taro, { Component } from '@tarojs/taro';
import {
  <PERSON>U<PERSON>iew, MUNavBarWeapp, MUIcon, MUText, MUScrollView, MUImage, MUButton, MUDialog, MUDrawer
} from '@mu/zui';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import classNames from 'classnames';
import pageHoc from '@utils/pageHoc';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import {
  filterQualCoupons
} from '@utils/repay-util';
import { urlDomain } from '@utils/url_config.js';
import Madp from '@mu/madp';
import { Url, getCurrentPageUrlWithArgs } from '@mu/madp-utils';
import CustomConfig from '@config';
import { getStore, setStore } from '@api/store';
import { sloganUrl, miniProgramChannel, EVENT_CODE_MAP } from '@utils/constants';
import icTicked from '@components/assets/img/icon_tick.png';
import icTickedRed from '@components/assets/img/icon_tick_red.png';
import close from '@components/assets/img/close.png';

import ExtendListPlan from './components/extend-list-plan';
import Protocol from '@components/protocol/index';
import OriginalIousList from './components/original-ious-list/index';
import { injectState } from '@mu/leda';
import { OpRepayment } from '@mu/op-comp';
import { getProductAllParams, opService, getPageConf } from '@mu/business-basic';

import './list.scss';
import './components/extend-list-plan/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/loading-dialog/index.scss');
  require('@components/bill-list/index.scss');
  require('@components/list-item/index.scss');
  require('@components/empty-sign/index.scss');
}
// 默认页面展位id
const pageId = '547ccaed-a0fa-4e08-a463-e80f3a33a258';

const cannotExtendExplainContent = [
  '没有在途利率借据，例如大多数商城消费借据无法办理',
  '借据办理延后还或再分期的次数已超过2次',
  '借据距离上次办理完延后还或再分期后，还未进行过第一期的还款',
  '借据不支持还款计划变更',
  '如以上原因均不符合，可咨询招联客服',
];

// 其他，reasonNo为99
const extendReasonsObj = {
  1: [{ reason: '重大自然灾害', supply: '含水灾、地震、海啸、雪灾等', reasonNo: '8' }, { reason: '重大社会事件', supply: '含车祸、爆炸等', reasonNo: '9' }],
  99: [{ reason: '周转资金未到', reasonNo: '1' }, { reason: '债务太多', reasonNo: '2' }, { reason: '失业', reasonNo: '3' }, { reason: '生意失败', reasonNo: '4' }, { reason: '收入降低/工资拖欠', reasonNo: '5' }, { reason: '本人/家人生病', reasonNo: '6' }, { reason: '遭遇诈骗', reasonNo: '7' }, { reason: '重大自然灾害', supply: '含水灾、地震、海啸、雪灾等', reasonNo: '8' }, { reason: '重大社会事件', supply: '含车祸、爆炸等', reasonNo: '9' }]
};

const themeColor = Util.getThemeColor(CustomConfig.theme);

// 处理微信小程序内嵌H5返回空白的问题
if (process.env.TARO_ENV === 'h5') {
  window.onpageshow = (event) => {
    if (
      event.persisted || (window.performance && (window.performance.navigation.type === 2 || window.performance.navigation.type === '2'))
    ) {
      if (Madp.getStorageSync('EXTEND_BACK', 'SESSION') === 'Y') {
        Madp.setStorageSync('EXTEND_BACK', '', 'SESSION');
        Madp.miniProgram.navigateBack();
      }
    }
  };
}

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: pageId
    }
  }
}, {
  pageId: 'ExtendList',
  dispatchOnMount: true,
})
@pageHoc({ title: '延后还' })
@injectState({
  pageId() {
    return pageId;
  },
  getPageConf: () => getPageConf(pageId, true),
  stateKeys: []
})
export default class ExtendList extends Component {
  config = {
    navigationBarTitleText: '延后还',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      showPage: false,
      selectedReason: '', // 选中原因
      selectedCnt: '', // 选中延期期数
      selectedExtendBillsOrderNo: [], // 选中延期借据号
      selectedExtendBillsAmount: '', // 选中延期金额
      showCannotExtendExplain: false, // 展示不可延期说明
      showPeriodArea: false, // 展开期数选择
      showReasonArea: false, // 展开原因选择
    };
    this.majorEventSwitch = ''; // 重大事件开关
    this.applyScene = ''; // 申请场景
    this.availSelectExtendReasons = []; // 可选延期原因
    this.applyNo = ''; // 案件号
    this.contractApplyList = []; // 需签署协议列表
    this.contractInfos = []; // 3.0合同列表
    this.isCheckedContract = false; // 协议勾选状态
    this.needRepayType = ''; // 还款方式
    this.adjustCntList = []; // 可选延期期数
    this.availSelectExtendBills = []; // 可延期借据
    this.selectedExtendBillsCount = ''; // 选中延期笔数
    this.cannotSelectExtendBills = []; // 不可延期借据
    this.totalBillsCount = ''; // 延后还款借据总笔数
    this.duePayTotalAmt = ''; // 需先还款金额
    this.extendListPlanData = {}; // 新还款计划
    this.selectedReasonNo = ''; // 选中原因编码
    this.awardNo = ''; // 选中的券编码
    this.supplySuccess = Url.getParam('supplySuccess');
    this.isVplus = false; // 是否为v+会员
    this.resultId = Url.getParam('resultId') || ''; // 问卷结果
    this.needFileList = []; // 待补充资料列表
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
    this.custTypeAndFileTypeList = []; // 待补充身份和资料信息列表
    this.opRetainOpened = ''; // // 是否已经成功展示过挽留弹窗
    this.dispatchFlag = false; // 记录进入办理页后是否自动跳转到其他页面
    this.repaymentFlag = Url.getParam('repaymentFlag') || '';
  }


  async componentDidMount() {
    // 若是补充身份信息且成功回来的，则跳转到补充联系人页面
    if (this.supplySuccess === '1') {
      this.handleContinue();
      return;
    }

    // 检查是否办理按钮点击离开的办理页，是则直接返回首页
    const isclose = this.checkAndClosePage();
    if (isclose) {
      return;
    }

    // 清空缓存数据
    setStore({ selectedBillList: [], extendRepayInfo: {}, contractApplyList: [], contractInfos: [] });
    Madp.setStorageSync('doKycExit', '', 'SESSION');
    // 办理场景相关信息查询，名单可以和cdp标签一起查
    /* 重大事件(cdp名单)，l_zdsj_dlypb='Y'
     * v+会员(cif标签)，HIGH_VALUE_CUST=H
     * 微光卡(cdp标签)，wg_card_can_opn_type in (fdz_zsk,gx_zsk)且lst_wg_card_exp_dt距今剩余天数大于等于0天
     * 信用不负期待(cdp名单)，l_fd_v9zk_zx='Y'
     * 风险标签(cdp标签)，
     * 117-SSA01，人工作业；117-SSA02，博弈
    */

    const redirectUrl = Url.getParam('redirectUrl');
    if (redirectUrl) {
      Madp.setStorageSync('ServiceResultRedirectUrl', encodeURIComponent(redirectUrl), 'LOCAL');
    } else {
      Madp.removeStorageSync('ServiceResultRedirectUrl', 'LOCAL');
    }

    const [majorEventSwitch, cdpData, cifData, couponList] = await Promise.all([
      this.getMajorEventSwitch(),
      this.getTagContext({ tagType: 0, userSheetCodeList: ['l_fd_v9zk_zx', 'l_zdsj_dlypb'], tagCodeList: ['wg_card_can_opn_typ', 'lst_wg_card_exp_dt'] }),
      this.getTagContext({ tagType: 2, tagCodeList: ['HIGH_VALUE_CUST'] }),
      this.getTransCouponList()
    ]);
    this.judgeIsVplus(cifData);
    this.majorEventSwitch = majorEventSwitch;
    this.applyScene = this.getApplyScene(cdpData, cifData, couponList);
    if (majorEventSwitch === 'Y') {
      if (this.applyScene === '1') {
        this.availSelectExtendReasons = extendReasonsObj['1'];
      } else {
        this.availSelectExtendReasons = extendReasonsObj['99'];
      }
    }
    if (this.resultId) {
      this.handleKycBack();
    }
    // 建案、延期借据查询
    await Promise.all([
      this.applyCase(),
      this.queryExtendList()
    ]);
    // 延期试算
    await this.delayRepayCal(true, () => {
      const { selectedExtendBillsOrderNo, selectedCnt } = this.state;
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'FinishedFirstCal', beaconContent: { cus: { selectedExtendBillsOrderNo, selectedCnt } } });
    });
    // 正常进入页面将需要回退标志位置为‘’，解决从介绍页返回会意外退出页面的问题
    Madp.setStorageSync('SXF_BACK', '', 'SESSION');
    this.setState({
      showPage: true,
      selectedReason: this.availSelectExtendReasons.length === 1 ? this.availSelectExtendReasons[0] && this.availSelectExtendReasons[0].reason : '请选择',
    });
    if (this.availSelectExtendReasons && this.availSelectExtendReasons.length === 1) { // 可选择的原因只有一个
      this.selectedReasonNo = this.availSelectExtendReasons[0].reasonNo;
    }

    // 当停留在当前办理页时，push当前页面路由
    if (process.env.TARO_ENV === 'h5' && !this.dispatchFlag) {
      Util.pushUrlState('keepState');
    }
  }

  checkAndClosePage = () => {
    let closeFlag = false;
    const leaveFlag = Madp.getStorageSync('billExtedConfirmBtnLeave', 'SESSION');
    if (leaveFlag === 'Y') {
      Util.closeOrBack('billExtedConfirmBtnLeave');
      closeFlag = true;
    }
    return closeFlag;
  };

  // 提供放置挽留弹窗的位置
  async beforeRouteLeave(from, to, next, options) {
    const isOverdueCust = getStore('isOverdueCust'); // 全部待还接口判断
    try {
      // 返回还款首页时，触发op交互事件：挽留弹窗
      if ((to.path === from.path) && isOverdueCust && this.opRetainOpened !== 'SUCCESS') {
        const opRes = await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.billExtendRetain);
        // 标记是否打开过，如选择优惠还款留在当前页，再次进入挽留逻辑，直接next(true),不再进入op
        this.opRetainOpened = opRes;
        // 如未正确打开op，也进行返回兜底
        if (opRes !== 'SUCCESS') {
          Util.closeOrBack();
        } else {
          // 如果成功打开op，则不继续导航,取消路由变化；解决二次进入后无法返回的问题
          next(false);
          return;
        }
      } else {
        if (from.path === to.path) {
          Util.closeOrBack();
        } else {
          next(true);
        }
      }
    } catch (error) {
      console.log('error:', error);
      if (from.path === to.path) {
        Util.closeOrBack();
      } else {
        next(true);
      }
    }
  }

  async opOnPageEvent(eventName, interactionEventCode) {
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: pageId,
            opRetainClose: () => { Util.closeOrBack(); }, // 退出方法
            trackPageId: 'billExtend',
            pageCode: 'extendList' // op用于区分挽留弹窗
          },
          // 正确打开op后，op组件调用该方法返回‘SUCCESS’
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve('ERROR');
      }
    });
  }

  // 处理从补充身份证回来的流程接续
  handleContinue = () => {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'UpdateIDCardSuccess' });
    const { applyNo: applyNoFromStore } = getStore('extendRepayInfo') || {};
    const applyNo = (applyNoFromStore || Url.getParam('applyNo')) || '';
    if (this.miniChannelFlag) {
      Taro.redirectTo({
        url: `/pages/identity/contact?billType=extend&applyNo=${applyNo}&repaymentFlag=${this.repaymentFlag}`
      });
    } else {
      Util.router.replace({
        path: `/pages/identity/contact?billType=extend&repaymentFlag=${this.repaymentFlag}`,
        query: {
          applyNo,
        }
      });
    }
  }

  // 获取重大事件开关
  getMajorEventSwitch = async () => {
    let majorEventSwitch = '';
    if (getProductAllParams && typeof getProductAllParams === 'function') {
      ({ majorEventSwitch } = await getProductAllParams('YHH.YHH01') || {});
    }
    return majorEventSwitch;
  }

  // 办理标签查询
  getTagContext = async (paramObj) => {
    const { data } = await Dispatch.repayment.getTagContext({
      cacheType: 1,
      sourceModule: 'RBF',
      ...paramObj
    });
    return data || {};
  }

  // 判断是否为v+会员
  judgeIsVplus = (cifData) => {
    const { tagMap } = cifData || {};
    setStore({ isVplus: false }); // 清缓存
    if (tagMap && tagMap.HIGH_VALUE_CUST === 'H') {
      this.isVplus = true;
      setStore({ isVplus: true });
    }
  }

  // 办理资格券查询，117-SSA02博弈催收、117-SSA01作业催收 SSA07不展示
  async getTransCouponList() {
    const { awardDetailList = [] } = await Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }) || {};
    const { usableQualConponList } = filterQualCoupons(awardDetailList) || {};
    const allCoupon = (usableQualConponList || []).filter((item) => item.awardType === '117');
    if (allCoupon && allCoupon.length > 0) {
      this.awardNo = (allCoupon[0] && allCoupon[0].awardNo) || '';
    }
    return {
      SSA01Coupon: allCoupon.filter((item) => item.subUseSceneCode === 'SSA01'),
      SSA02Coupon: allCoupon.filter((item) => item.subUseSceneCode === 'SSA02'),
    };
  }

  // 问卷作答回来，先提交kyc作答资料，并存下问卷答题结果
  handleKycBack = async () => {
    const { fileType, questionnaireId, applyNo } = getStore('extendRepayApplyMaterial') || {};
    Madp.setStorageSync('doKycBack', 'Y', 'SESSION');
    const { ret } = await Dispatch.repayment.postLoanAddData({
      applyNo,
      addInfoScene: '00',
      applyMaterialList: [{
        fileType,
        questionnaireId,
        fileResult: this.resultId,
      }],
    }, {});

    if (ret !== '0') {
      this.dispatchFlag = true;
      Madp.redirectTo({ url: `/pages/service-result/index?serviceType=extend&status=3&repaymentFlag=${this.repaymentFlag}` });
    }
  }

  // 办理场景确认
  // 1重大事件
  // 2V+
  // 3微光卡
  // 4信用不负期待
  // 5逾期客户名单(客户名下有延后还资格券，且券场景=催收博弈场景)
  // 6风险名单(本次先不处理)
  // 7作业办理名单(客户名下有延后还资格券，且券场景=作业场景)
  // 99其他
  getApplyScene = (cdpData = {}, cifData = {}, couponList) => {
    const { userSheetMap = {}, tagMap: cdpTagMap = {} } = cdpData || {};
    const { tagMap: cifTagMap = {} } = cifData || {};
    const { SSA01Coupon = [], SSA02Coupon = [] } = couponList || {};
    let applyScene = '';
    if (this.majorEventSwitch === 'Y' && userSheetMap && userSheetMap.l_zdsj_dlypb && userSheetMap.l_zdsj_dlypb.codeVal === 'Y') {
      applyScene = '1'; // 重大事件
    } else if (cifTagMap && cifTagMap.HIGH_VALUE_CUST === 'H') {
      applyScene = '2'; // V+会员
    } else if (cdpTagMap && ['fdz_zsk', 'gx_zsk'].includes(cdpTagMap.wg_card_can_opn_typ) && cdpTagMap.lst_wg_card_exp_dt >= 0) {
      applyScene = '3'; // 微光卡
    } else if (userSheetMap && userSheetMap.l_fd_v9zk_zx && userSheetMap.l_fd_v9zk_zx.codeVal === 'Y') {
      applyScene = '4'; // 信用不负期待
    } else if (SSA02Coupon && SSA02Coupon.length > 0) {
      applyScene = '5'; // 逾期客户名单
    } else if (SSA01Coupon && SSA01Coupon.length > 0) {
      applyScene = '7'; // 作业办理名单
    }

    return applyScene || '99';
  }

  // 延后还建案
  applyCase = async () => {
    const { ret, errCode, errMsg, data } = await Dispatch.repayment.postLoanApplyCase({
      serviceType: '001',
      applyScene: this.applyScene,
      eventFlag: this.majorEventSwitch || 'N',
      interfaceVersion: '1.0'
    }) || {};
    this.applyStatusDispatchHandler(ret, errCode, errMsg, data);
  }

  // 根据不同的建案状态跳转到不同的页面
  applyStatusDispatchHandler(ret, errCode, errMsg, data) {
    // 在途服务案件拒绝
    const { transitApplyCase } = data || {};
    const {
      applyNo: originApplyNo, canClose
    } = transitApplyCase || {};
    if (originApplyNo && canClose === 'Y') {
      this.dispatchFlag = true;
      setStore({ transitApplyCase });
      Madp.redirectTo({
        url: `/pages/service-pending/index?serviceType=extend&repaymentFlag=${this.repaymentFlag}`,
      });
      return;
    }
    if (ret !== '0') {
      this.dispatchFlag = true;
      let urlParam = 'serviceType=extend&status=3';
      // 准入拒绝使用返回码方式
      if (errCode === 'UMDP02427') { // UMDP02427 不符合办理条件；需前端特殊处理展示
        urlParam = `${urlParam}&subStatus=1`;
      } else if (errMsg) {
        urlParam = `${urlParam}&subStatus=1&errMsg=${errMsg}`;
      }
      Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
      return;
    }
    const {
      applyNo, applyStatus, contractApplyList, contractInfos, delayRepayPackageInfo, needFileList, applyInfoDone, custTypeAndFileTypeList,
    } = data || {};

    // 设置待补充资料
    this.needFileList = needFileList;
    // 设置待补充身份和资料
    this.custTypeAndFileTypeList = custTypeAndFileTypeList;

    let needManualKyc = false;
    this.applyNo = applyNo || '';
    this.contractApplyList = contractApplyList || [];
    this.contractInfos = contractInfos || [];
    const {
      adjustCntList, needRepayType, actualNeedRepayType, delayRepayApplyInfo, isMajorEvent
    } = delayRepayPackageInfo || {};
    this.adjustCntList = adjustCntList;
    this.needRepayType = actualNeedRepayType || needRepayType || ''; // 有实际还款模式用实际还款模式
    this.isMajorEvent = isMajorEvent;
    const { adjustCnt, orderNoList } = delayRepayApplyInfo || {};
    // 重新存储合同信息
    setStore({ contractApplyList, contractInfos });
    if (applyStatus === '1' && (this.adjustCntList || []).length) {
      needFileList && needFileList.forEach((item) => {
        // 需要自助kyc
        if (item.fileType === 'K01' && this.resultId === '') {
          this.dispatchFlag = true;
          // 存下文件类型和问券ID，后面提交案件时需要
          const extendRepayApplyMaterial = {
            applyNo,
            fileType: item.fileType,
            questionnaireId: item.questionnaireId,
          };
          setStore({ extendRepayApplyMaterial });
          dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'NeedSelfKyc', beaconContent: { cus: { } } });
          // 缓存标识是否已做kyc，用于解决iPhone13在问卷填写页面点击返回时无法回到还款首页（直接退出）的问题
          Madp.setStorageSync('doKycExit', 'Y', 'SESSION');
          const redirectUrl = getCurrentPageUrlWithArgs();
          const maxAdjustCnt = (this.adjustCntList || []).sort((a, b) => Number(b) - Number(a))[0] || '';
          const isOverdueCust = getStore('isOverdueCust');
          Madp.redirectTo({
            url: `${urlDomain}/${Madp.getChannel()}/survey/#/pages/questionnaire/index?surveySerialNo=${item.questionnaireId}&useTitle=延后还办理问卷&needNav=1&redirectUrl=${encodeURIComponent(redirectUrl)}&benefitPoint=${maxAdjustCnt}&busiType=02&isOverdue=${isOverdueCust ? 'Y' : 'N'}&repaymentFlag=${this.repaymentFlag}`
          });
        }
      });

      // 已经提交过案件服务信息，但未完成补充资料
      if (applyInfoDone === 'Y') {
        this.dispatchFlag = true;
        const extendRepayInfo = {
          applyNo: this.applyNo,
          needFileList,
          custTypeAndFileTypeList,
        };
        setStore({ extendRepayInfo, contractApplyList: this.contractApplyList, contractInfos: this.contractInfos });
        const urlParam = `applyNo=${applyNo}&serviceType=extend&applyInfoDone=1&repaymentFlag=${this.repaymentFlag}`;
        if (this.miniChannelFlag) {
          Taro.redirectTo({
            url: `/pages/identity/information?${urlParam}`
          });
        } else {
          Madp.redirectTo({ url: `/pages/identity/information?${urlParam}` });
        }
      }

      // 发起延后还试算
      this.setState({ selectedCnt: this.adjustCntList.sort((a, b) => Number(a) - Number(b))[0] });
    } else if (applyStatus === '3' || applyStatus === '5' || applyStatus === '8') { // 跳转延后还审核页
      this.dispatchFlag = true;
      needFileList && needFileList.forEach((item) => {
        // 需要人工kyc（仅做记录，用于结果页展示）
        if (item.fileType === 'K02') {
          needManualKyc = true;
          dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'NeedManualKyc', beaconContent: { cus: { } } });
        }
      });
      const urlParam = `serviceType=extend&status=2${applyStatus === '8' ? '&subStatus=2' : (needManualKyc ? '&subStatus=1' : '')}`;
      Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
    } else if (applyStatus === '4') { // 待完成补充待办，跳转资料补充页
      this.dispatchFlag = true;
      const extendRepayInfo = {
        applyNo: this.applyNo,
        needFileList,
        custTypeAndFileTypeList,
      };
      setStore({ extendRepayInfo, contractApplyList: this.contractApplyList, contractInfos: this.contractInfos });
      const urlParam = `applyNo=${applyNo}&serviceType=extend&oldMode=1&repaymentFlag=${this.repaymentFlag}`;
      if (this.miniChannelFlag) {
        Taro.redirectTo({
          url: `/pages/identity/information?${urlParam}`
        });
      } else {
        Madp.redirectTo({ url: `/pages/identity/information?${urlParam}` });
      }
    } else if (applyStatus === '9') { // 跳转至延后还成功页
      this.dispatchFlag = true;
      const urlParam = `serviceType=extend&status=1&repaymentFlag=${this.repaymentFlag}`;
      Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}` });
    } else if (applyStatus === '6') { // 返回applyStatus 6--风控审核拒绝
      this.dispatchFlag = true;
      const urlParam = 'serviceType=extend&status=3&subStatus=2';
      Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}` });
    } else if (applyStatus === '10') { // 10--审核通过且业务办理失败
      this.dispatchFlag = true;
      const urlParam = 'serviceType=extend&status=3&subStatus=3';
      Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}` });
    } else if (applyStatus === '7') { // 7--审核通过待业务提交，跳转去还款确认页
      this.dispatchFlag = true;
      const extendRepayInfo = {
        applyNo: this.applyNo,
        delayRepayAdjustInfo: {
          adjustCnt,
          orderNoList,
          needRepayType: this.needRepayType,
          isMajorEvent: this.isMajorEvent
        },
      };
      setStore({ extendRepayInfo, contractApplyList: this.contractApplyList, contractInfos: this.contractInfos });
      Madp.redirectTo({
        url: `/pages/bill-extend/confirm?applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}`,
      });
    } else {
      this.dispatchFlag = true;
      const urlParam = 'serviceType=extend&status=3&subStatus=3';
      Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
    }
  }

  // 查询延期还款借据
  queryExtendList = async () => {
    const { extendList, cannotExtendList, isOverdueCust } = await Dispatch.repayment.getExtendRepayBills();
    this.availSelectExtendBills = extendList || [];
    this.selectedExtendBillsCount = this.availSelectExtendBills.length;
    this.cannotSelectExtendBills = cannotExtendList || [];
    this.totalBillsCount = this.availSelectExtendBills.length + this.cannotSelectExtendBills.length;
    setStore({ isOverdueCust });
    this.setState({
      selectedExtendBillsOrderNo: this.availSelectExtendBills.map((item) => (item || {}).orderNo),
    });
  }

  // 延后还试算
  delayRepayCal = async (init = false, callback) => {
    const { selectedExtendBillsOrderNo, selectedCnt } = this.state;
    const {
      availSelectExtendBills, adjustCntList
    } = this;
    if ((!this.needRepayType) || (!availSelectExtendBills || availSelectExtendBills.length === 0) || (!adjustCntList || adjustCntList.length === 0)) {
      // 参数不符合要求
      return;
    }
    // 选择进行下月还的借据号列表
    const orderNoList = selectedExtendBillsOrderNo.length ? selectedExtendBillsOrderNo : availSelectExtendBills.map((item) => (item || {}).orderNo);
    const { ret, errCode, errMsg, data } = await Dispatch.repayment.delayRepayCal({
      orderNoList,
      adjustCnt: selectedCnt || (adjustCntList || []).sort((a, b) => Number(a) - Number(b))[0],
      needRepayType: this.needRepayType,
    });
    if (ret !== '0') {
      this.dispatchFlag = true;
      let urlParam = 'serviceType=extend&status=3';
      // 准入拒绝使用返回码方式
      if (errCode === 'UMDP02427') { // UMDP02427 不符合办理条件；需前端特殊处理展示
        urlParam = `${urlParam}&subStatus=1`;
      } else if (errMsg) {
        urlParam = `${urlParam}&subStatus=1&errMsg=${errMsg}`;
      }
      Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
      return;
    }
    // 将试算用的下月还借据号列表保存
    setStore({
      selectedBillList: orderNoList
    });
    const {
      totalDelayAmt, duePayTotalAmt, firstPayDate, repayPlanAfterDelay, canDelayOrderNoList
    } = data || {};
    this.selectedExtendBillsCount = (canDelayOrderNoList || []).length;
    this.duePayTotalAmt = duePayTotalAmt;
    this.extendListPlanData = {
      title: '新还款计划',
      firstPayDate,
      repayPlanAfterDelay
    };
    if (init === true) {
      const filterBillLists = this.availSelectExtendBills.filter((item) => (canDelayOrderNoList || []).indexOf(item.orderNo) <= -1);
      this.availSelectExtendBills = this.availSelectExtendBills.filter((item) => (canDelayOrderNoList || []).indexOf(item.orderNo) > -1);
      this.cannotSelectExtendBills = [...this.cannotSelectExtendBills, ...filterBillLists];
    }
    this.setState({
      selectedExtendBillsAmount: totalDelayAmt, // 延期总本金
      selectedExtendBillsOrderNo: canDelayOrderNoList
    }, () => {
      if (typeof callback === 'function') callback();
    });
  }

  // 修改借据信息
  changeLoanInfo = () => {
    const orderNoListLast = getStore('selectedBillList') || []; // 下月还上次试算的借据列表
    const { selectedExtendBillsOrderNo = [] } = this.state;
    if ((selectedExtendBillsOrderNo && selectedExtendBillsOrderNo.length) !== orderNoListLast.length || !Util.arrayCompare(orderNoListLast, selectedExtendBillsOrderNo)) {
      this.setState({
        selectedExtendBillsOrderNo: [...orderNoListLast]
      });
    }
    this.OriginalIousList.show();
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ChangeLoanInfoDrawer' });
  }

  // 处理原始借据列表的全选取消
  onCancelAll = () => {
    const { selectedExtendBillsOrderNo } = this.state;
    const mustChooseBillList = this.availSelectExtendBills.filter(bill => bill.surplusDays <= 0) || []; // 筛选出到期或逾期借据
    if (mustChooseBillList && mustChooseBillList.length > 0) {
      Util.toast('已到期借据需优先办理，不可取消勾选');
      this.setState({ selectedExtendBillsOrderNo: mustChooseBillList.map((item) => item.orderNo) });
    } else {
      // 最少要选中一个
      Util.toast('至少必须勾选一笔借据');
      this.setState({ selectedExtendBillsOrderNo: (selectedExtendBillsOrderNo || []).length ? [selectedExtendBillsOrderNo[0]] : [] });
    }
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'LoanInfoCancelAll' });
  }

  // 处理原始借据列表的全选选中
  onSelectAll = () => {
    const { selectedExtendBillsOrderNo } = this.state;
    this.availSelectExtendBills.forEach((bill) => {
      if (selectedExtendBillsOrderNo.indexOf(bill.orderNo) === -1) {
        selectedExtendBillsOrderNo.push(bill.orderNo);
      }
    });
    this.setState({ selectedExtendBillsOrderNo });
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'LoanInfoSelectAll' });
  }

  // 原始借据列表选择处理
  handleSelect = (bill) => {
    const { selectedExtendBillsOrderNo } = this.state;
    const billIndex = selectedExtendBillsOrderNo && selectedExtendBillsOrderNo.indexOf(bill.orderNo);
    if (billIndex > -1) {
      // 取消选中
      // surplusDays：正数代表离还款日还差几天，负数就代表逾期几天（逾期），0就是今天正好是还款日（到期）
      // 如果是逾期或到期账单，不可取消选择
      if (bill.surplusDays <= 0) {
        Util.toast('已到期借据需优先办理，不可取消勾选');
      } else if (selectedExtendBillsOrderNo.length === 1) {
        Util.toast('至少必须勾选一笔借据');
      } else {
        selectedExtendBillsOrderNo.splice(billIndex, 1);
      }
    } else {
      selectedExtendBillsOrderNo.push(bill.orderNo);
    }
    this.setState({ selectedExtendBillsOrderNo });
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'LoanItemHandle', beaconContent: { cus: { billInfo: bill, selected: billIndex > -1 } } });
  }

  // 确认选择借据进行试算
  handleConfirm = async () => {
    const { selectedExtendBillsOrderNo } = this.state;
    const orderNoListLast = getStore('selectedBillList'); // 下月还上次试算的借据列表
    // 试算前后借据列表内容不变，则无需重复试算
    if (Util.arrayCompare(orderNoListLast, selectedExtendBillsOrderNo)) {
      return;
    }
    await this.delayRepayCal(false, () => {
      const { selectedExtendBillsOrderNo: caledSelectedExtendBillsOrderNo, selectedCnt } = this.state;
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'HandleConfirmBtn', beaconContent: { cus: { selectedExtendBillsOrderNo: caledSelectedExtendBillsOrderNo, selectedCnt } } });
    });
  }

  // 案件提交
  extendSubmit = async () => {
    const {
      selectedReason, selectedCnt, selectedExtendBillsOrderNo,
    } = this.state;
    if ((this.availSelectExtendReasons || []).length && selectedReason === '请选择') {
      Util.toast('请选择办理原因');
      return;
    }
    // 办理页不展示补充资料协议《贷后资料授权书》
    const contractList = (this.contractApplyList && this.contractApplyList.filter((item) => item.contractType !== 'POST_INFO_AUTH')) || [];
    const contractThreeList = (this.contractInfos && this.contractInfos.filter((item) => item.contractCode !== 'GRXXSQ_DHZLSQ')) || [];
    if ((contractList.length > 0 || contractThreeList.length > 0) && !this.isCheckedContract) { Util.toast('请阅读并同意服务单'); return; }
    // 入参处理
    const extendRepayInfo = {
      applyNo: this.applyNo,
      delayRepayAdjustInfo: {
        adjustCnt: selectedCnt,
        orderNoList: selectedExtendBillsOrderNo,
        delayRepayReason: this.selectedReasonNo || '99',
        awardNo: this.awardNo,
        needRepayType: this.needRepayType,
        isMajorEvent: this.isMajorEvent,
        beforeNeedRepayAmt: this.duePayTotalAmt,
      },
      needFileList: this.needFileList,
      custTypeAndFileTypeList: this.custTypeAndFileTypeList,
    };
    setStore({ extendRepayInfo });
    // 重大事件开启时期，非重大事件名单客户，选择重大事件原因
    if (this.majorEventSwitch === 'Y' && this.applyScene !== '1' && extendReasonsObj['1'].filter((item) => item.reason === selectedReason).length) {
      // 获取lbs
      await this.getLbs();
    }
    // 必须进行联系人身份证补充
    await this.goSupply();
  }

  getLbs = async () => {
    await Util.checkLbsPermission(() => { });
  }

  async goSupply() {
    const { authInfoDetails } = await Dispatch.repayment.checkSupplyInfo('delay_repay');
    const isNeedSupplyID = !!authInfoDetails && authInfoDetails.filter((process) => process.authParamType === 'COMPENSATE_ID_INFO').length;
    const params = {
      scene: 'SCENE_DELAY_PAY',
      billType: 'extend',
      applyNo: this.applyNo,
    };
    // 记录从延后还提交办理按钮离开的场景
    Madp.setStorageSync('billExtedConfirmBtnLeave', 'Y', 'SESSION');
    if (isNeedSupplyID) {
      Util.gotoSupplyInfo(params);
    } else {
      if (this.miniChannelFlag) {
        Taro.redirectTo({
          url: `/pages/identity/contact?applyNo=${this.applyNo}&scene=SCENE_DELAY_PAY&billType=extend&repaymentFlag=${this.repaymentFlag}`
        });
      } else {
        Util.router.replace({
          path: `/pages/identity/contact?repaymentFlag=${this.repaymentFlag}`,
          query: params
        });
      }
    }
  }

  get paddingBottomHeight() {
    let paddingBottom = '';
    const { duePayTotalAmt } = this;
    const { showPeriodArea } = this.state;
    if (duePayTotalAmt && Number(duePayTotalAmt) > 0) {
      if (showPeriodArea) {
        paddingBottom = '126';
      } else {
        paddingBottom = '111';
      }
    } else {
      if (showPeriodArea) {
        paddingBottom = '120';
      } else {
        paddingBottom = '69';
      }
    }
    return paddingBottom;
  }

  get billExtendInfo() {
    const { selectedCnt } = this.state;
    const orderNoListLast = getStore('selectedBillList'); // 下月还上次试算的借据列表
    const billExtendInfo = {
      delayMonth: selectedCnt,
      loanNo: (orderNoListLast || []).join('、'),
    };
    return billExtendInfo;
  }

  render() {
    const { showPage } = this.state;
    if (!showPage) {
      return <MUView />;
    }
    const {
      selectedExtendBillsAmount, showCannotExtendExplain,
      selectedCnt, showPeriodArea, selectedReason, showReasonArea, selectedExtendBillsOrderNo,
    } = this.state;
    const {
      contractApplyList, contractInfos, availSelectExtendReasons, adjustCntList, selectedExtendBillsCount, cannotSelectExtendBills,
      totalBillsCount, duePayTotalAmt, extendListPlanData, availSelectExtendBills
    } = this;
    // 办理页不展示补充资料协议《贷后资料授权书》
    const contractList = (contractApplyList && contractApplyList.filter((item) => item.contractType !== 'POST_INFO_AUTH')) || [];
    const contractInfoList = (contractInfos && contractInfos.filter((item) => item.contractCode !== 'GRXXSQ_DHZLSQ')) || []; // 3.0合同列表
    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="延后还"
          leftArea={[
            {
              type: 'icon',
              value: 'back'
            }
          ]}
        />
        <MUView
          className="pages-bg extend-list"
          style={`padding-bottom: ${this.paddingBottomHeight}px`}
        >
          <MUView className="extend-list__bills">
            <MUView className="extend-list__bills__wrapper">
              <MUView className="bills__top">
                <MUImage className={classNames('bills__top__slogan', { 'bills__top__slogan-plus': this.isVplus })} src={this.isVplus ? sloganUrl.upperLeftVplus : sloganUrl.upperLeft} />
                <MUView className="bills__top__title">延后还借据本金</MUView>
                <MUView className="bills__top__cnts">可延期{selectedExtendBillsCount}笔/共{totalBillsCount}笔</MUView>
                {cannotSelectExtendBills.length ? (
                  <MUView
                    className="bills__top__explain"
                    beaconId="ShowCannotExtendExplain"
                    onClick={() => this.setState({ showCannotExtendExplain: true })}
                  >
                    <MUIcon value="info" size="16" color="#808080" />
                  </MUView>
                ) : null}
              </MUView>
              <MUView className="bills__middle">
                <MUView className="bills__middle__amount">¥
                  <MUText className="bills__middle__amount__text">{selectedExtendBillsAmount}</MUText>
                </MUView>
                <MUView
                  className="bills__middle__modify brand-text"
                  beaconId="ShowExtendBills"
                  onClick={() => this.changeLoanInfo()}
                >修改借据</MUView>
              </MUView>
              {duePayTotalAmt && Number(duePayTotalAmt) > 0 ? (
                <MUView>
                  <MUView className="bills__line" />
                  <MUView className="bills__bottom">
                    办理前，需先还清全部借据到期金额
                    <MUText className="bills__bottom__point">{duePayTotalAmt}元</MUText>
                  </MUView>
                </MUView>
              ) : null}
              <MUView
                className="bills__bubble brand-bg"
                beaconId="JumpIntroduce"
                onClick={() => Madp.navigateTo({ url: '/pages/bill-extend/intro' })}
              >
                <MUView className="bills__bubble__text">了解延后还</MUView>
                <MUView className="bills__bubble__guide">
                  <MUIcon value="jump-cicle" size="14" color="#fff" />
                </MUView>
              </MUView>
            </MUView>
          </MUView>
          <MUView className="extend-list__period-reason">
            <MUView className="extend-list__period-reason__wrapper">
              <MUView className="period-reason-item">
                <MUView className="period-reason-item__title">延后期数</MUView>
                <MUView
                  className="period-reason-item__selected"
                  beaconId="ShowPeriodArea"
                  onClick={() => {
                    if ((adjustCntList || []).length <= 1) {
                      return;
                    }
                    this.setState({ showPeriodArea: !this.state.showPeriodArea });
                  }}
                >
                  <MUView className="period-reason-item__selected__text">{selectedCnt}期</MUView>
                  {(adjustCntList || []).length > 1 ? (
                    <MUView className="period-reason-item__selected__guide">
                      <MUIcon className={showPeriodArea ? 'icon-accordion-down' : ''} value="arrow-right" size="14" color="#CACACA" />
                    </MUView>
                  ) : null}
                </MUView>
              </MUView>
              {showPeriodArea ? (
                <MUView className="period-area">
                  <MUScrollView
                    className="period-area__scroll"
                    scrollX
                  >
                    {adjustCntList.map((item) => (
                      <MUView
                        className={`period-area__item${selectedCnt === item ? ' brand-selected' : ''}`}
                        beaconId="SelectedCnt"
                        onClick={() => {
                          if (selectedCnt !== item) {
                            this.setState({
                              selectedCnt: item
                            }, async () => {
                              await this.delayRepayCal(false, () => {
                                dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ChangedCnt', beaconContent: { cus: { selectedExtendBillsOrderNo, selectedCnt: item } } });
                              });
                            });
                          }
                        }}
                      >
                        <MUView>{item}期</MUView>
                        {selectedCnt === item ? (
                          <MUImage
                            className="period-area__item__ticked"
                            src={themeColor === '#E60027' ? icTickedRed : icTicked}
                          />
                        ) : null}
                      </MUView>
                    ))}
                  </MUScrollView>
                </MUView>
              ) : null}
              {(availSelectExtendReasons || []).length ? (
                <MUView className="period-reason-item">
                  <MUView className="period-reason-item__title">办理原因</MUView>
                  <MUView
                    className="period-reason-item__selected"
                    beaconId="ShowReasonArea"
                    onClick={() => {
                      if (availSelectExtendReasons.length <= 1) {
                        return;
                      }
                      this.setState({ showReasonArea: true });
                    }}
                  >
                    <MUView className={`period-reason-item__selected__text${selectedReason === '请选择' ? ' select-place-holder' : ''}`}>{selectedReason}</MUView>
                    {availSelectExtendReasons.length > 1 ? (
                      <MUView className="period-reason-item__selected__guide">
                        <MUIcon value="arrow-right" size="14" color="#CACACA" />
                      </MUView>
                    ) : null}
                  </MUView>
                </MUView>
              ) : null}
            </MUView>
          </MUView>
          {extendListPlanData.repayPlanAfterDelay && extendListPlanData.repayPlanAfterDelay.length ? (
            <ExtendListPlan
              extendListPlanData={extendListPlanData}
              isVplus={this.isVplus}
            />
          ) : null}
          <MUView className="extend-list__footer">
            {(contractList.length || contractInfoList.length) ? (
              <MUView className="footer__contract">
                <Protocol
                  trackPrefix="repayment.ExtendList"
                  onChecked={(v) => this.isCheckedContract = v}
                  contractApplyList={contractList}
                  contractInfoList={contractInfoList}
                  billExtendInfo={this.billExtendInfo}
                />
              </MUView>
            ) : null}
            <MUView className="footer__button">
              <MUButton
                type="primary"
                beaconId="ExtendSubmit"
                onClick={() => this.extendSubmit()}
              >提交办理</MUButton>
            </MUView>
          </MUView>
          <MUDialog
            className="dialog-standard-modify"
            isOpened={showCannotExtendExplain}
            beaconId="CannotExtendExplainDialog"
            onClose={() => { this.setState({ showCannotExtendExplain: false }); }}
          >
            <MUView className="cannot-extend-explain">
              <MUView className="cannot-extend-explain__bg" />
              <MUView className="cannot-extend-explain__wrap">
                <MUView className="cannot-extend-explain__wrap__title">不可延期借据说明</MUView>
                <MUView className="cannot-extend-explain__wrap__content">
                  {cannotExtendExplainContent.map((item, i) => (
                    <MUView className="content-item">
                      {i + 1}、{item}
                    </MUView>
                  ))}
                </MUView>
                <MUView
                  className="cannot-extend-explain__wrap__confirm"
                  beaconId="CannotExtendExplainDialogConfirm"
                  onClick={() => { this.setState({ showCannotExtendExplain: false }); }}
                >
                  <MUButton
                    type="primary"
                  >我知道了</MUButton>
                </MUView>
                <MUImage className={classNames('cannot-extend-explain__wrap__slogan', { 'cannot-extend-explain__wrap__slogan-plus': this.isVplus })} src={this.isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
              </MUView>
            </MUView>
          </MUDialog>
          {/* 原始借据列表 */}
          {selectedExtendBillsOrderNo.length ? (
            <OriginalIousList
              ref={(ref) => { this.OriginalIousList = ref; }}
              allBillList={(availSelectExtendBills || []).concat(cannotSelectExtendBills || [])}
              orderInfoListCanExtend={availSelectExtendBills}
              orderInfoListCanNotExtend={cannotSelectExtendBills}
              selectedOrderInfoList={selectedExtendBillsOrderNo}
              isVplus={this.isVplus}
              cancelAll={() => this.onCancelAll()}
              selectAll={() => this.onSelectAll()}
              onSelect={this.handleSelect}
              onConfirm={this.handleConfirm}
            />
          ) : null}
          <MUDrawer
            beaconId="ReasonSelectDrawer"
            show={showReasonArea}
            placement="bottom"
            height="400px"
            contentStyle={{ 'border-radius': '8px 8px 0 0' }}
            onClose={() => { this.setState({ showReasonArea: false }); }}
          >
            <MUView className="reason-select-drawer">
              <MUView className="reason-select-drawer__title">办理原因</MUView>
              <MUView className="reason-select-drawer__content">
                {availSelectExtendReasons.map((item) => (
                  <MUView
                    className={`reason-select-drawer__content__item${selectedReason === item.reason ? ' brand-selected' : ''}`}
                    beaconId="SelectedReason"
                    onClick={() => {
                      this.selectedReasonNo = item.reasonNo;
                      this.setState({ selectedReason: item.reason, showReasonArea: false });
                    }}
                  >
                    <MUView className="item__main">
                      {item.reason}
                      <MUView className="item__main__supply">{item.supply}</MUView>
                    </MUView>
                    {selectedReason === item.reason ? (
                      <MUImage
                        className="item__ticked"
                        src={themeColor === '#E60027' ? icTickedRed : icTicked}
                      />
                    ) : null}
                  </MUView>
                ))}
              </MUView>
              <MUView
                className="reason-select-drawer__close"
                beaconId="ReasonSelectDrawerClose"
                onClick={() => { this.setState({ showReasonArea: false }); }}
              >
                <MUImage src={close} />
              </MUView>
            </MUView>
          </MUDrawer>
        </MUView>
        {/* 交互式运营组件 */}
        <OpRepayment pageId={pageId} opEventKey="opPageLeave" />
      </MUView>
    );
  }
}
