import { Component } from '@tarojs/taro';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import {
  MUView, MUIcon, MUText
} from '@mu/zui';
import { merchantNoList } from '@utils/constants';
import './index.scss';

const themeColor = channelConfig.theme;

const EXTEND_MODE_NUM = {
  OLD: '00',
  NEW: '01'
};

export default class ListItem extends Component {
  static propTypes = {
    item: PropTypes.object,
    scene: PropTypes.string, // 当前借据列表需要展示选择按钮，未来期借据不需要
    checked: PropTypes.bool, // 按钮是否选中
    onItemCheck: PropTypes.func, // 按钮的选择操作
    disabled: PropTypes.bool, // 当前按钮不可选
    isAllZL: PropTypes.bool, //  是否全部商户都是招联金融
    jumpClick: PropTypes.func, // 跳转借据详情页
    extendMode: PropTypes.string, // 再分期模式
  }

  static defaultProps = {
    item: {},
    scene: 'current', // 当前分期列表(current)或再分期的分期列表（future)
    checked: false,
    onItemCheck: () => { },
    disabled: false,
    isAllZL: false,
    jumpClick: () => { },
    extendMode: '',
  }

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  getItemOption(item) {
    const { isAllZL, scene, extendMode } = this.props;
    let option = {};
    if (!Object.keys(item).length) return option;
    const showMerchant = !isAllZL && item.merchantName && merchantNoList.indexOf(item.merchantNo) <= -1;
    if (scene === 'current') {
      option = {
        isOverDue: Boolean(item.displayOverdueDays),
        title: '剩余待还',
        amount: `${item.surplusPayTotalAmt}元`,
        subTitle: '',
        remainAmtOrCnt: `剩余${item.surplusInstallCnt || (item.installTotalCnt - item.installCnt + 1)}/${item.installTotalCnt}期`,
        subDesc: `${Util.getDateCollection(item.loanDate).join('-')} ${item.businessType} ${item.installTotalAmt}元 ${showMerchant ? `来自${item.merchantName.slice(0, 5)}` : ''}`,
      };
    } else {
      option = {
        isOverDue: false, // 一般情况再分期后的借据都不会为逾期状态
        title: '每期应还',
        preCnts: Number(item.extensionInstallTotalCnt) - Number(item.extensionPaidInstallCnt),
        amount: `${item.expectRepayAmt}元`,
        subTitle: `剩余${Number(item.extensionInstallTotalCnt) - Number(item.extensionPaidInstallCnt)}/${item.extensionInstallTotalCnt}期`,
        remainAmtOrCnt: extendMode === EXTEND_MODE_NUM.OLD ? `剩余待还${item.extensionPayTotalAmt}元` : `最后一期应还${item.extLastPlanPayTotalAmt}元`,
        subDesc: `${Util.getDateCollection(item.loanDate).join('-')} ${item.businessType === 'D00' ? '借款' : '消费'} ${item.installTotalAmt}元 ${showMerchant ? `来自${item.merchantName.slice(0, 5)}` : ''}`,
      };
    }
    return option;
  }

  render() {
    const { item = {}, scene, checked, disabled, onItemCheck, jumpClick, extendMode } = this.props;
    const itemOption = this.getItemOption(item);

    return (
      <MUView className="detail-item">
        <MUView className="detail-item-top">
          {scene === 'current' ? (
            <MUView beaconId="SelectClick" onClick={() => onItemCheck(item)}>
              {
                disabled ? (
                  <MUIcon className="item-icon" value="unchecked" size={18} color="rgba(52,119,255, 0.1)" />
                ) : (
                  <MUIcon className="item-icon" value={checked ? 'checked' : 'unchecked'} size={18} color={themeColor || '#3477FF'} />
                )
              }
            </MUView>
          ) : <MUView className="blank-placeholder" />}
          <MUView className="item-detail">
            <MUView className="item-detail-left">
              <MUView className="item-detail-left-title">
                {
                  (scene === 'current') || (scene === 'future' && (extendMode === EXTEND_MODE_NUM.OLD || Number(itemOption.preCnts) < 2)) ? itemOption.title : (
                    <MUText>前<MUText className="title-blue brand-text">{Number(itemOption.preCnts) - 1}</MUText>期 {itemOption.title}</MUText>
                  )
                }
                {
                  itemOption.isOverDue && (
                    <MUText className="item-detail-left-title__label">逾期借据</MUText>
                  )
                }
              </MUView>
              <MUView className="item-detail-left-date">{itemOption.subTitle}</MUView>
            </MUView>
            <MUView className="item-detail-right" beaconId="JumpItemDetail" onClick={() => jumpClick(item)}>
              <MUView className="item-detail-right-money">
                <MUView>{itemOption.amount}</MUView>
                <MUView><MUIcon className="icon" value="arrow-right" size={16} color="#CACACA" /></MUView>
              </MUView>
              <MUView className="item-detail-right-remain">{itemOption.remainAmtOrCnt}</MUView>
            </MUView>

          </MUView>
        </MUView>
        <MUView className={classNames('detail-item-bottom', { interval: scene === 'future' })}>
          {itemOption.subDesc}
        </MUView>
      </MUView>
    );
  }
}
