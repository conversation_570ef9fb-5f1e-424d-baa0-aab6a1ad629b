import Taro, { Component } from '@tarojs/taro';
import {
  MUView, MUScrollView, MUText, MUIcon, MUDialog, MUButton, MUImage
} from '@mu/zui';
import PropTypes from 'prop-types';
import Madp from '@mu/madp';
import classNames from 'classnames';
import { miniProgramChannel, sloganUrl } from '@utils/constants';
import { setStore } from '@api/store';

import './index.scss';

const interestExplainObj = {
  title: '息费说明',
  explain: '本期息费高于其他期数息费是因为办理延后还服务后，延后还款的本金在延期期间需按原借款合同约定支付息费，固本期息费高于其他期数息费。',
  formulaTitle: '计算公式：',
  formula: '息费=延后还款本金*约定利率*延后天数',
  buttonText: '我知道了'
};

export default class ExtendListPlan extends Component {
  static propTypes = {
    extendListPlanData: PropTypes.object,
    beaconPrefix: PropTypes.string,
    isVplus: PropTypes.bool,
  };

  static defaultProps = {
    extendListPlanData: {},
    beaconPrefix: '',
    isVplus: false,
  }

  constructor(props) {
    super(props);
    this.state = {
      showInterestExplain: false, // 展示息费说明
    };
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
  }

  getRepayDate = (date) => {
    return `${date.substring(0, 4)}/${date.substring(4, 6)}/${date.substring(6, 8)}`;
  }

  jumpToPlanDetail = (itemData = {}) => {
    setStore({ futureBillList: itemData && itemData.delayRepayPlan });
    if (this.miniChannelFlag) {
      Taro.navigateTo({
        url: `/pages/bill-list-future/index?billType=extend&repayDate=${itemData.payDate}`
      });
    } else {
      Madp.navigateTo({
        url: `/pages/bill-list-future/index?billType=extend&repayDate=${itemData.payDate}`
      });
    }
  }

  render() {
    const {
      extendListPlanData, beaconPrefix = '', isVplus
    } = this.props || {};
    const {
      title, repayPlanAfterDelay
    } = extendListPlanData || {};
    const { showInterestExplain } = this.state;
    const {
      explain, formulaTitle, formula, buttonText
    } = interestExplainObj;

    return (
      <MUView className="extend-list__plan">
        <MUView className="extend-list__plan__wrapper">
          <MUView className="plan__title">{title}</MUView>
          <MUView className="plan__tip brand-selected">
            延后还款的本金，正常按日计息，具体以还款计划为准
          </MUView>
          <MUView className="plan__details">
            <MUScrollView
              className="plan__details__scroll"
              scrollY
            >
              {repayPlanAfterDelay.map((item, i) => (
                <MUView className="plan__details__item">
                  <MUView className="item__repay-date">{this.getRepayDate(item.payDate)}</MUView>
                  <MUView className="item__divide">
                    <MUView className="item__divide__circle brand-border" />
                    <MUView
                      className="item__divide__line"
                      style={repayPlanAfterDelay.length - 1 === i ? 'display: none' : ''}
                    />
                  </MUView>
                  <MUView className="item__loan">
                    <MUView className="item__loan__message">
                      <MUView className="message__total">
                        <MUView className="message__total__extend-money">{item.surplusPayTotalAmt ? Number(item.surplusPayTotalAmt).toFixed(2) : '0.00'}元</MUView>
                        {(item.delayRepayPlanNum && Number(item.delayRepayPlanNum) > 0) ? (
                          <MUView className="message__total__extend-num brand-selected">
                            {item.delayRepayPlanNum}笔延期借据
                          </MUView>
                        ) : null}
                      </MUView>
                      <MUView className="message__part">
                        <MUView>
                          {`本金${item.surplusPayPrincipalAmt ? Number(item.surplusPayPrincipalAmt).toFixed(2) : '0.00'}元，息费`}
                          <MUText className={item.hasFirstRepayPlan === 'Y' ? 'message__part__point' : ''}>
                            {`${item.surplusPayInteAmt ? Number(item.surplusPayInteAmt).toFixed(2) : '0.00'}元`}
                          </MUText>
                        </MUView>
                        {item.hasFirstRepayPlan === 'Y' ? (
                          <MUView
                            className="message__part__explain"
                            beaconId="ShowInterestExplain"
                            onClick={() => this.setState({ showInterestExplain: true })}
                          >
                            <MUIcon value="info" size="16" color="#808080" />
                          </MUView>
                        ) : null}
                      </MUView>
                    </MUView>
                    <MUView
                      className="item__loan__guide"
                      beaconId={`${beaconPrefix}jumpPlanDetail`}
                      onClick={() => this.jumpToPlanDetail(item)}
                    >
                      <MUIcon value="arrow-right" size="14" color="#CACACA" />
                    </MUView>
                  </MUView>
                </MUView>
              ))}
            </MUScrollView>
          </MUView>
        </MUView>
        <MUDialog
          className="dialog-standard-modify"
          isOpened={showInterestExplain}
          beaconId="ShowInterestExplainDialog"
          onClose={() => { this.setState({ showInterestExplain: false }); }}
        >
          <MUView className="show-interest-explain">
            <MUView className="show-interest-explain__bg" />
            <MUView className="show-interest-explain__wrap">
              <MUView className="show-interest-explain__wrap__title">{interestExplainObj.title}</MUView>
              <MUView className="show-interest-explain__wrap__explain">{explain}</MUView>
              <MUView className="show-interest-explain__wrap__formula-title">{formulaTitle}</MUView>
              <MUView className="show-interest-explain__wrap__formula">{formula}</MUView>
              <MUView
                className="show-interest-explain__wrap__confirm"
              >
                <MUButton
                  type="primary"
                  beaconId="ShowInterestExplainDialogConfirm"
                  onClick={() => { this.setState({ showInterestExplain: false }); }}
                >
                  {buttonText}
                </MUButton>
              </MUView>
              <MUImage
                className={classNames(
                  'show-interest-explain__wrap__slogan',
                  { 'show-interest-explain__wrap__slogan-plus': isVplus }
                )}
                src={isVplus ? sloganUrl.middleVplus : sloganUrl.middle}
              />
            </MUView>
          </MUView>
        </MUDialog>
      </MUView>
    );
  }
}
