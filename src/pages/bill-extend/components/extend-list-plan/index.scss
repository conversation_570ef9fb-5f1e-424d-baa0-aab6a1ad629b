.extend-list__plan {
  margin: 20px;

  &__wrapper {
    padding: 38px 30px 10px;
    background: #fff;
    border-radius: 16px;

    .plan {
      &__title {
        font-size: 32px;
        line-height: 32px;
        color: #333;
        font-weight: 500;
      }

      &__tip {
        margin-top: 20px;
        padding: 6px 16px;
        border-radius: 8px;
        font-size: 22px;
        line-height: 36px;
        height: 36px;
      }

      &__details {
        margin: 36px 0;
        height: 556px;
        overflow: hidden;

        &__scroll {
          height: 556px;
        }

        &__item {
          display: flex;
          align-items: flex-start;
          font-size: 28px;
          line-height: 28px;
          color: #333;

          .item {
            &__repay-date {
              display: flex;
              align-items: center;
              height: 40px;
              width: 160px;
            }

            &__divide {
              margin: 0 26px 0 20px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;

              &__circle {
                margin: 10px 0;
                width: 10px;
                height: 10px;
                border: 4px solid #3477FF;
                border-radius: 50%;
                font-size: 0;
              }

              &__line {
                margin: 2px 6px;
                width: 2px;
                height: 67px;
                background: #cacaca;
                border-radius: 1px;
              }
            }

            &__loan {
              flex: 1;
              display: flex;
              justify-content: space-between;
              align-items: flex-start;

              &__message {
                .message {
                  &__total {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    height: 40px;

                    &__extend-money {
                      color: #333333;
                      text-align: left;
                      font-size: 28px;
                      line-height: 28px;
                    }

                    &__extend-num {
                      margin-left: 10px;
                      padding: 7px 10px;
                      font-size: 24px;
                      line-height: 26px;
                      border-radius: 4px;
                    }
                  }

                  &__part {
                    margin-top: 10px;
                    display: flex;
                    align-items: center;
                    font-size: 24px;
                    line-height: 24px;
                    color: #a6a6a6;

                    &__point {
                      color: #cc1f15;
                    }

                    &__explain {
                      margin-left: 10px;
                      font-size: 0;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .show-interest-explain {
    &__bg {
      height: 165px;
      background-image: linear-gradient(180deg, #3477FF 0%, #FFFFFF 100%);
      opacity: 0.3;
    }

    &__wrap {
      position: relative;
      margin-top: -125px;
      padding: 0 40px;

      &__title {
        margin-bottom: 20px;
        font-size: 36px;
        line-height: 54px;
        color: #333;
        font-weight: 600;
        text-align: center;
      }

      &__explain {
        margin-bottom: 20px;
        font-size: 28px;
        line-height: 42px;
        color: #333;
        font-weight: 400;
        text-align: left;
      }

      &__formula-title,
      .show-interest-explain__wrap__formula {
        font-size: 24px;
        line-height: 42px;
        color: #808080;
        font-weight: 400;
        text-align: left;
      }

      &__formula {
        font-weight: 600;
      }

      &__confirm {
        margin: 40px 0 30px;
      }
      &__slogan {
        width: 206px;
        height: 44px;
        margin: 0 auto;
        padding-bottom: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
  
        &-plus {
          width: 249px;
        }
      }
    }
  }
}