import { Component } from '@tarojs/taro';
import {
  MUView,
  MUText,
  MUIcon,
  MUModal,
  MUButton,
} from '@mu/zui';
import PropTypes from 'prop-types';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import CustomModal from '../custom-modal/index';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import Madp from '@mu/madp';
import './index.scss';

const themeColor = Util.getThemeColor(channelConfig.theme);

export default class DetailItem extends Component {
    static propTypes = {
      title: PropTypes.string,
      hasPay: PropTypes.bool,
      afterTitle: PropTypes.string,
      plan: PropTypes.oneOfType([PropTypes.object]),
      showSectionRateFlag: PropTypes.string,
      loanType: PropTypes.string,
    }

    static defaultProps = {
      title: '',
      hasPay: false,
      afterTitle: '',
      plan: {},
      showSectionRateFlag: '',
      loanType: '',
    }

    state = {
      showAccordion: false,
      showWaiveModal: false,
      showSectionRateModal: false,
    }

    transWaiveAmtDetail = {}; // 还款使用优惠处理

    getAccordionContent() {
      const { plan, showSectionRateFlag, loanType } = this.props;
      const principalAmt = plan.planStatus === '01' ? plan.actualPayPrincipalAmt : plan.payPrincipalAmt;
      // 期费用与利息字段合并
      let payInteAmt = 0;
      let payPeriodFeeAmt = 0;
      if (loanType === 'I') {
        payInteAmt = plan.planStatus === '01' ? plan.actualPayInteAmt : plan.payInteAmt;
      }
      if (loanType === 'F') {
        payPeriodFeeAmt = plan.planStatus === '01' ? plan.actualPayInteAmt : plan.payInteAmt;
      }
      const payFineAmt = plan.planStatus === '01' ? plan.actualPayFineAmt : plan.payFineAmt;
      const payPrepayFeeAmt = plan.planStatus === '01' ? plan.actualPayPrepayFeeAmt : plan.payPrepayFeeAmt;
      const payOneTimeFeeAmt = plan.planStatus === '01' ? plan.actualPayOneTimeFeeAmt : plan.payOneTimeFeeAmt;
      if (payOneTimeFeeAmt && Number(payOneTimeFeeAmt) > 0) {
        if (loanType === 'I') {
          payInteAmt = payInteAmt && Number(payInteAmt) >= 0
            ? ((Number(payOneTimeFeeAmt) * 100 + Number(payInteAmt) * 100) / 100).toFixed(2) : payOneTimeFeeAmt;
        } else if (loanType === 'F') {
          payPeriodFeeAmt = payPeriodFeeAmt && Number(payPeriodFeeAmt) >= 0
            ? ((Number(payOneTimeFeeAmt) * 100 + Number(payPeriodFeeAmt) * 100) / 100).toFixed(2) : payOneTimeFeeAmt;
        }
      }
      const transWaiveAmtDetail = this.handleAwardDetailList(plan.repayPlanAwardDetailList);
      const {
        merchantPriceWaiveAmt, loanWaiveAmt, manualWaiveAmt, memberWaiveAmt, repayWaiveAmt, settleWaiveAmt, tempWaiveAmt
      } = transWaiveAmtDetail;
      const waiveAmt = Number(merchantPriceWaiveAmt)
        + Number(loanWaiveAmt)
        + Number(manualWaiveAmt)
        + Number(memberWaiveAmt)
        + Number(repayWaiveAmt)
        + Number(settleWaiveAmt)
        + Number(tempWaiveAmt);
      const showSectionRateIcon = showSectionRateFlag === 'Y' && plan.actualPayRecallInteAmt
        && Number(plan.actualPayRecallInteAmt) > 0;
      const { showSectionRateModal } = this.state;
      return (
        <MUView className="accordion-content">
          <MUView className="accordion-row">
            <MUView className="accordion-row-left">本金</MUView>
            <MUView className="accordion-row-right">
              {principalAmt}
              元
            </MUView>
          </MUView>
          {payInteAmt > 0 && (
            <MUView className="accordion-row">
              <MUView className="accordion-row-left">利息</MUView>
              <MUView
                className="accordion-row-right"
                beaconId={showSectionRateIcon ? 'showSectionRateModal' : 'noSectionRateModal'}
                onClick={(e) => {
                  if (showSectionRateIcon) {
                    e.stopPropagation();
                    this.setState({
                      showSectionRateModal: true
                    });
                  }
                }}
              >
                {payInteAmt}
                元
                {showSectionRateIcon ? (
                  <MUText className="info-icon-wrapper">
                    <MUText className="info-icon" />
                  </MUText>
                ) : null}
              </MUView>
            </MUView>
          )}
          {payPeriodFeeAmt > 0 && (
            <MUView className="accordion-row">
              <MUView className="accordion-row-left">期费用</MUView>
              <MUView className="accordion-row-right">
                {payPeriodFeeAmt}
                元
              </MUView>
            </MUView>
          )}
          {payFineAmt > 0 && (
            <MUView className="accordion-row">
              <MUView className="accordion-row-left">罚息</MUView>
              <MUView className="accordion-row-right">
                {payFineAmt}
                元
              </MUView>
            </MUView>
          )}
          {payPrepayFeeAmt > 0 && (
            <MUView className="accordion-row">
              <MUView className="accordion-row-left">提前还款违约金</MUView>
              <MUView className="accordion-row-right">
                {payPrepayFeeAmt}
                元
              </MUView>
            </MUView>
          )}
          {Number(waiveAmt) > 0 && (
            <MUView className="accordion-row">
              <MUView className="accordion-row-left">已优惠</MUView>
              <MUView className="accordion-row-right">
                {waiveAmt.toFixed(2)}
                元
                <MUText beaconId="clickWaiveIcon" onClick={this.switchWaiveModal} className="info-icon-wrapper">
                  <MUText className="info-icon" />
                </MUText>
              </MUView>
            </MUView>
          )}
          <MUModal
            beaconId="SectionRateModal"
            isOpened={showSectionRateModal}
            className="section-rate-modal"
            onClose={() => this.setState({ showSectionRateModal: false })}
          >
            <MUView>
              <MUView className="section-rate-modal__title">利息说明</MUView>
              <MUView className="section-rate-modal__content">
                利息{Number(payInteAmt).toFixed(2)}元（此借据使用了分段利率，提前还款退还已优惠利息共
                <MUText className="section-rate-modal__content--special">
                  {Number(plan.actualPayRecallInteAmt).toFixed(2)}元
                </MUText>）
              </MUView>
              <MUView className="section-rate-modal__desc">
                *退还的已优惠利息=提前还款本金*优惠天数*(优惠前年利率-优惠后年利率)/365
              </MUView>
              <MUButton
                customStyle={`color: ${themeColor}`}
                beaconId="SectionRateModalButton"
                onClick={() => this.setState({ showSectionRateModal: false })}
              >我知道了</MUButton>
            </MUView>
          </MUModal>
        </MUView>
      );
    }

  isEmpty = (value) => {
    if (value === null || value === undefined) { // 检查 null 和 undefined
      return true;
    }

    if (typeof value === 'string' || Array.isArray(value)) {
      return value.length === 0; // 检查空字符串和空数组
    }

    if (typeof value === 'object') {
      return Object.keys(value).length === 0; // 检查空对象
    }

    return false; // 其他类型默认不为空
  };

  // 处理优惠券与优惠标签
handleAwardDetailList = (awardDetailList = []) => {
  const discountDetailInfo = {
    merchantPriceWaiveAmt: 0, // APP专享折扣
    loanWaiveAmt: 0, // 借款优惠券
    memberWaiveAmt: 0, // 微光卡折扣
    tempWaiveAmt: 0, // 限时特价
    repayWaiveAmt: 0, // 还款优惠
    settleWaiveAmt: 0, // 随机立减
    manualWaiveAmt: 0, // 协商减免
  };

  // 返回的可能是null、[]
  if (this.isEmpty(awardDetailList)) {
    return discountDetailInfo;
  }


  awardDetailList.forEach((item) => {
    switch (item.awardType) {
      case '11':
        discountDetailInfo.merchantPriceWaiveAmt = item.awardAmt;
        break;
      case '3X':
        discountDetailInfo.loanWaiveAmt = item.awardAmt;
        break;
      case '71':
        discountDetailInfo.memberWaiveAmt = item.awardAmt;
        break;
      case '21':
        discountDetailInfo.tempWaiveAmt = item.awardAmt;
        break;
      case '4X':
        discountDetailInfo.repayWaiveAmt = item.awardAmt;
        break;
      case '61':
        discountDetailInfo.settleWaiveAmt = item.awardAmt;
        break;
      case '51':
        discountDetailInfo.manualWaiveAmt = item.awardAmt;
        break;
      default:
        console.log(`Unknown awardType: ${item.awardType}`);
    }
  });

  this.transWaiveAmtDetail = discountDetailInfo;

  return discountDetailInfo;
};

    switchAccordion = () => {
      const { showAccordion } = this.state;
      this.setState({ showAccordion: !showAccordion });
      Util.handleTouchScroll(true, 'original-detail-item-frozen');
      if (!showAccordion) {
        dispatchTrackEvent({
          target: this,
          event: EventTypes.EV,
          beaconId: 'traderecords.LoanDetail.openAccordion'
        });
      }
    }

    switchWaiveModal = () => {
      const { showWaiveModal } = this.state;
      this.setState({ showWaiveModal: !showWaiveModal });
    }

    render() {
      const {
        title, hasPay, afterTitle, plan
      } = this.props;

      const { showAccordion, showWaiveModal } = this.state;
      const { transWaiveAmtDetail } = this;

      return (
        <MUView className="original-detail-item">
          <MUView className="original-detail-item-content">
            <MUView className="original-detail-item-content-left">
              <MUText className="original-detail-item-content-left-pay">{hasPay ? '已还' : '未还'}</MUText>
              <MUView>
                <MUView className="original-detail-item-content-left-date">{title}</MUView>
                {plan.extendInteDateFlag === 'Y' && plan.inteDate ? (
                  <MUView className="original-detail-item-credit-product-info">
                    诚信保护期至
                    <MUView
                      beaconId="CreditProductInfo"
                      style={`color:${themeColor}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        Madp.showModal({
                          content: '每日按正常利率计息，诚信保护期内完成还款不影响个人征信。',
                          showCancel: false,
                          confirmText: '知道了',
                          confirmColor: themeColor,
                        });
                      }}
                    >
                      {`${Util.getDateCollection(plan.inteDate).splice(1).join('月')}日`}
                    </MUView>
                  </MUView>) : null}
              </MUView>
            </MUView>
            <MUText className="original-detail-item-content-money">
              <MUView>{afterTitle}</MUView>
              <MUView beaconId="switchAccordion" className="icon-accordion-wrapper" onClick={this.switchAccordion}>
                <MUIcon className={`icon-accordion ${showAccordion ? 'icon-accordion-up' : 'icon-accordion-down'}`} value="arrow-left" />
              </MUView>
            </MUText>
          </MUView>
          {
            showAccordion
              ? this.getAccordionContent()
              : <MUView />
          }
          <CustomModal
            pageId="traderecords.LoanDetail"
            beaconId="ShowWaiveModal"
            isOpened={showWaiveModal}
            closeOnClickOverlay={false}
          >
            <MUView className="waive-modal">
              <MUIcon
                className="waive-modal-close-icon"
                beaconId="closeIcon"
                onClick={this.switchWaiveModal}
                color="#CACACA"
                value="close2"
                size="20"
              />
              <MUView className="waive-modal-title">优惠详情</MUView>
              {Number(transWaiveAmtDetail && transWaiveAmtDetail.merchantPriceWaiveAmt) > 0 && (
                <MUView className="waive-row">
                  <MUView className="waive-row-left">APP专享折扣</MUView>
                  <MUView className="waive-row-right">
                    已优惠
                    {transWaiveAmtDetail.merchantPriceWaiveAmt}
                    元
                  </MUView>
                </MUView>
              )}
              {Number(transWaiveAmtDetail && transWaiveAmtDetail.loanWaiveAmt) > 0 && (
                <MUView className="waive-row">
                  <MUView className="waive-row-left">借款优惠券</MUView>
                  <MUView className="waive-row-right">
                    已优惠
                    {transWaiveAmtDetail.loanWaiveAmt}
                    元
                  </MUView>
                </MUView>
              )}
              {Number(transWaiveAmtDetail && transWaiveAmtDetail.memberWaiveAmt) > 0 && (
                <MUView className="waive-row">
                  <MUView className="waive-row-left">
                    微光卡折扣
                    <MUText className="benefit-card-icon" />
                  </MUView>
                  <MUView className="waive-row-right">
                    已优惠
                    {transWaiveAmtDetail.memberWaiveAmt}
                    元
                  </MUView>
                </MUView>
              )}
              {Number(transWaiveAmtDetail && transWaiveAmtDetail.tempWaiveAmt) > 0 && (
                <MUView className="waive-row">
                  <MUView className="waive-row-left">限时特价</MUView>
                  <MUView className="waive-row-right">
                    已优惠
                    {transWaiveAmtDetail.tempWaiveAmt}
                    元
                  </MUView>
                </MUView>
              )}
              {Number(transWaiveAmtDetail && transWaiveAmtDetail.repayWaiveAmt) > 0 && (
                <MUView className="waive-row">
                  <MUView className="waive-row-left">还款优惠</MUView>
                  <MUView className="waive-row-right">
                    已优惠
                    {transWaiveAmtDetail.repayWaiveAmt}
                    元
                  </MUView>
                </MUView>
              )}
              {Number(transWaiveAmtDetail && transWaiveAmtDetail.settleWaiveAmt) > 0 && (
                <MUView className="waive-row">
                  <MUView className="waive-row-left">随机立减</MUView>
                  <MUView className="waive-row-right">
                    已优惠
                    {transWaiveAmtDetail.settleWaiveAmt}
                    元
                  </MUView>
                </MUView>
              )}
              {Number(transWaiveAmtDetail && transWaiveAmtDetail.manualWaiveAmt) > 0 && (
                <MUView className="waive-row">
                  <MUView className="waive-row-left">协商减免</MUView>
                  <MUView className="waive-row-right">
                    已优惠
                    {transWaiveAmtDetail.manualWaiveAmt}
                    元
                  </MUView>
                </MUView>
              )}
            </MUView>
          </CustomModal>
        </MUView>
      );
    }
}
