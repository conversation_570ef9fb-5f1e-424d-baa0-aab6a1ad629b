.original-detail-item {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px 30px;
  color: #808080;
  border-bottom: 1px solid #e6ebf0;
  background-color: #fff;

  &-content {
    display: flex;
    flex: 1;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    &-left {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      &-pay {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 22px;
        line-height: 22px;
        border: 2px solid #808080;
        padding: 4px 6px;
        border-radius: 4px;
      }

      &-date {
        color: #333;
        font-size: 30px;
        margin-left: 20px;
      }
    }

    &-money {
      color: #333;
      font-size: 30px;
      line-height: 30px;
      display: flex !important;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      .icon-accordion-wrapper {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
      }
    }
  }

  &-credit-product-info {
    display: flex;
    margin-left: 20px;
    font-size: 28px;
    line-height: 1;
    color: #808080;
  }

  .icon-accordion {
    margin-left: 8px;
    color: #a6a6a6;
    font-size: 26px !important;
  }

  .icon-accordion-up {
    transform: rotate(90deg);
  }

  .icon-accordion-down {
    transform: rotate(-90deg);
  }

  .accordion-content {
    font-size: 24px;
    color: #a6a6a6;
    padding-top: 24px;

    .accordion-row {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .accordion-row-right {
        padding-right: 34px;
        position: relative;

        .info-icon-wrapper {
          position: absolute;
          top: -4px;
          right: -10px;
          display: block;
          padding: 10px;

          .info-icon {
            display: block;
            width: 24px;
            height: 24px;
            background: url("../../img/info-icon.png") no-repeat;
            background-size: contain;
          }
        }
      }
    }

    .section-rate-modal {
      text-align: center;

      &__title {
        font-size: 36px;
        line-height: 1;
        color: #333;
        font-weight: 500;
      }

      &__content {
        padding: 36px 40px 0;
        font-size: 32px;
        line-height: 45px;
        color: #808080;
        font-weight: 400;
      }

      &__content--special {
        color: #FE5A5F;
      }

      &__desc {
        padding: 20px 40px 36px;
        font-size: 26px;
        line-height: 40px;
        color: #888888;
        font-weight: 400;
      }
    }
  }
}

.waive-modal {
  color: #333;
  padding: 30px 40px;
  padding-top: 0;
  position: relative;

  .waive-modal-close-icon {
    position: absolute;
    top: -20px;
    right: 20px;
  }

  .waive-modal-title {
    text-align: center;
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 40px;
  }

  .waive-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24px;
    height: 44px;

    .waive-row-left {
      display: flex;
      align-items: center;
    }

    .waive-row-right {
      color: #808080;
    }
  }
}