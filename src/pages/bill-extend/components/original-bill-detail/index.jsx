import Taro, { Component } from '@tarojs/taro';
import {
  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, MUIcon, MUImage, MUModal
} from '@mu/zui';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import Dispatch from '@api/actions';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import { getLoginInfo } from '@mu/business-basic';

import DetailMainItem from '../detail-main-item/index';
import DetailItem from '../original-detail-item/index';
import ActivePayIcon from '../../img/active_pay.png';
import AutoPayIcon from '../../img/auto_pay.png';
import LoanIcon from '../../img/loan.png';
import LoanInstallmentIcon from '../../img/loan_installment.png';
import ConsumeIcon from '../../img/consume.png';
import finishedImg from '../../img/finished.png';

import './index.scss';

export default class OriginalBillDetail extends Component {
  static propTypes = {
    orderNo: PropTypes.string,
  };

  static defaultProps = {
    orderNo: '',
  }

  constructor(props) {
    super(props);
    this.state = {
      recordDetail: {},
      repayScheduleList: [],
      orderInfo: {},
      isOpened: false,
    };
  }

  componentWillReceiveProps(nextProps) {
    const { orderNo: orderNoOld } = this.props;
    if (nextProps.orderNo !== orderNoOld) {
      this.getLoanDetail(nextProps.orderNo);
    }
  }

  async getLoanDetail(orderNo) {
    const loginInfo = await getLoginInfo();
    const { userIdHash, custIdHash } = loginInfo || {};
    const result = await Dispatch.repayment.queryDetailNew({ orderNo, queryScene: '02', userHashNo: userIdHash, custHashNo: custIdHash });
    const { data } = result || {};
    const { loanInfoDetail } = data || {};
    const loanRecordDetail = this.getRecordDetail(loanInfoDetail);
    this.setState({
      repayScheduleList: loanInfoDetail && loanInfoDetail.repaymentPlanList,
      orderInfo: loanInfoDetail,
      recordDetail: loanRecordDetail,
    });
  }

  // TODO: 贷款暂未返回，返回后验证
  getRecordDetail = (orderInfo = {}) => {
    const recordDetail = orderInfo;
    recordDetail.transAmt = orderInfo.installTotalAmt;
    recordDetail.transType = 'LOAN'; // transType借款记录写死的LOAN
    let isInstallment = false;
    if (recordDetail.transType === 'LOAN') { // 借款记录
      if ((orderInfo.merchantNo === '20900005' || orderInfo.merchantNo === '20900011') && orderInfo.businessType === 'T00') { // 招行和浦发 消费类型
        recordDetail.iconLink = LoanInstallmentIcon;
        isInstallment = true;
      } else if (orderInfo.transCode === 'L01') {
        recordDetail.iconLink = LoanIcon;
      } else if (orderInfo.transCode === 'L02' || orderInfo.transCode === 'L04') {
        recordDetail.iconLink = ConsumeIcon;
      } else if (orderInfo.transDesc === '招联商城') {
        recordDetail.iconLink = ConsumeIcon;
      } else {
        recordDetail.iconLink = LoanIcon;
      }
    } else if (orderInfo.transCode === 'R05') {
      recordDetail.iconLink = AutoPayIcon;
    } else {
      recordDetail.iconLink = ActivePayIcon;
    }
    if (isInstallment) {
      recordDetail.merchantName = '招联分期花';
    } else {
      recordDetail.merchantName = orderInfo.transDesc; // merchantName 商户名
    }
    if (orderInfo.orderStatus === '01' || orderInfo.orderStatus === '10' || orderInfo.orderStatus === '13' || orderInfo.paidCnts === orderInfo.installTotalCnt) {
      if (orderInfo.totalReturnAmt && Number(orderInfo.totalReturnAmt) > 0) { // 不为空且大于0说明有退款
        recordDetail.hasRefund = true;
      }
    }
    const datePattern = /(\d{4})(\d{2})(\d{2})[ ]*(\d{2})(\d{2})(\d{2})/;
    recordDetail.transDate = `${orderInfo.loanDate}${orderInfo.loanTime}`.replace(datePattern, '$1/$2/$3 $4:$5:$6');
    return recordDetail;
  }

  payStateMsg() {
    const { orderInfo, recordDetail } = this.state;
    if (recordDetail.transCode === 'TC3') { // 账单分期
      return '已分期';
    }
    return `已还${orderInfo.paidCnts}期/共${orderInfo.installTotalCnt}期`;
  }

  // 20201201 -> 2020/12/01
  dateFilter(date) {
    return JSON.stringify(date).replace(/(\d{4})(\d{2})(\d{2})/, '$1/$2/$3');
  }

  getRepayHeight = () => (`${Taro.pxTransform(1000 - 130)}`)

  show() {
    this.setState({
      isOpened: true
    }, () => {
      const { isOpened } = this.state || {};
      if (isOpened) {
        dispatchTrackEvent({
          target: this,
          beaconId: 'OriginalBillDetailShow',
          event: EventTypes.SO,
          beaconContent: {
            cus: {}
          }
        });
      }
    });
  }

  hide() {
    this.setState({
      isOpened: false
    }, () => {
      const { isOpened } = this.state || {};
      Util.handleTouchScroll(true, 'original-detail-item-frozen');
      if (!isOpened) {
        dispatchTrackEvent({
          target: this,
          beaconId: 'OriginalBillDetailHide',
          event: EventTypes.BC,
          beaconContent: {
            cus: {}
          }
        });
      }
    });
  }

  render() {
    const { isOpened, recordDetail, orderInfo, repayScheduleList } = this.state;
    // 复杂的收款对象
    let Payee;
    let contentMsg = orderInfo && orderInfo.bankName;
    if (recordDetail && recordDetail.transCode === 'TC3') { // 账单分期
      Payee = <MUView />;
    } else if (recordDetail && (recordDetail.transCode === 'L02' || recordDetail.transCode === 'L04')) {
      if (orderInfo && orderInfo.goodsName) {
        Payee = <DetailMainItem title="收款商户" content={orderInfo.goodsName} />;
      } else {
        Payee = <MUView />;
      }
    } else {
      if (orderInfo && orderInfo.bankName === '支付宝') {
        contentMsg = '支付宝账户';
      } else {
        contentMsg = `${orderInfo && orderInfo.bankName || ''}(${orderInfo && orderInfo.receiveBankCardNo && orderInfo.receiveBankCardNo.substr(-4) || ''})`;
      }
      if (orderInfo && orderInfo.bankName && contentMsg && contentMsg !== '()') {
        Payee = <DetailMainItem title="收款方" content={contentMsg} />;
      } else {
        Payee = <MUView />;
      }
    }

    return (
      <MUView>
        <MUDrawer
          beaconId="LoanIousDrawer"
          show={isOpened}
          placement="bottom"
          height={Taro.pxTransform(1000)}
          onClose={() => { this.hide(); }}
        >
          <MUView className="bill-details">
            <MUView className="bill-details-top">
              <MUView className="bill-details-top-holder" />
              <MUView className="bill-details-top-center">
                <MUText className="bill-details-top-center-title">借款详情</MUText>
              </MUView>
              <MUView beaconId="DrawerClose" className="bill-details-top-closeImg" onClick={() => { this.hide(); }} >
                <MUIcon className="bill-details-top-closeImg-close" value="close2" size={18} color="#A6A6A6" />
              </MUView>
            </MUView>
            <MUView className="bill-details-content" style={{ height: this.getRepayHeight() }}>
              <MUView className="bill-details-content-panel">
                <MUView className="header">
                  <MUImage className="header-img" src={recordDetail && recordDetail.iconLink} />
                  <MUText className="header-text">{recordDetail && recordDetail.merchantName}</MUText>
                </MUView>
                <MUView className="middle">
                  <MUView className="middle-amount">
                    <MUText className="middle-amount-label">￥</MUText>
                    <MUText className="middle-amount-value">{recordDetail && recordDetail.transAmt}</MUText>
                  </MUView>
                </MUView>
              </MUView>
              <MUView className="bill-details-content-info">
                {Payee}
                <DetailMainItem
                  title={recordDetail && (recordDetail.transCode === 'L02' || recordDetail.transCode === 'L04') ? '消费时间' : '借款时间'}
                  content={recordDetail && recordDetail.transDate}
                />
              </MUView>
              <MUView className="bill-details-content-list">
                {orderInfo && orderInfo.installTotalCnt && (
                  <MUView className="repay">
                    <MUText className="repay-process">
                      还款进度
                    </MUText>
                    {orderInfo && orderInfo.orderStatus === '01'
                      ? (
                        <MUView className="repay-finish">
                          <MUImage className="repay-finish-img" src={finishedImg} />
                          <MUText className="repay-finish-text">已还清</MUText>
                        </MUView>
                      )
                      : <MUView className="repay-period theme-color">{this.payStateMsg()}</MUView>}
                  </MUView>
                )}
                {repayScheduleList
                  ? repayScheduleList.map((plan) => (
                    <DetailItem
                      plan={plan}
                      title={this.dateFilter(plan.repayDate)}
                      hasPay={plan.planStatus === '01'}
                      afterTitle={`￥${plan.payTotalAmt || ''}`}
                      showSectionRateFlag={orderInfo.actualPayRecallInteAmt && Number(orderInfo.actualPayRecallInteAmt) > 0 ? 'Y' : 'N'}
                      loanType={orderInfo.loanType}
                    />
                  ))
                  : <MUView />}
              </MUView>

            </MUView>
          </MUView>
        </MUDrawer>
      </MUView>
    );
  }
}
