.bill-details {
  background-color: #F3F3F3;
  height: 100%;

  &-top {
    height: 100px;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 8px;

    &-holder {
      width: 105px;
    }

    &-center {
      flex: 1;
      display: flex;
      font-size: 36px;
      color: #333333;
      justify-content: center;
      font-weight: bold;
    }

    &-closeImg {
      padding-right: 30px;
      margin: auto 0;
      height: 100px;
      width: 75px;
      position: relative;
      background-color: transparent;

      &-close {
        display: block;
        width: 32px;
        height: 32px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      image {
        width: 32px;
        height: 32px;
      }

      img {
        width: 32px;
        height: 32px;
      }
    }
  }

  &-content {
    height: 100% - 100;
    margin: 20px;
    overflow-y: scroll;

    &-panel {
      padding: 30px 30px 45px;
      background: #fff;
      border-bottom: 1px solid #e6ebf0;

      .header {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &-img {
          width: 50px;
          height: 50px;
          margin-right: 20px;
        }

        &-text {
          font-size: 30px;
          color: #333;
        }
      }

      .middle {
        &-amount {
          margin-top: 15px;
          font-size: 84px;
          color: #333;
        }

        &-refund {
          width: 135px;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          margin-left: 20px;

          &-text {
            white-space: nowrap;
            color: #f76f63;
            font-weight: 400;
            font-size: 32px;
            line-height: 24px;
          }

          &-icon {
            font-size: 32px !important;
            color: #cacaca;
          }
        }

      }
    }

    &-info {
      background: #fff;
      padding-top: 25px;
      padding-bottom: 25px;
      font-size: 30px;
      line-height: 36px;
    }

    &-list {
      margin-top: 25px;

      .repay {
        background: #fff;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 32px 30px;
        border-bottom: 1px solid #e6ebf0;

        &-process {
          font-size: 30px;
        }

        &-finish {
          display: flex;
          align-items: center;
          font-size: 30px;
          color: #09bb07;

          &-img {
            width: 32px;
            height: 32px;
            margin-right: 3px;
          }
        }

        &-period {
          font-size: 30px;
        }
      }
    }
  }
}

.refund-modal {
  .mu-modal__header {
    padding-top: 40px;
  }
}