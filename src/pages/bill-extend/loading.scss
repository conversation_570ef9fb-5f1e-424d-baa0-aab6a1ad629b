.loading-page {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  background: #fff;
  height: 100vh;

  @keyframes img-loading {
    from {
      transform: translate(-50%, -50%) rotate(0deg);
    }

    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  &_content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 78px;
    width: 160px;
    height: 160px;

    .img {
      position: absolute;
      left: 50%;
      top: 50%;
      height: 160px;
      width: 160px;
      opacity: 1;
      animation: img-loading 1s linear none 0s infinite;
    }

    .count {
      opacity: 1;
      color: #333333;
      text-align: center;
      font-size: 80px;
      font-weight: 300;
      line-height: 80px;
    }
  }

  &_title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 62px;
    color: #333333;
    text-align: center;
    font-size: 40px;
    font-weight: 500;
    height: 40px;
    line-height: 40px;
  }

  &_desc {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    color: #808080;
    text-align: center;
    font-size: 28px;
    height: 42px;
    line-height: 42px;
  }

  &_slogan {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    bottom: 0;
  }
}