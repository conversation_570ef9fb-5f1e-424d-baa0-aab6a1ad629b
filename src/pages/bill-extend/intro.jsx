/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import {
  MUView, MUImage, MUButton
} from '@mu/zui';
import {
  track, EventTypes
} from '@mu/madp-track';
import Madp from '@mu/madp';
import classNames from 'classnames';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import { Url } from '@mu/madp-utils';
import { sloganUrl } from '@utils/constants';
import { getStore } from '@api/store';
import './intro.scss';

const IMG_TOP = 'https://file.mucfc.com/zlh/3/0/202401/202401171211570f1101.png';
const IMG_MIDDLE = 'https://file.mucfc.com/zlh/3/0/202401/20240117121156499794.png';
const IMG_BOTTOM = 'https://file.mucfc.com/zlh/3/0/202401/202401171211568dbcf7.png';

@track({ event: EventTypes.PO }, {
  pageId: 'BillExtendIntro',
  dispatchOnMount: true,
})
@pageHoc({ title: '了解延后还' })
export default class BillExtendIntro extends Component {
  config = {
    navigationBarTitleText: '了解延后还',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  backHandle = () => {
    const param = Url.getParam('external') || '';
    if (param) {
      Util.router.replace('/pages/bill-extend/list?repaymentFlag=Y');
    } else {
      Madp.navigateBack();
    }
  }

  render() {
    const isVplus = getStore('isVplus') || false; // 是否为v+会员
    return (
      <MUView className="intro-bg">
        <MUView className="intro-bg-blue">
          <MUView className="intro-bg-content">
            <MUView className="intro-bg-content-img">
              <MUImage className={classNames('slogan', { 'slogan-plus': isVplus })} src={isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
              <MUImage className="img_top" src={IMG_TOP} />
              <MUImage className="img_middle" src={IMG_MIDDLE} />
              <MUImage className="img_bottom" src={IMG_BOTTOM} />
            </MUView>
            <MUView className="intro-bg-content-blank" />
            <MUView className="intro-bg-content-btn">
              <MUButton type="primary" className="btn" beaconId="IntroBtnClick" onClick={() => { this.backHandle(); }}>立即办理</MUButton>
            </MUView>
          </MUView>
        </MUView>
      </MUView>
    );
  }
}