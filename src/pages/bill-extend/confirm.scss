.extend-confirm {
    &__main {
        display: flex;
        flex-direction: column;
    }

    &__special-card {
        padding: 20px;
        margin-bottom: 10px;
        position: relative;
    }

    &__special-card-slogan {
        width: 206px;
        height: 44px;
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 99;
  
        &-plus {
          width: 249px;
        }
    }

    &__special-card-img {
        position: absolute;
        width: 710px;
        height: 490px;
    }

    &__special-card-content {
        position: relative;
        margin-left: 40px;

        &-title {
            color: #333333;
            font-size: 36px;
            font-weight: 600;
            margin-top: 36px;
        }

        &-subtitle {
            color: #808080;
            font-size: 24px;
            margin-top: 40px;
        }

        &-tips {
            &-img {
                width: 59px;
                height: 32px;
            }

            &-text {
                position: relative;
                top: -7px;
                margin-left: 10px;
                color: #333333;
                font-size: 26px;

                &-highlight {
                    margin: 0 3px;
                    font-size: 26px;
                    font-weight: 400;
                    color: #ff8844;
                }
            }

            &-line-3 {
                font-size: 20px;
                font-weight: 600;
            }

            &-line-2 {
                font-size: 28px;
                font-family: "DIN Alternate";
            }
        }

        &-subcards {
            margin-left: 55px;
            margin-top: 10px;
            white-space: nowrap;

            &-1 {
                position: relative;
                width: 218px;
                height: 140px;
                display: inline-block;
            }

            &-2 {
                position: relative;
                width: 302px;
                height: 203px;
                display: inline-block;
            }

            &-1-img {
                position: absolute;
                width: 218px;
                height: 140px;
            }

            &-2-img {
                position: absolute;
                width: 302px;
                height: 203px;
            }

            &-1-text {
                color: #8899bc;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);

                &-1 {
                    font-size: 20px;
                    font-weight: 600;
                }
                &-2 {
                    font-size: 28px;
                    font-family: "DIN Alternate";
                }
            }

            &-2-text {
                color: #ffffff;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
 
                &-1 {
                    font-size: 32px;
                    font-weight: 600;
                }
                &-2 {
                    font-size: 22px;
                    font-weight: 600;
                }    
                &-3 {
                    font-size: 40px;
                    font-family: "DIN Alternate";
                }
            }

            &-arrow {
                z-index: 100;
                width: 80px;
                height: 80px;
                position: absolute;
                right: -30px;
                top: 5px;
            }
        }
    }
    
    &__normal-card {
        padding: 20px;
        margin-bottom: 60px;
        position: relative;
    }

    &__normal-card-slogan {
        width: 206px;
        height: 44px;
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 99;
  
        &-plus {
          width: 249px;
        }
    }

    &__normal-card-img {
        position: absolute;
        width: 710px;
        height: 240px;
    }

    &__normal-card-content {
        position: relative;
        margin-top: 60px;
        margin-left: 40px;

        &-text {
            display: inline-block;

            &-title {
                color: #333333;
                font-size: 36px;
                font-weight: 600;
            }

            &-subtitle {
                margin-top: 15px;
                color: #808080;
                font-size: 26px;
            }
        }
    }

    &__banner {
        margin: 0 20px;
        padding: 0 30px;
        background-color: white;
        height: 100px;
        border-radius: 12px;

        &-text {
            width: 50%;
            display: inline-block;
            color: #333333;
            text-align: left;
            font-size: 32px;
            font-weight: 600;
        }

        &-amount {
            width: 50%;
            display: inline-block;
            color: #333333;
            text-align: right;
            font-size: 32px;
        }
    }

    &__plan {
        // flex: 1;
        overflow-y: hidden;

        /* ExtendListPlan为公共组件（list-new.jsx也在用）
         * 此处针仅对confirm页面进行适配（效果：仅窗口可滑动）
         */
        .extend-list__plan {
            height: calc(100% - 80px);
            &__wrapper {
                height: 100%;
                display: flex;
                flex-direction: column;
            }
            .plan {
                &__details {
                    // flex: 1;
                    height: calc(100% - 40px);
                    &__scroll {
                      height: 100%;
                    }
                }
            }
            .mu-dialog__content {
                padding: 0;
            }
        }
    }

    &__footer {
        position: fixed;
        bottom: 0;
        background-color: #ffffff;
        width: 100%;
        
        &-repay {
            height: 120px;
        }
        
        &-confirm {
            height: 160px;
        }

        &-text {
            width: 50%;
            display: inline-block;

            &-1 {
                margin: 20px 0 0 30px;
                font-size: 28px;
                font-weight: 400;
                color: #333333;
            }
            &-2 {
                margin: 0 0 0 30px;
                font-size: 22px;
                font-weight: 400;
                background: #ffffff;
            }
            &-highlight {
                margin: 0 3px;
                font-size: 28px;
                font-weight: 600;
                color: #ff8844;
            }
        }

        &-repay-btnbox {
            width: 50%;
            display: inline-block;
            .at-button {
                position: absolute;
                bottom: 0;
                right: 0;
                height: 120px;
                width: 220px;
                font-weight: 600;
            }
        }

        &-confirm-btnbox {
            width: 50%;
            display: inline-block;
            padding: 30px 0;
            .at-button {
                height: 100px;
                width: 335px;
                font-weight: 600;
            }
        }
    }
}