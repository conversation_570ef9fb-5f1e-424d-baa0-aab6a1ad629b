/* eslint-disable object-curly-newline */
/* eslint-disable arrow-body-style */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable react/sort-comp */
/* eslint-disable prefer-destructuring */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUButton, MUForm, MUInput
} from '@mu/zui';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import ChannelConfig from '@config/index';
import Dispatch from '@api/actions';
import { track, EventTypes } from '@mu/madp-track';

import './identity.scss';

const themeColor = Util.getThemeColor(ChannelConfig.theme);

@track({ event: EventTypes.PO }, {
  pageId: 'TransferIdentity',
  dispatchOnMount: true,
})
@pageHoc({ title: '转账还款申诉' })
export default class TransferIdentity extends Component {
  constructor() {
    super();
    this.state = {
      name: '',
      id: '',
      mobile: '',
    };
  }

  async nextOnClick() {
    const { name, id, mobile } = this.state;
    const ret = await Dispatch.repayment.checkIdentity({
      accountType: '01',
      loanPersonIdNo: id,
      loanPersonMobile: mobile,
      loanPersonName: name,
      type: 'transfer',
    });
    const { result, serialNo } = ret;
    if (result === 'Y') {
      const urlParam = serialNo ? `?serialNo=${serialNo}` : '';
      Util.router.push(`/pages/transfer/cert-upload${urlParam}`);
    } else {
      Madp.showModal({
        content: '借款人信息输入不正确',
        confirmText: '去修改',
        confirmColor: themeColor,
        cancelText: '取消',
      });
    }
    return null;
  }

  get disabledBtn() {
    const { name, id, mobile } = this.state;
    return !name || !id || !mobile;
  }

  render() {
    const { name, id, mobile } = this.state;
    return (
      <MUView className="pages-bg transfer-identity">
        <MUForm className="form" title="请输入借款人身份信息">
          <MUInput
            name="nameInput"
            title="姓名"
            type="text"
            beaconId="NameInput"
            placeholder="请输入借款人的真实姓名"
            value={name}
            onChange={(val) => this.setState({ name: val })}
          />
          <MUInput
            name="idInput"
            title="身份证号"
            type="idcard"
            beaconId="IDInput"
            placeholder="请输入借款人的身份证号"
            value={id}
            onChange={(val) => this.setState({ id: val })}
          />
          <MUInput
            name="mobileInput"
            title="手机号码"
            type="phone"
            beaconId="MobileInput"
            placeholder="请输入借款人的注册号码"
            value={mobile}
            onChange={(val) => this.setState({ mobile: val })}
          />
        </MUForm>
        <MUButton
          className="next-btn"
          type="primary"
          beaconId="Submit"
          onClick={() => this.nextOnClick()}
          disabled={this.disabledBtn}
        >
          下一步
        </MUButton>
      </MUView>
    );
  }
}
