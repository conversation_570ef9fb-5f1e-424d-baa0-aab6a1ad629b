/* eslint-disable max-len */
/* eslint-disable prefer-rest-params */
/* eslint-disable taro/manipulate-jsx-as-array */
/* eslint-disable react/sort-comp */
/* eslint-disable no-empty */
/* eslint-disable operator-linebreak */
/* eslint-disable no-trailing-spaces */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/jsx-one-expression-per-line */
import { Component } from '@tarojs/taro';
import {
  MUView, MUButton, MUListItem, MUText, MUList, MUImage, MUModal, MUNavBarWeapp
} from '@mu/zui';
import Madp from '@mu/madp';
import { track, dispatchTrackEvent, EventTypes, disableTrackAlert } from '@mu/madp-track';
import { getLoginInfo } from '@mu/business-basic';
import { Url } from '@mu/madp-utils';
// import appInfos from '@utils/app-config-infos';
import Util from '@utils/maxin-util';
import ChannelConfig from '@config/index';
import Dispatch from '@api/actions';
import { getStore } from '@api/store';
import pageHoc from '@utils/pageHoc';
import './guide.scss';
import RedEnvelope from './imgs/RedEnvelope.png';
import InfoIcon from './imgs/InfoIcon.png';

const themeColor = Util.getThemeColor(ChannelConfig.theme);

disableTrackAlert();

@track({ event: EventTypes.PO }, {
  pageId: 'TransferGuide',
  dispatchOnMount: true,
})
@pageHoc({ title: '银行转账还款' })
export default class TransferGuide extends Component {
  config = {
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor() {
    super(...arguments);
    this.billType = Url.getParam('billType') || 'total';
    this.reserveType = Url.getParam('reserveType');
    this.isDueTagCust = getStore('nearBills').isDueTagCust === 'Y';
    this.amount = Url.getParam('amount') || '0.00';// 转账金额，由还款支付页面提供；成功预约后，由接口返回
    this.unAccountAmt = '';
    this.repayConfig = {};
    this.selectedBillList = getStore('selectedBillList');
    this.isMUAPP = Madp.getChannel() === '0APP';
    this.isAli = process.env.TARO_ENV === 'alipay';
    this.transferRules = {
      title: '转账还款规则',
      rules: [
        { desc: '1、预约成功后，当天收到您的足额转账后，1小时内会按预约账单进行还款入账；' },
        { desc: '2、预约成功后，当天24点前累计收到您的转账金额小于预约金额，将会按我司默认还款顺序进行入账：' },
        { desc: '1）若有逾期或已到期账单，则优先偿还逾期和已到期账单；', subDesc: true },
        { desc: '2）若无逾期及已到期账单，则优先偿还7天内到期的账单；', subDesc: true },
        { desc: '3）若无逾期、已到期或7天内到期的账单，方可偿还未到期账单；', subDesc: true },
        { desc: '3、因各银行的转账到账时间不一，为保证您的资金能在当天还款入账成功，请在23点前足额转账。' },
        { desc: '4、转账成功后请密切关注还款结果通知，若超过1小时还未收到还款成功通知，可咨询在线客服。' },
      ],
    };
    this.mobile = ''; // 掩码手机号
    this.state = {
      orderNos: [],
      accountName: '',
      accountNo: '',
      bankName: '',
      unreceived: true,
      unbooked: true,
      showTransferInfo: false,
      showCancelInfo: false,
      isAfter23: (new Date()).getHours() >= 23, // 产品要求，23点之后，预约还款提示时间需要从23时改为24时
      showNewCompanyName: false
    };
  }

  async componentDidMount() {
    // const repayType = this.billType === '7days' ? '3' : '2';
    this.queryReserveState();
    const {
      bankCustName: accountName,
      bankCardNo: accountNo,
      bankName
    } = await Dispatch.repayment.sendAccountDetail({
      sendNotifyFlag: 'N'
    }) || {};
    // await Dispatch.oldApis.getMorePayWay({ repayType });
    await this.initPageConfig();
    this.setState({
      accountName,
      accountNo,
      bankName,
    });
    const { mobile = '' } = await getLoginInfo() || {};
    this.mobile = mobile;
  }

  async queryReserveState() {
    const { basicCustDto } = getStore('sessionInfo') || {};
    let { custId } = basicCustDto || {};
    if (!custId) {
      const { basicCustDto: newBasicCustDto } = await Dispatch.repayment.getNewUserInfo();
      const { custId: newCustId } = newBasicCustDto || {};
      custId = newCustId || '';
    }
    // eslint-disable-next-line max-len
    const { unAccountAmt, reservationAmt, reservationOrders } = await Dispatch.repayment.queryReserveState({ custNo: custId }) || {};
    if (reservationOrders && reservationOrders.length > 0) {
      this.setState({
        orderNos: reservationOrders
      });
    }
    if (reservationAmt === null) { // 未预约
      // eslint-disable-next-line no-useless-return
      return;
    } else if (unAccountAmt === null) { // 已预约，未收到转账
      this.amount = reservationAmt;
      this.setState({
        unbooked: false,
      });
    } else {
      this.amount = reservationAmt;
      this.unAccountAmt = unAccountAmt;
      this.setState({
        unbooked: false,
        unreceived: false,
      });
    }
  }

  async initPageConfig() {
    this.repayConfig = await Dispatch.repayment.getCommonConfig('repayTransfer.config') || {};
    // 29799需求，通过参数修改司名
    const { showNewCompanyName = 'N' } = this.repayConfig || {};
    this.setState({ showNewCompanyName: showNewCompanyName === 'Y' });
    if (this.repayConfig.transferRules) {
      this.transferRules = this.repayConfig.transferRules;
    }
  }

  getAwardAmout(amt) {
    const amount = Number(amt) - 50000;
    if (amount <= 0) {
      return 0;
    }
    const list = [10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30];// 返现的金额挡位
    const index = Math.floor(amount / 10000);
    return list[index] || 30; // 最高30元
  }

  copyAccountNo() {
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: 'CopyAccountNo', target: this });
    const { accountNo } = this.state;
    Madp.setClipboardData({ data: accountNo });
    Util.toast('账号复制成功');
  }

  copyAccountName() {
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: 'CopyAccountName', target: this });
    const { accountName } = this.state;
    Madp.setClipboardData({ data: accountName });
    Util.toast('户名复制成功');
  }

  // 提交预约
  async submitReservation() {
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'SubmitReservation' });
    const { amount, reserveType, selectedBillList } = this;
    // const { selectedBillList } = this.state;
    const reservationOrders = [];
    if (this.reserveType === '02') {
      // 7天待还塞借据号基础上加"-期数"
      selectedBillList.forEach((bill) => { reservationOrders.push(`${bill.orderNo}_${bill.installCnt}`); });
    } else {
      // 全部待还直接塞借据号
      selectedBillList.forEach(({ orderNo }) => { reservationOrders.push(orderNo); });
    }
    const data = {
      reservationAmt: amount,
      reservationType: reserveType,
      reservationOrders,
    };
    const { result, desc } = await Dispatch.repayment.submitReservation(data) || {};
    if (result === 'Y') {
      this.toReserveSuccess();
    } else {
      Madp.showToast({
        title: desc || '提交预约失败，请稍后再试',
        icon: 'none',
        duration: 2000
      });
    }
  }

  openCancelInfo() {
    dispatchTrackEvent({
      target: this, event: EventTypes.BC, beaconId: 'CancelReservation', beaconContent: { cus: { result: '0' } }
    });
    this.setState({
      showCancelInfo: true
    });
  }

  cancelConfirm() {
    dispatchTrackEvent({
      target: this, event: EventTypes.BC, beaconId: 'CancelReservation', beaconContent: { cus: { result: '1' } }
    });
    this.cancelReservation();
    this.setState({
      showCancelInfo: false
    });
  }

  confirmNewCompanyName = () => {
    dispatchTrackEvent({
      target: this, event: EventTypes.BC, beaconId: 'confirmNewCompanyName'
    });
    this.setState({
      showNewCompanyName: false
    });
  }

  closeCancelInfo() {
    dispatchTrackEvent({
      target: this, event: EventTypes.BC, beaconId: 'CancelReservation', beaconContent: { cus: { result: '2' } }
    });
    this.setState({
      showCancelInfo: false
    });
  }

  // 取消预约
  async cancelReservation() {
    // const { selectedBillList } = this;
    // 去重
    // let orderNos = {};
    // selectedBillList.forEach((bill) => { orderNos[bill.orderNo] = 0; });
    // orderNos = Object.keys(orderNos);
    const { orderNos } = this.state;
    const data = { orderNos };
    const { result, desc } = await Dispatch.repayment.cancelReservation(data) || {};
    if (result === 'Y') {
      setTimeout(() => {
        Madp.showToast({
          title: '取消预约成功',
          icon: 'none',
          duration: 2000
        });
      }, 0);
      setTimeout(() => {
        Madp.navigateBack({ delta: 1 });
      }, 2000);
    } else {
      setTimeout(() => {
        Madp.showToast({
          title: desc || '取消预约失败，请稍后再试',
          icon: 'none',
          duration: 2000
        });
      }, 0);
    }
  }

  // 发送账户信息
  async sendAccountMessage() {
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'SendAccountMessage' });
    await Dispatch.repayment.sendAccountDetail({
      sendNotifyFlag: 'Y'
    });
  }

  /**
   * 跳转到预约成功页面，关闭当前页面
   */
  toReserveSuccess() {
    Util.router.replace({
      path: '/pages/common-result/result',
      query: {
        type: 'order-success'
      }
    });
  }

  openTransferInfo() {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'OpenTransferInfo' });
    const {
      amount, unAccountAmt
    } = this;
    const remainAmount = (Number(amount) - Number(unAccountAmt)).toFixed(2);
    let isProcessing = false;
    if (remainAmount <= 0) {
      isProcessing = true;
    }
    Madp.showModal({
      content: `${isProcessing ? '还款入账处理中，请稍后留意还款结果通知' : `已收到转账${unAccountAmt}元，再转账${remainAmount}元即可完成还款入账，若要对已转账金额立即入账，可取消预约`}`,
      confirmText: '我知道了',
      confirmColor: themeColor,
      showCancel: false
    });
  }

  closeTransferInfo() {
    // dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'CloseTransferInfo' });
    this.setState({
      showTransferInfo: false
    });
  }

  render() {
    const {
      accountName,
      accountNo,
      bankName,
      unbooked,
      unreceived,
      showTransferInfo,
      showCancelInfo,
      isAfter23,
      showNewCompanyName
    } = this.state;
    const {
      amount, unAccountAmt, isMUAPP, repayConfig, isAli
    } = this;
    const remainAmount = (Number(amount) - Number(unAccountAmt)).toFixed(2);
    let isProcessing = false;
    if (remainAmount <= 0) {
      isProcessing = true;
    }
    const discountAmount = this.getAwardAmout(amount);
    const showdiscount = repayConfig.transferChannels && repayConfig.transferChannels.indexOf(Madp.getChannel()) > -1;
    // const accountNoDisplay = accountNo && accountNo.replace(/(.{4})/g, '$1 ');
    const nodes = this.transferRules.rules.map((rule) => (
      <MUView className={rule.subDesc ? 'bank-transfer-rules-sub-title' : 'bank-transfer-rules-up-title'}>{rule.desc}</MUView>
    ));
    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="银行转账还款"
          leftArea={[
            {
              type: 'icon',
              value: 'back'
            }
          ]}
        />
      <MUView className="pages-bg">
        <MUView className="bank-title">
          <MUView>
            {unbooked && <MUText className="bank-title-first-line">提交预约成功后，</MUText>}
            <MUText>请于</MUText><MUText className="bank-title-main">今日{isAfter23 ? 24 : 23}:00前</MUText><MUText>转账到以下账户</MUText>
          </MUView>
          {discountAmount && showdiscount ? (
            <MUView className="bank-title-discount">
              <MUView className="bank-title-discount-redEnvelope">
                <MUImage src={RedEnvelope} className="bank-title-discount-redEnvelope-img" />
                <MUText className="bank-title-discount-redEnvelope-text">{isMUAPP ? 'APP专享' : '省钱攻略'}</MUText>
              </MUView>
              <MUView className="bank-title-discount-detail">
                {isMUAPP ? <MUText>还款入账成功</MUText> : <MUText>回招联APP预约</MUText>}
                <MUText>，预计可返</MUText>
                <MUText className="bank-title-discount-detail-discountAmount">{discountAmount}元</MUText>
                <MUText>红包</MUText>
              </MUView>
            </MUView>
          ) : ''}
        </MUView>
        <MUList hasBorder={false} className="bank-info">
          {(unbooked || unreceived) ? (
            <MUListItem hasBorder={false} title="转账金额" disabledArrow extraText={`${amount}元`} beaconId="transferAmount" />
          ) : (
            <MUView className={`bank-info-box ${isAli ? 'bank-info-box-right' : ''}`} beaconId="transferAmount">
              <MUView className="at-list__item item-content__info-title bank-info-box-accountNo_title">转账金额</MUView>
              <MUView className="bank-info-box-remarkBox">
                <MUView className="bank-info-box-remarkBox-mobile">{amount}元</MUView>
                <MUView className="bank-info-box-remarkBox-remark">
                  <MUText>已收到转账</MUText>
                  <MUText className="bank-info-box-remarkBox-remark-receiveAmount">{unAccountAmt}元</MUText>
                  <MUView className="bank-info-box-remarkBox-remark-con">
                    <MUImage src={InfoIcon} beaconId="TransferInfo" className="bank-info-box-remarkBox-remark-con-infoIcon" onClick={this.openTransferInfo.bind(this)} />
                  </MUView>
                </MUView>
              </MUView>
            </MUView>
          )}
          <MUView className={`bank-info-box ${isAli ? 'bank-info-box-right' : ''}`} beaconId="transferAccountName">
            <MUView className="at-list__item item-content__info-title">户名</MUView>
            <MUView className="bank-info-box-accountName">
              <MUText>{accountName}</MUText>
              <MUButton className="bank-info-box-accountName-copyBtn" customStyle={`border-color: ${themeColor};color: ${themeColor}`} beaconId="copyAccountName" onClick={this.copyAccountName.bind(this)}>复制</MUButton>
            </MUView>
          </MUView>
          <MUListItem hasBorder={false} title="开户行" disabledArrow extraText={bankName} beaconId="transferBankName" />

          <MUView className={`bank-info-box ${isAli ? 'bank-info-box-right' : ''}`} beaconId="transferAccountNo">
            <MUView className="at-list__item item-content__info-title bank-info-box-title">账号</MUView>
            <MUView className="bank-info-box-accountName">
              <MUView className="bank-info-box-accountName-accountNo">{accountNo}</MUView>
              <MUButton className="bank-info-box-accountName-copyBtn" customStyle={`border-color: ${themeColor};color: ${themeColor}`} beaconId="copyAccountNo" onClick={this.copyAccountNo.bind(this)}>复制</MUButton>
            </MUView>
          </MUView>

          <MUView className="bank-info-box" beaconId="transferRemark">
            <MUView className="at-list__item item-content__info-title bank-info-box-accountNo_title">转账备注</MUView>
            <MUView className="bank-info-box-remarkBox">
              <MUView className="bank-info-box-remarkBox-mobile">{this.mobile}</MUView>
              <MUView className="bank-info-box-remarkBox-remark">注册手机号，请务必正确填写</MUView>
            </MUView>
          </MUView>

          <MUView className="bank-info-notice">
            <MUView className="bank-info-notice-box">
              <MUView>可通过银行网点、网上银行、手机银行等渠道进行转账</MUView>
            </MUView>
          </MUView>
        </MUList>

        {unbooked ? (<MUButton type="primary" className="guide_commitBtn" beaconId="SubmitReserve" onClick={this.submitReservation.bind(this)} disabled={showNewCompanyName && accountName === '招联消费金融股份有限公司'}>提交预约</MUButton>
        ) : (<MUButton type="secondary" className="guide_commitBtn" beaconId="CancelReserve" onClick={this.openCancelInfo.bind(this)}>取消预约</MUButton>)}
        <MUView className="bank-transfer-rules">
          <MUView className="bank-transfer-rules-title">{this.transferRules.title}</MUView>
          {nodes}
        </MUView>

        <MUModal
          isOpened={showTransferInfo}
          beaconId="ShowDepreciateChance"
          content={`${isProcessing ? '还款入账处理中，请稍后留意还款结果通知' : `已收到转账${unAccountAmt}元，再转账${remainAmount}元即可完成还款入账，若要对已转账金额立即入账，可取消预约`}`}
          confirmText="我知道了"
          onConfirm={this.closeTransferInfo.bind(this)}
        />

        <MUModal
          isOpened={showCancelInfo}
          beaconId="ShowDepreciateChance"
          content="取消预约后，再使用银行卡转账还款时，将按我司默认还款顺序还款"
          confirmText="确认取消"
          cancelText="不取消了"
          onConfirm={this.cancelConfirm.bind(this)}
          onCancel={this.closeCancelInfo.bind(this)}
        />
        {showNewCompanyName && unbooked && accountName === '招联消费金融股份有限公司' &&
        <MUView 
          className="company-name"
        >
          <MUModal
            type="text"
            isOpened={showNewCompanyName && unbooked}
            beaconId="showUpdateCompanyNameTips"
            title="还款账户更名通知"
            content="还款账户名称已变更为：<span class='company-name_new'> 招联消费金融股份有限公司</span><br>原名称-招联消费金融有限公司 已废弃；<br>请使用新名称进行转账，以免还款失败。"
            confirmText="我知道了"
            onConfirm={this.confirmNewCompanyName.bind(this)}
            closeOnClickOverlay={false}
          />
        </MUView>}
      </MUView>
      </MUView>
    );
  }
}
