@import "../../components/weapp/index.scss";

.bank-title {
  width: 100%;
  font-size: 36px;
  background-color: #fff;
  // height: 220px;
  font-size: 40px;
  color: #333333;
  text-align: center;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 50px 0;

  .bank-title-first-line {
    display: block;
  }

  &-main {
    color: #ff8800;
    margin-top: 30px;
    font-size: 40px;
  }

  &-discount {
    // width: 538px;
    height: 44px;
    background: #fbfbfb;
    border: 2px solid #eeeeee;
    border-radius: 8px;
    font-size: 22px;
    color: #808080;
    font-weight: 400;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    &-redEnvelope {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      // width: 142px;
      height: 44px;
      background: #fff1e0;
      padding-left: 20px;
      padding-right: 12px;

      &-img {
        width: 24px;
        height: 30px;
      }

      &-text {
        font-size: 22px;
        color: #ff8800;
        line-height: 22px;
        font-weight: 400;
        padding-left: 10px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
      }
    }

    &-detail {
      // text-align: center;
      font-size: 22px;
      color: #808080;
      line-height: 22px;
      font-weight: 400;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding-left: 12px;
      padding-right: 20px;

      &-discountAmount {
        color: #ff8800;
      }
    }
  }

  &-btn {
    width: 280px !important;
    height: 60px !important;
    border: 2px solid #3477ff !important;
    border-radius: 30px !important;
    font-size: 26px !important;
    color: #3477ff !important;
    text-align: center !important;
    font-weight: 500 !important;
    margin-top: 40px !important;
  }
}

.bank-info {
  margin-top: 25px;
  background-color: #fff;

  .item-extra__info {
    color: #333 !important;
  }

  .item-content__info-title {
    color: #808080 !important;
  }

  &-box {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-right: 0.64rem;

    &-right {
      padding-right: 0.31rem !important;
    }

    .at-list__item:not(:last-child)::after {
      border-bottom: 0 solid #e5e5e5 !important;
    }

    &-title {
      padding-right: 0 !important;
    }

    &-accountName {
      // background: #000;
      // width: 450px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      font-size: 32px;
      font-weight: 400;
      vertical-align: middle;
      box-sizing: border-box;

      &-copyBtn {
        margin-left: 16px !important;
        width: 64px !important;
        height: 36px !important;
        border: 2px solid #3477ff;
        border-radius: 4px !important;
        font-size: 20px !important;
        color: #3477ff;
      }

      &-accountNo {
        // overflow: hidden;
        // text-overflow: ellipsis;
        white-space: nowrap;
        // width: 445px;
        // height: 100px;
        font-size: 32px;
        font-weight: 400;
        vertical-align: middle;
        // box-sizing: border-box;
        // padding-right: 0px;
      }
    }

    &-remarkBox {
      padding-top: 17px;

      &-mobile {
        font-size: 32px;
        color: #333333;
        text-align: right;
        // line-height: 32px;
        font-weight: 400;
      }

      &-remark {
        font-size: 22px;
        color: #a6a6a6;
        text-align: center;
        // line-height: 22px;
        font-weight: 400;

        &-receiveAmount {
          color: #ff8800;
        }

        &-con {
          display: inline-table;
          height: 50px;
          padding-left: 5px;

          &-infoIcon {
            vertical-align: middle;
            margin-bottom: 3px;
            width: 30px;
            height: 30px;
          }
        }
      }
    }
  }

  &-notice {
    font-size: 24px;
    color: #808080;
    padding: 30px;
    text-align: center;
    display: flex;
    justify-content: center;

    &-box {
      padding: 12px 30px;
      background: #f3f3f3;
      border-radius: 8px;
    }

    &-highlight {
      color: #ff8800;
    }

    .info-icon {
      margin: 5px 5px 0 0;
    }
  }
}

.guide_commitBtn {
  margin: 50px 30px 20px !important;
}

.bank-transfer-rules {
  /* stylelint-disable-next-line */
  font-family: "Helvetica Neue";
  padding: 30px 30px 130px;
  line-height: 45px;
  text-align: left;
  font-size: 25px;
  color: #a6a6a6;

  &-title {
    color: #808080;
    margin-bottom: 20px;
  }

  &-sub-title {
    // margin-top: 10px;
  }

  &-up-title {
    margin-top: 10px;
  }
}
.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}

.company-name {
  .content-text__container {
    text-align: left;
    font-size: 30px;
    font-weight: 500;
  }
  &_new {
    color: #ff8800;
  }
}
