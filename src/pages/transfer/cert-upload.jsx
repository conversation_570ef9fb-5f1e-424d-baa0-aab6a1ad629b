/* eslint-disable max-len */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-param-reassign */
/* eslint-disable arrow-body-style */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable react/sort-comp */
/* eslint-disable prefer-destructuring */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUButton, MUNoticebar
} from '@mu/zui';
import { Url } from '@mu/madp-utils';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import CustomConfig from '@config/index';
import Dispatch from '@api/actions';
import { track, EventTypes } from '@mu/madp-track';
import CertForm from '@components/cert-form';
import classNames from 'classnames';
import './cert-upload.scss';
import '@components/cert-form/index.scss';

const themeColor = Util.getThemeColor(CustomConfig.theme);
@track({ event: EventTypes.PO }, {
  pageId: 'TransferCert',
  dispatchOnMount: true,
})
@pageHoc({ title: '转账还款申诉' })
export default class TransferCert extends Component {
  constructor() {
    super();
    this.state = {
      sectionLen: 1,
      isAddBtnActivated: false,
      userCash: false, // 如果是现金汇款，不允许添加多笔
    };
    this.formData = [];
    this.serialNo = Url.getParam('serialNo');
    this.formDoneList = [false]; // 完成表：是否全部表格信息都填完整了，默认只有一个空表格，故默认没填完整
  }

  async checkResult() {
    const { result, desc, status, remitType } = await Dispatch.repayment.checkTransferResult({ serialNo: this.serialNo });
    if (result === 'Y') {
      if (status === '4') Util.router.push('/pages/common-result/result?type=transfer-done');
      else if (status === '5') Util.router.push('/pages/common-result/result?type=transfer-fail');
      else if (status === '2') Util.router.push('/pages/common-result/result?type=transfer-unknown');
      else if (status === '1' || status === '3') Util.router.push(`/pages/common-result/result?type=transfer${remitType === '2' ? '-cash' : ''}-pending`);
      else Util.router.push('/pages/common-result/result?type=transfer-unknown');
    } else {
      desc && Madp.showToast({
        title: desc,
        icon: 'none',
        duration: 2000
      });
    }
  }

  updateForm(ret) {
    const { userCash } = this.state;
    const index = ret.type;
    // 如果是现金汇款，不允许添加多笔，不需要付款方名称、付款方银行
    if (ret.payWaysIndex) {
      this.formData.length = 1;
      this.formDoneList.length = 1;
      this.formData[0] = { ...ret };
      this.setState({
        sectionLen: 1,
        userCash: true
      });
      if (!userCash) {
        this.formDoneList = [false];
      }
      delete ret.type;
      delete ret.payWaysIndex;
      delete ret.account;
      delete ret.bankOrg;
    } else { // 如果是 银行卡转账 不需要 凭证号
      this.formData[index] = { ...ret };
      delete ret.type;
      delete ret.payWaysIndex;
      delete ret.voucherNo;
      this.setState({
        userCash: false
      });
    }
    let done = true;
    // 遍历ret 看看表单里面哪个字段还是空，就将done= false
    Object.keys(ret).forEach((key) => {
      if (!ret[key]) done = false;
    });
    // 更新完成表
    if (this.formDoneList[index] !== done) {
      this.formDoneList[index] = done;
    }
  }

  onAddSection() {
    const { sectionLen } = this.state;
    this.setState({
      sectionLen: sectionLen + 1,
    });
    this.formDoneList = this.formDoneList.concat(false);
  }

  onTouchStart() {
    this.setState({ isAddBtnActivated: true });
  }

  onTouchEnd() {
    this.setState({ isAddBtnActivated: false });
  }

  async submitForm() {
    const { userCash } = this.state;
    const remitIdentityDetailList = [];
    const remitImageUrlList = [];
    this.formData.forEach((form) => {
      remitImageUrlList.push(form.certData);
      if (userCash) {
        remitIdentityDetailList.push({
          remitType: '2',
          remitVoucherNo: form.voucherNo,
          remitDate: form.date,
          remitAmt: form.amount,
        });
      } else {
        remitIdentityDetailList.push({
          remitType: '1',
          remitName: form.account,
          remitDate: form.date,
          remitAmt: form.amount,
          remitBank: form.bankOrg,
        });
      }
    });
    const { result, desc } = await Dispatch.repayment.submitTransferCert({ remitIdentityDetailList, remitImageUrlList, serialNo: this.serialNo });
    if (result === 'Y') Util.router.push(`/pages/common-result/result?type=transfer${userCash ? '-cash' : ''}-pending`);
    else {
      desc && Madp.showToast({
        title: desc,
        icon: 'none',
        duration: 2000
      });
    }
  }

  get disabledBtn() {
    let doneList = this.formDoneList;
    return doneList.filter((done) => !done).length;
  }

  render() {
    const { sectionLen, isAddBtnActivated, userCash } = this.state;
    return (
      <MUView className="pages-bg transfer-cert">
        <MUNoticebar
          icon="notice"
          showMore
          onTextClick={() => this.checkResult()}
          beaconId="CheckResult"
          className={themeColor === '#E60027' ? 'notice-bar-theme' : ''}
        >
          若您已经提交申诉，请点击这里查询申诉结果。
        </MUNoticebar>
        {
          userCash ? null : <MUView
            className={classNames('add-form-section brand-text', { activated: isAddBtnActivated })}
            beaconId="AddSection"
            onClick={() => this.onAddSection()}
            onTouchStart={() => this.onTouchStart()}
            onTouchEnd={() => this.onTouchEnd()}
          >
            添加
          </MUView>
        }

        {
          [...Array(sectionLen)].map((item, index) => (
            <CertForm title={`第${index + 1}笔转账`} type={index} onFormChanged={(ret) => this.updateForm(ret)} />
          ))
        }
        <MUButton className="submit-btn" type="primary" beaconId="Submit" disabled={this.disabledBtn} onClick={() => this.submitForm()}>
          申诉
        </MUButton>
      </MUView>
    );
  }
}
