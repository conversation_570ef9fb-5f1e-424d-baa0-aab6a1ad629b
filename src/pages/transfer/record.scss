.transfer-record {
  color: #333;
  font-size: 24px;
  overflow: auto;
  height: 100%;
  background-color: #fff;
  &-topimg {
    display: block;
    width: 120px;
    height: 120px;
    margin: 60px auto;
  }
  &-title {
    font-size: 40px;
    margin-top: 50px;
    text-align: center;
    font-weight: 600;
    line-height: 1;
  }
  &-balance {
    margin: 40px 0;
    font-size: 80px;
    font-weight: 600;
    text-align: center;
    line-height: 1;
    &::after {
      content: '元';
      font-size: 40px;
      font-weight: 400;
      margin-left: 10px;
    }
  }
  &-yuyue {
    &-item {
      display: flex;
      margin: 0 30px 40px;
      justify-content: space-between;
      font-size: 32px;
      line-height: 1;
      .orange {
        color: #FF8844;
      }
    }
  }
  &-list {
    margin: 0 30px;
    /* stylelint-disable-next-line */
    border-top: 1PX solid #F3F3F3;
    padding: 40px 0;
    font-size: 28px;
    &-item {
      &-detail {
        display: flex;
        flex-direction: row;
        align-items: center;
        line-height: 1;
        &::before {
          content: "";
          width: 10px;
          height: 10px;
          background: #FFFFFF;
          border: 4px solid #3477FF;
          border-radius: 50%;
          margin-left: 6px;
          margin-right: 16px;
        }
      }
      &-card {
        /* stylelint-disable-next-line */
        border-left: 1PX solid #CACACA;
        padding: 12px 26px 40px;
        margin-left: 13px;
        color: #A6A6A6;
        &.last {
          border-left-color: transparent;
          padding-bottom: 0;
        }
      }
    }
  }
  &-bottom {
    background-color: #F3F3F3;
    padding: 40px 30px;
    &-title {
      color: #808080;
      line-height: 1;
      margin-bottom: 30px;
    }
    &-line {
      color: #A6A6A6;
      line-height: 1;
      margin-bottom: 20px;
      &-link {
        color: #3477FF;
      }
    }
  }
  &-modal-content-line {
    color: #888888;
    font-size: 32px;
    text-align: left;
    margin-top: 20px;
  }
}