import { Component } from '@tarojs/taro';
import {
  MUView, MUText, MUImage, MUModal,
} from '@mu/zui';
import dayjs from 'dayjs';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import Dispatch from '@api/actions';
import pageHoc from '@utils/pageHoc';
import waitImg from './imgs/wait.png';

import './record.scss';


@track({ event: EventTypes.PO }, {
  pageId: 'TransferRecord',
  dispatchOnMount: true,
})
@pageHoc({ title: '转账还款详情' })
export default class TransferRecord extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShowTipModal: false,
      unAccountAmt: '0.00', // 已识别未入账金额
      remitDetailList: [], // 转账详情列表
      reservationFlag: false, // 是否预约
      reservationAmt: '0.00', // 预约还款金额
      surplusAmt: '0.00', // 剩余金额
    };
  }

  componentDidMount() {
    this.fetchData();
  }

  async fetchData() {
    const [data, { overpayBalance, preRepayAmt, overpayControlDetailList }] = await Promise.all([
      Dispatch.repayment.queryRemitDetail(),
      Dispatch.repayment.queryOverpayAccountInfo()
    ]);
    const overpayControlFlag = (overpayControlDetailList || []).filter((item = {}) => item.controlCode === 'C601').length ? 'Y' : 'N';
    this.setState({
      unAccountAmt: data.unAccountAmt,
      remitDetailList: data.remitDetailList,
      reservationFlag: data.reservationFlag === 'Y',
      reservationAmt: data.reservationRepayApply ? data.reservationRepayApply.reservationAmt : '',
      surplusAmt: data.reservationRepayApply
        ? Math.max(+data.reservationRepayApply.reservationAmt - +data.unAccountAmt - +(overpayControlFlag === 'Y' ? '0.00' : overpayBalance) - +(overpayControlFlag === 'Y' ? '0.00' : preRepayAmt), 0).toFixed(2)
        : 0
    });

    dispatchTrackEvent({
      target: this,
      beaconId: 'viewRransferRecord',
      event: EventTypes.EV,
      beaconContent: { cus: { reservationFlag: data.reservationFlag } }
    });
  }

  render() {
    const {
      isShowTipModal, unAccountAmt, reservationAmt, remitDetailList, reservationFlag, surplusAmt
    } = this.state;
    return (
      <MUView className="transfer-record">
        <MUImage src={waitImg} className="transfer-record-topimg" />
        <MUView className="transfer-record-title">转账处理中</MUView>
        <MUView className="transfer-record-balance">{unAccountAmt}</MUView>
        {
          reservationFlag && (
            <MUView className="transfer-record-yuyue">
              <MUView className="transfer-record-yuyue-item">
                <MUView>预约还款金额</MUView>
                <MUView>
                  ￥
                  {reservationAmt}
                  元
                </MUView>
              </MUView>
              <MUView className="transfer-record-yuyue-item">
                <MUView>还需转账金额</MUView>
                <MUView className="orange">
                  ￥
                  {surplusAmt}
                  元
                </MUView>
              </MUView>
            </MUView>
          )
        }
        <MUView className="transfer-record-list">
          {
            remitDetailList.map((item, i) => (
              <MUView className="transfer-record-list-item">
                <MUView className="transfer-record-list-item-detail">
                  {dayjs(item.remitTime).format('MM-DD HH:mm')}
                  {' '}
                  收到转账
                  {' '}
                  {(+item.remitAmt).toFixed(2)}
                  元
                </MUView>
                <MUView className={`transfer-record-list-item-card ${i === remitDetailList.length - 1 ? 'last' : ''}`}>
                  付款卡：
                  {item.remitBank}
                  (
                  {item.remitBankNo}
                  )
                </MUView>
              </MUView>
            ))
          }
        </MUView>
        <MUView className="transfer-record-bottom">
          <MUView className="transfer-record-bottom-title">
            还款入账说明
          </MUView>
          {
            reservationFlag && (
              <MUView className="transfer-record-bottom-line">
                1.预约金额
                {reservationAmt}
                元全部转账后可自动还款入账
              </MUView>
            )
          }
          <MUView className="transfer-record-bottom-line">
            {
              reservationFlag ? '2.最晚今天24:00自动还款入账' : `1.预计${remitDetailList[remitDetailList.length - 1] ? dayjs(remitDetailList[remitDetailList.length - 1].remitTime).add(1, 'hour').format('MM-DD HH:mm') : ''}后自动还款入账`
            }
          </MUView>
          <MUView className="transfer-record-bottom-line">
            {reservationFlag ? '3' : '2'}
            .入账顺序点击
            <MUText
              className="transfer-record-bottom-line-link"
              beaconId="showTipModal"
              onClick={() => { this.setState({ isShowTipModal: true }); }}
            >
              这里
            </MUText>
            查询
          </MUView>
        </MUView>
        <MUModal
          isOpened={isShowTipModal}
          beaconId="tipModal"
          title="转账还款入账顺序"
          content={(
            <MUView className="transfer-record-modal-content">
              <MUView className="transfer-record-modal-content-line">1.若有逾期或已到期账单，则优先偿还逾期和已到期账单</MUView>
              <MUView className="transfer-record-modal-content-line">2.若无逾期借款及已到期账单，则优先偿还7天内到期的账单</MUView>
              <MUView className="transfer-record-modal-content-line">3.若无逾期、已到期或7天内到期的账单，方可偿还未到期账单</MUView>
            </MUView>
          )}
          confirmText="我知道了"
          closeOnClickOverlay={false}
          onConfirm={() => { this.setState({ isShowTipModal: false }); }}
        />
      </MUView>
    );
  }
}
