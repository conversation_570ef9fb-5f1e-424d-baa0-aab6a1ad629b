@import '../../components/weapp/index.scss';

.modify-payday {
  .tips {
    margin: 0 30px 20px 30px;
    font-size: 26px;
    color: #808080;
    line-height: 42px;
    text-align: justify;
  }

  .bill-text {
    font-size: 26px;
    margin-left: 30px;
  }

  .payday-picker {
    .picker-title {
      color: #333;
    }
  }

  .payday-select {
    .at-list__item-mid.item-mid {
      margin-left: 25px;
    }
  }

  .bill-list-desc {
    padding: 40px 32px 20px;
    color: #808080;
    font-size: 26px;
  }

  .contract-checker {
    display: flex;
    align-items: flex-end;

    .content {
      margin-left: 10px;
    }
  }

  .submit-btn {
    margin: 54px 32px 24px;
    // position: absolute;
    bottom: 10px;
    width: calc(100% - 64px);
  }

  &-currentPayDay {
    font-size: 32px;
    margin-top: 43px;
    background-color: #fff;

    &-first {
      padding: 20px 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .at-list__item .item-extra__info {
      color: #333;
    }
  }

  &-tipwrap {
    width: 100%;
    font-size: 32px;
    padding-top: 20%;

    &-text {
      line-height: 40px;
      padding: 0 40px;
    }

    &-content {
      margin-bottom: 30px;
      padding: 60px 40px 0;
      font-size: 28px;
      color: #808080;
    }

    .tipsModal{
      .mu-modal__footer .at-modal__action .at-button {
        width: 560px;
      }
    }
  }

  &-disable {
    width: 100%;
    // height: 100vh;
    font-size: 32px;
    display: flex;
    justify-content: center;
    padding-top: 20%;

    &-text {
      line-height: 1;
    }

    &-modal {
      .modal-title {
        font-size: 36px;
        font-weight: bold;
        text-align: center;
        margin: 20px;
      }

      &-content {
        margin-bottom: 30px;
        padding: 0 40px;
        font-size: 28px;
        color: #808080;
      }

      .modal-btn {
        padding: 0 40px;
        margin-bottom: 20px;
      }
    }
  }

  &-list {
    margin-bottom: 140px;
  }

  &-contract {
    margin-left: 30px;
    margin-top: 52px;
    font-size: 26px;
    color: #808080;
    line-height: 42px;
    display: flex;
    word-wrap: break-word;

    .repay-footer-checker {
      margin-right: 16px;
    }
  }

  &-title {
    margin-top: 30px;
    font-size: 36px;
    color: #333333;
    text-align: center;
    line-height: 36px;
    // font-weight: bold;
  }

  &-closeIcon {
    width: 32px;
    height: 32px;
    /* display: inline; */
    position: absolute;
    top: 30px;
    right: 30px;
  }

  &-table {

    width: 100%;

    .one-line {
      // width: 100%;
      margin: 16px 4.5%;
      display: flex;
      justify-content: space-around;

      .one-item {
        width: 64px;
        height: 64px;
        font-size: 28px;
        color: #333333;
        text-align: center;
        line-height: 64px;

        .day-num {
          width: 64px;
          height: 64px;
          line-height: 64px;
        }

        .defaultPayday {
          background: #F76F63;
          border-radius: 50%;
          color: #fff;
        }

        .check-day {
          border-radius: 50%;
          background: #3477FF;
          color: #fff;
        }
      }

    }

  }

  .tip-txt {
    color: #FF0000;
  }

  .modal-tip-txt {
    color: #000;
  }

  .modal-tip-btn {
    // display: flex;
    width: 100%;
    overflow: hidden;

    .at-button {
      // flex: auto;
      width: 50%;
      display: inline-flex;
      justify-content: center;
      box-sizing: border-box;
      border-radius: 0;
    }

    .next-btn-txt {
      color: #3477FF;
    }
  }

  .confirmRepayDayDialog {
    font-family: "PingFang SC";

    &-title {
      font-family: PingFangSC-Medium;
      color: #333333;
      text-align: center;
      line-height: 54px;
      font-size: 36px;
      font-weight: 500;

    }

    &-content {
      margin-top: 20px;
      margin-bottom: 40px;
      font-size: 28px;
      color: #808080;
      font-weight: 400;
      line-height: 42px;
      text-align: left;

      &-sub {
        color: #FF8844;
      }
    }

    &-btnContent {
      display: flex;
    }

    &-confirmText {
      width: 230px;
      height: 88px;
      background: #3477FF;
      border-radius: 8px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 36px;
      color: #FFFFFF;
      line-height: 54px;
      margin-left: 20px;
    }

    &-closeDailogText {
      width: 230px;
      height: 88px;
      background: #F3F3F3;
      border: 2px solid #CACACA;
      font-family: PingFangSC-Medium;
      border-radius: 8px;
      font-weight: 500;
      font-size: 36px;
      color: #333333;
      line-height: 54px;
    }
  }
}