/* eslint-disable prefer-rest-params */
/* eslint-disable max-len */
/* eslint-disable object-curly-newline */
/* eslint-disable prefer-spread */
/* eslint-disable no-empty */
/* eslint-disable no-unused-expressions */
/* eslint-disable arrow-body-style */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable react/sort-comp */
/* eslint-disable prefer-destructuring */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUListItem, MUActionSheet, MUText, MUModal, MUButton, MUImage, MUDialog, MUNoticebar,
} from '@mu/zui';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import Dispatch from '@api/actions';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { setStore } from '@api/store';
import { isAlipay } from '@mu/madp-utils';
import Protocol from '@components/protocol/index';

import './index.scss';

const themeColor = Util.getThemeColor(channelConfig.theme);
const ClosePng = 'https://file.mucfc.com/zlh/3/0/202305/202305182019591e0b87.png';

@track({ event: EventTypes.PO }, {
  pageId: 'PaydayModify',
  dispatchOnMount: true,
})
@pageHoc({ title: '修改还款日' })
export default class PaydayModify extends Component {
  constructor() {
    super(...arguments);
    this.state = {
      payDayString: '',
      payDayNumber: null,
      isNotDefaultDay: false, // 非当前还款日，
      canModifyPayDay: true, // 是否可变更还款日
      billList: [],
      showActionSheet: false,
      prepareDate: 1,
      tip: false, // 二次弹窗
      isOpenedNullPayDay: true, // 还款日为空时在不可变更页展示弹窗
      showPage: false,
      isCheckedContract: false, // 是否勾选了协议
      effectiveNextDueDate: '', // 最近一期生效还款日
      submitip: false, // 是否提示最近一期还款日弹窗
      minPayDate: '', // 最近一期还款日
      showContract: false // 是否可以展示合同，第一次试算不展示，26-28的借款到期日是错的
    };
    this.defaultPayday = null; // 用户目前的还款日
    this.findFlag = true;
    // Array(n)出来的数组只是声明了其长度，里面数组都是指向的empty，对于empty，map不起作用
    // 但解构时可以将empty转成undefined，则可以被map处理[...Array(28)]
    // 或者通过apply方式，apply第二个参数可以接受类对象，赋值时会遍历他得到n个undefined.
    this.payDayRange = Array.apply(null, { length: 28 }).map((item, index) => `${index + 1}日`);
    this.contractApplyList = []; // 合同数据
    this.showDiversionNotice = false; // 展示导流提示
  }

  async componentDidMount() {
    const [
      { data, errMsg, ret } = {},
    ] = await Promise.all([
      Dispatch.repayment.checkCurrentPayDayNew({ queryScene: '10' }, true),
      this.findFlag ? this.findNearBill() : (() => {})()
    ]);
    if (this.findFlag) this.findFlag = false;
    const { accountList } = data || {};
    // minRepayDate-下一期还款日
    const { repayDay = '', overdueStatus = '', minRepayDate } = (accountList && accountList[0]) || {};
    // 首先赋值判断是否有还款日，提示有无还款日弹窗
    this.defaultPayday = repayDay;
    if (ret === '0') {
      if (repayDay && overdueStatus === '1') {
        let payDayNumber = null;
        const dayNum = Number(repayDay);
        if (dayNum > 0 && dayNum < 29) payDayNumber = dayNum; // 因为只能选1到28，不知道会不会返回其他数字，这里需要处理一下
        this.setState({
          payDayString: `${repayDay || ('\xa0\xa0\xa0')}日`,
          payDayNumber,
          prepareDate: payDayNumber
        });
        this.defaultPayday = payDayNumber;
        dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'EnterPage', beaconContent: { cus: { custType: this.showDiversionNotice ? '1' : '0' } } });
      } else {
        // 可见可还逾期才提示去还款
        if (overdueStatus === '2' || overdueStatus === '3') { // 2逾期，3宽限期提示去还款
          this.showRepayToast();
        }
        this.setState({ canModifyPayDay: false });
      }
    } else {
      errMsg && Madp.showToast({
        title: errMsg,
        icon: 'none',
        duration: 2000
      });
    }

    // 确保canModifyPayDay为最终态后，再将showPage设置为true
    this.setState({ showPage: true, effectiveNextDueDate: minRepayDate });
  }

  async componentDidShow() {
    // 第一次进入页面时，会先执行didshow生命周期函数，findFlag默认值为true，此时不执行didshow中的该方法，执行didmount中的findNearBill
    if (!this.findFlag) {
      await this.findNearBill();
    }
  }

  // 准入规则-修改为查询建案接口
  async findNearBill() {
    // 调用建案接口，查看准入规则
    const caseRes = await Dispatch.repayment.applyConsultRepayCase({
      serviceType: '014',
      interfaceVersion: '1.0'
    });
    const { ret, data } = caseRes || {};
    const { contractApplyList, contractInfos, applyNo, caseStatus, existDiversionBillFlag } = data || {};
    this.contractApplyList = contractApplyList;
    this.contractInfos = contractInfos;
    // 接口响应正常成功+返回案件号applyNo有值+caseStatus为110，即建案成功,可修改还款日
    const applyCaseSuccess = (ret === '0') && applyNo && (caseStatus === '110');
    if (applyCaseSuccess) {
      this.showDiversionNotice = existDiversionBillFlag === 'Y';
      this.setState({
        applyNo
      });
    } else {
      // 如账户接口未逾期，建案接口返回逾期，则为【可见不可还逾期】，此时不提示去还款，因为还了也不能修改, 展示不可修改文案即可
      // 由账户接口进一步判断是否逾期提示先还款
      this.setState({
        canModifyPayDay: false,
      });
    }
  }

  /* 可见可还逾期才提示去还款
   * 账户接口与建案接口都会返回是否逾期，账户接口返回的逾期为【可见可还逾期】
   * 如账户接口未逾期，建案接口返回逾期，则为【可见不可还逾期】，此时不提示去还款，因为还了也不能修改
  */
   showRepayToast = () => {
     Madp.showModal({
       content: '请先还清已到期金额，再更改还款日',
       confirmText: '去还款',
       confirmColor: themeColor,
       showCancel: false,
       cancelText: '返回',
       success(res) {
         if (res.confirm) {
           setStore({ redirectFirstPage: '/pages/payday-modify/index' });
           if (isAlipay()) {
             Madp.reLaunch({ url: '/pages/index/index' });
           } else {
             Madp.redirectTo({ url: '/pages/index/index?needBack=1' });
           }
         } else if (res.cancel) {
           this.goBackLastPage();
           // Madp.navigateBack({ delta: 1 });
         }
       }
     });
   }


  // 日期确认
   async selectPayday() {
     const { prepareDate = '', payDayNumber = '' } = this.state;
     const currentDay = new Date().getDate(); // 当前日

     if (prepareDate !== this.defaultPayday) {
       const params = {
         adjustRepayDay: prepareDate.toString() || payDayNumber.toString(),
         adjustType: '02',
         adjustSource: '01',
         originRepayDay: this.defaultPayday ? this.defaultPayday.toString() : null,
         interfaceVersion: '2.0'
       };
       const { adjustRepayDateOrderInfoList, result, effectiveNextDueDate, minPayDate } = await Dispatch.repayment.getModifyRepayDateList(params);

       if (this.defaultPayday && result !== 'Y') {
         this.setState({ canModifyPayDay: false });
       }

       const nextMonthDate = effectiveNextDueDate ? effectiveNextDueDate.toString() : '000000';
       const nextMonthStr = nextMonthDate.slice(4, 6);
       const currentMonth = new Date().getMonth() + 1; // 当前月
       const currentMonthStr = currentMonth < 10 ? `0${currentMonth}` : String(currentMonth);

       this.setState({
         billList: adjustRepayDateOrderInfoList ? [...adjustRepayDateOrderInfoList] : [],
         effectiveNextDueDate,
         payDayString: this.payDayRange[prepareDate - 1],
         payDayNumber: prepareDate,
         isNotDefaultDay: (prepareDate) !== this.defaultPayday,
         showActionSheet: (nextMonthStr === currentMonthStr) && this.defaultPayday <= currentDay, // 日期显示 false
         minPayDate,
         showContract: true
       });

       // 原有是说 对比下一期最新还款日期是否是当前月，是的话展示【可能本月二次还款..】的弹窗
       // 开会结论说 【变更后在当月， 在加一个，原账户还款日 <= 当前日期】
       if ((nextMonthStr === currentMonthStr) && this.defaultPayday <= currentDay) {
         this.setState({
           tip: true
         });
       } else {
         this.setState({
           tip: false
         });
       }
     }
   }


   // 对比下一期生效的还款日与最近一期还款日是否相同
   onConfirmRepayDate = () => {
     const { effectiveNextDueDate, minPayDate } = this.state;
     /* 当最近一期生效还款日与最近一期还款日不同时，提示客户本月仍需还款
     * 出现该情况的场景为7天待还、还款日当天修改还款日
     * 原因：七天内待还不修改当期还款日
     * 举例：如还款日是16号，在8.15号修改为17号，9.17新还款日生效，8.16仍需还款，
            在8.4号修改（七天外），直接修改当期还款日，此时两个日期相同，均为8.17
     */
     if (effectiveNextDueDate !== minPayDate) {
       this.setState({
         submitip: true
       });
     } else {
       this.onConfirm();
     }
   }

   async onConfirm() {
     const { payDayNumber = '', applyNo, minPayDate } = this.state;
     const params = {
       applyNo,
       changeRepayDateApplyInfo: {
         adjustType: '02',
         originRepayDay: this.defaultPayday ? this.defaultPayday.toString() : null,
         adjustRepayDay: payDayNumber.toString(),
       }
     };
     const { ret, data } = await Dispatch.repayment.submitConsultRepayCase(params);
     if (ret !== '0') {
       this.showSubmitFailModal();
     } else {
       // submitCase接口响应正常成功+返回caseStatus为210即变更还款日成功
       const { caseStatus } = data || {};
       if (caseStatus === '210') {
         const dateCollection = minPayDate ? Util.getDateCollection(minPayDate && minPayDate.toString()) : [];
         Util.router.replace(`/pages/common-result/result?type=payday-modify&repayDate=${dateCollection.join('-')}`);
       } else {
         if (!this.defaultPayday) {
           this.setState({ canModifyPayDay: false });
         } else {
           this.showSubmitFailModal();
         }
       }
     }
   }

  /**
   * 是默认还款日或者没选协议, 就返回true, 让提交按钮置灰
   */
   get btnDisabled() {
     const { billList, payDayNumber, isCheckedContract } = this.state;

     if (billList.length > 0) { // 有账单是要考虑协议是否勾选
       return this.defaultPayday === payDayNumber || !isCheckedContract;
     }
     // 没账单时，只考虑是否是默认还款日
     return this.defaultPayday === payDayNumber;
   }

   showSubmitFailModal() {
     Madp.showModal({
       title: '温馨提示',
       content: '提交失败，请稍后再试或联系在线客服',
       showCancel: false,
       confirmText: '我知道了',
       confirmColor: themeColor,
     });
   }

  // 返回入口页
  goBackLastPage = () => {
    if (process.env.TARO_ENV === 'h5') {
      if (window.history.length < 2) {
        Madp.closeWebView();
      } else {
        Madp.navigateBack({ delta: 1 });
      }
    } else {
      Madp.navigateBack({ delta: 1 });
    }
  }

  renderModifyPayDay = () => {
    const { payDayString, canModifyPayDay, billList, showActionSheet, isNotDefaultDay, prepareDate, tip, isOpenedNullPayDay, payDayNumber, effectiveNextDueDate, submitip, minPayDate, showContract } = this.state;
    const row = [0, 1, 2, 3];
    const cell = [1, 2, 3, 4, 5, 6, 7];
    return canModifyPayDay ? (
      <MUView className="pages-bg modify-payday">
        {this.showDiversionNotice ? (<MUNoticebar icon="notice" className={themeColor === '#E60027' ? 'brand-selected' : ''}>仅招联金融借款可修改还款日，请注意修改后的还款计划</MUNoticebar>) : null}
        <MUView className="modify-payday-currentPayDay">
          <MUListItem
            title="还款日"
            beaconId="Selector"
            extraText={`每月${payDayString}`}
            onClick={() => this.setState({
              showActionSheet: true
            })}
          />
          {
            effectiveNextDueDate ? (
              <MUView className="modify-payday-currentPayDay-first">
                <MUView className="modify-payday-currentPayDay-first-title">最近一期生效还款日</MUView>
                <MUView className="modify-payday-currentPayDay-first-date">{Util.dateFormatter(effectiveNextDueDate && effectiveNextDueDate.toString())}</MUView>
              </MUView>
            ) : null
          }
        </MUView>

        {(billList.length > 0 && showContract) && (
        <MUView className="modify-payday-contract">
          <Protocol
            trackPrefix="repayment.PaydayModify"
            onChecked={(v) => { this.setState({ isCheckedContract: v }); }}
            contractApplyList={this.contractApplyList}
            contractInfoList={this.contractInfos}
            billExtendInfo={{ billList, payDayNumber }}
          />
        </MUView>
        )}
        <MUButton
          disabled={this.btnDisabled}
          className="submit-btn"
          beaconId="Submit"
          type="primary"
          onClick={() => this.onConfirmRepayDate()}
        >提交
        </MUButton>

        <MUView className="tips">
          {/* 优化后 */}
          <MUView className="title">注意事项</MUView>
          <MUView className="title">1、逾期后不支持修改</MUView>
          <MUView className="title">2、提交修改后，不可撤销，且半年内不能再修改</MUView>
          <MUView className="title">3、新还款日<MUText className="tip-txt">仅针对未到期期次生效，近7天内的还款日不支持修改</MUText></MUView>
        </MUView>
        <MUActionSheet beaconId="ActionSheet" isOpened={showActionSheet} onClose={() => this.setState({ showActionSheet: false })}>
          <MUView className="modify-payday-title">请选择每月还款日期</MUView>
          <MUImage
            className="modify-payday-closeIcon"
            src={ClosePng}
            beaconId="closeIcon"
            onClick={() => this.setState({ showActionSheet: false })}
          />
          <MUView className="modify-payday-table">
            {
              row.map((oneRow) => {
                return (
                  <MUView className="one-line">
                    {cell.map((oneCell) => {
                      const date = oneCell + oneRow * 7;
                      if (date> 25) return <MUView className="one-item" />;
                      let className = 'day-num ';
                      if (date === this.defaultPayday) {
                        className += 'defaultPayday';
                      } else if (prepareDate === date) {
                        className += 'check-day';
                      }
                      return (
                        <MUView
                          className="one-item"
                          beaconId="Selector"
                          // onClick={() => this.selectPayday(date)}>
                          onClick={() => this.setState({
                            prepareDate: date,
                            isNotDefaultDay: (date) !== this.defaultPayday,
                          })}
                        >
                          <MUView className={className} style={prepareDate === date ? `background: ${themeColor}` : ''}>{date}</MUView>
                        </MUView>
                      );
                    })}
                  </MUView>
                );
              })
            }
          </MUView>
          <MUButton
            disabled={!isNotDefaultDay}
            className="submit-btn"
            beaconId="Submit"
            type="primary"
            onClick={() => this.selectPayday()}
          >确定
          </MUButton>
        </MUActionSheet>
        {/* 变更还款日二次弹窗 跟产品确认已不会出现当月二次还款情况 */}
        <MUModal
          // type="image"
          className="modify-payday-disable-modal"
          beaconId="disableModal"
          isOpened={tip}
        >
          <MUView className="modify-payday-disable-modal-content">
            <MUView className="modal-tip-txt">您的下期还款时间为{Util.dateFormatter(effectiveNextDueDate && effectiveNextDueDate.toString(), 'datedot')}，即您本月还需再还一次款</MUView>
          </MUView>
          <MUView className="modal-tip-btn">
            <MUButton beaconId="reselectBtn" onClick={() => this.setState({ tip: false })}>重新选择</MUButton>
            <MUButton className="next-btn-txt" customStyle={`color: ${themeColor}`} beaconId="nextStepBtn" onClick={() => this.setState({ tip: false, showActionSheet: false })}>下一步</MUButton>
          </MUView>
        </MUModal>
        {/* 提交修改日确认弹窗 */}
        <MUDialog
          className="confirmRepayDayDialog"
          beaconId="disableModal"
          isOpened={submitip}
        >
          <MUView className="confirmRepayDayDialog-title">是否确认修改?</MUView>
          <MUView className="confirmRepayDayDialog-content">修改后，您最近一期还款日是<MUText className="confirmRepayDayDialog-content-sub">{Util.dateFormatter(minPayDate && minPayDate.toString())}</MUText>，请确认是否修改？</MUView>
          <MUView className="confirmRepayDayDialog-content">注意：近7天内的还款日不支持修改，请注意按时还款</MUView>
          <MUView className="confirmRepayDayDialog-btnContent">
            <MUButton className="confirmRepayDayDialog-closeDailogText" onClick={() => this.setState({ submitip: false })}>放弃修改</MUButton>
            <MUButton className="confirmRepayDayDialog-confirmText" onClick={() => { this.setState({ submitip: false }); this.onConfirm(); }}>确认修改</MUButton>
          </MUView>
        </MUDialog>
      </MUView>
    ) : (
      <MUView className="pages-bg modify-payday-tipwrap">
        <MUView className="modify-payday-tipwrap-text">您暂不符合修改还款日的条件，如有疑问请联系在线客服。</MUView>
        <MUView className="modify-payday-tipwrap-content">
          <MUView>温馨提示：</MUView>
          <MUView>1、若在半年内修改过还款日，不支持再次修改</MUView>
          <MUView>2、若有修改过还款日的借款未结清，请结清后再修改</MUView>
          <MUView>3、该服务不支持逾期客户使用，需先还清逾期欠款才可使用</MUView>
          <MUView>4、若您的借款仅剩下最后一期，请结清后再修改</MUView>
          <MUView>5、您的借款已办理过延后还，在延期后的首个还款日之前，不支持修改</MUView>
        </MUView>
        <MUModal
          className="tipsModal"
          isOpened={!this.defaultPayday && isOpenedNullPayDay}
          beaconId="defaultPaydayNullModal"
          content="您现在无还款日，暂不支持变更。"
          confirmText="知道了"
          onConfirm={() => {
            this.setState({ isOpenedNullPayDay: false });
            this.goBackLastPage();
          }}
        />
      </MUView>
    );
  };

  render() {
    const { showPage } = this.state;
    return showPage ? this.renderModifyPayDay()
      : (<MUView />);
  }
}
