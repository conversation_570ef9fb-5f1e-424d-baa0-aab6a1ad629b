import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUButton, MUImage
} from '@mu/zui';
import {
  track, EventTypes
} from '@mu/madp-track';
import pageHoc from '@utils/pageHoc';
import INTRO_TOP from './img/intro_top.png';
import INTRO_MIDDLE from './img/intro_middle.png';
import INTRO_BOTTOM from './img/intro_bottom.png';

import './intro.scss';

@track({ event: EventTypes.PO }, {
  pageId: 'RenewLoansIntro',
  dispatchOnMount: true,
})
@pageHoc({ title: '分期还本' })
export default class RenewLoansIntro extends Component {
  config = {
    navigationBarTitleText: '分期还本',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '分期还本' });
  }

  render() {
    return (<MUView className="renew-loans-intro">
      <MUImage className="renew-loans-intro__top" src={INTRO_TOP} />
      <MUImage className="renew-loans-intro__middle" src={INTRO_MIDDLE} />
      <MUImage className="renew-loans-intro__bottom" src={INTRO_BOTTOM} />
      <MUView className="renew-loans-intro__btn">
        <MUButton className="renew-loans-intro__btn--primary" beaconId="JumpToConfirm" onClick={() => Madp.navigateBack()}>去申请分期还本</MUButton>
      </MUView>
    </MUView>);
  }
}
