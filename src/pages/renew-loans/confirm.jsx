/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUText, MUButton, MUIcon, MUScrollView, MUImage
} from '@mu/zui';
import WaRichtext from '@mu/wa-richtext';
import { throttle, Url, isMuapp } from '@mu/madp-utils';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { urlDomain, getLoginInfo, opService, getPageConf } from '@mu/business-basic';
import pageHoc from '@utils/pageHoc';
import { injectState } from '@mu/leda';
import { getStore, setStore } from '@api/store';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import { OpRepayment } from '@mu/op-comp';
import {
  EVENT_CODE_MAP
} from '@utils/constants';

import Protocol from '@components/protocol/index';
import CommonDialog from './components/common-dialog';
import LoadingDialog from '@components/loading-dialog';
import RepayPlan from './components/repay-plan';
import checkBlue from './img/check_blue.png';
import FingerGif from '@components/assets/img/finger-confirm.gif';

import './confirm.scss';

const renewLoansConfirmPageId = 'aac7dc9b-739c-4660-ad3f-a23999b572b2';

@track({ event: EventTypes.PO }, {
  pageId: 'RenewLoansConfirm',
  dispatchOnMount: true,
})
@pageHoc({ title: '确认变更信息' })
@injectState({
  pageId() {
    return renewLoansConfirmPageId;
  },
  getPageConf: () => getPageConf(renewLoansConfirmPageId, true),
})
export default class RenewLoansConfirm extends Component {
  config = {
    navigationBarTitleText: '确认变更信息',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      totalAmt: '',
      origRepayBillList: [], // 变更前账单列表
      repayBillList: [], // 变更后账单列表
      selectedCnt: 0,
      isCheckedContract: '', // 表示合同组件是否同意勾选合同, 值为hasCheckedContract标识选中
      showCommonDialog: false,
      commonDialogInfo: {},
      origBillInfo: {},
      currBillInfo: {},
    };
    this.showPage = false;
    this.loginInfoHash = {};
    this.adjustCntList = []; // 可延长分期数
    this.applyNo = ''; // 案件号
    this.orderNo = ''; // 借据号
    this.supplySuccess = Url.getParam('supplySuccess');
    this.contractInfos = getStore('contractInfos') || []; // 3.0合同
    this.loadingDialog = {
      show: () => { },
      hide: () => { },
    };
    this.showLoadingDialog = false;
    this.finishLoading = false;
  }

  componentDidShow() {
    clearTimeout(this.loadingTimer);
    Madp.setNavigationBarTitle({ title: '确认变更信息' });
  }

  async componentDidMount() {
    await this.getLoginInfo();
    await this.initData();

    // 若是补充身份信息且成功回来的，直接提交案件
    if (this.supplySuccess === '1') {
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'UpdateIDCardSuccess' });
      this.submitCase(); // 提交案件
      return;
    }

    /**
     * 注意push路由的时机：不停留在当前页的不添加路由
     */
    if (process.env.TARO_ENV === 'h5') {
      Util.pushUrlState('keepState');
    }
  }

  // 初始化用户信息
  getLoginInfo = async () => {
    const { userIdHash, custIdHash } = await getLoginInfo() || {};
    this.loginInfoHash = { userHashNo: userIdHash, custHashNo: custIdHash };
  }

  // 初始化页面展示数据
  initData = async () => {
    const { applyNo, orderNo, canAdjustPeriodRange, adjustPeriod } = getStore('renewLoansInfo') || {}; // 获取缓存数据
    // 按照业务要求上升一个借据号判断 用户行为
    if (orderNo && orderNo.length > 0) {
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'orderNo', beaconContent: { cus: { orderNo } } });
    }
    if ((this.supplySuccess !== '1' || (this.supplySuccess === '1' && adjustPeriod)) && applyNo && orderNo && (canAdjustPeriodRange || []).length) {
      this.applyNo = applyNo;
      this.orderNo = orderNo;
      this.adjustCntList = canAdjustPeriodRange || []; // 可调整期数范围

      const initSelectedCnt = this.supplySuccess === '1' ? adjustPeriod : this.adjustCntList[this.adjustCntList.length - 1];

      // 初始化数据
      await this.extendRepayCal(initSelectedCnt);
    } else {
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'GetStoreError', beaconContent: { cus: { applyNo, orderNo, canAdjustPeriodRange, adjustPeriod } } });
      this.JumpToResult('?status=4'); // 兜底异常页
    }
  }

  // 分期还本试算
  extendRepayCal = async (adjustPeriod) => {
    const { orderNo, installTotalCnt } = getStore('renewLoansInfo') || {};
    const param = {
      ...this.loginInfoHash,
      orderNoList: [orderNo],
      extendScene: '03',
      extendMode: '00',
      phasePlanList: [
        {
          startPeriod: Number(installTotalCnt || 0) + 1,
          endPeriod: Number(installTotalCnt || 0) + Number(adjustPeriod || 0),
        }
      ],
      extendInstallTotalCnt: Number(installTotalCnt || 0) + Number(adjustPeriod || 0)
    };
    const { ret, data } = await Dispatch.repayment.postLoanExtendRepayCal(param) || {};
    if (ret === '0') {
      const { surplusTotalPrincipalAmt, repayBillList = [], origRepayBillList = [] } = data || {};
      if ((repayBillList || []).length > 0 && (origRepayBillList || []).length > 0) {
        this.setState({
          totalAmt: surplusTotalPrincipalAmt, // 展期后待还总本金
          selectedCnt: adjustPeriod,
          repayBillList,
          origRepayBillList,
          origBillInfo: this.getOrigBillInfo(origRepayBillList),
          currBillInfo: this.getCurrBillInfo(repayBillList)
        });
        this.showPage = true;
      } else {
        this.JumpToResult('?status=3'); // 异常页（当作办理失败）
      }
    } else {
      this.JumpToResult('?status=3'); // 异常页（当作办理失败）
    }
  }

  getOrigBillInfo = (billList = []) => {
    const { adjustBillRepayPlanList: adjustBillRepayPlanListFirst } = (billList && billList[0]) || {};
    const { adjustBillRepayPlanList: adjustBillRepayPlanListLast, repayDate: repayDateLast } = (billList && billList[billList.length - 1]) || {};
    const { installCnt: startInstallCnt, weightedIrrAnnualRate, penaltyRate: originalPenaltyRate } = (adjustBillRepayPlanListFirst && adjustBillRepayPlanListFirst[0]) || {};
    const { installCnt: endInstallCnt } = (adjustBillRepayPlanListLast && adjustBillRepayPlanListLast[0]) || {};

    return {
      startInstallCnt, // 变更前借据起始期数
      endInstallCnt, // 变更前借据结束期数
      yearRate: weightedIrrAnnualRate, // 变更前借据年利率
      lastRepayDate: repayDateLast, // 变更前借据最后一期还款日
      originalPenaltyRate, // 变更前借据日罚息利率
    };
  }

  getCurrBillInfo = (billList = []) => {
    const { repayDate: repayDateFirst, billAmt: billAmtFirst } = (billList && billList[0]) || {}; // 还款日和还款金额取变后第一个还款日的
    const { adjustBillRepayPlanList: adjustBillRepayPlanListLast, repayDate: repayDateLast } = (billList && billList[billList.length - 1]) || {}; // 结束期次和续贷后借款到期日期取展期后最后一个还款日的
    const { installCnt: endInstallCnt, principalTypeDesc, weightedIrrAnnualRate, stardIrrAnnualRate, penaltyRate } = (adjustBillRepayPlanListLast && adjustBillRepayPlanListLast[0]) || {};
    const { installTotalCnt } = getStore('renewLoansInfo') || {};

    return {
      startInstallCnt: installTotalCnt + 1, // 变更后借据起始期数
      endInstallCnt, // 变更后借据结束期数
      principalTypeDesc, // 还款方式
      yearRate: weightedIrrAnnualRate, // 变更后借据年利率
      underlineYearRate: stardIrrAnnualRate, // 变更后借据划线年利率
      penaltyRate, // 变更后借据日罚息利率
      lastRepayDate: repayDateLast, // 变更后借据的最后一期还款日
      firstRepayDate: repayDateFirst, // 变更后借据的第一期还款日
      repayAmt: billAmtFirst, // 变更后借据的第一期还款金额
    };
  }

  confirmHandler = async () => {
    const { isCheckedContract, selectedCnt, currBillInfo } = this.state;
    const { firstRepayDate, repayAmt } = currBillInfo || {};
    // 判断当前是否存在合同且已经勾选合同
    if (!isCheckedContract && (this.contractInfos && this.contractInfos.length > 0)) {
      Madp.showToast({ title: '请阅读并同意协议', icon: 'none' });
      return;
    }

    // 缓存数据
    const renewLoansInfo = getStore('renewLoansInfo') || {};
    setStore({ renewLoansInfo: { ...renewLoansInfo, adjustPeriod: selectedCnt, firstRepayDate, repayAmt } });

    const { authInfoDetails } = await Dispatch.repayment.checkSupplyInfo('extend_principal') || {};
    const isNeedSupplyID = !!authInfoDetails && authInfoDetails.filter((process) => process.authParamType === 'COMPENSATE_ID_INFO').length;
    if (isNeedSupplyID) {
      const supplyParams = {
        scene: 'SCENE_LOAN_RENEWAL',
        billType: 'renew-loans',
        applyNo: this.applyNo,
      };
      Util.gotoSupplyInfo(supplyParams);
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'GotoSupplyInfo' });
    } else {
      this.submitCase();
    }
  }

  submitCase = async () => {
    const { applyNo, orderNo, adjustPeriod, installTotalCnt } = getStore('renewLoansInfo') || {};
    if (!applyNo) {
      this.JumpToResult('?status=4'); // 兜底异常页
      return;
    }
    const { ret, data } = await Dispatch.repayment.postLoanServiceSubmitCase({
      ...this.loginInfoHash,
      applyNo: applyNo,
      serviceType: '016',
      orderNoList: [orderNo],
      phasePlanList: [
        {
          startPeriod: Number(installTotalCnt) + 1,
          endPeriod: Number(installTotalCnt) + Number(adjustPeriod),
        }
      ],
      installCnt: Number(installTotalCnt) + Number(adjustPeriod),
    });
    const { applyStatus } = data || {};
    if (ret === '0') {
      this.applyStatusNext(applyStatus);
    } else {
      this.JumpToResult('?status=4'); // 兜底异常页
    }
  }

  // 案件详情查询
  async queryCustCaseDetail() {
    const { applyNo } = getStore('renewLoansInfo');
    const { ret, data } = await Dispatch.repayment.postLoanQueryCaseDetail({
      ...this.loginInfoHash,
      applyNo: applyNo || this.applyNo,
    });
    const { applyStatus } = data || {};
    if (ret === '0') {
      this.applyStatusNext(applyStatus);
    } else {
      this.JumpToResult('?status=4');
      this.finishLoading = true;
      clearTimeout(this.loadingTimer);
      this.showLoadingDialog = false;
      this.loadingDialog && this.loadingDialog.hide();
    }
  }

  applyStatusNext = async (status) => {
    if (status === '140') { // 终审审批中
      // 若未开启倒计时，则开启
      if (!this.showLoadingDialog) {
        this.showLoadingDialog = true;
        this.loadingDialog && this.loadingDialog.show((finishLoading) => {
          this.finishLoading = finishLoading;
          clearTimeout(this.loadingTimer);
          this.JumpToResult('?status=2'); // 审批中
        });
        dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'LoadingDialogShow' });
      }
      // 若已开启倒计时，则继续轮询
      if (this.showLoadingDialog && !this.finishLoading) {
        this.loadingTimer = setTimeout(async () => {
          await this.queryCustCaseDetail();
        }, 1000);
      }
    } else if (status === '150' || status === '220') {
      this.JumpToResult('?status=3'); // 办理失败
      this.finishLoading = true;
      this.showLoadingDialog = false;
      this.loadingDialog && this.loadingDialog.hide();
    } else if (status === '210') {
      this.JumpToResult('?status=1'); // 办理成功
      this.finishLoading = true;
      this.showLoadingDialog = false;
      this.loadingDialog && this.loadingDialog.hide();
    } else if (status === '240') { // 审核通过且业务处理中（资金处理中）--理论上存在，但实际不存在
      this.JumpToResult('?status=2');
      this.finishLoading = true;
      this.showLoadingDialog = false;
      this.loadingDialog && this.loadingDialog.hide();
    } else { // 兜底异常页
      this.JumpToResult('?status=4');
      this.finishLoading = true;
      this.showLoadingDialog = false;
      this.loadingDialog && this.loadingDialog.hide();
      clearTimeout(this.loadingTimer);
    }
  }

  jumpToTradeReocrd = () => {
    Madp.navigateTo({
      url: `${urlDomain}/${Madp.getChannel()}/traderecords/#/pages/ious-information/index?orderNo=${this.orderNo}&needLogin=1`,
      useAppRouter: isMuapp()
    });
  }

  JumpToResult = (urlParam) => {
    Util.router.replace({
      path: `/pages/renew-loans/result${urlParam}`,
    });
  }

  showRepayExplainDialog = () => {
    const { currBillInfo = {} } = this.state;
    this.setState({
      showCommonDialog: true,
      commonDialogInfo: {
        dialogType: 'RepayExplainDialog',
        beaconId: 'RepayExplainDialog',
        currBillInfo,
      }
    });
    // Util.controlScroll('renew-loans-confirm', 'hidden');
    dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'RepayExplainDialog.Show' });
  }

  showInterestExplainDialog = () => {
    const { currBillInfo = {}, origBillInfo = {} } = this.state;
    this.setState({
      showCommonDialog: true,
      commonDialogInfo: {
        dialogType: 'InterestExplainDialog',
        beaconId: 'InterestExplainDialog',
        currBillInfo,
        origBillInfo,
      }
    });
    // Util.controlScroll('renew-loans-confirm', 'hidden');
    dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'InterestExplainDialog.Show' });
  }

  opGetRenewLoansIndexRetainDialogData() {
    const { origRepayBillList } = this.state;
    const origRepayBillInfo = (origRepayBillList || [])[(origRepayBillList || []).length - 1] || {};
    return {
      renewLoansCount: 1,
      renewLoansTotalPrincipalAmt: origRepayBillInfo.billPrincipalAmt,
      origLastCntRepayDate: `${Number((origRepayBillInfo.repayDate || '').substring(4, 6))}月${Number((origRepayBillInfo.repayDate || '').substring(6))}日`,
      renewLoansMaxPeriod: Math.max(...this.adjustCntList),
    };
  }

  async opOnPageEvent(eventName, interactionEventCode) {
    const renewLoansIndexRetainDialogData = this.opGetRenewLoansIndexRetainDialogData();
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: renewLoansConfirmPageId,
            renewLoansIndexRetainDialogData,
          },
          // 失败返回false，成功并点击返回对应的点击位，submit、cancel、close
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve('ERROR');
      }
    });
  }

  // 提供放置挽留弹窗的位置
  async beforeRouteLeave(from, to, next) {
    try {
      // 返回时，触发op交互事件：挽留弹窗
      if (to.path === from.path) {
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'Exit' });
        const opRes = await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.renewLoansConfirmRetain);
        if (!opRes || opRes === 'cancel' || opRes === 'close' || opRes === 'ERROR') {
          Madp.navigateBack();
          next(true);
        } else {
          // 如果成功打开op，则不继续导航,取消路由变化；解决二次进入后无法返回的问题
          next(false);
          return;
        }
      } else {
        next(true);
      }
    } catch (error) {
      console.log('error:', error);
      if (from.path === to.path) {
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'Exit' });
        Madp.navigateBack();
        next(true);
      } else {
        next(true);
      }
    }
  }

  render() {
    const {
      totalAmt, selectedCnt,
      repayBillList, origRepayBillList,
      origBillInfo, currBillInfo,
      showCommonDialog, commonDialogInfo
    } = this.state;

    const { startInstallCnt, endInstallCnt, yearRate, principalTypeDesc } = currBillInfo || {};

    const { beaconId: commonDialogBeaconId, dialogType: commonDialogType } = commonDialogInfo || {};

    return this.showPage ? (<MUView className="renew-loans-confirm">
      <MUView className="confirm-top">
        <MUView className="confirm-top__title">分期本金(元)</MUView>
        <MUView className="confirm-top__amount">
          <MUView className="confirm-top__amount--wrap">
            ￥
            <MUText className="confirm-top__amount--number">{totalAmt}</MUText>
          </MUView>
          <MUView className="confirm-top__amount--info" beaconId="JumpToTradeReocrd" onClick={this.jumpToTradeReocrd}>
            查看原借据
            <MUIcon value="jump-cicle" size="16" color="#3477FF" />
          </MUView>
        </MUView>
        <MUView className="confirm-top__desc"><WaRichtext beaconId="TopTitleRichtext" content={Util.getRepaymentLocale('FQHB01.FQHB01WA005')} /></MUView>
      </MUView>
      <MUView className="confirm-content">
        {this.adjustCntList.length === 2 ? (
          <MUView className="confirm-period">
            <MUView className="confirm-period__title">分期数</MUView>
            <MUScrollView
              className="confirm-period-scroll"
              scrollX
            >
              {this.adjustCntList.map((item, index) => (
                <MUView
                  className={`confirm-period-scroll__item${selectedCnt === item ? ' selected' : ''}`}
                  beaconId="SelectedCnt"
                  beaconContent={{ cus: { SelectedCnt: item } }}
                  onClick={throttle(() => {
                    if (selectedCnt !== item) {
                      this.extendRepayCal(item);
                    }
                  }, 1000)}
                >
                  <MUView>{item}期</MUView>
                  {selectedCnt === item ? (<MUImage className="confirm-period-scroll__item--checked" src={checkBlue} />) : null}
                </MUView>
              ))}
            </MUScrollView>
          </MUView>
        ) : null}
        <RepayPlan
          origRepayBillList={origRepayBillList}
          repayBillList={repayBillList}
          adjustCntLength={this.adjustCntList.length}
        />
        {/* 新还款计划信息 */}
        <MUView className="confirm-content-detail">
          {this.adjustCntList.length === 1 ? (<MUView className="confirm-top-item">
            <MUView className="confirm-top-item__title">分期数</MUView>
            <MUView className="confirm-top-item__light">{this.adjustCntList[0]}期</MUView>
          </MUView>) : null}
          <MUView className="confirm-top-item">
            <MUView className="confirm-top-item__title">{`第${startInstallCnt}-${endInstallCnt}期年利率(单利)`}</MUView>
            <MUView className="confirm-top-item__right">
              <MUView className="confirm-top-item__right--result">{Util.stringToPersent(yearRate, 4, false)}</MUView>
              <MUIcon className="confirm-top-item__right--result--icon" beaconId="ShowInterestExplainDialog" onClick={this.showInterestExplainDialog} value="info" size="15" color="#808080" />
            </MUView>
          </MUView>
          <MUView className="confirm-top-item">
            <MUView className="confirm-top-item__title">还款方式</MUView>
            <MUView className="confirm-top-item__right">
              <MUView className="confirm-top-item__right--result">{principalTypeDesc}</MUView>
              <MUIcon className="confirm-top-item__right--result--icon" beaconId="ShowRepayExplainDialog" onClick={this.showRepayExplainDialog} value="info" size="15" color="#808080" />
            </MUView>
          </MUView>
          <MUView className="confirm-top-item no-padding">
            <MUView className="confirm-top-item__title">提前还款规则</MUView>
            <MUView className="confirm-top-item__light">提前还款免违约金</MUView>
          </MUView>
        </MUView>
      </MUView>
      {/* 底部区域占位 */}
      <MUView className="renew-loans-confirm-space" />
      {/* 底部区域位置 */}
      <MUView className="renew-loans-confirm-bottom">
        <Protocol
          trackPrefix="repayment.RenewLoansConfirm"
          onChecked={(v) => { this.setState({ isCheckedContract: v }); }}
          contractInfoList={this.contractInfos}
          billExtendInfo={{ totalAmt, origBillInfo, currBillInfo }}
        />
        <MUButton
          className="renew-loans-confirm-bottom__button"
          onClick={throttle(this.confirmHandler, 2000)}
          beaconId="SubmitApply"
        >
          立即办理
        </MUButton>
        <MUView className="renew-loans-confirm-bottom__gif"><MUImage src={FingerGif} /></MUView>
      </MUView>
      <CommonDialog
        show={showCommonDialog}
        dialogType={commonDialogType}
        beaconId={commonDialogBeaconId}
        origBillInfo={origBillInfo}
        currBillInfo={currBillInfo}
        onClose={() => {
          this.setState({ showCommonDialog: false, commonDialogInfo: {} });
          // Util.controlScroll('renew-loans-confirm');
        }}
      />
      <LoadingDialog
        serviceType="renew-loans"
        onRef={(ref) => { this.loadingDialog = ref; }}
        countTime={10}
        isNewStyle
      />
      {/* 交互式运营组件 */}
      <OpRepayment pageId={renewLoansConfirmPageId} opEventKey="opPageLeave" />
    </MUView >) : <MUView />;
  }
}
