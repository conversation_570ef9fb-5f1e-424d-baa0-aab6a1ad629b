/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUIcon, MUText, MUImage,
} from '@mu/zui';
import WaRichtext from '@mu/wa-richtext';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { getLocale, getLoginInfo, opService, getPageConf } from '@mu/business-basic';
import { setStore } from '@api/store';
import pageHoc from '@utils/pageHoc';
import { injectState } from '@mu/leda';
import Util from '@utils/maxin-util';
import Dispatch from '@api/actions';
import { OpRepayment } from '@mu/op-comp';
import {
  EVENT_CODE_MAP, miniProgramChannel,
} from '@utils/constants';
import {
  isMuapp,
  Url,
} from '@mu/madp-utils';

import ListItem from './components/list-item';
import FingerGif from '@components/assets/img/finger-confirm.gif';

import './index.scss';

const renewLoansIndexPageId = '1ea0cc9d-1eb0-4cd3-99a3-bbec87ce9d84';
const channel = Madp.getChannel();

@track({ event: EventTypes.PO }, {
  pageId: 'RenewLoansIndex',
  dispatchOnMount: true,
})
@pageHoc({ title: '分期还本' })
@injectState({
  pageId() {
    return renewLoansIndexPageId;
  },
  getPageConf: () => getPageConf(renewLoansIndexPageId, true),
})
export default class RenewLoansIndex extends Component {
  config = {
    navigationBarTitleText: '分期还本',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      showPage: false,
      canBusiOrderList: [], // 可办理分期还本借据列表
      cannotBusiOrderList: [], // 当前不可办理分期还本借据列表（未到办理日期）
      showCannotBusiOrderList: false, // 展开不可办理借据列表
    };
    this.applyNo = ''; // 案件号
    this.contractInfos = []; // 合同信息 3.0
    this.loginInfoHash = {}; // 用户信息 hash
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '分期还本' });
  }

  async componentDidMount() {
    await this.getLoginInfo();

    // 初始化获取建案信息，建案状态
    await this.initApplyCase();
  }

  getLoginInfo = async () => {
    const { userIdHash, custIdHash } = await getLoginInfo() || {};
    this.loginInfoHash = { userHashNo: userIdHash, custHashNo: custIdHash };
  }

  initApplyCase = async () => {
    const { data, ret, errCode } = await Dispatch.repayment.postLoanCustApplyCase({ serviceType: '016', ...this.loginInfoHash }) || {};
    if (ret !== '0') {
      this.jumpToRefuse(errCode);
      return;
    }

    const { applyNo, intransitApplyNo, applyStatus, intransitApplyStatus, contractInfoList, } = data || {};
    if (applyStatus === '110' || intransitApplyStatus === '110') {
      this.applyNo = applyNo || intransitApplyNo; // 设置案件号
      this.contractInfos = (contractInfoList || []).filter(item => item && (item.contractCode === 'DHYWBG_FQHB' || item.contractCode === 'DHBGXY')); // 设置合同信息
      this.queryPackageInfo();
    } else if (applyStatus === '140' || applyStatus === '240' || intransitApplyStatus === '140' || intransitApplyStatus === '240') {
      this.JumpToResult('?status=2'); // 审批中
    } else {
      this.jumpToRefuse();
    }
  }

  queryPackageInfo = async () => {
    const { data, ret } = await Dispatch.repayment.postLoanQueryPackageInfo({ serviceType: '016', ...this.loginInfoHash }) || {};
    if (ret !== '0') {
      this.jumpToRefuse();
      return;
    }

    const canBusiOrderList = [];
    const cannotBusiOrderList = [];
    const { orderInfoList } = data || {};
    (orderInfoList || []).forEach(item => {
      if (item.canBusiTag === 'Y') {
        canBusiOrderList.push(item);
      } else if (item.canBusiTag === 'N' && item.rejectCode === '0707') {
        cannotBusiOrderList.push(item);
      }
    });
    this.setState({
      showPage: true,
      canBusiOrderList,
      cannotBusiOrderList,
      showCannotBusiOrderList: canBusiOrderList.length < 1 && cannotBusiOrderList.length > 0,
    });

    dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'EnterPage', beaconContent: { cus: { canBusiOrderLength: canBusiOrderList.length } } });

    /**
     * 注意push路由的时机：不停留在当前页的不添加路由
     */
    if (process.env.TARO_ENV === 'h5') {
      Util.pushUrlState('keepState');
    }
  }

  jumpToIntro = () => {
    Madp.navigateTo({
      url: '/pages/renew-loans/intro'
    });
  }

  jumpToConfirm = (item) => {
    const { orderNo, canAdjustPeriodRange, loanDate, installTotalCnt, installTotalAmt } = item || {};
    setStore({
      contractInfos: this.contractInfos,
      renewLoansInfo: { applyNo: this.applyNo, orderNo, canAdjustPeriodRange, loanDate, installTotalCnt, installTotalAmt }
    }); // 缓存合同信息
    Madp.navigateTo({
      url: '/pages/renew-loans/confirm'
    });
  }

  jumpToRefuse = (errCode) => {
    let status = '';
    switch (errCode) {
      case 'UMDP02585': // 准入拒绝-逾期客户
        status = '1';
        break;
      case 'UMDP02724': // 准入拒绝-无借据可办理
        status = '2';
        break;
      case 'UMDP03428': // 准入拒绝-未逾期管控客户
        status = '3';
        break;
      default:
        status = '4'; // 准入拒绝-兜底异常
        break;
    }
    Util.router.replace({
      path: `/pages/service-refuse/index?serviceType=RenewLoans&status=${status}`
    });
  }

  JumpToResult = (urlParam) => {
    Util.router.replace({
      path: `/pages/renew-loans/result${urlParam}`,
    });
  }

  opGetRenewLoansIndexRetainDialogData() {
    const { canBusiOrderList } = this.state;
    let renewLoansTotalPrincipalAmt = 0;
    let formatPeriodRange = [];
    (canBusiOrderList || []).forEach((item) => {
      renewLoansTotalPrincipalAmt = Util.floatAdd(renewLoansTotalPrincipalAmt, item.surplusPrincipalAmt || 0);
      formatPeriodRange = formatPeriodRange.concat(item.canAdjustPeriodRange || []);
    });
    return {
      renewLoansCount: (canBusiOrderList || []).length,
      renewLoansTotalPrincipalAmt: Number(renewLoansTotalPrincipalAmt).toFixed(2),
      renewLoansMaxPeriod: Math.max(...formatPeriodRange) || 0,
    };
  }

  async opOnPageEvent(eventName, interactionEventCode) {
    const renewLoansIndexRetainDialogData = this.opGetRenewLoansIndexRetainDialogData();
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: renewLoansIndexPageId,
            renewLoansIndexRetainDialogData,
          },
          // 失败返回false，成功并点击返回对应的点击位，submit、cancel、close
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve('ERROR');
      }
    });
  }

  selfCloseOrBack = () => {
    const isMiniChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
    // h5返回小程序，关闭webview
    if (isMiniChannel) {
      Madp.miniProgram.navigateBack();
      return;
    }
    if (isMuapp() && Url.getParam('repaymentFlag') !== 'Y') {
      Madp.closeWebView();
    } else {
      Madp.navigateBack().then().catch(() => {
        Madp.closeWebView();
      });
    }
  };

  // 提供放置挽留弹窗的位置
  async beforeRouteLeave(from, to, next) {
    try {
      // 返回时，逾期触发op交互事件：挽留弹窗
      if (to.path === from.path) {
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'Exit' });
        const opRes = await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.renewLoansIndexRetain);
        // 需退出
        if (!opRes || opRes === 'cancel' || opRes === 'close' || opRes === 'ERROR') {
          this.selfCloseOrBack();
          next(true);
        } else {
          // 如果成功打开op，则不继续导航,取消路由变化；解决二次进入后无法返回的问题
          next(false);
          return;
        }
      } else {
        next(true);
      }
    } catch (error) {
      console.log('error:', error);
      if (from.path === to.path) {
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'Exit' });
        this.selfCloseOrBack();
        next(true);
      } else {
        next(true);
      }
    }
  }

  render() {
    const {
      showPage,
      cannotBusiOrderList,
      canBusiOrderList,
      showCannotBusiOrderList,
    } = this.state;
    const canBusiDateList = (cannotBusiOrderList || []).map((item) => (item || {}).latestCanBusiDate);
    const formatCanBusiDateList = canBusiDateList.sort((a, b) => a - b);

    return showPage ? (<MUView className="renew-loans-index">
      <MUView className="renew-loans-index-bg" style={{ top: channel === '3CMBAPP' ? '45px' : 0 }} />
      <MUView className="renew-loans-index-top">
        <MUView className="renew-loans-index-top__title"><WaRichtext beaconId="TopTitleRichtext" content={getLocale('FQHB01.FQHB01WA003')} /></MUView>
        <MUView className="renew-loans-index-top__desc">
          <WaRichtext beaconId="TopDescRichtext" content={getLocale('FQHB01.FQHB01WA004')} />
          <MUText className="renew-loans-index-top__desc--blue" beaconId="JumpToIntro" onClick={this.jumpToIntro}>
            查看攻略 <MUIcon className="renew-loans-index-top__desc--icon" value="jump-cicle" color="#3477FF" size={14} /></MUText>
        </MUView>
      </MUView>
      {/* 可办理借据 */}
      {canBusiOrderList && canBusiOrderList.length > 0 ? (<MUView className="renew-loans-index-available">
        {canBusiOrderList.map((item) => <ListItem jumpToConfirm={() => this.jumpToConfirm(item)} item={item} showConfirmBtn={(canBusiOrderList || []).length > 1} />)}
      </MUView>) : null}
      {/* 不可办理借据 */}
      {cannotBusiOrderList && cannotBusiOrderList.length > 0 ? (<MUView className={`renew-loans-index-unAvailable${(canBusiOrderList || []).length > 0 ? ' renew-loans-index-unAvailable--special' : ''}`}>
        <MUView className="list-title">
          共有{cannotBusiOrderList.length}笔借据未到可办理时间
          {(canBusiOrderList || []).length > 0 ? (
            <MUView
              beaconId="ShowCannotBusiOrderList"
              onClick={() => this.setState({ showCannotBusiOrderList: !showCannotBusiOrderList })}
            >
              （{Util.getDateCollection(formatCanBusiDateList[0]).join('-')}起可办理）
              <MUIcon value={showCannotBusiOrderList ? 'caret-up' : 'caret-down'} size="12" color="#808080" />
            </MUView>
          ) : null}
        </MUView>
        {showCannotBusiOrderList ? (
          <MUView className={(canBusiOrderList || []).length > 0 ? 'list-content' : ''}>
            {cannotBusiOrderList.map((item) => <ListItem canDelay={false} item={item} />)}
          </MUView>
        ) : null}
      </MUView>) : null}
      {/* 底部区域占位 */}
      <MUView className="renew-loans-index-space" />
      {(canBusiOrderList || []).length === 1 ? (
        <MUView className="renew-loans-index-confirm" beaconId="PageJumpToConfirm" onClick={() => this.jumpToConfirm(canBusiOrderList[0])}>
          立即办理
          <MUView className="renew-loans-index-confirm-gif"><MUImage src={FingerGif} /></MUView>
        </MUView>
      ) : null}
      {/* 交互式运营组件 */}
      <OpRepayment pageId={renewLoansIndexPageId} opEventKey="opPageLeave" />
    </MUView>) : <MUView />;
  }
}
