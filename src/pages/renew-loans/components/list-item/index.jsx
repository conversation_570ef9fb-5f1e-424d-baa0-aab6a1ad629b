import { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import {
  MUView,
} from '@mu/zui';
import Util from '@utils/maxin-util';
import './index.scss';


export default class ListItem extends Component {
  static propTypes = {
    canDelay: PropTypes.bool,
    item: PropTypes.object,
    showConfirmBtn: PropTypes.bool,
    jumpToConfirm: () => {},
  }

  static defaultProps = {
    canDelay: true,
    item: {},
    showConfirmBtn: false,
    jumpToConfirm: () => {},
  }

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  render() {
    const { canDelay = true, item = {}, showConfirmBtn, jumpToConfirm } = this.props;
    const {
      loanDate, businessType, installTotalAmt,
      surplusPrincipalAmt, surplusTotalCnt, canAdjustPeriodRange,
      latestCanBusiDate
    } = item || {};

    return (
      <MUView key={item.orderNo} className="list-item">
        {canDelay ? (<MUView className="list-item-delay">
          <MUView className="amount-area">
            <MUView className="text">剩余待还本金</MUView>
            <MUView className="number">{surplusPrincipalAmt}元</MUView>
          </MUView>
          <MUView className="period-area">
            <MUView className="text">剩余待还期数</MUView>
            <MUView className="number">{surplusTotalCnt}期</MUView>
          </MUView>
          {showConfirmBtn ? (
            <MUView className="action-area">
              <MUView className="bubble">可再分{Math.max(...canAdjustPeriodRange)}期</MUView>
              <MUView className="confirm" beaconId="JumpToConfirm" onClick={jumpToConfirm}>立即办理</MUView>
              <MUView className="bubble--block" />
            </MUView>
          ) : (
            <MUView className="action-area">
              <MUView className="text">可分期数</MUView>
              <MUView className="number number--highlight">{Math.max(...canAdjustPeriodRange)}期</MUView>
            </MUView>
          )}
        </MUView>) : (<MUView className="list-item-not-delay">
          <MUView className="not-delay-top">
            {Util.getDateCollection(latestCanBusiDate).join('-')}起可办理延长{Math.max(...canAdjustPeriodRange)}期
          </MUView>
          <MUView className="not-delay-bottom">
            <MUView className="text">剩余待还本金：</MUView>
            <MUView className="number">{surplusPrincipalAmt}元</MUView>
          </MUView>
        </MUView>)}
        <MUView className="list-item__info">{`${Util.getDateCollection(loanDate).join('-')} ${businessType || '借款'}${installTotalAmt}元`}</MUView>
      </MUView>
    );
  }
}
