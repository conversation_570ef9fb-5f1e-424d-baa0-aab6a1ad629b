import { Component } from '@tarojs/taro';
import {
  MUText, MUView,
} from '@mu/zui';
import PropTypes from 'prop-types';

import './index.scss';

export default class RepayPlan extends Component {
  static propTypes = {
    origRepayBillList: PropTypes.array,
    repayBillList: PropTypes.array,
    adjustCntLength: PropTypes.number,
  };

  static defaultProps = {
    origRepayBillList: [],
    repayBillList: [],
    adjustCntLength: 0,
  }

  constructor(props) {
    super(props);
  }

  amountDatailText = (billItem = {}) => {
    let tags = [];
    if (Number(billItem.billPrincipalAmt) > 0) {
      tags.push(`本金${Number(billItem.billPrincipalAmt).toFixed(2)}元`);
    }
    if (Number(billItem.billInteAmt) > 0) {
      tags.push(`利息${Number(billItem.billInteAmt).toFixed(2)}元`);
    }
    return tags.length ? tags.join('，') : '';
  }

  renderPlanDetial = (billList = [], origRepayBillList = []) => {
    return (billList || []).length > 0 && billList.map((billItem, index) => (
      <MUView className="detail-list">
        <MUView className="detail-list__left">
          <MUView className="detail-list__left--cnt">{`第${billItem.adjustBillRepayPlanList[0].installCnt}期`}</MUView>
          {/* 第 1 期默认展示年份，第 1+n 期若跨年也展示年份 */}
          {index === 0 || (index > 0 && billList[index - 1].repayDate.slice(0, 4) !== billItem.repayDate.slice(0, 4)) ? <MUView className="detail-list__left--year">{`${billItem.repayDate}`.slice(0, 4)}</MUView> : null}
          <MUView className="detail-list__left--date">{`${`${billItem.repayDate}`.slice(4, 6)}-${`${billItem.repayDate}`.slice(6)}`}</MUView>
        </MUView>
        <MUView className="detail-list__middle">
          <MUView className="detail-list__middle--circle" />
          {index !== billList.length - 1 && <MUView className="detail-list__middle--line" />}
        </MUView>
        <MUView className="detail-list__right">
          <MUView className="detail-list__right--principal">
            {`${Number(billItem.billAmt).toFixed(2)}元`}
            {(index === (origRepayBillList || []).length - 1) ? (
              <MUView>
                <MUText className="detail-list__right__bill--line">{Number(((origRepayBillList || [])[index] || {}).billAmt || 0)}元</MUText>
              </MUView>
            ) : null}
          </MUView>
          <MUView className="detail-list__right--interest">
            {this.amountDatailText(billItem)}
            {(index === (origRepayBillList || []).length - 1) ? (
              <MUView>
                <MUText className="detail-list__right__bill--recnt">{Number(((origRepayBillList || [])[index] || {}).billPrincipalAmt || 0)}元本金再分{(billList || []).length - (origRepayBillList || []).length}期还</MUText>
              </MUView>
            ) : null}
          </MUView>
        </MUView>
      </MUView>
    ));
  }

  render() {
    const { origRepayBillList = [], repayBillList = [], adjustCntLength } = this.props;

    return (
      <MUView className={`repay-plan${adjustCntLength > 1 ? ' repay-plan--special' : ''}`}>
        <MUView className="repay-plan-sum">
          新还款计划
          <MUText className="repay-plan-sum__desc">
            共{(repayBillList || []).length}期 (原剩余待还{(origRepayBillList || []).length}期，分期期数{(repayBillList || []).length - (origRepayBillList || []).length}期)
          </MUText>
        </MUView>
        <MUView className="repay-plan-detail">{this.renderPlanDetial(repayBillList, origRepayBillList)}</MUView>
      </MUView>
    );
  }
}
