import {
  MUView,
  MUIcon,
  MUButton,
  MUText,
} from '@mu/zui';
import Util from '@utils/maxin-util';

import './index.scss';

/**
 * 利率说明、还款方式弹窗
 */
export default function CommonDialog({
  show,
  beaconId,
  dialogType,
  onClose,
  currBillInfo,
  origBillInfo,
}) {
  const renderInterestDialogContent = () => {
    const { startInstallCnt: origStartInstallCnt, endInstallCnt: origEndInstallCnt, yearRate: origYearRate } = origBillInfo || {};
    const { startInstallCnt, endInstallCnt, yearRate, underlineYearRate } = currBillInfo || {};
    const origInstallCnt = Number(origStartInstallCnt) === Number(origEndInstallCnt) ? origStartInstallCnt : `${origStartInstallCnt}-${origEndInstallCnt}`;
    return (<MUView className="dialog-content">
      <MUView className="interest-area">
        <MUView className="interest-area-item">
          <MUView className="interest-area-item__title">{`第${origInstallCnt}期年利率(单利)`}</MUView>
          <MUView className="interest-area-item__light">{Util.stringToPersent(origYearRate, 4, false)}</MUView>
        </MUView>
        <MUView className="interest-area-item">
          <MUView className="interest-area-item__title">{`第${startInstallCnt}-${endInstallCnt}期年利率(单利)`}</MUView>
          <MUView className="interest-area-item__right">
            {(Number(yearRate) > 0 && Number(underlineYearRate) > 0 && (Number(yearRate) < Number(underlineYearRate)) && Number(origYearRate) > 0 && (Number(origYearRate) !== Number(underlineYearRate))) ? (
              <MUView className="interest-area-item__right--interest">{Util.stringToPersent(yearRate, 4, false)}<MUText className="interest-area-item__right--interest--line">{Util.stringToPersent(underlineYearRate, 4, false)}</MUText></MUView>
            ) : (<MUView className="interest-area-item__right--interest">{Util.stringToPersent(yearRate, 4, false)}</MUView>)}
          </MUView>
        </MUView>
      </MUView>
      {(Number(yearRate) > 0 && Number(underlineYearRate) > 0 && (Number(yearRate) < Number(underlineYearRate)) && Number(origYearRate) > 0 && (Number(origYearRate) !== Number(underlineYearRate))) ? (
        <MUView className="interest-desc">
          <MUView>说明：</MUView>
          <MUText className="interest-desc__orange">{Util.stringToPersent(origYearRate, 4, false)}</MUText>为您当前办理一笔新借款的初始年利率(单利)；本次办理分期还本，自
          <MUText className="interest-desc__orange">{`第${startInstallCnt}期`}</MUText>起，采用年利率(单利)
          <MUText className="interest-desc__orange">{Util.stringToPersent(yearRate, 4, false)}</MUText>计收利息，前{origEndInstallCnt}期利率不变
        </MUView>
      ) : null}
    </MUView>);
  };

  const renderRepayExplainDialogContent = () => {
    return (<MUView className="dialog-content-repay" />);
  };

  return show ? (
    <MUView
      className="common-dialog"
      onTouchMove={(e) => { e.preventDefault(); e.stopPropagation(); }}
    >
      <MUView className="dialog-container">
        <MUIcon
          className="dialog-close"
          size={18}
          value="close2"
          beaconId={`${beaconId}.Close`}
          onClick={onClose}
        />
        <MUView className="dialog-title">{dialogType === 'RepayExplainDialog' ? '还款方式' : '利率说明'}</MUView>
        {dialogType === 'RepayExplainDialog' ? (renderRepayExplainDialogContent()) : (renderInterestDialogContent())}
        <MUButton
          className="dialog-button"
          onClick={onClose}
          beaconId={`${beaconId}.Confirm`}
        >
          我知道了
        </MUButton>
      </MUView>
    </MUView>
  ) : null;
}