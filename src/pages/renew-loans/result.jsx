/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUButton, MUImage, MURichText, MUIcon
} from '@mu/zui';
import { Url } from '@mu/madp-utils';
import { urlDomain } from '@mu/business-basic';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { getStore } from '@api/store';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import { isMiniProgramChannel } from '@utils/constants';
import FAIL_IMAGE from './img/fail_logo.png';

import './result.scss';

const channel = Madp.getChannel();

@track({ event: EventTypes.PO }, {
  pageId: 'RenewLoansResult',
  dispatchOnMount: true,
})
@pageHoc({ title: '办理结果' })
export default class RenewLoansResult extends Component {
  config = {
    navigationBarTitleText: '办理结果',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '办理结果' });
  }

  async componentWillMount() {
    // status = 1为成功, status = 2 为审核中, status = 3 为失败页, status = 4 为兜底异常页
    const status = Url.getParam('status');
    let statusConfigData = {};

    if (status === '1') { // 成功
      const { firstRepayDate, repayAmt } = getStore('renewLoansInfo') || {};
      const { dateFormat: todayDate } = Util.getCurrentDateTimeInFormat();
      if (firstRepayDate && repayAmt) {
        statusConfigData = {
          statusIcon: 'success',
          statusText: '申请成功，已变更还款计划',
          statusTips: todayDate === firstRepayDate
            ? `本借据<span style="color: #FF8844;">今日</span>应还<span style="color: #FF8844;">${repayAmt}元</span></br>请按时还款，避免逾期`
            : `最近一个还款日是<span style="color: #FF8844;">${Util.dateFormatter(firstRepayDate, 'chinese')}</span></br>请按时还款`,
          mainBtn: '查看还款计划',
          subBtn: '返回首页',
        };
      } else {
        statusConfigData = {
          statusImage: FAIL_IMAGE,
          statusText: '办理失败',
          statusTips: '很抱歉，您暂不符合办理条件，请及时还款，保持良好信用。',
          mainBtn: '返回首页',
        };
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'GetStoreError', beaconContent: { cus: { firstRepayDate, repayAmt } } });
      }
    } else if (status === '2') { // 审核中
      statusConfigData = {
        statusIcon: 'waiting',
        statusText: '申请已提交，审批中',
        statusTips: '您有一笔借款已提交分期还本变更申请，预计<span style="color: #FF8844;">5分钟</span>完成审核，请留意短信通知',
        mainBtn: '返回首页',
        subBtn: '',
      };
    } else if (status === '3') { // 办理失败
      statusConfigData = {
        statusImage: FAIL_IMAGE,
        statusText: '本次申请未通过',
        statusTips: '很抱歉，经综合评估，您暂不符合办理条件，请继续累积信用',
        mainBtn: '返回首页',
      };
    } else if (status === '4') { // 异常页
      statusConfigData = {
        statusImage: FAIL_IMAGE,
        statusText: '办理失败',
        statusTips: '很抱歉，您暂不符合办理条件，请及时还款，保持良好信用。',
        mainBtn: '返回首页',
      };
    }

    this.setState({
      statusCofig: statusConfigData
    });
  }

  beforeRouteLeave(from, to, next, state) {
    if (to.path === '/pages/renew-loans/confirm' || to.path === '/pages/renew-loans/index') {
      if (isMiniProgramChannel) {
        this.JumpToRepayIndex();
      } else {
        Madp.redirectTo({url: `${urlDomain}/${channel}/repayment/#/pages/index/index`});
      }
    } else {
      next(true);
    }
  }

  JumpToRepayIndex = (showRepayPlan = false) => {
    if (isMiniProgramChannel) {
      Madp.miniProgram.reLaunch({
        url: `/repayment/pages/index/index${showRepayPlan ? '?showRepayPlan=1' : ''}`,
      });
    } else {
      const status = Url.getParam('status');
      // 增加标识，分期还本成功后回到还款首页需要刷新页面，更新账单信息或拉起还款计划
      if (status === '1') Madp.setStorageSync('renewLoansBack', 'Y', 'SESSION');

      if (channel === '3CMBAPP') {
        Madp.redirectTo({url: `${urlDomain}/${channel}/repayment/#/pages/index/index${showRepayPlan ? '?showRepayPlan=1' : ''}` });
      } else {
        if (showRepayPlan) Madp.setStorageSync('showRepayPlan', 'Y', 'SESSION');
        const length = window && window.history && window.history.length;
        if (length > 2) {
          Madp.navigateBack({ delta: 2 });
        } else {
          Madp.closeWebView().then().catch(() => {
            Madp.navigateBack();
          });
        }
      }
    }
  }

  render() {
    const { statusCofig } = this.state;
    const { statusIcon, statusImage, statusText, statusTips, mainBtn = '返回', subBtn } = statusCofig || {};

    return (<MUView className={`renew-loans-result ${channel === '3CMBAPP' ? 'special' : ''}`}>
      <MUView className="renew-loans-result-top">
        {statusIcon ? <MUIcon className="renew-loans-result-top__img" value={statusIcon} size="60" color={statusIcon === 'success' ? '#03D560' : '#3477FF'} />
          : <MUImage className="renew-loans-result-top__img" src={statusImage} />}
        <MUView className="renew-loans-result-top__text">{statusText}</MUView>
        <MURichText className="renew-loans-result-top__tips" nodes={statusTips} />
        <MUView className="renew-loans-result-top__buttons">
          {subBtn && (
            <MUButton
              className="renew-loans-result-top__buttons--sub"
              beaconId="SubButtonClick"
              onClick={() => this.JumpToRepayIndex(false)}
            >
              {subBtn}
            </MUButton>
          )}
          <MUButton
            beaconId="MainButtonClick"
            className="renew-loans-result-top__buttons--main"
            onClick={() => {
              if (mainBtn === '查看还款计划') {
                this.JumpToRepayIndex(true);
              } else {
                this.JumpToRepayIndex(false);
              }
            }}
          >{mainBtn}</MUButton>
        </MUView>
      </MUView>
    </MUView>);
  }
}
