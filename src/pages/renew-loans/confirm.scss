.renew-loans-confirm {
  height: 100vh;
  width: 100vw;
  background-color: #f0f0f0;

  .confirm-top {
    padding: 30px;
    background-color: #FFFFFF;

    &__title {
      font-size: 28px;
      line-height: 32px;
      color: #333;
      font-weight: 500;
    }

    &__amount {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin: 30px 0;

      &--wrap {
        font-size: 36px;
        line-height: 36px;
        color: #333;
        font-weight: 500;
      }

      &--number {
        margin-left: 6px;
        color: #333;
        font-size: 60px;
        font-weight: 700;
        font-family: "DINAlternate-Bold";
        line-height: 44px;
      }

      &--info {
        color: #3477ff;
        font-size: 26px;
        line-height: 32px;
        font-weight: 400;

        .mu-icon {
          margin-left: 8px;
          margin-bottom: 4px;
        }
      }
    }

    &__desc {
      padding: 12px 0 12px 20px;
      background: #3477ff1a;
      border-radius: 8px;
      font-size: 26px;
      line-height: 26px;
      color: #3477FF;
      font-weight: 400;
    }
  }

  .confirm-content {
    margin: 20px;
    border-radius: 16px;

    &-detail {
      margin-top: 20px;
      padding: 30px;
      background: #fff;
      border-radius: 16px;

      .confirm-top-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 24px;
        color: #333333;
        font-size: 28px;
        line-height: 42px;
        font-weight: 400;
  
        &__right {
          display: flex;
          align-items: center;
          justify-content: center;
  
          &--result {
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
  
            &--icon {
              margin-left: 10px;
            }
          }
        }
  
        &__light {
          font-weight: 600;
        }
      }

      .no-padding {
        padding-bottom: 0;
      }
    }
  }

  .confirm-period {
    width: auto;
    padding: 32px 30px 34px 30px;
    border-radius: 16px 16px 0 0;
    background: #FFFFFF;
    overflow: hidden;

    &__title {
      color: #333333;
      font-size: 32px;
      line-height: 42px;
      font-weight: 600;
    }

    &-scroll {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-top: 20px;

      &__item {
        position: relative;
        width: 48%;
        height: 72px;
        border-radius: 8px;
        text-align: center;
        color: #333333;
        font-size: 28px;
        line-height: 72px;
        font-weight: 600;
        background: #f3f3f3;

        &--checked {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 56px;
          height: 56px;
        }
      }

      .selected {
        color: #3477ff;
        background-color: #E2EBFF;
      }
    }
  }

  &-space {
    width: 100%;
    height: 210px;
  }

  &-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 210px;
    background-color: #FFFFFF;
    z-index: 9;

    .protocol {
      height: 38px;
      display: flex;
      align-items: center;
      line-height: 38px;
      padding: 20px 30px 0 30px;

      .protocol-container {
        .protocal-link,
        .mu-icon-checked,
        .mu-text__default__brand {
          color: #3477FF !important;
        }
  
        .at-button--primary {
          background-color: #3477FF !important;
        }
      }
    }

    &__button {
      width: auto;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 20px 40px;
      color: #f3f3f3;
      font-size: 36px;
      font-weight: 600;
      border-radius: 50px;
      background: #3477ff;
    }

    &__gif {
      position: absolute;
      bottom: -22px;
      right: 20px;
      width: 110px;
      height: 110px;
      font-size: 0;

      .taro-img, image {
        width: 100%;
        height: 100%;
      }
    }
  }
}