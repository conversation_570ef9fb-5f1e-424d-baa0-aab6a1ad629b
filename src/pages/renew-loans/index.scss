.renew-loans-index {
  min-height: calc(100vh - 50px);
  width: 100vw;
  padding-bottom: 50px;

  &-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 547px;
    background-image: url('./img/index_bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    opacity: 0.5;
  }

  &-top {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 80px 50px 0;
    margin-bottom: 56px;

    &__title {
      width: 100%;
      height: 48px;
      margin-bottom: 32px;
      color: #000000;
      font-size: 48px;
      font-weight: 600;
      line-height: 48px;
    }

    &__desc {
      position: relative;

      &--blue {
        position: absolute;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        color: #3477FF;
        font-size: 24px;
        font-weight: 400;
      }

      &--icon {
        margin-left: 5px;
      }
    }
  }

  &-available {
    position: relative;
    margin: 0 20px;
  }

  &-unAvailable {
    position: relative;
    margin: 0 20px;

    &--special {
      height: calc(100% - 727px);
      overflow: hidden;
    }

    .list-title {
      display: flex;
      margin: 8px 0 16px 30px;
      font-size: 26px;
      line-height: 39px;
      font-weight: 400;
      color: #808080;
    }
    
    .list-content {
      height: calc(100% - 790px);
      overflow: scroll;
    }
  }

  &-space {
    width: 100%;
    height: 110px;
  }

  &-confirm {
    position: fixed;
    left: 0;
    bottom: 40px;
    margin: 0 40px;
    width: calc(100% - 80px);
    background: #3477ff;
    border-radius: 50px;
    font-size: 36px;
    line-height: 100px;
    color: #fff;
    font-weight: 600;
    text-align: center;

    &-gif {
      position: absolute;
      top: 32px;
      right: -20px;
      width: 110px;
      height: 110px;
      font-size: 0;

      .taro-img, image {
        width: 100%;
        height: 100%;
      }
    }
  }
}