@import '../../components/weapp/index.scss';

.repay-fail {
  background-color: #fff;
  text-align: center;
  position: relative;
  min-height: 100vh; // 很关键

  &-icon {
    margin-top: 120px;
    margin-bottom: 30px;
  }

  &-content {
    font-size: 40px;
    line-height: 40px;
    font-weight: 500;
    margin-bottom: 36px;
    color: #333333;
    text-align: center;
  }

  &-guide {
    font-size: 32px;
    color: #888888;
    margin: 0 40px;
  }

  &-sub-guide {
    font-size: 32px;
    color: #888888;
    margin-bottom: 50px;
  }

  &-btn {
    width: 350px;
    margin-bottom: 50px;
  }

  &-topImg {
    margin: 80px 0 50px;
    width: 160px;
    height: 160px;
  }

  &-btn-swan {
    width: 350px;
    margin-bottom: 50px;
    border: 1PX solid #3477ff;
    background: #3477ff;
    color: #fff;
    &::after {
      border: unset;
    }    
  }
}

.repay-fail-chat {
  position: absolute;
  bottom: 0;
  width: 100%;

  .chat-entry {
    .chat-entry__bottom {
      margin: 36px 0;
    }

    display: flex;
    justify-content: center;
  }
}
.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}
