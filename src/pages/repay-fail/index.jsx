/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import {
  MUView, MUIcon, MUButton, MUImage, MUNavBarWeapp
} from '@mu/zui';
import { Button } from '@tarojs/components';
import Madp from '@mu/madp';
import {
  // isMuapp,
  Url,
} from '@mu/madp-utils';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import { repaymentFn, getTemplateIds, pluginMessageHandler } from '@mu/business-plugin-utils';
import {
  getSwanTemplate, opService, getPageConf, getProductAllParams
} from '@mu/business-basic';
import { miniProgramChannel, isSwanMini, EVENT_CODE_MAP } from '@utils/constants';
import CustomConfig from '@config/index';
import { urlDomain } from '@utils/url_config';
import { dispatchTrackEvent, track, EventTypes } from '@mu/madp-track';
import { ChatEntry } from '@mu/chat-entry-component';
import { injectState } from '@mu/leda';
import { RepaymentPlugin } from '@mu/business-plugin';
import { OpRepayment } from '@mu/op-comp';

import './index.scss';

const themeColor = Util.getThemeColor(CustomConfig.theme);
const { repayFailFn } = repaymentFn || {};
const repayFailPageId = 'edf4e032-cd82-43d7-9e91-1c6edd6f02eb';
const repayFailPng = 'https://file.mucfc.com/zlh/3/0/202305/20230518202216cd319f.png';

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: repayFailPageId
    }
  }
}, {
  pageId: 'RepayFail',
  dispatchOnMount: true,
})
@pageHoc({ title: '还款结果' })
@injectState({
  pageId() {
    return repayFailPageId;
  },
  getPageConf: () => getPageConf(repayFailPageId, true),
  stateKeys: ['virtualIdCommon']
})
export default class RepayFail extends Component {
  config = {
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      resultFromUrl: '',
      disableBackBtn: false, // 是否禁用返回按钮
      templateIds: [],
      subscribeId: `id${new Date().getTime().toString(36)}${Math.random().toString(36).substr(2, 9)}`
    };
    this.hocConfig = { // pageHoc会读这个字段来设置标题
      navTitle: '还款结果'
    };
    this.pageStatus = ''; // 页面状态
    this.openInteractiveSubscribe = false; // 开放交互运营订阅
  }

  componentWillMount() {
    const needRefresh = Madp.getStorageSync('needRefresh', 'SESSION');
    if (needRefresh === 'Y' && process.env.TARO_ENV === 'h5') {
      Madp.setStorageSync('needRefresh', '', 'SESSION');
      window.location.reload();
      return;
    }
  }

  async getSwanId() {
    const channel = Madp.getChannel();
    const templateIds = await getTemplateIds('subscribestdFailClick', channel, 'repayment');
    this.setState({ templateIds });
  }

  async componentDidMount() {
    this.fillBackgroundColor();
    const resultFromUrl = Url.getParam('result');
    this.preRepayScene = Url.getParam('preRepayScene') || '';
    this.startLocation = Url.getParam('startLocation') || '';
    this.startState = Url.getParam('startState') || '';
    this.setState({
      resultFromUrl,
    });

    const expressScene = Url.getParam('expressScene') || '';
    const isExpress = Url.getParam('isExpress') || '';
    dispatchTrackEvent({
      event: EventTypes.EV,
      beaconId: 'RepayFAIL',
      target: this,
      beaconContent: {
        cus: {
          isExpress: isExpress === '0' ? false : !!expressScene,
          expressScene: expressScene,
          transSeqno: Url.getParam('transSeqno') || '',
        }
      },
    });
    if (isSwanMini) {
      this.getSwanId();
    }

    this.getPageStatus();
  }

  // 获取页面状态
  getPageStatus = () => {
    let pageStatus = '';
    const repaySence = Url.getParam('repaySence') || '';
    switch (repaySence) {
      case 'advanceRepay':
        pageStatus = 'repayFailAdvance';
      break;
      case 'normalRepay':
        pageStatus = 'repayFail';
      break;
      case 'overdueRepay':
        pageStatus = 'repayFailOverdue';
      break;
      default:
        pageStatus = '';
        break;
    }
    this.pageStatus = pageStatus;
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: 'ShowPageStatus',
      beaconContent: { cus: { pageStatus } }
    });
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: this.hocConfig.navTitle });
  }

  queryInteractiveSubscribeResult = async () => {
    let interactiveSubscribeChannelList = '';
    if (getProductAllParams && typeof getProductAllParams === 'function') {
      ({ interactiveSubscribeChannelList } = await getProductAllParams('HK.HK01') || {});
    }
    this.openInteractiveSubscribe = (interactiveSubscribeChannelList || '').indexOf('ALL') > -1 || (interactiveSubscribeChannelList || '').indexOf(Madp.getChannel()) > -1;
  }

  async ledaDidMount() {
    // 订阅初始化
    await this.queryInteractiveSubscribeResult();
    if (!this.openInteractiveSubscribe) {
      // 进入页面时激活订阅功能
      const subscribestdFailCode = 'subscribestdFail';
      this.actionSubscribeHandler(subscribestdFailCode);
    }
  }

  actionSubscribeHandler(scene, callback, tmplIds = []) {
    const { virtualIdCommon, subscribeId } = this.state || {};
    let param = {};
    if (isSwanMini) {
      param = {
        virtualIdCommon,
        scene,
        tmplIds,
        subscribeId
      };
      repayFailFn.subscribeHandler(param, callback, this);
    } else {
      pluginMessageHandler({
        businessName: 'repayment',
        pageType: 'repayFail',
        scene,
        data: {}, // 需要传额外数据
        success: () => {
        },
        fail: () => {
        },
        complete: () => {
          callback && callback();
        }
      });
    }
  }

  /**
   * 百度编译问题，Button如果只有bindsubscribe，点击会显示不存在onBackBtnClick函数
   * 所以需要给Button加个onClick，这样点击一次会调用两次本函数，用e.type区分
   */
  onBackBtnClickSwan(e) {
    if (!isSwanMini) {
      return;
    }
    if (isSwanMini && e.type === 'tap') {
      return;
    }
    const { disableBackBtn, templateIds } = this.state;

    if (!disableBackBtn) {
      const tmplIds = getSwanTemplate(e, templateIds) || [];// 后端模版
      // 点击之后返回按钮不可用
      this.setState({
        disableBackBtn: true
      });

      const callBackFn = () => {
        // 事件完成之后，返回按钮变为可用
        this.setState({
          disableBackBtn: false
        });
        if (this.preRepayScene) {
          this.jumpOrClose();
        } else {
          this.jumpToIndex();
        }
        this.getSwanId();
      };

      this.actionSubscribeHandler('subscribestdFailClick', callBackFn, tmplIds);
    }
  }

  async opOnPageEvent(eventName, interactionEventCode) {
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: repayFailPageId,
            subscribeBannerProps: {
              type: 'click',
              compClassName: 'leda-app-wrap',
              compInnerClassName: 'leda-comp-inner',
              luiTrack: {
                moduleId: 'repayment', //用于触发上报，beaconId = {moduleId}.{pageId}.{name}
                pageId: 'RepayFail',
              },
              name: `${this.pageStatus}Subscribe`,
              boothTitle: '订阅',
              id: 'localSubscribeBanner'
            },
            // 避免对现有事件影响，交互订阅新增回调按新增处理
            callback: (res) => {
              resolve(res);
            }
          },
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve(true);
      }
    });
  }

  async onBackBtnClick() {
    const { disableBackBtn } = this.state;

    if (!disableBackBtn) {
      // 点击之后返回按钮不可用
      this.setState({
        disableBackBtn: true
      });

      const callBackFn = () => {
        // 事件完成之后，返回按钮变为可用
        this.setState({
          disableBackBtn: false
        });
        if (this.preRepayScene) {
          this.jumpOrClose();
        } else {
          this.jumpToIndex();
        }
      };
      if (Madp.getChannel() === '0HWYFW' || Madp.getChannel() === '3WYDAPP') callBackFn(); // 鸿蒙元服务、沃易贷APP跳过订阅
      if (!this.openInteractiveSubscribe) {
        this.actionSubscribeHandler('subscribestdFailClick', callBackFn);
      } else {
        await this.opOnPageEvent('opBackClick', EVENT_CODE_MAP[`${this.pageStatus}Click`]);
        callBackFn();
      }
    }
  }

  jumpToIndex() {
    Util.router.replace('/pages/index/index');
  }

  jumpOrClose() {
    const imUrl = process.env.TARO_ENV === 'h5'
      ? (sessionStorage.getItem('CSP_CHAT_URL') || '')
      : (Madp.getStorageSync('CSP_CHAT_URL', 'SESSION') || '');
    if (this.preRepayScene) {
      if (window.history.length < 3 || !this.startLocation || (Number(this.startState) === 0 && Number(this.startLocation) === 1) || (!imUrl && this.preRepayScene === '4')) { // 用户直接进的承接页
        Madp.closeWebView();
      } else if (this.preRepayScene === '4') {
        Madp.reLaunch({ url: imUrl || `${urlDomain}/${Madp.getChannel()}/csp/#/pages/chat/index` });
      } else if (miniProgramChannel.indexOf(Madp.getChannel()) > -1) {
        Madp.redirectTo({ url: `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/bargain/index?_needLogin=1&fromResult=1` });
      } else {
        Madp.reLaunch({ url: `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/bargain/index` });
      }
    }
  }

  fillBackgroundColor() {
    if (process.env.TARO_ENV === 'h5') {
      const pageElements = document.getElementsByClassName('repay-fail');
      const winHeight = window.innerHeight;
      // 每次更新会多一个page，不懂是什么机制，取最后一个
      pageElements[pageElements.length - 1].style.height = `${winHeight}px`;
    }
  }

  get failDesc() {
    const { type } = this.$router.params;
    let desc = {
      content: '还款失败',
      guide: '抱歉，还款失败，请更换银行卡再试',
      subGuide: '如有疑问请联系在线客服！',
      btnText: this.preRepayScene ? '返回' : '回到还款首页',
    };
    if (type === 'adjust') {
      desc = {
        ...desc,
        content: '账单调整申请提交失败',
        guide: '请重新提交或过段时间再试',
        subGuide: '',
        btnText: '返回',
      };
    }
    return desc;
  }

  getResultData(resultFromUrl) {
    const resultData = JSON.parse(resultFromUrl || '{}');
    return resultData;
  }

  render() {
    const {
      resultFromUrl,
      templateIds,
      subscribeId
    } = this.state;
    const { type } = this.$router.params;
    const resultData = this.getResultData(resultFromUrl);
    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="还款结果"
          leftArea={[
            {
              type: 'icon',
              value: 'back'
            }
          ]}
        />
        <MUView className="repay-fail">
          {
            (type === 'total' || type === '7days' || type === 'extend') ? (
              <MUImage className="repay-fail-topImg" src={repayFailPng} />
            ) : (
              <MUIcon value="warning" size="80" className="repay-fail-icon" />
            )
          }
          <MUView className="repay-fail-content">{this.failDesc.content}</MUView>
          <MUView className="repay-fail-guide">{resultData.transDesc || this.failDesc.guide}</MUView>
          <MUView className="repay-fail-sub-guide">{this.failDesc.subGuide}</MUView>
          {
            isSwanMini ? (
              <Button
                className="repay-fail-btn-swan"
                beaconId="failBtn"
                onClick={this.onBackBtnClickSwan.bind(this)}
                bindsubscribe="onBackBtnClickSwan"
                openType="subscribe"
                template-id={templateIds}
                subscribe-id={subscribeId}
              >
                {this.failDesc.btnText}
              </Button>
            ) : (
              <MUButton
                className="repay-fail-btn"
                type="primary"
                beaconId="failBtn"
                onClick={this.onBackBtnClick.bind(this)}
              >
                {this.failDesc.btnText}
              </MUButton>
            )
          }

          <MUView className="repay-fail-chat">
            {Util.showChatEntry() && <ChatEntry busiEntrance={Util.getBusiEntrance()} themeColor={themeColor} />}
          </MUView>
        </MUView>
        {/* 插槽：事件订阅 */}
        <RepaymentPlugin
          businessName="repayment"
          pageType="repayFail"
          loc="bottom"
        />
        {/* 交互式运营组件 */}
        <OpRepayment pageId={repayFailPageId} opEventKey="opBackClick" />
      </MUView>
    );
  }
}
