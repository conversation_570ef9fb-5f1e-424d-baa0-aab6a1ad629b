.sectionPayView {
  display: flex;
  flex-direction: column;
  background-color: white;
  width: 100vw;
  overflow: hidden;
  height: 800px;

  .top {
    height: 100px;
    width: 100vw;

    margin-left: calc(50% - 160px);
    margin-top: 20px;

    .titleDesc {
      font-size: 32px;
    }

    .iconInfo {
      position: absolute;
      top: 42px;
      /* stylelint-disable-next-line */
      right: 10PX;
    }
  }

  .image {
    align-self: center;
    margin-top: 60px;
    width: 200px;
    height: 200px;
  }

  .textPay {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    text-align: center;
    color: '333333';
    font-size: 30px;
  }

  .sectionViewBottom {
    margin-top: 100px;
    display: flex;
    flex-direction: row;

    .imageSelect {
      margin-top: 3px;
      width: 32px;
      height: 32px;
      margin-left: calc(100vw / 750 * 155);
    }

    .textOne {
      margin-left: 16px;
      text-align: center;
      color: #333333;
      font-size: 26px;
    }

    .textTwo {
      text-align: center;
      color: #388ff0;
      font-size: 26px;
    }
  }

  .confirm-button {
    margin-left: 30px;
    margin-right: 30px;
    margin-top: 60px;
    height: 100px;
  }

}