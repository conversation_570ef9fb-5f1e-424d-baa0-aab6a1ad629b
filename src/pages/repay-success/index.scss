@import '../../components/weapp/index.scss';
@import '~@mu/cui/dist/style/mgm-banner.scss';

.repay-success {
  background-color: #fff;
  text-align: center;
  position: relative;
  padding: 60px 30px 30px;

  // height: 100%;

  &-content {
    margin-top: 30px;
    font-size: 40px;
    color: #808080;
    line-height: 100%;
  }

  &-sub-content {
    font-size: 28px;
    color: #888888;
    margin-bottom: 50px;
  }

  .stages-msg {
    font-size: 28px;
    color: #888888;
    text-align: center;
    line-height: 42px;
  }

  &-tips {
    margin-top: 10px;
    color: #B2B2B2;
    font-size: 28px;
  }

  &-overRepayAmt {
    margin-top: 22px;
    color: #808080;
    text-align: center;
    font-size: 28px;
    font-weight: 400;
    font-family: "PingFang SC";
    line-height: 42px;
    &_link {
      margin-left: 10px;
      color: #3477ff;
    }
  }

  &-total {
    margin-top: 30px;
    font-size: 48px;
    line-height: 1;
  }

  &-amount {
    font-size: 80px;
    font-weight: 700;
  }

  &-info {
    margin-top: 10px;
    font-size: 32px;
    line-height: 80px;
    padding-top: 30px;

    &-list {
      font-size: 32px;
      line-height: 68px;
      display: flex;
      justify-content: space-between;
      width: auto;

      &-discount {
        color: #FF8800;
      }

      &__detail {
        display: flex;
        align-items: center;

        &__icon {
          margin-left: 10px;
        }
      }
    }
  }

  &-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 50px;
    height: 36px;
    font-weight: 500;
    font-size: 36px;
    color: #3477ff;
    line-height: 36px;
    //width: 650px;
    //height: 88px;
   // margin-top: 20px !important;

    .at-button__text {
      font-size: 36px;
    }
  }

  &-btn-swan {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 50px;
    height: 36px;
    font-weight: 500;
    font-size: 36px;
    color: #3477ff;
    line-height: 36px;
    background-color: unset;
    .at-button__text {
      font-size: 36px;
    }
    &::after {
      border: unset;
    }
  }

  &-holder {
    position: fixed;
    bottom: 0;
  }

  .repay-success-info-container-new {
    // 微信小程序样式兼容
    >mu-view {
      &:first-child {
        .success-info-item-new {
          margin-top: 80px!important;
        }
      }
      .success-info-item-new {
        margin-top: 40px!important;
      }
    }
    
    .success-info-item-new {
      &:first-child {
        margin-top: 80px;
      }

      // &:last-child {
      //   padding-bottom: 0;
      // }

      display: flex;
      justify-content: space-between;
      flex-direction: row;
      //padding-bottom: 40px;
      margin-top: 40px;

      .info-item-key {
        font-weight: 400;
        font-size: 32px;
        color: #333333;
        line-height: 32px;

        .discount-info-label {
          margin-left: 10px;
          width: 140px;
          height: 40px;
          padding: 3px 10px;
          border: 1.5px solid #ff8844;
          border-radius: 4px;
          font-weight: 600;
          font-size: 24px;
          color: #FF8844;
          letter-spacing: 0;
          line-height: 24px;
          box-sizing: border-box;
        }
      }

      .info-item-value {
        font-weight: 400;
        font-size: 32px;
        color: #333333;
        text-align: right;
        line-height: 32px;

        &.info-item-price-container {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
        }

        .info-value-price {
          color: #FF8844;
        }

        .info-value-price {
          color: #FF8844;
        }

        .item-mid-arrow {
          margin-left: 10px;
        }
      }
    }

    .success-info-item-sub-new {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      //padding-bottom: 40px;
      padding-top: 40px;

      .info-item-key,
      .info-item-value {
        font-weight: 400;
        font-size: 28px;
        color: #808080;
        line-height: 28px;
      }

      .info-item-value {
        padding-right: 40px;
      }
    }
  }

  .mgm-banner {
    padding: 48px 0 0;
  }
}

.repay-success-loan-more {
  padding: 10px 30px 0;
  text-align: right;
  &.repay-success-loan-more-padding-more {
    padding-top: 20px;
  }

  .success-loan-more-tips-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;

    .success-loan-more-tips {
      margin-bottom: 16px;
      position: relative;
      // max-width: 260px;
      display: inline-block;
      padding: 6px 18px;
      font-weight: 400;
      font-size: 20px;
      color: #6F4025;
      letter-spacing: 0;
      text-align: left;
      line-height: 20px;
      background: #FFEAD3;
      border-radius: 15px;
      text-align: right;

      .tips-arrow {
        position: absolute;
        bottom: -10px;
        left: 50%;
        width: 0;
        height: 0;
        border-top: 10px solid #FFEAD3;
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
      }
    }
  }
}

.auto-repay-guide {
  margin: 54px 30px 0;
  position: relative;

  &__bubble {
    width: max-content;
    position: absolute;
    right: 0;
    top: -34px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 20px;
    color: #FFFFFF;
    font-size: 24px;
    line-height: 24px;
    font-weight: 400;
    background-color: #FF8844;
    border-radius: 0 15px 0 15px;
    z-index: 1;
  }

  &__button {
    width: 690px;
    height: 100px;
    border-radius: 8px;
  }
}

.repay-success-all {
  width: 100%;
  min-height: 100vh; // 很关键
  box-sizing: border-box;
}

.repay-success-all_176 {
  padding-bottom: 176px;
}

.repay-success-all_260 {
  padding-bottom: 260px;
  background: #f3f3f3;
}

.repay-entryRate {
  .entry-Rate {
    margin: 20px;
  }
}

.repay-success-chat {
  display: flex;
  justify-content: center;
  width: 100%;
  // margin-top: -96px;

  .chat-entry {
    display: flex;
    justify-content: center;
  }
}

.repay-success-chat_96 {
  margin-top: -96px;
}

.repay-success-chat_180 {
  margin-top: -180px;
}

.repay-success-chat_none {
  display: none;
}

.mu-subscribe-wrap {
  padding-top: 20px;
}

.at-drawer__content {
  overflow: hidden;
  border-radius: 16px 16px 0 0;
  background-color: #FFFFFF !important;
}

.bottom-height {
  margin-bottom: 100px;
}

.finger-guide-dialog {
  .mu-modal__container {
    width: 650px;

    .at-button {
      height: 100px !important;
    }
  }

  .at-button__text {
    font-size: 26px;
  }
}

.finger-guide-content {
  font-size: 26px;
}

.finger-guide-radio {
  padding-left: 12px;

  .mu-radio__title__protocal {
    font-size: 26px !important;
  }
}

.alipay-sign-dialog {
  .btn-confirm {
    margin: 0 40px;
  }
}

.common-modal {
  .content {
    padding: 20px 0;

    .rate {
      color: #FF8844;
      font-size: 56px;
      padding: 48px 0 10px;
      font-weight: 600;
    }
  }
}

.gbl-basic-lifefollow {
  .app-wrap>div {
    margin-top: 20px;
    background: #F3F3F3;
  }
}

.depreciate {
  // width: 560px !important;
  // height: 672px !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  // position: relative;
  &-cancelIcon {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 24px;
    right: 24px;
  }

  &-successIcon {
    width: 120px;
    height: 120px;
    margin-top: 10px;
    margin-bottom: 24px;
  }

  &-title {
    font-weight: 600;
    font-size: 36px;
    color: #333333;
    line-height: 36px;
  }

  &-annualRate {
    font-weight: 600;
    font-size: 48px;
    color: #FF8844;
    margin-top: 30px;
    margin-bottom: 25px;
  }

  &-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &-interestRate {
      width: 312px;
      height: 38px;
      background: #FFF8F0;
      border: 1px solid rgba(255, 136, 68, 0.5);
      border-radius: 19px;
      font-weight: 400;
      font-size: 28px;
      color: #FF8844;
      line-height: 36px;
    }
  }

  &-text {
    font-weight: 400;
    font-size: 28px;
    color: #808080;
    text-align: center;
    line-height: 42px;
    margin-top: 24px;
    margin-bottom: 40px;
  }

  &-btn {
    width: 480px;
    height: 88px;
    background: #3477FF;
    border-radius: 8px;
    font-weight: 600;
    font-size: 36px;
    color: #FFFFFF !important;
    line-height: 36px;
  }
}

.promotion {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &-successIcon {
    width: 120px;
    height: 120px;
    margin-top: 10px;
    margin-bottom: 24px;
  }

  &-title {
    font-weight: 600;
    font-size: 36px;
    color: #333333;
    line-height: 36px;
    margin-bottom: 40px;
  }

  &-text {
    font-weight: 400;
    font-size: 28px;
    color: #808080;
    text-align: center;
    line-height: 42px;
    margin-top: 24px;
  }

  &-btn {
    width: 480px;
    height: 88px;
    background: #3477FF;
    border-radius: 8px;
    font-weight: 400;
    font-size: 40px !important;
    color: #FFFFFF !important;
    line-height: 36px;
  }
}

.quick-question-wraaper {
  margin: 20px;
}

.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}

.pages-slogan {
  margin-top: -96px !important;
}

.random-reduce-dialog {
  .mu-dialog__container {
    background-image: url('https://file.mucfc.com/ebn/3/0/202306/202306191520476a905f.png');
    background-color: rgba(255, 225, 255, 0);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 560px;
    height: 648px;
    overflow: visible !important;
  }

  &__content {
    &__title {
      margin-top: 20px;
      font-size: 36px;
      line-height: 36px;
      color: #333;
      font-weight: 600;
    }

    &__desc {
      margin: 24px auto 16px;
      padding: 10px 24px;
      width: 184px;
      font-size: 34px;
      line-height: 34px;
      color: #FE334D;
      font-weight: 600;
      border: 1PX solid #FE334D;
      border-radius: 29px;
    }

    &__amount {
      font-size: 100px;
      line-height: 100px;
      color: #FE334D;
      font-weight: 600;

      &__unit {
        font-size: 40px;
        line-height: 40px;
        color: #FE334D;
        font-weight: 600;
      }
    }

    &__button {
      margin: 160px 20px 0;
    }
  }

  &__close {
    position: absolute;
    bottom: -110px;
    left: calc(50% - 30px);
    width: 60px;
    height: 60px;
  }
}