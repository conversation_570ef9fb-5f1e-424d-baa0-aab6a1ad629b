/* eslint-disable indent */
/* eslint-disable eqeqeq */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUText, MUImage, MUView, MUT<PERSON>, M<PERSON><PERSON>raw<PERSON>, MUIcon, MUButton
} from '@mu/zui';
import Madp from '@mu/madp';
import { urlDomain } from '@utils/url_config';
import MUBioMetrics from '@mu/biometrics-utils';
import { EnableBiometricsWrap } from '@mu/biometrics-shell';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { getEnv } from '@mu/madp-utils';
import PropTypes from 'prop-types';
import Dispatch from '@api/actions';
import { AgreementDrawer } from '@mu/agreement';
import faceIdImage from './img/faceId.png';
import fingerImage from './img/finger.png';
import selectImage from './img/Select.png';
import unSelectImage from './img/unSelect.png';
import './indexPay.scss';

const currentEnv = process.env.BUILD_ENV || getEnv();
export default class OpenFingerPay extends Component {
  static propTypes = {
    userId: PropTypes.string.isRequired
  }

  constructor(props) {
    super(props);
    this.state = {
      bioType: '1', // 生物类型识别，0 不支持。1指纹识别，2面容识别
      selected: false,
      toastOpened: false,
      mainOpened: false,
      contractInfo: {},
      alreadyForceRead: false,
      contractList: [],
      forceReadContractList: [],
    };
    this.contractInterfaceVersion = '';
  }

  async componentDidMount() {
    // 采用组合来判断，手续是外面传的isOpened 为false，还有本地的次数没超过2次才弹出
    window.muna.ShareDataPlugin.getStorage('openFingerPaySwitch', (result) => {
      if (result == '' || result == undefined || result == 'undefined') {
        window.muna.ShareDataPlugin.storage('openFingerPaySwitch', this.getMonthKey());
        window.muna.ShareDataPlugin.storage('openFingerPaySwitchCount', '1');
        this.setState({
          mainOpened: true,
        });
      } else if (this.getMonthKey() == result) {
        window.muna.ShareDataPlugin.getStorage('openFingerPaySwitchCount', (count) => {
          // 方便测试，更改次数限制。测试环境每次都可以弹
          if (currentEnv.indexOf('st') > -1) {
            this.setState({
              mainOpened: true,
            });
          } else if (count == '1') {
            window.muna.ShareDataPlugin.storage('openFingerPaySwitchCount', '2');
            this.setState({
              mainOpened: true,
            });
          } else {
            this.setState({
              mainOpened: false,
            });
          }
        });
      } else {
        window.muna.ShareDataPlugin.storage('openFingerPaySwitch', this.getMonthKey());
        window.muna.ShareDataPlugin.storage('openFingerPaySwitchCount', '1');
        this.setState({
          mainOpened: true,
        });
      }
    });
    this.getContractInfo();
    const { checkIsSupported } = MUBioMetrics;
    const result = await checkIsSupported(2);
    // console.log('result', JSON.stringify(result));
    this.setState({
      bioType: result.fingerOrFace
    }, () => {
      this.setTitle();
    });
  }

  getMonthKey = () => {
    const date = new Date();
    const month = date.getMonth() + 1;
    let monthKey;
    switch (month) {
      case 1:
        monthKey = 'January';
        break;
      case 2:
        monthKey = 'February';
        break;
      case 3:
        monthKey = 'March';
        break;
      case 4:
        monthKey = 'April';
        break;
      case 5:
        monthKey = 'May';
        break;
      case 6:
        monthKey = 'June';
        break;
      case 7:
        monthKey = 'July';
        break;
      case 8:
        monthKey = 'August';
        break;
      case 9:
        monthKey = 'September';
        break;
      case 10:
        monthKey = 'October';
        break;
      case 11:
        monthKey = 'November';
        break;
      case 12:
        monthKey = 'December';
        break;
      default:
        break;
    }
    return monthKey;
  }

  async getContractInfo() {
    // 查询合同配置
    const { ret, data } = await Dispatch.repayment.queryContractConfig({
      contractConfigScene: 'FINGER_AUTH',
      subSysCode: 'rbf-repayment-fe', // 方便日志定位问题
     }) || {};
    if (ret !== '0') return;
    const { contractConfigInfoList = [] } = data || {};

     // 获取合同列表
     this.getContractList(contractConfigInfoList);
  }

  getContractList = (contractConfigInfoList) => {
    const contractList = [];
    const forceReadContractList = [];
    (contractConfigInfoList || []).map(async (contract) => {
      const { contractContent, forceReadContractContent } = await this.getContractContent(contract) || {};
      contractList.push(contractContent);
      if (JSON.stringify(forceReadContractContent) !== '{}') {
        forceReadContractList.push(forceReadContractContent);
      }
    });

    this.setState({
      contractList,
      forceReadContractList
    });
  }

  getContractContent = async (contractConfigInfoList) => {
    const {
      interfaceVersion, contractCategory,
      contractVersion, contractEdition,
      contractType, contractCode,
      forceRead, forceReadTime,
      needCustSignature, needCompanySignature,
      agreementTitle, agreementSummary,
      contractName, contractCustName,
      contractCustShortName,
    } = contractConfigInfoList || {};

    this.contractInterfaceVersion = interfaceVersion;

    let contractParams = {
      contractVersion,
      contractCategory,
    };

    const contractData = {
      contractName,
      contractCustName,
      contractCustShortName,
      needCustSignature,
      needCompanySignature,
      agreementTitle,
      agreementSummary,
    };

    if (interfaceVersion === '3.0') {
      contractParams = {
        contractCode,
        contractData,
        ...contractParams,
      };
    } else {
      contractParams = {
        contractEdition,
        contractData,
        ...contractParams,
        ...(contractType ? { contractTypeList: [contractType] } : {}),
      };
    }

    const res = await Dispatch.repayment.queryContractInfo({
      scene: 'PREVIEW',
      interfaceVersion,
      ...contractParams,
    }) || {};
    const { contractList } = res && res.data;

    let forceReadContractContent = {};
    if (!!forceRead && forceRead === 'Y') {
      forceReadContractContent = {
        title: contractCustName,
        htmlFile: ((contractList || [])[0] || {}).htmlFile || '',
        forceReadTime: Number(forceReadTime || '5'),
      };
    }

    const contractContent = {
      title: contractCustName || contractName,
      htmlFile: ((contractList || [])[0] || {}).htmlFile || '',
    };

    return { forceReadContractContent, contractContent };
  }

  setTitle = () => {
    const { bioType } = this.state;
    let desc = '';
    if (bioType == '1') {
      desc = '指纹支付';
    } else if (bioType == '2') {
      desc = '面容ID支付';
    } else {
      desc = '指纹支付';
    }
    this.desc = desc;
    this.title = `开启${desc}更便捷`;
  }

  protocalAction = () => {
    const { contractList, forceReadContractList, alreadyForceRead } = this.state;
    this.setState({
      contractInfo: {
        showContract: true,
        forceReadTime: !alreadyForceRead && forceReadContractList && forceReadContractList.length > 0 ? forceReadContractList[0].forceReadTime : 0,
        list: contractList
      }
    });
  }

  selectAction = () => {
    const { selected, alreadyForceRead, forceReadContractList } = this.state;
    if (selected) {
      this.setState({
        selected: !selected
      });
    } else if (!alreadyForceRead && forceReadContractList && forceReadContractList.length > 0) {
      this.setState({
        contractInfo: {
          showContract: true,
          forceReadTime: forceReadContractList[0].forceReadTime || 0,
          list: forceReadContractList
        }
      });
    } else {
      this.setState({
        selected: !selected
      });
    }
  }

  resetPassword = () => {
    const url = urlDomain;
    const channel = Madp.getChannel();
    const resurlUrl = `${url}/${channel}/safecenter/#/member/reset-password/now-phone-check`;
    Madp.navigateTo({
      url: resurlUrl
    });
  }

  closefinger = () => {
    const { disableBiometrics } = MUBioMetrics;

    Madp.showModal({
      title: '开启失败',
      content: `${this.desc}库信息变化，指纹/面容已失效`,
      confirmText: '好的',
      cancelText: '',
      showCancel: false,
      confirmColor: '#3477ff',
      success: async (res) => {
        if (res.confirm) {
          this.setState({
            mainOpened: false,
          });
          try {
            const closeResult = await disableBiometrics(2);
            console.log(`closeResult${closeResult}`);
          } catch (e) {
            console.log('disableBiometrics报错：', e);
          }
        }
      }
    });
  }

  onConfirmButtonClickAction = () => {
    this.enableBiometricsRef.startProcess();
  }

  onCloseClick = () => {
    this.setState({
      mainOpened: false
    });
  }

  overCount = () => {
    let descTitle;
    const { bioType } = this.state;
    if (bioType == '1') {
      descTitle = '指纹';
    } else if (bioType == '2') {
      descTitle = '面容ID';
    }
    Madp.showModal({
      title: '开启失败',
      content: `${descTitle}验证错误次数已超限，请关闭本机屏幕后再重新开启`,
      confirmText: '好的',
      cancelText: '',
      showCancel: false,
      confirmColor: '#3477ff',
      success: (res) => {
        // eslint-disable-next-line no-empty
        if (res.confirm) {
        }
      }
    });
  }

  showModal = (msg) => {
    let desc = '系统繁忙，请稍后再试';
    const { bioType } = this.state;
    const title = bioType == '1' ? '指纹' : '面容';
    if (msg.errCode != '140') {
      if (msg.errCode == '105') {
        desc = `当前设备${title}存在安全风险，暂时无法使用`;
      } else if (msg.errCode == '116') {
        desc = `识别到您设备的${title}未开启，请先在本机系统“设置”中添加${title}才能使用此功能`;
      } else if (msg.errCode == '123' || msg.errCode == '124' || msg.errCode == '125') {
        desc = msg.errMsg;
      }
      Madp.showModal({
        title: '开启失败',
        content: desc,
        confirmText: '好的',
        cancelText: '',
        showCancel: false,
        confirmColor: '#3477ff',
        success: (res) => {
          if (res.confirm) {
            this.setState({
              mainOpened: false
            });
          }
        }
      });
    }
  }

  closeAgreementDrawer = () => {
    const { contractInfo } = this.state;
    this.setState({
      contractInfo: {
        ...contractInfo,
        showContract: false
      }
    });
  }

  submitAgreementDrawer = () => {
    const { contractInfo } = this.state;
    this.setState({
      contractInfo: {
        ...contractInfo,
        showContract: false
      },
      alreadyForceRead: true,
      selected: true
    });
  }

  render() {
    const { userId } = this.props;
    const {
      bioType, selected, toastOpened, successDesc, mainOpened, contractInfo
    } = this.state;
    // const { EnableBiometrics } = MUBioMetrics;
    let type;
    let desc;
    let srcImage;
    if (bioType == '1') {
      type = '指纹密码';
      desc = '指纹';
      srcImage = fingerImage;
    } else if (bioType == '2') {
      type = '面容ID';
      desc = '面容';
      srcImage = faceIdImage;
    } else {
      type = '指纹密码';
      desc = '指纹';
      srcImage = fingerImage;
    }
    const descTitle = `${type}仅对本机有效`;
    let selectImg;
    if (selected) {
      selectImg = selectImage;
    } else {
      selectImg = unSelectImage;
    }
    return (
      <MUView>
        {process.env.TARO_ENV === 'h5' && (
          <MUDrawer
            beaconId="openFingerPay"
            show={mainOpened}
            placement="bottom"
            height="486px"
            onClose={this.onCloseClick}
          >
            <MUView className="sectionPayView">
              <MUView className="top">
                <MUText className="titleDesc">{`开启${desc}支付更便捷`}</MUText>
                <MUIcon className="iconInfo" value="close2" size={18} onClick={this.onCloseClick} beaconId="closeFingerPay" />
              </MUView>
              <MUImage src={srcImage} className="image" />
              <MUText selectable="true" className="textPay">{descTitle}</MUText>
              <MUView className="sectionViewBottom">
                <MUImage beaconId="selectedImage" src={selectImg} className="imageSelect" onClick={this.selectAction} />
                <MUText beaconId="selectedText" selectable="true" className="textOne" onClick={this.selectAction}>我已阅读并同意</MUText>
                <MUText beaconId="protocalTitle" selectable="true" className="textTwo" onClick={this.protocalAction}>指纹与面容ID协议</MUText>
              </MUView>
              <MUButton beaconId="openPayBtn" type="primary" className="confirm-button" disabled={!selected} onClick={this.onConfirmButtonClickAction}>立即开启</MUButton>
            </MUView>
            <AgreementDrawer
              agreementViewProps={{
                type: 1,
                list: contractInfo.list,
                current: 0,
              }}
              show={contractInfo.showContract}
              close={this.closeAgreementDrawer}
              submit={this.submitAgreementDrawer}
              totalCount={contractInfo.forceReadTime}
              height="486px"
            />
            <MUToast
              beaconId="toast"
              isOpened={toastOpened}
              text={successDesc}
              icon="success"
              onClose={() => { this.setState({ toastOpened: false }); }}
            />
            <EnableBiometricsWrap
              onRef={(ref) => { this.enableBiometricsRef = ref; }}
              title="同意并输入6位交易密码"
              showTitleIcon
              userId={userId}
              biometricScene={2}
              contractInterfaceVersion={this.contractInterfaceVersion}
              onClickTpRightText={() => { this.resetPassword(); }}
              onError={(msg) => {
                dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'OpenFailure', target: this });
                this.showModal(msg);
              }}
              onVerifyOverNum={() => {
                this.overCount();
              }}
              onEnableOk={() => {
                dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'OpenSuccess', target: this });
                this.setState({
                  toastOpened: true,
                  successDesc: '开启成功',
                });
                setTimeout(() => {
                  this.onCloseClick();
                }, 1000);
              }}
            />
          </MUDrawer>
        )}

      </MUView>
    );
  }
}
