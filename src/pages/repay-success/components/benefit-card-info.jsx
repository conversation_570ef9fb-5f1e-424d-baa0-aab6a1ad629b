/* eslint-disable max-len */
import Madp from '@mu/madp';
import {
  MUView, MUImage
} from '@mu/zui';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { MGMBanner } from '@mu/cui';
import { isMiniProgram } from '@utils/repay-util';
import { taroHostName } from '@utils/constants';

import './benefit-card-info.scss';
const vPlusBenefitImg = 'https://file.mucfc.com/ebn/3/18/2023010/20231019103946ee9c5f.png';

export default function BenefitCardInfo({ vplusData = {}, isMgmChannel = false, parentId = '' }) {
  // V+会员信息在招行渠道不展示
  const { isVPlus } = vplusData || {};
  const isCMBAPP = Madp.getChannel() === '3CMBAPP';
  // 卡片展示逻辑调整为：V+卡片
  // v+卡片展示条件
  const isVplusShowStatus = isVPlus && !isCMBAPP;
  // v+卡片详情链接
  const VPLUS_MAIN_URL = isMiniProgram() ? `${taroHostName}scene/#/pages/vmember/center/index?titleBarColor=272A4F&needLogin=1` : `${taroHostName}scene/#/pages/vmember/center/index?titleBarColor=272A4F`;

  if (isVplusShowStatus) {
    dispatchTrackEvent({
      event: EventTypes.SO,
      beaconId: 'repayment.RepaySuccess.VplusInfoBar'
    });
  }

  return (
    <MUView>
      {isVplusShowStatus ? (
        <MUView className="bc-infobar vplus-infobar">
          <MUImage className="bc-infobar-icon" src={vPlusBenefitImg} />
          <MUView className="bc-infobar-content">
            <MUView>
              您有V+会员多项权益可使用
            </MUView>
            <MUView>
              点击查看了解更多
            </MUView>
          </MUView>

          <MUView
            className="bc-infobar-but"
            beaconId="VplusInfoClick"
            onClick={() => {
              Madp.navigateTo({
                url: VPLUS_MAIN_URL
              });
            }}
          >
            详情
          </MUView>
        </MUView>
      ) : <MUView />}
      { (isMgmChannel && isVplusShowStatus === false) ? (
        <MGMBanner parentId={parentId} />
      ) : <MUView />}
    </MUView>
  );
}
