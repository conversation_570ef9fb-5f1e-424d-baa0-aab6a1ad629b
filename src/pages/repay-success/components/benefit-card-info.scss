.bc-infobar {
  display: flex;
  width: 690px;
  height: 140px;
  background-image: linear-gradient(138deg, #FFFFFF 0%, #FFF6E5 100%);
  /* stylelint-disable-next-line */
  border: 2PX solid #F5CBA4;
  border-radius: 16px;
  margin: 48px auto;
  align-items: center;
  padding: 0 30px;
  margin-bottom: 0;
  box-sizing: border-box;

  /* stylelint-disable-next-line */
  mu-view:nth-child(2) {
    flex: 1;
  }

  &-icon {
    width: 60px;
    height: 60px;

  }

  &-content {
    flex: 1;
    text-align: center;
    font-weight: 400;
    font-size: 25px;
    color: #000000;
    font-family: PingFangSC-Regular, sans-serif;
    line-height: 1.8;

    &-price {
      color: #FF8844;
    }
  }

  &-but {
    width: 120px;
    height: 56px;
    background: #232323;
    border-radius: 30px;
    text-align: center;
    line-height: 56px;
    font-size: 26px;
    font-weight: 600;
    color: #F8D6B3;
  }

  &.vplus-infobar {
    .bc-infobar-but {
      background-image: linear-gradient(269deg, #B75704 0%, #CB8021 100%);
    }
    .bc-infobar-content {
      text-align: left;
      padding-left: 18px;
    }
  }
}
