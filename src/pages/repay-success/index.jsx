/* eslint-disable no-nested-ternary */
/* eslint-disable react/jsx-indent */
/* eslint-disable operator-linebreak */
/* eslint-disable no-empty */
/* eslint-disable indent */
/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
/* eslint-disable prefer-rest-params */
/* eslint-disable arrow-body-style */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable react/sort-comp */
/* eslint-disable prefer-destructuring */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUIcon, MUText, MUButton, MUModal, MURadio, MUImage, MUNavBarWeapp, MUSlogan, MUDialog, MURichText
} from '@mu/zui';
import {
  isAlipay,
  isMuapp,
  isIOS,
  Url,
  getCurrentPageUrl,
  isWechat,
  getWebViewName
} from '@mu/madp-utils';
import classNames from 'classnames';
import { Button } from '@tarojs/components';
// import { PublicDetainDialog } from '@mu/lui';
import { injectState, refresh } from '@mu/leda';
import { RepaymentPlugin as BusinessPlugin } from '@mu/business-plugin';
import { repaymentFn, getTemplateIds, pluginMessageHandler } from '@mu/business-plugin-utils';
import {
  getSwanTemplate, opService, getPageConf, stayWithTime, getProductAllParams, getLoginInfo
} from '@mu/business-basic';
import pageHoc from '@utils/pageHoc';
import Util, { ModalControl } from '@utils/maxin-util';
import { miniProgramChannel, taroHostName, isSwanMini, EVENT_CODE_MAP } from '@utils/constants';
import { getAvailLoanFlag } from '@utils/getLoanMoreTips';
import { isMiniProgram } from '@utils/repay-util';
import Dispatch from '@api/actions';
import { getStore, setStore } from '@api/store';
import ChannelConfig from '@config/index';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import MUBioMetrics from '@mu/biometrics-utils';
import { AgreementDrawer } from '@mu/agreement';
import AutoRepayDrawer from '@components/auto-repay-drawer/index';
import { MGMBanner } from '@mu/cui';
import { OpRepayment } from '@mu/op-comp';
import { currentEnv, urlDomain } from '@utils/url_config';
import DiversionInfoCard from '@components/diversion-info-card/index';

import './index.scss';
// 2021-5-12 zhuming增加指纹支付引导
import OpenFingerPay from './OpenFingerPay';
import BenefitCardInfo from './components/benefit-card-info';
import './components/benefit-card-info.scss';
// import SubscribeBtn from '@mu/subscribe-btn';
// import '@components/repay-modal/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/auto-repay-drawer/index.scss');
  require('@components/diversion-info-card/index.scss');
}

const { repaySuccessFn } = repaymentFn || {};
let repaySuccessPageId = 'edf4e032-cd82-43d7-9e91-1c6edd6f02eb';

if (currentEnv === 'st2') {
  repaySuccessPageId = 'eabbafbc-1277-49d9-8044-3a666392f1b3';
}

const mgmChannels = ['0APP', '2APP', '3APP', '0WEC', '1WEC', '2WEC', '0ZFB', '2ZFB', '16ZFB', '17ZFB', '0MNP', '1MNP', '0ZFBMNPJD', '3CUAPP'];

const currentChannel = Madp.getChannel();

// 提额降价弹窗用到的mapCode
const SubscribeBtn = (process.env.TARO_ENV === 'h5' && currentChannel === '3CMBAPP') ? require('@mu/subscribe-btn/dist/h5/index').default : null;

const themeColor = Util.getThemeColor(ChannelConfig.theme);

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: repaySuccessFn.getPageId() || repaySuccessPageId
    }
  }
}, {
  pageId: 'RepaySuccess',
  dispatchOnMount: true,
})
@pageHoc({ title: '还款结果' })
@injectState({
  pageId() {
    if (currentEnv === 'st2') {
      return repaySuccessPageId;
    } else {
      return repaySuccessFn.getPageId() || repaySuccessPageId;
    }
  },
  getPageConf: () => getPageConf(repaySuccessPageId, true),
  stateKeys: [...repaySuccessFn.getLedaNameList()]
})
export default class RepaySuccess extends Component {
  config = {
    // NEW-PROJECT-NEED-UPDATE
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor() {
    super(...arguments);
    this.state = {
      showContent: false, // 内容展示
      billType: '',
      resultData: {},
      showFingerGuide: false,
      noMoreFingerGuide: false,
      canShowAliContract: false,
      // showCommonModal: false,
      depreciateCount: 5,
      opendFingerPay: '', // 指纹支付状态
      disableBackBtn: false, // 是否禁用返回按钮
      // title: "打开还款消息通知",
      // template: "wJX6Bc8mn-z-45ckfCt6wZxH0Wtde03Cyifr56lRTqA"
      showRandomReduceDialog: true, // 是否展示随机立减弹窗
      showNewRepayInfoDiscount: false,
      isVPlus: false, // 是否为vplus会员
      tips: '', // 一元借/一口借引导再借一笔气泡提示
      showGuideLoanMore: false, // 一元借/一口借再借一笔按钮展示
      showAutoRepayDrawer: false, // 是否展示自动还款底部弹窗
      guideTitle: '', // 引导绑卡/支付宝按钮文案
      guideSence: '', // 引导客户场景
      templateIds: [],
      subscribeId: `id${new Date().getTime().toString(36)}${Math.random().toString(36).substr(2, 9)}`,
      contractInfo: {},
      noInteractionEvent: false, // 没有交互运营事件
      isShowCmbSub: false, // 是否展示招行签约弹窗
      diversionBillInfo: {}, // 导流账单信息
    };
    this.isMgmChannel = mgmChannels.indexOf(Madp.getChannel()) > -1;
    this.repayConfig = {};
    this.signedAlipayWithholding = ''; // 当前渠道是否支持签约支付宝代扣，Y/N
    this.commonModalInfo = {};
    this.modalControl = new ModalControl(this);
    this.isReapySuccess = Url.getParam('isReapySuccess') || ''; // 区分还款已提交页面和还款结果页面
    this.expressScene = Url.getParam('expressScene') || '';
    this.specialRepayScene = Url.getParam('specialRepayScene') || ''; // 特殊还款场景
    this.isExpress = Url.getParam('isExpress') || '';
    this.qryPra = Url.getParam('settleQuery') || '';
    if (this.qryPra) {
      this.decPra = decodeURIComponent(this.qryPra) || '';
      this.settleQuery = JSON.parse(decodeURIComponent(this.decPra) || '{}');
    } else {
      this.settleQuery = Madp.getStorageSync('settleQuery', 'LOCAL') || {};
    }
    this.contractInfoList = []; // TBS返回的支付扣款协议信息
    console.log('this.settleQuery:>>', this.settleQuery);
    this.hasShowRandomReduceDialog = false; // 标识是否已弹过随机立减弹窗，弹过就不在弹广告弹窗
    this.pageStatus = ''; // 页面状态
    this.hasTriggerOpDetainDialog = false; // 标识还款交互运营退出挽留弹窗是否触发
    this.openInteractiveSubscribe = false; // 开放交互运营订阅
    this.showFlowCardSwitch = ''; // 导流借据开放参数
  }

  async componentWillMount() {
    const { resultFromUrl } = this.getDataFormLocal();
    let resultData = {};
    if (!resultFromUrl) {
      resultData = Madp.getStorageSync('payResult', 'LOCAL') || {};
    } else {
      resultData = this.getResultData(resultFromUrl);
    }
    this.setState({
      resultData: resultData
    });
    // 获取一口借/一元借结清后引导再借的气泡内容
    // 其中custOrderStatus为"01"代表全部结清，其中custOrderStatus不为"01"且orderStatus为01代表单笔结清
    // 当前使用全部结清为判断依据
    const { isYyjOrYjkLoan } = resultData;
    const { custOrderStatus } = this.settleQuery || {};
    // 获取一口借结清引导再借气泡文案
    const data = await Dispatch.repayment.getAccount({
      queryScene: '21'
    });
    const {
      accountList, awardInfoList, diversionBillInfo,
    } = data || {};
    this.setState({
      diversionBillInfo: diversionBillInfo || {},
    });
    if (custOrderStatus === '01' && isYyjOrYjkLoan) {
      // 此处代码执行和下面的tips的this.setState拆分开是防止页面元素在showGuideLoanMore变成true时出现页面元素闪现问题
      this.setState({
        showGuideLoanMore: true,
      });
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'ShowLoanMoreBtn'
      });
      const { availLoanWaiveFlag } = getAvailLoanFlag(accountList, awardInfoList);
      this.setState({
        tips: availLoanWaiveFlag ? '借款有限时优惠，赶紧去看看~' : '',
      });
      if (availLoanWaiveFlag) dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'ShowLoanWaiveBubble'
      });
    }
    // 获取账户信息是否为plus
    await this.queryAwardAccountIsVPlus();
  }

  async getSwanId() {
    const channel = Madp.getChannel();
    const templateIds = await getTemplateIds('subscribestdSuccessClick', channel, 'repayment');
    this.setState({ templateIds });
  }

  async componentDidMount() {
    console.log('getCurrentPageUrl', getCurrentPageUrl());
    await this.initUserInfor();
    this.setHttps();
    this.checkFromAlipayUrl();
    this.setState({ showContent: true });
    this.getFingerPayStatus();
    const [
      ,
      { data }
    ] = await Promise.all([
      this.initPageConfig(),
      Dispatch.repayment.getChannelParams({
        channelParamKeyList: ['signedAlipayWithholding', 'showFlowCardSwitch'],
        paramTypeList: ['CHANNEL_PARAM']
      }),
    ]);
    this.signedAlipayWithholding = ((data || {}).channelParamMap || {}).signedAlipayWithholding || '';
    // 有导流借据且渠道部分开放导流业务，展示导流卡片
    this.showFlowCardSwitch = ((data || {}).channelParamMap || {}).showFlowCardSwitch || '';
    // 从链接中获取交易类型和交易结果
    const { billType, resultFromUrl } = this.getDataFormLocal();
    this.preRepayScene = Url.getParam('preRepayScene') || '';
    this.startLocation = Url.getParam('startLocation') || '';
    this.startState = Url.getParam('startState') || '';
    let resultData = {};
    if (!resultFromUrl) {
      resultData = Madp.getStorageSync('payResult', 'LOCAL') || {};
    } else {
      resultData = this.getResultData(resultFromUrl);
    }
    // this.initPoleStart(resultData);
    this.cancelSendMsg(resultData);
    if (this.settleQuery) console.log('you', this.settleQuery);
    if (!this.settleQuery) console.log('wu', this.settleQuery);
    let settleType = '00';
    if (this.isReapySuccess && this.settleQuery && Object.keys(this.settleQuery).length !== 0) {
      const { custOrderStatus, repaymentLogList = [] } = this.settleQuery; // 客户级结清标识custOrderStatus: '01'代表全部结清
      if (custOrderStatus === '01') {
        settleType = '01';
      }
      for (let i = 0; i < repaymentLogList.length;) {
        const { orderStatus } = repaymentLogList[i];
        if (orderStatus === '01') { // 借据状态: '01'代表借据结清，如果custOrderStatus不为01且orderStatus为01代表单笔结清；如果是其他状态就是非结清状态
          if (custOrderStatus !== '01') {
            settleType = '02';
          }
        }
        i += 1;
      }

      this.setState({
        settleType,
      });
    }

    this.setState({
      billType,
      resultData,
      actualRender: true // 增加标识，用于渠道插件中解决弹窗渲染问题（走到这里，若settleType有值在渠道插件中就能获取到）
    });

    // 上报法诉费埋点
    const hasCourtCostFlag = getStore('hasCourtCostFlag');
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: hasCourtCostFlag === 'Y' ? 'HasCourtCost' : 'NoCourtCost',
    });
    setStore({ hasCourtCostFlag: 'N' }); // 重置法诉费flag

    // 上报溢缴款埋点
    const overRepayAmt = getStore('overRepayAmt');
    if (Number(overRepayAmt) > 0) {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: 'overRepayAmt',
      });
    }

    const { transSeqno } = this.settleQuery; // 客户级结清标识custOrderStatus: '01'代表全部结清
    dispatchTrackEvent({
      event: EventTypes.EV,
      beaconId: `Repay${this.isReapySuccess ? 'SUC' : 'PROC'}`,
      target: this,
      beaconContent: {
        cus: {
          isExpress: this.isExpress === '0' ? false : !!this.expressScene,
          expressScene: this.expressScene,
          transSeqno: transSeqno || '',
        }
      },
    });

    this.initGuideInfo();

    // 还款成功，清除“银行卡余额不足而允许微信支付方式”标记
    if (this.isReapySuccess) {
      if (this.userId) {
        let idTimeObj = Madp.getStorageSync('BANKCARD_CANT_AFFORD_ALLOW_WECHAT_REPAY', 'LOCAL') || {};
        if (idTimeObj[this.userId]) {
          idTimeObj[this.userId] = '';
          Madp.setStorageSync('BANKCARD_CANT_AFFORD_ALLOW_WECHAT_REPAY', idTimeObj, 'LOCAL');
        }
      }
    }
    if (isSwanMini) {
      this.getSwanId();
    }

    this.getPageStatus(settleType, billType);
    this.handleOpPageEnterEvent();
    this.handleOpPageStayEvent();
  }

  // 交互式-进入事件
  handleOpPageEnterEvent = async () => {
    const opRes = await this.opOnPageEvent('opPageEnter', EVENT_CODE_MAP[`${this.pageStatus}Open`]);
    if (!opRes) this.noInteractionEventCallback();
  }

  // 交互运营-停留事件
  handleOpPageStayEvent = () => {
    this.stayTimer = null;
    // 增加标识，还款服务弹窗触发了就不在触发还款停留事件
    if (!this.hasTriggerOpDetainDialog && typeof stayWithTime === 'function') {
      this.stayTimer = stayWithTime(10000, async () => {
        if (this.stayTimer) {
          await this.opOnPageEvent('opPageStay', EVENT_CODE_MAP[`${this.pageStatus}Stay`]);
        }
      });
    }
  }

  async opOnPageEvent(eventName, interactionEventCode) {
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: repaySuccessPageId,
            subscribeBannerProps: {
              type: 'click',
              compClassName: 'leda-app-wrap',
              compInnerClassName: 'leda-comp-inner',
              luiTrack: {
                moduleId: 'repayment', //用于触发上报，beaconId = {moduleId}.{pageId}.{name}
                pageId: 'RepaySuccess',
              },
              name: `${this.pageStatus}Subscribe`,
              boothTitle: '订阅',
              id: 'localSubscribeBanner'
            },
            // 避免对现有事件影响，交互订阅新增回调按新增处理
            callback: (res) => {
              resolve(res);
            }
          },
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve(true);
      }
    });
  }

  // 获取页面状态
  getPageStatus = (settleType, billType) => {
    let pageStatus = '';
    const repaySence = Url.getParam('repaySence') || '';
    const isStandardRepay = ['7days', 'total'].indexOf(billType) > -1 && this.specialRepayScene !== 'consult';
    if (this.isReapySuccess && isStandardRepay) {
      switch (repaySence) {
        case 'advanceRepay':
          // 提前还款成功-全部结清、部分结清
          if (settleType === '01') {
            pageStatus = 'repaySuccessAdvanceSettle';
          } else if (settleType === '02') {
            pageStatus = 'repaySuccessAdvancePartSettle';
          } else {
            pageStatus = 'repaySuccessAdvance';
          }
          break;
        case 'normalRepay':
          // 正常还款成功-全部结清、部分结清
          pageStatus = settleType === '01' ? 'repaySuccessSettle' : 'repaySuccess';
          break;
        case 'overdueRepay':
          // 逾期还款成功-全部结清、部分结清
          pageStatus = settleType === '01' ? 'repaySuccessOverdueSettle' : 'repaySuccessOverdue';
          break;
        default:
          pageStatus = '';
          break;
      }
    } else if (!this.isReapySuccess && isStandardRepay) {
      switch (repaySence) {
        case 'advanceRepay':
          pageStatus = 'repaySuccessAdvancePending';
          break;
        case 'normalRepay':
          pageStatus = 'repaySuccessPending';
          break;
        case 'overdueRepay':
          pageStatus = 'repaySuccessOverduePending';
          break;
        default:
          pageStatus = '';
          break;
      }
    }
    this.pageStatus = pageStatus;
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: 'ShowPageStatus',
      beaconContent: { cus: { billType, settleType, isReapySuccess: this.isReapySuccess, repaySence: this.repaySence, pageStatus, expressScene: this.expressScene, specialRepayScene: this.specialRepayScene } }
    });
  }

  // 交互式运营弹窗优先级高于其他弹窗，没有交互式运营弹窗是才展示其他弹窗
  noInteractionEventCallback = () => {
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: 'NoInteractionEvent',
      beaconContent: { cus: { desc: '不满足交互式运营弹窗触发条件' } }
    });
    this.setState({ noInteractionEvent: true });
  }

  // 跳转小招荷包
  overRepayAmtLink = () => {
    Madp.reLaunch({
      url: `${urlDomain}/${Madp.getChannel()}/loan/#/pages/over-pay/index`
    });
  }

  // 初始化引导绑代扣卡或绑定支付宝代扣的信息
  initGuideInfo = async () => {
    // 仅在还款成功页进行引导
    if (!this.isReapySuccess) return;
    const { bankCardInfoList = [], signedZFBFlag, contractInfoList } = await Dispatch.repayment.getBankCardsList({ transScene: 'REPAY', transAmt: '0' }) || {};
    // 过滤出可用的代扣卡
    const withHoldBankCardInfoList = bankCardInfoList.filter((item) => item.cardType === '1' && item.status === '0');
    // 1. 既没有绑代扣卡又没有绑定支付宝代扣
    if (withHoldBankCardInfoList.length < 1 && signedZFBFlag === 'N') {
      // 场景0：支付宝渠道由客户选择绑定代扣卡或签约支付宝代扣
      if (isAlipay() || process.env.TARO_ENV === 'alipay') {
        await this.getContractList();
        this.contractInfoList = contractInfoList;
        this.setState({
          guideTitle: '开通自动还款',
          guideSence: '0'
        });
        dispatchTrackEvent({
          target: this,
          event: EventTypes.SO,
          beaconId: 'AutoRepayEntry',
          beaconContent: { cus: { type: '01', text: '开通自动还款' } }
        });
        return;
      }
      // 场景1：非支付宝渠道直接引导绑卡
      this.setState({
        guideTitle: '开通银行卡自动还款',
        guideSence: '1'
      });
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'AutoRepayEntry',
        beaconContent: { cus: { type: '02', text: '开通银行卡自动还款' } }
      });
      return;
    }
    // 2. 签约支付宝代扣但没有绑代扣卡
    if (withHoldBankCardInfoList.length < 1 && signedZFBFlag === 'Y') {
      // 场景2：直接引导绑卡
      this.setState({
        guideTitle: '开通银行卡自动还款',
        guideSence: '2'
      });
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'AutoRepayEntry',
        beaconContent: { cus: { type: '03', text: '开通银行卡自动还款' } }
      });
      return;
    }
    // 3. 绑定银行卡但没有签约支付宝代扣
    if (withHoldBankCardInfoList.length > 0 && signedZFBFlag === 'N') {
      // 场景3：当前渠道支持签约支付宝代扣，才引导绑定支付宝
      if (this.signedAlipayWithholding === 'Y') {
        await this.getContractList();
        // 引导签约支付宝代扣
        this.setState({
          guideTitle: '开通支付宝自动还款',
          guideSence: '3'
        });
        dispatchTrackEvent({
          target: this,
          event: EventTypes.SO,
          beaconId: 'AutoRepayEntry',
          beaconContent: { cus: { type: '04', text: '开通支付宝自动还款' } }
        });
      }
    }
  }

  // 获取是否为vplus账户
  async queryAwardAccountIsVPlus() {
    const { awardAccountInfo } = await Dispatch.repayment.queryAwardAccount() || {};
    if (awardAccountInfo) {
      const { accountStatus, awardAccountLevel } = awardAccountInfo;
      if (accountStatus === 1 && awardAccountLevel === 9) {
        this.setState({ isVPlus: true });
      }
    }
  }

  // 测试环境交易中台给的回跳地址有可能是http链接
  setHttps() {
    if (process.env.TARO_ENV !== 'h5' || !window) return;
    const currentUrl = window.location.href;
    if (currentUrl.indexOf('https://') === -1) {
      Util.router.replace(currentUrl.replace('http://', 'https://'));
    }
  }

  componentWillUnmount() {
    // 中断发起的停留交互运营
    if (this.stayTimer) {
      this.stayTimer = null;
    }
  }

  queryInteractiveSubscribeResult = async () => {
    let interactiveSubscribeChannelList = '';
    if (getProductAllParams && typeof getProductAllParams === 'function') {
      ({ interactiveSubscribeChannelList } = await getProductAllParams('HK.HK01') || {});
    }
    this.openInteractiveSubscribe = (interactiveSubscribeChannelList || '').indexOf('ALL') > -1 || (interactiveSubscribeChannelList || '').indexOf(Madp.getChannel()) > -1;
  }

  async ledaDidMount() {
    // 订阅初始化
    await this.queryInteractiveSubscribeResult();
    if (!this.openInteractiveSubscribe) {
      // 进入页面时激活订阅功能
      const subscribestdSuccessCode = 'subscribestdSuccess';
      this.actionSubscribeHandler(subscribestdSuccessCode);
      refresh({
        pageId: repaySuccessPageId,
        isRequest: false,
      });
    }
  }

  // 交易中台接口回跳地址直接到结果页。需要重定向到倒计时页面
  // 地址接口返回的api是mucfc.repayment.normalRepay.alipayH5Callback
  checkFromAlipayUrl() {
    if (process.env.TARO_ENV !== 'h5' || !window) return;
    // 链接中获取判断标识
    const alipayUrlSign = Number(Url.getParam('alipayUrl')) === 1;
    if (alipayUrlSign) {
      Util.router.replace({
        path: '/pages/repay-countdown/index',
        query: { counterSeconds: '5', alipayUrl: '1' },
      });
    }
  }

  getDataFormLocal() {
    // console.log('href :>> ', window.location.href);
    const fromAlipayUrl = Number(Url.getParam('alipayUrlAfterRediret')) === 1;
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: 'FromAlipayUrl',
      beaconContent: { cus: fromAlipayUrl }
    });
    let billType = Url.getParam('type') || 'total';
    let resultFromUrl = Url.getParam('result');
    if (fromAlipayUrl) {
      // 因为支付宝跳转支付后会清缓存，无奈之下便写进缓存
      const payResult = Madp.getStorageSync('payResult', 'LOCAL');
      // console.log('payResult 22:>> ', payResult);
      billType = payResult.billType || 'total';
      resultFromUrl = payResult.result;
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: 'FromAlipayUrlPayResult',
        beaconContent: { cus: payResult }
      });
    }
    return { billType, resultFromUrl };
  }

  async getFingerPayStatus() {
    if (process.env.TARO_ENV === 'h5' && isMuapp() && isIOS() && Util.isAppSupport()) {
      const { checkIsEnabled } = MUBioMetrics;
      try {
        const isEnabled = await checkIsEnabled(2);
        this.setState({
          opendFingerPay: isEnabled,
        });
      } catch (error) {
      }
    }
  }

  async initUserInfor() {
    // 多接口用到用户id，存储在this中
    const { basicCustDto } = getStore('sessionInfo') || {};
    let { userId } = basicCustDto || {};
    if (!userId) {
      const { basicCustDto: newBasicCustDto } = await Dispatch.repayment.getNewUserInfo();
      const { userId: newUserId } = newBasicCustDto || {};
      userId = newUserId || '';
    }
    this.userId = userId;
  }


  cancelSendMsg(data) {
    const { transSeqno } = data;
    if (!this.isReapySuccess) return;
    // console.log('todo 取消短信发送接口调用 :>> ');
    Dispatch.repayment.cancelSendMsg({
      cancelSendDelayMessageReq: transSeqno
    });
  }

  async beforeRouteLeave(from, to, next) {
    // 在途停留事件取消
    if (this.stayTimer) {
      this.stayTimer = null;
    }
    Madp.setStorageSync('resultPageJumpOut', 'Y', 'SESSION');
    if (this.hasTriggerOpDetainDialog) {
      next(true);
    } else if (['repaySuccessAdvanceSettle', 'repaySuccessAdvance', 'repaySuccessSettle', 'repaySuccess'].includes(this.pageStatus) && ['/pages/index/index', '/index', '/pages/bill-list-near/index', '/pages/bill-list-all/index'].includes(to.path)) {
      this.hasTriggerOpDetainDialog = true;
      if (await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP[`${this.pageStatus}Exit`])) { // 预埋：击中运营点情况需要在运营组件内部处理返回首页
        next(false);
        return;
      }
      next(true);
    } else if (repaySuccessFn.beforeRouteLeaveHandler) {
      repaySuccessFn.beforeRouteLeaveHandler(from, to, next, this.state);
    } else {
      next(true);
    }
  }

  beforeMiniRouteLeave = async () => {
    // 在途停留事件取消
    if (this.stayTimer) {
      this.stayTimer = null;
    }
    if (this.hasTriggerOpDetainDialog) {
      Madp.reLaunch({ url: '/pages/index/index' });
    } else if (['repaySuccessAdvanceSettle', 'repaySuccessSettle', 'repaySuccess'].includes(this.pageStatus)) {
      this.hasTriggerOpDetainDialog = true;
      if (!await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP[`${this.pageStatus}Exit`])) { // 预埋：击中运营点情况需要在运营组件内部处理返回首页
        Madp.reLaunch({ url: '/pages/index/index' });
      }
    } else if (repaySuccessFn.beforeRouteLeaveHandler && this.expressScene !== '21' && this.expressScene !== '22' && this.expressScene !== '23' && this.expressScene !== '24' && this.expressScene !== '25') {
      repaySuccessFn.beforeRouteLeaveHandler();
    } else {
      Madp.navigateBack();
    }
  }

  async initPageConfig() {
    this.repayConfig = await Dispatch.repayment.getCommonConfig('repaySuccess.config') || {};
  }

  checkFingerSet() {
    if (!window.localStorage.getItem('noAlertSetFingerPay')) { // 指纹支付判断
      return new Promise((resolve, reject) => {
        window.muapp.LocalBiometricPlugin.getSupport((result) => {
          // 还款结果页查询是否支持开通指纹支付
          if (result.scenes && result.scenes.includes && result.scenes.includes(2)) { // 支持开通指纹支付
            window.muapp.LocalBiometricPlugin.isOpen(2, (res) => {
              // 还款结果页查询是否已开通指纹支付
              if (res && !res.open) {
                // 未开启指纹功能
                resolve({ support: true, open: false });
              } else {
                resolve({ support: true, open: true });
              }
            });
          } else {
            // 不支持开通
            resolve({ support: false, open: false });
          }
        }, () => {
          // 还款结果页查询是否有开启指纹支付进入错误回调
          reject({ support: false, open: false });
        });
      });
    } else {
      return Promise.resolve({ support: false, open: false });
    }
  }

  getResultData(resultFromUrl) {
    if (!resultFromUrl) return {};
    if (resultFromUrl && typeof resultFromUrl === 'object') {
      return resultFromUrl;
    }
    let result = decodeURIComponent(resultFromUrl);
    if (process.env.TARO_ENV !== 'h5') {
      result = decodeURIComponent(result);
    }
    let resultData = {};
    try {
      resultData = JSON.parse(result || '{}');
    } catch (error) {
      resultData = JSON.parse(resultFromUrl || '{}');
    }
    return resultData;
  }

  actionSubscribeHandler(scene, callback, tmplIds = []) {
    const { virtualIdCommon, subscribeId } = this.state || {};
    let param = {};
    if (isSwanMini) {
      param = {
        virtualIdCommon,
        scene,
        tmplIds,
        subscribeId
      };
      repaySuccessFn.subscribeHandler(param, callback, this);
    } else {
      pluginMessageHandler({
        businessName: 'repayment',
        pageType: 'repaySuccess',
        scene,
        data: {}, // 需要传额外数据
        success: () => {
        },
        fail: () => {
        },
        complete: () => {
          callback && callback();
        }
      });
    }
  }

  /**
   * 百度编译问题，Button如果只有bindsubscribe，点击会显示不存在onBackBtnClick函数
   * 所以需要给Button加个onClick，这样点击一次会调用两次本函数，用e.type区分
   */
  onBackBtnClickSwan(e) {
    if (!isSwanMini) {
      return;
    }
    if (isSwanMini && e.type === 'tap') {
      return;
    }
    const { disableBackBtn, templateIds } = this.state;
    if (!disableBackBtn) {
      const tmplIds = getSwanTemplate(e, templateIds) || [];// 后端模版
      // 点击之后返回按钮不可用
      this.setState({
        disableBackBtn: true
      });

      const callBackFn = () => {
        // 事件完成之后，返回按钮变为可用
        this.setState({
          disableBackBtn: false
        });
        if (this.preRepayScene) {
          this.jumpOrClose();
        } else {
          this.jumpToIndex();
        }
        this.getSwanId();
      };
      this.actionSubscribeHandler('subscribestdSuccessClick', callBackFn, tmplIds);
    }
  }

  async onBackBtnClick() {
    const { disableBackBtn } = this.state;

    if (!disableBackBtn) {
      // 点击之后返回按钮不可用
      this.setState({
        disableBackBtn: true
      });

      const callBackFn = () => {
        // 事件完成之后，返回按钮变为可用
        this.setState({
          disableBackBtn: false
        });
        if (this.preRepayScene) {
          this.jumpOrClose();
        } else {
          this.jumpToIndex();
        }
      };
      if (currentChannel === '0HWYFW' || currentChannel === '3WYDAPP') return callBackFn(); // 鸿蒙元服务、沃易贷APP跳过订阅
      if (!this.openInteractiveSubscribe) {
        this.actionSubscribeHandler('subscribestdSuccessClick', callBackFn);
      } else {
        await this.opOnPageEvent('opBackClick', EVENT_CODE_MAP[`${this.pageStatus}Click`]);
        callBackFn();
      }
    }
  }

  jumpToIndex() {
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'GoBackHome' });
    const ret = Util.checkFinishAction();
    let url = '/pages/index/index';
    const redirectFirstPage = getStore('redirectFirstPage');
    if (redirectFirstPage && redirectFirstPage !== '') {
      url = redirectFirstPage;
      setStore({ redirectFirstPage: '' });
      Util.router.replace(url);
    } else if (ret.close || (this.repayConfig.closeAfterFinish && this.repayConfig.closeAfterFinish.indexOf(Madp.getChannel()) > -1)) {
      Madp.closeWebView();
    } else if (ret.redirect || (this.repayConfig.closeJump && this.repayConfig.closeJump[Madp.getChannel()])) {
      this.toJump(ret.redirect || this.repayConfig.closeJump[Madp.getChannel()]);
    } else if (ChannelConfig.closeAfterFinish) {
      Madp.closeWebView();
    } else if (currentChannel === '3WYDAPP') { // 沃易贷入口固定返回还款首页
      Util.router.replace(url);
    } else if (Madp.getChannel().indexOf('APP') > -1) {
      Madp.closeWebView();
    } else if (miniProgramChannel.indexOf(Madp.getChannel()) > -1) { // 小程序跳转链接处理
      Madp.reLaunch({ url });
    } else if (isWechat() || isAlipay() || isSwanMini || (['0JRTTXCX', '0DY1XCX', '0DY2XCX', '0DY3XCX'].indexOf(currentChannel) > -1)) { // 小程序跳转链接处理
      Madp.reLaunch({ url });
    } else if (currentChannel === '0HWYFW') {
      const Backlen = window.history.length;
      if (Backlen > 1) window.history.go((-Backlen + 1));
      else Madp.closeWebView().then().catch(() => {
        Util.router.replace('/pages/index/index');
      });
    } else {
      const Backlen = window.history.length;
      window.history.go((-Backlen + 1));
    }
  }

  jumpOrClose() {
    const imUrl = process.env.TARO_ENV === 'h5'
      ? (sessionStorage.getItem('CSP_CHAT_URL') || '')
      : (Madp.getStorageSync('CSP_CHAT_URL', 'SESSION') || '');
    if (this.preRepayScene) {
      if (window.history.length < 3 || !this.startLocation || (Number(this.startState) === 0 && Number(this.startLocation) === 1) || (!imUrl && this.preRepayScene === '4')) { // 用户直接进的承接页
        Madp.closeWebView();
      } else if (this.preRepayScene === '4') {
        Madp.reLaunch({ url: imUrl || `${urlDomain}/${Madp.getChannel()}/csp/#/pages/chat/index` });
      } else if (miniProgramChannel.indexOf(Madp.getChannel()) > -1) {
        Madp.redirectTo({ url: `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/bargain/index?_needLogin=1&fromResult=1` });
      } else {
        Madp.reLaunch({ url: `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/bargain/index` });
      }
    }
  }

  toJump(url) {
    if (url) {
      if (url.indexOf('http') > -1) {
        if (process.env.TARO_ENV === 'h5') {
          Madp.redirectTo({ url });
        } else {
          Madp.navigateTo({ url });
        }
      } else {
        Util.router.push(url);
      }
    }
  }

  formatter(val) {
    let formattedDate = val;
    const dateString = val.toString();
    if (!val) {
      formattedDate = '';
    } else if (/\d{4}\.\d{2}\.\d{2}/.test(dateString)) {
      const yy = dateString.split('.')[0];
      const mm = dateString.split('.')[1];
      const dd = dateString.split('.')[2];
      formattedDate = `${mm}月${dd}日`;
      if (Number(yy) !== (new Date().getFullYear())) {
        formattedDate = `${yy}年${mm}月${dd}日`;
      }
    } else if (dateString.length === 8 && (/^\d+$/).test(dateString)) {
      const yy = String(dateString).slice(0, 4);
      const mm = String(dateString).slice(4, 6);
      const dd = String(dateString).slice(6, 8);
      formattedDate = `${mm}月${dd}日`;
      if (Number(yy) !== (new Date().getFullYear())) {
        formattedDate = `${yy}年${mm}月${dd}日`;
      }
    }
    return formattedDate;
  }

  toOpenFinger() {
    const { noMoreFingerGuide } = this.state;
    if (noMoreFingerGuide) {
      window.localStorage.setItem('noAlertSetFingerPay', 1);
      // Madp.setStorageSync('noAlertSetFingerPay', 1, 'LOCAL');
    }
    this.modalControl.quit('showFingerGuide');
    // this.setState({ showFingerGuide: false });
    //跳转指纹支付设置
    const host = urlDomain;
    const channel = Madp.getChannel();
    const params = '/safecenter/#/finger-pay-set';
    const result = `${urlDomain}/${channel}${params}`;
    Madp.redirectTo({url: result});
  }

  cancelFinger() {
    const { noMoreFingerGuide } = this.state;
    if (noMoreFingerGuide) {
      window.localStorage.setItem('noAlertSetFingerPay', 1);
    }
    this.modalControl.hide('showFingerGuide');
  }

  protocalAction = async () => {
    const { contractInfo } = this.state;
    this.setState({
      contractInfo: {
        ...contractInfo,
        showContract: true,
      }
    });
  }

  getContractList = async () => {
    const { contractInfo } = this.state;
    if (contractInfo && contractInfo.list && contractInfo.list.length) return;
    // tbs没法区分新旧合同，现在先从ubs拿合同配置，等全部切3.0后再直接从tbs取（this.contractInfoList）
    const { ret, data } = await Dispatch.repayment.queryContractInfo({
      scene: 'CONFIG',
      contractConfScene: 'ADD_ALIPAY_ACCOUNT',
      interfaceVersion: '3.0'
    }) || {};
    if (ret !== '0') return;
    const { contractConfigInfoList = [] } = data || {};
    const contractList = [];
    let contractText = '';
    (contractConfigInfoList || []).map(async (contract) => {
      const { interfaceVersion, contractCustShortName, contractName } = contract || {};
      if (interfaceVersion === '3.0') {
        contractText = contractText ? `${contractText}、${contractCustShortName}` : contractCustShortName;
      } else {
        contractText = contractText ? `${contractText}、${contractName}` : contractName;
      }
      const contractContent = await this.getContractContent(contract) || {};
      contractList.push(contractContent);
    });

    this.setState({
      contractInfo: {
        contractText,
        list: contractList,
      }
    });
  }

  getContractContent = async (contract) => {
    const {
      interfaceVersion, contractCategory,
      contractVersion, contractEdition,
      contractType, contractCode,
      needCustSignature, needCompanySignature,
      agreementTitle, agreementSummary,
      contractName, contractCustName,
      contractCustShortName,
    } = contract || {};

    let contractParams = {
      contractVersion,
      contractCategory,
    };

    const result = await getLoginInfo();
    const { custName, idNo } = result || {};
    const date = new Date();
    const [year, month, day] = [date.getFullYear(), date.getMonth() + 1, date.getDate()];

    const contractData = {
      contractName,
      contractCustName,
      contractCustShortName,
      needCustSignature,
      needCompanySignature,
      agreementTitle,
      agreementSummary,
      // 支付扣款协议银行名称参考卡管理页面写死...
      bankName: 'XXXXXXXXX',
      accountName: custName,
      accountNo: '*****XXXX',
      name: custName,
      certId: idNo,
      yearNow: year,
      monthNow: month,
      dayNow: day,
    };

    if (interfaceVersion === '3.0') {
      contractParams = {
        contractCode,
        contractPreviewData: {
          baseContractInfo: {
            name: custName,
            certId: idNo,
            signDate: `${year}${`0${month}`.slice(-2)}${`0${day}`.slice(-2)}`,
          },
          bankCardInfo: {
            bankName: 'XXXXXXXXX',
            accountName: custName,
            accountNo: '*****XXXX',
          },
          needCustSignature,
          needCompanySignature,
        },
        ...contractParams,
      };
    } else {
      contractParams = {
        contractEdition,
        contractData,
        ...contractParams,
        ...(contractType ? { contractTypeList: [contractType] } : {}),
      };
    }

    const res = await Dispatch.repayment.queryContractInfo({
      scene: 'PREVIEW',
      interfaceVersion,
      ...contractParams,
    }) || {};
    const { contractList } = (res && res.data) || {};

    const contractContent = {
      title: contractCustName || contractName,
      htmlFile: ((contractList || [])[0] || {}).htmlFile || '',
    };

    return contractContent;
  }

  get fingerGuideContent() {
    if (process.env.TARO_ENV === 'h5') {
      const { noMoreFingerGuide } = this.state;
      return (
        <MUView>
          <MUView className="finger-guide-content">推荐打开指纹支付，可享受更快捷还款体验哦~</MUView>
          <MURadio
            className="finger-guide-radio"
            beaconId="FingerGuideNoMore"
            type="protocal"
            value={noMoreFingerGuide ? 'noMoreFingerGuide' : ''}
            options={[{ type: 'protocal', labelLeft: '不再提示', value: 'noMoreFingerGuide' }]}
            onClick={() => this.setState({ noMoreFingerGuide: !noMoreFingerGuide })}
          />
        </MUView>
      );
    }
    return <MUView />;
  }

  // 获取新版本成功页总优惠金额
  getNewTotalDiscount() {
    const { resultData } = this.state || {};
    const { totalAwardAmt, settleWaiveAmt, waiveTotalAmt, randomReduceFlag } = resultData || {};
    // 修复用券多次提交偶发随机立减标志错误更新导致结果页展示了重复优惠的问题
    const randomReduceAmt = (randomReduceFlag === 'Y' && (!totalAwardAmt || Number(totalAwardAmt) === 0)) ? waiveTotalAmt : '';
    let total = 0;

    // 优惠券金额
    if (totalAwardAmt) {
      total = Util.floatAdd(Number(total), Number(totalAwardAmt)).toFixed(2);
    }
    // 超限减免金额
    if (settleWaiveAmt) {
      total = Util.floatAdd(Number(total), Number(settleWaiveAmt)).toFixed(2);
    }
    // 随机立减金额
    if (randomReduceAmt) {
      total = Util.floatAdd(Number(total), Number(randomReduceAmt)).toFixed(2);
    }

    return total;
  }

  toggleShowNewRepayInfoDiscount = () => {
    const { showNewRepayInfoDiscount } = this.state || {};
    this.setState({
      showNewRepayInfoDiscount: !showNewRepayInfoDiscount
    });
  }

  // 一口借结清后引导再借跳转
  guideToLoan() {
    const LOAN_URL = isMiniProgram() ? '/loan/pages/index/index?cashLoanMode=1' : `${taroHostName}loan/#/pages/index/index?cashLoanMode=1`;

    Madp.navigateTo({
      url: LOAN_URL
    });
  }

  onGuideButtonClicked = () => {
    const { guideSence } = this.state;
    switch (guideSence) {
      case '0':
      case '3':
        this.setState({ showAutoRepayDrawer: true });
        break;
      case '1':
      case '2':
        this.guideBindBank();
        break;
      default:
        console.log('引导场景不正确');
        break;
    }
  }

  guideBindBank = () => {
    const redirectUrl = `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/index/index`;
    const miniRedirectUrl = process.env.TARO_ENV === 'weapp' ? '%2Ftabbar%2Fpages%2Findex%2Findex' : '%2Fpages%2Findex%2Findex';
    Madp.navigateTo({
      url: isMiniProgram()
        ? `/usercenter/pages/bankcard/AddOrVerifyBankCard?action=repayment&redirectUrl=${decodeURIComponent(miniRedirectUrl)}`
        : `${urlDomain}/${Madp.getChannel()}/usercenter/#/bankcard/bind-card?action=repayment&redirectUrl=${encodeURIComponent(redirectUrl)}`
    });
  }

  guideBindAliapy = () => {
    const redirectUrl = `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/index/index`;
    const miniRedirectUrl = process.env.TARO_ENV === 'weapp' ? '%2Ftabbar%2Fpages%2Findex%2Findex' : '%2Fpages%2Findex%2Findex';
    Madp.navigateTo({
      url: isMiniProgram()
        ? `/usercenter/pages/bankcard/AddOrVerifyBankCard?_bankCardPageType=6&redirectUrl=${decodeURIComponent(miniRedirectUrl)}`
        : `${urlDomain}/${Madp.getChannel()}/usercenter/#/pages/bankcard/AddOrVerifyBankCard?_bankCardPageType=6&redirectUrl=${encodeURIComponent(redirectUrl)}`
    });
  }

  closeAgreementDrawer = () => {
    const { contractInfo } = this.state;
    this.setState({
      contractInfo: {
        ...contractInfo,
        showContract: false
      }
    });
  }

  render() {
    const {
      showContent,
      billType,
      resultData,
      showFingerGuide,
      opendFingerPay,
      showRandomReduceDialog,
      showNewRepayInfoDiscount,
      isVPlus, // 是否为vplus会员
      showGuideLoanMore,
      tips,
      showAutoRepayDrawer,
      guideTitle,
      guideSence,
      templateIds,
      subscribeId,
      contractInfo,
      noInteractionEvent,
      isShowCmbSub,
      diversionBillInfo,
    } = this.state;
    const overRepayAmt = getStore('overRepayAmt');
    const { trackedBeaconId } = this.props;
    const { settleWaiveAmt, waiveTotalAmt, randomReduceFlag, totalAwardAmt } = resultData || {};
    const randomReduceAmt = randomReduceFlag === 'Y' ? waiveTotalAmt : '';
    const canShowRandomReduce = noInteractionEvent && randomReduceFlag === 'Y' && waiveTotalAmt && this.repayConfig.minRandomReduceAmt && (Number(waiveTotalAmt || 0) >= Number(this.repayConfig.minRandomReduceAmt || 0));

    // 从虚拟展示获取问券id
    // const virtualIdCommonId = virtualIdCommon && virtualIdCommon.dataObj && virtualIdCommon.dataObj.id;
    const resultInfo = {
      successText: this.isReapySuccess ? '还款成功' : '还款已提交',
      successSubText: '',
      btnText: this.preRepayScene ? '返回' : '回到首页',
    };
    let normalResultInfoTips = false;
    if (billType === 'total' || billType === '7days') {
      normalResultInfoTips = true;
    }
    let fingerView = null;
    if (noInteractionEvent && process.env.TARO_ENV === 'h5' && isMuapp() && isIOS() && Util.isAppSupport()) {
      if (opendFingerPay === '') {
        fingerView = <MUView />;
      } else if (opendFingerPay === false) {
        fingerView = <OpenFingerPay userId={this.userId} />;
      }
    }

    const isShow = (process.env.TARO_ENV === 'h5' && resultData.installTotalCnt) ||
      (process.env.TARO_ENV === 'h5' && resultData.repayAmt) ||
      (process.env.TARO_ENV === 'h5' && resultData.repayDay) ||
      (this.isReapySuccess && resultData.totalAwardAmt && Number(resultData.totalAwardAmt) > 0) ||
      (this.isReapySuccess && resultData.settleWaiveAmt && Number(resultData.settleWaiveAmt) > 0);

    const businessPluginHeaderData = {
      stateData: this.state,
      isReapySuccess: this.isReapySuccess,
      resultData,
      // 弹窗优先级：交互式运营弹窗 > 随机立减弹窗 > 广告弹窗
      canShowAdModel: noInteractionEvent && !(canShowRandomReduce && showRandomReduceDialog) && !this.hasShowRandomReduceDialog
    };

    const businessPluginMiddleData = {
      stateData: this.state,
      repaySuccessPageId,
      isReapySuccess: this.isReapySuccess,
    };

    const channelInc = ['0APP', '2APP', '3APP', '3CMBAPP'];
    const chatFlag = !channelInc.includes(Madp.getChannel());
    const sloganFlag = this.isReapySuccess;
    // 获取优惠的总金额
    const newTotalDiscount = this.getNewTotalDiscount() || 0;
    // 导流卡片开放
    const showDiversionInfoCard = this.showFlowCardSwitch === 'PART_OPEN' && (Number((diversionBillInfo || {}).diversionDueBillAmt || 0) > 0);

    return showContent ? (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="还款结果"
          leftArea={[
            {
              type: 'icon',
              value: 'back',
              onClick: this.beforeMiniRouteLeave
            }
          ]}
        />
        <MUView className="pages-bg">
          <MUView
            className={classNames({
              'repay-success-all': true,
              'repay-success-all_176': !(chatFlag && sloganFlag),
              'repay-success-all_260': chatFlag && sloganFlag,
            })}
          >
            <MUView className="repay-success">
              <MUIcon value="success" size="65" className={'repay-success-icon'} />
              <MUView className="repay-success-content">{resultInfo.successText}</MUView>
              {resultInfo.successSubText ? <MUView className="repay-success-sub-content">{resultInfo.successSubText}</MUView> : null}
              {billType !== 'extend' && billType !== 'sxf' && (
                <MUView className="repay-success-abstract">
                  {resultData.actualAmt ? (
                    <MUView className="repay-success-total">
                      ￥
                      <MUText className="repay-success-amount">{(this.expressScene !== '31' && billType === 'fee-reduce') ? Util.floatMinus(resultData.actualAmt || 0, newTotalDiscount || 0).toFixed(2) : resultData.actualAmt}</MUText>
                    </MUView>
                  ) : ''}
                  {Number(overRepayAmt) > 0 ? (
                    <MUView className="repay-success-overRepayAmt">有{overRepayAmt}元退还至小招荷包溢缴款<MUText className="repay-success-overRepayAmt_link" beaconId="goOverRepayAmt" onClick={this.overRepayAmtLink}>点击查看</MUText></MUView>
                  ) : null}
                  {(normalResultInfoTips || (this.expressScene !== '31' && billType === 'fee-reduce')) && !this.isReapySuccess ? (
                    <MUView className="repay-success-tips" style="white-space:pre-wrap"><MUView>具体还款结果以通知短信为准<MUView />每一次按约还款，都将为您的信用增值</MUView></MUView>
                  ) : null}
                </MUView>
              )}
              {billType === 'extend' && (resultData && resultData.firstRepayDate) && (
                <MUView className="stages-msg">
                  <MUText>{`首次还款日为${this.formatter(+resultData.firstRepayDate)}`}</MUText>
                </MUView>
              )}
              {/* 新版本还款尾页金额信息展示 */}
              <MUView className="repay-success-info-container-new">
                {(this.isReapySuccess && newTotalDiscount && newTotalDiscount > 0) ? (<MUView
                  className="success-info-item-new"
                  onClick={() => {
                    this.toggleShowNewRepayInfoDiscount();
                  }}
                >
                  <MUView className="info-item-key">
                    <MUText>优惠总金额</MUText>
                    {((settleWaiveAmt && Number(settleWaiveAmt) > 0) || (randomReduceAmt && Number(randomReduceAmt) > 0)) ?
                      <MUText className="discount-info-label">含随机立减</MUText> :
                      null}
                  </MUView>
                  <MUView className="info-item-value info-item-price-container">
                    <MURichText className="info-value-price" nodes={`<span style="color:#FF8844;">-${newTotalDiscount}</span>元`} />
                    <MUIcon className="item-mid-arrow" color="#cacaca" value={showNewRepayInfoDiscount ? 'arrow-up' : 'arrow-right'} size={15} />
                  </MUView>
                </MUView>) : null}

                {(this.isReapySuccess && ((settleWaiveAmt && Number(settleWaiveAmt) > 0) || (randomReduceAmt && Number(randomReduceAmt) > 0) || (totalAwardAmt && Number(totalAwardAmt) > 0)) && showNewRepayInfoDiscount) ? (<MUView>
                  {(settleWaiveAmt && Number(settleWaiveAmt) > 0) ? <MUView className="success-info-item-sub-new">
                    <MUView className="info-item-key">
                      <MUText>随机立减</MUText>
                    </MUView>
                    <MUView className="info-item-value">
                      <MUText>-{settleWaiveAmt}元</MUText>
                    </MUView>
                  </MUView> : null}

                  {(randomReduceAmt && Number(randomReduceAmt) > 0) ? <MUView className="success-info-item-sub-new">
                    <MUView className="info-item-key">
                      <MUText>随机立减</MUText>
                    </MUView>
                    <MUView className="info-item-value">
                      <MUText>-{randomReduceAmt}元</MUText>
                    </MUView>
                  </MUView> : null}
                  {(totalAwardAmt && Number(totalAwardAmt) > 0) ? <MUView className="success-info-item-sub-new">
                    <MUView className="info-item-key">
                      <MUText>还款优惠券</MUText>
                    </MUView>
                    <MUView className="info-item-value">
                      <MUText>-{totalAwardAmt}元</MUText>
                    </MUView>
                  </MUView> : null}
                </MUView>) : null}

                {(!this.isReapySuccess && newTotalDiscount && newTotalDiscount > 0 && this.expressScene !== '31' && billType === 'fee-reduce') ? (<MUView
                  className="success-info-item-new"
                  onClick={() => {
                    this.toggleShowNewRepayInfoDiscount();
                  }}
                >
                  <MUView className="info-item-key">
                    <MUText>优惠总金额</MUText>
                    {((settleWaiveAmt && Number(settleWaiveAmt) > 0) || (randomReduceAmt && Number(randomReduceAmt) > 0)) ?
                      <MUText className="discount-info-label">含随机立减</MUText> :
                      null}
                  </MUView>
                  <MUView className="info-item-value info-item-price-container">
                    <MURichText className="info-value-price" nodes={`<span style="color:#FF8844;">-${newTotalDiscount}</span>元`} />
                    <MUIcon className="item-mid-arrow" color="#cacaca" value={showNewRepayInfoDiscount ? 'arrow-up' : 'arrow-right'} size={15} />
                  </MUView>
                </MUView>) : null}

                {(!this.isReapySuccess && ((settleWaiveAmt && Number(settleWaiveAmt) > 0) || (randomReduceAmt && Number(randomReduceAmt) > 0) || (totalAwardAmt && Number(totalAwardAmt) > 0)) && showNewRepayInfoDiscount && this.expressScene !== '31' && billType === 'fee-reduce') ? (<MUView>
                  {(settleWaiveAmt && Number(settleWaiveAmt) > 0) ? <MUView className="success-info-item-sub-new">
                    <MUView className="info-item-key">
                      <MUText>随机立减</MUText>
                    </MUView>
                    <MUView className="info-item-value">
                      <MUText>-{settleWaiveAmt}元</MUText>
                    </MUView>
                  </MUView> : null}

                  {(randomReduceAmt && Number(randomReduceAmt) > 0) ? <MUView className="success-info-item-sub-new">
                    <MUView className="info-item-key">
                      <MUText>随机立减</MUText>
                    </MUView>
                    <MUView className="info-item-value">
                      <MUText>-{randomReduceAmt}元</MUText>
                    </MUView>
                  </MUView> : null}
                  {(totalAwardAmt && Number(totalAwardAmt) > 0) ? <MUView className="success-info-item-sub-new">
                    <MUView className="info-item-key">
                      <MUText>还款优惠券</MUText>
                    </MUView>
                    <MUView className="info-item-value">
                      <MUText>-{totalAwardAmt}元</MUText>
                    </MUView>
                  </MUView> : null}
                </MUView>) : null}
                {/* {this.isReapySuccess && overPayAmt && Number(overPayAmt) > 0 ?
                  <MUView className="success-info-item-new">
                    <MUText className="info-item-key">小招荷包抵扣</MUText>
                    <MUText className="info-item-value">
                      -{overPayAmt}元
                    </MUText>
                  </MUView> : null} */}
                {/* {(this.isReapySuccess && actualRepayTotalAmt && Number(actualRepayTotalAmt) > 0 && newTotalDiscount && newTotalDiscount > 0) ? <MUView className="success-info-item-new">
                  <MUText className="info-item-key">实际支付金额</MUText>
                  <MUText className="info-item-value">
                    {actualRepayTotalAmt}元
                  </MUText>
                </MUView> : null} */}
                {(this.expressScene !== '31' && billType === 'fee-reduce' && Number(resultData.actualAmt || 0)) ? (<MUView className="success-info-item-new">
                  <MUText className="info-item-key">还款金额</MUText>
                  <MUText className="info-item-value">
                    {resultData.actualAmt}元
                  </MUText>
                </MUView>) : null}
                {resultData && resultData.installTotalCnt && isShow && process.env.TARO_ENV === 'h5' ? (
                  <MUView className="success-info-item-new">
                    <MUText className="info-item-key">分期数</MUText>
                    <MUText className="info-item-value">
                      {resultData.installTotalCnt}期
                    </MUText>
                  </MUView>
                ) : null}
                {resultData && resultData.repayAmt && isShow && process.env.TARO_ENV === 'h5' ? (
                  <MUView className="success-info-item-new">
                    <MUText className="info-item-key">每期应还</MUText>
                    <MUText className="info-item-value">
                      {resultData.repayAmt}元
                    </MUText>
                  </MUView>
                ) : null}
                {resultData && resultData.repayDay && isShow && process.env.TARO_ENV === 'h5' ? (
                  <MUView className="success-info-item-new">
                    <MUText className="info-item-key">还款日</MUText>
                    <MUText className="info-item-value">
                      每月{resultData.repayDay}日
                    </MUText>
                  </MUView>
                ) : null}
                {resultData && resultData.repayDay && isShow && process.env.TARO_ENV === 'h5' ? (
                  <MUView className="success-info-item-new">
                    <MUText className="info-item-key">还款方式</MUText>
                    <MUText className="info-item-value">
                      本金均摊，按期付费
                    </MUText>
                  </MUView>
                ) : null}
              </MUView>

              {!showGuideLoanMore && !isSwanMini && (['0JRTTXCX', '0DY1XCX', '0DY2XCX', '0DY3XCX', '0WAP1', '0HWYFW', '3WYDAPP'].indexOf(currentChannel) < 0) && billType !== 'fee-reduce'
                ? <BenefitCardInfo vplusData={{ isVPlus }} isMgmChannel={this.isMgmChannel} parentId={trackedBeaconId} />
                : billType !== 'fee-reduce' && this.isMgmChannel && <MGMBanner parentId={trackedBeaconId} />}
              {/* <MUButton
                type="secondary"
                className="repay-success-btn"
                onClick={this.onBackBtnClick.bind(this)}
                beaconId="StepBack"
              >
                {resultInfo.btnText}
              </MUButton> */}
            </MUView>
            {showGuideLoanMore && !isSwanMini && getWebViewName() !== 'ks' && (['0JRTTXCX', '0DY1XCX', '0DY2XCX', '0DY3XCX', '0WAP1', '0HWYFW', '3WYDAPP'].indexOf(currentChannel) < 0)
              ? <MUView className={`repay-success-loan-more ${tips ? ''
                : 'repay-success-loan-more-padding-more'}`}
              >
                {tips ? <MUView className="success-loan-more-tips-container">
                  <MUView className="success-loan-more-tips">
                    {tips ? <MURichText nodes={tips} /> : null}
                    <MUView className="tips-arrow" />
                  </MUView>
                </MUView> : null}
                <MUButton type="primary" beaconId="LoanMoreBtn" onClick={this.guideToLoan.bind(this)}>再借一笔</MUButton>
              </MUView> : null}
            {/*  针对招行渠道接入招呼组件，展示了招呼就不展示引导绑卡*/}
            {currentChannel === '3CMBAPP' && !showGuideLoanMore &&
              (<SubscribeBtn
                trackedBeaconId={`${trackedBeaconId}${this.isReapySuccess ? '.success' : '.submit'}`}
                onShowBtn={(isShowCmbSub) => {
                  this.setState({ isShowCmbSub: isShowCmbSub })
                }} />)}
            {/* 引导绑卡或签约支付宝代扣的按钮 */}
            {
              !showGuideLoanMore && guideSence && !isShowCmbSub && (
                <MUView className="auto-repay-guide">
                  <MUView className="auto-repay-guide__bubble">还款日自动还款，安全又省心</MUView>
                  <MUButton type="primary" beaconId="AutoRapayEntryClick" className="auto-repay-guide__button" onClick={() => this.onGuideButtonClicked()}>{guideTitle}</MUButton>
                </MUView>
              )
            }

            {billType !== 'fee-reduce' ? (<BusinessPlugin
              moduleName="repayment"
              pageType="repaySuccess"
              type="header"
              data={businessPluginHeaderData}
            />) : null}
            <MUView className="gbl-basic-hitEggs" />
            <MUView className="gbl-basic-nineboxLuckdraw" />
            <MUView className="gbl-basic-pickReward" />
            {showDiversionInfoCard ? (
              <DiversionInfoCard 
                diversionInfo={diversionBillInfo}
                themeColor={themeColor}
                trackPage="repayment.RepaySuccess."
              />
            ) : null}
            {/* 展位 合集 */}
            {billType !== 'fee-reduce' ? (<BusinessPlugin moduleName="repayment" pageType="repaySuccess" type="middle" data={businessPluginMiddleData} ledaPageId={repaySuccessPageId} />) : null}
            {
              isSwanMini ? (
                <Button
                  className="repay-success-btn-swan"
                  beaconId="StepBack"
                  key={resultInfo.btnText}
                  onClick={this.onBackBtnClickSwan.bind(this)}
                  bindsubscribe="onBackBtnClickSwan"
                  openType="subscribe"
                  template-id={templateIds}
                  subscribe-id={subscribeId}
                >
                  <MUText className="loan-result-page__content__message text-color">{resultInfo.btnText}</MUText>
                </Button>
              ) : (
                <MUView
                  className="repay-success-btn"
                  beaconId="StepBack"
                  key={resultInfo.btnText}
                  onClick={this.onBackBtnClick.bind(this)}
                >
                  <MUText className="loan-result-page__content__message text-color">{resultInfo.btnText}</MUText>
                </MUView>
              )
            }

          </MUView>
          {billType !== 'fee-reduce' ? (
            <MUView
              className={classNames({
                'repay-success-chat_96': chatFlag && !sloganFlag,
                'repay-success-chat_180': chatFlag && sloganFlag,
                'repay-success-chat_none': !chatFlag,
              })}
            >
              <BusinessPlugin moduleName="repayment" pageType="repaySuccess" type="footer" />
            </MUView>
          ) : null}
          {sloganFlag ? (<MUSlogan onlyLogo className={`${(!chatFlag && sloganFlag) ? 'pages-slogan' : ''}`} />) : null}
          {/* 展位 */}
          <AutoRepayDrawer
            onlyBindAlipay={guideSence === '3'}
            showAutoRepayDrawer={showAutoRepayDrawer}
            agreeBindBank={this.guideBindBank}
            agreeBindAlipay={this.guideBindAliapy}
            cancelBind={() => this.setState({ showAutoRepayDrawer: false })}
            viewContract={this.protocalAction}
            contractInfo={contractInfo}
          />
          {contractInfo && contractInfo.list && contractInfo.list.length ? (
            <AgreementDrawer
              agreementViewProps={{
                type: 1,
                list: contractInfo.list,
                current: 0
              }}
              show={contractInfo.showContract}
              totalCount={0}
              close={this.closeAgreementDrawer}
              submit={this.guideBindAliapy}
              height="67%"
            />
          ) : null}
          <MUModal
            className="finger-guide-dialog"
            beaconId="FingerGuide"
            isOpened={showFingerGuide}
            closeOnClickOverlay={false}
            title="温馨提示"
            content={this.fingerGuideContent}
            confirmText="立即打开"
            cancelText="以后再说"
            onConfirm={() => this.toOpenFinger()}
            onCancel={() => this.cancelFinger()}
          />
          {fingerView || null}
          <MUDialog
            isOpened={canShowRandomReduce && showRandomReduceDialog}
            className="random-reduce-dialog"
            beaconId="RandomReduceDialog"
            beaconContent={{
              cus: {
                randomReduceAmt: resultData ? resultData.waiveTotalAmt : ''
              }
            }}
          >
            <MUView className="random-reduce-dialog__content">
              <MUView className="random-reduce-dialog__content__title">本次还款已为您</MUView>
              <MUView className="random-reduce-dialog__content__desc">随机立减</MUView>
              <MUView className="random-reduce-dialog__content__amount">
                {resultData ? resultData.waiveTotalAmt : ''}
                <MUText className="random-reduce-dialog__content__amount__unit">元</MUText>
              </MUView>
              <MUView className="random-reduce-dialog__content__button">
                <MUButton
                  customStyle="background-image: linear-gradient(270deg, #FFDBAE 0%, #FFEAC9 100%, #FFA16D 100%); box-shadow: 0 6px 6px 0 #DE2F44; border-radius: 44px; color: #FF314E;"
                  beaconId="RandomReduceDialogConfrim"
                  onClick={() => this.setState({ showRandomReduceDialog: false }, () => { this.hasShowRandomReduceDialog = true; })}
                >
                  我知道了
                </MUButton>
              </MUView>
            </MUView>
            <MUIcon
              className="random-reduce-dialog__close"
              value="close-plain"
              size={30}
              color="#ffffff"
              beaconId="RandomReduceDialogClose"
              onClick={() => this.setState({ showRandomReduceDialog: false }, () => { this.hasShowRandomReduceDialog = true; })}
            />
          </MUDialog>
          {/* 插槽：订阅事件 */}
          <BusinessPlugin businessName="repayment" pageType="repaySuccess" loc="bottom" />
          {/* 交互式运营组件 */}
          <OpRepayment pageId={repaySuccessPageId} opEventKey="opPageEnter" />
          <OpRepayment pageId={repaySuccessPageId} opEventKey="opPageLeave" />
          <OpRepayment pageId={repaySuccessPageId} opEventKey="opPageStay" />
          <OpRepayment pageId={repaySuccessPageId} opEventKey="opBackClick" />
        </MUView>
      </MUView>
    ) : <MUView>
      <MUNavBarWeapp
        className="loan-navbar"
        title="还款结果"
        leftArea={[
          {
            type: 'icon',
            value: 'back',
            onClick: this.beforeMiniRouteLeave
          }
        ]}
      />
    </MUView>;
  }
}
