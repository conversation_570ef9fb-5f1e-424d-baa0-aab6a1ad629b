/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUImage
} from '@mu/zui';
import Madp from '@mu/madp';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import { getStore } from '@api/store';
import Util from '@utils/maxin-util';
import CustomConfig from '@config/index';
import pageHoc from '@utils/pageHoc';
import { Url } from '@mu/madp-utils';

import './list.scss';

const themeColor = Util.getThemeColor(CustomConfig.theme);
const mark = 'https://file.mucfc.com/ebn/3/0/202401/2024010511322006c69a.png';

@track({ event: EventTypes.PO }, {
  pageId: 'HomeList',
  dispatchOnMount: true,
})
@pageHoc({ title: '更多服务' })
export default class HomeList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      repayServiceTitle: '',
      repayServiceList: [],
      loanBusinessTitle: '',
      loanBusinessList: []
    };
    this.havePrepayFeeRightsCoupon = this.havePrepayFeeRightsCoupon.bind(this);
  }

  componentDidMount() {
    this.initEvent();
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '更多服务' });
  }

  config = {
    navigationBarTitleText: '更多服务'
  }

  // 初始化事件 获取初始化数据
  // eslint-disable-next-line react/sort-comp
  async initEvent() {
    const repayServiceColumnsParam = getStore('repayServiceColumnsParam') || {};
    const {
      repayServiceTitle, repayServiceColumnsList, loanBusinessTitle
    } = repayServiceColumnsParam;
    this.setState({
      repayServiceTitle,
      repayServiceList: repayServiceColumnsList ? repayServiceColumnsList.filter((item) => (item.businessCategory === 'repayService' && item.uniqueName !== 'More')) : [],
      loanBusinessTitle,
      loanBusinessList: repayServiceColumnsList ? repayServiceColumnsList.filter((item) => (item.businessCategory === 'loanBusiness' && item.uniqueName !== 'More')) : [],
    }, () => {
      const { repayServiceList, loanBusinessList } = this.state;
      const homeListTracks = repayServiceList.concat(loanBusinessList);
      if (homeListTracks && homeListTracks.length > 0) {
        homeListTracks.forEach((homeItem) => {
          dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: homeItem ? `${homeItem.uniqueName}Show` : '' });
        });
      }
    });
  }

  havePrepayFeeRightsCoupon() {
    return Url.getParam('havePrepayFeeRightsCoupon') === '1';
  }

  handleServiceJump = (item) => {
    const { uniqueName, targetUrl } = item || {};
    const custStatus = Url.getParam('custStatus');
    let finalTargetUrl = targetUrl;
    if (uniqueName === 'BillAdvancedStage') {
      finalTargetUrl = finalTargetUrl.indexOf('?') > -1 ? `${targetUrl}&mtago=31025.01.10` : `${targetUrl}?mtago=31025.01.10`;
    } else if (uniqueName === 'BillExtend') {
      finalTargetUrl = finalTargetUrl.indexOf('?') > -1 ? `${targetUrl}&mtago=31025.01.02` : `${targetUrl}?mtago=31025.01.02`;
    } else if (uniqueName === 'BillListAll') {
      finalTargetUrl = finalTargetUrl.indexOf('?') > -1 ? `${targetUrl}&custStatus=${custStatus}` : `${targetUrl}?custStatus=${custStatus}`;
    }
    Util.configUrlJump(finalTargetUrl);
  }

  getIconView(item) {
    return (
      <MUView
        className="home-list__list__item"
        beaconId={`${item.uniqueName}Click`}
        onClick={() => this.handleServiceJump(item)}
      >
        {this.havePrepayFeeRightsCoupon() && item.uniqueName === 'BillListAll' && (<MUImage className="home-list__list__item__mark" src={mark} />)}
        {item.imgUrl && (<MUImage className="home-list__list__item__icon" src={themeColor === '#E60027' ? item.unicomImgUrl : item.imgUrl} />)}
        {item.title && <MUView className="home-list__list__item__title">{item.title.substring(0, 10)}</MUView>}
      </MUView>
    );
  }

  getHomeListView(homeItem) {
    return (
      <MUView>
        {(homeItem && homeItem.serviceList && homeItem.serviceList.length > 0) ? (
          <MUView>
            <MUView className="home-list__title">{homeItem.serviceTitle}</MUView>
            <MUView className="home-list__list">
              {homeItem.serviceList.map((item, index) => (
                this.getIconView(item)
              ))}
            </MUView>
          </MUView>
        ) : null}
      </MUView>
    );
  }

  // 解决下个页面有挽留弹窗点击不能一次退出的问题
  beforeRouteLeave(from, to, next) {
    if (from.path === to.path) {
      Madp.navigateBack();
      next(true);
    } else {
      next(true);
    }
  }

  render() {
    const {
      repayServiceTitle, repayServiceList, loanBusinessTitle, loanBusinessList
    } = this.state;
    if ((!repayServiceList || repayServiceList.length <= 0) && (!loanBusinessList || loanBusinessList.length <= 0)) {
      return <MUView />;
    }
    const homeListData = [
      { serviceTitle: repayServiceTitle, serviceList: repayServiceList },
      { serviceTitle: loanBusinessTitle, serviceList: loanBusinessList }
    ];
    return (
      <MUView className="pages-bg">
        <MUView style={`min-height: ${CustomConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
          <MUView className="home-list">
            {homeListData.map(
              (homeItem) => this.getHomeListView(homeItem)
            )}
          </MUView>
        </MUView>
      </MUView>
    );
  }
}
