/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUButton, MUImage
} from '@mu/zui';
import { Url } from '@mu/madp-utils';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { urlDomain } from '@mu/business-basic';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import { miniProgramChannel, StandardService } from '@utils/constants';
import REFUSE_TOP from '@components/assets/img/refuse_top.png';

import './index.scss';

const channel = Madp.getChannel();
const isMiniProgramChannel = miniProgramChannel.indexOf(channel) > -1;

const SERVICE_CONFIG = {
  [StandardService.ConsultRepayApply]: {
    title: '协商还',
    statusConfig: {
      1: {
        title: '您有生效中的协商还计划',
        statusTips: '您已办理过协商还服务，请前往还款首页查看每月还款金额，按约还款',
        mainBtn: '查看每月还款金额',
        mainBtnLink: isMiniProgramChannel ? '/repayment/pages/index/index?showRepayPlan=1' : '/pages/index/index?showRepayPlan=1',
        subBtn: '返回首页',
        subBtnLink: isMiniProgramChannel ? '/repayment/pages/index/index' : '/pages/index/index',
      },
      2: {
        title: '抱歉，您暂不符合办理条件',
        statusTips: '您的借据不满足协商还服务办理条件，如有其他需咨询的，可联系贷后客服协商',
        mainBtn: '联系贷后客服',
        mainBtnLink: `${urlDomain}/${Madp.getChannel()}/csp/#/pages/chat/index?busiEntrance=HKSYWLTC&needLogin=1&useTitle=贷后客服`,
        subBtn: '返回首页',
        subBtnLink: isMiniProgramChannel ? '/repayment/pages/index/index' : '/pages/index/index',
      },
      3: {
        title: '抱歉，您暂不符合办理条件',
        statusTips: '综合评估暂未通过，建议及时还款保持良好个人信用',
        mainBtn: '联系贷后客服',
        mainBtnLink: `${urlDomain}/${Madp.getChannel()}/csp/#/pages/chat/index?busiEntrance=HKSYWLTC&needLogin=1&useTitle=贷后客服`,
        subBtn: '返回首页',
        subBtnLink: isMiniProgramChannel ? '/repayment/pages/index/index' : '/pages/index/index',
      }
    }
  },
  default: {
    title: '服务申请',
    statusConfig: {
      default: {
        title: '抱歉，您暂不符合办理条件',
        statusTips: '请稍后再试，如有疑问可联系贷后客服',
        mainBtn: '联系贷后客服',
        mainBtnLink: `${urlDomain}/${Madp.getChannel()}/csp/#/pages/chat/index?needLogin=1&useTitle=贷后客服`, // 无入口码
        subBtn: '返回首页',
        subBtnLink: isMiniProgramChannel ? '/repayment/pages/index/index' : '/pages/index/index',
      }
    }
  }
};

const service = Url.getParam('serviceType') || '';
const errCode = Url.getParam('errCode') || '';
const serviceConfig = SERVICE_CONFIG[service] || SERVICE_CONFIG['default'];
const serviceTitle = serviceConfig.title || '服务申请';

@track({ event: EventTypes.PO }, {
  pageId: 'StandardServiceRefuse',
  dispatchOnMount: true,
})
@pageHoc({ title: serviceTitle })
export default class ServiceRefuse extends Component {
  config = {
    navigationBarTitleText: serviceTitle,
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: serviceTitle });
  }

  async componentWillMount() {
    const status = Url.getParam('status');
    const statusConfigData = (serviceConfig.statusConfig || {})[status] || (serviceConfig.statusConfig || {}).default || {};
    this.setState({
      statusCofig: statusConfigData
    }, () => {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'StandardServiceRefuse',
        beaconContent: {
          cus: {
            rejectReason: errCode,
            service
          }
        }
      });
    });
  }


  // 主按钮点击
  mainBtnHandler = () => {
    const { statusCofig } = this.state;
    Madp.redirectTo({
      url: statusCofig.mainBtnLink,
    });
  }

  // 副按钮点击
  subBtnHandler = () => {
    const { statusCofig } = this.state;
    Madp.redirectTo({
      url: statusCofig.subBtnLink,
    });
  }

  render() {
    const status = Url.getParam('status');
    const { statusCofig } = this.state;
    const { title, statusTips, mainBtn = '返回' } = statusCofig || {};

    return (<MUView className="consult-refuse">
      <MUImage className="consult-refuse-bg" style={{ top: channel === '3CMBAPP' ? '45px' : 0 }} src={REFUSE_TOP} />
      <MUView className="consult-refuse-top">
        <MUView className="consult-refuse-top__title">{title}</MUView>
        <MUView className="consult-refuse-top__content">
          <MUView className="consult-refuse-top__content--desc">{statusTips}</MUView>
          <MUButton
            className="consult-refuse-top__content--btn"
            beaconId="JumpToExpressRepay"
            beaconContent={{ cus: { status, service, rejectReason: errCode } }}
            onClick={this.mainBtnHandler}
          >{mainBtn}
          </MUButton>
        </MUView>
      </MUView>
      <MUView className="consult-refuse-back" beaconId="JumpToRepayIndex" beaconContent={{ cus: { status, service, rejectReason: errCode } }} onClick={this.subBtnHandler}>返回首页</MUView>
    </MUView>);
  }
}
