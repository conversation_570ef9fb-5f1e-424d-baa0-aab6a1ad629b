/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import {
  MUView, MUIcon, MURichText, MUImage, MUButton, MUSlogan,
} from '@mu/zui';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import Util from '@utils/maxin-util';
import pageHoc from '@utils/pageHoc';
import { getLoginInfo } from '@mu/business-basic';
import Dispatch from '@api/actions';
import {
  isMuapp,
  Url,
} from '@mu/madp-utils';
import Madp from '@mu/madp';
import { miniProgramChannel , chatPageUrl} from '@utils/constants';
import promiseResultFail from './imgs/promise-result-fail.png';
import ShowPromiseModal from './components/ShowPromiseModal';
import './result.scss';


const supportCalendarTip = isMuapp() && (Madp.getChannel() === '0APP' || Madp.getChannel() === '2APP');

@track({ event: EventTypes.PO }, {
  pageId: 'RepayRromiseResult',
  dispatchOnMount: true,
})
@pageHoc({ title: '还款承诺办理' })
export default class RepayRromiseResult extends Component {
  config = {
    navigationBarTitleText: '还款承诺办理'
  }

  constructor(props) {
    super(props);

    this.state = {
      resultInfo: {},
      isShowAmtModal: false,
    };
    this.repayAmount = '';//还款待还金额
    this.repayCouponAmt = '';//最高可省金额
  }

  async  componentDidMount() {
    const loginInfo = await getLoginInfo(); 
    const { userIdHash, custIdHash } = loginInfo || {};
    this.loginInfoHash = { userHashNo: userIdHash, custHashNo: custIdHash };
    this.applyNo = Url.getParam('applyNo');
    await this.queryCustCaseDetail(); 
    const resultInfo = this.initData();
    this.setState({
      resultInfo,
    });
  }

  async queryCustCaseDetail() {
    const { ret, data } = await Dispatch.repayment.postLoanQueryCaseDetail({
      ...this.loginInfoHash,
      applyNo: this.applyNo,
    });
    const { applyStatus, promiseRepayDate, repayAmt,caseAwardDetail } = data || {};
    const { expireDate, awardAmt, awardNo} = caseAwardDetail || {};
    if (ret === '0' && applyStatus === '210') {
      this.status = "1";
      this.repayAmount = repayAmt ;
      this.repayCouponAmt = awardAmt ;
      let isShowCoupon = false;
      if (awardNo && awardNo.length && awardAmt&& awardAmt.length){
        isShowCoupon = true;
      }
      //返回标准日期格式
      if (promiseRepayDate && promiseRepayDate.length === 8){
        this.calendarDate = `${promiseRepayDate.slice(0, 4)}-${promiseRepayDate.slice(4, 6)}-${promiseRepayDate.slice(6, 8)} 09:00:00`;//转化成日历需要的时间格式
      }
      
      this.applyDate = this.getTimeFormat(promiseRepayDate,false);
      this.applyDateAll = this.timestampToDate(expireDate);
      //这里面判断是否展示券弹窗
      const resultInfo = this.initData();
      this.setState({
        resultInfo,
        isShowAmtModal: isShowCoupon,
      });
    } else {
      this.status = '2';
      const resultInfo = this.initData();
      this.setState({
        resultInfo
      });
    }

  }

  timestampToDate(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}`+"年"+`${month}`+"月"+`${day}`+"日";
}
  initData = () => {
    let resultInfo = {};
    if (this.status === '1') {
      resultInfo = {
        title: '办理成功',
        nodesContent: `已承诺<span style="color: #FF890E;">${this.applyDate}</span>前还款<span style="color: #FF890E;">${this.repayAmount}</span>元，请按约还款，期间暂缓催收。缓催期间仍会收取利息/分期手续费、罚金、违约金等`,
        btnText: supportCalendarTip ? '添加日历提醒' : '返回首页',
      };
      return resultInfo;
    }
    if (this.status === '2') {
      resultInfo = {
        title: '办理失败',
        nodesContent: '很抱歉，您暂不符合办理条件，请继续累积信用',
        btnText: '联系贷后客服',
      };
      return resultInfo;
    }
    return resultInfo;
  }
  //导航跳转
  btnFailureAction = () => {
    Madp.navigateTo({
      url: chatPageUrl
    });
  }

    //成功跳转
    btnSuccessAction = () => {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: 'RepayPromiseAddCalendar'
      });
      if (supportCalendarTip) {
        const urlParam = `title=招联金融-承诺还款提醒&activeBeginTime=${this.calendarDate}&description=您承诺今日还款，请按约到招联金融还清逾期金额，今日未还清全部逾期金额缓催失效。立即还款mucf.cc/bt7Ee`;
        const calendarUrl = `muapp://app/addCalendar?${urlParam}`;
        window.muapp.PagerJumpPlugin.route(calendarUrl);
      } else {
        this.btnToMainAction();
      }
    }
  //失败跳转
  btnToMainAction = () => {

    const isMiniProgramChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
    if (isMiniProgramChannel) {
      Madp.miniProgram.reLaunch({
        url: '/repayment/pages/index/index',
      });
    } else {
      Util.router.replace({
        path: '/pages/index/index',
      });
    }
  }

  getTimeFormat = (timeStr,showAll) => {
    if (timeStr && timeStr.length === 8) {
      const year = timeStr.substring(0, 4);
      const month = timeStr.substring(4, 6);
      const day = timeStr.substring(6, 8);
      const showAllResult = year + "年" + month + "月" + day + "日";
      const showReault = month + "月" + day + "日";
      if (showAll) {
        return showAllResult;
      }
      return showReault;
    }
    return "";
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '还款承诺办理' });
  }

  render() {
    const { resultInfo ,isShowAmtModal} = this.state;
    const { status } = this;
    if (Object.keys(resultInfo || {}).length <= 0) {
      return <MUView />;
    }

    const {
      title, nodesContent, btnText,
    } = resultInfo;
    
    return (
      <MUView className="pages-bg">
        <MUView className="promise-result">
          {status === '1' ? (
            <MUView className="promise-result__status-picture">
              <MUIcon value="success" size="60" />
            </MUView>
          ) : (
            <MUView className="promise-result__status-picture">
              <MUImage src={promiseResultFail} />
            </MUView>
          )}
          <MUView className="promise-result__status-title">{title}</MUView>
          <MUView className="promise-result__desc">
            <MURichText nodes={nodesContent} />
          </MUView>
          {status === '1' ? (
            <MUView className="promise-result__buttons">
              <MUButton
                beaconId="promiseSuccessAction"
                type="primary"
                circle
                onClick={this.btnSuccessAction.bind(this)}
              >{btnText}</MUButton>
              {supportCalendarTip ? (
                <MUView
                  className="promise-result__buttons__back brand-text"
                  onClick={this.btnToMainAction.bind(this)}
                >返回首页</MUView>
              ) : null}
            </MUView>
          ) : (
            <MUView className="promise-result__buttons">
              <MUButton
                beaconId="promiseFailureAction"
                type="primary"
                circle
                onClick={this.btnFailureAction.bind(this)}
              >{btnText}</MUButton>
                <MUView
                  beaconId="promiseResultBackClick"
                  className="promise-result__buttons__back brand-text"
                  onClick={this.btnToMainAction.bind(this)}
                >返回首页</MUView>
            </MUView>
          )}
          <MUView className="promise-result__logo">
            <MUSlogan onlyLogo />
          </MUView>

        </MUView>
        <ShowPromiseModal amtExpiryTime={this.applyDateAll} amtAcount={this.repayCouponAmt} isOpen={isShowAmtModal} beaconId="promiseResultModal"/>
      </MUView>
    );
  }
}
