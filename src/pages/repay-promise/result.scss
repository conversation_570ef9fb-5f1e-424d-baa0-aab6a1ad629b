.promise-result {
  overflow: hidden;
  position: relative;
  min-height: 100vh;
  background: #fff;
  &__status-picture {
    margin: 60px auto 40px;
    width: 120px;
    height: 120px;
    // overflow: hidden;
    // font-size: 0;
    .taro-img {
      width: 100%;
      height: 100%;
    }
  }
  &__status-title {
    font-size: 40px;
    line-height: 60px;
    font-weight: 600;
    color: #333;
    text-align: center;
  }
  &__desc {
    margin: 30px 40px 0;
    font-size: 28px;
    line-height: 42px;
    font-weight: 400;
    color: #888;
    text-align: center;
  }
  &__buttons {
    margin: 48px 40px 0;
    &__back {
      margin-top: 50px;
      font-size: 36px;
      line-height: 54px;
      font-weight: 600;
      text-align: center;
    }
  }
  &__logo {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 48px;
  }
}