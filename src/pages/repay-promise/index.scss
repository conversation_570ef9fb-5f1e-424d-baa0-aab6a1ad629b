@import '../../components/weapp/index.scss';

.repay-promise {
  background: #fff;
  &__content {
    overflow: hidden;
    .content-header {
      padding: 44px 0 24px 40px;
      &__title {
        font-size: 40px;
        line-height: 48px;
        font-weight: 600;
        color: #333;
      }
      &__desc {
        padding-top: 16px;
        font-size: 32px;
        line-height: 48px;
        font-weight: 400;
        color: #333;
      }
    }
    .content-topic {
      width: 750px;
      height: 728px;
      background: url(https://file.mucfc.com/zlh/3/18/202504/20250417105809233542.png) no-repeat;
      background-size: 100% 100%;
      overflow: hidden;
      position: relative;
      &--confirm {
        margin-top: 35px;
      }
      &__first-selected-line {
        display: flex;
        align-items: center;
        margin: 136px 65px 0 80px;
        font-size: 28px;
        line-height: 70px;
        font-weight: 400;
        color: #333;
        text-indent: 45px;//首行缩进
      }
      &__first-line, .content-topic__second-line, .content-topic__third-line, .content-topic__sign {
        margin: 29px 40px 0 80px;
        font-size: 28px;
        line-height: 48px;
        font-weight: 400;
        color: #333;
      }
      &__first-line {
        margin: 136px 0 0 138px;
        display: flex;
        align-items: center;
        &--confirm {
          margin: 140px 0 0 138px;
        }
      }
      &__reason {
        margin: 0 8px;
        padding: 5px 16px;
        width: 368px;
        height: 42px;
        border: 1PX solid #a6a6a6;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .reason-text {
          font-size: 28px;
          line-height: 42px;
          font-weight: 400;
          color: #A6A6A6;
          &--selected {
            color: #333;
          }
        }
        .reason-icon {
          width: 26px;
          height: 15px;
          margin-bottom: 8px;
          font-size: 0;
          .taro-img {
            width: 100%;
            height: 100%;
            overflow: visible;
          }
        }
      }
      .content-topic__second-line {
        margin: 25px 0 0 80px;
        &--confirm {
          margin: 29px 0 0 80px;
        }
        &__special {
          padding: 0 16px;
        }
      }
      .content-topic__sign {
        margin: 29px 89px 0 0;
        text-align: right;
        &--confirm {
          margin: 106px 89px 0 0;
        }
      }
      .content-topic__seal {
        width: 234.6px;
        height: 177.8px;
        position: absolute;
        top: 414px;
        left: 110px;
      }
    }
    .content-handle {
      margin: 30px 40px;

      .protocol-container {
        margin-left: 0;
      }
    }
    .content-selector {
      .at-action-sheet__container {
        max-height: none;
      }
    }
    .content-buttons {
      padding-top: 45px;
      position: relative;
      &__to-repay {
        margin: 0 40px 44px;
      }
      &__waive {
        width: 253px;
        height: 49px;
        position: absolute;
        top: 15px;
        right: 30px;
        font-size: 22px;
        line-height: 40px;
        font-weight: 400;
        color: #fff;
        text-align: center;
        background: url(./imgs/repay-promise-waive.png) no-repeat;
        background-size: 100% 100%;
      }
      &__back {
        font-size: 36px;
        line-height: 54px;
        font-weight: 600;
        text-align: center;
      }
    }
    .content-tip {
      margin-top: 288px;
      font-size: 24px;
      line-height: 36px;
      font-weight: 400;
      color: #979797;
      text-align: center;
    }
  }
  .zl-orange {
    color: #FF8844;
    font-weight: 600;
  }
}
