/* eslint-disable indent */
/* eslint-disable eqeqeq */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUText, MUImage, MUView, MUToast, MUDrawer, MUIcon, MUButton
} from '@mu/zui';
import Madp from '@mu/madp';
import PropTypes from 'prop-types';
import './showPromiseModal.scss';
import couponImg from '../imgs/repay-promise-coupon.png';
import { urlDomain } from '@utils/url_config';
import {
   EventTypes, dispatchTrackEvent
} from '@mu/madp-track';

export default class ShowPromiseModal extends Component {
  static propTypes = {
    amtExpiryTime: PropTypes.string.isRequired,//有效期时间
    amtAcount: PropTypes.string.isRequired,//优惠金额
    beaconId: PropTypes.string.isRequired,//beaconId
  }

  constructor(props) {
    super(props);
    this.state = {
      isShow: false,
    }
  }

  componentDidMount() {
    const isOpen = Boolean(this.props.isOpen);
    this.setState({
      isShow: isOpen
    });
    if (isOpen){
      dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.beaconId}.ShowPromiseModal` });
    }
  }

  onCloseClick = () => {
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: `${this.props.beaconId}.close` });
    this.setState({
      isShow: false
    });
  }

  onConfirmButtonClickAction = () => {
    this.setState({
      isShow: false
    }, () => {
      Madp.navigateTo({
        url: `${urlDomain}/${Madp.getChannel()}/benefit/#/pages/coupon/index`
      });
    });
  }

  render() {
    const { amtExpiryTime, amtAcount } = this.props;
    const {
      isShow,
    } = this.state;
    return (
          <MUDrawer
            beaconId="showPromiseModal"
            show={Boolean(isShow)}
            placement="bottom"
            height="345px"
            ifPreventMove={true}
            onClose={this.onCloseClick}
            contentStyle={{ borderRadius: '20px 20px 0 0' }}
          >
            
            <MUView className="sectionCouponView">
              <MUIcon
                className="close_img"
                size={18}
                value="close2"
                onClick={this.onCloseClick}
              />
                <MUView className="titleDesc">送您一张限时优惠券</MUView>
                <MUView className="couponImg">
                  <MUView className="leftSection">息费减免</MUView>
                  <MUView className="rightSection">
                    <MUView className="amtDesc">最高省
                      <MUText className="amtMount">{amtAcount}元</MUText>
                    </MUView>
                    <MUText className="timeText">有效期至{amtExpiryTime}</MUText>
                    <MUText className="descText">结清所有逾期金额可用，未按承诺还款券失效</MUText>
                  </MUView>
                </MUView>
                <MUButton beaconId="showCouponBtn" type="primary" className="confirm-button" onClick={this.onConfirmButtonClickAction}>查看优惠券</MUButton>
                <MUView className="infomation">
                  <MUText className="tag">服务</MUText>
                  <MUText className="text">本弹窗由招联推送</MUText>
                </MUView>
            </MUView>
          </MUDrawer>
        )
  }
}
