.sectionCouponView {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100vw;
  overflow: hidden;
  opacity: 1;
  border: 0 solid #6e6d6d;
  background: #ffffff;
  z-index: 1000;
  box-sizing: border-box;
  .close_img{
    position: absolute;
    right: 30px;
    top: 30px;
    color: #b2b2b2;
  }
  .titleDesc {
      width: 100%;
      height: 60px;
      line-height: 60px;
      color: #000;
      text-align: center;
      font-size: 40px;
      font-weight: 600;
      padding: 38px 0 64px 0;
      box-sizing: border-box;
      background: linear-gradient(180deg, #E1EAFF 0%, #fff 100%);
  }
    .couponImg{
      background-color: transparent;
      margin-top: 40px;
      width: 710px;
      height: 242px;
      align-self: center;
      display: flex;
      flex-direction: row;
      background: url(https://file.mucfc.com/cop/0/0/202402/2024020209362165e173.png) no-repeat;
      background-size: 100% 100%;
      overflow: hidden;
      .leftSection{
        margin-top: 48px;
        margin-left: 90px;
        width: 90px;
        height: 108px;
        opacity: 1;
        color: #a9643c;
        text-align: center;
        font-size: 36px;
        font-weight: 600;
        font-family: "PingFang SC";
        line-height: 54px;
      }
      .rightSection{
        height: 200px;
        width: 450px;
        margin-left: 90px;
        margin-top: 40px;
        display: flex;
        flex-direction: column;
        .amtDesc{
          width: 300px;
          height: 44px;
          opacity: 1;
          color: #a9643c;
          text-align: left;
          font-size: 36px;
          font-weight: 600;
          font-family: "PingFang SC";
          line-height: 43.2px;
        }
        .amtMount{
          width: 169px;
          height: 44px;
          opacity: 1;
          color: #ff8844;
          text-align: left;
          font-size: 36px;
          font-weight: 600;
          font-family: "PingFang SC";
          line-height: 43.2px;
        }
        .timeText{
          margin-top: 5px;
          width: 300px;
          height: 36px;
          opacity: 1;
          color: #a9643c;
          text-align: left;
          font-size: 24px;
          font-weight: 400;
          font-family: "PingFang SC";
          line-height: 36px;
        }
        .descText{
          margin-top: 5px;
          width: 400px;
          height: 30px;
          opacity: 1;
          color: #a6a6a6;
          text-align: left;
          font-size: 20px;
          font-weight: 400;
          font-family: "PingFang SC";
          line-height: 30px;
        }
      }
    }

  .confirm-button {
      margin-top: 30px;
      width: 670px;
      height: 100px;
      border-radius: 50px;
      opacity: 1;
      border: 0 solid #05050514;
      background: #3477ff;
  }
  .infomation {
    margin-top: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    .tag {
      margin-left: 40px;
      color: #ffffff;
      width: 68px;
      height: 32px;
      border-radius: 4px;
      opacity: 1;
      border: 0 solid #979797;
      background: #0000004d;
      font-size: 22px;
      font-weight: 400;
      font-family: "PingFang SC";
      line-height: 32px;
      text-align: center;
    }
    .text {
      flex: 1;
      text-align: left;
      margin-left: 12px;
      width: 192px;
      height: 30px;
      opacity: 1;
      color: #a6a6a6;
      text-align: left;
      font-size: 24px;
      font-weight: 400;
      font-family: "PingFang SC";
      line-height: 30px;
    }
  }
}