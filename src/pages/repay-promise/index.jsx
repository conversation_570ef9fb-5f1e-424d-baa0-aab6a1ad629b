/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUImage, MUText, MUButton, MUActionSheet, MUActionSheetItem,
} from '@mu/zui';
import { isMuapp, Url } from '@mu/madp-utils';
import { injectState } from '@mu/leda';
import { track, EventTypes } from '@mu/madp-track';
import { getLoginInfo, opService, getPageConf, getProductAllParams } from '@mu/business-basic';
import pageHoc from '@utils/pageHoc';
import Protocol from '@components/protocol/index';
import { EVENT_CODE_MAP } from '@utils/constants';
import ChannelConfig from '@config/index';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import repayPromiseSelect from './imgs/repay-promise-select.png';
import { OpRepayCommitment } from '@mu/op-comp';
import './index.scss';

// 默认页面展位id
const repayPromiseLedaId = 'de955929-4d4d-4b4f-a221-5c0f84832c71';

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: repayPromiseLedaId
    }
  }
}, {
  pageId: 'RepayPromise',
  dispatchOnMount: true,
})
@pageHoc({ title: '缓催' })
@injectState({
  pageId() {
    return repayPromiseLedaId;
  },
  getPageConf: () => getPageConf(repayPromiseLedaId, true),
  stateKeys: []
})
export default class RepayPromise extends Component {
  config = {
    navigationBarTitleText: '缓催',
  };
  constructor(props) {
    super(props);
    this.state = {
      pageStatus: '0', // 0-进入，1-可办理
      showReasonSelector: false, // 展开原因选择
      selectedReasonText: ''
    };
    this.selecedReason = '';//获取已经选择的原因，这个时候不能点击;
    this.isCanSelectedReason = true;//获取已经选择的原因，这个时候不能点击;
    this.applyNo = ''; // 缓催案件号
    this.promiseRepayDate = ''; // 承诺还款日期
    this.waiveAmt = ''; // 息费优惠券面值
    this.promiseMan = ''; // 承诺人
    this.contractInfos = []; // 需签署协议列表，3.0
    this.isCheckedContract = false; // 协议勾选状态
    this.availSelectPromiseReasons = []; // 可选择办理原因
    this.selectedReasonNo = ''; // 选中原因编码
    this.opRetainOpened = ''; // // 是否已经成功展示过挽留弹窗
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '缓催' });
  }

  async componentDidMount() {
    await this.getLoginInfo();
    //获取产品参数--缓催原因字段信息
    let difficultyOption = '';
    if (getProductAllParams && typeof getProductAllParams === 'function') {
      ({ difficultyOption } = await getProductAllParams('HC.HC01') || {});
    }
    if(difficultyOption && difficultyOption.length){
      const objStr = JSON.parse(difficultyOption);
      this.availSelectPromiseReasons = Object.entries(objStr).map(([key, value]) => ({ reasonNo: key,
        reasonText: value }));
    }
    // 初始化获取建案信息，建案状态
    await this.initApplyCase();

  }

  getTimeFormat = (timeStr,showAll) => {
    if (timeStr && timeStr.length === 8) {
      const year = timeStr.substring(0, 4);
      const month = timeStr.substring(4, 6);
      const day = timeStr.substring(6, 8);
      const showAllResult = year + "年" + month + "月" + day + "日";
      const showReault = month + "月" + day + "日";
      if (showAll) {
        return showAllResult;
      }
      return showReault;
    }
    return "";
  }

  getLoginInfo = async () => {
    const { userIdHash, custIdHash, custName } = await getLoginInfo() || {};
    this.loginInfoHash = { userHashNo: userIdHash, custHashNo: custIdHash };
    this.promiseMan = custName;
  }

  initApplyCase = async () => {
    const { data, ret, errCode } = await Dispatch.repayment.postLoanCustApplyCase({ serviceType: '010', ...this.loginInfoHash }) || {};
    if (ret !== '0') {
      this.jumpToRefuse(errCode);
      return;
    }

    const { applyNo, intransitApplyNo, applyStatus, intransitApplyStatus, contractInfoList, } = data || {};
    if (applyStatus === '110' || intransitApplyStatus === '110') {
      this.applyNo = applyNo || intransitApplyNo; // 设置案件号
      this.contractInfos = (contractInfoList || []).filter(item => item && item.contractCode === 'DHBGXY'); // 设置合同信息，只需要贷后协议
      
      this.queryPackageInfo();
    } else {
      //拒绝页面按照errcode 进行展示错误文案
      this.jumpToRefuse(errCode);
    }
  }

  //查询套餐信息展示时间和优惠信息
  queryPackageInfo = async () => {
    const { data, ret } = await Dispatch.repayment.postLoanQueryPackageInfo({ serviceType: '010', ...this.loginInfoHash }) || {};
    if (ret !== '0') {
      this.jumpToRefuse();
      return;
    }
    //拿到日期和优惠金额

    this.promiseRepayDate = data.promiseRepayDate || '';
    this.waiveAmt = data.waiveAmt || '';
    this.selecedReason = data.applyReason || '';//这个时候返回了B端的建案信息，不能点击
    if (this.selecedReason && this.selecedReason.length){
      this.isCanSelectedReason = false;
      
      //这里面还要获取对应的映射编码从数组availSelectPromiseReasons,跟进原因字段获取,selectedReasonNo
      this.availSelectPromiseReasons.forEach((item) => {
        if (item.reasonText === this.selecedReason) {
          this.selectedReasonNo = item.reasonNo;
        }
      });
    }
    this.setState({
      pageStatus: '1',
      selectedReasonText: this.selecedReason
    });
    
    if (process.env.TARO_ENV === 'h5') {
      Util.pushUrlState('keepState');
    }
  }

  // 提供放置挽留弹窗的位置
  async beforeRouteLeave(from, to, next, options) {
    if (from.path === to.path && this.opRetainOpened !== 'SUCCESS') {
      // 触发交互运营弹窗
      const opRes = await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.repayPromiseExit);
      // // 如未正确打开op，也进行返回兜底
      this.opRetainOpened = opRes;
      // 如未正确打开op，也进行返回兜底
      if (opRes !== 'SUCCESS') {
        Util.closeOrBack();
      } else {
        // 如果成功打开op，则不继续导航,取消路由变化；解决二次进入后无法返回的问题
        next(false);
        return;
      }
    } else {
      if (from.path === to.path) {
        Util.closeOrBack();
      } else {
        next(true);
      }
    }
  }

  closeOrBack = () => {
    if (process.env.TARO_ENV === 'h5') {
      if (isMuapp() && Url.getParam('repaymentFlag') !== 'Y') {
        Madp.closeWebView();
      } else {
        Madp.navigateBack({ delta: 1 }).then().catch(() => {
          Madp.closeWebView();
        });
      }
    } else {
      Madp.navigateBack({ delta: 1 }).then().catch(() => {
        const launchUrl = '%2Fpages%2Findex%2Findex';
        Madp.reLaunch({
          url: decodeURIComponent(launchUrl),
        });
      });
    }
  }

  async opOnPageEvent(eventName, interactionEventCode) {
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: repayPromiseLedaId,
            repayPromiseDate: this.promiseRepayDate,
            opRetainClose: () => { Util.closeOrBack(); }, // 退出方法
            trackPageId: 'repayPromise',
            pageCode: 'repayPromise' // op用于区分挽留弹窗
          },
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve('ERROR');
      }
    });
  }

  checkSupply = async () => {
    const { authInfoDetails } = await Dispatch.repayment.checkSupplyInfo('delay_repay') || {};
    const isNeedSupplyID = (authInfoDetails || []).filter((process) => process.authParamType === 'COMPENSATE_ID_INFO').length;
    const params = {
      scene: 'SCENE_DELAY_PAY',
      billType: 'extend',
      applyNo: this.applyNo,
    };
    if (isNeedSupplyID) {
      Util.gotoSupplyInfo(params);
      return true;
    }
    return false;
  }

  applyRepayCompose = async () => {
    if (!this.selectedReasonNo) {
      Madp.showToast({
        title: '请选择还款困难原因',
        icon: 'none'
      });
      return;
    }
    if ((this.contractInfos || []).length && (!this.isCheckedContract)) {
      Madp.showToast({
        title: '请阅读并同意协议',
        icon: 'none'
      });
      return;
    }
    const needSupply = await this.checkSupply();
    if (needSupply) return;
    this.repayPromiseSubmit();
  }

  repayPromiseSubmit = async () => {
    const { ret, data } = await Dispatch.repayment.postLoanServiceSubmitCase({
      ...this.loginInfoHash,
      applyNo: this.applyNo,
      serviceType: '010',
      custType: this.selectedReasonNo,//申请客群
      promiseRepayDate: this.promiseRepayDate,//还款承诺日期
    });
    const { applyStatus } = data || {};
    if (ret === '0') {
      this.applyStatusNext(applyStatus);
    } else {
      this.JumpToResult('?status=4'); // 兜底异常页
    }
  }

  applyStatusNext = async (status) => {
    if (status === '210') {
      this.JumpToResult('?status=1'); // 办理成功
      this.finishLoading = true;
      this.showLoadingDialog = false;
      this.loadingDialog && this.loadingDialog.hide();
    } else { // 兜底异常页
      this.JumpToResult('?status=4');
      this.finishLoading = true;
      this.showLoadingDialog = false;
      this.loadingDialog && this.loadingDialog.hide();
      clearTimeout(this.loadingTimer);
    }
  }

  selectPrommiseReason = (item) => {
    this.selectedReasonNo = item.reasonNo;
    this.setState({
      showReasonSelector: false,
      selectedReasonText: item.reasonText
    });
  }

  jumpToRefuse = (errCode) => {
    Util.router.replace({
      // 准入拒绝（若错误码为 errCode 则是存在已生效的协商还、否则为兜底拒绝页）
      path: `/pages/service-refuse/index?serviceType=RepayPromise&errCode=${errCode}`
    });
  }

  JumpToResult = (urlParam) => {
    Util.router.replace({
      path: `/pages/repay-promise/result${urlParam}&&applyNo=${this.applyNo}`,
    });
  }

  render() {
    const {
      pageStatus, showReasonSelector, selectedReasonText
    } = this.state;
    const {
      contractInfos, availSelectPromiseReasons,isCanSelectedReason
    } = this;
    if (pageStatus === '0') {
      return <MUView />;
    }

    return (
      <MUView className="pages-bg repay-promise">
        <MUView className={`repay-promise__content${isMuapp() ? ' repay-promise__content--safe' : ''}`} style={`${ChannelConfig.showMuNavBar ? 'min-height: calc(100vh - 100px);' : ''}`}>
          <MUView className="content-header">
            <MUView className="content-header__title">承诺按约还款</MUView>
            {this.waiveAmt ? (<MUView className="content-header__desc">
              <MUText className="zl-orange">{this.getTimeFormat(this.promiseRepayDate,false)}</MUText>前暂缓催收
              ,还可得<MUText className="zl-orange">{this.waiveAmt}元息费</MUText>优惠券
            </MUView>) : (<MUView className="content-header__desc">
              <MUText className="zl-orange">{this.getTimeFormat(this.promiseRepayDate,false)}</MUText>前暂缓催收
            </MUView>)
            }

          </MUView>
          {(this.selecedReason && this.selecedReason.length > 0) ? (
            <MUView className="content-topic">
            <MUView className="content-topic__first-selected-line">
              <MUView>   本人 因{this.selecedReason}，目前还款困难。本人承诺于<MUText className="zl-orange content-topic__second-line--special">{this.getTimeFormat(this.promiseRepayDate, true)}</MUText>前还清逾期金额。在此期间，本人申请暂缓对本人进行催收</MUView>
            </MUView>
            <MUView className="content-topic__sign">承诺人：{this.promiseMan}</MUView>
          </MUView>
          ) : (
            <MUView className="content-topic">
            <MUView className="content-topic__first-line">
              <MUView>本人 因</MUView>

              <MUView
                className="content-topic__reason"
                beaconId="ShowReasonSelector"
                onClick={() => this.setState({ showReasonSelector: isCanSelectedReason })}
              >
                <MUView className={`reason-text${selectedReasonText !== '' ? ' reason-text--selected' : ''}`}>{selectedReasonText || '点击选择还款困难原因'}</MUView>
                <MUView className="reason-icon">
                  <MUImage src={repayPromiseSelect} />
                </MUView>
          </MUView>
              <MUView>，</MUView>
            </MUView>
            <MUView className="content-topic__second-line">
              目前还款困难。本人承诺于 <MUText className="zl-orange content-topic__second-line--special">{this.getTimeFormat(this.promiseRepayDate, true)}</MUText> 前
            </MUView>
            <MUView className="content-topic__third-line">还清逾期金额。在此期间，本人申请暂缓对本人</MUView>
            <MUView className="content-topic__third-line">进行催收</MUView>
            <MUView className="content-topic__sign">承诺人：{this.promiseMan}</MUView>
          </MUView>
          )}
          
          <MUView className="content-handle">
            {(contractInfos || []).length ? (
              <MUView className="footer__contract">
                <Protocol
                  beaconId="repayment.RepayPromise"
                  onChecked={(v) => this.isCheckedContract = v}
                  contractInfoList={contractInfos}
                  billExtendInfo={{reason: selectedReasonText, baseDate: this.promiseRepayDate }}
                />
              </MUView>
            ) : null}
            <MUView className="content-handle__button">
              <MUButton
                type="primary"
                circle
                beaconId="ApplyRepayPromise"
                onClick={this.applyRepayCompose}
              >申请缓催</MUButton>
            </MUView>
          </MUView>
          <MUActionSheet
            className="content-selector"
            isOpened={showReasonSelector}
            cancelText="取消"
            beaconId="ReasonSelector"
            onClose={() => this.setState({ showReasonSelector: false })}
          >
            {availSelectPromiseReasons.map((reasonItem) => (
              <MUActionSheetItem
                key={reasonItem.reasonText}
                beaconId="ReasonSelectorItem"
                beaconContent={{ cus: { selectedReasonNo: reasonItem.reasonNo } }}
                onClick={() => this.selectPrommiseReason(reasonItem)}
              >{reasonItem.reasonText}</MUActionSheetItem>
            ))}
          </MUActionSheet>
        </MUView>
        {/* 交互式运营组件 */}
        <OpRepayCommitment pageId={repayPromiseLedaId} opEventKey="opPageLeave" />
      </MUView>
    );
  }
}
