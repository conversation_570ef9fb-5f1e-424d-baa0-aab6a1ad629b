/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUButton, MUImage, MUSlogan
} from '@mu/zui';
import WaRichtext from '@mu/wa-richtext';
import { Url } from '@mu/madp-utils';
import { getLocale } from '@mu/business-basic';
import { track, EventTypes } from '@mu/madp-track';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';

import { miniProgramChannel, chatPageUrl } from '@utils/constants';
import REFUSE_TOP from '@components/assets/img/refuse_top.png';
import FAQ_ICON from '@components/assets/img/faq_icon.png';

import './index.scss';

const channel = Madp.getChannel();

const SERVICE_TYPE = Url.getParam('serviceType');
const SERVICE_TYPE_MAP = {
  RenewLoans: '分期还本',
  RepayPromise: '缓催'
};

@track({ event: EventTypes.PO }, {
  pageId: `${SERVICE_TYPE}Refuse`,
  dispatchOnMount: true,
})
@pageHoc({ title: SERVICE_TYPE_MAP[SERVICE_TYPE] || '准入拒绝' })
export default class ServiceRefuse extends Component {
  config = {
    navigationBarTitleText: SERVICE_TYPE_MAP[SERVICE_TYPE] || '准入拒绝',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: SERVICE_TYPE_MAP[SERVICE_TYPE] || '准入拒绝' });
  }

  async componentWillMount() {
    const status = Url.getParam('status');
    const errCode = Url.getParam('errCode');
    let statusConfigData = {};
    
    if (SERVICE_TYPE === 'RenewLoans') {
      this.btnToChat = false;
      if (status === '1') { // 准入拒绝-逾期客户
        statusConfigData = {
          title: '抱歉，您暂不符合办理条件',
          statusTips: '您已逾期，请先还款后再来尝试，建议按时还款保持良好个人信用',
          mainBtn: '查看账单',
          subBtn: '返回首页',
        };
      } else if (status === '2') { // 准入拒绝-无借据可办理
        statusConfigData = {
          title: '抱歉，您暂无可办理的借据',
          statusTips: '办理分期还本的借据需满足一定条件，包括借据未逾期，还款方式为按月付息，到期还本等，详见下方说明',
          subBtn: '返回首页',
        };
      } else if (status === '3' || status === '4') { // 准入拒绝-未逾期管控客户/兜底页面
        statusConfigData = {
          title: '抱歉，您暂不符合办理条件',
          statusTips: '综合评估未通过，建议按时还款保持良好个人信用',
          subBtn: '返回首页',
        };
      }
    } else if (SERVICE_TYPE === 'RepayPromise') {
      if (errCode === 'UMDP03463') { // 客户没有待还的可见可还借据逾期
        statusConfigData = {
          title: '抱歉，您暂不符合办理条件',
          statusTips: '很抱歉，您暂不符合还款承诺缓催条件，如有其他疑问，可联系客服咨询',
          mainBtn: '点击跳转在线客服',
          showSlogan: true,
        };
        this.btnToChat = true;
      }else if (errCode === 'UMDP02739') { // 准入拒绝-存在已生效的协商还
        statusConfigData = {
          title: '抱歉，您暂不符合办理条件',
          statusTips: '您有生效中的协商还服务，按约还款不会收到催收打扰，无需办理还款承诺缓催，如有其他疑问，可联系客服咨询',
          mainBtn: '联系在线客服',
          showSlogan: true,
        };
        this.btnToChat = true;
      }else if (errCode === 'UMDP03464') { // 客户进入办理页面时就已超过最迟还款日期
        statusConfigData = {
          title: '抱歉，您暂不符合办理条件',
          statusTips: '很抱歉，您已错过服务办理时间，无法办理还款承诺缓催，如有其他疑问，可联系客服咨询',
          mainBtn: '联系客服咨询',
          showSlogan: true,
        };
        this.btnToChat = true;
      } else if (errCode === 'UMDP03465') { // 有生效中的还款承诺
        statusConfigData = {
          title: '抱歉，您暂不符合办理条件',
          statusTips: '您已办理还款承诺服务，无需重复办理，请按约定及时还款',
          mainBtn: '回到首页',
          showSlogan: true,
        };
        this.btnToChat = false;
      }  
      else { // 准入拒绝-兜底页面
        statusConfigData = {
          title: '抱歉，您暂不符合办理条件',
          statusTips: '综合评估暂未通过，请继续累积信用，建议您按时还款',
          mainBtn: '回到首页',
          showSlogan: true,
        };
        this.btnToChat = false;
      }
    }

    this.setState({
      statusCofig: statusConfigData
    });
  }

  JumpToRepayIndex = () => {
    const isMiniProgramChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
    
    if (isMiniProgramChannel) {
      if(this.btnToChat){
        Madp.navigateTo({
          url: chatPageUrl
        });
      }else{
        Madp.miniProgram.reLaunch({
          url: '/repayment/pages/index/index',
        });
      }
    } else {
      if (this.btnToChat) {
        Madp.navigateTo({
          url: chatPageUrl
        });
      }else{
        Util.router.replace({
          path: '/pages/index/index',
        });
      }
    }
  }

  render() {
    const status = Url.getParam('status');
    const { statusCofig } = this.state;
    const { title, statusTips, mainBtn, subBtn, showSlogan = false } = statusCofig || {};

    return (<MUView className="service-refuse">
      <MUImage className="service-refuse-bg" style={{ top: channel === '3CMBAPP' ? '45px' : 0 }} src={REFUSE_TOP} />
      <MUView className="service-refuse-top">
        <MUView className="service-refuse-top__title">{title}</MUView>
        <MUView className="service-refuse-top__content">
          <MUView className="service-refuse-top__content--desc">{statusTips}</MUView>
          {mainBtn ? (<MUButton
            className="service-refuse-top__content--btn"
            beaconId="JumpToExpressRepay"
            beaconContent={{ cus: { status } }}
            onClick={this.JumpToRepayIndex}
          >{mainBtn}</MUButton>) : null}
        </MUView>
      </MUView>
      <MUView className="service-refuse-faq">
        <MUView className="service-refuse-faq__title">您可能想问</MUView>
        {/* TODO：修改文案编码，修改后确认下 FAQ 会不会遮挡 slogan (题目内容比较短，理论上不需要调整) */}
        <MUView className="faq-content">
          <MUView className="faq-content__subTitle">
            <MUImage className="faq-content__subTitle--icon" src={FAQ_ICON} />
            <WaRichtext beaconId="FaqTitleRichtext" content={SERVICE_TYPE === 'RepayPromise' ? getLocale('HC01.HC01WA001'):getLocale('FQHB01.FQHB01WA001')} />
          </MUView>
          <MUView className="faq-content__main">
            <WaRichtext beaconId="FaqContentRichtext" content={SERVICE_TYPE === 'RepayPromise' ? getLocale('HC01.HC01WA002'):getLocale('FQHB01.FQHB01WA002')} />
          </MUView>
        </MUView>
        {SERVICE_TYPE === 'RepayPromise' ? (
          <MUView className="faq-content">
            <MUView className="faq-content__subTitle">
              <MUImage className="faq-content__subTitle--icon" src={FAQ_ICON} />
              <WaRichtext beaconId="FaqTitleRichtext" content={getLocale('HC01.HC01WA003')} />
            </MUView>
            <MUView className="faq-content__main">
              <WaRichtext beaconId="FaqContentRichtext" content={getLocale('HC01.HC01WA004')} />
            </MUView>
          </MUView>
        ) : <MUView/>}
        
      </MUView>
      {subBtn ? <MUView className="service-refuse-back" beaconId="JumpToRepayIndex" onClick={this.JumpToRepayIndex}>{subBtn}</MUView> : null}
      {showSlogan ? <MUSlogan className="service-refuse-slogan" onlyLogo /> : null}
    </MUView>);
  }
}
