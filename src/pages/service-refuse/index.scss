.service-refuse {
  height: 100%;
  min-height: 100vh;
  background: #F3F3F3;

  &-bg {
    position: absolute;
    left: 0;
    top: 0;
    height: 274px;
    width: 100%;
  }

  &-top {
    position: relative;
    width: 100%;

    &__title {
      height: 60px;
      margin-top: 76px;
      margin-left: 60px;
      color: #333333;
      text-align: left;
      font-size: 40px;
      font-weight: 600;
    }

    &__content {
      width: auto;
      margin: 24px 20px 20px;
      box-sizing: border-box;
      padding: 40px;
      border-radius: 24px;
      background: #FFFFFF;

      &--desc {
        color: #333333;
        font-size: 28px;
        font-weight: 400;
      }

      &--btn {
        height: 100px;
        margin-top: 40px;
        color: #FFFFFF;
        text-align: center;
        font-size: 36px;
        font-weight: 600;
        border-radius: 50px;
        border: 0 solid #979797;
        background: #3477ff;
      }
    }
  }

  &-faq {
    width: auto;
    margin: 0 20px;
    padding: 24px 30px 40px;
    border-radius: 16px;
    border: 2px solid #FFFFFF;
    background: linear-gradient(180deg, #eef3fc 0%, #FFFFFF 100%);

    &__title {
      height: 48px;
      margin-bottom: 30px;
      color: #333333;
      font-size: 32px;
      font-weight: 600;
      line-height: 48px;
    }

    .faq-content {
      &__subTitle {
        height: 48px;
        display: flex;
        align-items: center;

        &--icon {
          height: 36px;
          width: 36px;
          margin-right: 14px;
        }
      }

      &__main {
        margin-left: 50px;
      }
    }
  }

  &-back {
    margin-top: 48px;
    color: #3477ff;
    text-align: center;
    font-size: 36px;
    font-weight: 600;
  }

  &-slogan {
    width: 100%;
    position: fixed;
    left: 0;
    bottom: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}