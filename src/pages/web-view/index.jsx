import { Component } from '@tarojs/taro';
import { WebView } from '@tarojs/components';
import { Url } from '@mu/madp-utils';
import Madp from '@mu/madp';
import NavBarView from '@components/nav-bar-view/index';
import ChannelConfig from '@config/index';

import './index.scss';

export default class IframePage extends Component {
  state = {
    src: '',
    navTitle: '',
  }

  componentWillMount() {
    const srcUrl = Url.getParam('pageUrl');
    const isMuSecureUrl = Url.isMuSecure(srcUrl);
    if (isMuSecureUrl) {
      this.setState({
        src: srcUrl,
      });
    }
  }

  onLoad(e) {
    try {
      Madp.setNavigationBarTitle({ title: e.target.contentDocument.title });
      this.setState({ navTitle: e.target.contentDocument.title });
    } catch (err) {
      console.log(err);
    }
  }

  render() {
    const { src, navTitle } = this.state;
    // 客服组件要从这里跳过去，判断参数不套用招行导航栏
    return Url.getParam('showNav') ? (
      <WebView className="web-view" src={src} onLoad={(e) => this.onLoad(e)} frameBorder="0" />
    ) : (
      <NavBarView title={navTitle}>
        <WebView
          className={ChannelConfig.showMuNavBar ? 'web-view cmb-web-view' : 'web-view'}
          src={src}
          onLoad={(e) => this.onLoad(e)}
          frameBorder="0"
        />
      </NavBarView>
    );
  }
}
