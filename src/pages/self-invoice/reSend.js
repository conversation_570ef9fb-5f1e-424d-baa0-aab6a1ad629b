import { Component } from '@tarojs/taro';
import {
  MUView, MUInput, MUButton
} from '@mu/zui';
import { track, EventTypes, disableTrackAlert } from '@mu/madp-track';
import Madp from '@mu/madp';
import './invoice-set.scss';
import Dispatch from '@api/actions';
import Utility from '../../utils/Utility';
import { STORE } from './store';
import NavBarView from '@components/nav-bar-view';

const kInputPlaceHolder = '请输入用于接收电子发票的邮箱';

disableTrackAlert();

@track({ event: EventTypes.PO }, {
  pageId: 'reSend',
  dispatchOnMount: true,
})
export default class reSend extends Component {
  constructor() {
    super();
    this.state = {
      inputMail: '',
      buttonCheck: false
    };
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '发票发送' });
  }

  inputNameAction = (text) => {
    this.setState({ inputMail: text }, this.buttonStateCheck);
  }

  buttonStateCheck=() => {
    if (this.state.inputMail.length > 0) {
      this.setState({ buttonCheck: true });
    } else {
      this.setState({ buttonCheck: false });
    }
  }

  onSubmit = async () => {
    if (!Utility.isValidateEmailV2(this.state.inputMail)) {
      Madp.showToast({
        title: '请输入正确邮箱',
        icon: 'none'
      });
      return;
    }
    const { batNo } = STORE.selectedHistoryRecord || {};
    try {
      const res = await Dispatch.repayment.reApply({ batNo, email: this.state.inputMail });
      if (res) {
        Madp.showToast({
          title: '发送成功',
          icon: 'none',
          duration: 3000
        });
        Madp.closeWebView();
      }
    } catch (error) {
      Madp.showToast({
        title: '发送失败',
        icon: 'none'
      });
    }
  }

  render() {
    const { inputMail, buttonCheck } = this.state;
    return (
      <MUView>
        {process.env.TARO_ENV === 'h5' ? <NavBarView title="发票发送" /> : null}
        <MUView className="invoice-set-tip">接收方式</MUView>
        <MUInput
          beaconId="email"
          name=""
          title="电子邮箱"
          type="text"
          placeholder={kInputPlaceHolder}
          value={inputMail}
          onChange={this.inputNameAction}
        />
        <p className="invoice-set-notice">
          说明：仅支持重新发送招联开具的发票；输入邮箱后，点击提交按钮，会重新给您发送电子发票，发票信息无法变更
        </p>
        <MUButton
          beaconId="submit"
          type="primary"
          className="invoice-set-confirm-btn"
          disabled={!buttonCheck}
          onClick={this.onSubmit}
        >
          提交
        </MUButton>
      </MUView>
    );
  }
}
