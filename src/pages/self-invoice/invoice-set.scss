.invoice-set {
  display: flex;

  &-tip {
    margin-left: 30px;
    font-size: 26px;
    color: #808080;
    margin-top: 40px;
    margin-bottom: 20px;
  }

  &-input {
    display: flex;
    align-items: center;

    &-leftView {
      color: #333;
      font-size: 32px;
      height: 100%;
      line-height: 100%;
      margin-left: 30px;
      width: 160px;
    }

    &-inputView {
      height: 100px;
      display: flex;
      align-items: center;
      font-size: 32px;
    }

    &-unit {
      height: 100px;
      color: #808080;
      display: flex;
      align-items: center;
      font-size: 32px;
      margin-left: auto;
      margin-right: 50px;
      justify-self: flex-end;
    }
  }

  &-notice {
    margin: 20px 30px;
    line-height: normal;
    font-size: 24px;
    color: #a6a6a6;

    &-highlight {
      color: red;
    }
  }

  &-confirm-btn {
    margin-top: 30px !important;
    margin-left: 30px !important;
    margin-right: 30px !important;
  }
}

.organization {
  display: flex;
  align-items: flex-start;
}

.invoice-organization {
  padding: 24px 30px;
  &__item {
    height: unset !important;
    align-items: flex-start !important;
    &-left {
      min-width: 160px !important;
      line-height: unset !important;
      margin-left: unset !important;
    }
    &-right {
      height: unset !important;
    }
  }
}

.union {
  margin-top: 0;
  margin-bottom: 0;
}


.invoice-set-tip__icon{ 
  height: 28px;
  width: 28px;
  margin-top: 47px;
  margin-left: 4px;
}

