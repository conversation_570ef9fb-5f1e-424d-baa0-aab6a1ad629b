import { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import {
  MUView, MUInput, MUButton, MUDialog,
  MUModal, MUImage
} from '@mu/zui';
import { MUTradePasswordEncryptedWrap } from '@mu/trade-password-encrypted-shell';
import { track, dispatchTrackEvent, EventTypes, disableTrackAlert } from '@mu/madp-track';
import Madp from '@mu/madp';
import './invoice-set.scss';
import Dispatch from '@api/actions';
import { STORE } from './store';
import Utility from '../../utils/Utility';
import MailCheckDialog from './compoments/mail-check-dialog/index';
import tipsIcon from './assets/image/tips-icon.png';
import NavBarView from '@components/nav-bar-view';
import { getLoginInfo } from '@mu/business-basic';

const kInputPlaceHolder = '请输入用于接收电子发票的邮箱';
const titleType = '个人';

disableTrackAlert();
@track({ event: EventTypes.PO }, {
  pageId: 'invoiceSet',
  dispatchOnMount: true,
})
export default class Index extends Component {
  constructor() {
    super();
    this.state = {
      name: '--',
      inputMail: '',
      credit: (STORE.submitData || {}).totalInvAmt,
      buttonCheck: false,
      showComfirm: false,
      showTrdVerify: false,
      needCooperateInvoiceFlag: (STORE.submitData || {}).needCooperateInvoiceFlag,
      showModal: false,
      companyList: (STORE.submitData || {}).companyList
    };
  }

  inputNameAction = (text) => {
    this.setState({ inputMail: text }, this.buttonStateCheck);
  }

  buttonStateCheck = () => {
    if (this.state.inputMail.length) {
      this.setState({ buttonCheck: true });
    } else {
      this.setState({ buttonCheck: false });
    }
  }

  onSubmit = () => {
    if (!Utility.isValidateEmailV2(this.state.inputMail)) {
      Madp.showToast({
        title: '请输入正确邮箱',
        icon: 'none'
      });
      return;
    }
    this.showComfirmDialog();
  }

  closeComfirmDialog = () => {
    this.setState({
      showComfirm: false
    });
  }

  showComfirmDialog = () => {
    this.setState({
      showComfirm: true
    });
  }

  onComfirm = () => {
    this.closeComfirmDialog();
    this.verifyTradePwd();
  }

  verifyTradePwd = async () => {
    this.setState({
      showTrdVerify: true
    });
  }

  closeVerify = () => {
    this.setState({
      showTrdVerify: false
    });
  }

  onOk = async (token) => {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'InvoiceSuccess', target: this });
    const { submitData: { invoiceCompany } = {} } = STORE;
    this.closeVerify();
    this.businessId = this.generateUUID();
    const tempMerchantNo = [];
    const merchantNos = [];
    invoiceCompany.forEach(item => {
      const { merchantNo, merchantOrgName } = item || {};
      if (!tempMerchantNo.includes(merchantNo)) {
        tempMerchantNo.push(merchantNo);
        merchantNos.push({ merchantNo, merchantOrgName, orgId: '', subMerchantNo: '' });
      }
    });
    // 取已选的发票的最早时间
    let startDate = Infinity;
    invoiceCompany.forEach(item => {
      const { businessDate } = item || {};
      if (Number(businessDate) < startDate) startDate = Number(businessDate);
    });
    const data = {
      bizContent: this.businessId,
      passwordToken: token,
      operateType: '1',
      email: this.state.inputMail,
      invTitle: titleType,
      merchantNos,
      startDate: String(startDate)
    };
    if (`${(STORE.submitData || {}).allSelectFlag}` === '1') {
      data.scopeType = '1';
    } else {
      data.scopeType = '2';
      data.transSeqnos = [...(STORE.submitData || {}).selectTransSeqno];
    }
    const ret = await Dispatch.repayment.applyInv(data) || {};
    if (ret.transSeqno) {
      Madp.redirectTo({ url: '/pages/self-invoice/invoiceDetail' });
    } else {
      Madp.showToast({
        title: '提交失败,请稍后再试',
        icon: 'none'
      });
      // Madp.closeWebView();
    }
  }

  onOverPwAttemptNum = () => {
    Madp.showToast({
      title: '密码输入次数超限，请稍后再试',
      icon: 'none'
    });
    this.closeVerify();
  }

  generateUUID() {
    let d = new Date().getTime();
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      // eslint-disable-next-line no-mixed-operators
      return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    });
    return uuid;
  }

  onTipsClick = () => {
    this.setState({
      showModal: true
    });
  }

  onTipsClose = () => {
    this.setState({
      showModal: false
    });
  }

  async componentDidShow() {
    Madp.setNavigationBarTitle({ title: '发票设置' });
    let custInfo;
    try {
      custInfo = await getLoginInfo();
    } catch (ignore) {}
    const { custName: maskRealName } = custInfo || {};
    this.setState({ name: maskRealName });
  }

  render() {
    const {
      inputMail, buttonCheck, showComfirm, showModal,
      showTrdVerify, name, credit, companyList, needCooperateInvoiceFlag
    } = this.state;
    return (
      <MUView>
        {process.env.TARO_ENV === 'h5' ? <NavBarView title="发票设置" /> : null}
        <MUView className="invoice-set-tip">电子发票详情</MUView>
        {/* <MUInput name="name" title="个人姓名" type="text" value="张三" disabled /> */}
        <View style="background-color:white;">
          <View className="invoice-set-input">
            <View className="invoice-set-input-leftView">个人姓名</View>
            <View className="invoice-set-input-inputView">{name}</View>
          </View>

          <View className="invoice-set-input">
            <View className="invoice-set-input-leftView">抬头类型</View>
            <View className="invoice-set-input-inputView">{titleType}</View>
          </View>

          <View className="invoice-set-input">
            <View className="invoice-set-input-leftView">发票金额</View>
            <View className="invoice-set-input-inputView">{credit}元</View>
          </View>
        </View>

        <MUView className="invoice-set-tip">接收方式</MUView>
        <MUInput
          beaconId="inputEMail"
          name="name"
          title="电子邮箱"
          type="text"
          placeholder={kInputPlaceHolder}
          value={inputMail}
          onChange={this.inputNameAction}
        />

        {needCooperateInvoiceFlag
        && <MUView>
          <MUView className="organization">
            <MUView className="invoice-set-tip">开票机构</MUView>
            <MUImage src={tipsIcon} className="invoice-set-tip__icon" onClick={this.onTipsClick} />
          </MUView>
          <View className="invoice-organization" style="background-color:white;">
            {(companyList || []).map((item, i) =>
              (<View className="invoice-set-input invoice-organization__item">
                <View className="invoice-set-input-leftView invoice-organization__item-left">{i === 0 ? '机构名称' : ''}</View>
                <View className="invoice-set-input-inputView invoice-organization__item-right">{item}</View>
              </View>)
            )}
          </View>
        </MUView>}
        {
          !needCooperateInvoiceFlag ? <p className="invoice-set-notice">
            说明：系统会在收到开票申请的
            <span className="invoice-set-notice-highlight">
              24小时
            </span>
            内完成开票，并发送至你的电子邮箱，请注意查收！
          </p> : <MUView>

            <p className="invoice-set-notice" style="margin-bottom: 0">说明：</p>
            <p className="invoice-set-notice union">
              1、由招联开具的发票，
              <span className="invoice-set-notice-highlight">
                24小时
              </span>
              内完成开票；由其他机构开具的发票，预计1个月左右完成开票
            </p>
            <p className="invoice-set-notice union">
              2、发票开具后，将发送至你的电子邮箱，请注意查收。
            </p>
          </MUView>
        }
        <MUButton
          beaconId="submintInvoiceApply"
          type="primary"
          className="invoice-set-confirm-btn"
          disabled={!buttonCheck}
          onClick={this.onSubmit}
        >
          提交
        </MUButton>

        <MUDialog
          isOpened={showComfirm}
          onClickOverlay={this.closeComfirmDialog}
        >
          <MailCheckDialog
            onCancel={this.closeComfirmDialog}
            onSubmit={this.onComfirm}
            mail={inputMail}
            name={name}
            titleType={titleType}
            credit={credit}
          />
        </MUDialog>

        <MUTradePasswordEncryptedWrap
          beaconId="TradePassword"
          title="请输入交易密码"
          scene="SCENE_SELF_INVOICE"
          verifyType="TRA"
          codeLen={6}
          leftText=""
          rightText=""
          isOpened={showTrdVerify}
          onClose={this.closeVerify}
          onOk={(msg, token) => { this.onOk(token); }}
          onOverPwAttemptNum={this.onOverPwAttemptNum}
          needShuffle={false}
        />
        <MUModal
          isOpened={showModal}
          title="开票机构说明"
          content="你的借款存在其他放款机构，将由实际放款机构为你开具发票"
          confirmText="我知道了"
          onConfirm={this.onTipsClose}
          onClose={this.onTipsClose}
          onClickOverlay={this.onTipsClick}
        />
      </MUView>
    );
  }
}
