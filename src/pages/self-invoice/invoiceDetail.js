import { Component } from '@tarojs/taro';

import { MUText, MUButton, MUIcon, MUSlogan } from '@mu/zui';
import { track, EventTypes, disableTrackAlert } from '@mu/madp-track';
import Madp from '@mu/madp';
import { View } from '@tarojs/components';
import './invoice-detail.scss';
import { clearStore } from './store';
import NavBarView from '@components/nav-bar-view';

disableTrackAlert();
@track({ event: EventTypes.PO }, {
  pageId: 'invoiceResult',
  dispatchOnMount: true,
})
export default class Index extends Component {
  constructor() {
    super();
    this.state = {
    };
  }

  toMain = () => {
    clearStore();
    Madp.redirectTo({ url: '/pages/self-invoice/index' });
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '开票结果' });
  }

  render() {
    return (
      <View>
        <View className="invoice-detail-page">
          {process.env.TARO_ENV === 'h5' ? <NavBarView title="开票结果" /> : null}
          <View className="invoice-detail-invoicing">
            <MUIcon className="icon" value="checked" size="120" color="#03d560" />
            <MUText className="null_p">系统正在开票，请留意邮箱</MUText>
            <MUButton type="primary" full className="invoice-detail-invoicing-btn" onClick={this.toMain}>
              返回开票首页
            </MUButton>
          </View>
        </View>
        <MUSlogan onlyLogo className="invoice-detail-slogan" />
      </View>
    );
  }
}
