import Madp from '@mu/madp';

const STORE_KEY = 'self_invoice_data';

const RECORD_DEFAULTS = {
  currentTab: 0,
  // invApplyInfoList: [],
  // resumeBusinessDate: '',
  // resumeTransSeqno: '',
  // noMoreFlag_repay: false,
  submitData: {
    totalInvAmt: '',
    allSelectFlag: '',
    selectTransSeqno: [],
    invoiceCompany: [],
    needCooperateInvoiceFlag: false,
    companyList: []
  },
  invInfoList: [],
  resumeBatNo: '',
  // eslint-disable-next-line camelcase
  noMoreFlag_history: false,
  selectedHistoryRecord: {},
};

const RECORD_STORE = {
  ...RECORD_DEFAULTS
};

Object.keys(RECORD_STORE).forEach((key) => {
  Object.defineProperty(RECORD_STORE, key, {
    set(val) {
      let data = Madp.getStorageSync(STORE_KEY, 'SESSION');
      if (!data) data = {};
      data[key] = val;
      Madp.setStorageSync(STORE_KEY, data, 'SESSION');
    },
    get() {
      let data = Madp.getStorageSync(STORE_KEY, 'SESSION');
      if (!data) data = {};
      if (data[key] === undefined) {
        data[key] = RECORD_DEFAULTS[key];
      }
      return data[key];
    }
  });
});

export const clearStore = () => {
  try {
    Madp.removeStorageSync(STORE_KEY, 'SESSION');
  } catch (e) {
    try {
      Madp.clearStorageSync();
    } catch (err) {}
  }
};
export const STORE = RECORD_STORE;

export default RECORD_DEFAULTS;
