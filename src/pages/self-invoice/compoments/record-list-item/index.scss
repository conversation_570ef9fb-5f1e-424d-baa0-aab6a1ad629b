.record-item--wrapper {
  padding: 29px 30px 29px 32px;
  background-color: #fff;

  .record-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 0;
  
    &-icon {
      margin-left: 30px;
      border-radius: 8px;
      align-self: flex-start;
    }
  
    .record-control-icon {
      margin-left: 30px;
      width: 28px;
      height: 28px;
      border: 1PX solid #e5e5e5;
      border-radius: 8px;
      background: #f3f3f3;
    }
  
    &-info {
      padding-left: 10px;
    }
  
    &-date {
      margin-left: 20px;
      font-size: 28px;
      line-height: 42px;
      color: #333;
      font-weight: 400;
    }
  
    &-amt {
      margin-left: auto;
      font-size: 28px;
      line-height: 42px;
      color: #333;
      font-weight: 400;
    }
  
    &-devider {
      height: 1px;
      width: 3px;
      background: #ffffff;
    }
  }
  .record-control {
    margin: 20px 0 0 90px;
    font-size: 28px;
    line-height: 28px;
    color: #a6a6a6;
  }
}