import { Component } from '@tarojs/taro';
import { MUView, MUIcon, MUText } from '@mu/zui';
import PropTypes from 'prop-types';
import './index.scss';
import Utility from '@utils/Utility';

export default class RecordItem extends Component {
  static propTypes = {
    selected: PropTypes.bool,
    onSelected: PropTypes.func,
    onTipClick: PropTypes.func,
    InvApplyInfo: PropTypes.shape({
      businessDate: PropTypes.string,
      invAmt: PropTypes.string,
      transSeqno: PropTypes.string
    }),
    hideInfoIcon: PropTypes.bool,
  }

  static defaultProps = {
    selected: false,
    onSelected: () => { },
    onTipClick: () => { },
    InvApplyInfo: {
      businessDate: '----年--月--日',
      invAmt: '--',
      transSeqno: ''
    },
    hideInfoIcon: false,
  }

  onRecordSelect = () => {
    const { InvApplyInfo } = this.props;
    if (InvApplyInfo && InvApplyInfo.mslTag === '01') {
      this.props.onTipClick('1');
      return;
    }
    if (InvApplyInfo && InvApplyInfo.transactionType === '01') {
      this.props.onTipClick('2', InvApplyInfo);
    }
  }

  render() {
    const {
      InvApplyInfo: { businessDate, invAmt, transactionType, needCooperateInvoiceFlag, invoiceApplyStatus } = {}, selected, onSelected, hideInfoIcon
    } = this.props;
    let tipType = '';
    if (needCooperateInvoiceFlag === 'Y') {
      tipType = '4';
    } else if (invoiceApplyStatus === '1') {
      tipType = '6';
    } else if (invoiceApplyStatus === '2') {
      tipType = '7';
    }

    return (
      <MUView className="record-item--wrapper">
        <MUView className="record-item">
          {!hideInfoIcon ? (
            <MUView>
              {(invoiceApplyStatus !== '1' && invoiceApplyStatus !== '2' && needCooperateInvoiceFlag !== 'Y') ? (<MUIcon
                  beaconId="selectInvoiceItem"
                  className="record-item-icon"
                  value={selected ? 'checked-box' : 'unchecked-box'}
                  size="16"
                  color="#3477FF"
                  onClick={onSelected}
                />) : (<MUView
                  className="record-control-icon"
                />)
              }
            </MUView>
          ) : null}
          {(invoiceApplyStatus === '1' || invoiceApplyStatus === '2' || needCooperateInvoiceFlag === 'Y') ? (<MUText className="record-item-date" onClick={() => this.props.onTipClick(tipType)}>
            {Utility.tranformDate(businessDate)}
            {!hideInfoIcon ? (<MUIcon className="record-item-info" value="info" size="16" color="#808080" />) : null}
          </MUText>) : (<MUText className="record-item-date" onClick={this.onRecordSelect}>
            {Utility.tranformDate(businessDate)}
            {transactionType === '01' && !hideInfoIcon && (<MUIcon className="record-item-info" value="info" size="16" color="#808080" />)}
          </MUText>)}
          <MUText className="record-item-amt">{`${invAmt}元`}</MUText>
        </MUView>
      </MUView>
    );
  }
}
