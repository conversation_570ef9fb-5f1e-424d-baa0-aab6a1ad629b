.fill-view {
  padding-bottom: 158px;
  box-sizing: border-box;
  overflow: scroll;

  .trade-record-area {
    padding: 34px 24px 34px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .area__brand {
      display: flex;
      align-items: center;

      .area__brand__check {
        margin-right: 20px;
        width: 28px;
        height: 28px;
        // border: 1PX solid #3477FF;
        border-radius: 8px;
        font-size: 0;
      }

      .area__brand__disabled {
        border: 1PX solid #e5e5e5;
        background: #f3f3f3;
      }

      .area__brand__text {
        font-size: 32px;
        line-height: 32px;
        color: #333;
        font-weight: 600;
      }

      .area__brand__info {
        margin-left: 10px;
      }
    }

    .area__count {
      display: flex;
      align-items: center;

      .area__count__text {
        font-size: 32px;
        line-height: 32px;
        color: #333;
        font-weight: 600;
      }

      .area__count__arrow {
        padding-left: 8px;
      }
    }
  }

  .trade-record-list2 {
    margin: 20px 20px 0;
    background-color: #fff;
    border-radius: 16px;
  }

  .trade-record-divide {
    width: 100%;
    height: 1PX;
    background: #e5e5e5;
  }
}

.trade-record-list {
  max-height: 580px;
  margin: 20px 20px 0;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;

  .mu-list__title {
    padding: 10px 15px;
    font-size: 30px;
  }

  .pull-up-desc,
  .no-more-desc {
    height: 64px;
    display: block;
    font-size: 24px;
    line-height: 64px;
    text-align: center;
    color: #a6a6a6;
    // margin-bottom: 250px;
  }

  .scroll-view {
    height: 480px;
  }

  .empty-sign {
    margin-bottom: 20px;
    text-align: center;

    i {
      text-align: center;
      display: block;
      width: 200px;
      height: 200px;
      margin: 74px auto 20px;
      background: url(../../../../components/assets/img/<EMAIL>) no-repeat;
      background-image: image-set(url(../../../../components/assets/img/<EMAIL>) 2x, url(../../../../components/assets/img/<EMAIL>) 3x);
      background-size: contain;
    }

    .null_p {
      font-size: 28px;
      color: #808080;
    }
  }
}

.trade-record-list1 {
  height: 100vh;

  .trade-record-area {
    margin: 20px;
    padding: 34px 24px 34px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 16px;

    .area__brand {
      display: flex;
      align-items: center;

      .area__brand__check {
        margin-right: 20px;
        width: 28px;
        height: 28px;
        border: 1PX solid #3477FF;
        border-radius: 8px;
        font-size: 0;
      }

      .area__brand__disabled {
        border-color: #e5e5e5;
        background: #f3f3f3;
      }

      .area__brand__text {
        font-size: 32px;
        line-height: 32px;
        color: #333;
        font-weight: 600;
      }

      .area__brand__info {
        margin-left: 10px;
      }
    }
  }

  .empty-sign {
    margin-bottom: 20px;
    text-align: center;

    i {
      text-align: center;
      display: block;
      width: 200px;
      height: 200px;
      margin: 74px auto 20px;
      background: url(../../../../components/assets/img/<EMAIL>) no-repeat;
      background-image: image-set(url(../../../../components/assets/img/<EMAIL>) 2x, url(../../../../components/assets/img/<EMAIL>) 3x);
      background-size: contain;
    }

    .null_p {
      font-size: 28px;
      color: #808080;
    }
  }
}

.diversion-unsupported-invoice {
  .mu-modal__container {
    padding-top: 0;
  }
  &__content {
    padding: 0 40px 25px;
    &__tip {
      margin: 64px auto 40px;
      width: 100px;
      height: 100px;
      font-size: 0;
      .taro-img {
        width: 100%;
        height: 100%;
      }
    }
    &__title {
      font-size: 36px;
      line-height: 54px;
      color: #333;
      font-weight: 600;
      text-align: center;
    }
    &__desc {
      margin-top: 30px;
      font-size: 28px;
      line-height: 42px;
      color: #808080;
      font-weight: 400;
      text-align: center;
    }
    &__supply {}
    &__buttons {
      margin-top: 60px;
      font-size: 32px;
      text-align: center;
      position: relative;
      .button__bubble {
        position: absolute;
        right: 0;
        top: -30px;
        &__img {
          width: 180px;
          height: 48px;
          font-size: 0;
          .taro-img {
            width: 100%;
            height: 100%;
          }
        }
        &__text {
          position: absolute;
          left: 0;
          top: 0;
          width: 180px;
          padding: 7px 0;
          font-size: 22px;
          line-height: 26px;
          color: #fff;
          font-weight: 400;
          background: linear-gradient(135deg, #ffbd79 0%, #ff8844 100%);
          border-radius: 20px;
          text-align: center;
        }
      }
      .button__confirm {
        padding: 20px 0;
        line-height: 48px;
        border-radius: 44px;
        background: #3477FF;
        color: #fff;
        font-weight: 600;
      }
      .button__cancel {
        margin-top: 25px;
        font-size: 28px;
        line-height: 42px;
        color: #808080;
        font-weight: 600;
      }
    }
  }
}