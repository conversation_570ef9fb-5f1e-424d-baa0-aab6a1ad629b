import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import Dispatch from '@api/actions';
import {
  MUList, MUText, MUPullToRefresh, MUModal, MUScrollView, MUView,
  MUIcon
} from '@mu/zui';
import Madp from '@mu/madp';
import { STORE } from '../../store';
import RecordListItem from '../record-list-item/index';
import RecordBottom from '../record-bottom';
import './index.scss';
import Utility from '@utils/Utility';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';

import DiversionUnsupportedInvoiceModal from '../DiversionUnsupportedInvoiceModal';
import diversionModalInfo from '../../assets/image/diversion-modal-info.png';
import diversionModalBubble from '../../assets/image/diversion-modal-bubble.png';

const pageSize = '11';
export default class RecordListTab extends Component {
  constructor(props) {
    super(props);
    this.state = {
      invApplyInfoList: [],
      invApplyInfoListOnlyZL: [], // 开票机构为招联的可开票列表
      noticeText: '系统处理中，请耐心等待',
      resumeBusinessDate: '',
      resumeTransSeqno: '',
      noMoreFlag: false,
      selectTransSeqno: new Set(),
      // totalInvAmt: '-', // 总可开票额
      allSelectFlag: false,
      pageSelectFlag: false,
      modalContent: '',
      showTip: '0', // 是否展示联合贷提示弹窗
      tipContent: false, // 是否展示联合贷提示弹窗内容
      invApplyInfoListOnlyZLShow: [], // 开票机构为招联的可展示列表
      invApplyInfoListCooperate: [], // 开票机构为合作方列表
      totalCnt: '-', // 总的交易数
      cooperateCnt: 0, // 合作方交易数
      showZlList: false, // 是否展示招联列表
      showCooperateList: false, // 是否展示合作方列表
      showDiversionUnsupportedInvoice: false, // 导流不支持开票弹窗
    };
    this.transferOrgName = ''; // 受让方机构名称
    this.transferMerchantMobileNo = ''; // 受让方联系方式
    this.needShowDiversionMerchantInfo = false; // 是否展示导流开票信息
    this.diversionMerchantInfoList = []; // 导流开票信息列表
    this.diversionUnsupportedInvoiceParam = {}; // 导流不支持开票弹窗参数
  }

  async componentDidMount() {
    // await this.getRecords(true);
  }

  getRecords = async (init = false, identified = true) => {
    if (!identified) {
      this.setState({ noticeText: '暂无可开票交易' });
      return;
    }
    const { invApplyInfoList: oldInvApplyInfoList } = this.state;
    if (init && oldInvApplyInfoList && oldInvApplyInfoList.length !== 0) {
      return;
    }
    const { resumeBusinessDate, resumeTransSeqno, selectTransSeqno, invApplyInfoListCooperate } = this.state;
    const {
      invApplyInfoList: newInvApplyInfoList, invDataStatus, invStartYear, transferOrgName, transferMerchantMobileNo, needDiversionMerchantInfo, diversionMerchantInfoList, totalCnt,
    } = await Dispatch.repayment.queryRecords({
      pageSize,
      resumeBusinessDate,
      resumeTransSeqno,
      filterCancelCustomerData: true, // 过滤已注销客户数据，返回已还息费减免
    }) || {};
    this.transferOrgName = transferOrgName;
    this.transferMerchantMobileNo = transferMerchantMobileNo;
    const { showFlowCardSwitch, trackPageId } = this.props || {};
    this.needShowDiversionMerchantInfo = needDiversionMerchantInfo === 'Y' && showFlowCardSwitch === 'ALL_OPEN';
    this.diversionMerchantInfoList = diversionMerchantInfoList || [];
    if (init) {
      dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPageId}EnterPage`, beaconContent: { cus: { custType: this.needShowDiversionMerchantInfo ? '1' : '0' } } });
    }
    const invApplyInfoList = (oldInvApplyInfoList || []).concat(newInvApplyInfoList) || [];
    let tempInvApplyInfoListOnlyZL = [];
    let showInvApplyInfoListOnlyZL = [];
    let cooperateCnt = 0;
    invApplyInfoList.forEach((invApplyInfo) => {
      if ((invApplyInfo || {}).invoiceApplyStatus === '1' || (invApplyInfo || {}).invoiceApplyStatus === '2') {
        showInvApplyInfoListOnlyZL.push(invApplyInfo);
        return;
      }
      if (invApplyInfo && invApplyInfo.transactionType === '01' && (invApplyInfo.invAmt || '').indexOf('-') < 0) {
        invApplyInfo.invAmt = `${(-Number(invApplyInfo.invAmt)).toFixed(2)}`;
      }
      // 已还息费减免交易记录自动勾选（不包含非招联出资部分的记录）
      if (invApplyInfo && invApplyInfo.transactionType === '01' && invApplyInfo.needCooperateInvoiceFlag === 'F') {
        selectTransSeqno.add(invApplyInfo.transSeqno);
      }
      // 记录开票机构为招联（即非置灰）的可开票列表
      if (invApplyInfo && invApplyInfo.needCooperateInvoiceFlag === 'F') {
        tempInvApplyInfoListOnlyZL.push(invApplyInfo);
        showInvApplyInfoListOnlyZL.push(invApplyInfo);
        return;
      }
      // 记录开票机构为合作方列表
      if (invApplyInfo && invApplyInfo.needCooperateInvoiceFlag === 'Y') {
        cooperateCnt += 1;
        if (invApplyInfoListCooperate.map((item) => item.merchantNo).includes(invApplyInfo.merchantNo)) {
          const tempInvApplyInfoList = invApplyInfoListCooperate.filter((item) => item.merchantNo === invApplyInfo.merchantNo)[0].invApplyInfoList;
          if (!tempInvApplyInfoList.map((item) => item.transSeqno).includes(invApplyInfo.transSeqno)) {
            invApplyInfoListCooperate.filter((item) => item.merchantNo === invApplyInfo.merchantNo)[0].invApplyInfoList.push(invApplyInfo);
          }
        } else {
          invApplyInfoListCooperate.push({
            merchantNo: invApplyInfo.merchantNo,
            merchantOrgName: invApplyInfo.merchantOrgName,
            invApplyInfoList: [invApplyInfo],
          });
        }
      }
    });
    let noInvApplyInfoList = false;
    if (!invApplyInfoList.length && invApplyInfoList.length <= 0) {
      noInvApplyInfoList = true;
      this.setState({ noticeText: '暂无可开票交易' });
    }
    if (`${invDataStatus}` === '1') {
      this.setState({
        modalContent: `您在${invStartYear}年1月1日至前日暂时没有此类交易`
      });
      return noInvApplyInfoList;
    } else if (`${invDataStatus}` === '2') {
      this.setState({
        modalContent: `您在${invStartYear}年1月1日至前日的还款交易均已提交开票申请。具体进度请查看“开票历史”`
      });
      return noInvApplyInfoList;
    }
    const noMoreFlag = ((newInvApplyInfoList || []).length < pageSize);
    this.setState({
      invApplyInfoList,
      invApplyInfoListOnlyZL: tempInvApplyInfoListOnlyZL,
      invApplyInfoListOnlyZLShow: showInvApplyInfoListOnlyZL,
      resumeBusinessDate:
        newInvApplyInfoList && newInvApplyInfoList.length > 0
          ? (newInvApplyInfoList[newInvApplyInfoList.length - 1] || {}).businessDate : '',
      resumeTransSeqno:
        newInvApplyInfoList && newInvApplyInfoList.length > 0
          ? (newInvApplyInfoList[newInvApplyInfoList.length - 1] || {}).transSeqno : '',
      noMoreFlag,
      pageSelectFlag: selectTransSeqno && selectTransSeqno.size !== 0 && selectTransSeqno.size === tempInvApplyInfoListOnlyZL.length, // 自动勾选条数等于本页总的开票机构为招联条数，且不等于0则勾选本页全选按钮
      invApplyInfoListCooperate,
      totalCnt,
      cooperateCnt,
      showZlList: !!showInvApplyInfoListOnlyZL.length,
      showCooperateList: !!invApplyInfoListCooperate.length,
    }, () => {
      const { allSelectFlag, pageSelectFlag, noMoreFlag: noMoreFlagState } = this.state;
      if (allSelectFlag || pageSelectFlag) {
        this.addAllPage(true);// 下一分页数据也加入selectTransSeqno
      }
      if (pageSelectFlag && noMoreFlagState) {
        this.setState({ allSelectFlag: true });
      }
    });
    Taro.stopPullDownRefresh();
  }

  selectAll = () => {
    // 无可选记录时，点击勾选框无效
    const { invApplyInfoListOnlyZL = [] } = this.state;
    if (invApplyInfoListOnlyZL.length <= 0) return;
    this.setState((state) => ({ allSelectFlag: !state.allSelectFlag }), () => {
      if (this.state.allSelectFlag) {
        this.addAllPage();
        // 调全选接口，交易数、额度数改变
      } else {
        this.clearAllPage();
        // 交易数、总额度0
      }
    });
  }

  addAllPage = (isPullFresh) => {
    const transSeqnoList = new Set();
    const { invApplyInfoList } = this.state;
    invApplyInfoList.forEach((item) => {
      // 上拉刷新加载下一页时，自动勾选已还息费减免交易记录（不包含非招联出资部分的记录）
      if ((item || {}).invoiceApplyStatus === '1' || (item || {}).invoiceApplyStatus === '2') {
        return;
      } else if (isPullFresh && item && item.transactionType === '01' && item.needCooperateInvoiceFlag === 'F') {
        transSeqnoList.add(item.transSeqno);
      } else if (item && item.needCooperateInvoiceFlag === 'F') {
        // 非上拉刷新，记录开票机构为招联（即非置灰）的可开票列表
        transSeqnoList.add(item.transSeqno);
      }
    });
    this.setState({
      pageSelectFlag: true,
      selectTransSeqno: transSeqnoList
    });
  }

  clearAllPage = () => {
    this.setState({
      pageSelectFlag: false,
      selectTransSeqno: new Set()
    });
  }

  selectPageAll = () => {
    // 无可选记录时，点击勾选框无效
    const { invApplyInfoListOnlyZL = [] } = this.state;
    if (invApplyInfoListOnlyZL.length <= 0) return;
    this.setState((state) => ({ pageSelectFlag: !state.pageSelectFlag }), () => {
      if (this.state.pageSelectFlag) {
        this.addAllPage();
        if (this.state.noMoreFlag) {
          this.setState({
            allSelectFlag: true
          });
        } else {
          this.setState({
            allSelectFlag: false
          });
        }
        // 交易数、额度数改变
      } else {
        this.clearAllPage();
        this.setState({
          allSelectFlag: false
        });
        // 交易数、额度数改变
      }
    });
  }

  onItemClick = (transSeqno, i) => {
    const { selectTransSeqno, invApplyInfoList, invApplyInfoListOnlyZL } = this.state;
    if (!selectTransSeqno.has(transSeqno)) {
      selectTransSeqno.add(transSeqno);
      if (invApplyInfoList && invApplyInfoList[i] && invApplyInfoList[i].transactionType !== '01') {
        invApplyInfoList.forEach((invApplyInfo, index) => {
          if (invApplyInfo && invApplyInfo.transactionType === '01' && index < i) {
            selectTransSeqno.add(invApplyInfo.transSeqno);
          }
        });
      }
    } else {
      let availUnChecked = true;
      if (invApplyInfoList && invApplyInfoList[i] && invApplyInfoList[i].transactionType === '01') {
        invApplyInfoList.forEach((invApplyInfo, index) => {
          if (index > i && invApplyInfo && invApplyInfo.transactionType !== '01' && (selectTransSeqno || {}).has(invApplyInfo.transSeqno)) {
            availUnChecked = false;
          }
        });
      }
      if (availUnChecked) {
        selectTransSeqno.delete(transSeqno);
      }
    }
    if ((selectTransSeqno || {}).size === (invApplyInfoListOnlyZL || []).length) {
      this.setState({
        pageSelectFlag: true,
        allSelectFlag: this.state.noMoreFlag
      });
    } else {
      this.setState({ pageSelectFlag: false, allSelectFlag: false });
    }
    this.setState({ selectTransSeqno });
    // 最后计算交易数、额度总数，注意setstate异步性
  }

  sumInvoiceAmt = (invApplyInfoList, selectTransSeqno) => {
    let sumCredit = 0;
    invApplyInfoList.forEach((item) => {
      if (selectTransSeqno.has(item.transSeqno)) {
        sumCredit += Number(item.invAmt);
      }
    });
    return Number(sumCredit).toFixed(2);
  }

  allInvoiceCompany = (invApplyInfoList, selectTransSeqno) => {
    const invoiceCompanyInfoList = [];
    let needCooperateInvoiceFlag = false;
    let companisList = [];
    let notNeedCooperatieInvoiceLimit = 0;
    let hasZL = false;
    invApplyInfoList.forEach((item) => {
      if (selectTransSeqno.has(item.transSeqno)) {
        if (item && item.needCooperateInvoiceFlag === 'Y') needCooperateInvoiceFlag = true;
        if (item && item.needCooperateInvoiceFlag === 'F') {
          notNeedCooperatieInvoiceLimit += Number(item.invAmt);
          hasZL = true;
        }
        invoiceCompanyInfoList.push({
          merchantNo: item && item.merchantNo,
          merchantOrgName: item && item.merchantOrgName,
          businessDate: item && item.businessDate
        });
        !companisList.includes(item && item.merchantOrgName) && companisList.push(item && item.merchantOrgName);
      }
    });
    if (notNeedCooperatieInvoiceLimit <= 0 && hasZL) {
      let merchantOurSelves = (invApplyInfoList || []).find(item => item && (selectTransSeqno || {}).has(item.transSeqno) && item.needCooperateInvoiceFlag === 'F') || {};
      const { merchantOrgName = '' } = merchantOurSelves;
      if (JSON.stringify(merchantOurSelves) !== '{}' && companisList.includes(merchantOrgName)) {
        companisList = companisList.filter(item => item !== merchantOrgName);
      }
    }
    (STORE.submitData || {}).needCooperateInvoiceFlag = needCooperateInvoiceFlag;
    // 如果招联部分加起来小于等于零，不需要展示机构名称
    return { invoiceCompanyInfoList, companisList };
  }

  sumInvoiceCnt = (selectTransSeqno, allSelectFlag, length) => {
    if (allSelectFlag) { // 全选(返回非置灰记录条数，即开票机构为招联的可开票列表的长度)
      return length;
    }
    return (selectTransSeqno || {}).size || 0;
  }

  closeModal = () => {
    this.setState({ modalContent: '' });
  }

  toggleTipModal = (isShow, info) => {
    // isShow === '0' 时关闭弹窗，此时不要设置 tipContent='', 否则会闪一下再关闭
    if (isShow === '0') {
      this.setState({ showTip: isShow });
      return;
    }
    let tipContent = '';
    if (isShow === '1') {
      tipContent = '该借据为联合贷借据，不支持自助开具，若需要开具，请联系客服处理。';
    } else if (isShow === '2') {
      tipContent = `${Utility.tranformDate(info.businessDate)}已退减息费${(-Number(info.invAmt)).toFixed(2)}元`;
    } else if (isShow === '3') {
      tipContent = '您勾选的开票总金额小于等于0，暂不支持开票，如有疑问请联系在线客服';
    } else if (isShow === '4') {
      tipContent = '该笔交易不支持自助开票，请联系客服';
    } else if (isShow === '5') {
      tipContent = '当前交易不支持自助开票，请联系客服';
    } else if (isShow === '6') {
      tipContent = '该交易暂不支持开具发票，如有疑问可联系人工客服95786';
    } else if (isShow === '7') {
      tipContent = `您在招联的该笔还款金额已转让至<span style="color:#FF890E;">${this.transferOrgName}</span>，该交易不支持在招联金融开具发票，如需开具发票，请联系<span style="color:#FF890E;">${this.transferOrgName}${this.transferMerchantMobileNo}</span>`;
    }
    // 若同时setState，在IOS会出现弹窗组件中content更新不及时，弹窗内容为空的情况
    this.setState({ tipContent }, () => {
      this.setState({
        showTip: isShow
      });
    });
  }

  onSubmit = () => {
    const {
      allSelectFlag, selectTransSeqno, invApplyInfoList, invApplyInfoListOnlyZL
    } = this.state;
    (STORE.submitData || {}).totalInvAmt = this.sumInvoiceAmt(invApplyInfoList, selectTransSeqno);
    const { invoiceCompanyInfoList, companisList } = this.allInvoiceCompany(invApplyInfoList, selectTransSeqno);
    (STORE.submitData || {}).invoiceCompany = invoiceCompanyInfoList;
    (STORE.submitData || {}).companyList = companisList;
    if (!allSelectFlag) (STORE.submitData || {}).allSelectFlag = '';
    if (invApplyInfoListOnlyZL && invApplyInfoListOnlyZL.length <= 0) {
      // 开票机构为招联（即非置灰）的可开票列表长度小于等于0，说明都是置灰的，不能提交并弹窗提示
      this.toggleTipModal('5');
      return;
    }
    if ((STORE.submitData || {}).totalInvAmt <= 0) {
      this.toggleTipModal('3');
      return;
    } else if (allSelectFlag) {
      (STORE.submitData || {}).allSelectFlag = '1';
    } else if (selectTransSeqno && selectTransSeqno.size > 0) {
      (STORE.submitData || {}).selectTransSeqno = Array.from(selectTransSeqno);
    } else {
      Madp.showToast({
        title: '您还未选择待开票记录',
        icon: 'none'
      });
      return;
    }
    Madp.navigateTo({
      url: '/pages/self-invoice/invoiceSet',
    });
  }

  showDiversionModal = (item) => {
    const { trackPageId } = this.props || {};
    const { merchantNo, merchantShortName, merchantPhoneNo } = item || {};
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPageId}DiversionModalClick`, beaconContent: { cus: { merchantNo, merchantShortName } } });
    this.diversionUnsupportedInvoiceParam = {
      imgUrl: diversionModalInfo,
      title: '暂不支持自助开票',
      desc: `如有需要可联系<span style="color:#FF8844;">${merchantShortName}客服</span>协助处理`,
      contactPhone: merchantPhoneNo,
      bubbleImgUrl: diversionModalBubble,
      cancelText: '暂不开票',
      confirmText: `联系${merchantShortName}客服`,
      onCancel: () => {
        dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPageId}DiversionModalCancel`, beaconContent: { cus: { merchantNo, merchantShortName } } });
        this.setState({ showDiversionUnsupportedInvoice: false });
      },
      onConfirm: () => {
        dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPageId}DiversionModalConfirm`, beaconContent: { cus: { merchantNo, merchantShortName } } });
        Madp.makePhoneCall({ phoneNumber: merchantPhoneNo });
      },
    };
    this.setState({
      showDiversionUnsupportedInvoice: true,
    });
    dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPageId}DiversionModalShow`, beaconContent: { cus: { merchantNo, merchantShortName } } });
  }

  render() {
    const { navHeight = 0 } = this.props;
    const {
      pageSelectFlag, allSelectFlag,
      selectTransSeqno, invApplyInfoList,
      noMoreFlag, modalContent,
      noticeText, showTip,
      tipContent, invApplyInfoListOnlyZL, invApplyInfoListOnlyZLShow,
      invApplyInfoListCooperate, totalCnt, cooperateCnt, showZlList, showCooperateList, showDiversionUnsupportedInvoice,
    } = this.state;
    const { needShowDiversionMerchantInfo, diversionMerchantInfoList, diversionUnsupportedInvoiceParam } = this;
    const itemList = (
      <MUList>
        {
          invApplyInfoListOnlyZLShow.map((item, i) => (
            <RecordListItem
              InvApplyInfo={item}
              selected={selectTransSeqno && selectTransSeqno.has(item && item.transSeqno) || allSelectFlag}
              onSelected={() => this.onItemClick(item.transSeqno, i)}
              onTipClick={(value, info) => { this.toggleTipModal(value, info); }}
            />))
        }
      </MUList>
    );
    return (
      <MUView>
        {
          (invApplyInfoList || []).length <= 0 ? (
            <MUView className="trade-record-list1">
              {needShowDiversionMerchantInfo ? (
                <MUView>
                  {diversionMerchantInfoList.map((item) => (
                    <MUView className="trade-record-area" onClick={() => this.showDiversionModal(item)}>
                      <MUView className="area__brand">
                        <MUView className="area__brand__check area__brand__disabled" />
                        <MUText className="area__brand__text">{item.merchantShortName}{item.merchantCooperationMode === '0' ? '及其合作金融机构' : ''}借款</MUText>
                        <MUIcon className="area__brand__info" value="info" size="16" color="#cacaca" />
                      </MUView>
                    </MUView>
                  ))}
                </MUView>
              ) : (
                <View className="empty-sign">
                  <i />
                  <MUText className="null_p">{noticeText}</MUText>
                </View>
              )}

              {(modalContent || []).length > 0 && (
                <MUModal
                  beaconId="noticeModal"
                  isOpened
                  content={modalContent}
                  confirmText="知道了"
                  onConfirm={this.closeModal}
                />
              )}
            </MUView>
          ) : (
            <MUView className="fill-view" style={{ height: `calc(100vh + -${Taro.pxTransform(100 + navHeight)})` }}>
              {(invApplyInfoListOnlyZL || []).length > 0 ? (
                <MUView className="trade-record-list">
                  <MUView className="trade-record-area">
                    <MUView className="area__brand">
                      <MUView className="area__brand__check" onClick={this.selectAll} >
                        <MUIcon value={allSelectFlag ? 'checked-box' : 'unchecked-box'} size="16" color="#3477FF" />
                      </MUView>
                      <MUText className="area__brand__text">招联金融借款</MUText>
                    </MUView>
                    <MUView className="area__count" onClick={() => this.setState({ showZlList: !showZlList })}>
                      <MUText className="area__count__text">
                        共{Number(totalCnt || 0) - cooperateCnt}笔
                      </MUText>
                      <MUIcon className="area__count__arrow" value={`arrow-${showZlList ? 'down' : 'up'}`} size="16" color="#979797" />
                    </MUView>
                  </MUView>
                  {showZlList ? <MUView className="trade-record-divide" /> : null}
                  {(showZlList && noMoreFlag) ? (
                    <MUScrollView
                      scrollY
                      className="scroll-view"
                    >
                      { itemList}
                      <MUText className="no-more-desc">暂无更多记录</MUText>
                    </MUScrollView>
                  ) : null}
                  {(showZlList && (!noMoreFlag)) ? (
                    <MUPullToRefresh direction="up" onRefresh={this.getRecords}>
                      <MUScrollView
                        scrollY
                        className="scroll-view"
                      >
                        {itemList}
                        <MUText className="no-more-desc">上拉加载更多</MUText>
                      </MUScrollView>
                    </MUPullToRefresh>
                  ) : null}
                </MUView>
              ) : null}
              {(invApplyInfoListCooperate || []).length > 0 ? (
                <MUView>
                  {invApplyInfoListCooperate.map((item) => (
                    <MUView className="trade-record-list">
                      <MUView className="trade-record-area">
                        <MUView className="area__brand">
                          <MUView className="area__brand__check area__brand__disabled" />
                          <MUText className="area__brand__text">{item.merchantOrgName}借款</MUText>
                        </MUView>
                        <MUView className="area__count" onClick={() => this.setState({ showCooperateList: !showCooperateList })}>
                          <MUText className="area__count__text">
                            共{item.invApplyInfoList.length}笔
                          </MUText>
                          <MUIcon className="area__count__arrow" value={`arrow-${showCooperateList ? 'down' : 'up'}`} size="16" color="#979797" />
                        </MUView>
                      </MUView>
                      {showCooperateList ? <MUView className="trade-record-divide" /> : null}
                      {(showCooperateList && noMoreFlag) ? (
                        <MUScrollView
                          scrollY
                          className="scroll-view"
                        >
                          {
                            item.invApplyInfoList.map((itemItem) => (
                              <RecordListItem
                                InvApplyInfo={itemItem}
                                onTipClick={(value, info) => { this.toggleTipModal(value, info); }}
                              />))
                          }
                          {(invApplyInfoListOnlyZL || []).length <= 0 ? (<MUText className="no-more-desc">暂无更多记录</MUText>) : null}
                        </MUScrollView>
                      ) : null}
                      {(showCooperateList && (!noMoreFlag)) ? (
                        <MUPullToRefresh direction="up" onRefresh={this.getRecords}>
                          <MUScrollView
                            scrollY
                            className="scroll-view"
                          >
                            {
                              item.invApplyInfoList.map((itemItem) => (
                                <RecordListItem
                                  InvApplyInfo={itemItem}
                                  onTipClick={(value, info) => { this.toggleTipModal(value, info); }}
                                />))
                            }
                            {(invApplyInfoListOnlyZL || []).length <= 0 ? (<MUText className="no-more-desc">上拉加载更多</MUText>) : null}
                          </MUScrollView>
                        </MUPullToRefresh>
                      ) : null}
                    </MUView>
                  ))}
                </MUView>
              ) : null}
              {needShowDiversionMerchantInfo ? (
                <MUView>
                  {diversionMerchantInfoList.map((item) => (
                    <MUView className="trade-record-area trade-record-list2" onClick={() => this.showDiversionModal(item)}>
                      <MUView className="area__brand">
                        <MUView className="area__brand__check area__brand__disabled" />
                        <MUText className="area__brand__text">{item.merchantShortName}{item.merchantCooperationMode === '0' ? '及其合作金融机构' : ''}借款</MUText>
                        <MUIcon className="area__brand__info" value="info" size="16" color="#cacaca" />
                      </MUView>
                    </MUView>
                  ))}
                </MUView>
              ) : null}
              <RecordBottom
                count={this.sumInvoiceCnt(selectTransSeqno, allSelectFlag, invApplyInfoListOnlyZL.length)}
                total={this.sumInvoiceAmt(invApplyInfoList, selectTransSeqno)}
                pageSelectFlag={pageSelectFlag}
                allSelectFlag={allSelectFlag}
                onSelectPageAll={this.selectPageAll}
                onSelectedAll={this.selectAll}
                onSubmit={this.onSubmit}
              />
              <MUModal
                isOpened={showTip !== '0'}
                closeOnClickOverlay={false}
                title="温馨提示"
                content={tipContent}
                confirmText="我知道了"
                onConfirm={e => this.toggleTipModal('0')}
              />
            </MUView>
          )
        }
        {/* 导流不支持弹窗 */}
        <DiversionUnsupportedInvoiceModal
          isOpened={showDiversionUnsupportedInvoice}
          diversionUnsupportedInvoicParam={diversionUnsupportedInvoiceParam}
        />
      </MUView>
    );
  }
}
