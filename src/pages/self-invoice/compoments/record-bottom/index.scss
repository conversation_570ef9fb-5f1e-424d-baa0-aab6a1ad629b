.container {
  width: 100%;
  height: 248px;
  position: fixed;
  z-index: 999;
  bottom: 0;

  &-nochat {
    height: 168px;
  }

  .record-chat {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
  }

  .record-bottom {
    background: #fff;
    height: 168px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
  
    &-tip {
      text-align: left;
      padding: 14px 0 14px 30px;
      height: 39px;
      color: #666666;
      border-bottom: 1PX solid #E5E5E5;
      font-size: 26px;
      line-height: 38px;
      font-weight: 400;
  
      &-highlight {
        color: #FF8800;
      }
    }
  
    &-check {
      // margin-left: 30px;
      height: 49px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      height: 100%;
  
      &-txt {
        margin-left: 30px;
        font-size: 28px;
        line-height: 40px;
        color: #333;
        align-items: center;
        justify-content: center;

        .mu-text__default {
          padding-left: 16px;
        }
      }
  
      &-notice {
        width: 110px;
        height: 32px;
        margin-left: 30px;
        // background: #EEEEEE;
        border-radius: 16px;
        color: #808080;
        font-size: 20px;
        text-align: center;
      }
  
      &-btn {
        margin-right: 24px !important; 
        width: 180px !important;
        height: 80px !important;
        font-size: 28px !important;
        color: #FFF !important;
        border-radius: 40px !important;
        background: #3477FF !important;
        text-align: center;
      }
    }
  }
}