import { Component } from '@tarojs/taro';
import {
  MUView, MUText, MUButton, MUIcon
} from '@mu/zui';
import { ChatEntry } from '@mu/chat-entry-component';
import { isMuapp, isMuHarmonyApp, isCmb } from '@mu/madp-utils';
import Utility from '@utils/Utility';
import PropTypes from 'prop-types';
import './index.scss';

export default class RecordBottom extends Component {
  static propTypes = {
    count: PropTypes.number,
    total: PropTypes.number,
    allSelectFlag: PropTypes.bool,
    pageSelectFlag: PropTypes.bool,
    onSelectPageAll: PropTypes.func,
    onSelectedAll: PropTypes.func,
    onSubmit: PropTypes.func
  }

  static defaultProps = {
    count: 0,
    total: 0.00,
    allSelectFlag: false,
    pageSelectFlag: false,
    onSelectPageAll: () => { },
    onSelectedAll: () => { },
    onSubmit: () => { }
  }

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  // 展示客服组件判断（非APP(不包括鸿蒙3APP）和非招行渠道展示在页面底部）
  showChatEntry = () => !((isMuapp() && !isMuHarmonyApp()) || isCmb());
  render() {
    const {
      count, total, onSubmit, allSelectFlag, pageSelectFlag, onSelectPageAll, onSelectedAll
    } = this.props;
    return (
      <MUView className={`container ${this.showChatEntry() ? '' : 'container-nochat'}`} beaconId="bottom">
        {this.showChatEntry() && (<MUView className="record-chat"><ChatEntry busiEntrance={Utility.getBusiEntrance()} extraParam={{ needLogin: 1 }} /></MUView>)}
        <MUView className="record-bottom">
          <MUText className="record-bottom-tip">
            <span className="record-bottom-tip-highlight">
              {count}
            </span>
            笔交易流水，共
            <span className="record-bottom-tip-highlight">
              {total}
            </span>
            元
          </MUText>
          <MUView className="record-bottom-check">
            <MUView className="record-bottom-check-txt" onClick={onSelectPageAll}>
              <MUIcon
                beaconId="selectALLThisPage"
                value={pageSelectFlag ? 'checked' : 'unchecked'}
                size="19"
                color={pageSelectFlag ? '#577BFF' : '#C5CDD4'}
                customStyle={{ marginBottom: '3px' }}
              />
              <MUText>本页全选</MUText>
            </MUView>
            <MUView className="record-bottom-check-txt" onClick={onSelectedAll}>
              <MUIcon
                beaconId="selectALL"
                value={allSelectFlag ? 'checked' : 'unchecked'}
                size="19"
                color={allSelectFlag ? '#577BFF' : '#C5CDD4'}
                customStyle={{ marginBottom: '3px' }}
              />
              <MUText>全部全选</MUText>
            </MUView>
            <MUButton beaconId="nextStep" className="record-bottom-check-btn" onClick={onSubmit}>下一步</MUButton>
          </MUView>
        </MUView>
      </MUView>
    );
  }
}
