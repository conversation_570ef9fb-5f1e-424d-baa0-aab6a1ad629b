import { Component } from '@tarojs/taro';
import {
  MUView, MUText, MU<PERSON>utton
} from '@mu/zui';
import { View } from '@tarojs/components';
import PropTypes from 'prop-types';
import './index.scss';

export default class mailDialog extends Component {
  static propTypes = {
    name: PropTypes.string,
    titleType: PropTypes.string,
    credit: PropTypes.string,
    mail: PropTypes.string,
    onCancel: PropTypes.func,
    onSubmit: PropTypes.func

  }

  static defaultProps = {
    name: '--',
    titleType: '--',
    credit: '----',
    mail: '------------------------',
    onCancel: () => { },
    onSubmit: () => { }
  }

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  render() {
    const {
      name, titleType, credit, mail, onCancel, onSubmit
    } = this.props;
    return (
      <MUView className="mail-dialog" >
        <MUText className="mail-dialog-title">发票信息</MUText>
        <View className="mail-dialog-input">
          <View className="mail-dialog-input-leftView">个人姓名</View>
          <View className="mail-dialog-input-rightView">{name}</View>
        </View>

        <View className="mail-dialog-input">
          <View className="mail-dialog-input-leftView">抬头类型</View>
          <View className="mail-dialog-input-rightView">{titleType}</View>
        </View>

        <View className="mail-dialog-input">
          <View className="mail-dialog-input-leftView">发票金额</View>
          <View className="mail-dialog-input-rightView">{credit}</View>
        </View>

        <View className="mail-dialog-input">
          <div className="mail-dialog-input-leftView">电子邮箱</div>
          <View className="mail-dialog-input-rightView">{mail}</View>
        </View>

        <div className="mail-dialog-btn">
          <MUButton
            beaconId="cancelSubmitMail"
            type="secondary"
            className="mu-button--popup mail-dialog-btn-item"
            onClick={onCancel}
          >
            取消
          </MUButton>
          <MUButton
            beaconId="confirmSubmitMail"
            type="primary"
            className="mu-button--popup mail-dialog-btn-item"
            onClick={onSubmit}
          >
            确认提交
          </MUButton>
        </div>
      </MUView>
    );
  }
}
