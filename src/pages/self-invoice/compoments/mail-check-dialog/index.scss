.mail-dialog {
  // height: 648px;
  display: flex;
  flex-direction: column;

  // justify-content: center;
  &-title {
    font-size: 36px;
    color: #333333;
  }

  &-input {
    display: flex;
    align-items: center;
    margin-top: 10px;

    &-leftView {
      color: #808080;
      font-size: 32px;
      margin-left: 30px;
      width: 180px;
      text-align: left;
    }

    &-rightView {
      color: #333333;
      display: flex;
      align-items: center;
      font-size: 32px;
      line-height: 32px;
      margin-left: 20px;
      margin-right: 20px;
      width: 300px; //定义宽度后自动换行https://www.cnblogs.com/Mblog/archive/2009/12/21/1628670.html
      word-break: break-all;
      text-align: left;
    }
  }

  &-divider {
    height: 1px;
    background: #D8D8D8;
  }

  &-notice {
    margin-top: 20px;
    line-height: normal;
    margin-left: 15px;
    margin-right: 15px;
    font-size: 24px;
    color: #808080;
    text-align: start;
  }

  &-btn {
    margin-top: 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-item {
      height: 88px;
      width: 230px;
    }
  }
}