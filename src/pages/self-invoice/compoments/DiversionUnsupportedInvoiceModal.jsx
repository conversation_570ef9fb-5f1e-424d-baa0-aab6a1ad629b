/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import {
  MUModal,
  MUView,
  MUImage,
  MURichText,
} from '@mu/zui';

/**
 * 导流不支持自助开票弹窗
 */
export default function DiversionUnsupportedInvoiceModal({
  isOpened,
  diversionUnsupportedInvoicParam = {},
}) {
  return (
    <MUModal
      closeOnClickOverlay={false}
      isOpened={isOpened}
      className="diversion-unsupported-invoice"
    >
      <MUView className="diversion-unsupported-invoice__content">
        <MUView className="diversion-unsupported-invoice__content__tip">
          <MUImage src={diversionUnsupportedInvoicParam.imgUrl} />
        </MUView>
        <MUView className="diversion-unsupported-invoice__content__title">{diversionUnsupportedInvoicParam.title}</MUView>
        <MURichText
          className="diversion-unsupported-invoice__content__desc"
          nodes={diversionUnsupportedInvoicParam.desc}
        />
        {diversionUnsupportedInvoicParam.supply ? (
          <MUView className="diversion-unsupported-invoice__content__supply">
            {diversionUnsupportedInvoicParam.supply}
          </MUView>
        ) : null}
        <MUView className="diversion-unsupported-invoice__content__buttons">
          <MUView className="button__bubble">
            <MUView className="button__bubble__img"><MUImage src={diversionUnsupportedInvoicParam.bubbleImgUrl} /></MUView>
            <MUView className="button__bubble__text">{diversionUnsupportedInvoicParam.contactPhone}</MUView>
          </MUView>
          <MUView
            className="button__confirm"
            onClick={() => diversionUnsupportedInvoicParam.onConfirm()}
          >
            {diversionUnsupportedInvoicParam.confirmText}
          </MUView>
          <MUView
            className="button__cancel"
            onClick={() => diversionUnsupportedInvoicParam.onCancel()}
          >
            {diversionUnsupportedInvoicParam.cancelText}
          </MUView>
        </MUView>
      </MUView>
    </MUModal>
  );
}
