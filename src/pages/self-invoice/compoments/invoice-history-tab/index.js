import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';

import {
  MUText, MUPullToRefresh, MUView
} from '@mu/zui';
import Dispatch from '@api/actions';
import InvoiceHistoryItem from '../invoice-history-item';
import './index.scss';

const pageSize = '15';
export default class HistoryListTab extends Component {
  constructor(props) {
    super(props);
    this.state = {
      invInfoList: [],
      noMoreFlag: false,
      pageNo: 1
    };
  }

  async componentDidMount() {
    // await this.getRecords(true);
  }

  getRecords = async (init = false, identified = true) => {
    if (!identified) {
      return;
    }
    const { invInfoList: oldInvInfoList, pageNo } = this.state;
    if (init && oldInvInfoList && oldInvInfoList.length !== 0) {
      return;
    }
    const { invApplyInfoList: newInvInfoList } = await Dispatch.repayment.queryInvHistory({
      pageSize,
      filterCancelCustomerData: false,
      needDetailFlag: '1',
      useSence: 'APP',
      pageNo: String(pageNo),
    }) || {};
    const invInfoList = oldInvInfoList.concat(newInvInfoList || []);
    const noMoreFlag = ((newInvInfoList || []).length < pageSize);
    this.setState({
      invInfoList,
      noMoreFlag,
      pageNo: pageNo + 1
    });
    Taro.stopPullDownRefresh();
  }

  render() {
    const { invInfoList, noMoreFlag } = this.state;
    const itemList = (
      <MUView>
        {
          invInfoList.map((item) => (
            <InvoiceHistoryItem InvoiceSummary={item} />))
        }
      </MUView>
    );
    return (
      <View className="history-list">
        {
          (invInfoList || []).length <= 0 ? (
            <View className="empty-sign">
              <i />
              <MUText className="null_p">暂无开票历史</MUText>
            </View>
          ) : (
            <View className="list-group">
              {
                noMoreFlag ? (
                  <View>
                    { itemList }
                    <MUText className="no-more-desc">没有更多历史了</MUText>
                  </View>
                ) : (
                  <MUPullToRefresh direction="up" onRefresh={this.getRecords}>
                    { itemList }
                    <MUText className="pull-up-desc">上拉加载更多</MUText>
                  </MUPullToRefresh>
                )
              }
            </View>
          )
        }
      </View>
    );
  }
}
