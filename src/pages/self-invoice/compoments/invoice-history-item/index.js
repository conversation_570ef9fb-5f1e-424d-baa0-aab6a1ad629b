import { Component } from '@tarojs/taro';
import { <PERSON>UView, MUIcon, MUText } from '@mu/zui';
import PropTypes from 'prop-types';
import './index.scss';
import Madp from '@mu/madp';
import { STORE } from '../../store';
import Utility from '../../../../utils/Utility';

export default class InvoiceHistoryItem extends Component {
  static propTypes = {
    InvoiceSummary: PropTypes.shape({
      invTotalAmt: PropTypes.string,
      invApplyTime: PropTypes.string,
      status: PropTypes.string,
      invDetails: PropTypes.array,
    })
  }

  static defaultProps = {
    InvoiceSummary: {
      invTotalAmt: '--',
      invApplyTime: '--------',
      status: 'N',
      invDetails: [],
    }
  }

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  onItemClick = () => {
    const { InvoiceSummary: { invMerchantDetails, needCooperateInvoiceFlag, status } = {} } = this.props;
    // 任一机构开票成功，就可以进入开发票详情页
    let canEnterDetailFlag = false;
    const successInvoice = (invMerchantDetails || []).find(item => item && (item.invoiceStatus === '2' || item.invoiceStatus === '4'));
    if (needCooperateInvoiceFlag === 'Y') {
      if (successInvoice) canEnterDetailFlag = true;
      if ((status === 'N' || status === 'D' || status === 'R') && !canEnterDetailFlag) {
        Madp.showToast({
          title: '系统正在开票，请稍后查看',
          icon: 'none'
        });
        return;
      }
    } else {
      if (status === 'N' || status === 'D' || status === 'R') {
        Madp.showToast({
          title: '系统正在开票，请稍后查看',
          icon: 'none'
        });
        return;
      }
    }
    STORE.selectedHistoryRecord = this.props.InvoiceSummary;
    Madp.navigateTo({
      url: '/pages/self-invoice/invoiceHistoryDetail'
    });
  }

  render() {
    const { InvoiceSummary: { invApplyTime, invTotalAmt, status } = {} } = this.props;
    const statusText = {
      true: '开票失败',
      [status === 'Y']: '已开票',
      [status === 'N' || status === 'D' || status === 'R']: '开票中',
    }['true'];
    return (
      <MUView className="invoice-history-item" onClick={this.onItemClick}>
        <MUView className="invoice-history-item-desc">
          <MUText className="invoice-history-item-desc-amt">{`${invTotalAmt}元`}</MUText>
          <MUText className="invoice-history-item-desc-time">{Utility.tranformDate(invApplyTime)}</MUText>
        </MUView>
        <p className="invoice-history-item-status" style={{ color: (status === 'Y' || status === 'F') ? '#A6A6A6' : '#3477FF' }}>
          {statusText}
        </p>
        <MUIcon beaconId="selectHistoryItem" value="arrow-right" size="15" color="#CACACA" className="invoice-history-item-arrow" />
      </MUView>
    );
  }
}
