import Taro, { Component } from '@tarojs/taro';
import {
  MUTabs, MUTabsPane, MUView, MUModal
} from '@mu/zui';
import { isMuapp, isCmb, isMuHarmonyApp, Url } from '@mu/madp-utils';
import { ChatEntry } from '@mu/chat-entry-component';
import Madp from '@mu/madp';
import { track, EventTypes, dispatchTrackEvent, disableTrackAlert } from '@mu/madp-track';
import { STORE } from './store';
import './index.scss';
import RecordListTab from './compoments/record-list-tab';
import RecordHistoryTab from './compoments/invoice-history-tab';
import { urlDomain } from '@utils/url_config';
import Dispatch from '@api/actions';
import Utility from '@utils/Utility';
import NavBarView from '@components/nav-bar-view';
import { getLoginInfo } from '@mu/business-basic';

disableTrackAlert();
@track({ event: EventTypes.PO }, {
  pageId: 'invoiceIndex',
  dispatchOnMount: true,
})
export default class Index extends Component {
  constructor() {
    super();
    const { currentTab = 0 } = STORE;
    this.state = {
      currentTab,
      isOpened: false,
      showFlowCardSwitch: '',
    };
    this.tabList = [{ title: '申请开票' }, { title: '开票历史' }];

    this.recordListTabRef = Taro.createRef();
    this.hasInvApplyInfoList = false;

    this.configParams = {
      blockModalContent: '尊敬的客户：受深圳疫情影响，您申请的发票预计一周后才可开出，给您造成的不便敬请谅解。',
      blockModalBtn: '我知道了',
      blockModalShowSwitch: 'close',
      blockModalCloseSwitch: 'close',
    };
    // 客户是否已实名，默认实名
    this.identified = true;
    this.recordHistoryTabRef = Taro.createRef();
  }

  async componentDidMount() {
    const [
      ,
      { data: channelParams },
    ] = await Promise.all([
      this.getCust(),
      Dispatch.repayment.getChannelParams({
        channelParamKeyList: ['showFlowCardSwitch'],
        paramTypeList: ['CHANNEL_PARAM']
      }),
    ]);
    this.setState({
      showFlowCardSwitch: ((channelParams || {}).channelParamMap || {}).showFlowCardSwitch || '',
    });
    const [
      noInvApplyInfoList,
      configData,
    ] = await Promise.all([
      this.recordListTabRef.current.getRecords(true, this.identified),
      Dispatch.repayment.injectConfigParams('invoiceIndexPage'),
      this.recordHistoryTabRef.current.getRecords(true, this.identified),
    ]);
    this.configParams = {
      ...this.configParams,
      ...configData,
    };
    this.hasInvApplyInfoList = !noInvApplyInfoList;
    this.blockModalControll();
  }

  async componentDidShow() {
    Madp.setNavigationBarTitle({ title: '贷款息费发票' });
    await this.getCust();
  }

  onTabClick = (value) => {
    STORE.currentTab = value;
    this.setState({
      currentTab: value
    });
  }

  getCust = async () => {
    let custInfo;
    try {
      custInfo = await getLoginInfo();
    } catch (ignore) {
      return;
    }
    const { loginSuccess, identified } = custInfo || {};
    if (!loginSuccess) {
      const host = urlDomain;
      const params = '/loginregister/#/auth?mgpAuth=1&redirectUrl=';
      const redirectUrl = encodeURIComponent(window.location.href);
      const result = `${host}/${Madp.getChannel()}${params}${redirectUrl}`;
      window.location.replace(result);
    } else {
      this.identified = identified;
    }
  }

  blockModalControll = () => {
    const { canShowBlockModal } = this;
    if (canShowBlockModal) {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: 'BlockModalShow',
      });
      this.setState({ isOpened: true });
    }
  }

  blockModalConfirm = () => {
    const { blockModalShowSwitch, blockModalCloseSwitch } = this.configParams;
    this.setState({ isOpened: false });
    if (blockModalShowSwitch === 'open' && blockModalCloseSwitch === 'open') {
      Madp.closeWebView().catch(() => {
        Madp.navigateBack({
          fail: () => {
            Madp.reLaunch({
              url: (process.env.TARO_ENV === 'h5'
                ? Url.getParam('entryUrl')
                : decodeURIComponent(Url.getParam('entryUrl') || ''))
                || '/pages/index/index'.replace('/repayment', ''),
            });
          },
        });
      });
    }
  }

  // 展示客服组件判断（APP(非鸿蒙)和招行渠道展示在右上角）
  showChatEntry = () => (isMuapp() && !isMuHarmonyApp()) || isCmb();

  get canShowBlockModal() {
    const { blockModalShowSwitch } = this.configParams;
    const { hasInvApplyInfoList } = this;
    return hasInvApplyInfoList && blockModalShowSwitch === 'open';
  }

  render() {
    const { currentTab, isOpened, showFlowCardSwitch } = this.state;
    const { blockModalContent, blockModalBtn } = this.configParams;
    const { canShowBlockModal, blockModalConfirm } = this;
    const showNav = isCmb();
    return (
      <MUView>
        {showNav ? <NavBarView title="贷款息费发票" showChatEntry /> : null}
        <MUTabs
          className="trade-record-tabs"
          current={currentTab}
          tabList={this.tabList}
          onClick={this.onTabClick}
          beaconId="tabs"
        >
          <MUTabsPane current={currentTab} index={0}>
            <RecordListTab ref={this.recordListTabRef} navHeight={showNav ? 100 : 0} showFlowCardSwitch={showFlowCardSwitch} trackPageId="repayment.invoiceIndex." />
          </MUTabsPane>
          <MUTabsPane current={currentTab} index={1}>
            <RecordHistoryTab ref={this.recordHistoryTabRef} />
          </MUTabsPane>
        </MUTabs>
        {canShowBlockModal ? (
          <MUModal
            type="default"
            beaconId="BlockModal"
            closeOnClickOverlay={false}
            isOpened={isOpened}
            content={blockModalContent}
            confirmText={blockModalBtn}
            onConfirm={blockModalConfirm}
          />
        ) : null}
        {this.showChatEntry() && <ChatEntry busiEntrance={Utility.getBusiEntrance()} extraParam={{ needLogin: 1 }} />}
      </MUView>
    );
  }
}
