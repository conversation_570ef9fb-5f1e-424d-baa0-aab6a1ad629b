.history-detail {
  display: flex;

  &-tip {
    margin-left: 30px;
    font-size: 26px;
    color: #808080;
    margin-top: 40px;
    margin-bottom: 20px;
  }

  &-input {
    display: flex;
    align-items: center;

    &-leftView {
      color: #333333;
      font-size: 32px;
      height: 100%;
      line-height: 100%;
      margin-left: 30px;
      width: 160px;
    }

    &-inputView {
      height: 100px;
      display: flex;
      align-items: center;
      font-size: 32px;
    }
    .merchant-left {
      width: 70%;
    }


    &-unit {
      height: 100px;
      color: #808080;
      display: flex;
      align-items: center;
      font-size: 32px;
      // align-self: flex-end;
      margin-right: 50px;
      margin-left: 5px;
      justify-self: flex-end;
    }
  }

  &-notice {
    margin-top: 20px;
    line-height: normal;
    margin-left: 30px;
    margin-right: 30px;
    font-size: 24px;
    color: #A6A6A6;

    &-highlight {
      color: red;
    }
  }

  &-btns {
    display: flex;
    align-items: center;
    margin-top: 40px;
    margin-left: 20px;
    margin-right: 20px;
  }
}

.invoice-history-item {
  margin-top: 20px;
  height: 70px;
  display: flex;
  align-items: center;
  padding: 30px;
  background: #ffffff;

  &-desc {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    margin-left: 20px;

    &-amt {
      color: #000000;
      font-size: 32px;
    }

    &-time {
      color: #808080;
      font-size: 26px;
    }
  }

  &-status {
    font-size: 32px;
    color: #a6a6a6;
    line-height: 32px;
    white-space: nowrap;
  }

  &-arrow {
    margin-left: 15px;
    margin-top: 5px;
  }
}

.invoice {
  padding: 12px 0;
  background-color: #ffffff;
  &-merchant {
    line-height: 1.6;
    height: unset;
    align-items: flex-start;
    &__left {
      flex: 1;
      line-height: 1.6;
    }
    &__right {
      margin-right: 30px;
      height: unset !important;
    }
  }
}