import { Component } from '@tarojs/taro';

import Madp from '@mu/madp';
import { MUList, MUView } from '@mu/zui';
import { STORE } from './store';
import './trade-records.scss';
import RecordItem from './compoments/record-list-item';
import { track, EventTypes, disableTrackAlert } from '@mu/madp-track';
import NavBarView from '@components/nav-bar-view';

disableTrackAlert();
@track({ event: EventTypes.PO }, {
  pageId: 'tradeRecords',
  dispatchOnMount: true,
})
export default class TradeRecords extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  componentDidMount() {
    Madp.setNavigationBarTitle({ title: '交易记录' });
  }

  RecordItemList(item) {
    return item.map((i) => {
      return <RecordItem InvApplyInfo={i} disabled hideInfoIcon />;
    });
  }

  render() {
    const { invMerchantDetails = [], invTotalAmt = 0 } = STORE.selectedHistoryRecord || {};
    let count = 0;
    let list = [];
    invMerchantDetails.forEach((item) => {
      if (item && item.invAmountDetails) {
        count += item.invAmountDetails.length;
        list.push(this.RecordItemList(item.invAmountDetails));
      }
    });

    return (
      <MUView className="trade-records">
        {process.env.TARO_ENV === 'h5' ? <NavBarView title="交易记录" /> : null}
        <MUView className="trade-records-txt">{`${count}笔交易，共${invTotalAmt}元`}</MUView>
        <MUList>
          { list }
        </MUList>
      </MUView>
    );
  }
}
