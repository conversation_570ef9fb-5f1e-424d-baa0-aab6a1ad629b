import { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import {
  MUView, MUIcon, MUButton, MUText
} from '@mu/zui';
import { track, EventTypes, disableTrackAlert } from '@mu/madp-track';
import Madp from '@mu/madp';
import './history-detail.scss';
import { STORE } from './store';
import Utility from '../../utils/Utility';
import NavBarView from '@components/nav-bar-view';
import { getLoginInfo } from '@mu/business-basic';

disableTrackAlert();
@track({ event: EventTypes.PO }, {
  pageId: 'invoiceHistoryDetail',
  dispatchOnMount: true,
})
export default class Index extends Component {
  constructor() {
    super();
    this.state = {
      name: '--'
    };
  }

  toReSend =() => {
    Madp.navigateTo({
      url: '/pages/self-invoice/reSend'
    });
  }

  toTradeRecords=() => {
    Madp.navigateTo({
      url: '/pages/self-invoice/tradeRecords'
    });
  }

  async componentDidShow() {
    Madp.setNavigationBarTitle({ title: '发票详情' });
    let custInfo;
    try {
      custInfo = await getLoginInfo();
    } catch (ignore) {}
    const { custName: maskRealName } = custInfo || {};
    this.setState({ name: maskRealName });
  }

  handleDateScope=(startDate, endDate) => {
    if (startDate && endDate && startDate.length
      && endDate.length && startDate.length >= 8 && endDate.length >= 8) {
      return `${Utility.tranformDate(startDate)}-${Utility.tranformDate(endDate)}`;
    }
    return '------';
  }

  render() {
    const { name } = this.state;
    const {
      invTotalAmt, invTitle, email, startDate, endDate,
      invMerchantDetails, needCooperateInvoiceFlag
    } = STORE.selectedHistoryRecord || {};
    let count = 0;
    (invMerchantDetails || []).forEach((item) => {
      const { invAmountDetails = null } = item || {};
      // console.log(item.invAmountDetails)
      if (invAmountDetails) count += invAmountDetails.length;
    });

    // 仅在开票机构存在招联，且招联发票状态为“已开票”时展示
    let showReSendBtn = false;
    (invMerchantDetails || []).forEach(item => {
      const { merchantNo, invoiceStatus } = item || {};
      if ((merchantNo === '10000' || merchantNo === '10001') && invoiceStatus === '2') showReSendBtn = true;
    });
    return (
      <MUView>
        {process.env.TARO_ENV === 'h5' ? <NavBarView title="发票详情" /> : null}
        <MUView className="history-detail-tip">电子发票详情</MUView>
        <View style="background-color:white;">
          <View className="history-detail-input">
            <View className="history-detail-input-leftView">个人姓名</View>
            <View className="history-detail-input-inputView">{name}</View>
          </View>

          <View className="history-detail-input">
            <View className="history-detail-input-leftView">抬头类型</View>
            <View className="history-detail-input-inputView">{invTitle}</View>
          </View>

          <View className="history-detail-input">
            <View className="history-detail-input-leftView">发票金额</View>
            <View className="history-detail-input-inputView">{invTotalAmt}</View>
            <View className="history-detail-input-unit">元</View>
          </View>
        </View>

        <MUView className="history-detail-tip">接收方式</MUView>
        <View className="history-detail-input" style="background-color:white;">
          <View className="history-detail-input-leftView">电子邮箱</View>
          <View className="history-detail-input-inputView">{email}</View>
        </View>

        {needCooperateInvoiceFlag === 'Y' && (invMerchantDetails || []).length > 0 && <MUView>
          <MUView className="history-detail-tip">开票机构</MUView>
          <MUView className="invoice">
            {(invMerchantDetails || []).map(item =>
              (<View className="history-detail-input invoice-merchant">
                <View className="history-detail-input-leftView invoice-merchant__left">{item && item.merchantOrgName}</View>
                <View
                  className="history-detail-input-inputView invoice-merchant__right"
                  style={{ color: (item && (item.invoiceStatus === '2'
                || item.invoiceStatus === '4')) ? '#A6A6A6' : '#3477FF' }}
                >
                  {(item && item.invoiceStatus === '2') ? '已开票'
                    : (item && (item.invoiceStatus === '0' || item.invoiceStatus === '1' || item.invoiceStatus === '3'))
                      ? '开票中' : '开票失败'}
                </View>
              </View>))}
          </MUView>
        </MUView>}
        <MUView className="invoice-history-item" beaconId="toTradeRecords" onClick={this.toTradeRecords}>
          <MUView className="invoice-history-item-desc">
            <MUText className="invoice-history-item-desc-amt">{`含${(invMerchantDetails || []).length}张发票${count}笔交易记录`}</MUText>
            <MUText className="invoice-history-item-desc-time">{this.handleDateScope(startDate, endDate)}</MUText>
          </MUView>
          <p className="invoice-history-item-status">查看</p>
          <MUIcon beaconId="checkTradeDetail" value="arrow-right" size="15" color="#CACACA" className="invoice-history-item-arrow" />
        </MUView>

        {((needCooperateInvoiceFlag === 'F') || (needCooperateInvoiceFlag === 'Y' && showReSendBtn))
        && <MUView className="history-detail-btns">
          <MUButton beaconId="reSend" type="primary" full onClick={this.toReSend}>重新发送发票</MUButton>
        </MUView>}

      </MUView>
    );
  }
}
