/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import {
  MUView,
  MUForm,
  MUInput,
  MUImage,
  MUListItem,
} from '@mu/zui';
import ContactImg from '../../img/contact_icon.png';

import './index.scss';

/**
 * 联系人表单
 */
export default function ContactForm({
  title,
  index,
  selectedRelationList = [],
  nameList = [],
  mobileList = [],
  supportContactSelect,
  onInfoInput,
  onContactClick,
  showRelationSheet,
}) {
  const relationShip = selectedRelationList[index] && selectedRelationList[index].name;
  return (
    <MUForm title={title} className="contact-form">
      <MUListItem
        beaconId="SecondRelation"
        className={`contact-form-listitem ${relationShip ? '' : 'extra-text-grey'}`}
        title="关系"
        renderExtraText={<MUView>{relationShip || '请选择联系人关系'}</MUView>}
        arrow="mu-icon-arrow-right"
        onClick={() => showRelationSheet && showRelationSheet(index)}
      />
      <MUView className="repayment-contact-item">
        <MUView className="repayment-contact-item__input">
          <MUInput
            beaconId="SecondName"
            name="secondName"
            title="姓名"
            type="text"
            enableBlurTrim
            maxLength={50}
            placeholder="请输入常用联系人姓名"
            value={nameList[index]}
            enableNative={false}
            onBlur={(val) => onInfoInput('name', val, index)}
          />
          <MUInput
            beaconId="SecondMobile"
            name="secondMobile"
            title="联系电话"
            type="phone"
            maxLength={11}
            placeholder="仅用于在紧急情况下联系"
            value={mobileList[index]}
            enableNative={false}
            onChange={(val) => onInfoInput('mobile', val, index)}
          />
        </MUView>
        {supportContactSelect ? (
          <MUView
            className="repayment-contact-item__icon"
            beaconId="SecondClickContact"
            onClick={() => { onContactClick(index, 'secondContact'); }}
          >
            <MUImage className="repayment-contact-item__icon-img" src={ContactImg} />
            <MUView className="repayment-contact-item__icon-text">选择</MUView>
            <MUView className="repayment-contact-item__icon-text">联系人</MUView>
          </MUView>
        ) : null}
      </MUView>
    </MUForm>
  );
}
