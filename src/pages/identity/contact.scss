@import '../../components/weapp/index.scss';
@import "~@mu/zui/dist/style/mixins/index.scss";

.repayment-contact {
  .contact-form-listitem {
    background: #FFF;

    .item-extra__info {
      padding-right: 0;
    }
    .item-extra {
      justify-content: flex-start;
    }
  }
  .contact-form-listitem.extra-text-grey {
    .item-extra {
      color: #CACACA;
    }
  }
  &-item {
    display: flex;

    mu-view:first-of-type {
      flex: 2;
      display: flex;
    }

    &__input {
      flex: 2;
    }

    &__icon {
      width: 130px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      color: #333;
      font-size: 24px;
      padding: 0 16px;
      justify-content: center;
      box-sizing: border-box;

      @include hairline-left();

      &-img {
        width: 42px;
        height: 42px;
        margin-bottom: 2px;
      }
    }
  }
  .next-step-btn {
    margin: 50px 30px 40px;
    height: 90px;
  }

  .footer {
    color: #A6A6A6;
    font-size: 24px;
    text-align: center;
    width: 100%;
    height: 80px;

    .safe-icon {
      display: inline-block;
      width: 40px;
      height: 40px;
      vertical-align: bottom;
      margin-right: 16px;
    }
  }
}
