/* eslint-disable no-unused-expressions */
/* eslint-disable max-len */
/* eslint-disable object-curly-newline */
/* eslint-disable react/sort-comp */
import Taro, { Component } from '@tarojs/taro';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import Madp from '@mu/madp';
import { getStore, setStore } from '@api/store';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import pageHoc from '@utils/pageHoc';
import ContactApi from '@utils/contact-api';
import { MUView } from '@mu/zui';
import LoadingDialog from '@components/loading-dialog';
import { miniProgramChannel } from '@utils/constants';
import { Url } from '@mu/madp-utils';

@track({ event: EventTypes.PO }, {
  pageId: 'Contact',
  dispatchOnMount: true,
})
@pageHoc({ title: '信息补充' })
export default class Contact extends Component {
  config = {
    navigationBarTitleText: '信息补充'
  }

  constructor(props) {
    super(props);
    this.state = {
    };
    this.billType = '';
    this.countTime = 10; // 倒计时默认10s
    this.queryDialog = {
      show: () => { },
      hide: () => { },
    };
    this.timer = null;
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
    this.applyNo = Url.getParam('applyNo') || ''; // 案件申请号
    this.repaymentFlag = Url.getParam('repaymentFlag') || '';
  }

  async componentDidMount() {
    const { billType } = this.$router.params;
    this.billType = billType || this.billType;
    let scene;
    switch (this.billType) {
      case 'extend':
        scene = 'SCENE_DELAY_PAY';
        break;
      case 'adjust':
        scene = 'SCENE_DELAY_GRACE';
        break;
      case 'credit-product':
        scene = 'SCENE_PACK_DELAY_GRACE';
        break;
      case 'advanced-stage':
        scene = 'SCENE_SXF';
        break;
      default:
        break;
    }
    await ContactApi.of(scene).check(() => this.submitContact());
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '信息补充' });
  }

  submitContact() {
    if (this.billType === 'advanced-stage') {
      this.proccessAdvancedStage();
    } else if (this.billType === 'extend') {
      this.proccessRepayExtend();
    } else if (this.billType === 'adjust') {
      this.proccessRepayAdjust();
    } else if (this.billType === 'credit-product') {
      this.proccessRepayCreditProduct();
    }
  }

  async proccessRepayAdjust() {
    // 延长宽限期 上个页面已处理参数
    const adjustRepayInfo = getStore('adjustRepayInfo');
    const { hasApplied, lastApplyDate } = await Dispatch.repayment.applyExtendRepay({ ...adjustRepayInfo });
    if (hasApplied === 'Y' && lastApplyDate) {
      Util.toast(`您已于${lastApplyDate}申请延期还款，三个月后再申请`);
    } else {
      // 失败中台抛异常，成功接口啥也不返回，能过就是能成功，牛
      Util.router.replace(`/pages/common-result/result?type=${this.billType}-success`);
      // else Util.router.replace(`/pages/common-result/result?type=${this.billType}-fail`);
    }
  }

  // 延期还款（延后还）提交处理
  async proccessRepayExtend() {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'contactUpdateSuccess', target: this });
    const { applyNo, delayRepayAdjustInfo, needFileList } = getStore('extendRepayInfo') || {};
    const {
      adjustCnt, orderNoList, delayRepayReason, awardNo, needRepayType, beforeNeedRepayAmt
    } = delayRepayAdjustInfo || {};

    // 判断是否需要补充资料
    const currentNeedFileList = needFileList && needFileList.filter((item) => (item.fileType !== 'K01' && item.fileType !== 'K02')); // 过滤掉自助kyc的，到这里已经做过了
    if (currentNeedFileList && currentNeedFileList.length > 0) {
      // 这里在去补充资料页面前，先把案件办理信息提交了，保证下次进入能续案
      const { ret } = await Dispatch.repayment.postLoanAddData({
        applyNo,
        addInfoScene: '00',
        delayRepayApplyInfo: {
          adjustCnt,
          orderNoList,
          delayRepayReason,
          awardNo,
          needRepayType,
          beforeNeedRepayAmt,
        },
      }, {});

      if (ret !== '0') {
        Util.router.replace({ url: `/pages/service-result/index?_windowSecureFlag=1&serviceType=extend&status=3&repaymentFlag=${this.repaymentFlag}` });
        return;
      }

      if (this.miniChannelFlag) {
        Taro.redirectTo({
          url: `/pages/identity/information?_windowSecureFlag=1&serviceType=${this.billType}&applyNo=${applyNo || this.applyNo}&repaymentFlag=${this.repaymentFlag}`
        });
      } else {
        Util.router.replace({
          path: `/pages/identity/information?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}`,
          query: {
            serviceType: this.billType,
            applyNo: applyNo || this.applyNo
          }
        });
      }
      return;
    }

    const { ret, data } = await Dispatch.repayment.postLoanSubmitCase({
      applyNo: applyNo || this.applyNo,
      delayRepayApplyInfo: {
        adjustCnt,
        orderNoList,
        delayRepayReason,
        awardNo,
        needRepayType,
        beforeNeedRepayAmt,
      },
    });
    const { applyStatus, additionalContractList, delayRepayPackageInfo } = data || {};
    const { actualNeedRepayType, needRepayType: needRepayTypeSubmit, isMajorEvent } = delayRepayPackageInfo || {};
    if (ret === '0') {
      // 入参处理
      const extendRepayInfo = {
        applyNo: applyNo || this.applyNo,
        delayRepayAdjustInfo: {
          ...delayRepayAdjustInfo,
          needRepayType: actualNeedRepayType || needRepayTypeSubmit || '',
          isMajorEvent,
        },
      };
      setStore({ extendRepayInfo, contractApplyList: additionalContractList });
      this.applyStatusNext(applyStatus, needFileList);
    } else {
      let urlParam = 'status=3&serviceType=extend';
      if (applyStatus === '6') {
        urlParam = `${urlParam}&subStatus=2`;
      }
      Util.router.replace({
        path: `/pages/service-result/index?_windowSecureFlag=1&${urlParam}&repaymentFlag=${this.repaymentFlag}`,
      });
    }
  }

  async proccessAdvancedStage() {
    const { applyNo, extendPackageInfo, awardNo, needFileList, firstPhasePeriods, extendInstallTotalCnt, expectRepayAmtPercent } = getStore('advancedStageInfo') || {};
    const selectedBillList = getStore('selectedBillList');

    // 判断是否需要补充资料
    const currentNeedFileList = needFileList && needFileList.filter((item) => (item.fileType !== 'K01' && item.fileType !== 'K02')); // 过滤掉自助kyc的，到这里已经做过了
    if (currentNeedFileList && currentNeedFileList.length > 0) {
      // 这里在去补充资料页面前，先把案件办理信息提交了，保证下次进入能续案
      const { ret } = await Dispatch.repayment.postLoanAddData({
        applyNo,
        addInfoScene: '00',
        extendApplyInfo: {
          orderNoList: selectedBillList,
          awardNo,
          extendPackage: extendPackageInfo,
          firstPhasePeriods,
          extendInstallTotalCnt,
          expectRepayAmtPercent,
        },
      }, {});

      if (ret !== '0') {
        Util.router.replace({ path: '/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=3' });
        return;
      }

      if (this.miniChannelFlag) {
        Taro.redirectTo({
          url: `/pages/identity/information?_windowSecureFlag=1&serviceType=${this.billType}&applyNo=${applyNo || this.applyNo}&repaymentFlag=${this.repaymentFlag}`
        });
      } else {
        Util.router.replace({
          path: `/pages/identity/information?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}`,
          query: {
            serviceType: this.billType,
            applyNo: applyNo || this.applyNo
          }
        });
      }
      return;
    }

    const { ret, data } = await Dispatch.repayment.postLoanSubmitCase({
      applyNo: applyNo || this.applyNo,
      extendApplyInfo: {
        orderNoList: selectedBillList,
        awardNo,
        extendPackage: extendPackageInfo,
        firstPhasePeriods,
        extendInstallTotalCnt,
        expectRepayAmtPercent,
      },
      ignoreLoading: true
    });
    const { applyStatus, additionalContractList } = data || {};
    if (ret === '0') {
      setStore({ contractApplyList: additionalContractList });
      this.applyStatusNext(applyStatus, needFileList);
    } else {
      let urlParam = 'status=3';
      if (applyStatus === '6') {
        urlParam = `${urlParam}&subStatus=2`;
      }
      Util.router.replace({
        path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&${urlParam}&repaymentFlag=${this.repaymentFlag}`,
      });
    }
  }

  async proccessRepayCreditProduct() {
    // 延长信用保护期 上个页面已处理参数
    const creditProductRepayInfo = getStore('creditProductRepayInfo');
    // 贷后调整申请
    const { errCode, data } = await Dispatch.repayment.adjustApply({ ...creditProductRepayInfo });
    if (errCode === 'COM00000') {
      if (data && data.applyStatus === '5' && data.applyNo) {
        // 延长宽限期提交
        const { errCode: extendCode, data: extendData } = await Dispatch.repayment.extendGracePeriod({
          applyNo: data.applyNo
        });
        if (extendCode === 'COM00000') {
          if (extendData && extendData.transStatus === 'SUC' && extendData.graceDate) {
            setStore({
              creditProductResultInfo: {
                status: extendData.transStatus,
                graceDate: extendData.graceDate,
              }
            });
          }
          Util.router.replace('/pages/credit-product/result');
        } else {
          Util.router.replace('/pages/credit-product/result');
        }
      } else {
        // 接口成功，状态失败
        Util.router.replace('/pages/credit-product/result');
      }
    } else {
      Util.router.replace('/pages/credit-product/result');
    }
  }

  applyStatusNext(status, needFileList) {
    const { applyNo } = getStore('advancedStageInfo') || {};

    let needManualKyc = false; // 标识是否需要人工kyc（仅做记录，用于结果页展示）
    needFileList && needFileList.forEach((item) => {
      if (item.fileType === 'K02') {
        needManualKyc = true;
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'NeedManualKyc', beaconContent: { cus: { } } });
      }
    });

    switch (status) {
      case '3': // 等待风控结果，轮询
        if (this.billType === 'extend') { // 延后还进轮询页
          Util.router.replace({
            path: `/pages/bill-extend/loading?billType=extend&applyNo=${applyNo}${needManualKyc ? '&needManualKyc=1' : ''}`,
          });
        } else { // 再分期进轮询页
          Util.router.replace({
            path: `/pages/bill-advanced-stage/loading?billType=advanced-stage&applyNo=${applyNo}${needManualKyc ? '&needManualKyc=1' : ''}`,
          });
        }
        break;
      case '4': // 人工，需补充资料
        if (this.miniChannelFlag) {
          Taro.redirectTo({
            url: `/pages/identity/information?_windowSecureFlag=1&serviceType=${this.billType}&applyNo=${applyNo || this.applyNo}`
          });
        } else {
          Util.router.replace({
            path: `/pages/identity/information?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}`,
            query: {
              serviceType: this.billType,
              applyNo: applyNo || this.applyNo
            }
          });
        }
        break;
      case '5': // 提交审核（人工kyc）
        if (this.billType === 'extend') { // 延后还
          Util.router.replace({
            path: `/pages/service-result/index?_windowSecureFlag=1&serviceType=extend&status=2${needManualKyc ? '&subStatus=1' : ''}&repaymentFlag=${this.repaymentFlag}`,
          });
        } else {
          Util.router.replace({
            path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=2${needManualKyc ? '&subStatus=1' : ''}&repaymentFlag=${this.repaymentFlag}`,
          });
        }
        break;
      case '6': // 风控拒绝，办理失败
        if (this.billType === 'extend') { // 延后还
          Util.router.replace({
            path: `/pages/service-result/index?_windowSecureFlag=1&serviceType=extend&status=3&subStatus=2&repaymentFlag=${this.repaymentFlag}`,
          });
        } else {
          Util.router.replace({
            path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=3&subStatus=2&repaymentFlag=${this.repaymentFlag}`,
          });
        }
        break;
      case '7': // 审核通过，去还款
        if (this.billType === 'extend') { // 延后还
          Util.router.replace({
            path: `/pages/bill-extend/confirm?repaymentFlag=${this.repaymentFlag}`,
            query: {
              applyNo: this.applyNo,
            }
          });
        } else {
          if (this.miniChannelFlag) {
            Taro.redirectTo({
              url: `/pages/express-repay/index?_windowSecureFlag=1&billType=${this.billType}&repaymentFlag=${this.repaymentFlag}`
            });
          } else {
            Util.router.replace({
              path: `/pages/express-repay/index?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}`,
              query: {
                billType: this.billType
              }
            });
          }
        }
        break;
      case '8': // 提交完成，审核中
        if (this.billType === 'extend') { // 延后还
          Util.router.replace({
            path: `/pages/service-result/index?_windowSecureFlag=1&serviceType=extend&status=2&subStatus=2&repaymentFlag=${this.repaymentFlag}`,
          });
        } else {
          Util.router.replace({
            path: '/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=2&subStatus=2',
          });
        }
        break;
      case '9': // 提交完成，办理成功
        if (this.billType === 'extend') { // 延后还
          Util.router.replace({
            path: `/pages/service-result/index?_windowSecureFlag=1&serviceType=extend&status=1&repaymentFlag=${this.repaymentFlag}`,
          });
        } else {
          Util.router.replace({
            path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=1&repaymentFlag=${this.repaymentFlag}`,
          });
        }
        break;
      case '10': // 提交完成，办理失败
        if (this.billType === 'extend') { // 延后还
          Util.router.replace({
            path: `/pages/service-result/index?_windowSecureFlag=1&serviceType=extend&status=3&subStatus=3&repaymentFlag=${this.repaymentFlag}`,
          });
        } else {
          Util.router.replace({
            path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=3&subStatus=3&repaymentFlag=${this.repaymentFlag}`,
          });
        }
        break;
      default: // 异常情况
        Madp.showToast({
          icon: 'none',
          title: '系统繁忙，请退出后重试',
          duration: 1500,
        });
        break;
    }
  }

  componentWillUnmount() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  render() {
    return (
      <MUView>
        <LoadingDialog
          onRef={(ref) => { this.queryDialog = ref; }}
          countTime={this.countTime}
          serviceType={this.billType}
        />
      </MUView>
    );
  }
}
