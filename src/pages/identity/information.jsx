/* eslint-disable max-len */
import Taro, { Component } from '@tarojs/taro';
import {
  MUView, MUImage, MUText, MUListItem, MUActionSheet, MUActionSheetItem, MURadio, MUTextarea, MUButton, MUModal, MUIcon
} from '@mu/zui';
import { Canvas } from '@tarojs/components';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import Madp from '@mu/madp';
import { Url } from '@mu/madp-utils';
import classNames from 'classnames';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import ImagePicker from '@components/image-picker';
import { isMiniProgram } from '@utils/repay-util';
import channelConfig from '@config/index';
import pageHoc from '@utils/pageHoc';
import './information.scss';
import '@components/image-picker/index.scss';
import { AgreementDrawer } from '@mu/agreement';
import ContractParamUtil from '@utils/contract-param-util';
import { getStore, setStore } from '@api/store';
import { miniProgramChannel, sloganUrl, StandardService } from '@utils/constants';
import { getLoginInfo } from '@mu/business-basic';

const defaultBannerUrl = 'https://file.mucfc.com/ebn/3/0/202401/202401261907208079a1.png'; // 延期还款
const restagingBannerUrl = 'https://file.mucfc.com/ebn/3/0/2023010/20231025181042d08adb.png'; // 再分期
const consultRepayBannerUrl = require('./img/consult-repay.png'); // 协商还
const defaultFileConfig = [
  { category: 'C02', title: '受灾/疫影响证明', guide: '查看证明示例', url: 'https://file.mucfc.com/ebn/3/18/202304/20230410163425f71439.png', desc: '如受灾证明、新冠检测证明等' },
  { category: 'C07', title: '资产证明', guide: '查看证明示例', url: 'https://file.mucfc.com/ebn/3/18/202304/20230410163425390fbd.png', desc: '收入流水、房产证、营业执照、土地承包合同等' },
  { category: 'C06', title: '社会贡献证明', guide: '查看证明示例', url: 'https://file.mucfc.com/ebn/3/18/202304/202304101634255f4a7f.png', desc: '献血证明、慈善捐款证明、企业获奖证书等' },
  { category: 'C08', title: '失业证明', guide: '查看证明示例', url: 'https://file.mucfc.com/ebn/3/0/2023010/2023102518121361f7a0.png', desc: '如失业证、停业证明、失业补助金申领记录等' },
  { category: 'C09', title: '确诊证明', guide: '查看证明示例', url: 'https://file.mucfc.com/ebn/3/0/2023010/20231025181328b33585.png', desc: '病例单、住院档案、医保结算单等' },
  { category: 'C04', title: '疾病证明', guide: '查看证明示例', url: 'https://file.mucfc.com/ebn/3/0/2023010/20231025181328b33585.png', desc: '病例单、住院档案、医保结算单等' },
  { category: '999', title: '其他证明', desc: '其他困难证明、辅助证明材料等' }
];
const defaultCustConfig = [
  { custType: '09', custTypeName: '受灾群众' },
  { custType: '10', custTypeName: '疫情确诊人群' },
  { custType: '11', custTypeName: '自雇受困人群' },
  { custType: '12', custTypeName: '灾/疫区援助人员' },
  { custType: '13', custTypeName: '重疾人群' },
  { custType: '14', custTypeName: '失业人群' },
  { custType: '99', custTypeName: '其他' }
];
const defaultContract = {
  contractType: 'POST_INFO_AUTH',
  contractEdition: 'COMMON',
  contractVersion: '',
  contractCategory: 'POST_INFO_AUTH',
  contractName: '贷后资料授权书'
};

const themeColor = Util.getThemeColor(channelConfig.theme);
const Safe = 'https://file.mucfc.com/ebn/21/0/2023010/20231010145348138d81.png';
@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      scene: Url.getParam('serviceType') === 'advanced-stage' ? 'SCENE_SXF' : 'EXTEND'
    }
  }
}, {
  pageId: 'CompleteInformation',
  dispatchOnMount: true,
})

@pageHoc({ title: '补充资料' })
export default class Information extends Component {
  constructor(props) {
    super(props);
    this.state = {
      contractInfo: {}, // 协议信息
      showIdentitySheet: false,
      selectedCustType: {},
      needFileTypeList: [], // 需补充资料列表
      situationDesc: '',
      showExample: false, // 是否展示示例
      exampleUrl: '', // 示例图片地址
      showRestagingModel: true, // 再分期提示弹窗
    };
    this.serviceType = Url.getParam('serviceType'); // 服务类型
    this.custAndFileList = []; // 可补充身份和资料列表
    this.applyNo = Url.getParam('applyNo') || ''; // 案件申请号
    this.oldMode = Url.getParam('oldMode') === '1' || false; // 标识补充资料是否走旧模式仅提交资料（兼容存量客户）
    this.applyInfoDone = Url.getParam('applyInfoDone') === '1' || false; // 标识服务办理申请信息是否已保存过
    this.isVplus = getStore('isVplus') || false; // V+会员标识
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
    this.dispatchFlag = false; // 是否分发页面
    this.repaymentFlag = Url.getParam('repaymentFlag') || ''; // 还款业务跳进的标识
    this.serviceInfo = { // 服务信息
      bgImg: '', // 背景图
      serviceName: '', // 服务名称
    };
  }


  async componentDidMount() {
    // 清除最近一次上传的资料信息
    (defaultFileConfig || []).forEach((defaultFileItem = {}) => {
      defaultFileItem.uploadFiles = [];
      defaultFileItem.originFiles = [];
    });
    await this.getLoginInfo();
    // 1、设置服务信息
    this.setServiceInfo();
    // 2、身份和资料信息
    this.initCustAndFileData();
    // 3、协议信息
    this.initContractInfo();
  }

  setServiceInfo() {
    // 设置背景图、服务名称
    if (this.serviceType === 'advanced-stage') {
      this.serviceInfo.bgImg = restagingBannerUrl;
      this.serviceInfo.serviceName = '再分期';
    } else if (this.serviceType === 'consult-repay') {
      this.serviceInfo.bgImg = consultRepayBannerUrl;
      this.serviceInfo.serviceName = '协商还';
    } else {
      this.serviceInfo.bgImg = defaultBannerUrl;
      this.serviceInfo.serviceName = '延后还';
    }
  }

  initCustAndFileData() {
    let aliasCustTypeAndFileTypeList = [];
    if (this.serviceType === 'advanced-stage') { // 再分期
      ({ custTypeAndFileTypeList: aliasCustTypeAndFileTypeList } = getStore('advancedStageInfo') || {});
    } else if (this.serviceType === 'consult-repay') { // 协商还
      const consultReapayInfo = getStore('consultReapayInfo') || {};
      aliasCustTypeAndFileTypeList = consultReapayInfo.aliasCustTypeAndFileTypeList || [];
      const { needMaterialList, applySorce, judicialConfirmFlagFromApply } = consultReapayInfo || {};
      this.needMaterialList = needMaterialList || [];
      this.applySorce = applySorce || '';
      this.trackInfo = {
        applySorce: this.applySorce === 'fromC' ? 'C' : 'B',
        judicial: judicialConfirmFlagFromApply,
      };
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'consultReapayInfo',
        beaconContent: {
          cus: {
            ...this.trackInfo
          }
        }
      });
      // 如果是来源于B端，则展示所有的资料
      if (applySorce === 'fromB') {
        this.needFileTypeListInit();
      }
    } else { // 延后还
      ({ custTypeAndFileTypeList: aliasCustTypeAndFileTypeList } = getStore('extendRepayInfo') || {});
    }
    if (this.applySorce === 'fromB') {
      this.custAndFileList = defaultCustConfig; // 来源于B端，则展示所有的身份
    } else {
      (aliasCustTypeAndFileTypeList || []).forEach((item) => {
        (defaultCustConfig || []).forEach((defaultCustItem) => {
          if ((defaultCustItem || {}).custType === (item || {}).custType) {
            this.custAndFileList.push({ ...defaultCustItem, ...item });
          }
        });
      });
    }
  }


  // 协商还来源于B端，待补充资料列表固定
  needFileTypeListInit = () => {
    let needFileTypeList = [];
    (this.needMaterialList || []).forEach((item) => {
      (defaultFileConfig || []).forEach((defaultFileItem) => {
        if (item.materialType === defaultFileItem.category && item.materialStatus !== '1') {
          needFileTypeList.push(defaultFileItem);
        }
      });
    });
    this.setState({
      needFileTypeList,
    });
  }

  async initContractInfo() {
    let needSignContractList = [];
    if (this.serviceType === 'advanced-stage') {
      const { applyNo } = getStore('advancedStageInfo');
      this.applyNo = applyNo || this.applyNo;
    } else if (this.serviceType === 'consult-repay') {
      const { applyNo } = getStore('consultReapayInfo');
      this.applyNo = applyNo || this.applyNo;
    } else {
      const { applyNo } = getStore('extendRepayInfo');
      this.applyNo = applyNo || this.applyNo;
    }
    // 再分期/延后还 协议获取
    const contractApplyList = getStore('contractApplyList') || [];
    let contractInfos = getStore('contractInfos') || [];
    // 协商还协议获取, 新服务只有3.0合同，故不用获取contractApplyList（2.0）合同
    if (this.serviceType === 'consult-repay') {
      const { consultContractInfo } = getStore('consultReapayInfo') || {};
      contractInfos = consultContractInfo || [];
    }

    const contractList = [];
    const contractInfo = {};
    if ((contractApplyList || []).length > 0) {
      needSignContractList = (contractApplyList || []).filter((contract) => (contract.contractType === 'POST_INFO_AUTH'));
      if (needSignContractList.length) {
        const contractParam = await ContractParamUtil.getInfoAuthParam();
        contractList.push({
          title: defaultContract.contractName,
          params: {
            ...contractParam,
            ...defaultContract,
            ...(needSignContractList.length ? needSignContractList[0] : {}),
          }
        });
      }
    }
    if ((contractInfos || []).length > 0) {
      needSignContractList = (contractInfos || []).filter((contract) => (contract.contractCode === 'GRXXSQ_DHZLSQ'));
      if (needSignContractList.length) {
        const contractParam = await ContractParamUtil.getInfoAuthParam({}, '3.0');
        const htmlFile = await this.htmlFilePrivew((needSignContractList[0] || {}).contractCode, contractParam);
        contractList.push({
          title: defaultContract.contractName,
          htmlFile
        });
      }
    }
    contractInfo.contractList = contractList;
    contractInfo.contractText = contractList.map((contract) => contract.title).join('、');
    this.setState({
      contractInfo
    });
  }

  // 请求预览html
  htmlFilePrivew = (contractCode, contractPreviewData) => {
    return Dispatch.repayment.queryContractInfo({
      scene: 'PREVIEW',
      interfaceVersion: '3.0',
      contractCode,
      contractPreviewData,
    }).then(res => {
      const { data } = res || {};
      const { contractList } = data || {};
      return contractList && contractList[0] && contractList[0].htmlFile;
    });
  };

  // 选择身份，如果是C端，则展示对应身份需要补充的资料
  chooseCustType = (identity) => {
    let needFileTypeList = [];
    // 如果是协商还且来源于B端，待补充资料列表固定
    if (this.serviceType === 'consult-repay' && this.applySorce === 'fromB') {
      this.setState({
        selectedCustType: identity,
        showIdentitySheet: false,
      });
    } else {
      // 原有的逻辑筛选展示对应的资料
      ((identity || {}).fileTypeList || []).forEach((item) => {
        (defaultFileConfig || []).forEach((defaultFileItem = {}) => {
          if (item === defaultFileItem.category) {
            needFileTypeList.push(defaultFileItem);
          }
        });
      });
      this.setState({
        selectedCustType: identity,
        showIdentitySheet: false,
        needFileTypeList,
      });
    }
  }

  textInput(event) {
    this.setState({
      situationDesc: event.target.value
    });
  }

  showExampleUrl(url) {
    const { exampleUrl } = this.state;
    this.setState({
      showExample: true,
      exampleUrl: url || exampleUrl,
    });
  }

  async onImgPicked(files, category) { // 上传图片前的文件选择操作，但不包括图片上传的功能
    const { needFileTypeList } = this.state;
    (needFileTypeList || []).forEach((item, i) => {
      if (item.category === category) {
        let uploadFiles = [];
        (files || []).forEach((fileItem) => {
          if (fileItem.path) uploadFiles.push(fileItem.path);
        });
        needFileTypeList[i].uploadFiles = uploadFiles;
        needFileTypeList[i].originFiles = files;
      }
    });
    this.setState({
      needFileTypeList,
    });
  }

  toViewContract = () => {
    this.setState((prevState) => prevState.contractInfo.showContract = true, () => {
      if (Util.isH5Env()) {
        Madp.showToast({
          title: '',
          icon: 'none',
          duration: 0
        });
      }
    });
  }

  handlerCheckboxClick = () => {
    this.setState((prevState) => prevState.contractInfo.checkBoxChecked = !prevState.contractInfo.checkBoxChecked);
  }

  contractSubmitClick = (value) => {
    this.setState((prevState) => {
      prevState.contractInfo.checkBoxChecked = value || prevState.contractInfo.checkBoxChecked;
      prevState.contractInfo.showContract = false;
    });
  }

  async submitInfo() {
    if (!this.checkInfo()) return;
    dispatchTrackEvent({
      event: EventTypes.EV,
      target: this,
      beaconId: 'CompleteInformationChecked',
      beaconContent: { cus: { scene: Url.getParam('serviceType') === 'advanced-stage' ? 'SCENE_SXF' : 'EXTEND' } }
    });

    // 兼容存量客户（仅做提交资料）
    if (this.oldMode) {
      this.oldSubmitInfo();
      return;
    }

    const { selectedCustType, situationDesc, needFileTypeList } = this.state;
    const fileParam = {};
    const applyFiles = {};
    needFileTypeList.forEach((checkItem) => {
      // 过滤掉选择其他时，未上传证明材料的项目
      if (checkItem && checkItem.uploadFiles) {
        fileParam[`img.${checkItem.category}`] = checkItem.uploadFiles;
      }
    });

    const { serviceCommonInfo, needFileList } = this.getCommonInfo() || {};
    if (this.serviceType === 'consult-repay') { // 协商还走新的提交
      this.submit(fileParam);
    } else {
      const { ret, data } = await Dispatch.repayment.postLoanSubmitCase({
        ...serviceCommonInfo,
        additionalApplyInfo: {
          custType: selectedCustType.custType,
          desc: situationDesc,
        },
        applyFiles,
        ignoreLoading: true
      }, fileParam);
      const { applyStatus } = data || {};
      if (ret === '0') {
        this.applyStatusNext(applyStatus, needFileList);
      } else {
        const urlParam = 'status=3';
        if (this.serviceType === 'advanced-stage') { // 再分期跳转链接
          Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
        } else { // 延后还跳转链接
          Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}&serviceType=extend&repaymentFlag=${this.repaymentFlag}` });
        }
      }
    }
  }

  checkInfo(fileList) {
    let toastMsg = '';
    const {
      selectedCustType, situationDesc, needFileTypeList, contractInfo
    } = this.state;
    if (!selectedCustType || !selectedCustType.custType) {
      toastMsg = '请选择身份';
    } else if (!situationDesc) {
      toastMsg = '请填写困难说明';
    } else if ((needFileTypeList || []).filter((item = {}) => (item.uploadFiles || []).length).length <= 0) {
      toastMsg = '请上传证明材料';
    } else if (((contractInfo || {}).contractList || []).length && (!contractInfo.checkBoxChecked)) {
      toastMsg = '请阅读并同意相关协议';
    }
    if (toastMsg) {
      Madp.showToast({
        title: toastMsg,
        icon: 'none',
        duration: 2000,
      });
      return false;
    }
    return true;
  }

  // 兼容存量客户（仅做提交资料）
  oldSubmitInfo = async () => {
    const { selectedCustType, situationDesc, needFileTypeList } = this.state;
    const fileParam = {};
    const applyFiles = {};
    needFileTypeList.forEach((checkItem) => {
      fileParam[`img.${(checkItem || {}).category}`] = (checkItem || {}).uploadFiles;
      applyFiles[`img.${(checkItem || {}).category}`] = '';
    });

    let needFileList = [];
    if (this.serviceType === 'advanced-stage') {
      const { needFileList: advancedStageNeedFileList } = getStore('advancedStageInfo') || {};
      needFileList = advancedStageNeedFileList;
    } else {
      const { needFileList: extendRepayNeedFileList } = getStore('extendRepayInfo') || {};
      needFileList = extendRepayNeedFileList;
    }

    const { ret, data } = await Dispatch.repayment.postLoanAddData({
      applyNo: this.applyNo,
      additionalApplyInfo: {
        custType: selectedCustType.custType,
        desc: situationDesc,
      },
      applyFiles,
    }, fileParam);
    const { applyStatus } = data || {};
    if (ret === '0') {
      this.applyStatusNext(applyStatus, needFileList);
    } else {
      const urlParam = 'status=3';
      if (this.serviceType === 'advanced-stage') { // 再分期跳转链接
        Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}` });
      } else { // 延后还跳转链接
        Madp.redirectTo({ url: `/pages/service-result/index?${urlParam}&serviceType=extend&repaymentFlag=${this.repaymentFlag}` });
      }
    }
  }

  applyStatusNext(status, needFileList) {
    let needManualKyc = false; // 标识是否需要人工kyc
    needFileList && needFileList.forEach((item) => {
      if (item.fileType === 'K02') {
        needManualKyc = true;
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'NeedManualKyc', beaconContent: { cus: {} } });
      }
    });

    switch (status) {
      case '3': // 等待风控结果，轮询
        if (this.serviceType === 'advanced-stage') { // 再分期轮询弹窗
          Util.router.replace({
            path: `/pages/bill-advanced-stage/loading?billType=advanced-stage&applyNo=${this.applyNo}${needManualKyc ? '&needManualKyc=1' : ''}`,
          });
        } else { // 延后还进轮询页
          Util.router.replace({
            path: `/pages/bill-extend/loading?billType=extend&applyNo=${this.applyNo}${needManualKyc ? '&needManualKyc=1' : ''}`,
          });
        }
        break;
      case '6': // 风控拒绝，办理失败
        if (this.serviceType === 'advanced-stage') {
          Util.router.replace({ // 再分期
            path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=3&subStatus=2&repaymentFlag=${this.repaymentFlag}`,
          });
        } else {
          Util.router.replace({ // 延后还
            path: `/pages/service-result/index?_windowSecureFlag=1&status=3&subStatus=2&serviceType=extend&repaymentFlag=${this.repaymentFlag}`,
          });
        }
        break;
      case '7': // 审核通过
        if (this.serviceType === 'advanced-stage') { // 再分期去还款
          if (this.miniChannelFlag) {
            Taro.navigateTo({
              url: `/pages/express-repay/index?_windowSecureFlag=1&billType=${this.serviceType}&repaymentFlag=${this.repaymentFlag}`
            });
          } else {
            Util.router.replace({
              path: `/pages/express-repay/index?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}`,
              query: {
                billType: this.serviceType
              }
            });
          }
        } else { // 延后还去还款信息确认页
          Util.router.replace({
            path: `/pages/bill-extend/confirm?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}`,
            query: {
              billType: this.serviceType,
              applyNo: this.applyNo
            }
          });
        }
        break;
      case '5': // 提交完成，审核中（人工kyc）
        if (this.serviceType === 'advanced-stage') {
          Util.router.replace({
            path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=2${needManualKyc ? '&subStatus=1' : ''}`,
          });
        } else {
          Util.router.replace({ // 延后还
            path: `/pages/service-result/index?_windowSecureFlag=1&serviceType=extend&status=2${needManualKyc ? '&subStatus=1' : ''}&repaymentFlag=${this.repaymentFlag}`,
          });
        }
        break;
      case '8': // 提交完成，审核中
        if (this.serviceType === 'advanced-stage') {
          Util.router.replace({
            path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=2&subStatus=2&repaymentFlag=${this.repaymentFlag}`,
          });
        } else {
          Util.router.replace({ // 延后还
            path: `/pages/service-result/index?_windowSecureFlag=1&status=2&serviceType=extend&subStatus=2&repaymentFlag=${this.repaymentFlag}`,
          });
        }
        break;
      case '9': // 提交完成，办理成功
        if (this.serviceType === 'advanced-stage') { // 再分期
          Util.router.replace({
            path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=1&repaymentFlag=${this.repaymentFlag}`,
          });
        } else {
          Util.router.replace({ // 延后还
            path: `/pages/service-result/index?_windowSecureFlag=1&status=1&serviceType=extend&repaymentFlag=${this.repaymentFlag}`,
          });
        }
        break;
      case '10': // 风控拒绝或提交完成，办理失败
        if (this.serviceType === 'advanced-stage') {
          Util.router.replace({
            path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=3&subStatus=3&repaymentFlag=${this.repaymentFlag}`,
          });
        } else {
          Util.router.replace({ // 延后还
            path: `/pages/service-result/index?_windowSecureFlag=1&status=3&subStatus=3&serviceType=extend&repaymentFlag=${this.repaymentFlag}`,
          });
        }
        break;
      default: // 异常情况
        Madp.showToast({
          icon: 'none',
          title: '系统繁忙，请退出后重试',
          duration: 1500,
        });
        break;
    }
  }

  getCommonInfo = () => {
    let serviceCommonInfo = {};
    let needFileList = [];
    if (this.serviceType === 'advanced-stage') {
      const { applyNo, extendPackageInfo, awardNo, needFileList: advancedStageNeedFileList, firstPhasePeriods, extendInstallTotalCnt, expectRepayAmtPercent } = getStore('advancedStageInfo') || {};
      const selectedBillList = getStore('selectedBillList');
      needFileList = advancedStageNeedFileList;
      if (this.applyInfoDone) {
        // 服务办理申请信息已保存过，无需再次提交
        serviceCommonInfo = {
          applyNo: applyNo || this.applyNo,
        };
      } else {
        serviceCommonInfo = {
          applyNo: applyNo || this.applyNo,
          extendApplyInfo: {
            orderNoList: selectedBillList,
            awardNo,
            extendPackage: extendPackageInfo,
            firstPhasePeriods,
            extendInstallTotalCnt,
            expectRepayAmtPercent,
          }
        };
      }
    } else {
      const { applyNo, delayRepayAdjustInfo, needFileList: extendRepayNeedFileList } = getStore('extendRepayInfo') || {};
      const {
        adjustCnt, orderNoList, delayRepayReason, awardNo, needRepayType, beforeNeedRepayAmt
      } = delayRepayAdjustInfo || {};
      needFileList = extendRepayNeedFileList;
      if (this.applyInfoDone) {
        // 服务办理申请信息已保存过，无需再次提交
        serviceCommonInfo = {
          applyNo: applyNo || this.applyNo,
        };
      } else {
        serviceCommonInfo = {
          applyNo: applyNo || this.applyNo,
          delayRepayApplyInfo: {
            adjustCnt,
            orderNoList,
            delayRepayReason,
            awardNo,
            needRepayType,
            beforeNeedRepayAmt,
          },
        };
      }
    }

    return { serviceCommonInfo, needFileList };
  }

  gotoFaqs() {
    if (this.miniChannelFlag && (this.serviceType === 'advanced-stage' || this.serviceType === 'extend')) {
      Taro.navigateTo({
        url: `/pages/identity/faqs?serviceType=${this.serviceType}`
      });
    } else {
      Madp.navigateTo({
        url: `/pages/identity/faqs?serviceType=${this.serviceType}`
      });
    }
  }

  // 处理从补充身份证回来的流程接续: 不需补充资料则直接去提交案件
  submit = async (fileParam) => {
    const { selectedCustType, situationDesc } = this.state;
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'UpdateIDCardSuccess' });
    const { applyNo, applyStatus } = getStore('consultReapayInfo') || {};
    // 1、先调用补资料接口提交信息
    const result = await Dispatch.repayment.postLoanAddCaseData({
      applyNo,
      applyFiles: {}, // 网关会将fileParam文档流里填充在这里
      custType: selectedCustType.custType, // 身份类型
      issueDesc: situationDesc, // 补充原因
      ...this.loginInfoHash
    }, fileParam);

    // 2、115调用一次案件提交接口，用以更新案件状态；130状态不调用案件提交接口，直接去分发页
    if (applyStatus === '115') {
      await this.submitCase();
    } else {
      // 更新资料列表
      const { data } = result || {};
      const { needMaterialList } = data || {};
      const currentConsultReapayInfo = getStore('consultReapayInfo') || {};
      const { judicialConfirmFlagFromApply, businessHandleDays } = currentConsultReapayInfo;
      this.businessHandleDays = businessHandleDays;
      setStore({
        consultReapayInfo: {
          ...currentConsultReapayInfo,
          needMaterialList
        }
      });
      // 3. 上报埋点
      dispatchTrackEvent({
        target: this,
        event: EventTypes.BC,
        beaconId: 'consultReapayInfoSubmitCase',
        beaconContent: {
          cus: {
            ...this.trackInfo,
            applyStatus
          }
        }
      });
      // 4. 直接分发页面
      this.applyStatusNextNew(applyStatus, judicialConfirmFlagFromApply);
    }
  }

  getLoginInfo = async () => {
    const { userIdHash, custIdHash } = await getLoginInfo() || {};
    this.loginInfoHash = { userHashNo: userIdHash, custHashNo: custIdHash };
  }


  // 提交案件-用以翻转状态
  submitCase = async () => {
    // 1. 准备提交参数
    const param = {
      serviceType: '006',
      applyNo: this.applyNo,
      ...this.loginInfoHash,
    };
    // 2. 提交案件信息
    const result = await Dispatch.repayment.postLoanServiceSubmitCase(param);
    const { data } = result || {};
    const { applyStatus, needMaterialList, businessHandleDays, judicialConfirmFlag } = data || {};
    // 更新 consultReapayInfo 中的 needMaterialList（透传给loading页面进行分发）
    const currentConsultReapayInfo = getStore('consultReapayInfo') || {};
    this.businessHandleDays = businessHandleDays;
    setStore({
      consultReapayInfo: {
        ...currentConsultReapayInfo,
        needMaterialList,
        businessHandleDays
      }
    });
    // 3. 上报埋点
    dispatchTrackEvent({
      target: this,
      event: EventTypes.BC,
      beaconId: 'consultReapayInfoSubmitCase',
      beaconContent: {
        cus: {
          ...this.trackInfo,
          applyStatus
        }
      }
    });
    // 4. 如果需要跳转到审核页(一般是非司法确认的情况)
    this.applyStatusNextNew(applyStatus, judicialConfirmFlag);
  }

   // 根据案件结果处理
   // 请用中文回答，解释这个函数 ai!
   applyStatusNextNew = (status, judicialConfirmFlag, businessHandleDays) => {
     const urlParam = this.resultRedirectUrl
       ? `applyNo=${this.applyNo}&status=1&resultRedirectUrl=${this.resultRedirectUrl}&repaymentFlag=${this.repaymentFlag}`
       : `applyNo=${this.applyNo}&status=1&repaymentFlag=${this.repaymentFlag}`;
     switch (status) {
       case '130': // 待人工审核: 判断是资料补充审核中（此场景为人工审核中被打回需补资料，此时仍是130，资料类型中有待补充内容）、司法审核中、还是待资料补充(需要看案件结果查询有没有返回)
         if (judicialConfirmFlag === 1 || judicialConfirmFlag === 2) { // 司法审批中
           this.toServicesReapyResult(3);
         } else { // 资料审批中
           this.toServicesReapyResult(2);
         }
         break;
       case '140': // 待风控审核
         Madp.redirectTo({
           url: `/pages/consult-repay-apply/loading?${urlParam}`
         });
         break;
       case '150': // 案件审核拒绝
         this.toServicesReapyResult(4);
         break;
       case '160': // 案件审核通过，待确认要素变更
         Madp.redirectTo({
           url: `/pages/consult-repay-apply/comfirm?applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}`
         });
         break;
       case '200': // 审核通过，去还款
         if (this.miniChannelFlag) {
           Madp.redirectTo({
             url: `/pages/express-repay/index?_windowSecureFlag=1&billType=preConsultRepay&applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}`
           });
         } else {
           Util.router.replace({
             path: `/pages/express-repay/index?_windowSecureFlag=1&applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}`,
             query: {
               billType: 'preConsultRepay'
             }
           });
         }
         break;
       case '210': // 案件审核通过，业务办理成功
         this.toServicesReapyResult(1);
         break;
       case '220': // 案件审核通过，业务办理失败
         this.toServicesReapyResult(6);
         break;
       default: // 异常情况
         this.toServicesReapyResult(6);
         break;
     }
   }

   toServicesReapyResult(status) {
     Util.router.replace({
       path: `/pages/standard-service-result/index?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}&serviceType=${StandardService.ConsultRepayApply}&status=${status}&businessHandleDays=${this.businessHandleDays}`,
     });
   }
   render() {
     const {
       contractInfo, showIdentitySheet, selectedCustType, needFileTypeList,
       situationDesc, showExample, exampleUrl, showRestagingModel
     } = this.state;
     return (
       <MUView className="pages-bg">
         <MUView className="complete-information">
           <MUImage
             mode="widthFix"
             beaconId="headerBanner"
             className="complete-information-banner"
             src={this.serviceInfo.bgImg}
           />
           <MUView className="complete-information-identity">
             <MUView className="complete-information-identity-text">
               <MUText className="star">*</MUText>
               您目前的身份是
             </MUView>
             <MUListItem
               beaconId="Identity"
               className={`complete-information-identity-input ${selectedCustType.custTypeName ? '' : 'extra-text-grey'}`}
               title="身份选择"
               renderExtraText={<MUView>{selectedCustType.custTypeName || '请选择'}</MUView>}
               arrow="mu-icon-arrow-right"
               onClick={() => { dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'ClickIdentitySheet' }); this.setState({ showIdentitySheet: true }); }}
             />
             <MUActionSheet
               beaconId="IdentitySheet"
               cancelText="取消"
               isOpened={showIdentitySheet}
               onClose={() => this.setState({ showIdentitySheet: false })}
             >
               {this.custAndFileList.map((identity) => (
                 <MUActionSheetItem beaconId="IdentitySheetItem" onClick={() => this.chooseCustType(identity)}>
                   {identity.custTypeName}
                 </MUActionSheetItem>
               ))}
             </MUActionSheet>
           </MUView>
           <MUView className="complete-information-identity">
             <MUView className="complete-information-identity-text">
               <MUText className="star">*</MUText>
               困难说明
             </MUView>
             <MUView className={`complete-information-textArea${isMiniProgram() ? '--mini' : ''}`}>
               <MUTextarea
                 count
                 value={situationDesc}
                 onChange={this.textInput.bind(this)}
                 maxLength={300}
                 placeholder="详细内容"
                 height={200}
                 beaconId="InputDetail"
               />
             </MUView>
           </MUView>
           <MUView>
             {needFileTypeList.map((item = {}, i) => {
               const fileList = item.originFiles || [];
               return (
                 <MUView className="transit">
                   <MUView className="transit__multipart">
                     <MUView className="transit__multipart__title">
                       {needFileTypeList.length === 1 ? <MUText className="star">*</MUText> : null}
                       {item.title}
                     </MUView>
                     <MUView>
                       <MUText className={item.guide && (item.desc.length <= 22 || item.desc.length >= 28) ? '' : 'transit__multipart__desc'}>{item.desc}</MUText>
                       <MUText
                         beaconId="exampleImgClick"
                         className={`transit__multipart__guide-${themeColor === '#E60027' ? 'red' : 'blue'}`}
                         onClick={() => this.showExampleUrl(item.url)}
                       >{item.guide}</MUText>
                     </MUView>
                   </MUView>
                   <MUView className="complete-information-provement-picker">
                     <MUView className="complete-information-provement-picker-count">
                       <MUText className="complete-information-provement-picker-count-tips">*单个文件大小请勿超过10M</MUText>
                       <MUText className="complete-information-provement-picker-count-countText">{`${fileList.length}/4`}</MUText>
                     </MUView>
                     <ImagePicker
                       className="complete-information-provement-picker-files"
                       files={fileList}
                       count={4}
                       onChange={(files) => this.onImgPicked(files, item.category)}
                     />
                   </MUView>
                 </MUView>
               );
             })}
           </MUView>
           {contractInfo.contractList && contractInfo.contractList.length ? (
             <MUView className="complete-information-contract">
               <MURadio
                 type="row"
                 className="complete-information-contract-checker"
                 beaconId="CompleteInformationChecker"
                 options={[{
                   type: 'protocal',
                   labelLeft: '我已阅读并同意',
                   linkText: contractInfo.contractText,
                   onLink: () => this.toViewContract(),
                   value: 'checked'
                 }]}
                 value={contractInfo.checkBoxChecked ? 'checked' : ''}
                 onClick={() => this.handlerCheckboxClick()}
               />
               <AgreementDrawer
                 agreementViewProps={{
                   type: (contractInfo.contractList && contractInfo.contractList.length > 1) ? 1 : 2,
                   list: contractInfo.contractList,
                   current: 0,
                 }}
                 show={contractInfo.showContract}
                 close={() => this.contractSubmitClick(false)}
                 submit={() => this.contractSubmitClick(true)}
                 totalCount={0}
               />
             </MUView>) : null}
           <MUView className="complete-information-commit">
             <MUButton type="primary" beaconId="CompleteInformationCommit" onClick={this.submitInfo.bind(this)}>提交</MUButton>
           </MUView>
           {this.serviceType !== 'consult-repay' ? <MUView className="complete-information-questions" style={`color: ${themeColor}`}>
             <MUText beaconId="faqs" onClick={() => this.gotoFaqs()}>常见问题</MUText>
           </MUView> : null}
           <MUView className="complete-information-tip">
             <MUImage className="complete-information-tip-icon" src={Safe} beaconId="SafeIcon" />
             <MUText className="complete-information-tip-text">招联金融采用银行级信息安全加密</MUText>
           </MUView>
           <MUModal
             beaconId="exampleModal"
             type="activity"
             isOpened={showExample}
             src={exampleUrl}
             confirmText="关闭"
             onImageClick={() => { }}
             onClose={() => this.setState({ showExample: false, exampleUrl: '' })}
           />
           <MUModal
             className="extend-repay-modal"
             beaconId="RestagingInforModel"
             type="tip"
             isOpened={showRestagingModel}
             closeOnClickOverlay={false}
           >
             <MUView className="extend-repay-modal-container">
               <MUIcon className="img" value="waiting" size={65} />
               <MUView className="title">申请资料补充</MUView>
               <MUView className="desc">{`办理${this.serviceInfo.serviceName}前，您需补充相关证明材料，我们将进行人工审核`}</MUView>
               <MUButton
                 className="btn"
                 beaconId="RestagingInforModelClose"
                 type="primary"
                 onClick={() => this.setState({ showRestagingModel: false })}
               >
                 我知道了
               </MUButton>
               {this.serviceType !== 'consult-repay' ? <MUImage className={classNames('slogan', { 'slogan-plus': this.isVplus })} src={this.isVplus ? sloganUrl.middleVplus : sloganUrl.middle} /> : null}
             </MUView>
           </MUModal>
         </MUView>
         {/* 用于小程序端压缩图片的画布组件 */}
         {isMiniProgram() && <Canvas
           className="compressImgCanvas"
           id="compressImgCanvas"
           canvasId="compressImgCanvas"
           style={{ width: '1200px', height: '1200px', position: 'absolute', top: '-9999px', left: '-9999px' }}
         />}
       </MUView>
     );
   }
}
