import { Component } from '@tarojs/taro';
import { MUView } from '@mu/zui';
import { track, EventTypes } from '@mu/madp-track';
import pageHoc from '@utils/pageHoc';
import { Url } from '@mu/madp-utils';
import './information.scss';

@track({ event: EventTypes.PO }, {
  pageId: 'CompleteInformationFaqs',
  dispatchOnMount: true,
})

@pageHoc({ title: '常见问题' })

export default class Information extends Component {
  constructor(props) {
    super(props);
    this.faqs = [];
    this.repayExtendFaqs = [
      {
        question: 'Q1：提交申请就可以延期吗？',
        answer: 'A1：您按要求提交申请后，后台会进行综合评定，若一经审核通过，会自动为您进行延期办理。'
      },
      {
        question: 'Q2：如何知道我是否申请成功？',
        answer: 'A2：3个工作日内会以短信通知您办理结果，请注意查收。'
      },
      {
        question: 'Q3：我已经逾期了，还能申请延后还吗？',
        answer: 'A3：建议您先结清当期欠款后，再来提交延后还申请。'
      },
      {
        question: 'Q4：我的申请没通过，还能再来申请吗？',
        answer: 'A4：抱歉，延后还仅可申请一次。'
      },
      {
        question: 'Q5：我已经延期成功了，还能再申请吗？',
        answer: 'A5：抱歉，延后还仅可办理一次。'
      }
    ];
    this.restagingFaqs = [
      {
        question: 'Q1：补充资料就可以再分期吗？',
        answer: 'A1：补充资料并提交后，后台将会进行综合评估，补充资料可以提升审核通过率哦~'
      },
      {
        question: 'Q2：如何知道我是否办理成功？',
        answer: 'A2：办理结果将会在3个工作日内以短信通知您，请注意查收~'
      },
      {
        question: 'Q3：我已经逾期了，还能申请再分期吗？',
        answer: 'A3：您需先还逾期欠款，才可以办理再分期服务哦'
      },
      {
        question: 'Q4：我的申请没通过，还能再来申请吗？',
        answer: 'A4：如您存在可办理再分期的借据，即可再次申请，请继续累计信用以提高申请通过率'
      },
      {
        question: 'Q5：我的再分期申请通过了，还能再次申请吗？',
        answer: 'A5：抱歉，再分期仅可办理一次'
      }
    ];
  }

  render() {
    this.faqs = Url.getParam('serviceType') === 'advanced-stage' ? this.restagingFaqs : this.repayExtendFaqs;
    return (
      <MUView className="complete-information-faps">
        {this.faqs.map((item, i) => (
          <MUView>
            <MUView className={i === 0 ? 'complete-information-faps-textQuestion complete-information-faps-top' : 'complete-information-faps-textQuestion'}>
              {item.question}
            </MUView>
            <MUView className="complete-information-faps-textAnswer">{item.answer}</MUView>
          </MUView>
        ))}
      </MUView>
    );
  }
}
