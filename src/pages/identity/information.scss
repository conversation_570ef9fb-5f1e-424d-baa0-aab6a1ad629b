.complete-information {
  background-color: #F3F3F3;
  &-banner {
    display: block;
    width: 100%;
    height: auto;
  }
  .lui__image-single-padding {
    margin-top: 0;
    width: 100%;
  }

  &-identity {
    &-text {
      font-weight: 400;
      font-size: 26px;
      color: #808080;
      line-height: 26px;
      padding: 40px 0 20px 30px;
    }

    &-input {
      background: #fff;
      padding: 24px 30px;
      .item-extra {
        justify-content: flex-start;
        .item-extra__info {
          padding-right: 0;
        }
      }
    }
    &-input.extra-text-grey {
      .item-extra {
        color: #CACACA;
      }
    }
    &-radio {
      background: #fff;
      padding: 14px 30px;

      .mu-radio__name {
        margin-right: 0.78rem;
      }
    }
  }
  &-textArea {
    position: relative;
  }
  &-textArea--mini {
    position: relative;
  }

  &-provement {
    &-text {
      font-weight: 400;
      font-size: 26px;
      color: #808080;
      line-height: 26px;
      margin: 40px 0 20px 30px;
    }

    &-picker {
      background: #fff;
      padding-bottom: 30px;
      .repay-img-picker {
        margin-top: 10px;
        padding-left: 30px;
        .img-picker-preview-block {
          margin-right: 40px;
        }
      }
      &-count {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24px 20px 10px 30px;
        &-countText, &-tips {
          font-weight: 400;
          font-size: 24px;
          color: #808080;
          // letter-spacing: 0;
          text-align: right;
        }
      }
      &-files {
        height: 120px;
        padding-bottom: 30px;
        padding-left: 30px;
        .at-image-picker__flex-box {
          height: 120px;
          padding: 0 0;
          .at-image-picker__flex-item {
            margin: 0;
          }
        }
        .at-image-picker__item {
          width: 120px;
          height: 120px;
        }
      }
    }
  }

  &-contract {
    padding: 40px 30px 0;
  }

  &-commit {
    padding: 40px 30px;
  }

  &-questions {
    font-weight: 400;
    font-size: 28px;
    color: #3477ff;
    text-align: center;
    line-height: 36px;
  }

  &-tip {
    text-align: center;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 60px 0;

    &-icon {
      width: 28px;
      height: 32px;
      padding-right: 12px;
    }

    &-text {
      font-weight: 400;
      font-size: 24px;
      color: #a6a6a6;
    }
  }

  &-faps {
    background: #fff;
    padding: 0 30px;
    height: 100vh;

    &-top {
      padding-top: 30px;
    }

    &-textQuestion {
      font-weight: 600;
      font-size: 28px;
      color: #333333;
      line-height: 42px;
      // padding-top: 60px;
    }

    &-textAnswer {
      font-weight: 400;
      font-size: 28px;
      color: #808080;
      line-height: 42px;
      padding-bottom: 60px;
    }
  }
  .transit {
    &__multipart {
      margin: 40px 30px 20px;
      font-size: 24px;
      line-height: 1;
      color: #808080;
      font-weight: 400;

      &__title {
        margin-bottom: 16px;
        font-size: 28px;
        color: #333;
      }

      &__desc {
        display: block;
        margin-bottom: 16px;
      }

      &__guide-blue {
        color: #3477FF;
      }

      &__guide-red {
        color: red;
      }
    }
  }
  .star {
    color: #fe5a5f;
  }
  .extend-repay-modal {
    .mu-modal__container {
      padding: 40px 30px 10px 30px;
      width: 480px;
    }

    &-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .title {
        margin-top: 40px;
        height: 54px;
        color: #333333;
        text-align: center;
        font-size: 36px;
        font-weight: 600;
        line-height: 54px;
      }

      .desc {
        margin-top: 20px;
        height: 84px;
        color: #808080;
        text-align: center;
        font-size: 28px;
        line-height: 42px;
      }

      .btn {
        width: 100%;
        height: 88px;
        margin-top: 40px;
        margin-bottom: 30px;
      }

      .slogan {
        width: 206px;
        height: 44px;
        padding-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    
        &-plus {
          width: 249px;
          height: 44px;
        }
      }
    }
  }
}
