@import '../../components/weapp/index.scss';

.repay-near {

  // 客服组件样式
  .chat-entry {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: -186px;
  }

  @supports (padding-bottom: constant(safe-area-inset-bottom)) {
    .chat-entry {
      margin-top: calc(-186px - constant(safe-area-inset-bottom));
    }
  }

  @supports (padding-bottom: env(safe-area-inset-bottom)) {
    .chat-entry {
      margin-top: calc(-186px - env(safe-area-inset-bottom));
    }
  }

  .future-list-content {
    width: 100%;
    min-height: 100vh; // 很关键

    &-header {
      background-color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;

      .date-text {
        margin-top: 60px;
        font-size: 32px;
        line-height: 32px;
        color: #808080;
      }

      .amount-text {
        margin-top: 30px;
        margin-bottom: 60px;
        font-size: 100px;
        line-height: 100px;
        font-weight: bold;
        color: #333333;
        font-family: DIN Alternate;
      }
    }
  }

  .near-list-content {
    width: 100%;
    min-height: 100vh; // 很关键
  }

  .theme-background-color {
    background: $color-brand;
  }
}

.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}