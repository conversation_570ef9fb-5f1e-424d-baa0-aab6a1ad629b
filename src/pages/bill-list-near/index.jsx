/* eslint-disable array-callback-return */
/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import { MUView, MUNavBarWeapp } from '@mu/zui';
import { track, EventTypes, dispatchTrackEvent, disableTrackAlert } from '@mu/madp-track';
import { Url } from '@mu/madp-utils';
import { repayStatusType } from '@utils/constants';
import BillListNew from '@components/bill-list-new';
import EmptySign from '@components/empty-sign';
import { ChatEntry } from '@mu/chat-entry-component';
import { getStore, setStore } from '@api/store';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import pageHoc from '@utils/pageHoc';
import ChannelConfig from '@config/index';


import './index.scss';

// import '@components/bill-list/index.scss';
// import '@components/list-item/index.scss';
// import '@components/empty-sign/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/bill-list-new/index.scss');
  require('@components/list-item-new/index.scss');
  require('@components/empty-sign/index.scss');
}

const themeColor = Util.getThemeColor(ChannelConfig.theme);

disableTrackAlert();
@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      expressScene: Url.getParam('isExpress') === '0' ? Url.getParam('expressScene') || '' : '',
      custStatus: Url.getParam('custStatus') || '',
    }
  }
}, {
  pageId: 'BillListNear',
  dispatchOnMount: true,
})
@pageHoc({ title: '待还账单' })
export default class BillListNear extends Component {
  config = {
    navigationBarTitleText: '待还账单',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      selectedBillList: [],
      nearBillList: [],
      unicomBillList: [],
      sevenDayBillList: [],
      readOnlyBillList: [],
      disabledBtn: false,
      disabledSelect: false,
      hasOverDueBill: false,
      overSeven: false,
      repayDate: '',
      nearBillsTotalAmount: 0,
      showSevenDayBillTip: false,
    };
    this.nearBills = {};
    this.billSectionList = [];
    this.showNotice = Url.getParam('showNotice') === '1';
    this.fromIndex = Url.getParam('fromIndex') === '1';
    this.selectAll = Url.getParam('selectAll') === '1';
    this.fromBargaining = Url.getParam('fromBargaining') === '1';
    this.chatEntryParam = getStore('chatEntryExtraParam') || {};
    this.overdueOrderNoSet = new Set();
    this.custStatus = Url.getParam('custStatus') || '';
  }

  async componentDidMount() {
    if (this.fromIndex) {
      // 从首页来的，已经获取过一遍nearbills，无需再发一次请求
      if (!Object.keys(getStore('nearBills')).length) {
        // 但如果是个空对象，则获取一遍
        await Dispatch.repayment.getNearBills({}, { setNearAmount: true });
      }
      if (process.env.TARO_ENV === 'h5') {
        const url = window.location.href;
        // 更新一遍fromIndex，防止各种跳转之后回来没有更新账单
        window.history.replaceState(null, null, url.replace('fromIndex=1', 'fromIndex=0'));
      }
    } else {
      // 不是从首页来的直接获取一遍
      await Dispatch.repayment.getNearBills({}, { setNearAmount: true });
    }
    const repayStatus = getStore('repayStatus');
    if (this.showNotice) {
      this.showNotice = false;
      if (repayStatus === repayStatusType.overDue) Util.toast('您有逾期账单，已默认勾选');
    }
    // 针对返回的账单中有七天外账单的情况，设置不可选中，不可还款
    if (repayStatus === repayStatusType.outSeven || repayStatus === '3') {
      this.setState({ disabledSelect: true, disabledBtn: true });
    }
    this.nearBills = getStore('nearBills') || {};
    this.allBills = getStore('allBills') || {};
    const { repayDate, courtCostBalance = 0 } = this.nearBills;
    const repayBillList = this.nearBills.repayBillList || [];
    // 默认先选逾期
    const selectedBillList = [];
    const overdueList = [];
    const todayList = [];
    const sevenDayList = [];
    const unicomList = [];
    // repayBillList[repayBillList.length-1].belongUnicomContractOrder = 'Y';
    repayBillList.forEach((bill) => {
      if (bill.displayOverdueDays) {
        this.overdueOrderNoSet.add(bill.orderNo);
      }
    });
    repayBillList.forEach((bill) => {
      if (bill.canPayFlag === 'Y' && !(bill.belongUnicomContractOrder === 'Y' && !this.overdueOrderNoSet.has(bill.orderNo))) {
        if (this.selectAll) selectedBillList.push(bill); // 全选的话直接添加
        else if (bill.usedToCalculate === 'Y') selectedBillList.push(bill); // 非全选只加用于首页计算的账单
      }
      if (bill.displayOverdueDays) {
        overdueList.push(bill);
      } else if (bill.belongUnicomContractOrder === 'Y' && !this.overdueOrderNoSet.has(bill.orderNo)) {
        unicomList.push(bill);
      } else if (Number(bill.surplusDays) === 0) {
        todayList.push(bill);
      } else {
        sevenDayList.push(bill);
      }
    });

    setStore({ selectedBillList });
    const TodayAndSevenDays = Boolean(sevenDayList.length || todayList.length);
    // 没有近期待还账单
    if (repayBillList.length === 0) dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'NoRepayBillList' });
    // 有可见不可还账单埋点
    if (this.nearBills.showBillList && this.nearBills.showBillList.length) dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'DisplayOnly' });
    // 有逾期且有7天内到期埋点
    if (overdueList.length && TodayAndSevenDays) dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'OverdueWithInSeven' });
    // 有逾期无7天内到期埋点
    else if (overdueList.length && !TodayAndSevenDays) dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'OverdueOnly' });
    // 无逾期有7天内到期埋点
    else if (!overdueList.length && TodayAndSevenDays) dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'WithoutOverdue' });
    let nearBillsTotalAmount = getStore('nearBillsTotalAmount');
    if (Number(courtCostBalance) > 0) {
      nearBillsTotalAmount = Util.floatMinus(Number(getStore('nearBillsTotalAmount') || '0.00'), Number(courtCostBalance || 0)).toFixed(2);
    }
    this.setState({
      nearBillList: repayBillList,
      sevenDayBillList: sevenDayList,
      unicomBillList: unicomList.length ? unicomList : [],
      showSevenDayBillTip: sevenDayList.length && (overdueList.length || todayList.length),
      selectedBillList,
      readOnlyBillList: this.nearBills.showBillList,
      hasOverDueBill: overdueList.length > 0,
      overSeven: !TodayAndSevenDays,
      repayDate,
      nearBillsTotalAmount,
    });
    this.setExtraParam();
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '待还账单' });
  }

  handleSelect = (bill) => {
    // 由于存在同笔借据，两个账单同时展示的情况，不能单纯用orderNo作为区分
    // 故直接用账单本身判断是否勾选
    if (bill.canPayFlag === 'N') {
      const reason = Util.getCantPayReason(bill);
      if (reason) Util.toast(reason);
      return;
    }
    let { selectedBillList } = this.state;
    const { nearBillList } = this.state;
    const billIndex = selectedBillList.indexOf(bill);
    if (this.unicomBillCheck(bill, selectedBillList, billIndex)) {
      return;
    }
    if (billIndex > -1) {
      // 取消选中，如果反选的是任意逾期账单，则反选所有小于该逾期天数的账单
      const maxOverdue = Math.max(...selectedBillList.map((item) => item.displayOverdueDays));
      // 选呆账的呆账借据号，03、04呆账，05核销
      const MustRapaybillOrderNos = selectedBillList.map((item) => { if (item.loanStatus === '03' || item.loanStatus === '04' || item.loanStatus === '05') { return item.orderNo; } });
      if (maxOverdue && bill.displayOverdueDays) {
        if (bill.loanStatus !== '03' && bill.loanStatus !== '04' && bill.loanStatus !== '05') {
          // 取消的不是呆账的情况
          if (MustRapaybillOrderNos.indexOf(bill.orderNo) > -1) {
            // 取消的是呆账借据中的，一笔非呆账账单的情况
            // 不可取消
            Util.toast('该笔借款到期账单须全部勾选');
            return;
          } else {
            // 取消的是正常逾期账单的情况
            // 有延期账单，并且点选的账单有延期的前提下，反选所有小于该逾期天数的账单
            // （反选的时候要排除同笔呆账借据的到期和逾期账单）
            // 先把自己去掉，否则后续判断大于等于选中逾期天数时会被保留
            selectedBillList.splice(billIndex, 1);
            const billList = selectedBillList.filter((item) => {
              // 呆账逾期账单、呆账到期账单必须勾选，呆账七天内可以不勾选
              if (MustRapaybillOrderNos.indexOf(item.orderNo) > -1 && Number(item.surplusDays) <= 0) {
                return item;
              } else if (item.displayOverdueDays >= bill.displayOverdueDays) {
                return item;
              }
            });
            selectedBillList = billList;
          }
        } else {
          // 取消的是呆账的时候，不给取消
          Util.toast('该笔借款到期账单须全部勾选');
          return;
        }
      } else if (Number(bill.surplusDays) === 0) {
        // 不是逾期，但是是到期账单
        if (MustRapaybillOrderNos.indexOf(bill.orderNo) > -1) {
          // 取消的是呆账借据中的，一笔非呆账账单的情况
          // 不可取消
          Util.toast('该笔借款到期账单须全部勾选');
          return;
        }
        selectedBillList.splice(billIndex, 1);
        const noInSeventbillList = selectedBillList.filter((item) => {
          // 取消的是当期账单，反选所有非当期账单. 呆账逾期账单、呆账到期账单必须勾选，呆账七天内可以不勾选
          if (MustRapaybillOrderNos.indexOf(item.orderNo) > -1 && Number(item.surplusDays) <= 0) {
            return item;
          } else if (Number(item.surplusDays) < 0) {
            return item;
          }
        });
        selectedBillList = noInSeventbillList;
      } else {
        // 不到期账单
        selectedBillList.splice(billIndex, 1);
      }
    } else {
      // 选中，判断到期是否选中。0818 tanbicheng
      const unselectedBillTodays = [];
      const unselectedBillList = nearBillList.filter((item) => {
        if (selectedBillList.indexOf(item) < 0) {
          if (Number(item.surplusDays) === 0) {
            unselectedBillTodays.push(item);
          }
          return item;
        }
      });
      // 选中，判断最长逾期是否选中
      const unselectedMaxOverdue = Math.max(...unselectedBillList.map((item) => item.displayOverdueDays));
      if (unselectedMaxOverdue > 0 && bill.displayOverdueDays < unselectedMaxOverdue) {
        Util.toast('需优先勾选最长逾期账单或全部逾期账单');
        return;
      } else if (unselectedMaxOverdue === 0 && unselectedBillTodays.length && unselectedBillTodays.indexOf(bill) === -1) {
        Util.toast('需优先勾选到期账单');
        return;
      }
      selectedBillList.push(bill);
    }
    this.setState({ selectedBillList });
    setStore({ selectedBillList });
  }

  // 联调合约机账单处理
  unicomBillCheck(bill, selectedBillList, billIndex) {
    if (bill.belongUnicomContractOrder === 'Y' && !this.overdueOrderNoSet.has(bill.orderNo)) {
      if (billIndex > -1) {
        selectedBillList.splice(billIndex, 1);
        this.setState({ selectedBillList });
        setStore({ selectedBillList });
      } else {
        Madp.showModal({
          content: '该笔为联通合约机消费，您每月按时缴纳话费则无需还款。您也可提前还款，还款后每月为您发放等额联通电子券（可在联通APP查看和使用），确认提前还款吗？',
          cancelText: '提前还款',
          confirmText: '我再想想',
          confirmColor: themeColor,
          success: (res) => {
            if (!res.confirm) {
              selectedBillList.push(bill);
              this.setState({ selectedBillList });
              setStore({ selectedBillList });
            }
          }
        });
      }
      return true;
    }
    return false;
  }

  onSelectAll() {
    const { nearBillList, selectedBillList } = this.state;
    // 排除不可还
    // let selectedBillList = nearBillList.filter((bill) => bill.canPayFlag === 'Y');
    let isToast = false;
    const unicomBills = [];
    let unicomBillLen = 0;
    nearBillList.forEach((bill) => {
      if (bill.canPayFlag !== 'Y') {
        isToast = true;
        return;
      }
      const isUnicom = bill.belongUnicomContractOrder === 'Y' && !this.overdueOrderNoSet.has(bill.orderNo);
      if (isUnicom) unicomBillLen += 1;
      if (selectedBillList.indexOf(bill) === -1) {
        if (isUnicom) {
          unicomBills.push(bill);
        } else {
          selectedBillList.push(bill);
        }
      }
    });
    this.setState({ selectedBillList });

    if (isToast) {
      Util.toast('全部借据中包含今日新增借据，无法全部勾选');
    } else if (unicomBills.length) {
      Madp.showModal({
        content: `您当期账单含有${unicomBillLen}笔联通合约机消费，联通合约机消费只需您每月按时缴纳话费即可，您确定要一同还款吗？`,
        cancelText: '提前还款',
        confirmText: '我再想想',
        confirmColor: themeColor,
        success: (res) => {
          if (!res.confirm) {
            selectedBillList.push(...unicomBills);
            this.setState({ selectedBillList });
          }
        }
      });
    }
  }

  onCancelAll() {
    const { selectedBillList } = this.state;
    // 筛选是呆账借据的账单，03、04呆账，05核销
    // const BadDebtsBills = selectedBillList.filter((item) => Number(item.displayOverdueDays) >= 90);
    const MustRapaybillOrderNos = selectedBillList.map((item) => { if (item.loanStatus === '03' || item.loanStatus === '04' || item.loanStatus === '05') { return item.orderNo; } });
    // 呆账借据的账单，到期和逾期的
    const BadDebtsBills = selectedBillList.filter((item) => MustRapaybillOrderNos.indexOf(item.orderNo) > -1 && Number(item.surplusDays) <= 0);
    if (BadDebtsBills.length) {
      // 全部取消账单，有呆账
      this.setState({ selectedBillList: BadDebtsBills });
    } else {
      // 全部取消账单，无呆账
      this.setState({ selectedBillList: [] });
    }
  }

  submitRepayment(param) {
    const { selectedBillList, nearBillList } = this.state;

    const expressScene = Url.getParam('expressScene') || '';
    const isExpress = Url.getParam('isExpress') || '';
    const queryParam = isExpress === '0' ? { expressScene, isExpress } : {};

    if (this.fromBargaining) {
      Madp.setStorageSync('bargain_bills', selectedBillList, 'SESSION');
      Madp.navigateBack();
      return;
    }
    const { isAllClear, isAllOnTime } = Util.judgeAllClear(nearBillList, selectedBillList) || {};
    const { isRedirect = '0' } = this.$router.params;
    setStore({ selectedBillList });
    // 需还法诉费
    const { courtCostBalance } = this.nearBills;
    if (courtCostBalance && Number(courtCostBalance) > 0) {
      param.amount = Util.floatAdd(Number(param.amount), Number(courtCostBalance)).toFixed(2);
      param.courtCostBalance = Number(courtCostBalance).toFixed(2);
    }
    const formatNearBillList = (nearBillList || []).filter((item = {}) => item.belongUnicomContractOrder !== 'Y');
    const formatSelectedBillList = (selectedBillList || []).filter((item = {}) => item.belongUnicomContractOrder !== 'Y');
    (`${isRedirect}` === '1' ? Util.router.replace : Util.router.push)({
      path: '/pages/express-repay/index?_windowSecureFlag=1&expressScene=12',
      query: {
        ...param,
        ...queryParam,
        currentBillClear: formatNearBillList.length === formatSelectedBillList.length ? 'Y' : '', // 非联通合约金账单需全部勾选
      }
    });
    dispatchTrackEvent({
      target: this,
      event: 'BC',
      beaconId: 'ClickToRepay',
      beaconContent: {
        cus: {
          isOnTimeClearAll: isAllOnTime && isAllClear ? 'Y' : 'N',
          expressScene: isExpress === '0' ? expressScene : '',
          custStatus: this.custStatus,
        }
      }
    });
  }

  /** 为招行渠道设置客服入口的参数 */
  setExtraParam() {
    if (Util.showChatEntry() && Madp.getChannel() === '3CMBAPP' && Object.keys(this.chatEntryParam).length) {
      Madp.eventCenter.trigger('CHAT_ENTRY_EXTRAPARAM');
    }
  }

  get getExtraParam() {
    const { hasOverDueBill, overSeven } = this.state;
    if (Object.keys(this.chatEntryParam).length) {
      return this.chatEntryParam;
    }
    const hasBill = !!(this.allBills && this.allBills.advanceBillList && this.allBills.advanceBillList.length)
      || !!(this.allBills && this.allBills.showBillList && this.allBills.showBillList.length)
      || !!(this.nearBills && this.nearBills.repayBillList && this.allBills.repayBillList.length);
    return {
      hasBill,
      overdue: hasOverDueBill,
      dueTagCust: !!(this.nearBills && this.nearBills.isDueTagCust)
        || !!(this.allBills && this.allBills.isDueTagCust),
      overSeven,
      needLogin: 1
    };
  }

  get getformattedDateStr() {
    const { repayDate } = this.state;
    const dateCollection = Util.getDateCollection(repayDate);
    return dateCollection.length === 3 ? `${dateCollection[1]}月${dateCollection[2]}日` : '';
  }

  render() {
    const {
      disabledSelect, disabledBtn, selectedBillList,
      nearBillList, readOnlyBillList, nearBillsTotalAmount,
      sevenDayBillList, unicomBillList, showSevenDayBillTip
    } = this.state;
    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="待还账单"
          leftArea={[
            {
              type: 'icon',
              value: 'back'
            }
          ]}
        />
        <MUView className="pages-bg repay-near">
          <MUView className="near-list-content" style={`min-height: ${ChannelConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
            {(nearBillList && nearBillList.length > 0) || (readOnlyBillList && readOnlyBillList.length > 0) ? (
              <MUView>
                <MUView className="future-list-content-header">
                  {this.getformattedDateStr && (
                    <MUView className="date-text">
                      {this.getformattedDateStr}
                      应还(元)
                    </MUView>
                  )}
                  <MUView className="amount-text">{nearBillsTotalAmount}</MUView>
                </MUView>
                <BillListNew
                  billList={nearBillList}
                  sevenDayBillList={sevenDayBillList}
                  unicomBillList={unicomBillList}
                  billType={this.fromBargaining ? 'bargaining' : '7days'}
                  btnContext={this.fromBargaining ? '去还价' : '去还款'}
                  selectedBillList={selectedBillList}
                  readOnlyBillList={readOnlyBillList}
                  onSelect={this.handleSelect}
                  submitRepayment={(param) => this.submitRepayment(param)}
                  cancelAll={() => this.onCancelAll()}
                  selectAll={() => this.onSelectAll()}
                  canAllSelect={false}
                  canSelectLength={nearBillList.filter((bill) => bill.canPayFlag === 'Y').length}
                  disabledSelect={disabledSelect}
                  disabledBtn={disabledBtn}
                  showCreditProductInfo="Y"
                  showCreditProductFlag="Y"
                  custStatus={this.custStatus}
                  showSevenDayBillTip={showSevenDayBillTip}
                />
              </MUView>
            ) : <EmptySign />}
          </MUView>
          {Util.showChatEntry() && <ChatEntry busiEntrance={Util.getBusiEntrance()} extraParam={this.getExtraParam} themeColor={themeColor} />}
        </MUView>
      </MUView>
    );
  }
}
