@import '../../components/weapp/index.scss';

.countdown {
  .repay-countdown {
    background-color: #fff;
    text-align: center;
    position: relative;
    height: 100vh;
    padding-top: 78px;

    .count-down-num {
      width: 160px;
      height: 160px;
      font-size: 80px;
      color: #333333;
      text-align: center;
      line-height: 80px;
      font-weight: 200;
      position: absolute;
      margin-top: 40px;
    }

    .count-down-ring {
      width: 160px;
      height: 160px;
      position: relative;
      margin: 0 auto;
    }

    .count-down-left,
    .count-down-right {
      width: 50%;
      height: 100%;
      position: absolute;
      overflow: hidden;
    }

    .count-down-left,
    .count-down-left-ring {
      left: 0;
    }

    .count-down-right,
    .count-down-right-ring {
      right: 1px;
    }

    .count-down-left-ring,
    .count-down-right-ring {
      width: 200%;
      height: 100%;
      position: absolute;
      /* stylelint-disable-next-line */
      border-width: 2PX;
      border-style: solid;
      border-radius: 50%;
      border-color: #3477FF #E5E5E5 #E5E5E5 #3477FF;
      box-sizing: border-box;
    }

    .count-down-left-ring {
      transform: rotate(135deg);
      animation: anim-count-down-left-ring 1s linear infinite;
      display: block;
    }

    .count-down-right-ring {
      transform: rotate(-45deg);
      animation: anim-count-down-right-ring 1s linear infinite;
      display: block;
    }

    @keyframes anim-count-down-left-ring {

      from,
      50% {
        /* from{ */
        transform: rotate(-225deg);
      }

      /* 50%, to { */
      to {
        transform: rotate(-45deg);
      }
    }

    @keyframes anim-count-down-right-ring {

      /* from, 50% { */
      from {
        transform: rotate(-45deg);
      }

      50%,
      to {
        /* to { */
        transform: rotate(135deg);
      }
    }

    &-waitText {
      height: 36px;
      font-size: 36px;
      color: #333333;
      text-align: center;
      line-height: 36px;
      font-weight: 500;
      margin: 62px 0 30px;
    }

    &-waitSubText {
      height: 28px;
      font-size: 28px;
      color: #FF8800;
      text-align: center;
      line-height: 28px;
      font-weight: 400;
    }
  }
}
.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}
