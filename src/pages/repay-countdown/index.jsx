/* eslint-disable indent */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import {
  MUView,
  MUNavBarWeapp
} from '@mu/zui';
import Madp from '@mu/madp';
import {
  Url, getCurrentPageUrlWithArgs
} from '@mu/madp-utils';
// import { setStore } from '@api/store';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import Dispatch from '@api/actions';
import { track, EventTypes } from '@mu/madp-track';
import './index.scss';

const waitText = '正在等待银行或支付账户返回结果…';
const waitSubText = '结果返回前，请不要重复提交';

const themeColor = Util.getThemeColor(channelConfig.theme);
@track({ event: EventTypes.PO }, {
  pageId: 'RepayCountdown',
  dispatchOnMount: true,
})
@pageHoc({ title: '还款结果' })
export default class RepayCountdown extends Component {
  config = {
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      countdownValue: Number(Url.getParam('counterSeconds'))
    };
    this.counterSeconds = Number(Url.getParam('counterSeconds'));
  }

  componentDidMount() {
    // 针对app跳转支付宝的情况
    const fromAlipayUrl = Number(Url.getParam('alipayUrl')) === 1;
    let resultFromUrl = Url.getParam('result');
    if (fromAlipayUrl) {
      // 因为支付宝跳转支付后会清缓存，无奈之下便写进缓存
      const payResult = Madp.getStorageSync('payResult', 'LOCAL');
      // console.log('payResult :>> ', payResult);
      resultFromUrl = payResult.result;
    }
    const resultData = this.getResultData(resultFromUrl);
    this.countTimer = setInterval(() => {
      if (this.counterSeconds < 0) {
        clearInterval(this.countTimer);
      } else {
        const { transSeqno, bankCardLimitSplit } = resultData;
        this.transRecordSingleQuery(transSeqno, `${bankCardLimitSplit}` === '1');
        // eslint-disable-next-line no-plusplus
        const second = --this.counterSeconds;
        this.setState({ countdownValue: second > 0 ? second : 0 });
      }
    }, 1000);
  }

  componentDidShow() {
  }

  getResultData(resultFromUrl) {
    if (!resultFromUrl) return {};
    if (resultFromUrl && typeof resultFromUrl === 'object') {
      return resultFromUrl;
    }
    let result = decodeURIComponent(resultFromUrl);
    if (process.env.TARO_ENV !== 'h5') {
      result = decodeURIComponent(result);
    }
    let resultData = {};
    try {
      resultData = JSON.parse(result || {});
    } catch (error) {
      resultData = JSON.parse(resultFromUrl || {});
    }
    return resultData;
  }

  // 查询交易结果
  async transRecordSingleQuery(transSeqno, isBankCardLimitSplit = false) {
    // console.log('object :>> ', transSeqno);
    const { transRecord = {} } = transSeqno
      ? await Dispatch.repayment.queryTransRecordSingle({ transSeqno, querySubTransFlag: 'Y', queryOrderFlag: 'Y' })
      : {};
    // const { transStatus = 'PROC', custOrderStatus, repaymentLogList } = transRecord || {};
    // console.log('custOrderStatus', custOrderStatus);
    // console.log('repaymentLogList', repaymentLogList);
    // const settleQuery = {
    //   custOrderStatus,
    //   repaymentLogList
    // };
    // const { transStatus = 'PROC', custOrderStatus, repaymentLogList = [] } = transRecord || {};
    const { transStatus = 'PROC', custOrderStatus, repaymentLogList } = transRecord || {};
    console.log(typeof custOrderStatus);
    console.log(typeof repaymentLogList);
    let settleQuery = {};
    console.log('custOrderStatus', custOrderStatus);
    console.log('repaymentLogList', repaymentLogList);
    if (custOrderStatus && repaymentLogList) {
      settleQuery = {
        custOrderStatus,
        repaymentLogList
      };
    }
    settleQuery.transSeqno = transSeqno;
    console.log('this.settleQuery', this.settleQuery);
    if (this.settleQuery) console.log('you-countdown', this.settleQuery);
    if (!this.settleQuery) console.log('wu-countdown', this.settleQuery);
    switch (transStatus) {
      case 'SUC':
        this.toDetailResult(!isBankCardLimitSplit ? 'success' : '', settleQuery);
        break;
      case 'FAIL':
        this.toDetailResult('fail', settleQuery);
        break;
      case 'PROC':
        this.toDetailResult();
        break;
      default:
        this.toDetailResult();
        break;
    }
  }

  toDetailResult(type, settleQuery) {
    console.log('settleQuery', settleQuery);
    const fromAlipayUrl = Number(Url.getParam('alipayUrl')) === 1;
    if (type) {
      clearInterval(this.countTimer);
    } else if (this.counterSeconds > 0) {
      // 处理中，但是截止时间还没到
      return;
    }
    clearInterval(this.countTimer);
    // 由于链接参数未知，故这样拼接，替换
    let currentUrl = '';
    if (process.env.TARO_ENV === 'h5') {
      currentUrl = window.location.href;
    } else {
      currentUrl = `/${getCurrentPageUrlWithArgs()}`;
    }
    // 当前页面链接种参数透传到结果页
    let nextUrl = currentUrl.replace('repay-countdown', 'repay-success');
    let query = {};
    // eslint-disable-next-line no-param-reassign
    if (type === 'success') {
      query = {
        isReapySuccess: '1',
        settleQuery: encodeURIComponent(JSON.stringify(settleQuery)),
      };
      // setStore({ settleQuery });
      // Madp.setStorageSync('settleQuery', settleQuery);
      // localStorage.setItem('settleQuery', settleQuery);
    } else if (type === 'fail') {
      nextUrl = currentUrl.replace('repay-countdown', 'repay-fail');
      query = {
        transSeqno: settleQuery && settleQuery.transSeqno || '',
      };
    }
    if (fromAlipayUrl) {
      // 清除alipayUrl避免死循环。给一个标识位，让成功页判断
      nextUrl = nextUrl.replace('alipayUrl', 'alipayUrlAfterRediret');
    }
    Util.router.replace({
      path: nextUrl,
      query
    });
  }

  render() {
    const { countdownValue } = this.state;
    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="还款结果"
          leftArea={[
            {
              type: 'icon',
              value: 'back'
            }
          ]}
        />
      <MUView className="countdown">
        <MUView className="repay-countdown">
          <MUView className="count-down-ring">
            <MUView className="count-down-num">{`${countdownValue}`}</MUView>
            <MUView className="count-down-left">
              <MUView className="count-down-left-ring" style={`border-left-color: ${themeColor};border-top-color: ${themeColor}`} />
            </MUView>
            <MUView className="count-down-right">
              <MUView className="count-down-right-ring" style={`border-left-color: ${themeColor};border-top-color: ${themeColor}`} />
            </MUView>
          </MUView>
          <MUView className="repay-countdown-waitText">{waitText}</MUView>
          <MUView className="repay-countdown-waitSubText">{waitSubText}</MUView>
        </MUView>
      </MUView>
      </MUView>
    );
  }
}
