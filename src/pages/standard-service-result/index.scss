.consult-repay-result {
  height: 100%;
  min-height: 100vh;
  background: #ffffff;
  padding-top: 40px;

  .consult-status-img-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    .consult-status-img {
      width: 120px;
      height: 120px;
    }
  }

  .consult-status-text {
    margin-top: 40px;
    font-weight: 500;
    font-size: 40px;
    color: #333333;
    line-height: 60px;
    text-align: center;
  }

  .consult-status-tips {
    margin: 26px auto 0;
    color: #808080;
    font-size: 28px;
    font-weight: 400;
    line-height: 42px;
    text-align: center;
    max-width: 600px;
  }

  .consult-result-btn {
    margin: 50px 40px 0 40px;

    // 新增返回首页专属样式
    &-link {
      color: #3477FF !important;
      font-size: 36px !important;
      font-weight: 600 !important;
      font-family: "PingFang SC";
      line-height: 54px !important;
      background: transparent !important;
      border: none !important;
      padding: 0 !important;
      margin-top: 48px;
      height: auto;
    }
  }

  .consult-repay-result-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    bottom: 0;
  }
}