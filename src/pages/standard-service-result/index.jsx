import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUImage, MUButton, MURichText, MUSlogan
} from '@mu/zui';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import { urlDomain } from '@utils/url_config';
import { isMiniProgramChannel, StandardService } from '@utils/constants';
import {
  Url,
} from '@mu/madp-utils';
import pageHoc from '@utils/pageHoc';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import ChannelConfig from '@config/index';
import './index.scss';
import ConsultRepayReject from './img/consult-repay-reject.png';

const SUCCESS_IMAGE = 'https://file.mucfc.com/zlh/3/0/202305/20230518202321a82936.png';
const PENDING_IMAGE = 'https://file.mucfc.com/zlh/3/0/202305/2023051820221669f9b7.png';
const themeColor = Util.getThemeColor(ChannelConfig.theme);

// 业务类型映射
const ServiceTypeMap = {
  [StandardService.ConsultRepayApply]: '协商还',
  [StandardService.BillAdvancedStage]: '再分期'
};

const serviceType = Url.getParam('serviceType');
const serviceName = ServiceTypeMap[serviceType] || ServiceTypeMap.ConsultRepayApply;

@track({ event: EventTypes.PO }, {
  pageId: 'StandardServiceResult',
  dispatchOnMount: true,
})
@pageHoc({ title: serviceName })
export default class ConsultReapyResult extends Component {
  config = {
    navigationBarTitleText: serviceName,
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      statusConfig: {}
    };
    this.repaymentFlag = Url.getParam('repaymentFlag') || ''; // 还款业务跳进的标识
  }

  async componentWillMount() {
    const status = Url.getParam('status');
    let statusConfigData = {};

    if (this.getStatusConfigMap()[status]) {
      const config = this.getStatusConfigMap()[status];
      if (typeof config === 'function') {
        statusConfigData = config(Url.getParam('businessHandleDays'));
      } else {
        // 处理异步方法
        statusConfigData = { ...config };
        if (typeof config.statusTips === 'function') {
          statusConfigData.statusTips = await config.statusTips();
        }
      }

      // 替换状态文案中的业务名称
      if (statusConfigData.statusText) {
        statusConfigData.statusText = statusConfigData.statusText.replace('协商还', serviceName);
      }
    }

    this.setState({ statusConfig: statusConfigData });
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'StandardServiceResult', beaconContent: { cus: { status } } });
  }

  getStatusConfigMap = () => ({
    1: {
      statusImage: SUCCESS_IMAGE,
      statusText: '办理成功',
      statusTips: async () => await this.initSuccessStatusInfo(),
      buttons: [
        { text: '查看协商还还款计划', type: 'primary', onClick: () => this.navigateToIndex(true) },
        { text: '返回首页', type: 'default', onClick: () => this.navigateToIndex(false) }
      ]
    },
    2: (businessHandleDays) => ({
      statusImage: PENDING_IMAGE,
      statusText: `已提交${serviceName}申请`,
      statusTips: `<span>预计${businessHandleDays}个工作日完成审核，请留意短信通知；<br/>审批期间仍会计收罚息，请在审批通过后及时登录查看最新的月还款计划</span>`,
      buttons: [{ text: '返回首页', type: 'primary', onClick: () => this.navigateToIndex(false) }]
    }),
    3: {
      statusImage: PENDING_IMAGE,
      statusText: `已提交${serviceName}申请`,
      statusTips: '<span>律所工作人员将与您联系，沟通确认还款方案；<br/>如您同意，签署调解协议书后可确认办理。</span>',
      buttons: [{ text: '返回首页', type: 'primary', onClick: () => this.navigateToIndex(false) }]
    },
    4: {
      statusImage: ConsultRepayReject,
      statusText: '本次申请未通过',
      statusTips: '很抱歉，您暂不符合办理条件，请及时还款，保持良好个人信用',
      buttons: [{ text: '返回首页', type: 'primary', onClick: () => this.navigateToIndex(false) }]
    },
    5: {
      statusImage: PENDING_IMAGE,
      statusText: '已提交还款，处理中',
      statusTips: '还款处理中，还款成功后将自动完成服务办理，请耐心等待，稍后查看办理结果',
      buttons: [{ text: '返回首页', type: 'primary', onClick: () => this.navigateToIndex(false) }]
    },
    6: {
      statusImage: ConsultRepayReject,
      statusText: '办理失败',
      statusTips: '很抱歉，您暂不符合办理条件，请及时还款，保持良好个人信用',
      buttons: [{ text: '返回首页', type: 'primary', onClick: () => this.navigateToIndex(false) }]
    },
  });

  initSuccessStatusInfo = async () => {
    try {
      const applyNo = Url.getParam('applyNo');

      const result = await Dispatch.repayment.queryRepayPlanAndStatus({
        queryType: '2',
        applyNo,
      });

      // 使用逻辑运算符访问对象属性
      const negotiateRepayTaskBill = (result && result.data && result.data.negotiateRepayTaskBill) || {};
      const repayPlanList = negotiateRepayTaskBill.repayPlanList || [];
      const firstRepayPlan = repayPlanList.length > 0 ? repayPlanList[0] : {};
      const repayDate = firstRepayPlan.repayDate || '';
      const taskThresholdAmt = firstRepayPlan.taskThresholdAmt || '0.00';

      // 日期格式校验
      const monthStr = repayDate.substring(4, 6) || '--';
      const dayStr = repayDate.substring(6, 8) || '--';
      const month = parseInt(monthStr) || '--';
      const day = parseInt(dayStr) || '--';

      return `${month}月${day}日 需还款<span id=yellow style="color: ${themeColor};">${taskThresholdAmt}</span>元，请按时还款`;
    } catch (error) {
      console.error('还款计划查询失败:', error);
      return '还款计划加载失败，请稍后重试'; // 异常兜底
    }
  }

  isWebViewInMicroApp() {
    if (process.env.TARO_ENV === 'h5') {
      const ua = navigator.userAgent.toLowerCase();
      return (
        /miniprogram/.test(ua)
        || /toutiaomicroapp/.test(ua)
        || /baiduboxapp/.test(ua)
      );
    }
    return false;
  }

// 统一跳转首页方法
navigateToIndex = (shouldShowPlan = false) => {
  const channel = Madp.getChannel();
  const basePath = '/pages/index/index';
  const confirmBtnLeaveFlag = this.handleConfirmBtnLeaveFlag();

  const urlParams = `repaymentFlag=${this.repaymentFlag}&showRepayPlan=${shouldShowPlan ? '1' : ''}`;
  // 处理3CMBAPP特殊渠道
  if (channel === '3CMBAPP') {
    // 离开时清楚缓存
    Madp.setStorageSync(confirmBtnLeaveFlag, '', 'SESSION');
    const fullUrl = `${urlDomain}/${channel}/repayment/#${basePath}?${urlParams}`;
    window.location.replace(fullUrl);
    return;
  }

  // 非小程序渠道设置sessionStorage
  if (!isMiniProgramChannel) {
    Madp.setStorageSync('showRepayPlan', shouldShowPlan ? 'Y' : '', 'SESSION');
  }

  const targetUrl = `${urlDomain}/${channel}/repayment/#${basePath}?${urlParams}`;

  if (isMiniProgramChannel) {
    Madp.setStorageSync(confirmBtnLeaveFlag, '', 'SESSION');
    Madp.miniProgram.reLaunch({
      url: `/repayment${basePath}?${urlParams}`
    }).catch(() => {
      Madp.redirectTo({ url: targetUrl });
    });
  } else {
    Madp.setStorageSync(confirmBtnLeaveFlag, '', 'SESSION');
    Madp.redirectTo({
      url: `${basePath}?${urlParams}`
    }).catch(() => {
      Madp.closeWebView().then().catch(() => {
        Madp.navigateBack();
      });
    });
  }
}

// 办理页的挽留弹窗标识，离开流程返回首页前需要清除，不清除下次进入办理页会直接返回首页
handleConfirmBtnLeaveFlag = () => {
  let confirmBtnLeaveFlag = '';
  if (serviceType === StandardService.ConsultRepayApply) { // 协商还
    confirmBtnLeaveFlag = 'consultApplyConfirmBtnLeave';
  } else if (serviceType === StandardService.BillAdvancedStage) { // 再分期
    confirmBtnLeaveFlag = 'ReinstallmentConfirmBtnLeave';
  }
  return confirmBtnLeaveFlag;
}

render() {
  const status = Url.getParam('status');
  const { statusConfig } = this.state;
  const { statusImage, statusText, statusTips, buttons } = statusConfig || {};

  return (
    <MUView className="pages-bg">
      <MUView className="consult-repay-result">
        <MUView className="consult-status-img-container">
          <MUImage className="consult-status-img" src={statusImage} />
        </MUView>

        <MUView className="consult-status-text">{statusText}</MUView>

        <MURichText className="consult-status-tips" nodes={statusTips} />

        <MUView className="consult-result-btns">
          {buttons && buttons.map((button, index) => (
            <MUButton
              key={index}
              beaconId="StandardServiceResultBtn"
              beaconContent={{
                cus: {
                  status
                }
              }}
              className={`consult-result-btn ${button.type === 'default' ? 'consult-result-btn-link' : ''}`}
              type={button.type === 'default' ? undefined : button.type}
              circle
              onClick={button.onClick}
            >
              {button.text}
            </MUButton>
          ))}
        </MUView>

        <MUView className="consult-repay-result-container">
          <MUSlogan className="result-slogan" onlyLogo />
        </MUView>
      </MUView>
    </MUView>
  );
}
}