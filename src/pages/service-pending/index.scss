.service-pending {
  min-height: 100vh;
  background: #fff;
  padding-top: 30px;
  box-sizing: border-box;
  position: relative;
  font-size: 0;
  &__plus {
    .slogan {
      width: 206px;
      height: 44px;
      padding-bottom: 35px;
      left: 50%;
      transform: translateX(-50%);
  
      &-plus {
        width: 249px;
      }
    }
  }
  &__img {
    width: 120px;
    height: 120px;
    margin: 0 auto;
    .taro-img {
      width: 100%;
      height: 100%;
    }
  }
  &__title {
    margin-top: 40px;
    font-size: 40px;
    line-height: 60px;
    color: #333;
    font-weight: 500;
    text-align: center;
  }
  &__desc {
    margin: 26px 30px 0;
    font-size: 28px;
    line-height: 42px;
    color: #888;
    font-weight: 400;
    text-align: left;
  }
  &__buttons {
    margin: 50px 0 50px 30px;
    display: flex;
    .at-button {
      margin-right: 30px;
      flex: 1;
    }
  }
  &__slogan {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    bottom: 0;
  }
}