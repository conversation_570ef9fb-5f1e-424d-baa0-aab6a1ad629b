/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUImage, MUButton, MURichText, MUSlogan
} from '@mu/zui';
import {
  track, EventTypes,
} from '@mu/madp-track';
import { sloganUrl } from '@utils/constants';
import { Url } from '@mu/madp-utils';
import classNames from 'classnames';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import { getStore, setStore } from '@api/store';
import Dispatch from '@api/actions';
import './index.scss';

const PENDING_IMAGE = 'https://file.mucfc.com/zlh/3/0/202305/2023051820221669f9b7.png';

@track({ event: EventTypes.PO }, {
  pageId: 'ServicePending',
  dispatchOnMount: true,
})
@pageHoc({ title: Url.getParam('serviceType') === 'extend' ? '延后还' : '再分期' })
export default class ServicePending extends Component {
  config = {
    navigationBarTitleText: Url.getParam('serviceType') === 'extend' ? '延后还' : '再分期',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);

    this.state = {
      showPage: false,
    };
    this.serviceType = Url.getParam('serviceType') || '';
    this.pendingDescText = ''; // 进行中描述文案
    this.repaymentFlag = Url.getParam('repaymentFlag') || '';
  }

  componentDidMount() {
    this.pendingDescText = this.getPendingDescText();
    if (this.pendingDescText) {
      this.setState({
        showPage: true,
      });
    }
  }

  getPendingDescText = () => {
    let pendingDescText = '';
    switch (this.serviceType) {
      case 'advanced-stage':
        pendingDescText = '您有一笔正在进行中的<span style="color: #FF6A00;">延后还</span>服务，该服务与再分期服务<span style="color: #FF6A00;">不可同时办理</span>，请确认您当前想要办理哪个服务？';
        break;
      case 'extend':
        pendingDescText = '您有一笔正在进行中的<span style="color: #FF6A00;">再分期</span>服务，该服务与延后还服务<span style="color: #FF6A00;">不可同时办理</span>，请确认您当前想要办理哪个服务？';
        break;
      default:
        break;
    }
    return pendingDescText;
  }

  continueOriginService = () => {
    let originServiceUrl = '';
    switch (this.serviceType) {
      case 'advanced-stage':
        originServiceUrl = `/pages/bill-extend/list?repaymentFlag=${this.repaymentFlag}`;
        break;
      case 'extend':
        originServiceUrl = `/pages/bill-advanced-stage/list?repaymentFlag=${this.repaymentFlag}`;
        break;
      default:
        break;
    }
    if (originServiceUrl) {
      setStore({
        transitApplyCase: {}
      });
      Util.router.replace(originServiceUrl);
    }
  }

  quitAndDo = async () => {
    try {
      const { applyNo } = getStore('transitApplyCase') || {};
      const { ret, errMsg, data } = await Dispatch.repayment.postLoanAddData({
        applyNo,
        addInfoScene: '01',
        updateScene: '01',
      }, {});
      const { applyStatus } = data || {};
      if (ret === '0' && applyStatus === '6') {
        this.jumpNewService();
      } else {
        Madp.showToast({
          title: errMsg || '系统繁忙，请稍候再试',
          icon: 'none'
        });
      }
    } catch (err) {
      Madp.showToast({
        title: '系统繁忙，请稍候再试',
        icon: 'none'
      });
    }
  }

  jumpNewService = () => {
    let newServiceUrl = '';
    switch (this.serviceType) {
      case 'advanced-stage':
        newServiceUrl = `/pages/bill-advanced-stage/list?repaymentFlag=${this.repaymentFlag}`;
        break;
      case 'extend':
        newServiceUrl = `/pages/bill-extend/list?repaymentFlag=${this.repaymentFlag}`;
        break;
      default:
        break;
    }
    if (newServiceUrl) {
      setStore({
        transitApplyCase: {}
      });
      Util.router.replace(newServiceUrl);
    }
  }

  render() {
    const { showPage } = this.state;
    if (!showPage) {
      return <MUView />;
    }

    const isVplus = getStore('isVplus') || false; // 是否为v+会员
    const { pendingDescText, serviceType } = this;

    return (
      <MUView className="pages-bg service-pending">
        <MUView className="service-pending__plus">
          <MUImage className={classNames('slogan', { 'slogan-plus': isVplus })} src={isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
        </MUView>
        <MUView className="service-pending__img">
          <MUImage className="status-img" src={PENDING_IMAGE} />
        </MUView>
        <MUView className="service-pending__title">已有进行中的还款服务</MUView>
        <MURichText className="service-pending__desc" nodes={pendingDescText} />
        <MUView className="service-pending__buttons">
          <MUButton
            type="secondary"
            onClick={this.continueOriginService}
          >
            {`办理${serviceType === 'extend' ? '再分期' : '延后还'}`}
          </MUButton>
          <MUButton
            type="primary"
            onClick={this.quitAndDo}
          >
            {`办理${serviceType === 'extend' ? '延后还' : '再分期'}`}
          </MUButton>
        </MUView>
        <MUView className="service-pending__slogan">
          <MUSlogan onlyLogo />
        </MUView>
      </MUView>
    );
  }
}
