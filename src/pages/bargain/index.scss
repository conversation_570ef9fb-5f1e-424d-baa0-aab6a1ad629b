.bargaining-page {
  background-image: url(./assets/bg.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
  padding-top: 193px;
  overflow: hidden;
  .repay-amount-section {
    margin: 0 30px;
    background-color: white;
    border: 2px solid #FFFFFF;
    box-shadow: 0 2px 30px 0 rgba(188,212,255,0.39);
    border-radius: 16px;
    padding: 30px;
    .r-a-subtitle {
      font-size: 26px;
      color: #A6A6A6;
      line-height: 1;
      height: 26px;
      display: flex;
      align-items: center;
    }
    .r-a-amount {
      margin-top: 18px;
      height: 100px;
      line-height: 100px;
      &-all {
        font-size: 80px;
        font-weight: 500;
      }
      &-fee {
        font-size: 30px;
        color: #474747;
      }
    }
    .r-a-part {
      margin-top: 20px;
      font-size: 24px;
      height: 32px;
      line-height: 32px;
      color: #3477FF;
      display: flex;
      align-items: baseline;
    }
  }
  .reduce-section {
    background: #FFFFFF;
    box-shadow: 0 2px 30px 0 rgba(188,212,255,0.39);
    border-radius: 16px;
    padding: 60px 0;
    margin: 20px 30px;
    .r-s-subtitle {
      font-size: 40px;
      color: rgba(0,0,0,0.80);
      text-align: center;
      height: 56px;
      font-weight: 500;
    }
    .r-s-input {
      text-align: center;
      margin: 80px 30px 0;
      font-size: 48px;
      border-bottom: 1PX solid #AAAAAA;
    }
    .r-s-detail {
      text-align: center;
      font-size: 26px;
      color: #A6A6A6;
      line-height: 32px;
      height: 32px;
      margin-top: 10px;
    }
    .r-s-button {
      width: 480px;
      height: 100px;
      background: #3477FF;
      border-radius: 50px;
      color: #FFFFFF;
      line-height: 100px;
      margin: 50px auto 0;
      font-size: 36px;
      text-align: center;
    }
    .r-s-tips {
      margin: 30px auto;
      font-size: 28px;
      color: #9A9A9A;
      text-align: center;
      line-height: 32px;
      height: 32px;
    }
  }

  .amount-detail-modal {
    .adm-subtitle {
      font-size: 32px;
      color: #353535;
      font-weight: 500;
      text-align: center;
      margin-bottom: 30px;
    }
    .adm-item {
      display: flex;
      justify-content: space-between;
      font-size: 32px;
      color: #353535;
      margin: 0 40px;
    }
    .adm-tip {
      font-size: 32px;
      color: #353535;
      margin: 12px 40px 35px;
    }
    .adm-button {
      height: 98px;
      line-height: 98px;
      text-align: center;
      font-size: 36px;
      color: #3477FF;
      border-top: 1PX solid #E5E5E5;
    }
  }
  // 还价结果区域
  .result-section {
    background-color: #fff;
    border-radius: 16px;
    margin: 24px 30px;
    overflow: hidden;
    .re-s-title {
      width: 626px;
      height: 87px;
      background: url(./assets/resultTop.png) no-repeat;
      background-size: 100% 100%;
      margin: 0 auto;
    }
    .re-s-subtitle {
      margin-top: 30px;
      border-left: 10px solid #3477FF;
      height: 48px;
      line-height: 48px;
      padding-left: 20px;
      font-size: 36px;
      color: #090909;
      display: flex;
      align-items: center;
      &-icon {
        width: 53px;
        height: 53px;
        margin-left: 20px;
      }
    }
    .re-s-time {
      height: 32px;
      line-height: 32px;
      font-size: 22px;
      color: #777777;
      background: #F4F4F4;
      border-radius: 4px;
      margin: 17px 30px 0;
      padding: 0 10px;
      width: fit-content;
    }
    .re-s-oneline {
      margin-top: 30px;
      font-size: 28px;
      color: #333333;
      margin-left: 32px;
      white-space: nowrap;
      &-grey {
        color: #808080;
        font-size: 26px;
      }
      &-price {
        font-size: 48px;
      }
    }
    .re-s-detail {
      background: #F4F7FF;
      border-radius: 20px;
      margin: 20px 30px;
      padding: 20px 30px;
      &-top {
        height: 48px;
        line-height: 48px;
        background-color: #fff;
        border-radius: 24px;
        font-size: 24px;
        color: #333;
        display: flex;
        align-items: center;
        justify-content: center;
        &-icon {
          width: 42px;
          height: 39px;
          margin-right: 9px;
        }
      }
      &-pre {
        font-size: 24px;
        color: #808080;
        line-height: 38px;
        height: 38px;
        text-align: center;
        margin-top: 25px;
      }
      &-line {
        font-size: 28px;
        height: 68px;
        margin: 30px;
        text-align: center;
        white-space: nowrap;
        &-price {
          font-size: 48px;
        }
        &-reduceprice {
          font-size: 48px;
          color: #3477FF;
        }
        &-icon {
          width: 21px;
          height: 30px;
          margin-left: 9px;
        }
      }
    }
    .re-s-exe-but {
      margin: 30px;
      height: 100px;
      background: #3477FF;
      border-radius: 50px;
      text-align: center;
      line-height: 100px;
      color: #fff;
      font-size: 36px;
      &-small {
        font-size: 24px;
      }
    }
    .re-s-count-tip {
      font-size: 26px;
      color: #808080;
      line-height: 26px;
      text-align: center;
      height: 26px;
      margin-top: -10px;
      margin-bottom: 30px;
    }
  }
  .continue-section {
    height: 70px;
    line-height: 70px;
    margin: 30px 0;
    text-align: center;
    color: #3477FF;
    font-size: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    &-icon {
      margin-left: 10px;
      width: 16px;
      height: 15px;
      line-height: 15px;
      display: block;
    }
  }

  .record-section {
    margin: 30px;
    background: #FFFFFF;
    border-radius: 16px;
    overflow: hidden;
    padding: 30px 0;
    .rc-s-title {
      text-align: center;
      font-size: 32px;
      color: #090909;
      height: 45px;
      line-height: 45px;
    }
    .rc-s-subtitle {
      margin-top: 40px;
      border-left: 10px solid #3477FF;
      height: 48px;
      line-height: 48px;
      padding-left: 28px;
      font-size: 28px;
      color: #090909;
      display: flex;
      align-items: center;
      &-icon {
        width: 40px;
        height: 40px;
        margin-left: 20px;
      }
    }
    .rc-s-time {
      height: 32px;
      line-height: 32px;
      font-size: 22px;
      color: #777777;
      background: #F4F4F4;
      border-radius: 4px;
      margin: 17px 30px 0;
      padding: 0 10px;
      width: fit-content;
    }
    .rc-s-line {
      height: 26px;
      display: flex;
      align-items: center;
      margin: 28px 30px 10px;
      &-k {
        font-size: 26px;
        color: #808080;
      }
      &-v {
        font-size: 28px;
        color: #333;
      }
      &-icon {
        width: 21px;
        height: 30px;
        margin-left: 10px;
      }
    }
  }

}
