/* eslint-disable no-confusing-arrow */
import { Component } from '@tarojs/taro';
import {
  MUInput,
  MUText,
  MUView,
  MUIcon,
  MUModal,
  MUImage
} from '@mu/zui';
import Madp from '@mu/madp';
import Dispatch from '@api/actions';
import repaymentApi from '@api/new/repaymentApi';
import { injectState } from '@mu/leda';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import { repayStatusType, miniProgramChannel } from '@utils/constants';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import xiaoluIcon from './assets/xiaoluIcon.png';
import successIcon from './assets/successIcon.png';
import upIcon from './assets/upIcon.png';
import failIcon from './assets/failIcon.png';
import downIcon from './assets/down.png';

import './index.scss';
import { getStore } from '@api/store';
import { createBargain, createPreRepay, distributionCoupon, orderRecord, queryTodayBargain, queryTodayPreRepay } from './bargain_api';
import { Url } from '@mu/madp-utils';
import { filterWaiveCoupons } from '@utils/repay-util';

const bargain_bills_key = 'bargain_bills';
const bargain_bills_key_own = 'bargain_bills_own';
const bargainCouponActivityId = '2023020802';
const indexMap = [, '一', '二', '三'];

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: 'ae39e5f5-804c-4215-a6a6-2ab93ed5900b'
    }
  }
}, {
  pageId: 'BargainPage',
  dispatchOnMount: true,
  customAlert: true,
})
@pageHoc({ title: '息费减免申请' })
@injectState({
  pageId: 'ae39e5f5-804c-4215-a6a6-2ab93ed5900b',
  stateKeys: ['imageSinglePadding']
})
export default class BargainPage extends Component {
  constructor(props) {
    super(props);
    this.reset();
  }

  reset() {
    this.state = {
      isShowTipModal: false, // 显示金额详情提示
      isHideDetailEnter: false, // 隐藏金额详情入口
      isDuetag: false, // 是否逾期打标
      immutTotalAmt: '0.00', // 待还总金额
      totalAmt: '0.00', // 待还金额
      totalInteFine: '0.00', // 待还利息
      totalPrincipal: '0.00', // 本金
      totalInte: '0.00', // 利息
      totalFine: '0.00', // 罚息
      totalPeriodFee: '0.00', // 期费用
      totalOnetimeFee: '0.00', // 提前手续费
      isShowPartRepay: true, // 显示先部分还款入口

      isShowRepayAmount: false, // 显示自定义还款金额，只在逾期打标合并账单时出现
      inputRepayAmount: '', // 输入的还款金额
      leastRepayAmount: '0.01', // 最少还款金额
      // repaymentAmount: '', // 确认后的还款金额

      isShowReduceAmount: true, // 显示期望减免区域
      inputReduceFee: '', // 输入的期望减免息费金额

      isShowResult: false, // 显示结果
      currentResult: null, // 当次讨价还价的结果

      recordList: [], // 讨价还价记录
    };
  }

  async componentDidShow() {
    Madp.showToast({
      icon: 'loading'
    });
    this.reset();
    const [status, { data, ret, errMsg }, { repayBillList }, { data: todayPreRepay }]
      = await Promise.all([this.checkCoupon(), queryTodayBargain(), repaymentApi.getNearBills(), queryTodayPreRepay()]);
    const todayBargains = (data && data.data) || [];
    if (status === 0) { // 有其他渠道派的减免券，直接返回
      Madp.hideToast();
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'hasOtherCoupon' });
      Madp.showModal({
        content: '您已有减免优惠资格',
        showCancel: false,
        confirmText: '立享减免',
        success: (res) => {
          dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'hasOtherCouponClick' });
          this.toRepayPage();
        }
      });
      return;
    }
    const isDuetag = getStore('repayStatus') === repayStatusType.dueTagCust;

    if (ret === '1') { // 有其他息费减免活动，直接返回
      Madp.hideToast();
      this.showDisableModal('hasOtherFeeReduce', errMsg);
      return;
    } if (todayBargains && todayBargains.length) { // 有今日记录，直接
      const currentResult = todayBargains.shift();
      this.setState({
        currentResult,
        isShowPartRepay: false,
        totalAmt: currentResult.overdueTotalAmt,
        totalInteFine: currentResult.totalInterestAmt,
        isHideDetailEnter: true,
        isShowReduceAmount: false,
        isShowResult: true,
        todayPreRepay,
        recordList: todayBargains
      });
      let bargainBills = Madp.getStorageSync(bargain_bills_key_own) || [];
      this.orderList = bargainBills.map(bill => (isDuetag ? bill.orderNo : `${bill.orderNo}_${bill.installCnt}`)).join(',');
    } else { // 没有查询到今日记录，从头开始流程
      let bargainBills = Madp.getStorageSync(bargain_bills_key, 'SESSION');
      if (bargainBills) {
        Madp.removeStorageSync(bargain_bills_key, 'SESSION');
      } else {
        bargainBills = isDuetag ? repayBillList : repayBillList.filter(b => b.usedToCalculate === 'Y');
      }
      Madp.setStorageSync(bargain_bills_key_own, bargainBills);
      this.orderList = bargainBills.map(bill => (isDuetag ? bill.orderNo : `${bill.orderNo}_${bill.installCnt}`)).join(',');

      let totalAmt = 0;
      let totalInte = 0;
      let totalPrincipal = 0;
      let totalFine = 0;
      let totalPeriodFee = 0;
      let totalOnetimeFee = 0;
      let leastRepayAmount = 0.01;
      bargainBills.forEach(b => {
        totalAmt = Util.floatAdd(totalAmt, b.surplusPayTotalAmt);
        totalInte = Util.floatAdd(totalInte, b.surplusPayInteAmt);
        totalFine = Util.floatAdd(totalFine, b.surplusPayFineAmt);
        totalPrincipal = Util.floatAdd(totalPrincipal, b.surplusPayPrincipalAmt);
        totalPeriodFee = Util.floatAdd(totalPeriodFee, b.surplusPayPeriodFeeAmt);
        totalOnetimeFee = Util.floatAdd(totalOnetimeFee, b.surplusPayOnetimeFeeAmt);
      });
      let totalInteFine = Util.floatAdd(Util.floatAdd(Util.floatAdd(totalInte, totalFine), totalPeriodFee), totalOnetimeFee);
      this.setState({
        isDuetag,
        immutTotalAmt: totalAmt.toFixed(2),
        totalAmt: totalAmt.toFixed(2),
        totalInte: totalInte.toFixed(2),
        totalFine: totalFine.toFixed(2),
        totalPeriodFee: totalPeriodFee.toFixed(2),
        totalOnetimeFee: totalOnetimeFee.toFixed(2),
        totalInteFine: totalInteFine.toFixed(2),
        totalPrincipal: totalPrincipal.toFixed(2),
        leastRepayAmount: leastRepayAmount.toFixed(2),
      });
      if (totalInteFine === 0) {
        this.showDisableModal('noInte', '您无可减免的息费金额');
      }
    }

    Madp.hideToast();
  }

  showDisableModal(beaconId, content) {
    const fromResult = Url.getParam('fromResult');
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId });
    Madp.showModal({
      content,
      showCancel: false,
      confirmText: '我知道了',
      success: (res) => {
        if (miniProgramChannel.indexOf(Madp.getChannel()) > -1) {
          if (fromResult === '1') {
            Madp.miniProgram.redirectTo({ url: '/repayment/pages/index/index' });
          } else {
            Madp.miniProgram.navigateBack();
          }
        } else if (window && (window.history.length === 1)) {
          Madp.closeWebView();
        } else {
          Madp.navigateBack();
        }
      }
    });
  }

  /**
   * 0 有其他渠道的减免券，弹窗提示跳到还款首页
   * 1 有讨价还价的减免券，查询当天的讨价还价状态
   * 2 没有减免券，从头走流程
   */
  async checkCoupon() {
    const { awardDetailList = [] } = await Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }) || {};
    let { availableCouponDetailList } = await filterWaiveCoupons(awardDetailList) || {};
    availableCouponDetailList = (availableCouponDetailList || []).filter(c => c.awardType === '216');
    if (availableCouponDetailList.length) {
      if (availableCouponDetailList.find(c => c.taskId === bargainCouponActivityId)) { // 有讨价还价派发的减免券
        return 1;
      } else { // 有其他渠道派发的减免券
        return 0;
      }
    }
    return 2;
  }

  repayAmountSubmit = () => {
    const { inputRepayAmount, immutTotalAmt, leastRepayAmount } = this.state;
    if (!+inputRepayAmount) {
      Madp.showToast({
        icon: 'none',
        title: '请输入有效的金额'
      });
      return false;
    }
    if (inputRepayAmount.split('.')[1] && inputRepayAmount.split('.')[1].length > 2) {
      Madp.showToast({
        icon: 'none',
        title: '最多输入2位小数'
      });
      return false;
    }
    if (+inputRepayAmount > +immutTotalAmt || +inputRepayAmount < +leastRepayAmount) {
      Madp.showToast({
        icon: 'none',
        title: `请输入${leastRepayAmount}~${immutTotalAmt}元`,
      });
      return;
    }
    this.setPartAmount(+inputRepayAmount);
  }

  setPartAmount = async (amt) => {
    const res = await Dispatch.repayment.repayTransTrial({
      transTotalAmt: amt.toFixed(2),
      repayMode: 'AMT'
    });
    let totalAmt = Number(res.shouldRepayAmt);
    let totalPrincipal = 0;
    let totalInte = 0;
    let totalFine = 0;
    let totalPeriodFee = 0;
    let totalOnetimeFee = 0;
    // debugger;
    ((res && res.repayTrialDetailList) || []).forEach(d => {
      totalPrincipal = Util.floatAdd(totalPrincipal, d.totalPayPrincipalAmt || 0);
      totalInte = Util.floatAdd(totalInte, d.totalPayInteAmt || 0);
      totalFine = Util.floatAdd(totalFine, d.totalPayFineAmt || 0);
      totalPeriodFee = Util.floatAdd(totalPeriodFee, d.totalPayPeriodFee || 0);
      totalOnetimeFee = Util.floatAdd(totalOnetimeFee, d.totalPayOnetimeFee || 0);
    });
    let totalInteFine = Util.floatAdd(Util.floatAdd(Util.floatAdd(totalInte, totalFine), totalPeriodFee), totalOnetimeFee);
    if (totalInteFine === 0) {
      Madp.showToast({
        icon: 'none',
        title: '您输入的金额没有达到息费，请重新输入'
      });
      this.setState({ inputRepayAmount: '' });
    } else {
      this.setState({
        isShowRepayAmount: false,
        isShowReduceAmount: true,
        totalAmt: totalAmt.toFixed(2),
        totalPrincipal: totalPrincipal.toFixed(2),
        totalInteFine: totalInteFine.toFixed(2),
        totalInte: totalInte.toFixed(2),
        totalFine: totalFine.toFixed(2),
        totalPeriodFee: totalPeriodFee.toFixed(2),
        totalOnetimeFee: totalOnetimeFee.toFixed(2),
      });
    }
  }

  checkInputReduceFee = () => {
    const { inputReduceFee, totalInteFine, currentResult } = this.state;
    if (!+inputReduceFee) {
      Madp.showToast({
        icon: 'none',
        title: '请输入有效的金额'
      });
      return false;
    }
    if (inputReduceFee.split('.')[1] && inputReduceFee.split('.')[1].length > 2) {
      Madp.showToast({
        icon: 'none',
        title: '最多输入2位小数'
      });
      return false;
    }
    if (+inputReduceFee > +totalInteFine) {
      Madp.showToast({
        icon: 'none',
        title: `您最多可申请减免${totalInteFine}元，请重新输入`
      });
      return false;
    }
    if (currentResult && +inputReduceFee <= +currentResult.actualWaiveAmt) {
      Madp.showToast({
        icon: 'none',
        title: '上一轮小鹿返回的减免金额已满足您的减免诉求，可直接使用哦'
      });
      return false;
    }
    return true;
  }

  // 执行讨价还价
  exec = async () => {
    if (!this.checkInputReduceFee()) return;
    Madp.showToast({
      icon: 'loading',
      title: '审核中',
      duration: 10000,
    });
    const { totalAmt, totalPrincipal, totalInteFine, inputReduceFee, recordList, currentResult } = this.state;
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `createBargain${recordList.length + (currentResult ? 2 : 1)}` });
    const { ret, errMsg, data } = await createBargain({
      overdueTotalAmt: totalAmt, // 总金额
      overduePrincipalAmt: totalPrincipal, // 本金
      totalInterestAmt: totalInteFine, // 总息费
      applyWaiveAmt: (+inputReduceFee).toFixed(2), // 申请减免金额
    });
    if (ret === '1') {
      Madp.showToast({
        icon: 'none',
        title: errMsg,
        duration: 1500,
      });
      return;
    }
    this.setState({
      isShowResult: true,
      isShowReduceAmount: false,
      isShowPartRepay: false,
      currentResult: data.data,
      recordList: currentResult ? [currentResult, ...recordList] : recordList
    });

    Madp.hideToast();
  }

  continue = () => {
    this.setState({
      isShowReduceAmount: true,
      inputReduceFee: ''
    });
  }

  // 领券
  takeCoupon = async () => {
    Madp.showToast({
      icon: 'loading',
      duration: 10000,
    });
    this.setState({
      isShowReduceAmount: false,
    });
    const { currentResult, totalAmt, isDuetag, todayPreRepay } = this.state;
    if (currentResult.bargainStatus === 'Y') {
      this.toRepayPage();
    } else if (currentResult.repayModeCode === 'PRE' && todayPreRepay && todayPreRepay.preRepayStatus === 'Y') {
      Madp.showToast({
        icon: 'none',
        duration: 2000,
        title: '正在为您申请减免优惠，请稍后再试'
      });
      return;
    } else {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: currentResult.applyWaiveAmt === currentResult.actualWaiveAmt ? `successAccept${currentResult.bargainRound}` : `failAccept${currentResult.bargainRound}`
      });

      if (currentResult.repayModeCode === 'NORMAL') { // 普通领取，领完券跳转还款首页
        const { ret, errMsg } = await distributionCoupon({
          bargainId: `${currentResult.id}`
        });
        if (ret === '1') {
          Madp.showToast({
            icon: 'none',
            title: errMsg,
            duration: 1500,
          });
          return;
        } else {
          await orderRecord({
            reservationAmt: totalAmt,
            reservationType: isDuetag ? '03' : '02',
            reservationOrders: this.orderList.split(','),
          });
          this.toRepayPage();
        }
      } else if (currentResult.repayModeCode === 'PRE') { // 预还款后发券，创建预还款后跳转到预还款也
        const { basicCustDto } = getStore('sessionInfo') || {};
        let { custId } = basicCustDto || {};
        if (!custId) {
          const { basicCustDto: newBasicCustDto } = await Dispatch.repayment.getNewUserInfo();
          const { custId: newCustId } = newBasicCustDto || {};
          custId = newCustId || '';
        }
        const { ret, errMsg } = await createPreRepay({
          totalAmt: currentResult.overdueTotalAmt,
          bargainId: currentResult.id,
          custId: custId
        });
        if (ret === '1') {
          Madp.showToast({
            icon: 'none',
            title: errMsg,
            duration: 1500,
          });
          return;
        }
        Madp.navigateTo({
          url: `/pages/pre-repay/before-pre-repay?repayScene=3&orderList=${this.orderList}&reservationType=${isDuetag ? '03' : '02'}&reservationAmt=${totalAmt}`,
        });
      }
    }
  }

  toRepayPage = () => {
    // alert('跳转还款首页');
    // return;
    if (Url.getParam('fromIndex') === '1') {
      Madp.navigateBack();
    } else {
      Madp.redirectTo({
        url: '/pages/index/index?needBack=1'
      });
    }
  }

  render() {
    const {
      isShowTipModal, totalAmt, totalInteFine, totalPrincipal, totalInte, totalFine, totalPeriodFee, totalOnetimeFee,
      isShowPartRepay, isHideDetailEnter, immutTotalAmt, imageSinglePadding,
      isDuetag, isShowRepayAmount, inputRepayAmount, leastRepayAmount, isShowReduceAmount, inputReduceFee,
      isShowResult, currentResult, recordList, todayPreRepay
    } = this.state;
    if (totalAmt === '0.00') {
      return <MUView />;
    }
    return (
      <MUView
        className="bargaining-page"
        style={
          imageSinglePadding && imageSinglePadding.propsData && imageSinglePadding.propsData.imgUrl
            ? { backgroundImage: `url(${imageSinglePadding.propsData.imgUrl})` }
            : {}
        }
      >
        <MUView className="repay-amount-section">
          <MUView className="r-a-subtitle">
            <MUText>还款金额（元）</MUText>
            {
              !isHideDetailEnter && (
                <MUIcon
                  value="info"
                  size="16"
                  beaconId="showAmountDetail"
                  onClick={() => {
                    this.setState({ isShowTipModal: true });
                  }}
                />
              )
            }
          </MUView>
          <MUView className="r-a-amount">
            <MUText className="r-a-amount-all">
              {totalAmt}
            </MUText>
            <MUText className="r-a-amount-fee">
              （含息费{totalInteFine}）
            </MUText>
          </MUView>
          {
            isShowPartRepay && (
              <MUView
                className="r-a-part"
                beaconId="partRepay"
                onClick={() => {
                  if (isDuetag) {
                    this.setState({
                      isShowRepayAmount: true,
                      isShowReduceAmount: false,
                      inputRepayAmount: '',
                      inputReduceFee: '',
                    });
                  } else {
                    Madp.navigateTo({
                      url: '/pages/bill-list-near/index?fromBargaining=1',
                    });
                  }
                }}
              >
                <MUText>先还部分金额</MUText>
                <MUIcon value="arrow-right" size="12" />
              </MUView>
            )
          }
        </MUView>
        {
          isShowRepayAmount && (
            <MUView className="reduce-section">
              <MUView className="r-s-subtitle">
                你现在可以还多少？
              </MUView>
              <MUInput
                className="r-s-input"
                placeholder={`请输入${leastRepayAmount}~${immutTotalAmt}元`}
                type="money"
                value={inputRepayAmount}
                beaconId="inputRepayAmount"
                onChange={(v) => this.setState({ inputRepayAmount: v })}
              />
              <MUView className="r-s-button" beaconId="partAmountSubmit" onClick={this.repayAmountSubmit}>
                确认
              </MUView>
            </MUView>
          )
        }
        {
          isShowResult && currentResult && (
            <MUView className="result-section">
              <MUView className="re-s-title" />
              <MUView className="re-s-subtitle">
                <MUText>第{indexMap[currentResult.bargainRound]}轮</MUText>
                <MUImage src={currentResult.applyWaiveAmt === currentResult.actualWaiveAmt ? successIcon : failIcon} className="re-s-subtitle-icon" />
              </MUView>
              <MUView className="re-s-time">
                {currentResult.createTime}
              </MUView>
              <MUView className="re-s-oneline">
                <MUText className="re-s-oneline-grey">我的申请：</MUText>
                <MUText>还款</MUText>
                <MUText>{Util.floatMinus(Number(currentResult.overdueTotalAmt), Number(currentResult.applyWaiveAmt).toFixed(2))}</MUText>
                <MUText>元，减免</MUText>
                <MUText className="re-s-oneline-price">{currentResult.applyWaiveAmt}</MUText>
                <MUText>元</MUText>
              </MUView>
              <MUView className="re-s-detail">
                <MUView className="re-s-detail-top">
                  <MUImage src={xiaoluIcon} className="re-s-detail-top-icon" />
                  <MUText>{currentResult.applyWaiveAmt === currentResult.actualWaiveAmt ? '恭喜您，还价成功' : '小鹿努力帮您申请到…'}</MUText>
                </MUView>
                {
                  currentResult.repayModeCode === 'PRE' && (
                    <MUView className="re-s-detail-pre">
                      {
                        (todayPreRepay && todayPreRepay.preRepayStatus === 'Y') ? `已预付${currentResult.prePayAmt}元` : `预还款${currentResult.prePayAmt}元后，可享受`
                      }
                    </MUView>
                  )
                }
                <MUView className="re-s-detail-line">
                  <MUText>还款</MUText>
                  <MUText>{Util.floatMinus(Number(currentResult.overdueTotalAmt), Number(currentResult.actualWaiveAmt).toFixed(2))}</MUText>
                  <MUText>元，减免</MUText>
                  <MUText className="re-s-detail-line-reduceprice">{currentResult.actualWaiveAmt}</MUText>
                  <MUText>元</MUText>
                  {
                    currentResult.bargainRound > 1 && Number(currentResult.actualWaiveAmt) > Number(recordList[0].actualWaiveAmt) && (
                      <MUImage className="re-s-detail-line-icon" src={upIcon} />
                    )
                  }
                </MUView>
              </MUView>
              <MUView className="re-s-exe-but" beaconId="takeCoupon" onClick={this.takeCoupon}>
                <MUText>立享减免</MUText>
                <MUText className="re-s-exe-but-small">（今日有效）</MUText>
              </MUView>
              {
                currentResult.bargainRound === 3 && (
                  <MUView className="re-s-count-tip">
                    今日还价机会已用完，可立享减免
                  </MUView>
                )
              }
            </MUView>
          )
        }
        {
          isShowResult && !isShowReduceAmount && currentResult.bargainRound < 3
          && currentResult.applyWaiveAmt !== currentResult.actualWaiveAmt
          && currentResult.bargainStatus === 'N'
          && (
            <MUView className="continue-section" beaconId="continue" onClick={this.continue}>
              <MUText>不满意优惠金额，继续尝试</MUText>
              <MUImage src={downIcon} className="continue-section-icon" />
            </MUView>
          )
        }
        {
          isShowReduceAmount && (
            <MUView className="reduce-section">
              <MUView className="r-s-subtitle">
                {
                  currentResult ? '不满意优惠金额，继续尝试' : '你想申请减免多少息费呢？'
                }
              </MUView>
              <MUInput
                className="r-s-input"
                placeholder={currentResult ? `最高可输入${totalInteFine}` : '请输入期望减免的息费金额'}
                value={inputReduceFee}
                beaconId="inputReduceFee"
                onChange={(v) => this.setState({ inputReduceFee: v })}
                type="money"
              />
              {
                Number(inputReduceFee) && Number(inputReduceFee) > 0 && Number(inputReduceFee) <= Number(totalInteFine) ? (
                  <MUView className="r-s-detail">
                    若还价成功，总计还款{Util.floatMinus(Number(totalAmt), Number(inputReduceFee))}元（含息费{Util.floatMinus(Number(totalInteFine), Number(inputReduceFee))}）
                  </MUView>
                ) : null
              }
              <MUView className="r-s-button" beaconId="startBargain" onClick={this.exec}>
                {
                  currentResult ? '继续还价' : '开始还价'
                }
              </MUView>
              <MUView className="r-s-tips">
                {
                  currentResult ? `今日剩余${3 - currentResult.bargainRound}次机会` : '今日有3次机会'
                }
              </MUView>
            </MUView>
          )
        }

        {
          recordList && !!recordList.length && (
            <MUView className="record-section">
              <MUView className="rc-s-title">还价记录</MUView>
              {
                recordList.map(record => (
                  <MUView>
                    <MUView className="rc-s-subtitle">
                      <MUText>第{indexMap[record.bargainRound]}轮</MUText>
                      <MUImage src={record.applyWaiveAmt === record.actualWaiveAmt ? successIcon : failIcon} className="rc-s-subtitle-icon" />
                    </MUView>
                    <MUView className="rc-s-time">
                      {record.createTime}
                    </MUView>
                    <MUView className="rc-s-line">
                      <MUText className="rc-s-line-k">我的申请：</MUText>
                      <MUText className="rc-s-line-v">还款{Util.floatMinus(Number(record.overdueTotalAmt), Number(record.applyWaiveAmt)).toFixed(2)}元，减免{record.applyWaiveAmt}元</MUText>
                    </MUView>
                    <MUView className="rc-s-line">
                      <MUText className="rc-s-line-k">还价结果：</MUText>
                      <MUText className="rc-s-line-v">
                        还款{Util.floatMinus(Number(record.overdueTotalAmt), Number(record.actualWaiveAmt)).toFixed(2)}元，减免{record.actualWaiveAmt}元
                        {
                          record.bargainRound === 2 && Number(record.actualWaiveAmt) > Number(recordList[recordList.length - 1].actualWaiveAmt) && (
                            <MUImage className="rc-s-line-icon" src={upIcon} />
                          )
                        }
                      </MUText>
                    </MUView>
                  </MUView>
                ))
              }
            </MUView>
          )
        }

        <MUModal isOpened={isShowTipModal} className="amount-detail-modal" beaconId="amountDetailModal">
          <MUView className="adm-subtitle">还款金额</MUView>
          <MUView className="adm-item">
            <MUView>本金</MUView>
            <MUView>{totalPrincipal}元</MUView>
          </MUView>
          {
            totalInte !== '0.00' && (
              <MUView className="adm-item">
                <MUView>利息</MUView>
                <MUView>{totalInte}元</MUView>
              </MUView>
            )
          }
          {
            totalFine !== '0.00' && (
              <MUView className="adm-item">
                <MUView>罚息</MUView>
                <MUView>{totalFine}元</MUView>
              </MUView>
            )
          }
          {
            totalPeriodFee !== '0.00' && (
              <MUView className="adm-item">
                <MUView>期费用</MUView>
                <MUView>{totalPeriodFee}元</MUView>
              </MUView>
            )
          }
          {
            totalOnetimeFee !== '0.00' && (
              <MUView className="adm-item">
                <MUView>提前还款手续费</MUView>
                <MUView>{totalOnetimeFee}元</MUView>
              </MUView>
            )
          }
          <MUView className="adm-tip">若需调整还款金额，可点击“先还部分金额”修改还款金额</MUView>
          <MUView
            className="adm-button"
            beaconId="detailConfirm"
            onClick={() => {
              this.setState({ isShowTipModal: false });
            }}
          >知道了</MUView>
        </MUModal>
      </MUView>
    );
  }
}
