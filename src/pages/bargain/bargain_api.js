import fetch from '@utils/mgp-fetch.js';

/**
 * 执行讨价还价
 */
export function createBargain(data) {
  return fetch('mucfc.cust.createBargain', { data, autoLoading: false }, true);
}

/**
 * 查询今天的讨价还价记录
 */
export function queryTodayBargain(data) {
  return fetch('mucfc.cust.queryTodayBargain', { data, autoToast: false }, true);
}

/**
 * 创建预支付，在去预支付时调用
 */
export function createPreRepay(data) {
  return fetch('mucfc.cust.createPreRepay', { data }, true);
}

/**
 * 获取今日预付款记录接口，如果已同意预付款的讨价还价
 */
export function queryTodayPreRepay(data) {
  return fetch('mucfc.cust.queryTodayPreRepay', { data });
}

/**
 * 执行派券，在直接领券时调用
 */
export function distributionCoupon(data) {
  return fetch('mucfc.cust.distributionCoupon', { data }, true);
}

/**
 * 优先入账借据记录
 */
export function orderRecord(data) {
  return fetch('mucfc.repayment.bargain.reserveRepay', { data });
}
