import { Component } from '@tarojs/taro';
import {
  <PERSON>U<PERSON>ie<PERSON>,
  MUButton
} from '@mu/zui';
import Madp from '@mu/madp';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import {
  isMuapp,
  isIOS,
  Url,
  compareMuappVersion,
  getCurrentPageUrlWithArgs,
} from '@mu/madp-utils';
import { injectState } from '@mu/leda';
import { RepaymentPlugin } from '@mu/business-plugin';
import Util from '@utils/maxin-util';
import CustomConfig from '@config/index';
import Dispatch from '@api/actions';
import { getStore } from '@api/store';
import pageHoc from '@utils/pageHoc';
import RepaymentGlobalStore from '@repayment/store/rootStore';
import { getUrlAllParams } from '@utils/commonUtils';


import './index.scss';
import './before-pre-repay.scss';
import Contract from '@components/contract/index';
import { inject } from '@tarojs/mobx';


// import '@components/statistic/index.scss';
// import '@components/bottom-drawer/index.scss';


if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/statistic/index.scss')
  require('@components/bottom-drawer/index.scss')
}

const themeColor = Util.getThemeColor(CustomConfig.theme);

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: '7ca5367f-3a00-43ff-becd-323e36aa474d'
    }
  }
}, {
  pageId: 'BeforePreRepay',
  dispatchOnMount: true,
})
@pageHoc({ title: '预还款' })
@inject(() => ({ repaymentGlobalStore: new RepaymentGlobalStore() }))
@injectState({
  pageId: '7ca5367f-3a00-43ff-becd-323e36aa474d',
  stateKeys: ['bannerWithoutBorder']
})
export default class BeforePreRepay extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showContractMUModal: false, // 补签弹窗
      capitalConstractNum: null, // 需要补签的资方商户号
      showFPAYContract: false, // 选择的银行卡需要签署支付扣款协议
      preRepayAmt: 0, // 预还款金额
      noPreRepayInfo: false, // 用户今日没有预还款信息或用户今日已支付预还款
      loading: true,
    };
    this.isCheckedContract = false; // 是否勾选了协议
  }

  async componentWillMount() {
    await this.getPreRepayAmt();
    this.savePreRepayInfo();
  }

  async componentDidMount() {
    // 截屏或录屏时toast提示
    if (isMuapp() && isIOS() && compareMuappVersion('6.3.0')) {
      /* eslint-disable-next-line */
      var callback = function (operation) {
        Madp.showToast({ title: `发现${operation}操作，请注意个人信息安全`, icon: 'none' });
      };
      window.muapp.NotificationPlugin.registerNativeNoticeEvent('screenshot', () => callback('截屏'));
      window.muapp.NotificationPlugin.registerNativeNoticeEvent('screenrecord', () => callback('录屏'));
    }
  }

  // 获取预还款金额
  async getPreRepayAmt() {
    const { data: res } = await Dispatch.repayment.queryTodayPreRepay();
    const { data } = res || {};
    const { preRepayAmt, preRepayStatus } = data || {};
    if (!preRepayStatus || preRepayStatus === 'Y') {
      this.setState({
        noPreRepayInfo: true,
        loading: false,
      });
    } else if (preRepayAmt) {
      this.setState({
        preRepayAmt,
        loading: false,
      });
    }
  }

  // 保存url传参过来的preRepayInfo:{orderList}(讨价还价)、还款场景repayScene、优先还款预约金额reservationAmt(讨价还价)、预约类型reservationType
  savePreRepayInfo() {
    const orderListString = Url.getParam('orderList') || '';
    let repayScene = Url.getParam('repayScene') || '';
    const reservationAmt = Url.getParam('reservationAmt') || '';
    const reservationType = Url.getParam('reservationType') || '';
    const preRepayInfo = {
      orderList: orderListString ? orderListString.split(',') : [],
      reservationAmt: reservationAmt || null,
      reservationType: reservationType || null,
    };
    // im场景进来的url需要解码
    const currentUrl = decodeURIComponent(getCurrentPageUrlWithArgs());
    if (getCurrentPageUrlWithArgs() !== currentUrl) {
      const repaySceneFromIM = getUrlAllParams(currentUrl);
      repayScene = JSON.parse(repaySceneFromIM.info).repayScene || '';
    }
    Madp.setStorageSync('preRepayInfo', preRepayInfo, 'SESSION');
    Madp.setStorageSync('repayScene', repayScene, 'SESSION');
    // 存储当前的历史记录位置,用于结果页计算需要回多少到im或讨价还价
    Madp.setStorageSync('startLocation', window.history.length, 'SESSION');
    Madp.setStorageSync('startState', Number(window.history.state.key), 'SESSION');
  }

  signInContract() {
    this.setState({
      showContractMUModal: false,
    });
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'AgreeContract' });
  }

  preSubmit() {
    const { isCheckedContract } = this;
    if (!isCheckedContract) {
      Madp.showToast({
        title: '请先阅读并勾选协议',
        icon: 'none'
      });
      return;
    } else {
      Madp.navigateTo({
        url: '/pages/pre-repay/new-index'
      });
    }
  }


  render() {
    const {
      showFPAYContract, showContractMUModal, capitalConstractNum, preRepayAmt, noPreRepayInfo, loading
    } = this.state;
    if (loading) {
      return <MUView />;
    }

    if (noPreRepayInfo) {
      return (
        <MUView className="pages-bg pre-repay" >
          <MUView className="pre-repay-logo-text">
            招联金融
          </MUView>
          <MUView className="pre-repay-no-info">
            链接已过期，请与您的管家联系重新获取
          </MUView>
          <MUButton
            className="pre-repay-close-button"
            onClick={() => {
              if (window.history.length < 2) {
                Madp.closeWebView();
              } else {
                Madp.navigateBack({ delta: 1 });
              }
            }}
            beaconId="PreRepayClose"
          >
            关闭
          </MUButton>
        </MUView>
      );
    } else {
      return (
        <MUView className="pages-bg pre-repay">
          <RepaymentPlugin
            businessName="repayment"
            pageType="beforePreRepay"
            loc="top"
            beaconId="BusinessOlugin"
          />
          <MUView className="pre-repay-wrapper">
            <MUView className="pre-repay-header">
              <MUView className="pre-repay-title">
                <MUView className="pre-repay-title-text">
                  预还款金额(元)
                </MUView>
              </MUView>
              <MUView className="pre-repay-amount">{preRepayAmt}</MUView>
              <MUView className="pre-repay-tip">
                <MUView>1、减免金额将以优惠券的形式派发，请关注优惠券到账提醒及有效期；</MUView>
                <MUView className="pre-repay-tip-red">2、预还款金额不予退还，可在还款时使用；</MUView>
                <MUView>3、优惠券有效期内未还款，预还款金额将会自动还款入账。</MUView>
              </MUView>
            </MUView>
            <Contract
              onChecked={(v) => { this.isCheckedContract = v; }}
              capitalConstractNum={capitalConstractNum}
              showFPAYContract={showFPAYContract}
              showContractMUModal={showContractMUModal}
              onModalClose={() => this.setState({ showContractMUModal: false })}
              onModalConfirm={() => this.signInContract()}
              showPREREPAYContract
            />
            <MUView
              className="repay-bottom-button"
            >
              <MUButton
                className="theme-background-color"
                customStyle={`background: ${themeColor}`}
                onClick={() => this.preSubmit()}
                beaconId="PreRepayImmediately"
              >
                立即支付
              </MUButton>
            </MUView>

          </MUView>
        </MUView>
      );
    }
  }
}
