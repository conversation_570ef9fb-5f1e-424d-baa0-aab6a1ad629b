import { Component } from '@tarojs/taro';
import {
  MUView,
  MUImage,
  MUModal,
} from '@mu/zui';
import Madp from '@mu/madp';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import classNames from 'classnames';
import {
  isMuapp,
  isWechat,
  isAlipay,
  isIOS,
  Url,
  debounce,
  compareMuappVersion,
  throttle,
  getCurrentPageUrlWithArgs
} from '@mu/madp-utils';
import { MUSafeSmsCodeHalfWrap } from '@mu/safe-sms-shell';
import { MUTradePasswordEncryptedWrap } from '@mu/trade-password-encrypted-shell';
import Util from '@utils/maxin-util';
import CustomConfig from '@config/index';
import RepayWay from '@components/repay-way/index';
import Statistic from '@components/statistic/index';
import BottomDrawer from '@components/bottom-drawer';
import { getStore, setStore } from '@api/store';
import { OnePassOrSmsCodeWrap, BiomtOrPasswOrLivenWrap } from '@mu/trade-verify-shell';
import Dispatch from '@api/actions';
import { wechatAppId, wechatH5AppId } from '@utils/url_config';
import pageHoc from '@utils/pageHoc';
import { errCodeToMsgMap } from '@utils/constants';
import { transformErrCode } from '@utils/payMethod';
import { getBusinessFullName, getLoginInfo } from '@mu/business-basic';

import './index.scss';
import './new-index.scss';
import Contract from '@components/contract/index';
import { inject } from '@tarojs/mobx';

// import '@components/repay-modal/index.scss';
// import '@components/repay-way/index.scss';
// import '@components/statistic/index.scss';
// import '@components/bottom-drawer/index.scss';


if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/repay-modal/index.scss');
  require('@components/repay-way/index.scss');
  require('@components/statistic/index.scss');
  require('@components/bottom-drawer/index.scss');
}

const themeColor = Util.getThemeColor(CustomConfig.theme);

@track({ event: EventTypes.PO }, {
  pageId: 'NewPreRepay',
  dispatchOnMount: true,
})
@pageHoc({ title: '预还款' })
export default class NewPreRepay extends Component {
  constructor(props) {
    super(props);
    this.billList = {};
    /** 交易密码校验组件返回的 token */
    this.pwdResultToken = '';
    /** 还款提交之后接口出参 */
    this.payResult = {};
    /** 前端自己生成了流水号。和后端自己的流水号不一样。不知道什么用了 */
    this.transRefNo = '';
    this.state = {
      openPwdTip: false,
      openSmsCodeTip: false,
      bankCards: [],
      showTradePwdDialog: false,
      showSmsCodeDialog: false,
      smsCodeToken: '', // 动码token
      clearAndDisableInput: false, // 控制是否情况密码输入框
      alipayOnly: false, // 仅支持支付宝支付
      showFPAYContract: false, // 选择的银行卡需要签署支付扣款协议
      // checkBoxChecked: false, // 支付扣款协议选择状态,
      showContractMUModal: false, // 补签弹窗
      SignContract: false, // 提交还款之后激活的补签流程，补签后直接提交立即还款
      bankCardErrorStatusList: {}, // 自定义的银行卡错误状态列表，避免获取银行卡刷新掉自定义状态
      repayConfig: {},
      maskRealName: '', // 客户姓名掩码
      capitalConstractNum: null, // 需要补签的资方商户号
      selectBankTransferFlag: false, // 是否选中银行卡转账项目
      bankCardLimitSplitFlag: 'N', // 是否需要大额拆分。选中了优惠券不能拆分
      preRepayAmt: 0, // 预还款金额
      showErrorTipModal: false, // 是否打开支付失败提示模态框
      fullName: '招联消费金融股份有限公司',
    };
    this.repayWayItem = {
      setAlipayOnlyCard: () => { },
      initDefaultCard: () => { },
      checkFirstPay: () => { },
      getPreRepayWayUserClick: () => {
        return false;
      },
    };
    this.statistic = {
      doSubmitRepay: () => { },
    };
    /** 是否是还没获取银行卡 */
    this.firstgetBankCardList = true;
    this.selectedTrialBillList = []; // 试算后更新该账单数据拿来做 “受支付机构影响，本次还款将拆成x笔扣款，请稍后留意还款结果通知”
    this.isShowTriaMinxBill = false; // 是否是修改金额试算 显示拆成多笔弹窗条件
    /** 签约所有资方的银行卡id */
    this.allSignedCardIds = [];
    /** 支持所有资方但需要补签的银行卡id */
    this.needSignCardIds = [];
    // fix: 支付宝还款在支付宝h5,部分机型可以返回还款支付宝页面
    /** 支付宝生活号，支付宝付款完毕，跳转到结果页面。好像没用了 */
    this.zfbPayGoBackNeedToResult = false;
    /** 支付还款方式数据。fix: 无银行卡用户仅能支付宝支付，无法选中问题 */
    this.alipayWayInfo = {
      isAlipay: true,
      bankCardId: 'alipay',
      bankOrg: {
        bankName: '支付宝还款',
        bankImage: 'https://file.mucfc.com/ebn/3/18/2023010/202310121438282ab9c7.png',
        bankCode: '2088'
      }
    };
    /** fix: 存在重复调用还款接口问题 */
    this.debounceApi = debounce((bo) => {
      this.immediatePayApi(bo);
    }, 2000, {
      leading: true, // 指定调用在节流开始前
      trailing: false
    });
    /** fix: app端外部跳转导致清除了保存数据的问题 */
    this.needExternalFillObj = false;
    /** 需要大额拆分的银行卡 */
    this.overLimitCards = [];
    /** fix: 是否选中银行卡转账项目(同步可靠) */
    this.selectBankTransferFlag = false;
    // 试算后的账单明细
    this.afterTrialBillDetail = [];
    this.bankList = []; // 接口回来的银行卡数据
    this.selectedBillAmountList = []; // 查询银行卡入参
    this.lastOpenWithin7Days = false;
    this.canleave = false;
    this.isCheckedContract = false; // 是否勾选了协议
    this.BottomDrawer = {
      show: () => { },
      hide: () => { },
    };
    this.errorTipModalcontent = '系统繁忙，请稍后再试';
    this.hideBottomDrawer = true;
    this.actualPayAmt = 0;
    this.isRepayWayShow = false;
    // 防止支付方式栏闪动，保存上一次用户选择的卡数据
    this.freezeCardObj = {
      preRepaySelectButtonText: '',
      bankImageUrl: '',
    };
    this.supportZFBCardChannelFlag = false; // 支持引导从支付宝选卡渠道技参
    this.supportOneBindCardChannelFlag = false; // 支持引导一键绑卡渠道技参配置
  }

  async componentDidMount() {
    this.getNewFullName();
    await Promise.all([
      this.initPageConfig(),
      this.getRepayChannelParams(),
    ]);
    this.transRefNo = Util.getTransRefNo();

    this.setMaskRealName();
    // 外部跳转填充函数。与didShow有部分重复

    // 截屏或录屏时toast提示
    if (isMuapp() && isIOS() && compareMuappVersion('6.3.0')) {
      /* eslint-disable-next-line */
      var callback = function (operation) {
        Madp.showToast({ title: `发现${operation}操作，请注意个人信息安全`, icon: 'none' });
      };
      window.muapp.NotificationPlugin.registerNativeNoticeEvent('screenshot', () => callback('截屏'));
      window.muapp.NotificationPlugin.registerNativeNoticeEvent('screenrecord', () => callback('录屏'));
    }
  }

  getNewFullName = async () => {
    try {
      const res = await getBusinessFullName();
      const { data = '招联消费金融股份有限公司' } = res || {};
      this.setState({ fullName: data });
    } catch (error) {
    }
  }

  async componentDidShow() {
    await this.getPreRepayAmt();
    await this.queryBankCardInfo();
    if (this.zfbPayGoBackNeedToResult) {
      this.zfbPayGoBackNeedToResult = false;
      this.onPayResultJump('success');
    }
    if (process.env.TARO_ENV !== 'h5') {
      this.needExternalFillObj = false;
    }
    // 初始化的时候先隐藏BottomDrawer支付方式选择，防止页面闪屏
    this.hideBottomDrawer = true;
    // 没有银行卡的时候直接拉起支付方式让用户选择
    if (!this.bankList.length || this.bankList.length === 0) {
      this.hideBottomDrawer = false;
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'noDefaultBankCard' });
      this.setState({}, () => this.BottomDrawer.show(() => {
        this.selectPreRepayWayOpen();
      }));
    } else { // 有银行卡的情况下为用户自动选择，这里会有闪屏的情况
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'haveDefaultBankCard' });
      this.setState({}, () => {
        this.BottomDrawer.show(async () => {
          await this.initPage();
        });
      });
    }
  }

  async initPage(isModalCancel = false) {
    await this.repayWayItem.initDefaultCard(this.bankCards);
    this.BottomDrawer.hide();
    const selectedCard = getStore('selectedCard');
    const {
      needSignFundList = [], supplementSignFlag, istTransferGuide = false, satisfyLimitAmtStatus, forceSignFlag
    } = selectedCard;
    // 补签列表中去除10000，要补签招联的话，根据supplementSignFlag字段判断
    const otherNeedSignFundList = needSignFundList ? needSignFundList.filter((item) => item !== '10000') : [];
    let showFPAYContract = false;
    let capitalConstractNum = '';
    // 正常补签招联，宝付强制补签走正常补签流程
    if (supplementSignFlag === 'Y' || (needSignFundList && needSignFundList.indexOf('10000') > -1) || (forceSignFlag === 'Y')) {
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ShowContractChecker' });
      showFPAYContract = true;
      capitalConstractNum = '';
    } else if (otherNeedSignFundList.length) { // 补签资方
      // 将需要补签的资方商户，按照对应金额的降序排序
      const sortList = otherNeedSignFundList.sort((min, max) => this.selectedBillCapitals[max] - this.selectedBillCapitals[min]);
      showFPAYContract = true;
      capitalConstractNum = sortList.length ? sortList[0] : '';
    } else { // 不需要补签的情况下直接拉起密码组件
      if (!isModalCancel) {
        this.hideBottomDrawer = false;
        this.statistic.doSubmitRepay();
        return null;
      }
    }
    this.selectBankTransferFlag = istTransferGuide;
    this.setState({
      showFPAYContract,
      capitalConstractNum,
      selectBankTransferFlag: istTransferGuide,
      bankCardLimitSplitFlag: satisfyLimitAmtStatus === 'N'
        && supplementSignFlag === 'N' ? 'Y' : 'N'
    }, () => {
      this.hideBottomDrawer = false;
    });
  }

  async initPageConfig() {
    const repayConfig = await Dispatch.repayment.getCommonConfig('repayPayment.config');
    this.setState({
      repayConfig
    }, () => { this.repayWayItem.checkFirstPay(); });
  }

  /**
   * 查询还款渠道技参
   */
  async getRepayChannelParams() {
    const res = await Dispatch.repayment.getChannelParams({
      channelParamKeyList: ['guideAlipaySelectCard', 'oneBindCard'],
      paramTypeList: ['CHANNEL_PARAM']
    });

    // supportLoansAlipayFlag，支持支付宝选卡
    const { guideAlipaySelectCard, oneBindCard }
      = res && res.data && res.data.channelParamMap || {};
      // 支持引导从支付宝选卡渠道技参配置
    this.supportZFBCardChannelFlag = guideAlipaySelectCard === 'Y';
    // 支持引导一键绑卡渠道技参配置
    this.supportOneBindCardChannelFlag = oneBindCard === 'Y';
  }

  /**
   *  设置用户掩码名字
   */
  async setMaskRealName() {
    const result = await getLoginInfo();
    const { custName } = result || {};
    this.setState({ maskRealName: custName });
  }

  // 获取预还款金额
  async getPreRepayAmt() {
    const { data: res } = await Dispatch.repayment.queryTodayPreRepay();
    const { data } = res || {};
    const { preRepayAmt, preRepayStatus } = data || {};
    if (!preRepayStatus || preRepayStatus === 'Y') {
      Madp.redirectTo({
        url: 'before-pre-repay'
      });
    } else if (preRepayAmt) {
      this.setState({
        preRepayAmt,
      });
    }
    this.actualPayAmt = preRepayAmt || 0;
  }

  onAddBankCard() {
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'AddBankCard' });
    const currentUrl = process.env.TARO_ENV === 'h5' ? getCurrentPageUrlWithArgs() : `/${getCurrentPageUrlWithArgs()}`;
    const param = {
      redirectUrl: 'goBack',
      backDelta: '1',
      action: 'repayment',
      returnUrl: currentUrl, // 跳转方法会将参数encode，去掉错误的多余encode
    };
    Util.externalJump('ADD_BANK_CARD', param);
  }

  async queryBankCardInfo() {
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: 'QueryBankCard',
      beaconContent: { cus: { amount: this.actualPayAmt } }
    });
    const { bankCardInfoList } = await Dispatch.repayment.getBankCardsList({
      transAmt: String(this.actualPayAmt),
      fundTransAmtDetails: [],
    });
    // 接口银行卡数据
    this.bankList = bankCardInfoList || [];
    this.sortBankList();

    // initDefaultCard方法第二个参数为空会有个bankCardErrorStatusList[bankCardId]=undefined的报错
    this.setState({
      bankCards: this.bankCards,
    });
    this.firstgetBankCardList = false;
  }

  /**
   * 选择银行卡
   * @param { Object } card 所选银行卡
   */
  onCardSelected = throttle(async (card) => {
    const {
      needSignFundList = [], supplementSignFlag, istTransferGuide = false, satisfyLimitAmtStatus, forceSignFlag
    } = card;
    // 补签列表中去除10000，要补签招联的话，根据supplementSignFlag字段判断
    const otherNeedSignFundList = needSignFundList ? needSignFundList.filter((item) => item !== '10000') : [];
    let showFPAYContract = false;
    let capitalConstractNum = '';
    // 正常补签招联，宝付强制补签走正常补签流程
    if (supplementSignFlag === 'Y' || (needSignFundList && needSignFundList.indexOf('10000') > -1) || (forceSignFlag === 'Y')) {
      // if (supplementSignFlag === 'Y') {
      dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'ShowContractChecker' });
      showFPAYContract = true;
      capitalConstractNum = '';
    } else if (otherNeedSignFundList.length) { // 补签资方
      // 将需要补签的资方商户，按照对应金额的降序排序
      const sortList = otherNeedSignFundList.sort((min, max) => this.selectedBillCapitals[max] - this.selectedBillCapitals[min]);
      showFPAYContract = true;
      capitalConstractNum = sortList.length ? sortList[0] : '';
    }
    this.selectBankTransferFlag = istTransferGuide;
    this.setState({
      showFPAYContract,
      capitalConstractNum,
      selectBankTransferFlag: istTransferGuide,
      bankCardLimitSplitFlag: satisfyLimitAmtStatus === 'N'
        && supplementSignFlag === 'N' ? 'Y' : 'N'
    });
  }, 500)

  /**
   * 动码验证通过。或动码豁免
   * 立即还款之前的补签，下一步是验证交易密码；
   * 立即还款之后的补签，下一步是再次提交立即还款。
   */
  async onSmsVerifyOk(token) {
    const { SignContract, capitalConstractNum: oldCapitalConstractNum } = this.state;
    // 用户补签完成后更新下银行卡信息
    await this.queryBankCardInfo();
    const selectedCard = getStore('selectedCard');
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'AuthCodeSuccess', beaconContent: { cus: { scene: (selectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (oldCapitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
    const afterFPAYCardList = this.bankCards.filter(item => item.bankCardNo === selectedCard.bankCardNo);
    setStore({
      selectedCard: afterFPAYCardList[0]
    });
    const {
      needSignFundList = [], supplementSignFlag, forceSignFlag
    } = afterFPAYCardList[0] || {};
    // 补签列表中去除10000，要补签招联的话，根据supplementSignFlag字段判断
    const otherNeedSignFundList = needSignFundList ? needSignFundList.filter((item) => item !== '10000') : [];
    let showFPAYContract = false;
    let capitalConstractNum = '';
    // 正常补签招联，宝付强制补签走正常补签流程
    if (supplementSignFlag === 'Y' || (needSignFundList && needSignFundList.indexOf('10000') > -1) || (forceSignFlag === 'Y')) {
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ShowContractChecker' });
      showFPAYContract = true;
      capitalConstractNum = '';
    } else if (otherNeedSignFundList.length) { // 补签资方
      // 将需要补签的资方商户，按照对应金额的降序排序
      const sortList = otherNeedSignFundList.sort((min, max) => this.selectedBillCapitals[max] - this.selectedBillCapitals[min]);
      showFPAYContract = true;
      capitalConstractNum = sortList.length ? sortList[0] : '';
    }
    this.setState({
      showFPAYContract,
      capitalConstractNum,
      showSmsCodeDialog: false
    });
    if (token) {
      this.setState({ smsCodeToken: token });
    }
    if (SignContract || CustomConfig.payBySms) { // 渠道支持动码还款
      this.debounceApi(SignContract);
      this.setState({
        SignContract: false
      });
    } else if (this.useTranComponentSign) {
      dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'NeedPassword' });
      this.biomtOrPasswOrLivenRef.startProcess();
    } else {
      dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'NeedPassword' });
      this.setState({
        showTradePwdDialog: true,
        clearAndDisableInput: false,
      });
    }
  }

  async onPasswordOk(token) {
    dispatchTrackEvent({
      target: this, event: EventTypes.BC, beaconId: 'PasswordSuccess', beaconContent: { cus: { result: '1' } }
    });
    this.pwdResultToken = token;
    if (!this.useTranComponentSign) {
      this.setState({
        showTradePwdDialog: false,
        clearAndDisableInput: true, // 清除密码残留
      });
    }
    await this.debounceApi();
  }

  onPasswordClose() {
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'ClosedPassword' });
    this.setState({
      showTradePwdDialog: false,
    });
  }

  onNeedModifyPass() {
    const that = this;
    Madp.showModal({
      title: '升级6位数字交易密码',
      content: '为提供更便捷的支付体验，特邀您升级为6位数字的交易密码',
      confirmText: '立即升级',
      confirmColor: themeColor,
      showCancel: false,
      success(res) {
        if (res.confirm) {
          that.onForgotPass();
        } else if (res.cancel) {
        }
      }
    });
  }

  onForgotPass() {
    Util.externalJump('SET_PWD');
  }

  // 获取动码，默认获取还款动码，也支持传参，例如补签流程
  async initToken() {
    const selectedCard = getStore('selectedCard');
    const { capitalConstractNum } = this.state;
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'NeedSmsCode', beaconContent: { cus: { scene: (selectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
    // 新交易组件不需要自行获取token
    Madp.setStorageSync('isShowedFPAYContract', '1', 'SESSION');
    // 新交易组件不需要自行获取token
    if (this.useTranComponentSign) {
      this.onePassOrSmsCodeRef.startProcess();
      return;
    }
    this.setState({ // 20221129,旧动码也无需自行获取token
      showSmsCodeDialog: true
    });
  }

  /**
 * 对银行卡进行筛选排序
 * 第1部分：默认卡（可用而且不用拆分，所以可能不存在）
 * 第2部分：不需要补签而且不用拆分；
 * 第3部分：需要补签才能使用的；
 * 第4部分：不需要补签但要拆分（包括超限额的）
 * 第5部分：不能使用的（例如无法补签资方的、有优惠券场景又要拆分的）；
 * 2345部分都按照单笔金额降序排序 (产品：陈福明)
 */
  sortBankList() {
    const originalCards = getStore('bankCards');
    // 筛选支持的卡，将资方不支持的卡放入bankCardErrorStatusList
    const allSignedCard = []; // 签约所有资方的银行卡id。第1部分+第2部分
    const needSignCard = []; // 支持所有资方但需要补签的银行卡id。第3部分
    const limitCard = []; // 需要拆分的卡。第4部分
    const errorCard = []; // 临时设置一个值来承载不支持银行卡卡号。第5部分
    this.overLimitCards = [];
    originalCards.forEach((card) => {
      const {
        bankCardId,
        signedFundList = [], // 银行卡已签约资方
        needSignFundList = [], // 银行卡需补签资方
        satisfyLimitAmtStatus,
        supplementSignFlag,
        status, // 银行卡状态，0：启用，1：禁用-已销户
      } = card;
      // 销户卡不可用
      if (status === '1') {
        this.setSelectCardsStatus(bankCardId, '卡已销户', false, false, 'close');
        errorCard.push(card);
        return;
      }

      // 需要大额拆分的银行卡
      if (satisfyLimitAmtStatus === 'N' && supplementSignFlag === 'N') {
        this.overLimitCards.push(bankCardId);

        limitCard.push(card);
        return;
      }
      if (supplementSignFlag === 'N' && (!needSignFundList || needSignFundList.length === 0)) { // 不需要补签的卡
        allSignedCard.push(card);
        this.allSignedCardIds.push(bankCardId);
      } else {
        needSignCard.push(card);
        this.needSignCardIds.push(bankCardId);
      }
    });

    const firstCard = allSignedCard.shift(); // 第一部分
    // 按照单笔限额排序
    allSignedCard.sort((min, max) => +max.singleDeductionLimit - +min.singleDeductionLimit);
    needSignCard.sort((min, max) => +max.singleDeductionLimit - +min.singleDeductionLimit);
    limitCard.sort((min, max) => +max.singleDeductionLimit - +min.singleDeductionLimit);
    errorCard.sort((min, max) => (min.status === max.status ? +max.singleDeductionLimit - +min.singleDeductionLimit : +min.status - +max.status));
    if (!firstCard) {
      this.bankCards = [...allSignedCard, ...needSignCard, ...limitCard, ...errorCard];
    } else {
      this.bankCards = [firstCard, ...allSignedCard, ...needSignCard, ...limitCard, ...errorCard];
    }
  }

  /**
   * 准备输入交易密码
   * 首先判断是否存在交易密码，若无则提示，若有则出现交易密码输入框和键盘
   */
  async preSubmit() {
    const {
      alipayOnly,
      showFPAYContract,
      bankCardLimitSplitFlag,
    } = this.state;
    if (showFPAYContract && Madp.getStorageSync('isShowedFPAYContract', 'SESSION') === '1') { // bugfix：补签情况下，完成补签动码但是未输入密码，然后关掉密码，此时要重新调用银行卡接口更新是否需要补签的状态
      await this.queryBankCardInfo();
    }
    const { isCheckedContract } = this;
    if (alipayOnly) dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'SubmitAlipayOnly' });
    else dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `${showFPAYContract ? 'SubmitWithContract' : 'SubmitCommon'}` });
    const selectedCard = getStore('selectedCard');

    if (showFPAYContract && !isCheckedContract) {
      Madp.showToast({
        title: '请勾选支付扣款协议',
        icon: 'none'
      });
      return;
    }
    if (!Object.keys(selectedCard).length || selectedCard.bankCardId === 'transferGuide') {
      Madp.showToast({
        title: '请勾选支付方式',
        icon: 'none'
      });
      return;
    }

    if (bankCardLimitSplitFlag === 'Y') {
      Madp.showModal({
        title: '提示',
        content: '由于您的银行卡设置了单笔支付限额，本次还款可能会分成多笔扣款，请保证银行卡余额充足，以免扣款不足导致还款失败',
        confirmText: '继续还款',
        confirmColor: themeColor,
        cancelText: '换一张卡',
        success: async (res) => {
          if (res.confirm) {
            await this.RepayPretreatment(selectedCard, this.actualRepayAmt);
          } else if (res.cancel) {
            this.BottomDrawer.show(() => {
              this.selectPreRepayWayOpen();
              this.repayWayItem.changMoreStatus(true);
            });
          }
        }
      });
    } else {
      await this.RepayPretreatment(selectedCard, this.actualRepayAmt);
    }
  }

  async RepayPretreatment(selectedCard, needPayAmt) {
    this.jumpPay(selectedCard, needPayAmt); // 区分支付宝、微信、银行卡不同模式
  }

  // 跳转支付，支付宝、微信和银行卡还款
  async jumpPay(selectedCard, needPayAmt) {
    // 若为支付宝和微信还款则直接跳支付宝，无需输入招联交易密码
    if (Number(needPayAmt) > 0 && (selectedCard.isAlipay || selectedCard.isWxPay)) {
      dispatchTrackEvent({
        target: this, event: EventTypes.SO, beaconId: 'OpenPayModel', beaconContent: { cus: { pay_type: selectedCard.isAlipay ? 'Alipay' : 'Wxpay' } }
      });
      await this.debounceApi();
      return;
    }
    this.normalPay();
  }

  async normalPay() { // 如果有补签豁免标识就不用二次验证动码
    const { showFPAYContract } = this.state;
    if (showFPAYContract) {
      await this.initToken('SCENE_SUPPLEMENT');// 如果要补签，先走补签验证动码
    } else if (CustomConfig.payBySms) {
      await this.initToken();
    } else if (this.useTranComponentSign) {
      dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'NeedPassword' });
      this.biomtOrPasswOrLivenRef.startProcess();
    } else {
      dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'NeedPassword' });
      this.setState({
        clearAndDisableInput: false,
        showTradePwdDialog: true
      });
    }
  }

  /**
   * @description: 改变选择的银行卡状态
   * @param { String } errorMsg 错误提示
   * @param { Boolean } needInit 是否重新选卡，默认 true
   * @param { Boolean } support 是否正常显示，默认 true
   * @param { String } reason 不可用原因，默认 error
   */
  setSelectCardsStatus(bankCardId, errorMsg, needInit = true, support = true, reason = 'error') {
    const { bankCardErrorStatusList, bankCards } = this.state;
    const StatusList = bankCardErrorStatusList;
    if (StatusList[bankCardId]) {
      // 如果已存在
      if (StatusList[bankCardId].reasonList.indexOf(reason) <= -1) {
        // 新的错误原因
        StatusList[bankCardId].reasonList.push(reason);
      }
    } else {
      // 卡不存在
      StatusList[bankCardId] = {
        errMsg: errorMsg, // 异常银行卡的提示语
        support, // 不支持的银行卡特殊展示
        reasonList: [reason]
      };
    }
    this.setState({
      bankCardErrorStatusList: StatusList
    }, () => { if (needInit && this.repayWayItem && this.repayWayItem.initDefaultCard) this.repayWayItem.initDefaultCard(bankCards, bankCardErrorStatusList); });
  }

  parseToDOM(str) {
    this.zfbPayGoBackNeedToResult = true;
    const div = document.createElement('div');
    if (typeof str === 'string') {
      div.innerHTML = str;
    }
    document.body.appendChild(div);
    div.getElementsByTagName('form')[0].submit();
  }

  // 支付宝h5 支付【生活号渠道还款 与支付宝支付对接的模式调整成“先下单后支付”模式】
  zfbPayResult(r) {
    const self = this;
    // eslint-disable-next-line no-undef
    ap.tradePay({
      tradeNO: r.prepayId // 订单号,
    }, async (res) => {
      if (res.resultCode === '9000') {
        self.onPayResultJump('success');
        dispatchTrackEvent({
          target: self,
          event: EventTypes.CK,
          beaconId: 'zfbAliPaySuccess',
        });
      } else if (res.resultCode === '6001') {
        // 如果是走这里就调用后台取消订单 接口 李志恒
        this.closeWXOrZFBPay(r, 'alipay'); // 预支付模式->支付宝关单
        dispatchTrackEvent({
          target: self,
          event: EventTypes.CK,
          beaconId: 'zfbAliPayCancelOrder',
        });
      } else {
        self.onPayResultJump('fail');
        dispatchTrackEvent({
          target: self,
          event: EventTypes.CK,
          beaconId: 'zfbAliPayFail',
        });
      }
    });
  }

  /**
   * 支付宝还款
   */
  async alipaySDK(r) {
    if (r.alipayUrl) {
      // 支付宝中采用路由跳转的形式进行支付，结果页展示信息保留在缓存中
      // 另支付宝会清session，所以存到local
      const payResult = {
        result: JSON.stringify(this.payResult),
      };
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: 'alipayUrlPayResult',
        beaconContent: { cus: payResult }
      });
      Madp.setStorageSync('payResult', payResult, 'LOCAL');
    }
    if (process.env.TARO_ENV === 'alipay') {
      // 支付宝小程序 原生支付
      const self = this;
      const zfbOrderId = r.prepayId; // 订单号
      if (zfbOrderId) {
        // eslint-disable-next-line no-undef
        my.tradePay({
          tradeNO: zfbOrderId,
          success: async (res) => {
            if (res.resultCode === '9000') {
              self.onPayResultJump('success');
              dispatchTrackEvent({
                target: self,
                event: EventTypes.EV,
                beaconId: 'AliPayMiniSuccess',
                beaconContent: { cus: { transRefNo: self.transRefNo, infor: JSON.stringify(res) } }
              });
            } else if (res.resultCode === '6001') {
              this.closeWXOrZFBPay(r, 'alipay'); // 预支付模式->支付宝关单
              dispatchTrackEvent({
                target: self,
                event: EventTypes.CK,
                beaconId: 'zfbAliPayCancelOrderMini',
              });
            } else {
              self.onPayResultJump('fail');
              dispatchTrackEvent({
                target: self,
                event: EventTypes.EV,
                beaconId: 'AliPayMiniFail',
                beaconContent: { cus: { transRefNo: self.transRefNo, infor: JSON.stringify(res) } }
              });
            }
          },
          fail: (res) => {
            self.onPayResultJump('fail');
            dispatchTrackEvent({
              target: self,
              event: EventTypes.EV,
              beaconId: 'AliPayMiniFail',
              beaconContent: { cus: { transRefNo: self.transRefNo, infor: JSON.stringify(res) } }
            });
          }
        });
      } else {
        Madp.showToast({
          title: '暂不支持支付宝支付，请稍选择其他还款方式',
          icon: 'error',
          duration: 2000
        });
      }
    } else if (isMuapp()) { // 招联自营APP
      const self = this;
      window.muapp.AliPayPlugin.alipayAction(r.alipayUrl || r.prepayId, async (params) => {
        if (params && params.resultStatus === '6001') { // 用户手动取消
          this.closeWXOrZFBPay(r, 'alipay'); // 非预支付模式->支付宝关单
          dispatchTrackEvent({
            target: self,
            event: EventTypes.EV,
            beaconId: 'MuappAliPaySDKCancel',
            beaconContent: { cus: self.transRefNo }
          });
          return;
        }
        const res = await Dispatch.repayment.alipayCallback(params);
        if (res && res.result === 'SUC') {
          self.onPayResultJump('success');
          dispatchTrackEvent({
            target: self,
            event: EventTypes.EV,
            beaconId: 'MuappAliPaySDKSuccess',
            beaconContent: { cus: self.transRefNo }
          });
        } else {
          dispatchTrackEvent({
            target: self,
            event: EventTypes.EV,
            beaconId: 'MuappAliPaySDKFail',
            beaconContent: { cus: self.transRefNo }
          });
        }
      });
      return;
    } else if (isAlipay() && !isMuapp() && process.env.TARO_ENV !== 'alipay') {
      // 是否在 支付宝 h5页面内生活号渠道还款
      this.zfbPayResult(r);
      return;
    }
    // h5页面唤起支付宝支付
    if (r.alipayUrl) {
      this.parseToDOM(r.alipayUrl);
    }
  }

  /**
     * 微信还款
     */
  wxPaySDK(r) {
    const self = this;
    if (process.env.TARO_ENV === 'weapp') { // 微信小程序
      Madp.requestPayment({
        timeStamp: r.timestamp,
        nonceStr: r.nonceStr,
        package: r.packageValue,
        signType: r.signType,
        paySign: r.sign,
        success: (res) => {
          self.onPayResultJump('success');
          dispatchTrackEvent({
            target: self,
            event: EventTypes.EV,
            beaconId: 'WechatWxPaySDKSuccess',
            beaconContent: { cus: { transRefNo: self.transRefNo, infor: JSON.stringify(res) } }
          });
        },
        fail: async (res) => {
          if (res && res.errMsg === 'requestPayment:fail cancel') { // 用户取消
            this.closeWXOrZFBPay(r, 'wechat'); // 微信关单
          } else {
            self.onPayResultJump('fail');
          }
          dispatchTrackEvent({
            target: self,
            event: EventTypes.EV,
            beaconId: 'WechatWxPaySDKFail',
            beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(res) } }
          });
        }
      });
    } else if (isWechat()) { // 微信公众号
      Madp.chooseWXPay({
        timeStamp: r.timestamp,
        nonceStr: r.nonceStr,
        package: r.packageValue,
        signType: r.signType,
        paySign: r.sign,
        success: (res) => {
          self.onPayResultJump('success');
          dispatchTrackEvent({
            target: self,
            event: EventTypes.EV,
            beaconId: 'WechatWxPaySDKSuccess',
            beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(res) } }
          });
        },
        fail: (res) => {
          if (res === 'cancel') {
            this.closeWXOrZFBPay(r, 'wechat'); // 微信关单
            return;
          }
          self.onPayResultJump('fail');
          dispatchTrackEvent({
            target: self,
            event: EventTypes.EV,
            beaconId: 'WechatWxPaySDKFail',
            beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(res) } }
          });
        }
      });
    } else if (isMuapp()) { // 招联自营APP
      try {
        window.muapp.WeChatPayPlugin.wechatpayAction({
          appId: r.appId,
          nonceStr: r.nonceStr,
          packageValue: r.packageValue,
          // 如果后端接口没送这个partnerId，无法调起微信支付
          partnerId: r.partnerId,
          prepayId: r.prepayId,
          sign: r.sign,
          timeStamp: r.timestamp,
        }, (result) => {
          dispatchTrackEvent({
            target: self,
            event: EventTypes.EV,
            beaconId: 'MuappWxPaySDKTest',
            beaconContent: { cus: { resultStatus: result.resultStatus } }
          });
          if (result.resultStatus === '9000') {
            self.onPayResultJump('success');
            dispatchTrackEvent({
              target: self,
              event: EventTypes.EV,
              beaconId: 'MuappWxPaySDKSuccess',
              beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(result) } }
            });
          } else if (result.resultStatus === '8000') { // 支付取消
            this.closeWXOrZFBPay(r, 'wechat'); // 微信关单
            dispatchTrackEvent({
              target: self,
              event: EventTypes.EV,
              beaconId: 'MuappWxPaySDKCancel',
              beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(result) } }
            });
          } else {
            Util.toast('微信支付失败');
            dispatchTrackEvent({
              target: self,
              event: EventTypes.EV,
              beaconId: 'MuappWxPaySDKFail',
              beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(result) } }
            });
          }
        }, (res) => {
          Util.toast('微信支付失败');
          dispatchTrackEvent({
            target: self,
            event: EventTypes.EV,
            beaconId: 'MuappWxPaySDKError',
            beaconContent: { cus: { transRefNo: self.transRefNo, error: JSON.stringify(res) } }
          });
        });
      } catch (error) {
        // error
      }
    } else if (r.prepayId && process.env.TARO_ENV === 'h5') {
      // const url = `${r.prepayId}&redirect_url=${encodeURIComponent(`${window.location.origin + window.location.pathname}#/pages/qrcode-repay/success`)}`;
      this.zfbPayGoBackNeedToResult = true;
      window.location.href = r.prepayId;
    } else {
      Util.toast('只有APP或Wechat渠道才可以使用微信还款！');
      dispatchTrackEvent({
        target: self,
        event: EventTypes.EV,
        beaconId: 'WxPayChannelError',
        beaconContent: { cus: self.transRefNo }
      });
    }
  }

  // 微信或支付宝关单（包括预支付和非预支付模式）
  closeWXOrZFBPay = async (r, mode) => {
    await Dispatch.repayment.orderClose({
      originalTransRefNo: r.transSeqno, // 还款流水号
      repayCloseMode: '01', // 关单模式，01-在线关单(预下单、非预下单模式关单)；02-批扣关单
    });
    Madp.showToast({
      title: mode === 'wechat' ? '微信取消' : '支付宝取消',
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * 立即支付
   */
  async immediatePayApi(noToken) {
    await this.standardPay(noToken);
  }

  // 正常还款
  async standardPay(noToken) {
    const { isWxPay } = getStore('selectedCard');
    const { alipayOnly } = this.state;
    const repayParams = this.prepareRepayParams(noToken);
    const {
      data, ret, errMsg, errCode
    } = await Dispatch.repayment.PreRepayApply(repayParams);
    this.transRefNo = Util.getTransRefNo();
    if (ret === '1') {
      dispatchTrackEvent({
        target: this, event: EventTypes.BC, beaconId: 'gotoRepayFail', beaconContent: { cus: { transSeqno: data ? data.transSeqno : '' } }
      });
      this.handleErrorCode(errMsg, errCode);
    } else {
      dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'gotoRepaySuccess' });
      this.payResult = data;
      this.payResult.alipayOnly = alipayOnly ? '1' : '0';
      this.payResult.bankCardLimitSplit = ((repayParams.bankCardLimitSplit
        || (repayParams.bankCardInfo && repayParams.bankCardInfo.bankCardLimitSplit)) === 'Y') ? '1' : '0';
      // 准备支付结果页的参数
      this.handlePayResult(repayParams);
      if (!isWxPay && (this.payResult.alipayUrl || this.payResult.prepayId)) {
        this.alipaySDK(this.payResult);
      } else if (isWxPay && (this.payResult.nonceStr || this.payResult.prepayId)) {
        this.wxPaySDK(this.payResult);
      } else if (this.payResult.transStatus === 'PROC') {
        this.onPayResultJump('success');
      } else {
        this.onPayResultJump('fail');
      }
    }
  }

  /** 立即还款错误码处理 */
  async handleErrorCode(errMsg, errCode) {
    let errorMsg = errCodeToMsgMap[errCode] || errMsg;
    const selectedCard = getStore('selectedCard');
    let showErrMsg = true;
    let setSelectCardsStatusMsg = '';
    const code = transformErrCode(errCode);
    switch (code) {
      case 'UMDP01152':// 超过限额
        setSelectCardsStatusMsg = '已超累计交易限额';
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'BankCardOverLimit' });
        break;
      case 'UMDP01155':// 余额不足
        setSelectCardsStatusMsg = '余额不足';
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'BalanceNotEnough' });
        break;
      case 'UMDP01156':// 协议不存在、协议异常 激活补签？？？
        showErrMsg = false;
        this.setState({ showContractMUModal: true });
        dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'ShowFPAYContractModal' });
        break;
      case 'UMDP01151':// 账户异常、密码异常
        setSelectCardsStatusMsg = '卡异常';
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'BankCardError' });
        break;
      case 'UMDP01708':// 银行卡已销户
        setSelectCardsStatusMsg = '您的银行卡已销户，请选择可用的银行卡或添加新卡';
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'BankCardError' });
        break;
      case 'UMDP01728':// 还款金额校验失败
        errorMsg = '还款信息已过期，请关闭当前页面后重新提交还款';
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'repayInfoOutDated' });
        break;
      default:
    }
    if (showErrMsg) {
      setTimeout(() => {
        // 弹窗显示支付失败文案
        // Util.showErrorMsg(errorMsg || '系统繁忙，请稍后再试');
        this.errorTipModalcontent = errorMsg || '系统繁忙，请稍后再试';
        this.setState({
          showErrorTipModal: true
        });
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'SystemError' });
      }, 50);
    }
    // 还款接口报错，自动刷新溢缴款接口，延迟2000ms让上面这个showToast能够显示出来
    setTimeout(() => {
      if (setSelectCardsStatusMsg) {
        this.setSelectCardsStatus(selectedCard.bankCardId, setSelectCardsStatusMsg);
      }
    }, 2000);
  }

  onPayResultJump(type) {
    this.toDetailResult(type);
  }

  toDetailResult(type) {
    const { repayConfig } = this.state;
    const repayScene = Madp.getStorageSync('repayScene', 'SESSION') || '4';
    const startLocation = Madp.getStorageSync('startLocation', 'SESSION');
    const startState = Madp.getStorageSync('startState', 'SESSION');
    const resultQuery = {
      counterSeconds: Number(repayConfig.counterSeconds) || 5,
      result: JSON.stringify(this.payResult),
      preRepayScene: repayScene,
      startLocation,
      startState,
    };
    if (type === 'success') {
      Util.router.replace({
        path: '/pages/repay-countdown/index',
        query: {
          ...resultQuery
        }
      });
    } else {
      Util.router.replace({
        path: '/pages/repay-fail/index',
        query: {
          ...resultQuery
        }
      });
    }
  }

  /**
   * 组装提交还款接口的请求参数
   */
  prepareRepayParams(noToken) {
    const commonInfo = { ...this.commonRepayInfo(noToken, true) };
    const preRepayInfo = Madp.getStorageSync('preRepayInfo', 'SESSION') || {};
    const repayScene = Madp.getStorageSync('repayScene', 'SESSION') || '';
    let securityInfoList = [];
    if (commonInfo.passwordToken) {
      securityInfoList = [{
        securityType: '1',
        securityContent: commonInfo.passwordToken
      }];
    } else if (commonInfo.smsCodeToken) {
      securityInfoList = [{
        securityType: '0',
        securityContent: commonInfo.smsCodeToken
      }];
    }

    const transTotalAmt = String(this.actualRepayAmt);
    const reqBody = {
      ...commonInfo,
      transTotalAmt, // 新还款金额参数
      securityInfoList, // 新还款 安全规则参数
      repayScene: repayScene || '4', // im场景会有客服服务案件号，是客服发卷，需要通知后台不发卷
      preRepayInfo: !repayScene || repayScene === '4' ? null : preRepayInfo, // im的情况下需要传 客服服务案件号;讨价还价要传 优先还款借据号列表
    };
    return reqBody;
  }

  /**
   * 还款方式，用于作为提交还款的入参
   * @params: transRefNo, currency, repayMode, repayWay, appId, bankCardId, smsCodeToken, passwordToken
   */
  commonRepayInfo(noToken, needBankCardInfo = false) {
    const bankCard = getStore('selectedCard');
    const {
      smsCodeToken, bankCardLimitSplitFlag
    } = this.state;
    let repayInfo = {
      transRefNo: this.transRefNo,
      currency: '156', // 代表人民币
      repayMode: this.repayMode,
    };
    // 银行卡还款
    let repayWay = 'BANK';
    if (bankCard.isAlipay && isMuapp()) {
      // app的支付宝
      repayWay = 'ALIPAY-SDK';
    } else if (bankCard.isAlipay && !isMuapp() && process.env.TARO_ENV !== 'alipay') {
      // h5 非app的支付宝 ALIPAY-H5  支付宝生活号 ALIPAY-H5_PRO
      repayWay = isAlipay() ? 'ALIPAY-H5_PRO' : 'ALIPAY-H5';
    } else if (bankCard.isWxPay && isWechat() && process.env.TARO_ENV === 'h5') {
      //  微信环境 h5 用各自公众号的appid
      repayWay = 'WEIXIN-JSAPI';
      repayInfo.appId = wechatAppId;
    } else if (bankCard.isWxPay && isMuapp() && process.env.TARO_ENV === 'h5') {
      // app的微信支付 APP用各自app的appid
      repayWay = 'WEIXIN-APP';
      repayInfo.appId = CustomConfig.appWechatAppId;
    } else if (bankCard.isWxPay && process.env.TARO_ENV === 'weapp') {
      // 微信小程序 todo 应该用小程序的appid
      repayWay = 'WEIXIN-JSAPI';
      repayInfo.appId = wechatAppId;
    } else if (bankCard.isAlipay && process.env.TARO_ENV === 'alipay') {
      // 支付宝小程序
      repayWay = 'ALIPAY-PRO';
    } else if (bankCard.isWxPay) {
      repayWay = 'WEIXIN-H5';
      repayInfo.appId = wechatH5AppId;
    }
    repayInfo.repayWay = repayWay;
    if (repayWay === 'BANK') {
      repayInfo.bankCardInfo = {
        bankCardId: bankCard.bankCardId,
        bankCardNo: bankCard.bankCardNo,
        bankCustName: bankCard.bankCustName,
        bankCardType: bankCard.cardType,
        bankName: bankCard.bankName,
        bankMobileNo: bankCard.bankMobileNo,
        bankCardLimitSplit: bankCardLimitSplitFlag
      };
    }
    /**
     * noToken一般是false,就走正常流程
     * 需要补签的操作之后，noToken为true，不传token了
     */
    if (!noToken && repayWay === 'BANK') { // 补签之后，不传任何已验证的token
      if (CustomConfig.payBySms) {
        repayInfo.smsCodeToken = smsCodeToken;
      } else {
        repayInfo.passwordToken = this.pwdResultToken;
      }
    }
    return repayInfo;
  }

  handlePayResult(repayParams) {
    const bankCard = getStore('selectedCard');
    this.payResult.actualAmt = this.payResult.repayTotalAmt;
    this.payResult.repayMode = this.repayMode;
    this.payResult.isDueTagOrD07 = this.isDueTagCust === 'Y' || this.isD07Tag;

    if (bankCard.bankOrgCode) {
      this.payResult.bankOrgCode = bankCard.bankOrgCode;
      this.payResult.cardNo = bankCard.cardNo;
    }
  }

  /**
   * 密码输入错误次数埋点
   * @param { num } 剩余输入次数
   */
  verifyFailedNum(num) {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `PasswordError0${5 - num}` });
  }

  signInContract() {
    this.setState({
      showContractMUModal: false,
      SignContract: true
    });
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'AgreeContract' });
    this.initToken('SCENE_SUPPLEMENT');
  }

  // 交易组件，回跳地址方式的结果处理。（优惠券、银行卡、金额设置，恢复原状）
  checkBioResult() {
    if (Madp.getChannel() !== '3CMBAPP' || process.env.TARO_ENV !== 'h5') return;
    const resultToken = Url.getParam('token');
    const bioResult = Url.getParam('bioResult') === 'pass';
    if (resultToken && bioResult) {
      this.onPasswordOk(resultToken);
    }
  }

  goChangePhoneNum() {
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'ResetPhone' });
    Util.externalJump('RESET_PHONE_URL');
  }

  /**
   * @description: 旧头部展示金额
   */
  get topShowAmt() {
    const { preRepayAmt } = this.state;
    return preRepayAmt;
  }


  /**
   * @description: 实际支付金额
   */
  get actualRepayAmt() {
    const { preRepayAmt } = this.state;
    return parseFloat(preRepayAmt).toFixed(2);
  }

  get repayMode() {
    return 'PRE_REPAY';
  }

  get isD07Tag() {
    let isD07Tag = false;
    const { repayControlDetailList } = this.billList;
    (repayControlDetailList || []).forEach((item = {}) => {
      if (item.controlCode === 'C401' && item.eventCode === 'GKSJ011') isD07Tag = true;
    });
    return isD07Tag;
  }

  /**
 * 是否为打标用户
 * 若仅打D07标，则isDueTagCust不返回Y，也即仅对101和C06标返回Y
 * @return { Boolean }
 */
  get isDueTagCust() {
    const { repayControlDetailList } = this.billList;
    if ((repayControlDetailList || []).length === 0) return 'N';
    let overDueFlag = false;
    (repayControlDetailList || []).forEach((item = {}) => {
      if (item.controlCode === 'C401' && (item.eventCode === 'GKSJ032' || item.eventCode === 'GKSJ033' || item.eventCode === 'GKSJ003')) overDueFlag = true;
    });
    if (overDueFlag) {
      return 'Y';
    } else {
      return 'N';
    }
  }

  /**
  * 0ZFBMNPJD H5 暂时要关闭支付宝，打开银行卡, 后续会下线这个设定
  */
  get closeWxAlipayFromCC() {
    const { repayConfig } = this.state;
    if (process.env.TARO_ENV === 'alipay') {
      return Boolean(repayConfig.closeAlipayMNP);
    } else {
      return repayConfig.closeAlipayChannel && repayConfig.closeAlipayChannel.indexOf(Madp.getChannel()) > -1;
    }
  }

  /**
   * 启用新交易组件 渠道开关 不配置就会启用
   */
  get useTranComponentSign() {
    const { repayConfig } = this.state;
    if (process.env.TARO_ENV === 'alipay') return false;
    else if (repayConfig.useTranComponent === 'allOld') return false;
    else if (repayConfig.useTranComponent === 'allNew') return true;
    return repayConfig.useTranComponent && repayConfig.useTranComponent.indexOf(Madp.getChannel()) > -1;
  }

  // 补签接口参数
  get getsignContractParam() {
    const { capitalConstractNum } = this.state;
    const selectedCard = getStore('selectedCard');
    const signContractParam = {
      cardId: selectedCard.bankCardId,
      entrance: 'SCENE_REPAYMENT',
      doVerifyCard: '1',
      whetherSupplement: true,
      firstScene: 'SIGNATURE_SENDSMS',
      isOneminute: true,
      forceSign: (selectedCard || {}).forceSignFlag === 'Y',
    };
    if (capitalConstractNum) {
      signContractParam.fundMerchantNo = capitalConstractNum;
    }
    return signContractParam;
  }

  // 返回回跳地址。交易组件在招行h5，活体完成之后要根据回跳地址继续流程
  get bioRedirectUrl() {
    if (Madp.getChannel !== '3CMBAPP' || process.env.TARO_ENV !== 'h5') {
      return undefined;
    } else {
      const CurrentUrl = window.location.href;
      const resultToken = Url.getParam('token');
      const bioResult = Url.getParam('bioResult');
      if (resultToken || bioResult) {
        // eslint-disable-next-line no-useless-escape
        const params = CurrentUrl.split(/\?|\&/);
        const filterparams = params.filter((item) => item.indexOf('token=') === -1 && item.indexOf('bioResult=') === -1);
        let newUrl = '';
        // eslint-disable-next-line array-callback-return
        filterparams.map((item, index) => {
          if (index === 0) {
            newUrl += `${item}?`;
          } else if (index < filterparams.length - 1) {
            newUrl += `${item}&`;
          } else {
            newUrl += `${item}`;
          }
        });
        return newUrl;
      } else {
        return CurrentUrl;
      }
    }
  }

  openIconModal() {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'clickTipModal', target: this });
    this.setState({
      openSmsCodeTip: true
    });
  }

  closeIconModal() {
    this.setState({
      openSmsCodeTip: false
    });
  }

  openPwdIconModal() {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'clickPwdTipModal', target: this });
    this.setState({
      openPwdTip: true
    });
  }

  closePwdIconModal() {
    this.setState({
      openPwdTip: false
    });
  }

  async selectPreRepayWayOpen() {
    const { bankCardErrorStatusList } = this.state;
    this.isRepayWayShow = true;
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'selectRepayWayOpen' });
    const initCard = getStore('selectedCard');
    await this.repayWayItem.initDefaultCard(this.bankCards, bankCardErrorStatusList);
    if (Object.keys(initCard).length !== 0) {
      await this.repayWayItem.selectCard(initCard);
    }
    this.repayWayItem.changePreRepayWayUserClick(true);
  }

  selectPreRepayWayClose() {
    this.isRepayWayShow = false;
    const haveSelectedCard = getStore('selectedCard');
    const {
      needSignFundList = [], supplementSignFlag, forceSignFlag
    } = haveSelectedCard;
    const otherNeedSignFundList = needSignFundList ? needSignFundList.filter((item) => item !== '10000') : [];
    // 支付宝和微信的情况下关闭密码框，要补签的情况下也是
    if ((Number(this.actualRepayAmt) > 0 && (haveSelectedCard.isAlipay || haveSelectedCard.isWxPay)) || (supplementSignFlag === 'Y' || (needSignFundList && needSignFundList.indexOf('10000') > -1) || otherNeedSignFundList.length || forceSignFlag === 'Y')) {
      if (this.useTranComponentSign) {
        this.biomtOrPasswOrLivenRef.handleTpClose();
      } else {
        this.setState({
          showTradePwdDialog: false,
          clearAndDisableInput: true, // 清除密码残留
        });
      }
    }
  }

  /** 补签失败处理 */
  smsVerifyFail() {
  }

  async errorTipModalCancelClick() {
    const { showFPAYContract } = this.state;
    // 补签之后要更新卡状态
    if (showFPAYContract && Madp.getStorageSync('isShowedFPAYContract', 'SESSION') === '1') {
      await this.queryBankCardInfo();
      // 隐藏BottomDrawer支付方式选择，防止页面闪屏
      this.hideBottomDrawer = true;
      this.setState({ showErrorTipModal: false }, () => {
        this.BottomDrawer.show(async () => {
          await this.initPage(true);
        });
      });
    } else {
      this.setState({
        showErrorTipModal: false
      });
    }
  }

  async errorTipModalConfirmClick() {
    // 重新查卡，更新卡状态
    await this.queryBankCardInfo();
    this.setState({
      showErrorTipModal: false
    }, () => {
      this.BottomDrawer.show(() => {
        this.selectPreRepayWayOpen();
      });
    });
  }

  render() {
    const {
      openPwdTip, openSmsCodeTip, bankCards, showSmsCodeDialog, showTradePwdDialog,
      clearAndDisableInput, showFPAYContract, showContractMUModal,
      SignContract, bankCardErrorStatusList, capitalConstractNum, selectBankTransferFlag, noInterestOrFeeFlag,
      repayConfig, showErrorTipModal, fullName
    } = this.state;
    const selectedCard = getStore('selectedCard');
    let preRepaySelectButtonText = '';
    let bankImageUrl = '';
    if (Object.keys(selectedCard).length !== 0) {
      if (selectedCard.isAlipay || selectedCard.isWxPay) {
        preRepaySelectButtonText = selectedCard.isAlipay ? '支付宝' : (selectedCard.isWxPay ? '微信' : '');
        const { bankOrg: { bankImage } } = selectedCard || {};
        bankImageUrl = bankImage;
      } else {
        const { bankName = '', cardType = '', bankCardNoMask = '', bankImage = '' } = selectedCard || {};
        bankImageUrl = bankImage;
        preRepaySelectButtonText = `${bankName} ${cardType === '1' ? '储蓄卡' : ''}(${bankCardNoMask.slice(-4)})`;
      }
    }
    if (this.repayWayItem && typeof this.repayWayItem.getPreRepayWayUserClick === 'function' && this.repayWayItem.getPreRepayWayUserClick()) {
      this.freezeCardObj.preRepaySelectButtonText = preRepaySelectButtonText;
      this.freezeCardObj.bankImageUrl = bankImageUrl;
    }
    // 用户点击的情况才改变页面上的支付方式显示，否则显示用户上一次显示的卡
    const showButtonText = this.freezeCardObj.preRepaySelectButtonText ? this.freezeCardObj.preRepaySelectButtonText : preRepaySelectButtonText;
    const showBankImageUrl = this.freezeCardObj.bankImageUrl ? this.freezeCardObj.bankImageUrl : bankImageUrl;

    return (
      <MUView className="pages-bg pre-repay">
        <MUView className="pre-repay-wrapper">
          <MUView className="pre-repay-header">
            <MUView className="pre-repay-title">
              <MUView className="pre-repay-title-text">
                预还款金额(元)
              </MUView>
            </MUView>
            <MUView className="pre-repay-amount">{this.topShowAmt}</MUView>
          </MUView>
          <MUView
            className="pre-repay-select-pre-repay-way"
            beaconId="changePreRepayWay"
            onClick={() => {
              this.BottomDrawer.show(() => {
                this.selectPreRepayWayOpen();
              });
            }}
          >
            <MUView className="pre-repay-select-text">
              支付方式
            </MUView>
            <MUView className="pre-repay-select-button">
              <MUImage className="pre-repay-select-button-bank-image" src={showBankImageUrl} />
              <MUView className="pre-repay-select-button-text">{showButtonText}</MUView>
              <MUView className="mu-icon mu-icon-arrow-right pre-repay-select-button-icon" />
            </MUView>
          </MUView>
          <MUView className={classNames({ 'visibility-hidden': this.hideBottomDrawer })}>
            <BottomDrawer
              ref={(ref) => { this.BottomDrawer = ref; }}
              title="选择支付方式"
              onClose={() => this.selectPreRepayWayClose()}
            >
              <MUView className="pre-repay-space-line" />
              <RepayWay
                isShow
                ref={(ref) => { this.repayWayItem = ref; }}
                bankCards={bankCards}
                closeWxAlipayFromCC={this.closeWxAlipayFromCC}
                isAlipayOnly={false}
                isDueTagCust={this.isDueTagCust === 'Y'}
                overDue={false}
                isOverDueUserFlag={false}
                select={(card, isUserClick = false) => {
                  this.onCardSelected(card);
                  if (isUserClick) {
                    this.BottomDrawer.hide();
                  }
                }}
                addBankCard={() => this.onAddBankCard()}
                amount={this.topShowAmt}
                bankCardErrorStatusList={bankCardErrorStatusList}
                is0100Bills={() => 'normalBill'}
                firstRepayConfig={repayConfig && repayConfig.firstRepayConfig}
                isShowDescTitle={false}
                guideZfbCard={this.supportZFBCardChannelFlag || this.supportOneBindCardChannelFlag}
              />
            </BottomDrawer>
          </MUView>

          <Contract
            onChecked={(v) => { this.isCheckedContract = v; }}
            capitalConstractNum={capitalConstractNum}
            showFPAYContract={showFPAYContract}
            showContractMUModal={showContractMUModal}
            onModalClose={() => this.setState({ showContractMUModal: false })}
            onModalConfirm={() => this.signInContract()}
            trackPrefix="repayment.NewPreRepay"
          />
          <Statistic
            ref={(ref) => { this.statistic = ref; }}
            amount={this.actualRepayAmt}
            isTotal={this.billType === 'total'}
            noInterestOrFee={noInterestOrFeeFlag}
            submitRepay={() => this.preSubmit()}
            transferGuide={() => this.onTransferGuide()}
            bankTransferFlag={selectBankTransferFlag}
            beaconId="repaySum"
            isPreRepay
          />
          {(
            <MUSafeSmsCodeHalfWrap
              title="同意并输入验证码"
              isOpened={showSmsCodeDialog}
              scene={(showFPAYContract || SignContract) ? 'SCENE_SUPPLEMENT' : (CustomConfig.payBySms ? '' : 'SCENE_REPAYMENT')}
              withLoginStatus
              needUseSendResponseMobile
              bizContent={this.transRefNo}
              useSystemInput
              replaceMsg={showFPAYContract || SignContract ? '手机号码已更换？' : ' '}
              needOnePass={false}
              onOk={(token, signChannelSuccess) => {
                console.log('ignore', signChannelSuccess);
                // 强制补签签约失败，关闭动码，弹窗引导换卡
                // const nearestSelectedCard = getStore('selectedCard');
                // dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'SignAuthCodeSuccess', beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                // if (!signChannelSuccess && (nearestSelectedCard || {}).forceSignFlag === 'Y') {
                //   dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'SignFail', beaconContent: { cus: { scene: 'BAOFU' } } });
                //   this.setState({ showSmsCodeDialog: false }, () => {
                //     const that = this;
                //     Madp.showModal({
                //       title: '验证失败',
                //       content: '请选择其他银行卡或绑定新卡',
                //       confirmText: '我知道了',
                //       confirmColor: themeColor,
                //       showCancel: false,
                //       success(res) {
                //         if (res.confirm) {
                //           dispatchTrackEvent({ target: that, event: EventTypes.EV, beaconId: 'VerifyFailConfirm', beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                //           that.setSelectCardsStatus(nearestSelectedCard.bankCardId, '验证失败');
                //         }
                //       }
                //     });
                //   });
                //   return;
                // }
                this.onSmsVerifyOk(token);
              }}
              onOposcExempt={(token) => { this.onSmsVerifyOk(token); }}
              onError={(errObj) => {
                // 补签银行发送动码失败，弹窗
                if ((showFPAYContract || SignContract) && ((errObj || {}).errCode === 105 || (errObj || {}).errCode === 106)) {
                  const nearestSelectedCard = getStore('selectedCard');
                  dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'SendCodeFail', beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                  this.setState({ showSmsCodeDialog: false }, () => {
                    const that = this;
                    Madp.showModal({
                      title: '动码发送失败',
                      content: '请选择其他银行卡或绑定新卡',
                      confirmText: '我知道了',
                      confirmColor: themeColor,
                      showCancel: false,
                      success(res) {
                        if (res.confirm) {
                          dispatchTrackEvent({ target: that, event: EventTypes.EV, beaconId: 'VerifyFailConfirm', beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                          that.setSelectCardsStatus(nearestSelectedCard.bankCardId, '验证失败');
                        }
                      }
                    });
                  });
                }
              }}
              onClose={() => this.setState({ showSmsCodeDialog: false })}
              beaconId="repaySafeSmsCodePopup"
              onTitleIconClicked={this.openIconModal.bind(this)}
              onVerifyFail={() => {
                const nearestSelectedCard = getStore('selectedCard');
                dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'AuthCodeFail', beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                Madp.showToast({ title: '验证失败，请稍后重试或更换支付方式', icon: 'none' });
              }}
              onMobileReplaceClick={() => {
                this.goChangePhoneNum();
              }}
              busiSendOpId={showFPAYContract || SignContract ? 'mucfc.user.verifyIdentify.commonSendSms' : ''}
              busiSendArgs={showFPAYContract || SignContract ? this.getsignContractParam : {}}
              busiVerifyOpId={showFPAYContract || SignContract ? 'mucfc.user.bankCard.addBankCard' : ''}
              busiVerifyArgs={showFPAYContract || SignContract ? this.getsignContractParam : {}}
            />
          )}
          {
            <MUTradePasswordEncryptedWrap
              onTitleIconClicked={this.openPwdIconModal.bind(this)}
              scene="SCENE_REPAYMENT"
              verifyType="TRA"
              isOpened={showTradePwdDialog}
              needFpPayment
              clearAndDisableInput={clearAndDisableInput}
              title="同意并输入6位数字交易密码"
              leftText=""
              rightText="忘记数字密码"
              onOk={(msg, token) => { this.onPasswordOk(token); }}
              onClose={() => this.onPasswordClose()}
              onOverPwAttemptNum={() => {
                Madp.showModal({
                  title: '温馨提示',
                  content: '密码输入错误次数超限，请稍候再试',
                  confirmText: '确认',
                  confirmColor: themeColor,
                  showCancel: false
                });
                dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'PwdOverTimes' });
              }}
              onNeedModifyPass={() => this.onNeedModifyPass()}
              onForgotPass={() => this.onForgotPass()}
              onVerifyFailed={(num) => {
                dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'PasswordFail' });
                this.verifyFailedNum(num);
              }}
              userId="yourUserId"
              beaconId="tradePasswordEncrypted"
            // hostname={apiHost.mgp}
            />
          }
          {<OnePassOrSmsCodeWrap
            title="同意并输入验证码"
            onTitleIconClicked={this.openIconModal.bind(this)}
            needOnePass={false}
            useSystemInput
            onRef={(ref) => { this.onePassOrSmsCodeRef = ref; }}
            scene={showFPAYContract || SignContract ? 'SCENE_SUPPLEMENT' : 'SCENE_REPAYMENT'}
            onOposcError={(errObj) => {
              // 补签银行发送动码失败，弹窗
              if ((showFPAYContract || SignContract) && ((errObj || {}).errCode === 105 || (errObj || {}).errCode === 106)) {
                const nearestSelectedCard = getStore('selectedCard');
                dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'SendCodeFail', beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                this.onePassOrSmsCodeRef && this.onePassOrSmsCodeRef.closeOposc && this.onePassOrSmsCodeRef.closeOposc();
                this.setState({ showSmsCodeDialog: false }, () => {
                  const that = this;
                  Madp.showModal({
                    title: '动码发送失败',
                    content: '请选择其他银行卡或绑定新卡',
                    confirmText: '我知道了',
                    confirmColor: themeColor,
                    showCancel: false,
                    success(res) {
                      if (res.confirm) {
                        dispatchTrackEvent({ target: that, event: EventTypes.EV, beaconId: 'VerifyFailConfirm', beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
                        that.setSelectCardsStatus(nearestSelectedCard.bankCardId, '验证失败');
                      }
                    }
                  });
                });
              }
            }}
            onOposcExempt={() => {
              // updateSmsToken(token);
              // this.payApply(false);
            }}
            bizcontent={this.transRefNo}
            onOposcOk={(token, signChannelSuccess) => {
              console.log('ignore', signChannelSuccess);
              // 强制补签签约失败，关闭动码，弹窗引导换卡
              // const nearestSelectedCard = getStore('selectedCard');
              // dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'SignAuthCodeSuccess', beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
              // if (!signChannelSuccess && (nearestSelectedCard || {}).forceSignFlag === 'Y') {
              //   dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'SignFail', beaconContent: { cus: { scene: 'BAOFU' } } });
              //   this.setState({ showSmsCodeDialog: false }, () => {
              //     const that = this;
              //     Madp.showModal({
              //       title: '验证失败',
              //       content: '请选择其他银行卡或绑定新卡',
              //       confirmText: '我知道了',
              //       confirmColor: themeColor,
              //       showCancel: false,
              //       success(res) {
              //         if (res.confirm) {
              //           dispatchTrackEvent({ target: that, event: EventTypes.EV, beaconId: 'VerifyFailConfirm', beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
              //           that.setSelectCardsStatus(nearestSelectedCard.bankCardId, '验证失败');
              //         }
              //       }
              //     });
              //   });
              //   return;
              // }
              this.onSmsVerifyOk(token);
            }}
            busiSendOpId={showFPAYContract || SignContract ? 'mucfc.user.verifyIdentify.commonSendSms' : ''}
            busiSendArgs={showFPAYContract || SignContract ? this.getsignContractParam : {}}
            busiVerifyOpId={showFPAYContract || SignContract ? 'mucfc.user.bankCard.addBankCard' : ''}
            busiVerifyArgs={showFPAYContract || SignContract ? this.getsignContractParam : {}}
            onSmsVerifyFail={() => {
              const nearestSelectedCard = getStore('selectedCard');
              dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'AuthCodeFail', beaconContent: { cus: { scene: (nearestSelectedCard || {}).forceSignFlag === 'Y' ? 'BAOFU' : (capitalConstractNum ? 'ZHUDAI' : 'OTHER') } } });
              Madp.showToast({ title: '验证失败，请稍后重试或更换支付方式', icon: 'none' });
            }}
            onMobileReplace={() => {
              // STORE.showCmbData = '1';
              // this.saveLoanData();
              dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'ResetPhone' });
              Util.externalJump('RESET_PHONE_URL');
            }}
          />}
          {<BiomtOrPasswOrLivenWrap
            title="同意并输入6位交易密码"
            clearAndDisableInput={clearAndDisableInput}
            onTitleIconClicked={this.openPwdIconModal.bind(this)}
            onRef={(ref) => { this.biomtOrPasswOrLivenRef = ref; }}
            isLivenAllowed={Madp.getChannel() !== '3CMBAPP'}
            scene="SCENE_REPAYMENT"
            // isBiomtAllowed={false}
            onSetTradePw={this.onForgotPass.bind(this)}
            onForgetPw={this.onForgotPass.bind(this)}
            onBopolError={() => { }}
            onCancel={() => { }}
            onLivenOverNum={() => { }}
            onBopolOk={(token) => {
              this.onPasswordOk(token);
            }}
            onTpVerifyFail={() => {
              // 失败回调。可以带参数，为剩余的验证次数
              dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'PasswordFail' });
            }}
            redirectUrl={this.bioRedirectUrl}
            needToastAfterVerifyOk={false}
            bioVerifyMethod="H5"
          />}
          <MUModal
            beaconId="tipModal"
            className="tipModal"
            type="text"
            isOpened={openSmsCodeTip}
            title="关于个人信息处理规则说明"
            content={`在以下场景中，${fullName}需要核实您的验证码，以验证您的手机号码是否真实、有效：<br>（1）注册、登录招联平台或您需要销户、挂失、解除挂失；<br>（2）您需要申请授信、借款或使用招联借款用于消费；<br>（3）您需要修改您向招联提供的信息（如姓名、手机号码）；<br>（4）您需要启用、修改手势密码，或设置、修改数字交易密码，或挂失、解挂失；<br>（5）其他需要验证您手机号码是否真实、有效的场景。<br>验证码是您的重要个人信息，一旦泄露或处理不当可能危害您的财产安全，招联将根据法律法规要求并参照行业最佳实践为您的个人信息安全提供保障。`}
            confirmText="我知道了"
            onClose={this.closeIconModal.bind(this)}
            closeOnClickOverlay={false}
            onConfirm={this.closeIconModal.bind(this)}
          />
          <MUModal
            beaconId="pwdTipModal"
            className="pwdTipModal"
            type="text"
            isOpened={openPwdTip}
            title="关于个人信息处理规则说明"
            content={`在以下场景中，${fullName}需要收集、验证您输入的交易密码：<br>(1)您在招联设置或修改您的交易密码时；<br>(2)您在招联进行借款、消费、红包提现等交易时；<br>(3)您在招联查看或获取重要信息时，如账户解挂、合同下载等。<br>交易密码是您的重要个人信息，一旦泄露或处理不当可能危害您的财产安全，招联将根据法律法规要求并参照行业最佳实践为您的个人信息安全提供保障。`}
            confirmText="我知道了"
            onClose={this.closePwdIconModal.bind(this)}
            closeOnClickOverlay={false}
            onConfirm={this.closePwdIconModal.bind(this)}
          />
          <MUModal
            beaconId="errorTipModal"
            isOpened={showErrorTipModal}
            content={this.errorTipModalcontent}
            confirmText="更换支付方式"
            cancelText="取消"
            onCancel={() => {
              this.errorTipModalCancelClick();
            }}
            closeOnClickOverlay={false}
            onConfirm={() => {
              this.errorTipModalConfirmClick();
            }}
          />
        </MUView>
      </MUView>
    );
  }
}
