@import '../../components/weapp/index.scss';

.pre-repay {
  position: relative;
  overflow: hidden;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  // background-color: #fff !important;

  &-no-info {
    font-size: 50px;
    padding: 200px 120px 300px 120px;
    font-weight: 600;
  }

  &-logo-text {
    font-size: 80px;
    padding-top: 50px;
    text-align: center;
    font-weight: 600;
  }

  &-close-button {
    background-color: #fff;
    color: #333;
    width: 300px;
    border-color: #333;
  }

  &-top {
    font-size: 50px;
    text-align: center;
    padding-top: 54px;
    padding-bottom: 54px;
    margin-bottom: 0;
  }

  &-wrapper {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 220px;
    overflow: scroll;

    .mu-contract-checker {
      padding: 0 0 0 30px;
    }

    .mu-trade-password-keboard {
      .mu-dialog {
        z-index: 800;

        .mu-dialog__overlay {
          display: none;
        }

        .mu-dialog__container {
          // border-top: 1px solid;
        }

        .mu-dialog__container {
          .mu-dialog__content {
            .mu-trade-password {
              .mu-trade-password__close {
                display: none;
              }
            }
          }
        }
      }
    }

    .at-drawer {
      z-index: 1001;
    }

  }

  &-header {
    padding-bottom: 60px;
    padding-top: 60px;
    background: #fff;
    text-align: center;
  }

  &-title {
    margin-bottom: 37px;

    &-text {
      font-size: 32px;
      line-height: 32px;
      color: #808080;
    }
  }

  &-amount {
    color: #333;
    font-size: 80px !important;
    font-weight: 600;
    height: 80px;
    line-height: 80px;
  }

  &-tip {
    font-size: 32px;
    line-height: 40px;
    text-align: left;
    padding-left: 57px;
    padding-right: 31px;
    padding-top: 30px;

    &-red {
      color: red;
      margin-top: 20px;
      margin-bottom: 20px;
    }
  }

  &-space-line {
    margin-top: 20px;
  }

  &-select-pre-repay-way {
    display: flex;
    justify-content: space-between;
    padding-left: 40px;
    padding-right: 40px;
    font-size: 32px;
    color: #808080;
    position: relative;
    z-index: 801;
    background-color: #fff;
    height: 100px;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  &-select-text {
    height: 26px;
    font-weight: 400;
    font-size: 26px;
    color: #808080;
    text-align: center;
    line-height: 100px;
  }

  &-select-button {
    display: flex;
    align-items: center;

    &-text {
      height: 32px;
      font-weight: 400;
      font-size: 32px;
      color: #333333;
      line-height: 32px;
    }

    &-bank-image {
      width: $mu-list-thumb-size;
      height: $mu-list-thumb-size;
    }

    &-icon {
      width: 15px;
      height: 26px;
      margin: auto;
    }
  }

  .repay-contract {
    width: 100%;
    background-color: #fff;
    position: fixed;
    bottom: 120px;
    display: flex;
    align-items: center;
    z-index: 801;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);
    // border-bottom: 1px solid #E5E5E5;

    .repay-contract-container {
      display: flex;
      align-items: center;

    }

    &-icon {
      margin-left: 13px;
      color: #808080;
      // line-height: 2px;
    }
  }

  .gbl-basic-lifefollow {
    .app-wrap>div {
      margin-top: 20px;
    }
  }

  .modal-content {
    font-size: 32px;
    text-align: center;
    padding: 0 $spacing-v-xxl;
    margin-bottom: $spacing-v-xl;
  }

  &-transefer-modal {
    .modal-content {
      color: #353535;

      .day-content {
        color: #FE5A5F;
      }
    }
  }

}

.display-none {
  display: none;
}

.repay-bottom-button {
  position: fixed;
  bottom: 0;
  padding-left: 30px;
  padding-right: 30px;
  // padding-bottom: 15px;
  background: #fff;
  height: 120px;
  z-index: 800;
  left: 0;
  right: 0;

  .theme-background-color {
    background: $color-brand;
    color: #fff;
    border-radius: 20px;
  }
}

.visibility-hidden {
  visibility: hidden;
}