import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUImage, MUButton, MURichText, MUSlogan
} from '@mu/zui';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import { miniProgramChannel } from '@utils/constants';
import {
  Url,
  isMuapp
} from '@mu/madp-utils';
import pageHoc from '@utils/pageHoc';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import ChannelConfig from '@config/index';
import { jumpToChatPage } from '@mu/chat-entry-component';
import './index.scss';
import ConsultRepayFail from './img/consult-repay-fail.png';

const SUCCESS_IMAGE = 'https://file.mucfc.com/zlh/3/0/202305/20230518202321a82936.png';
const PENDING_IMAGE = 'https://file.mucfc.com/zlh/3/0/202305/2023051820221669f9b7.png';
const themeColor = Util.getThemeColor(ChannelConfig.theme);
@track({ event: EventTypes.PO }, {
  pageId: 'ConsultReapyResult',
  dispatchOnMount: true,
})
@pageHoc({ title: '协商还' })
export default class ConsultReapyResult extends Component {
  config = {
    navigationBarTitleText: '协商还',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);

    this.state = {
      statusCofig: {}
    };
  }

  async componentWillMount() {
    // status = 1为成功, status = 2 为审核中, status = 3 为失败页
    const status = Url.getParam('status');
    const statusScence = Url.getParam('statusScence');
    let statusConfigData = {};

    if (status === '1') {
      statusConfigData = {
        statusImage: SUCCESS_IMAGE,
        statusText: '办理成功',
        statusTips: ''
      };
      const tips = await this.initSuccessStatusInfo();
      statusConfigData.statusTips = tips;
    } else if (status === '2') {
      statusConfigData = {
        statusImage: PENDING_IMAGE,
        statusText: '审批中',
        statusTips: '预计2个工作日审批完成，请耐心等待',
      };
    } else if (status === '3') {
      if (statusScence === '1') {
        statusConfigData = {
          statusImage: ConsultRepayFail,
          statusText: '办理失败',
          statusTips: '抱歉，您当前没有可办理的逾期借据',
        };
      } else {
        statusConfigData = {
          statusImage: ConsultRepayFail,
          statusText: '办理失败',
          statusTips: '抱歉，当前系统异常，可联系在线客服处理',
        };
      }
    }

    this.setState({
      statusCofig: statusConfigData
    });
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'Status', beaconContent: { cus: { status, statusScence } } });
  }

  initSuccessStatusInfo = async () => {
    const applyNo = Url.getParam('applyNo');
    const params = {
      queryType: '2',
      applyNo,
    };
    const result = await Dispatch.repayment.queryRepayPlanAndStatus(params);
    const { data } = result || {};
    const { negotiateRepayTaskBill } = data || {};
    const { repayPlanList } = negotiateRepayTaskBill || {};
    const firstRepayPlanList = repayPlanList && repayPlanList[0] || {};
    const { repayDate, taskThresholdAmt } = firstRepayPlanList || {};
    const month = repayDate && repayDate.substring(4, 6);
    let day = repayDate && repayDate.substring(6, 8);
    day = day && parseInt(day);

    return `${month}月${day}日 需还款<span id=yellow style="color: ${themeColor};">${taskThresholdAmt}</span>元，<span id=yellow style="color: ${themeColor};">请按时在招联首页操作还款</span>，若超期，会根据总待还金额恢复自动扣款`;
  }

  // 判断当前的webview是否在小程序环境中，已经在madp-util中了(改这里的时候它还没上生产)，20240110之后可以使用madp-util的isWebViewInMicroApp
  isWebViewInMicroApp() {
    if (process.env.TARO_ENV === 'h5') {
      const ua = navigator.userAgent.toLowerCase();
      // 抖音toutiaomicroapp 百度baiduboxapp
      return (
        /miniprogram/.test(ua)
        || /toutiaomicroapp/.test(ua)
        || /baiduboxapp/.test(ua)
      );
    }
    return false;
  }

  clickBackBtnHandler = () => {
    const redirectUrlFromUrl = Url.getParam('resultRedirectUrl') ? decodeURIComponent(Url.getParam('resultRedirectUrl')) : '';
    const consultResultRedirectUrlFromLocal = Madp.getStorageSync('consultResultRedirectUrl', 'LOCAL') ? decodeURIComponent(Madp.getStorageSync('consultResultRedirectUrl', 'LOCAL')) : '';
    const redirectUrl = redirectUrlFromUrl || consultResultRedirectUrlFromLocal || '';
    const isMiniProgramChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;

    // console.log(redirectUrl && redirectUrl !== 'undefined', isMiniProgramChannel, 'isMiniProgramChannel=====');
    // 特别处理如果为招联app场景下，非还款场景进入的时候直接关闭webview
    if (isMuapp() && redirectUrl.indexOf('repayment/#/') === -1) {
      Madp.closeWebView();
      return;
    }

    // 存在redirectUrl时，跳转到redirectUr
    if (redirectUrl && redirectUrl !== 'undefined') {
      if (isMiniProgramChannel) {
        Madp.miniProgram.reLaunch({
          url: redirectUrl
        });
      } else {
        Madp.redirectTo({ url: redirectUrl });
      }
    } else {
      if (isMiniProgramChannel) {
        Madp.miniProgram.reLaunch({
          url: redirectUrl
        });
      } else {
        const length = window && window.history && window.history.length;
        if (length === 1) {
          Madp.closeWebView();
        } else {
          Madp.navigateBack({ delta: length - 1 });
        }
      }
    }
  }

  clickConsultFailBtnHandler = () => {
    const busiEntrance = Util.getBusiEntrance();
    jumpToChatPage({
      busiEntrance,
      extraParam: {}
    });
  }

  render() {
    const status = Url.getParam('status');
    const statusScence = Url.getParam('statusScence');
    const { statusCofig } = this.state;
    const { statusImage, statusText, statusTips } = statusCofig || {};

    return (<MUView className="pages-bg">
      <MUView className="consult-repay-result">
        <MUView className="consult-status-img-container">
          <MUImage className="consult-status-img" src={statusImage} />
        </MUView>

        <MUView className="consult-status-text">{statusText}</MUView>

        <MURichText className="consult-status-tips" nodes={statusTips} />

        {status === '3' && statusScence !== '1'
          ? <MUButton
              beaconId="ConsultResultFailBtn"
              className="consult-result-btn"
              type="primary"
              onClick={() => {
                this.clickConsultFailBtnHandler();
              }}
          >联系在线客服</MUButton>
          : <MUButton
              beaconId="ConsultResultBackBtn"
              beaconContent={{
                cus: {
                  status,
                  statusScence
                }
              }}
              className="consult-result-btn"
              type="primary"
              onClick={() => {
                this.clickBackBtnHandler();
              }}
          >返回</MUButton>}

        <MUView className="consult-repay-result-container">
          <MUSlogan className="result-slogan" onlyLogo />
        </MUView>
      </MUView>
    </MUView>);
  }
}
