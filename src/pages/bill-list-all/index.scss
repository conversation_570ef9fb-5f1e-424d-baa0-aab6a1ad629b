@import '../../components/weapp/index.scss';

.reapy-all {

  // 客服组件样式
  .chat-entry {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: -186px;
  }

  @supports (padding-bottom: constant(safe-area-inset-bottom)) {
    .chat-entry {
      margin-top: calc(-186px - constant(safe-area-inset-bottom));
    }
  }
  @supports (padding-bottom: env(safe-area-inset-bottom)) {
    .chat-entry {
      margin-top: calc(-186px - env(safe-area-inset-bottom));
    }
  }

  .header {
    height: 252px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding-left: 55px;
    background-color: #FFFFFF;

    &-text {
      height: 32px;
      margin-top: 48px;
      opacity: 1;
      color: #333333;
      font-size: 32px;
      font-weight: 400;
      line-height: 32px;
    }

    &-amount {
      height: 100px;
      margin-top: 26px;
      opacity: 1;
      color: #333333;
      font-size: 80px;
      font-weight: 700;
      font-family: "DIN Alternate";
      line-height: 100px;
    }
  }

  .all-list-content-new {
    width: 100%;
    min-height: calc(100vh - 252px); // 很关键
  }

  .public-detain .mu-dialog__content .detain-dialog__btn-short .common {
    width: 500px;
    margin-left: unset;
  }

  .theme-background-color {
    background: $color-brand;
  }

  .lead-dialog {
    &-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0px 30px;

      &-icon {
        display: block;
        margin-top: 12px;
        margin-bottom: 30px;
      }

      &-title {
        width: 450px;
        line-height: 34px;
        margin-bottom: 30px;
        font-size: 36px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        color: #333333;
        text-align: center;
      }

      &-text {
        width: 450px;
        line-height: 40px;
        color: #808080;
        font-size: 28px;
        font-family: PingFangSC;
        font-weight: 400;
        text-align: center;
      }

      &-confirm {
        width: 450px;
        height: 88px;
        margin-top: 30px;
        font-size: 36px;
      }

      &-cancel {
        width: 450px;
        height: 88px;
        margin-bottom: 20px;
        color: #808080;
        font-size: 30px;
        border: none;
        background-color: #FFFFFF !important;
      }
    }

    .mu-modal__container {
      padding-top: 20px !important; 
    }
  }
}

.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}
