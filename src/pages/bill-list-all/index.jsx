/* eslint-disable no-unused-expressions */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable indent */
/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import { MUView, MUNavBarWeapp } from '@mu/zui';
import Madp from '@mu/madp';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import BillListNew from '@components/bill-list-new';
import EmptySign from '@components/empty-sign';
import { getStore, setStore } from '@api/store';
import { Url } from '@mu/madp-utils';
import { opService, getPageConf, getProductAllParams, stayWithTime } from '@mu/business-basic';
import Dispatch from '@api/actions';
import { injectState, refresh } from '@mu/leda';
import { inject } from '@tarojs/mobx';
import Util from '@utils/maxin-util';
import { repayServiceColumnsUrls, EVENT_CODE_MAP } from '@utils/constants';
import { filterQualCoupons } from '@utils/repay-util';
import pageHoc from '@utils/pageHoc';
import ChannelConfig from '@config/index';
import RepaymentGlobalStore from '@repayment/store/rootStore';
import { RepaymentPlugin as BusinessPlugin } from '@mu/business-plugin';
import { repaymentFn } from '@mu/business-plugin-utils';
import LeadToFeeReduceModal from '@components/modals/LeadToFeeReduceModal';
import { OpRepayment } from '@mu/op-comp';

import './index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/bill-list-new/index.scss');
  require('@components/list-item-new/index.scss');
  require('@components/repay-modal/index.scss');
  require('@components/detail-item/index.scss');
  require('@components/empty-sign/index.scss');
}

if (process.env.TARO_ENV === 'swan') {
  require('./index_swan.scss');
}

let { billListAllFn } = repaymentFn || {};
let ledaNameList = billListAllFn.getLedaNameList();

// 默认页面展位id
const BillListAllLeDaId = '3637e644-e2e2-440b-bd00-c50295d1b9ea';

const themeColor = Util.getThemeColor(ChannelConfig.theme);

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      custStatus: Url.getParam('custStatus') || '',
      pageId: billListAllFn.getPageId() || BillListAllLeDaId
    }
  }
}, {
  pageId: 'BillListAll',
  dispatchOnMount: true,
})
@pageHoc({ title: '全部待还' })
@inject(() => ({ repaymentGlobalStore: new RepaymentGlobalStore() }))
@injectState({
  pageId() {
    return billListAllFn.getPageId() || BillListAllLeDaId;
  },
  getPageConf: () => getPageConf(BillListAllLeDaId, true),
  stateKeys: [
    ...ledaNameList
  ]
})
export default class BillListAll extends Component {
  config = {
    navigationBarTitleText: '全部待还',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      selectedBillList: [], // 选择的账单，不移动
      selectedBillIndex: 0, // 当前选择的账单索引
      allBillList: [], // 可还借据
      readOnlyBillList: [], // 可见账单，不一定可还
      hasOverDueBill: false,
      unicomBillList: [],
      hasControlTag: false, // 判断是否打标用户
      showLeadToFeeReduceModal: false, // 是否展示取消借据勾选提示弹窗
      hasGradientReduceCoupon: false, // 是否有梯度减免资格券-提前还款场景
      totalFeeAmt: '', // 待还息费
    };
    this.allBills = {};
    this.repayConfig = {};
    this.chatEntryParam = getStore('chatEntryExtraParam') || {};
    this.billListAllStore = props.repaymentGlobalStore.billListAllStore;
    this.prepayFeeRightsCouponList = {}; // 免提还违约金资格券
    this.showPage = false;
    this.custStatus = Url.getParam('custStatus') || '';
    this.totalRepayAmt = '0';
    this.hasTriggerOpDetainDialog = false; // 标识还款交互运营退出挽留弹窗是否触发
    this.isAdvanceRepay = this.custStatus === 'OutSeven'; // 提前还款（仅考虑还款首页入口）
  }

  async componentDidMount() {
    if (Madp.getChannel() === '3CMBAPP') {
      //针对招行渠道清除支付页的金额和优惠券缓存等信息
      setStore({
        externalFillObj: {}
      });
    }
    const params = {
      additionalQueryList: ['001'], // 查询协商分期账单信息
    };
    await this.billListAllStore.getAllBills(params);
    this.allBills = this.billListAllStore.allBills;
    this.setState({ hasControlTag: this.billListAllStore.hasControlTag });

    if (this.handleErrorEnter()) return;

    const repayBillList = (this.allBills || {}).advanceBillList || []; // 可还列表
    const { overdueDays } = this.allBills; // 客户在途自营借据的最大逾期天数，若客户在途自营借据均未逾期，则逾期天数为0
    // 拿到客户提前结清账单接口返回的逾期标识再去查优惠券
    const hasGradientReduceCoupon = await this.getFeeRightsCouponList(overdueDays);

    const {
      surplusPayTotalInteAmt, // 利息
      surplusPayTotalPeriodFeeAmt, // 期费用
      surplusPayTotalFineAmt, // 罚息
      surplusPayTotalOnetimeFee // 一次性费用
    } = this.allBills;
    // 计算总的待还息费
    const totalFeeAmt = (Number(surplusPayTotalInteAmt) + Number(surplusPayTotalPeriodFeeAmt) + Number(surplusPayTotalFineAmt) + Number(surplusPayTotalOnetimeFee)).toFixed(2);

    let selectedBillList = [];
    const loanInfoBack = Madp.getStorageSync('hasJumpToLoanInfo', 'SESSION');
    Madp.removeStorageSync('hasJumpToLoanInfo', 'SESSION');
    if (loanInfoBack === 'Y') {
      // 从借据页面跳转回来的，自动勾选跳转前勾选的借据
      const selectedBillListFromSotre = Madp.getStorageSync('selectedBillList', 'SESSION');
      Madp.removeStorageSync('selectedBillList', 'SESSION');
      selectedBillList = (selectedBillListFromSotre || []).length > 0 ? selectedBillListFromSotre : [];
    } else {
      // 默认先选逾期
      selectedBillList = repayBillList.filter((bill) => bill.displayOverdueDays && bill.canPayFlag === 'Y') || [];
    }

    setStore({ selectedBillList });
    if (this.allBills.showBillList && this.allBills.showBillList.length) dispatchTrackEvent({ target: this, event: EventTypes.PO, beaconId: 'DisplayOnly' });
    // repayBillList[0].belongUnicomContractOrder = 'Y';
    const unicomBillList = repayBillList.filter((bill) => bill.belongUnicomContractOrder === 'Y' && !bill.displayOverdueDays);
    this.setState({
      allBillList: repayBillList,
      selectedBillList,
      readOnlyBillList: this.allBills.showBillList,
      hasOverDueBill: selectedBillList.length > 0,
      unicomBillList,
      hasGradientReduceCoupon,
      totalFeeAmt
    }, () => {
      // 从借据页面跳转回来的，已自动勾选跳转前勾选的借据，管控客户也不再调用onSelectAll
      if (loanInfoBack === 'Y') return;

      // 有可用的梯度减免-提前还款券，且全部可见可还借据的息费（利息+期费用+罚息+一次性费用）大于0，且为管控客户，则自动全选可见可还借据，不可取消
      const { hasControlTag } = this.state;
      if (hasGradientReduceCoupon && Number(totalFeeAmt) > 0 && hasControlTag) {
        this.onSelectAll();
      }
    });
    this.showPage = true;
    this.setExtraParam();
    // 交互运营-停留事件
    this.handleOpPageStayEvent();
  }

  // 提前还款-交互式运营事件-停留
  handleOpPageStayEvent = () => {
    this.stayTimer = null;
    if (!this.stayTimer && this.isAdvanceRepay && typeof stayWithTime === 'function') {
      this.stayTimer = stayWithTime(10000, async () => {
        if (this.stayTimer) {
          await this.opOnPageEvent('opPageStay', EVENT_CODE_MAP.billListAllAdvanceStay);
        }
      });
    }
  }

  // 提供放置挽留弹窗的位置
  async beforeRouteLeave(from, to, next) {
    // 在途停留事件取消
    if (this.stayTimer) {
      this.stayTimer = null;
    }
    if (this.hasTriggerOpDetainDialog) {
      next(true);
    } else if ((to.path === '/pages/index/index' || to.path === '/index') && this.isAdvanceRepay) {
      this.hasTriggerOpDetainDialog = true;
      if (await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.billListAllAdvanceExit)) {
        next(false);
        return;
      }
      next(true);
    } else if (billListAllFn.beforeRouteLeaveHandler) {
      billListAllFn.beforeRouteLeaveHandler(from, to, next);
    } else {
      next(true);
    }
  }

  beforeMiniRouteLeave = async () => {
    // 在途停留事件取消
    if (this.stayTimer) {
      this.stayTimer = null;
    }
    if (this.hasTriggerOpDetainDialog) {
      Madp.navigateBack();
    } else if (this.isAdvanceRepay) { // 提前还款（仅考虑还款首页入口）
      this.hasTriggerOpDetainDialog = true;
      if (!await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.billListAllAdvanceExit)) {
        Madp.navigateBack();
      }
    } else {
      Madp.navigateBack();
    }
  }

  // 打标客户重定向到还款首页
  handleErrorEnter = () => {
    let exit = false;
    const { isDueTagCust } = this.allBills || {};
    if (isDueTagCust === 'Y') {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: 'ControlCusErrorEnter',
      });
      Util.router.replace({ path: '/pages/index/index' });
      exit = true;
    }
    return exit;
  }

  // 查询梯度减免资格券
  async getFeeRightsCouponList(overdueDays) {
    if (Number(overdueDays) >= 1) return;
    const { awardDetailList = [] } = await Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }) || {};
    const { usableQualConponList } = filterQualCoupons(awardDetailList || [], (this.allBills || {}).advanceBillList) || {};
    const gradientReduceCouponList = (usableQualConponList || []).filter((item) => (item.awardType === '216' && item.subUseSceneCode === 'SSA08'));
    const prepayFeeRightsCouponList = (usableQualConponList || []).filter((item) => (item.awardType === '306'));
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: gradientReduceCouponList && gradientReduceCouponList.length > 0 ? 'HaveGradientReduceCoupon' : 'NoGradientReduceCoupon', target: this, beaconContent: { cus: {} } });
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: prepayFeeRightsCouponList && prepayFeeRightsCouponList.length > 0 ? 'HavePrepayFeeRightsCouponList' : 'NoPrepayFeeRightsCouponList', target: this, beaconContent: { cus: {} } });
    // 存储免提还资格券信息，到支付页使用
    setStore({ prepayFeeRightsCouponList });
    this.prepayFeeRightsCouponList = prepayFeeRightsCouponList;
    return gradientReduceCouponList && gradientReduceCouponList.length > 0;
  }

  async updateLego(opt = {}) {
    refresh({
      ...opt,
      pageId: BillListAllLeDaId,
      isRequest: true
    });
  }

  async initPageConfig() {
    this.repayConfig = await Dispatch.repayment.getMultipleCommonConfig(['repayAllBill.config', 'repayMarketing.config']) || {};
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '全部待还' });
  }

  /** 为招行渠道设置客服入口的参数 */
  setExtraParam() {
    if (Util.showChatEntry() && Madp.getChannel() === '3CMBAPP' && Object.keys(this.chatEntryParam).length) {
      Madp.eventCenter.trigger('CHAT_ENTRY_EXTRAPARAM');
    }
  }

  handleSelect = (bill) => {
    // 由于存在同笔借据，两个账单同时展示的情况，不能单纯用orderNo作为区分
    // 故直接用账单本身判断是否勾选
    if (bill.canPayFlag === 'N') {
      const reason = Util.getCantPayReason(bill);
      if (reason) Util.toast(reason);
      return;
    }
    const { selectedBillList, allBillList, hasGradientReduceCoupon, totalFeeAmt, hasControlTag } = this.state;
    const billIndex = selectedBillList.findIndex((selectedBill) => selectedBill.orderNo === bill.orderNo);
    if (this.unicomBillCheck(bill, selectedBillList, billIndex)) {
      return;
    }
    if (billIndex > -1) {
      // 取消选中，如果反选的是任意逾期账单，则反选所有小于该逾期天数的账单
      const maxOverdue = Math.max(...selectedBillList.map((item) => item.displayOverdueDays));
      if (maxOverdue && bill.displayOverdueDays) {
        // 有延期账单，并且点选的账单有延期的前提下，反选所有小于该逾期天数的账单
        // 先把自己去掉，否则后续判断大于等于选中逾期天数时会被保留
        // selectedBillList.splice(billIndex, 1);
        // const billList = selectedBillList.filter((item) => item.displayOverdueDays >= bill.displayOverdueDays);
        // selectedBillList = billList;
        Util.toast('逾期账单需优先还款，不可取消勾选');
      } else if (hasGradientReduceCoupon && Number(totalFeeAmt) > 0 && hasControlTag && (selectedBillList || []).length === (allBillList || []).length) {
        // 有可用的梯度减免-提前还款券，且全部可见可还借据的息费大于0，且为管控客户，且已经全选借据了，此时点击取消勾选弹窗提示
        this.setState({
          showLeadToFeeReduceModal: true,
          selectedBillIndex: billIndex
        });
      } else {
        selectedBillList.splice(billIndex, 1);
      }
    } else {
      // 选中，判断最长逾期是否选中
      const unselectedBillList = allBillList.filter((item) => selectedBillList.findIndex((selectedBill) => item.orderNo === selectedBill.orderNo) < 0);
      const unselectedMaxOverdue = Math.max(...unselectedBillList.map((item) => item.displayOverdueDays));
      if (unselectedMaxOverdue > 0 && bill.displayOverdueDays < unselectedMaxOverdue) {
        Util.toast('需优先勾选最长逾期账单或全部逾期账单');
        return;
      }
      selectedBillList.push(bill);
    }
    this.setState({ selectedBillList });
    setStore({ selectedBillList });
  }

  // 联调合约机账单处理
  unicomBillCheck(bill, selectedBillList, billIndex) {
    if (bill.belongUnicomContractOrder === 'Y' && !bill.displayOverdueDays) {
      if (billIndex > -1) {
        selectedBillList.splice(billIndex, 1);
        this.setState({ selectedBillList });
        setStore({ selectedBillList });
      } else {
        Madp.showModal({
          content: '该笔为联通合约机消费，您每月按时缴纳话费则无需还款。您也可提前还款，还款后每月为您发放等额联通电子券（可在联通APP查看和使用），确认提前还款吗？',
          cancelText: '提前还款',
          confirmText: '我再想想',
          confirmColor: themeColor,
          success: (res) => {
            if (!res.confirm) {
              selectedBillList.push(bill);
              this.setState({ selectedBillList });
              setStore({ selectedBillList });
            }
          }
        });
      }
      return true;
    }
    return false;
  }

  onSelectAll() {
    const { allBillList, selectedBillList } = this.state;
    // 排除不可还
    const notCanpayList = [];
    const unicomBills = [];
    let unicomLen = 0;
    allBillList.forEach((bill) => {
      if (bill.canPayFlag === 'N') {
        notCanpayList.push(bill);
      } else {
        const isUnicom = bill.belongUnicomContractOrder === 'Y' && !bill.displayOverdueDays;
        if (isUnicom) unicomLen += 1;
        if (selectedBillList.indexOf(bill) === -1) {
          if (isUnicom) {
            unicomBills.push(bill);
          } else {
            selectedBillList.push(bill);
          }
        }
      }
    });
    this.setState({ selectedBillList });
    if (notCanpayList.length) {
      Util.toast('全部借据中包含不支持提前还款的借据，无法全部勾选');
    } else if (unicomBills.length) {
      Madp.showModal({
        content: `您有${unicomLen}笔联通合约机消费，您每月按时缴纳话费则无需还款。您也可提前还款，还款后每月为您发放等额联通电子券，确认提前还款吗？`,
        cancelText: '提前还款',
        confirmText: '我再想想',
        confirmColor: themeColor,
        success: (res) => {
          if (!res.confirm) {
            selectedBillList.push(...unicomBills);
            this.setState({ selectedBillList });
          }
        }
      });
    }
  }

  /// +++++++++++++++++++【【【交互运营】】】 BEGIN+++++++++++++++++++++++++
  async opOnPageEvent(eventName, interactionEventCode) {
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: BillListAllLeDaId,
            custStatus: this.custStatus,
            overdueDay: this.overdueDay,
            opGetAdvanceRepayDetainInfo: this.opGetAdvanceRepayDetainInfo,
          },
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve(true);
      }
    });
  }

  // 交互式运营-获取提取提前还款挽留弹窗所需参数
  opGetAdvanceRepayDetainInfo = async () => {
    const { selectedBillList, hasControlTag } = this.state;
    const { overdueDays, accountStatus } = this.allBills || {};
    let [inteRateDiffThreshold, saveAmtThreshold] = ['', ''];
    if (getProductAllParams && typeof getProductAllParams === 'function') {
      const result = await getProductAllParams('HK.HK02');
      ({ inteRateDiffThreshold, saveAmtThreshold } = result || {});
    }
    // 勾选全部借据，进入在途借据降价流程
    const canJoinType = this.judgeCanJoinMarkingAct() || [];
    if (canJoinType.length && !hasControlTag) {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: 'launchRetainCutPrice',
      });
    }
    return {
      selectedBillList,
      accountStatus: Number(overdueDays || 0) > 0 ? '2' : accountStatus,
      inteRateDiffThreshold,
      saveAmtThreshold,
      canJoinType
    };
  }
  // +++++++++++++++++++【【【交互运营】】】 END+++++++++++++++++++++++++

  /** 特殊渠道关闭提前还款，不进行挽留逻辑 */
  /** 这个展位容易出现问题所以日志打印多了些，展位配置规则地方有两个，都得配置一下才行* */
  async deciseDoPrePay(param) {
    const selectedBillList = getStore('selectedBillList');
    const { allBillList, publicDetainDialog = {}, hasControlTag, hasGradientReduceCoupon, totalFeeAmt } = this.state;
    const { dataObj } = publicDetainDialog;
    const { amount } = param || {};
    this.totalRepayAmt = amount;

    // 选择的借据中，有逾期的话，不拦截；D
    // 只有最后一期,不拦截, 变更为最后一期也拦截
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: 'DeciseDoPrePay',
      beaconContent: { cus: { selectAll: selectedBillList.length === allBillList.length ? 'Y' : 'N', custStatus: this.custStatus } }
    });

    // 有可用的梯度减免-提前还款券，且全部可见可还借据的息费大于0，且为管控客户，且借据已全选，点击去还款直接跳转息费减免页面
    if (hasGradientReduceCoupon && Number(totalFeeAmt) > 0 && hasControlTag && selectedBillList.length === allBillList.length) {
      Util.configUrlJump(repayServiceColumnsUrls.FeeReduce);
      return;
    }

    // “允许部分提前还款的管控客户”跳过弹窗、KYC等，直接到支付页
    if (hasControlTag) {
      this.submitRepayment(param);
      return;
    }

    // 查询客户端开发参数配置
    await this.initPageConfig();

    // const needRepayBillList = selectedBillList.filter((item) => (item.surplusDays && Number(item.surplusDays) < -3));
    const cantRepayBillList = selectedBillList.filter((item) => ((Number(item.surplusInstallCnt) > 1) || (item.surplusDays && Number(item.surplusDays) > 7)));
    // canRepayBillList = canRepayBillList.filter((item) => ();
    console.log('不可提前还款账单：', cantRepayBillList);
    console.log('关闭渠道：', this.repayConfig.closePrepayment);
    console.log('当前渠道：', Madp.getChannel());
    // if (this.judgeAllSettle()) this.launchSettleAct(); // 针对全部最后一期，需要执行结清降价活动申请
    if (cantRepayBillList.length > 0 && this.repayConfig.closePrepayment && this.repayConfig.closePrepayment.indexOf(Madp.getChannel()) > -1) {
      if (!Object.keys(publicDetainDialog).length) { // 非白名单用户，或者已经展示过圣约弹窗，不拦截
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'NoLegoOrChannel' });
        this.preSubmit(param);
        console.log('无展位 可以还款');
      } else {
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ShowLimitLego' });
        console.log('有展位 限制还款');
        let paramData = {
          dataObj,
          param,
          preSubmit: this.preSubmit.bind(this),
          detainPopWindowHandler: this.detainPopWindowHandler.bind(this)
        };
        let publicDetainDialogUpdateConfig = billListAllFn.getPublicDetainDialogUpdateConfig(paramData);
        this.updateLego(publicDetainDialogUpdateConfig);
        // this.updateLego({
        //   publicDetainDialog: {
        //     isOpen: true, // 是否展示
        //     handler: (type, targetUrl, next) => {
        //       // console.log('handler: ', targetUrl, type);
        //       this.detainPopWindowHandler(type, targetUrl, next);
        //       console.log(next, 'next');
        //       console.log(dataObj.contentList && dataObj.contentList.length === 0 && type === 'none', 'dataObj.contentList && dataObj.contentList.length === 0 && type === \'none\'');
        //       if (dataObj.contentList && dataObj.contentList.length === 0 && type === 'none') {
        //         this.preSubmit(param);
        //       }
        //     }, // 按钮点击回调
        //     questionPopupKey: 'repayPage',
        //     pageId: 'repayment.BillListAll'
        //   }
        // });
      }
    } else {
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'CangoToRepay' });
      // console.log('有待还/逾期/只有一期的可以还款');
      this.preSubmit(param);
    }
  }

  /**
   *
   * @param {string} type 点击按钮的类型
   * @param {string} targetUrl 配置的跳转链接
   * @param {function} next 路由next⽅法
  */
  detainPopWindowHandler(type, targetUrl, next) {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'DetainPopWindowHandler', beaconContent: { cus: { targetUrl, type } } });
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `DetainDialog${type}` });
    switch (type) {
      case 'continue': {
        targetUrl && Madp.navigateTo({ url: targetUrl });
        break; // 点击确定按钮
      }
      case 'cancel': {
        typeof next === 'function' && next(true);
        break; // 点击确定按钮
      } // 点击取消回调
      case 'close': {
        typeof next === 'function' && next(false);
        break;
      } // 点击X 关闭弹窗的回调
      case 'none': {
        typeof next === 'function' && next(false);
        break;
      } // 没有配置内容时的回调
      default: {
        typeof next === 'function' && next(true);
        break;
      }
    }
    // 关闭弹窗，避免其他数据render造成异常弹窗
    this.updateLego({
      publicDetainDialog: {
        isOpen: false,
        handler: () => { },
      }
    });
  }

  async preSubmit(param) {
    const opRes = await this.opOnPageEvent('opRepayClick', EVENT_CODE_MAP.billListAllAdvanceClick);
    // 1、opRes返回为false表示未击中运营点：直接前置检查并跳转支付页
    // 2、opRes返回为true表示击中运营点且选择继续还款：需要先触发挽留降价建案、提交预审，再前置检查并跳转支付页
    // 3、击中运营点选择按期还款时就返回还款首页，由运营点内部处理返回
    this.submitRepayment(param);
  }

  // 还款前校验接口, 特殊接口把ret判断逻辑拿出fetch.js
  async submitRepayment(param) {
    // console.log(`submitRepayment`, param);
    // const { ret, errMsg, errCode } = await Dispatch.repayment.RepayPretreatment();
    await this.billListAllStore.getRepayPretreatment();
    const { ret, errMsg, errCode, data } = this.billListAllStore.repayPretreatment;

    if (ret === '0') {
      const { isRedirect = '0' } = this.$router.params;
      const { selectedBillList } = this.state;
      const { pendingRepayTransInfo = {} } = data || {};
      setStore({
        selectedBillList,
        pendingRepayTransInfo
      });
      this.removeClassName();
      // 需还法诉费
      const { courtCostBalance } = this.allBills;
      if (courtCostBalance && Number(courtCostBalance) > 0) {
        param.amount = Util.floatAdd(Number(param.amount), Number(courtCostBalance)).toFixed(2);
        param.courtCostBalance = Number(courtCostBalance).toFixed(2);
      }
      (`${isRedirect}` === '1' ? Util.router.replace : Util.router.push)({
        path: '/pages/express-repay/index?_windowSecureFlag=1&expressScene=13',
        query: {
          ...param,
          havePrepayFeeRightsCoupon: this.prepayFeeRightsCouponList && this.prepayFeeRightsCouponList.length > 0 ? '1' : '0'
        }
      });
    } else {
      setTimeout(() => {
        Util.showErrorMsg(errMsg);
        dispatchTrackEvent({
          target: this,
          event: EventTypes.EV,
          beaconId: `${errCode === 'UMDP00065' ? 'OtherRepaymentRunning' : 'RepayPretreatmentSystemError'}`
        });
      }, 50);
    }
  }

  /**
   * 判断能否参加降价活动(在途借据降价、降息)
   * @rule1-降价 可还借据全部勾选或勾选的借据金额大于5万
   * @rule2-降价 勾选的借据不含已参与活动降价的借据
   * @rule1-免息 可还提现借据全部勾选或勾选的提现借据金额大于5万
   * @rule3 借据是否全部到最后一期
   * @rule4 逾期不可参加
   *
   */
  judgeCanJoinMarkingAct() {
    const { selectedBillList, allBillList } = this.state;
    // 提前结清降价，全部借据勾选或提还金额大于5W
    const canJoinType = [];
    if ((selectedBillList.length === allBillList.length) || (Number(this.totalRepayAmt || '0') > 50000)) {
      canJoinType.push('allClear'); // rule1
    }
    // 提前结清免息，全部利率借据勾选或提还利率借据金额大于5W
    const allInteBillList = (allBillList || []).filter((item = {}) => item.loanType === 'I');
    const selectedInteBillList = (selectedBillList || []).filter((item = {}) => item.loanType === 'I');
    const totalInteRepayAmt = selectedInteBillList.reduce((prev, item = {}) => Util.floatAdd(prev, item.surplusPayTotalAmt || item.shouldReturnAmt || 0), 0);
    if ((allInteBillList.length && allInteBillList.every((item = {}) => (selectedBillList || []).includes(item))) || (Number(totalInteRepayAmt || '0') > 50000)) {
      canJoinType.push('inteClear');
    }
    let hasUnDueBill = false;
    let hasOverDueBill = false;
    selectedBillList.forEach((bill) => {
      if (canJoinType.indexOf('allClear') > -1 && bill.hasAdjustExePrice === 'Y') { // rule2
        canJoinType.splice(canJoinType.indexOf('allClear'), 1);
      }
      if ((bill.installTotalCnt !== bill.installCnt) || Number(bill.surplusPayUndueAmt) > 0) { // rule3
        hasUnDueBill = true;
      }
      if (bill.overdueDays > 0) { // rule4
        hasOverDueBill = true;
      }
    });
    console.log('judgeCanJoinMarkingAct', canJoinType, hasUnDueBill, hasOverDueBill);
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: 'JudgeCanJoinMarkingAct',
      beaconContent: {
        cus: {
          selectAll: selectedBillList.length === allBillList.length ? 'Y' : 'N',
          totalRepayAmt: this.totalRepayAmt,
          hasUnDueBill,
          hasOverDueBill,
          canJoinMarkingAct: canJoinType,
        }
      }
    });
    return (hasUnDueBill && !hasOverDueBill) ? canJoinType : [];
  }

  get getExtraParam() {
    const { hasOverDueBill } = this.state;
    if (Object.keys(this.chatEntryParam).length) {
      return this.chatEntryParam;
    } else {
      return {
        hasBill: !!((this.allBills && this.allBills.advanceBillList && this.allBills.advanceBillList.length)
          || (this.allBills && this.allBills.showBillList && this.allBills.showBillList.length)),
        overdue: hasOverDueBill,
        dueTagCust: !!(this.allBills && this.allBills.isDueTagCust),
        overSeven: false,
        needLogin: 1
      };
    }
  }

  // 处理问卷drawer组件引入的body的class未清除导致页面无法滚动的问题
  removeClassName = () => {
    if (document && document.body && document.body.classList && document.body.classList.remove) {
      document.body.classList.remove('mu-drawer__hidden');
    }
  }

  onLeadToFeeReduceModalCancel = () => {
    const { selectedBillList, selectedBillIndex } = this.state;
    selectedBillList.splice(selectedBillIndex, 1);
    this.setState({
      selectedBillList,
      showLeadToFeeReduceModal: false
    });
    setStore({ selectedBillList });
  }

  get bubbleInfo() {
    const {
      selectedBillList,
      allBillList,
      hasGradientReduceCoupon,
      totalFeeAmt,
      hasControlTag,
    } = this.state;
    let bubbleInfo = {};
    let payPrepayFee = 0;
    let allCanPartPay = true;
    (selectedBillList || []).forEach((bill) => {
      payPrepayFee += Number(bill.payPrepayFee || '0.00');
      if (bill.canPartPayFlag !== 'Y') allCanPartPay = false;
    });
    if (hasGradientReduceCoupon && Number(totalFeeAmt) > 0 && hasControlTag && (selectedBillList || []).length === (allBillList || []).length) {
      bubbleInfo = {
        type: 'feeReduce',
        title: 'HK02.HK02WA002',
      };
    } else if (this.prepayFeeRightsCouponList && this.prepayFeeRightsCouponList.length > 0 && payPrepayFee > 0) {
      bubbleInfo = {
        type: 'prePayFee',
        title: 'HK02.HK02WA003',
      };
    } else if (hasControlTag || ((selectedBillList || []).length && allCanPartPay)) {
      bubbleInfo = {
        type: 'partRepay',
        title: 'HK02.HK02WA004',
      };
    }
    if (bubbleInfo && bubbleInfo.type && bubbleInfo.title) {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: 'SubmitBubbleInfo',
        beaconContent: { cus: { type: bubbleInfo.type, title: bubbleInfo.title, custStatus: this.custStatus } }
      });
    }
    return bubbleInfo;
  }

  get surplusPayTotalPrincipalAmt() {
    const { readOnlyBillList } = this.state;
    const { surplusPayTotalPrincipalAmt } = this.allBills;
    if (readOnlyBillList && readOnlyBillList.length) {
      return (readOnlyBillList.reduce((sum, cur) => Number(sum) + Number(cur.surplusPayPrincipalAmt), surplusPayTotalPrincipalAmt)).toFixed(2);
    } else {
      return surplusPayTotalPrincipalAmt;
    }
  }

  render() {
    const {
      selectedBillList,
      allBillList,
      readOnlyBillList,
      unicomBillList,
      showLeadToFeeReduceModal,
      hasControlTag,
    } = this.state;

    const businessPluginFooterData = {
      stateData: this.state,
      chatEntryParam: this.chatEntryParam,
      allBills: this.allBills,
      themeColor: themeColor
    };

    const { overdueDays, courtCostBalance } = this.allBills;

    return this.showPage ? (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="全部账单"
          leftArea={[
            {
              type: 'icon',
              value: 'back',
              onClick: this.beforeMiniRouteLeave
            }
          ]}
        />
        <MUView className="pages-bg reapy-all">
          {((allBillList && allBillList.length > 0) || (readOnlyBillList && readOnlyBillList.length > 0)) ? (<MUView className="header">
            <MUView className="header-text">剩余待还本金(元)</MUView>
            <MUView className="header-amount">{this.surplusPayTotalPrincipalAmt}</MUView>
          </MUView>) : null}
          <MUView className="all-list-content-new" style={`min-height: ${ChannelConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
            {((allBillList && allBillList.length > 0) || (readOnlyBillList && readOnlyBillList.length > 0)) ? (
              <BillListNew
                billType="total"
                billList={allBillList}
                unicomBillList={unicomBillList}
                selectedBillList={selectedBillList}
                readOnlyBillList={readOnlyBillList}
                onSelect={this.handleSelect}
                submitRepayment={(param) => this.deciseDoPrePay(param)}
                canAllSelect={false}
                canSelectLength={allBillList.filter((bill) => bill.canPayFlag === 'Y').length}
                bubbleInfo={this.bubbleInfo}
                hideInterestWaiveBubble={Number(overdueDays || 0) > 0 || hasControlTag}
                btnContext={Number(overdueDays || 0) > 0 ? '立即还款' : ''}
                custStatus={this.custStatus}
                courtCostBalance={courtCostBalance}
              />
            ) : <EmptySign />}
          </MUView>
          <BusinessPlugin moduleName="repayment" pageType="billListAll" type="middle" />
          <BusinessPlugin
            moduleName="repayment"
            pageType="billListAll"
            type="footer"
            data={businessPluginFooterData}
          />

          {/* 取消借据勾选引导息费减免弹窗 */}
          <LeadToFeeReduceModal
            isOpened={showLeadToFeeReduceModal}
            themeColor={themeColor}
            onConfirm={() => {
              this.setState({
                showLeadToFeeReduceModal: false
              }, () => Util.configUrlJump(repayServiceColumnsUrls.FeeReduce));
            }}
            onCancel={this.onLeadToFeeReduceModalCancel}
          />
          <OpRepayment pageId={BillListAllLeDaId} opEventKey="opRepayClick" />
          <OpRepayment pageId={BillListAllLeDaId} opEventKey="opPageStay" />
          <OpRepayment pageId={BillListAllLeDaId} opEventKey="opPageLeave" />
        </MUView>
      </MUView>
    ) : null;
  }
}
