@import '../../components/weapp/index.scss';

.repayment-index {
  position: relative;

  &-header {
    background: #fff;
  }

  &-list.at-list {
    background-color: transparent;
    border-top: 1PX solid #F3F3F3;

    .at-list__item {
      padding: 20px 30px;
      font-size: 28px;
      line-height: 32px;
      color: #333;
      background: #FFF;
      .item-extra__info {
        font-size: 28px;
        line-height: 32px;
      }
    }

    &-futurePlan-listItem {
      background: #FFF;
      .item-content__info-title {
        overflow: visible;
      }
    }
  }

  .repay-entrance {
    .item-content {
      width: 300px !important;
    }
  }

  &-content {
    width: 100%;
    min-height: 100vh; // 很关键

    &-padding-chatentry {
      padding-bottom: 196px;
      background-color: #f3f3f3;
    }
  }

  &-content--safe {
    min-height: calc(100vh - constant(safe-area-inset-bottom)); // 很关键
    min-height: calc(100vh - env(safe-area-inset-bottom)); // 很关键
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

  // 客服组件样式
  .chat-entry {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: -136px;
  }

  @supports (padding-bottom: env(safe-area-inset-bottom)) {
    .chat-entry {
      margin-top: -env(safe-area-inset-bottom);
    }
  }

  @supports (padding-bottom: constant(safe-area-inset-bottom)) {
    .chat-entry {
      margin-top: -constant(safe-area-inset-bottom);
    }
  }

  .gbl-basic-lifefollow {
    .app-wrap > div {
      margin-top: 20px;
      background: #F3F3F3;
    }
  }

  .repayment-future-plan {

    &-listItem {
      background: #FFF;

      .at-list__item .item-content__info-title {
        overflow: visible;
      }
    }
  }

  .transfer-cust {
    &__content {
      margin-bottom: 20px;
      padding: 60px 50px 40px;
      background-color: #fff;
      text-align: center;
      .content__title {
        font-size: 32px;
        line-height: 32px;
        color: #808080;
        font-weight: 400;
      }
      .content__amount {
        padding: 30px 0 20px;
        font-size: 80px;
        line-height: 80px;
        color: #000000;
        opacity: 0.3;
        font-weight: 600;
      }
      .content__explain {
        font-size: 24px;
        line-height: 36px;
        color: #808080;
        font-weight: 400;
        &--orange {
          color: #FF8844;
        }
      }
      .content__description {
        margin-top: 30px;
        margin-left: 30px;
        margin-right: 30px;
        height: 80px;
        border-radius: 10px;
        background: #f3f3f3;
        align-items: center;
        text-align: center;
        font-size: 24px;
        align-content: center;
        &--orange {
          color: #FF8844;
        }
      }
    }
    &__guide-trade {
      padding: 14px 30px 14px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #fff;
      .guide-trade__left {
        display: flex;
        align-items: center;
        &__img {
          width: 72px;
          height: 72px;
          image, .taro-img {
            width: 100%;
            height: 100%;
          }
        }
        &__text {
          margin-left: 16px;
          font-size: 32px;
          line-height: 32px;
          color: #333;
          font-weight: 400;
        }
      }
      // --兼容微信小程序
      >mu-view:nth-child(2) {
        flex: 1;
      }
      .guide-trade__right {
        flex: 1;
        font-size: 0;
        text-align: right;
      }
    }
  }

  .lui__banner-without-border {
    &__swiper {
      height: 140px !important;
    }
  }
}

.special-right {
  margin-top: 20px;
}

.alipay-life-follow-node__default {
  margin-top: 20px;
}

.life-self-top {
  height: 0 !important;
  overflow: hidden;
}

.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}
