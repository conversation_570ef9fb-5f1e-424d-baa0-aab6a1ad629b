.over-due-effect {
  position: relative;
  &__bg {
    width: 100%;
    height: 720px;
    font-size: 0;
    .taro-img, image {
      width: 100%;
      height: 100%;
    }
  }

  &__content {
    position: absolute;
    left: 0;
    top: 0;
    overflow: hidden;
    &__title {
      padding: 40px 40px 20px 40px;
      font-size: 36px;
      line-height: 48px;
      color: #333;
      font-weight: 600;
    }
    &__desc {
      padding: 0 40px;
      font-size: 26px;
      line-height: 38px;
      color: #333;
    }
    &__explain {
      margin: 20px 30px 0 30px;
      padding: 50px 40px 0 40px;
      height: calc(100vh - 322px);
      border-radius: 16px;
      background: #fff;
      overflow: hidden;
      .explain-item {
        margin-bottom: 36px;
        display: flex;
        &__picture {
          width: 48px;
          height: 48px;
          font-size: 0;
          .taro-img, image {
            width: 100%;
            height: 100%;
          }
        }
        &__text {
          flex: 1;
          margin-left: 32px;
          font-size: 26px;
          line-height: 36px;
          color: #808080;
          font-weight: 400;
          &__title {
            color: #3477FF;
            &--special {
              color: #CC1F15;
            }
          }
          &__desc {
            font-size: 24px;
          }
        }
      }
      .explain-guide {
        margin-top: 64px;
        display: flex;
        justify-content: center;
        &__main {
          width: 360px;
          height: 100px;
          font-size: 36px;
          line-height: 100px;
          font-weight: 600;
          border-radius: 50px;
          &--special {
            background: #CC1F15 !important;
            border-color: #CC1F15;
          }
        }
      }
    }
  }
}
