/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import pageHoc from '@utils/pageHoc';
import {
  MUView, MUImage, MUButton
} from '@mu/zui';
import Madp from '@mu/madp';
import CustomConfig from '@config/index';
import Util from '@utils/maxin-util';
import { Url } from '@mu/madp-utils';

import './over-due-effect.scss';

const highSpeedRail = 'https://file.mucfc.com/ebn/3/0/202404/20240425205237065c33.png';
const blueOverDueEffectImg = 'https://file.mucfc.com/ebn/3/0/202404/2024042520523787c88f.png';
const redOverDueEffectImg = 'https://file.mucfc.com/ebn/3/0/202404/202404252053023d90d7.png';
const school = 'https://file.mucfc.com/ebn/3/0/202404/20240425205302b72728.png';
const swipe = 'https://file.mucfc.com/ebn/3/0/202404/20240425205302374b17.png';
const wallet = 'https://file.mucfc.com/ebn/3/0/202404/202404252053023a8656.png';
const themeColor = Util.getThemeColor(CustomConfig.theme);
const explainItems = [
  {
    itemPicture: highSpeedRail,
    itemTitle: '限制出行',
    itemDesc: '限制乘坐列车软卧、G 字头动车组列车全部座位、其他动车组列车一等以上座位、民航飞机等非生活和工作必需的消费行为。'
  },
  {
    itemPicture: swipe,
    itemTitle: '限制出入高消费场所',
    itemDesc: '限制住宿星级以上宾馆饭店、国家一级以上酒店及其他高消费住宿场所；限制其在夜总会、高尔夫球场等高消费场所消费。'
  },
  {
    itemPicture: school,
    itemTitle: '限制子女就读高收费学校',
    itemDesc: '限制失信执行人以其财产支付子女入学就读高收费私立学校。'
  },
  {
    itemPicture: wallet,
    itemTitle: '限制购买具有现金价值保险',
    itemDesc: '限制支付高额保费购买具有现金价值的保险产品。'
  }
];


@track({ event: EventTypes.PO }, {
  pageId: 'OverDueEffect',
  dispatchOnMount: true,
})
@pageHoc({ title: '逾期影响' })
export default class OverDueEffect extends Component {
  constructor(props) {
    super(props);
    this.overDueFlag = Url.getParam('overDue') === '1';
    this.toRepayIndexFlag = Url.getParam('toRepayIndex') === '1';
  }

  componentDidMount() {
    dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'EnterPage', beaconContent: { cus: { overDueFlag: this.overDueFlag } } });
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '逾期影响' });
  }

  config = {
    navigationBarTitleText: '逾期影响'
  }

  getOverDueEffectImg = () => {
    let overDueEffectImg = redOverDueEffectImg;
    if (!this.overDueFlag) {
      if (themeColor !== '#E60027') {
        overDueEffectImg = blueOverDueEffectImg;
      }
    }
    return overDueEffectImg;
  }

  toRepay = () => {
    if (this.toRepayIndexFlag) {
      Madp.redirectTo({
        url: '/pages/index/index',
      });
      return;
    }
    Madp.navigateTo({
      url: '/pages/express-repay/index?expressScene=15',
    });
  }

  render() {
    return (
      <MUView className="pages-bg">
        <MUView style={`min-height: ${CustomConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
          <MUView className="over-due-effect">
            <MUView className="over-due-effect__bg">
              <MUImage src={this.getOverDueEffectImg()} />
            </MUView>
            <MUView className="over-due-effect__content">
              <MUView className="over-due-effect__content__title">
                逾期将上报人行征信，带来以下影响
              </MUView>
              <MUView className="over-due-effect__content__desc">
                在《关于加快推进失信被执行人信用监督、警示和惩戒机制建设意见》中加强对失信被执行人及相关人员的联合惩戒措施。
              </MUView>
              <MUView className="over-due-effect__content__explain">
                {explainItems.map((item, i) => (
                  <MUView className="explain-item">
                    <MUView className="explain-item__picture">
                      <MUImage src={item.itemPicture} />
                    </MUView>
                    <MUView className="explain-item__text">
                      <MUView className={`explain-item__text__title${this.overDueFlag ? ' explain-item__text__title--special' : ''}`}>
                        {`${i + 1}. ${item.itemTitle}`}
                      </MUView>
                      <MUView className="explain-item__text__desc">
                        {item.itemDesc}
                      </MUView>
                    </MUView>
                  </MUView>
                ))}
                <MUView className="explain-guide">
                  <MUButton
                    className={`mu-button--list explain-guide__main${this.overDueFlag ? ' explain-guide__main--special' : ''}`}
                    type="primary"
                    circle
                    beaconId="ToRepay"
                    onClick={this.toRepay}
                  >去还款</MUButton>
                </MUView>
              </MUView>
            </MUView>
          </MUView>
        </MUView>
      </MUView>
    );
  }
}
