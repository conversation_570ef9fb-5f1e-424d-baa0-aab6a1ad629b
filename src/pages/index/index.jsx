/* eslint-disable no-unused-expressions */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-empty */
/* eslint-disable indent */
/* eslint-disable react/sort-comp */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUList, MUListItem, MUNavBarWeapp, MUText, MUImage, MUIcon, MUModal,
} from '@mu/zui';
import Madp from '@mu/madp';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import { urlDomain } from '@utils/url_config.js';
import {
  isMiniProgram, transformNewCouponAPIRes, filterWaiveCoupons, filterQualCoupons, prepareRepayDetailList,
} from '@utils/repay-util';
import {
  repayStatusType, repayServiceColumnsUrls, repayGuideSceneInfo, clipboardUrl, EVENT_CODE_MAP
} from '@utils/constants';
import { ChatEntry } from '@mu/chat-entry-component';
import RepayStatusCardNew from '@components/repay-status-card-new/index';
import ChannelConfig from '@config/index';
import { getStore, setStore } from '@api/store';
import { injectState, refresh } from '@mu/leda';
import { RepaymentPlugin as BusinessPlugin } from '@mu/business-plugin';
import { repaymentFn } from '@mu/business-plugin-utils';
import pageHoc from '@utils/pageHoc';
import { inject } from '@tarojs/mobx';
import RepaymentGlobalStore from '@repayment/store/rootStore';
import BusinessColumns from '@components/business-columns/index';
import { AgreementDrawer } from '@mu/agreement';
import { requestSubscribeMessage, stayWithTime, opService, getPageConf, getProductAllParams, getLoginInfo } from '@mu/business-basic';
import {
  templateSubscribe, getAppId, getTemplateType, getSubscribedIdList,
  FilterContentList, mnp, getSetting, hasUnrefused
} from '@utils/subscribe';
import {
  Url, isIOS, isMuapp, getWebViewName, isWechat, getEnv
} from '@mu/madp-utils';
import { platLogin } from '@utils/login';
import tradeRecordsGuide from '@components/assets/img/trade-records-guide.png';
import WaitPayPlan from '@components/wait-pay-plan/index';
import RepayGuide from '@components/repay-guide/index';
import AutoRepayDrawer from '@components/auto-repay-drawer/index';
import { OpRepayment } from '@mu/op-comp';
import { getAvailLoanFlag } from '@utils/getLoanMoreTips';
import DiversionInfoCard from '@components/diversion-info-card/index';
import './index.scss';
if (['tt'].includes(process.env.TARO_ENV)) {
  require('./index-tt.scss');
}
if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/repay-bubble/index.scss');
  // 引用一次
  require('@components/business-columns/index.scss');
  require('@components/wait-pay-plan/index.scss');
  require('@components/bottom-drawer/index.scss');
  require('@components/repay-status-card-new/index.scss');
  require('@components/repay-guide/index.scss');
  require('@components/auto-repay-drawer/index.scss');
  require('@components/diversion-info-card/index.scss');
}
const FuturePlan = 'https://file.mucfc.com/ebn/3/0/202404/20240425205237f9ecb8.png';
const rechargeImg = 'https://file.mucfc.com/ebn/3/0/202404/20240425205302f3ee75.png';
const redStatusImg = 'https://file.mucfc.com/ebn/3/0/202404/20240425205302ad50ce.png';
const { pageIndexFn } = repaymentFn || {};
const ledaNameList = pageIndexFn.getLedaNameList();
const RepaymentIndexPageId = 'e4a44f58-3b1d-40f3-8992-1455b9afcc81';
const themeColor = Util.getThemeColor(ChannelConfig.theme);

// 处理部分ios机型返回页面自动返回的问题
if (process.env.TARO_ENV === 'h5') {
  window.onpageshow = (event) => {
    if (
      event.persisted || (window.performance && (window.performance.navigation.type === 2 || window.performance.navigation.type === '2'))
    ) {
      setTimeout(() => {
        Madp.setStorageSync('repayJumpOut', '', 'SESSION');
      }, 50);
    }
  };
}

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: pageIndexFn.getPageId() || RepaymentIndexPageId
    }
  }
}, {
  pageId: 'RepaymentIndex',
  dispatchOnMount: true,
})
// 放在track后面，不然track好像会被覆盖，报页面id未配置
@pageHoc({ title: '还款' })
@inject(() => ({ repaymentGlobalStore: new RepaymentGlobalStore() }))
@injectState({
  pageId() {
    return pageIndexFn.getPageId() || RepaymentIndexPageId;
  },
  getPageConf: () => getPageConf(RepaymentIndexPageId, true),
  stateKeys: [
    ...ledaNameList
  ]
})
export default class RepaymentIndex extends Component {
  config = {
    navigationBarTitleText: '还款',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  };
  constructor(props) {
    super(props);
    this.nearBills = {}; // 7 天待还接口返回的数据
    this.state = {
      totalAmount: 0, // 七天可还账单金额
      courtCostBalance: '0.00', // 法诉费金额
      repayStatus: repayStatusType.loading, // 默认展示loading
      futurePlanList: [], // 剩余每月待还模块
      showChat: false, // 加载客服入口
      deductStatus: {}, // 代扣状态
      isVPlus: false, // 是否V+高价值客户
      transferStatus: false, // 客户转让状态
      availRepayWaiveFlag: false, // 还款可减免标志
      repayGuideInfo: {}, // 还款指南信息
      showAutoRepayDrawer: false,
      contractInfo: {},
      unSubscribedContentList: [], // 未订阅的消息模版
      defaultAvailShowButtons: false, // 按钮展示相关信息判断完成再渲染展示
      showUnicomBillTip: false, // 联通账单提示
      pageAvailShowButtons: false, // 展位加载完成展示按钮
      showCovenantBubble: false, // 是否展示圣约随机立减气泡
    };
    this.accountInfoList = []; // 账户信息列表
    this.awardInfoList = []; // 账户推荐券信息列表
    this.diversionBillInfo = {}; // 导流账单信息
    this.isStagable = false; // 是否显示账单分期入口
    this.advancedBillCounts = 0; // 全部待还账单数
    this.repayConfig = {};
    this.repayPlanResumeDate = ''; // 剩余每月待还模块中，用于续传的日期
    this.statusBillList = []; // 状态卡片中涉及的list
    this.statusBeaconPrefix = {};
    this.statusBeaconPrefix[repayStatusType.empty] = 'Empty'; // 暂无待还
    this.statusBeaconPrefix[repayStatusType.inSeven] = 'InSeven'; // 7天内
    this.statusBeaconPrefix[repayStatusType.today] = 'InSeven'; // 当天
    this.statusBeaconPrefix[repayStatusType.inSevenMutil] = 'InSeven'; // 7天外
    this.statusBeaconPrefix[repayStatusType.outSeven] = 'OutSeven';
    this.statusBeaconPrefix[repayStatusType.overDue] = 'OverDue'; // 已逾期未打标
    this.statusBeaconPrefix[repayStatusType.dueTagCust] = 'DueTag'; // 已打标
    this.statusBeaconPrefix[repayStatusType.clear] = 'Clear'; // 本期已还清
    this.isPageDataReady = false;
    this.isLedaDataReady = false;
    this.indexStore = props.repaymentGlobalStore.indexStore;
    this.businessColumns = []; // 还款服务栏位数据
    this.showPublicDetainDialog = false; // 四合一弹窗是否展示
    this.randomReduceText = ''; // 随机立减营销文案
    this.waivePayAmt = '0'; // 挂起减免总金额
    this.havePrepayFeeRightsCoupon = false; // 标志是否有免提前还违约金资格券（用于判断还款服务角标展示）
    this.transferInfo = {}; // 转让信息
    this.availLoanFlag = false; // 可借标志
    this.availLoanWaiveFlag = false; // 借款优惠标志，洼地、特价、价款劵
    this.showInteDate = ''; // 展示宽限日期，多宽限日期取最小的
    this.recommendService = {}; // 推荐服务
    this.indDeductionFlag = ''; // 独立代扣或0：100账单
    this.buttonShowTimer = null;
    this.unicomBillTipText = ''; // 联通账单提示文案
    this.finishedInitLedaFlag = false; // leda数据处理完成
    this.finishedInitDeductFlag = false; // 代扣信息初始化完成
    this.allAwardDetailList = []; // 所有券信息
    this.appExclusiveCouponDetailList = null; // APP专用券列表
    this.availableCouponDetailList = []; // 可用优惠券列表
    this.usableQualConponList = []; // 可用资格券列表
    this.stayTimer = null; // 逾期交互营销-停留
    this.hasTriggerOpDetainDialog = false; // 标识还款交互运营退出挽留弹窗是否触发
    this.repayServiceQualifyList = []; // 交互式运营-还款服务资格列表
    this.repaySence = ''; // 标识还款场景
    this.signedAlipayWithholding = ''; // 当前渠道是否支持签约支付宝代扣，Y/N
    this.showFlowCardSwitch = ''; // 导流借据开放渠道开关，Y/N
    this.showDiversionInfoCard = false; // 展示导流卡片
    this.userId = ''; // 用户id
  }

  async componentDidMount() {
    // 多接口用到用户id，存储在this中
    const { basicCustDto } = getStore('sessionInfo') || {};
    let { userId } = basicCustDto || {};
    if (!userId) {
      const { basicCustDto: newBasicCustDto } = await Dispatch.repayment.getNewUserInfo();
      const { userId: newUserId } = newBasicCustDto || {};
      userId = newUserId || '';
    }
    this.userId = userId;
    // 支付宝小程序多接口401报错会告警
    if (isMiniProgram()) {
      const { loginSuccess } = await getLoginInfo() || {};
      if (!loginSuccess) {
        platLogin();
        return;
      }
    }
    if (Madp.getChannel() === '3CMBAPP') {
      // 针对招行渠道清除支付页的金额和优惠券缓存等信息
      setStore({
        externalFillObj: {}
      });
    }
    const expressScene = Url.getParam('expressScene') || '';
    const awardNo = Url.getParam('awardId') || '';
    // 3WYDAPP指定跳转链接
    if (Madp.getChannel() === '3WYDAPP') {
      const assignedRedirectPage = Url.getParam('redirectUrl') || '';
      setStore({ assignedRedirectPage });
    }
    // 25场景108券入口 26给缓催日历场景使用
    if (expressScene && ['21', '22', '23', '24', '25','26', '41', '42', '43'].includes(expressScene)) {
      Util.router.replace({
        path: '/pages/express-repay/index',
        query: {
          expressScene,
          awardNo,
        }
      });
      return;
    }
    //  转让客户特殊处理 获取用户账户信息。1、无额度跳转申请；2、展示溢缴款数据；3、获取账单分期所需额度信息
    const [
      transferStatus,
      ,
      ,
      ,
      { data },
    ] = await Promise.all([
      this.getAccount(),
      this.initPageConfig(),
      this.getNearBillsAndAmount(),
      this.getCouponList(),
      Dispatch.repayment.getChannelParams({
        channelParamKeyList: ['signedAlipayWithholding', 'showFlowCardSwitch'],
        paramTypeList: ['CHANNEL_PARAM']
      }),
    ]);
    this.signedAlipayWithholding = ((data || {}).channelParamMap || {}).signedAlipayWithholding || '';
    // 有导流借据且渠道开放导流业务，进入导流还款首页
    this.showFlowCardSwitch = ((data || {}).channelParamMap || {}).showFlowCardSwitch || '';
    if ((this.diversionBillInfo.diversionBillFlag === '1'
      || this.diversionBillInfo.diversionBillFlag === '2'
      || this.diversionBillInfo.diversionBillFlag === '3'
      || this.diversionBillInfo.diversionBillFlag === '4')
      && this.showFlowCardSwitch === 'ALL_OPEN'
    ) {
      Madp.redirectTo({
        url: `${urlDomain}/${Madp.getChannel()}/ldf-repayment/#/pages/index/index?needLogin=1`,
      });
      return;
    }
    // 有导流借据到期金额且渠道开放导流业务，进入导流还款首页
    this.showDiversionInfoCard = this.showFlowCardSwitch === 'PART_OPEN' && (Number(this.diversionBillInfo.diversionDueBillAmt || 0) > 0);
    if (transferStatus) {
      dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'TransferCust' });
      return;
    }
    // 获取首页状态
    const topCardStatus = this.getTopCardStatus();
    dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'EnterPage', beaconContent: { cus: { custStatus: this.getCustStatus, showRepayPlan: Url.getParam('showRepayPlan') === '1' } } });
    // 上送首页加载时常
    this.uploadOnLoadTime();
    // 顶部代扣提示、支付代扣信息
    this.getDeductStatus(topCardStatus);
    // V+标签展示，逾期打标或真逾期不查询
    if (topCardStatus === repayStatusType.overDue) {
      this.queryAwardAccount();
    }

    this.isPageDataReady = true;
    if (this.isLedaDataReady && !this.finishedInitLedaFlag) {
      this.initLedaData();
    }
    // 兜底处理展位没加载的场景
    setTimeout(() => {
      if (this.isPageDataReady && this.isLedaDataReady && !this.finishedInitLedaFlag) {
        this.initLedaData();
      }
    }, 2000);

    // 待还计划展示
    const { negotiateRepayTaskBill } = this.indexStore;
    if (topCardStatus !== repayStatusType.dueTagCust && !(negotiateRepayTaskBill || {}).showNegotiatePlan) {
      this.getFuturePlans();
    }
    this.buttonShowTimer = setTimeout(() => {
      if (!this.availShowButtons) {
        this.setState({ defaultAvailShowButtons: true });
      }
    }, 2000);
    // 存储客服拓展参数，后续页面需要使用
    this.setExtraParam(true);

    // 还款用券引导
    if (!(negotiateRepayTaskBill || {}).showNegotiatePlan) {
      await this.getWaiveCouponList(topCardStatus);
    }

    // 解决支付宝小程序数据更新问题
    setTimeout(() => {
      this.initRepaySecene();
      this.handleOpPageEnterEvent();
      this.handleOpPageStayEvent();
    }, 10);
    if (process.env.TARO_ENV === 'h5' && Url.getParam('needBack') !== '1' && getWebViewName() !== 'ks') {
      Util.pushUrlState('keepState');
    }
  }

  // 判断提前还、正常还、逾期还
  initRepaySecene = () => {
    const { overDueTagFlag, nearBillsTotalAmount } = getStore('nearBills');
    if (Number(nearBillsTotalAmount || 0) > 0 && (Number(this.overdueDay || 0) > 0 || overDueTagFlag)) {
      this.repaySence = 'overdueRepay';
    } else if (this.getCustStatus === 'InSeven') {
      this.repaySence = 'normalRepay';
    } else if (this.getCustStatus === 'OutSeven') {
      this.repaySence = 'advanceRepay';
    }
  }

  // 交互运营-进入事件
  handleOpPageEnterEvent = async () => {
    const eventCodeMap = {
      overdueRepay: EVENT_CODE_MAP.repayIndexOverDueOpen,
      normalRepay: EVENT_CODE_MAP.repayIndexOpen,
      advanceRepay: EVENT_CODE_MAP.repayIndexAdvanceOpen,
    };
    const isOpHappen = await this.opOnPageEvent('opPageEnter', eventCodeMap[this.repaySence]);
    if (!isOpHappen) { // 如果没有交互运营弹窗
      this.opActivityCouponDialogCallback('hideDialog');
    }
  }

  // 交互运营-停留事件
  handleOpPageStayEvent = () => {
    this.stayTimer = null;
    const { stayTime } = this.repayConfig || {};
    // 增加标识，还款服务弹窗触发了就不在触发还款停留事件
    if (!this.hasTriggerOpDetainDialog && this.repaySence === 'overdueRepay' && !this.stayTimer && Number(stayTime || 0) > 0 && (typeof stayWithTime === 'function')) {
      this.stayTimer = stayWithTime(Number(stayTime) * 1000, async () => {
        if (this.stayTimer) {
          await this.opOnPageEvent('opPageStay', EVENT_CODE_MAP.repayIndexOverDueStay);
        }
      });
    }
  }

  async opOnPageEvent(eventName, interactionEventCode) {
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: RepaymentIndexPageId,
            custStatus: this.getCustStatus,
            overdueDay: this.overdueDay,
            opCheckCovenantRandomReduce: () => this.opCheckCovenantRandomReduce(),
            opGetCovenantCouponAwardAmount: () => this.opGetCovenantCouponAwardAmount(),
            opCheckAdvancedStageQualify: () => this.opCheckAdvancedStageQualify(),
            opGetCovenantDownloadParam: () => this.opGetCovenantDownloadParam(),
            opGetActivityCouponDialogInfo: () => this.opGetActivityCouponDialogInfo(),
            onCheckAndGetStayInfo: () => this.onCheckAndGetStayInfo(),
            opGetNegotiateRepayWaiveRatio: () => this.opGetNegotiateRepayWaiveRatio(),
            opHandleRepayServiceJump: (type) => this.opHandleRepayServiceJump(type),
            opGetOverDueRepayServiceData: (type) => this.opGetOverDueRepayServiceData(type),
          },
          callback: (res) => {
            console.log('交互式运营-页面事件 callback:::::::::', res, eventName, interactionEventCode);
            res && res.bubble && this.opSetRepayBubble(res.bubble);
            resolve(res);
          }
        });
      } catch (error) {
        resolve(true);
      }
    });
  }

  opCheckCovenantRandomReduce() {
    return getStore('repayStatus') === repayStatusType.inSeven;
  }

  // 交互式运营-获取0APP专署还款券最大可抵扣额
  opGetCovenantCouponAwardAmount() {
    if (!this.appExclusiveCouponDetailList || this.appExclusiveCouponDetailList.length === 0) return null;
    let max = 0.0;
    this.appExclusiveCouponDetailList.forEach(couponItem => {
      const { awardUseCondition = {}, awardType, awardAmt = 0.0 } = couponItem;
      const { transTerminalCondition, transTerminal = '' } = awardUseCondition;
      if (transTerminalCondition === 'in' && transTerminal.indexOf('0APP') >= 0) { // 0APP专署券
        if (awardType === '108' && Number(awardAmt) > max) {
          max = Number(awardAmt);
        }
      }
    });
    return max.toFixed(2);
  }

  // 交互式运营-设置还款气泡
  opSetRepayBubble(text) {
    if (!text) return;
    this.randomReduceText = text;
    this.setState({
      showCovenantBubble: true,
    });
    dispatchTrackEvent({ event: EventTypes.SO, beaconId: 'repayment.repayIndex.RepaymentMarketingCovenantBubble.Show' });
  }

  // 交互式运营-再分期准入资格检查
  opCheckAdvancedStageQualify() {
    return (this.repayServiceQualifyList || []).includes('BillAdvancedStage');
  }

  // 交互式运营-获取圣约落地页所需参数
  opGetCovenantDownloadParam() {
    const { totalAmount, courtCostBalance } = this.state;
    const { repayBillList } = getStore('nearBills');
    return {
      _windowSecureFlag: 1,
      amount: Number(totalAmount).toFixed(2),
      stages: this.statusBillList.length,
      billType: '7days',
      // ...this.getBenefitCardRoute(),
      ...(Number(courtCostBalance) > 0 ? { courtCostBalance } : {}), // 透传法诉费
      expressScene: '11',
      currentBillClear: (repayBillList || []).filter((item = {}) => item.belongUnicomContractOrder !== 'Y').length === (this.statusBillList || []).filter((item = {}) => item.belongUnicomContractOrder !== 'Y').length ? 'Y' : '',
    };
  }

  // 交互式运营-获取A3活动领券弹窗信息
  opGetActivityCouponDialogInfo() {
    const { activityCouponDialog = {} } = this.state;
    const { contentList = [] } = activityCouponDialog.dataObj || {};
    return contentList && contentList[0] || {};
  }

  // 交互式运营-逾期客户还款停留准入检查
  async onCheckAndGetStayInfo() {
    // 进入即代表停留超过配置时间
    let stayInfo = {};
    const usableQualConponList = await this.getQualCouponList();
    const { availRepayWaiveFlag, repayStatus } = this.state;
    const { negotiateRepayTaskBill, nearBills } = this.indexStore;
    if (!(usableQualConponList.filter((specialItem) => (specialItem.awardType === '216')).length || availRepayWaiveFlag) && Object.keys(negotiateRepayTaskBill || {}).length <= 0) {
      stayInfo = {
        hasRepayCoupon: !!(this.availableCouponDetailList || []).filter((item) => (item.awardType === '108')).length,
        nearBillsTotalPrincipalAmt: (nearBills || {}).nearBillsTotalPrincipalAmt,
        dueTagCust: repayStatus === repayStatusType.dueTagCust,
        nearBillsTotalAmount: (nearBills || {}).nearBillsTotalAmount,
        repayDetailList: prepareRepayDetailList(this.statusBillList, '7days'),
      };
    }
    return stayInfo;
  }

  // 交互式运营-获取协商还减免比例
  opGetNegotiateRepayWaiveRatio() {
    const { negotiateRepayPackageList = [] } = this.indexStore;
    const { waiveRatio = '' } = negotiateRepayPackageList[0] || {};
    return waiveRatio.toString();
  }

  // 交互式运营-逾期还款服务资格判断和数据获取
  opGetOverDueRepayServiceData() {
    const { repayStatus } = this.state;
    const { nearBills } = this.indexStore;
    const waiveCouponObject = (this.availableCouponDetailList || []).filter((item) => (item.awardType === '216' && item.subUseSceneCode === 'SSA01'))[0] || {}; // 固额、结清减免券
    const feeReduceCouponObject = (this.usableQualConponList || []).filter((item) => (item.awardType === '216' && item.subUseSceneCode === 'SSA02'))[0] || {}; // 息费减免券
    const repayDeductionCouponObject = (this.availableCouponDetailList || []).filter((item) => (item.awardType === '108'))[0] || {}; // 还款抵扣券

    // 判断是否满足 "已有息费减免券" 还款服务准入条件
    if (Object.keys(waiveCouponObject || {}).length > 0) {
      this.repayServiceQualifyList.push('WaiveCoupon');
    }
    // 判断是否满足 "还的多减得多" 还款服务准入条件
    if (Object.keys(feeReduceCouponObject || {}).length > 0) {
      this.repayServiceQualifyList.push('FeeReduce');
    }

    const { overdueRepayServiceSurveySerialNo = {}, overdueRepayServicePhoneCallSwitch = 'N', overdueRepayServicePhoneCallNumber } = this.repayConfig || {};
    return {
      dueTagCust: repayStatus === repayStatusType.dueTagCust,
      nearBillsTotalAmount: (nearBills || {}).nearBillsTotalAmount,
      nearBillsTotalPrincipalAmt: (nearBills || {}).nearBillsTotalPrincipalAmt,
      hasRepayCoupon: (!!Object.keys(waiveCouponObject || {}).length) || (!!Object.keys(feeReduceCouponObject || {}).length) || (!!Object.keys(repayDeductionCouponObject || {}).length),
      repayDetailList: prepareRepayDetailList(this.statusBillList, '7days'),
      waiveCouponObject,
      feeReduceCouponObject,
      overdueRepayServiceSurveySerialNo,
      overdueRepayServicePhoneCallSwitch,
      overdueRepayServicePhoneCallNumber,
      repayServiceQualifyList: this.repayServiceQualifyList,
    };
  }

  // 判断招行小钱包入口，做一个单独处理线上导航返回问题
  isCmbXqbScene() {
    const scene = Url.getParam('scene');
    // 针对小钱包做一个返回的处理
    return scene === 'ibfxqb';
  }

  // 提供放置挽留弹窗的位置
  async beforeRouteLeave(from, to, next, options) {
    // 在途停留事件取消
    if (this.stayTimer) {
      this.stayTimer = null;
    }
    const repayJumpOut = Madp.getStorageSync('repayJumpOut', 'SESSION');
    const resultPageJumpOut = Madp.getStorageSync('resultPageJumpOut', 'SESSION');
    // 还款成功之后返回还款首页，点左上角返回就退出
    if (resultPageJumpOut === 'Y' && (from.path === to.path)) {
      // 招行渠道回到招行渠道首页
      if (Madp.getChannel() === '3CMBAPP') {
        if (this.isCmbXqbScene()) {
          Madp.navigateBack({ delta: 1, needCloseWebviewOnFail: true });
        } else {
          Madp.reLaunch({
            url: `${urlDomain}/${Madp.getChannel()}/ibfcmb/#/pages/index/index`
          }).then().catch(() => {
            Madp.closeWebView();
          });
        }
        Madp.setStorageSync('resultPageJumpOut', '', 'SESSION');
        return;
      } else if (Madp.getChannel() === '3WYDAPP') {
        Madp.setStorageSync('resultPageJumpOut', '', 'SESSION');
        const assignedRedirectPage = getStore('assignedRedirectPage');
        setStore({ assignedRedirectPage: '' });
        if (assignedRedirectPage) {
          Madp.redirectTo({ url: assignedRedirectPage });
        } else {
          (Madp.closeWebView() || Promise.reject()).then().catch(() => {
            Madp.navigateBack({ delta: 1 });
          });
        }
        return;
      } else {
        Madp.setStorageSync('resultPageJumpOut', '', 'SESSION');
        (Madp.closeWebView() || Promise.reject()).then().catch(() => {
          Madp.navigateBack({ delta: 1 });
        });
      }
    }
    if (repayJumpOut === 'Y') {
      Madp.setStorageSync('repayJumpOut', '', 'SESSION');
      next(true);
      return;
    }
    // 从还款首页跳转出去重定向回首页的不挽留，退回到上一页面
    if (Url.getParam('needBack') === '1') {
      if (from.path === to.path) {
        Madp.navigateBack({ delta: 1 });
        next(true);
      }
    }
    this.goNextUrl = next;
    if (from.path === to.path) {
      this.shouldBackTwoUrl = true;
    }
    if (process.env.TARO_ENV === 'h5' && from.path === to.path) {
      const doKycExit = Madp.getStorageSync('doKycExit', 'SESSION');
      if (isIOS() && doKycExit === 'Y') {
        // 解决在再分期/延后还问卷填写页面点击返回时无法回到还款首页(触发了2次返回直接退出)的问题(目前仅在iPhone13出现)
        Madp.setStorageSync('doKycExit', '', 'SESSION');
        next(false);
        return;
      }
      // 还款日7日内，检查互动运营圣约挽留
      if (this.getCustStatus === 'InSeven') {
        this.hasTriggerOpDetainDialog = true;
        if (await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.repayIndexExit)) {
          next(false);
          return;
        }
      }
      // 逾期还款，检查互动运营还款服务挽留
      const { negotiateRepayTaskBill } = this.indexStore;
      if (this.repaySence === 'overdueRepay' && Object.keys(negotiateRepayTaskBill || {}).length <= 0) {
        // 1、关闭还款计划，防止弹窗重叠（之所以能放到这里，是因为点了返回后要么弹窗要么直接退出，所以可以先关闭）
        if (this.WaitPayPlanRef) this.WaitPayPlanRef.close();
        // 2、增加标识，还款服务弹窗触发了就不在触发还款停留（兼容还款服务先触发，还款停留后触发情况）
        this.hasTriggerOpDetainDialog = true;
        if (await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.repayIndexOverDueExit)) {
          next(false);
          return;
        }
      }
      next(true);
      this.closeOrBack();
    } else {
      next(true);
    }
  }

  // 微信自定义导航栏挽留弹框
  beforeMiniRouteLeave = async () => {
    if (this.hasTriggerOpDetainDialog) {
      // 交互运营挽留弹窗触发中，再次点击返回直接退出
      Madp.navigateBack();
      return;
    }

    // 在途停留事件取消
    if (this.stayTimer) {
      this.stayTimer = null;
    }
    // 还款日7日内，检查互动运营圣约挽留
    if (this.getCustStatus === 'InSeven') {
      this.hasTriggerOpDetainDialog = true;
      if (!await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.repayIndexExit)) {
        Madp.navigateBack({ delta: 1 });
        return;
      }
    }
    // 逾期还款，检查互动运营还款服务挽留
    const { negotiateRepayTaskBill } = this.indexStore;
    if (this.repaySence === 'overdueRepay' && Object.keys(negotiateRepayTaskBill || {}).length <= 0) {
      // 1、关闭还款计划，防止弹窗重叠（之所以能放到这里，是因为点了返回后要么弹窗要么直接退出，所以可以先关闭）
      if (this.WaitPayPlanRef) this.WaitPayPlanRef.close();
      // 2、增加标识，还款服务弹窗触发了就不在触发还款停留（兼容还款服务先触发，还款停留后触发情况）
      this.hasTriggerOpDetainDialog = true;
      // 为true击中运营点：触发弹窗；为false未击中运营点：退出还款
      if (!await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.repayIndexOverDueExit)) {
        Madp.navigateBack({ delta: 1 });
      }
    } else {
      Madp.navigateBack({ delta: 1 });
    }
  }

  closeOrBack = () => {
    // 招行固定返回招行招联首页
    const doKycBack = Madp.getStorageSync('doKycBack', 'SESSION');
    Madp.setStorageSync('doKycBack', '', 'SESSION');
    if (Madp.getChannel() === '3CMBAPP') {
      if (this.isCmbXqbScene()) {
        Madp.navigateBack({ delta: 1, needCloseWebviewOnFail: true });
      } else {
        Madp.reLaunch({
          url: `${urlDomain}/${Madp.getChannel()}/ibfcmb/#/pages/index/index`
        }).then().catch(() => {
          Madp.closeWebView();
        });
      }
    } else if (Madp.getChannel() === '3WYDAPP') {
      const assignedRedirectPage = getStore('assignedRedirectPage');
      setStore({ assignedRedirectPage: '' });
      if (assignedRedirectPage) {
        Madp.redirectTo({ url: assignedRedirectPage });
      } else {
        (Madp.closeWebView() || Promise.reject()).then().catch(() => {
          Madp.navigateBack({ delta: 1 });
        });
      }
    } else if (doKycBack !== 'Y' && (Madp.getChannel() === '0HWYFW' || Madp.getChannel() === '3CUAPP')) {
      Madp.navigateBack({ delta: 1, needCloseWebviewOnFail: true });
    } else {
      (Madp.closeWebView() || Promise.reject()).then().catch(() => {
        Madp.navigateBack({ delta: 1 });
      });
    }
  }

  async getCouponList() {
    const { awardDetailList = [] } = await Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }) || {};
    this.allAwardDetailList = awardDetailList || [];
  }

  async getWaiveCouponList(topCardStatus) {
    const formatAwardDetailList = this.allAwardDetailList.map((item) => transformNewCouponAPIRes(item));
    // 还款接入新优惠券查询接口，请求优惠券列表参数配置
    const amount = getStore('nearBillsTotalAmount');
    const { repayBillList } = getStore('nearBills');
    const waiveCouponParam = {
      repayType: 'normalPay',
      repayDetailList: this.statusBillList,
      repayTotalAmt: amount,
      currentBillClear: (repayBillList || []).filter((item = {}) => item.belongUnicomContractOrder !== 'Y').length === (this.statusBillList || []).filter((item = {}) => item.belongUnicomContractOrder !== 'Y').length ? 'Y' : '',
      amtHasChanged: false,
    };
    const { availableCouponDetailList, appExclusiveCouponDetailList } = await filterWaiveCoupons(formatAwardDetailList, waiveCouponParam);
    this.appExclusiveCouponDetailList = appExclusiveCouponDetailList;
    this.availableCouponDetailList = availableCouponDetailList;
    // 可还款使用息费减免券，固额+结清（216+01、02）
    if ((topCardStatus === repayStatusType.dueTagCust) || (topCardStatus === repayStatusType.overDue)) {
      this.setState({
        availRepayWaiveFlag: (availableCouponDetailList || []).filter((item) => (item.awardType === '216' && (item.awardAmtType === '1' || item.awardAmtType === '4'))).length > 0,
      });
    }
  }

  // 获取优惠券信息
  async getQualCouponList() {
    // 不是博弈互动的渠道就不进行接口请求
    let finalUsableQualCouponList = [];
    if (this.repayConfig.ByhdChannels && this.repayConfig.ByhdChannels.indexOf(Madp.getChannel()) === -1) return finalUsableQualCouponList;
    const { usableQualConponList } = await filterQualCoupons(this.allAwardDetailList, this.statusBillList, Number(this.overdueDay || 0) >= 1) || {};
    // 判断是否有免提前还违约金资格券
    this.havePrepayFeeRightsCoupon = (usableQualConponList || []).filter((item) => (item.awardType === '306')).length > 0;
    finalUsableQualCouponList = (usableQualConponList || []).filter((item) =>
      item.awardType === '216'
      || (item.awardType === '117' && (item.subUseSceneCode === 'SSA01' || item.subUseSceneCode === 'SSA02'))
      || item.awardType === '217'
    );
    return finalUsableQualCouponList;
  }

  componentDidShow() {
    // 解决再分期返回刷新问题
    const isAdvancedStageBack = Madp.getStorageSync('ADVANCED_STAGE_REPAY', 'SESSION') === 'Y';
    // 解决分期还本返回不刷新问题（部分机型）
    const isRenewLoansBack = Madp.getStorageSync('renewLoansBack', 'SESSION') === 'Y';
    if (process.env.TARO_ENV === 'h5' && (isAdvancedStageBack || isRenewLoansBack)) {
      Madp.setStorageSync('ADVANCED_STAGE_REPAY', '', 'SESSION');
      Madp.setStorageSync('renewLoansBack', '', 'SESSION');
      window.location.reload();
      return;
    }
    // 解决联通渠道返回后无法拉起还款计划问题
    if (Madp.getStorageSync('showRepayPlan', 'SESSION') === 'Y') {
      Madp.setStorageSync('showRepayPlan', '', 'SESSION');
      setTimeout(() => {
        this.WaitPayPlanRef.show();
      }, 1000);
    }
    Madp.setNavigationBarTitle({ title: '还款' });
    if (process.env.TARO_ENV !== 'h5') {
      // fix: 小程序还款之后继续还款选到了上次选的卡
      setStore({
        selectedCard: {},
      });
    }
  }

  ledaDidMount() {
    this.isLedaDataReady = true;
    if (this.isPageDataReady && !this.finishedInitLedaFlag) {
      this.initLedaData();
    }
  }

  // 查询缓催待办
  async getUserTodoData() {
    const { data } = await Dispatch.repayment.queryCommonTodo({
      userId: this.userId,
      todoTypeList: ['B001'],
      todoStatus: '1', // 1: 全部状态，2: 进行中（默认）
      operSys: 'TBF',
    });
    return data || {};
  }

  // 查询再分期推荐规则
  getTagContext = async () => {
    const extendRuleCode = '2307041700103801';
    const restagingRuleCode = '2311030848137527';
    const { data } = await Dispatch.repayment.getTagContext({
      cacheType: 1,
      tagType: 3,
      ruleCodeList: [extendRuleCode, restagingRuleCode],
      sourceModule: 'RBF'
    });
    const { ruleMap } = data || {};
    if (ruleMap) {
      return {
        extendRuleSwitch: ruleMap[extendRuleCode],
        restagingRuleSwitch: ruleMap[restagingRuleCode]
      };
    }
    return {};
  }

  // 延后还、再分期、协商还、分期还本接口准入
  getServicesQualify = async () => {
    const { basicCustDto } = getStore('sessionInfo') || {};
    const { identified } = basicCustDto || {};
    // 针对未实名用户直接不调用准入接口 解决生产报错日志，因为没有custid会报错
    if (!identified) {
      return {
        billExtendQualifyFlag: 'N',
        advancedStageQualifyFlag: 'N',
        consultQualifyFlag: 'N',
        renewLoansQualifyFlag: 'N',
        repayPromiseFlag: 'N'
      };
    }
    let servicesQualifySwitch = '';
    if (getProductAllParams && typeof getProductAllParams === 'function') {
      ({ servicesQualifySwitch } = await getProductAllParams('HK.HK03') || {});
    }
    if (servicesQualifySwitch === 'Y') {
      await this.indexStore.checkQualifyNew();
      const {
        billExtendQualifyFlag, advancedStageQualifyFlag, consultQualifyFlag, renewLoansQualifyFlag, repayPromiseFlag
      } = this.indexStore;
      return {
        billExtendQualifyFlag, advancedStageQualifyFlag, consultQualifyFlag, renewLoansQualifyFlag, repayPromiseFlag
      };
    } else {
      const [
        { extendRuleSwitch, restagingRuleSwitch },
        ,
      ] = await Promise.all([
        this.getTagContext(),
        this.indexStore.checkQualify({
          serviceType: '006', // 协商分期
          sourceType: '1' // 客户自主申请
        }),
        this.indexStore.checkQualifyNew(),
      ]);
      const { qualifyFlag, renewLoansQualifyFlag, repayPromiseFlag } = this.indexStore;
      return {
        billExtendQualifyFlag: extendRuleSwitch,
        advancedStageQualifyFlag: restagingRuleSwitch,
        consultQualifyFlag: qualifyFlag,
        renewLoansQualifyFlag: renewLoansQualifyFlag,
        repayPromiseFlag: repayPromiseFlag
      };
    }
  }

  async initLedaData() {
    this.finishedInitLedaFlag = true;
    const {
      publicDetainDialog = {}, activityCouponDialog, repayServiceColumns, virtualCustormerBubble
    } = this.state;
    const topCardStatus = this.getTopCardStatus();
    // 先加载其它展位，最后处理还款服务展位
    const { dataObj = {} } = publicDetainDialog;
    const { elementDataList = [] } = activityCouponDialog;
    const activityEleLen = elementDataList && elementDataList.length;

    if (dataObj && dataObj.contentList && dataObj.contentList.length) {
      this.sendBeacon('DetainDialogShow', 'SO');
    }
    // 解决repayStatus更新异常的问题
    setTimeout(() => {
      const userStatus = this.transformStatus(topCardStatus);
      refresh({
        pageId: RepaymentIndexPageId,
        isRequest: false,
        bannerWithoutBorder: {
          pageFlag: 'new'
        },
        loanIndexBanner: {
          userStatus,
          pageFlag: 'new'
        },
        publicDetainDialog: {
          isOpen: this.showPublicDetainDialog || !activityEleLen, // 是否展示
          handler: (type, targetUrl, next) => {
            this.detainPopWindowHandler(type, targetUrl, next, userStatus);
          }, // 按钮点击回调
        }
      });
    }, 10);
    // 还款服务栏位
    if (repayServiceColumns && repayServiceColumns.dataObj) {
      const {
        repayServiceTitle, contentList, loanBusinessTitle
      } = repayServiceColumns.dataObj;
      let repayServiceColumnsList = contentList || [];
      repayServiceColumnsList = repayServiceColumnsList.sort((a, b) => Number(a.serviceSeq ? a.serviceSeq : '40') - Number(b.serviceSeq ? b.serviceSeq : '40'));
      // 再分期，资格券、资格检查（在途案件）、推荐规则
      const [
        { billExtendQualifyFlag, advancedStageQualifyFlag, consultQualifyFlag, renewLoansQualifyFlag, repayPromiseFlag },
        ,
      ] = await Promise.all([
        // this.getUserTodoData(),
        this.getServicesQualify(),
        this.setStagable(topCardStatus),
      ]);
      const usableQualConponList = await this.getQualCouponList();
      this.usableQualConponList = usableQualConponList;
      // 息费减免、讨价还价互斥
      if (usableQualConponList.filter((specialItem) => (specialItem.awardType === '216')).length > 0) {
        repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'Bargain'));
      } else {
        repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'FeeReduce'));
      }
      // if (data && data.userTodoDtoList && data.userTodoDtoList.length > 0) {
      //   let repayPromiseTodo = [];
      //   const getTime = (time) => {
      //     return time ? new Date(time.replace(/-/g, '/')) : '';
      //   };
      //   data.userTodoDtoList.forEach((todoItem) => {
      //     if (todoItem && (todoItem.todoStatus === '1' || todoItem.todoStatus === '3') && ((Number(getTime(JSON.parse(todoItem.extInf || '{}').repayCommitmentTime)) + 24 * 60 * 60 * 1000) > Number(new Date()))) {
      //       repayPromiseTodo.push(todoItem);
      //     }
      //   });
      //   if (repayPromiseTodo.length <= 0) {
      //     repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'Promise'));
      //   }
      // } else {
      //   repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'Promise'));
      // }
      // 延后还
      if (usableQualConponList.filter((specialItem) => ((specialItem.awardType === '117' && specialItem.subUseSceneCode === 'SSA01') || (specialItem.awardType === '117' && specialItem.subUseSceneCode === 'SSA02'))).length <= 0 && billExtendQualifyFlag !== 'Y') {
        repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'BillExtend'));
      } else {
        this.repayServiceQualifyList.push('BillExtend');
      }
      // 再分期
      if (usableQualConponList.filter((specialItem) => (specialItem.awardType === '217')).length <= 0 && advancedStageQualifyFlag !== 'Y') {
        repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'BillAdvancedStage'));
      } else {
        this.repayServiceQualifyList.push('BillAdvancedStage');
      }
      // 分期还本
      // 逾期打标或逾期天数大于0，不展示分期还本
      const { overDueTagFlag } = getStore('nearBills') || {};
      if (renewLoansQualifyFlag !== 'Y') {
        repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'RenewLoans'));
      } else {
        if (overDueTagFlag || Number(this.overdueDay || 0) > 0) {
          repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'RenewLoans'));
        } else {
          this.repayServiceQualifyList.push('RenewLoans');
        }
      }
      // 还款承诺
      if (repayPromiseFlag !== 'Y') {
        repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'Promise'));
      } else {
        this.repayServiceQualifyList.push('RepayPromise');
      }
      // 账单分期
      if (!this.isStagable) {
        repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'BillsStaging'));
      }
      // 协商还资格通过，且无在途的协商还计划
      const { negotiateRepayTaskBill } = this.indexStore;
      if (consultQualifyFlag !== 'Y' || (negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}')) {
        repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'ConsultRepay'));
      } else {
        this.repayServiceQualifyList.push('ConsultRepay');
      }
      // 全部待还展示
      if ((topCardStatus === repayStatusType.dueTagCust)
        || !this.advancedBillCounts
        || Number(this.advancedBillCounts) <= 0
        || (negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}')) {
        repayServiceColumnsList = repayServiceColumnsList.filter((columnItem) => (columnItem && columnItem.uniqueName !== 'BillListAll'));
      }
      repayServiceColumnsList = repayServiceColumnsList.map((item) => {
        if (this.havePrepayFeeRightsCoupon && item.uniqueName === 'More') {
          // 有免提还违约金资格券，跳转服务页时带上参数，用于判断全部待还是否展示优惠券角标
          item.targetUrl = `${repayServiceColumnsUrls[item.uniqueName]}&havePrepayFeeRightsCoupon=1`;
          return item;
        }
        item.targetUrl = repayServiceColumnsUrls[item.uniqueName];
        return item;
      });
      // 保存还款服务列表在列表页展示
      setStore({
        repayServiceColumnsParam: {
          repayServiceTitle,
          repayServiceColumnsList,
          loanBusinessTitle
        }
      });

      if (repayServiceColumnsList && repayServiceColumnsList.length > 0) {
        if (topCardStatus !== repayStatusType.loading
          && topCardStatus !== repayStatusType.empty
          && topCardStatus !== repayStatusType.clear
          && Object.keys(negotiateRepayTaskBill || {}).length <= 0
        ) {
          this.recommendService = repayServiceColumnsList.filter((item) => item.businessCategory === 'repayService' && item.uniqueName !== 'More')[0] || {};
        }
        const recommendedServiceList = repayServiceColumnsList.filter((item) => item.uniqueName !== this.recommendService.uniqueName);
        this.businessColumns = [];
        this.businessColumns = recommendedServiceList.slice(0, 3).concat(recommendedServiceList.filter((columnItem) => (columnItem && columnItem.uniqueName === 'More')));
        this.businessColumns.forEach((businessItem) => {
          this.sendBeacon(businessItem.uniqueName, 'SO', 'Show');
          if ((this.realOverDueFlag) && businessItem) {
            businessItem.showImgUrl = businessItem.overDueImgUrl;
          } else if (themeColor === '#E60027' && businessItem) {
            businessItem.showImgUrl = businessItem.unicomImgUrl;
          } else {
            businessItem.showImgUrl = businessItem.imgUrl;
          }
        });
      }
    }
    if (!this.randomReduceText && virtualCustormerBubble && virtualCustormerBubble.dataObj && virtualCustormerBubble.dataObj.contentList && virtualCustormerBubble.dataObj.contentList[0] && virtualCustormerBubble.dataObj.contentList[0].title) {
      this.randomReduceText = virtualCustormerBubble.dataObj.contentList[0].title;
    }
    this.setState({ pageAvailShowButtons: true });
    const tempRepayStatus = getStore('repayStatus');
    if (tempRepayStatus !== topCardStatus) {
      this.sendBeacon(this.statusBeaconPrefix[tempRepayStatus], 'SO');
    }
  }

  detainPopWindowHandler(type, targetUrl, next, userStatus) {
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: `DetainDialog${type}` });
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'DetainDialogGoNext' });
    switch (type) {
      case 'continue': {
        targetUrl && Madp.navigateTo({ url: targetUrl });
        break; // 点击确定按钮
      }
      case 'cancel': {
        typeof next === 'function' && next(true);
        break; // 点击取消回调
      }
      case 'close': {
        typeof next === 'function' && next(false);
        break; // 点击X 关闭弹窗的回调
      }
      case 'none': {
        typeof next === 'function' && next(false);
        break;// 没有配置内容时的回调
      }
      default: {
        typeof next === 'function' && next(true);
        break;
      }
    }
    // 设置一次publicDetainDialog展位关闭，避免后续页面render导致重复弹窗
    refresh({
      pageId: RepaymentIndexPageId,
      publicDetainDialog: {
        isOpen: false,
        handler: () => { },
      },
      isRequest: false,
      loanIndexBanner: { userStatus } // 重置列表展位，不然会跟随remind消失
    });
  }

  getTopCardStatus() {
    const repayStatus = getStore('repayStatus');
    const nearBills = getStore('nearBills');
    // 首页头部卡片展示状态
    let status = repayStatusType.loading;
    if (this.advancedBillCounts && repayStatus === repayStatusType.loading) {
      status = repayStatusType.clear;
    } else if (repayStatus === repayStatusType.loading) {
      status = repayStatusType.empty;
      const { availLoanFlag, availLoanWaiveFlag } = getAvailLoanFlag(this.accountInfoList, this.awardInfoList);
      this.availLoanFlag = availLoanFlag;
      this.availLoanWaiveFlag = availLoanWaiveFlag;
    } else if (repayStatus) {
      status = repayStatus;
    }
    if (status !== repayStatusType.dueTagCust) {
      this.statusBillList = (nearBills.repayBillList || []).filter((bill) => bill.usedToCalculate === 'Y');
    } else {
      // 逾期打标是自己拼接的，直接给值就好
      this.statusBillList = nearBills.repayBillList;
    }
    this.setState({ repayStatus: status });
    this.sendBeacon(this.statusBeaconPrefix[status], 'SO');
    return status;
  }


  uploadOnLoadTime() {
    if (Util.isH5Env()) {
      // 不太精准的还款首页加载时间的埋点
      dispatchTrackEvent({
        event: EventTypes.EV,
        beaconId: 'repayment.RepaymentIndex.OnLoadTime',
        // eslint-disable-next-line no-undef
        beaconContent: { cus: { time: new Date().getTime() - TARO_REPAYMENT_STARTTIME } }
      });
      console.log('repayment Page load Time:', new Date().getTime() - TARO_REPAYMENT_STARTTIME);
    }
  }

  async initPageConfig() {
    this.repayConfig = await Dispatch.repayment.getMultipleCommonConfig(['repayIndex.config', 'repayMarketing.config']) || {};
  }

  async getAccount() {
    await this.indexStore.getAccount({
      queryScene: '21'
    });
    this.accountInfoList = this.indexStore.accountInfoList || [];
    this.awardInfoList = this.indexStore.awardInfoList || [];
    this.diversionBillInfo = this.indexStore.diversionBillInfo || {};
    if ((this.accountInfoList.filter((item) => (item || {}).acctBusiType === '01') || []).length > 0) {
      const { acctStatus } = this.accountInfoList.filter((item) => item.acctBusiType === '01')[0] || {};
      if (acctStatus === '3') {
        await this.indexStore.getTransferDetail();
        this.transferInfo = this.indexStore.assetTransferInfo || [];
        // this.transferInfo.transferDate = "********";
        // this.transferInfo.transferMerchantPhoneNo = "400-200-400";
        // this.transferInfo.transferOrgName = "PP资产公司";
        // this.transferInfo.transferTotalBalance = "1998.99";
        this.setState({
          transferStatus: true,
        });
        return true;
      }
    }
  }

  async queryAwardAccount() {
    const { awardAccountInfo } = await Dispatch.repayment.queryAwardAccount() || {};
    if (awardAccountInfo) {
      const { accountStatus, awardAccountLevel } = awardAccountInfo;
      if (accountStatus === 1 && awardAccountLevel === 9) {
        this.setState({ isVPlus: true });
      }
    }
  }

  /**
   * 获取“七天内待还账单”还款总额，以及7天待还账单列表
   * 普通用户与打标用户的“总额”字段不一样，要区分；打标用户实际查的全部待还，还款日会更新成下一期（非账单第一期）
   */
  async getNearBillsAndAmount() {
    const { agmType } = this.$router.params;
    let totalAmount = 0;
    await this.indexStore.getNearBills({ agmType, additionalQueryList: ['001'] }, { setNearAmount: true });
    const data = this.indexStore.nearBills;
    this.indDeductionFlag = (data || {}).indDeductionFlag;
    // 未实名客户直接展示暂无待还
    if (data.errCode === 'UMDP00946') {
      this.setState({ repayStatus: repayStatusType.empty });
    }
    this.nearBills = data;
    this.repayPlanResumeDate = data.resumeFirstDate;
    const repayStatus = getStore('repayStatus');
    if (data.isDueTagCust === 'Y') {
      totalAmount = data.dueRepayInfo && (data.dueRepayInfo.duePayTotalAmt || 0);
    } else {
      totalAmount = data.surplusTotalAmt || 0;
    }
    if (repayStatus === repayStatusType.overDue) {
      setStore({ isOverdueCust: true });
    }
    // 上报近期待还、法诉费埋点
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: Number(totalAmount) > 0 ? 'HasNearAmount' : 'noNearAmount',
    });
    const { courtCostBalance } = data;
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: Number(courtCostBalance) > 0 ? 'HasCourtCost' : 'NoCourtCost',
    });
    // 上报协商还埋点
    const { negotiateRepayTaskBill } = this.indexStore;
    if (negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}') {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'HasConsultRepay',
      });
    }
    // 上报还款按钮曝光埋点
    if (
      repayStatus === repayStatusType.overDue
      || repayStatus === repayStatusType.inSeven
      || repayStatus === repayStatusType.today
      || repayStatus === repayStatusType.inSevenMutil
      || repayStatus === repayStatusType.dueTagCust
      || (negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}')) {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'AvailRepayBtn',
      });
    }
    // advanceRepayAllowCount 可提前还款借据数 原来全部待还拿的数据
    this.advancedBillCounts = data.advanceRepayAllowCount;
    this.setState({
      totalAmount: Number(totalAmount).toFixed(2),
      courtCostBalance: Number(courtCostBalance) > 0 ? courtCostBalance : '0.00',
    });
  }

  // 获取待还账单状态
  getWaitPayStatus = (businessDate, repayDate) => {
    let waitPayStatus = 'undue';
    if (!businessDate || !repayDate) return waitPayStatus;
    const businessDateFormat = `${businessDate.substring(0, 4)}-${businessDate.substring(4, 6)}-${businessDate.substring(6)}`;
    const repayDateFormat = `${repayDate.substring(0, 4)}-${repayDate.substring(4, 6)}-${repayDate.substring(6)}`;
    const waitPayDay = Util.timeMinus(businessDateFormat, repayDateFormat, 'day', 'date');
    // [-7, 0), 到期；[0], 当天；(0, +∞), 逾期
    if (waitPayDay >= -7 && waitPayDay < 0) {
      waitPayStatus = 'due';
    } else if (waitPayDay === 0) {
      waitPayStatus = 'now';
    } else if (waitPayDay > 0) {
      waitPayStatus = 'overdue';
    }
    return waitPayStatus;
  }

  /**
   * 获取剩余每月待还
   */
  getFuturePlans = async () => {
    // 未来待还，支持并发查询的无beginDate查询，传queryType为1  原有 { beginDate: this.repayPlanResumeDate }
    await this.indexStore.acquireFuturePlans({ queryType: '1', recentBillFilterFlag: 'N' });
    const { data, errCode } = this.indexStore.futurePlans || {};
    // 未实名客户直接展示暂无待还
    if (errCode === 'UMDP00946') {
      this.setState({ repayStatus: repayStatusType.empty });
    }
    const { repayFutureBillDetailList, resumeDate, businessDate } = data || {};
    this.repayPlanResumeDate = resumeDate;
    const futurePlan = repayFutureBillDetailList || [];
    // 由于可能返回的列表里，全是可见不可还账单
    // 需要根据每月可还金额判断是否全是可见不可还账单
    const canPayList = futurePlan.filter((plan) => plan.monthRepayAmt && Number(plan.monthRepayAmt));
    // 存储客服拓展参数，后续页面需要使用
    this.setExtraParam(true);
    if (canPayList.length) {
      const { futurePlanList } = this.state;
      const updatedPlanList = futurePlanList.concat(canPayList.map((bill) => ({
        amount: bill.monthRepayAmt,
        date: Util.getDateCollection(bill.repayDate),
        canPay: bill.canPay === 'Y',
        billList: bill.monthDetailList,
        waitPayStatus: this.getWaitPayStatus(businessDate, bill.repayDate)
      })));
      this.setState({ futurePlanList: updatedPlanList });
    }
  }

  goNearPay = () => {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ToTitlePay', beaconContent: { cus: { custStatus: this.getCustStatus } } });
    const { repayStatus } = this.state;
    if (repayStatus === repayStatusType.dueTagCust) return;
    const nearPayAddr = `/pages/bill-list-near/index?fromIndex=1&custStatus=${this.getCustStatus}`;
    Util.router.push({
      path: nearPayAddr,
    });
  }

  goFuturePay(repayDate) {
    const { deductStatus } = this.state;
    const { deductTime } = deductStatus || {};
    // 由于未来待还判断和近期待还判断可见不可还账单方法不同，由近期待还查出的7天外数据，跳去账单页时，要把可见不可还账单重新拼接回去
    setStore({ futureBillList: (this.nearBills.repayBillList || []).concat(this.nearBills.showBillList || []) });
    const addrLink = repayDate ? `/pages/bill-list-future/index?fromIndex=1&repayDate=${repayDate}&custStatus=${this.getCustStatus}${deductTime ? `&deductTime=${deductTime}` : ''}` : `/pages/bill-list-future/index?fromIndex=1&custStatus=${this.getCustStatus}${deductTime ? `&deductTime=${deductTime}` : ''}`;
    Util.router.push(addrLink);
  }

  closeDialog() {
    this.showDialog = false;
    dispatchTrackEvent({ event: EventTypes.SO, beaconId: '10005.21.10003', beaconContent: '随机立减点击关闭' });
  }

  unicomBillCheck() {
    // 逾期打标、逾期用户不提示联通合约机订单提示
    const { repayStatus } = this.state;
    if ((repayStatus === repayStatusType.dueTagCust) || (repayStatus === repayStatusType.overDue)) return true;
    // 联通合约机订单到期不提示
    const unicomOrders = new Set();
    ((this.nearBills || {}).repayBillList || []).forEach((b = {}) => {
      if (b.belongUnicomContractOrder === 'Y' && b.surplusDays !== 0) {
        unicomOrders.add(b.orderNo);
      }
    });
    const count = unicomOrders.size;
    if (!count) return true;
    dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'ShowUnicomBillTip', beaconContent: { cus: { custStatus: this.getCustStatus } } });
    this.unicomBillTipText = `您当期账单含有${count}笔联通合约机消费，联通合约机消费只需您每月按时缴纳话费即可，您确定要一同还款吗？`;
    this.setState({ showUnicomBillTip: true });
    return false;
  }

  // 还款前校验接口, 特殊接口把ret判断逻辑拿出fetch.js
  async toRepayment(scene = '', jumpCheck = false) {
    setStore({ selectedBillList: this.statusBillList });
    if (this.repaySence === 'normalRepay') {
      await this.opOnPageEvent('opRepayClick', EVENT_CODE_MAP.repayIndexClick);
    }
    if (!jumpCheck) {
      if (!this.unicomBillCheck()) return;
    }
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ToRepayment', beaconContent: { cus: { custStatus: this.getCustStatus } } });
    await this.indexStore.getRepayPretreatment();
    const { ret, errMsg, errCode } = this.indexStore.repayPretreatment || {};

    if (ret === '0') {
      const { totalAmount, courtCostBalance } = this.state;
      let amount = '';
      if (scene === 'consult') {
        const { negotiateRepayTaskBill } = this.indexStore;
        const { taskShouldAmt, totalShouldAmt } = negotiateRepayTaskBill || {};
        if (taskShouldAmt && Number(taskShouldAmt) > 0) {
          amount = Number(taskShouldAmt).toFixed(2);
        } else {
          amount = Number(totalShouldAmt).toFixed(2);
        }
      } else {
        amount = Number(totalAmount).toFixed(2);
      }
      const { repayBillList } = getStore('nearBills');
      Util.router.push({
        path: '/pages/express-repay/index?_windowSecureFlag=1',
        query: {
          amount,
          stages: this.statusBillList.length,
          billType: '7days',
          ...(Number(courtCostBalance) > 0 ? { courtCostBalance } : {}), // 透传法诉费
          expressScene: '11',
          specialRepayScene: scene,
          currentBillClear: (repayBillList || []).filter((item = {}) => item.belongUnicomContractOrder !== 'Y').length === (this.statusBillList || []).filter((item = {}) => item.belongUnicomContractOrder !== 'Y').length ? 'Y' : '',
        }
      });
    } else {
      setTimeout(() => {
        Util.showErrorMsg(errMsg);
        dispatchTrackEvent({
          target: this,
          event: EventTypes.EV,
          beaconId: `${errCode === 'UMDP00065' ? 'OtherRepaymentRunning' : 'RepayPretreatmentSystemError'}`
        });
      }, 50);
    }
  }

  // 仅剩法诉费，跳转还款页
  async toRepaymentCourtOnly() {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ToRepaymentCourtOnly', beaconContent: { cus: { custStatus: this.getCustStatus } } });
    await this.indexStore.getRepayPretreatment();
    const { ret, errMsg, errCode } = this.indexStore.repayPretreatment || {};
    if (ret === '0') {
      const { courtCostBalance } = this.state;
      setStore({ selectedBillList: [] });
      Util.router.push({
        path: '/pages/express-repay/index?_windowSecureFlag=1',
        query: {
          amount: Number(courtCostBalance).toFixed(2),
          courtOnly: '1',
          expressScene: '11'
        }
      });
    } else {
      setTimeout(() => {
        Util.showErrorMsg(errMsg);
        dispatchTrackEvent({
          target: this,
          event: EventTypes.EV,
          beaconId: `${errCode === 'UMDP00065' ? 'OtherRepaymentRunning' : 'RepayPretreatmentSystemError'}`
        });
      }, 50);
    }
  }

  onStatusCardClick = async (cb, beaconMsg) => {
    const { repayStatus } = this.state;
    beaconMsg && this.sendBeacon(this.statusBeaconPrefix[repayStatus], 'EV', beaconMsg);
    if (cb && typeof cb === 'function') cb();
    if (beaconMsg === 'ToRepay') {
      const isOnTime = this.transformStatus(repayStatus) === 'noneOverDue';
      const { repayBillList } = getStore('nearBills');
      const { isAllClear } = Util.judgeAllClear(repayBillList, this.statusBillList) || {};
      dispatchTrackEvent({
        target: this,
        event: 'BC',
        beaconId: 'ClickToRepay',
        beaconContent: { cus: { isOnTimeClearAll: isOnTime && isAllClear ? 'Y' : 'N' } }
      });
    }
  }

  sendBeacon(beaconId, eventType = 'EV', afterMsg = '', beforeMsg = '') {
    if (!beaconId) return;
    dispatchTrackEvent({ target: this, event: EventTypes[eventType], beaconId: `${beforeMsg || ''}${beaconId}${afterMsg || ''}` });
  }

  // 将头部卡片状态转化为列表展位的对应状态
  transformStatus(repayStatus) {
    const { overDueTagFlag, d07Flag } = getStore('nearBills');
    let userStatus = '';
    switch (repayStatus) {
      case repayStatusType.empty:
        userStatus = 'noneBill';
        break;
      case repayStatusType.clear:
        userStatus = 'noneBill';
        break;
      case repayStatusType.inSeven:
        userStatus = 'noneOverDue';
        break;
      case repayStatusType.today:
        userStatus = 'noneOverDue';
        break;
      case repayStatusType.outSeven:
        userStatus = 'noneOverDue';
        break;
      case repayStatusType.inSevenMutil:
        userStatus = 'noneOverDue';
        break;
      case repayStatusType.overDue:
        userStatus = 'overDue';
        break;
      case repayStatusType.dueTagCust:
        userStatus = 'overDue';
        break;
      case repayStatusType.loading:
      default:
        userStatus = 'noneBill';
        break;
    }
    // 对D07打标客户的逾期状态进行重新判断
    if (userStatus === 'overDue' && d07Flag) {
      userStatus = overDueTagFlag || this.realOverDueFlag ? 'overDue' : 'noneOverDue';
    }
    if (!userStatus) return;
    return userStatus;
  }

  // 账单分期入口展示逻辑依赖账单接口和额度接口，两者异步执行，都会执行该函数，以保证能在两个数据都返回的情况下执行。
  setStagable = async (status) => {
    const { repayStatus: stausInState } = this.state;
    const repayStatus = status || stausInState;
    const { negotiateRepayTaskBill } = this.indexStore;
    try {
      const {
        closeStagable = false,
        canInstallChannel = ['0APP', '3CMBAPP', '3CUAPP', '2ZFB'],
        stagesCreditType = 'D01'
      } = this.repayConfig || {};
      if (closeStagable || canInstallChannel.indexOf(Madp.getChannel()) === -1) return;
      const { repayBillList } = getStore('nearBills');
      const stagingList = (repayBillList || []).filter((o) => o.canInstall === 'Y' && o.limitType === stagesCreditType && !o.displayOverdueDays && !!o.surplusDays);
      const stagingTotal = (stagingList || []).reduce((prev, cur) => parseFloat(cur.installTotalAmt) + parseFloat(prev), 0);
      const isStagable = (repayStatus === repayStatusType.inSeven || repayStatus === repayStatusType.inSevenMutil)
        && stagingList.length > 0
        // && ChannelConfig.canStaging
        && stagingTotal >= 100
        && (Object.keys(negotiateRepayTaskBill || {}).length <= 0); // 无在途的协商还计划
      if (isStagable) {
        const [accountInfo = {}] = (this.accountInfoList || []).filter(list => list.acctBusiType === '01');
        const [limitInfo = {}] = (accountInfo.limitInfoList || []).filter((o) => o.limitType === stagesCreditType);
        if (accountInfo.acctStatus === '1'
          && (accountInfo.controlCode === '0000' || accountInfo.controlCode === 'C206' || accountInfo.controlCode === 'C207')
          && limitInfo.status === 'Y' && +limitInfo.availLimit > 0) {
          this.isStagable = isStagable;
        }
      }
    } catch (e) { }
  }

  // 通过银行卡接口获取用户代扣信息，用于代扣文案展示判断
  getDeductStatus = async (topCardStatus) => {
    let deductStatus = {};
    let businessSceneStatus = '';
    // 逾期未打标
    if (topCardStatus === '0' || topCardStatus === 'dueTag') {
      businessSceneStatus = topCardStatus;
      // 宽限期内
      if (!this.realOverDueFlag) {
        if (Number(this.overdueDay || 0) > 0) {
          businessSceneStatus = 'protect';
        } else {
          businessSceneStatus = '5';
        }
      }
    } else {
      businessSceneStatus = topCardStatus;
    }
    const [
      { deductionInfo = {}, signedZFBFlag },
      { data: recommendData = {} },
    ] = await Promise.all([
      this.indexStore.getBankCardsList(),
      Dispatch.repayment.queryRecommendFaqs({
        entrance: 'HKYHKZN',
        businessScene: repayGuideSceneInfo[businessSceneStatus || 'DEFAULT']
      }),
    ]);
    deductStatus = { ...(deductionInfo || {}), signedZFBFlag, hasBankCard: !!Object.keys(deductionInfo || {}).length, hasAlipayDeduct: signedZFBFlag === 'Y' };
    if (topCardStatus !== repayStatusType.overDue
      && topCardStatus !== repayStatusType.dueTagCust
      && topCardStatus !== repayStatusType.empty) {
      if (!(deductStatus || {}).hasBankCard) {
        dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'BankDeductOpenGuide' });
      } else {
        dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'BankDeductTip' });
      }
    }
    this.finishedInitDeductFlag = true;
    this.setState({
      deductStatus
    }, () => {
      this.getRepayGuideInfo(recommendData);
    });
  }

  // 描述引导文案
  getWarningInfo(status) {
    const { overDueTagFlag, d07Flag } = getStore('nearBills');
    const { deductStatus } = this.state;
    const {
      hasBankCard, deductTime, bankName, bankCardNo
    } = deductStatus || {};
    const deductTimeText = Number((deductTime || '').substring(0, 2) || '') <= 12 ? `上午${deductTime}` : deductTime;
    let warningIcon = '';
    let warningText = [];
    let warningGuideText = '';
    switch (status) {
      case 'dueTagCust':
        if (overDueTagFlag) {
          if (Number(this.overdueDay || 0) > 0) {
            warningIcon = redStatusImg;
            // warningText = `您已逾期${this.overdueDay}天，请尽快还款`;
            warningText = ['HK03.HK03WA002', this.overdueDay];
            warningGuideText = '逾期影响';
          } else {
            // warningText = '<span style="color: #CC1F15;">您的账户已冻结，请尽快还款</span>';
            warningText = ['HK03.HK03WA003'];
          }
        } else if (d07Flag) {
          if (Number(this.overdueDay || 0) > 0) {
            // warningText = `<span style="color: #CC1F15;">您已逾期${this.overdueDay}天，且未按约定用途使用借款，请尽快结清所有借款</span>`;
            warningText = ['HK03.HK03WA004', this.overdueDay];
          } else {
            // warningText = '您未按约定用途使用借款，请尽快结清所有借款';
            warningText = ['HK01.HK01WA012'];
          }
        }
        break;
      case 'inSeven':
      case 'inSevenMutil':
      case 'today':
      case 'outSeven':
        if (this.finishedInitDeductFlag) {
          if (!hasBankCard) {
            warningIcon = rechargeImg;
            // warningText = '开通银行卡自动还款，安全又省心';
            warningText = ['HK01.HK01WA009'];
            warningGuideText = '去开通';
          } else {
            if (this.indDeductionFlag === 'Y') {
              // warningText = `还款日${deductTimeText}会自动扣款，请预留足够金额`;
              warningText = ['HK01.HK01WA014', deductTimeText];
            } else {
              // warningText = `还款日${deductTimeText}将优先从${bankName}${(bankName || '').indexOf('银行') > -1 ? '卡' : ''}(尾号${(bankCardNo || '').substring(bankCardNo.length - 4)})中扣款，请预留足够金额`;
              warningText = ['HK01.HK01WA010', deductTimeText, bankName, (bankCardNo || '').substring(bankCardNo.length - 4)]; // 去掉是否有“卡”的判断，统一不加
            }
          }
        }
        break;
      case 'overDue':
        if (this.realOverDueFlag) {
          warningIcon = redStatusImg;
          // warningText = `您已逾期${this.overdueDay}天，请尽快还款`;
          warningText = ['HK03.HK03WA002', this.overdueDay];
          warningGuideText = '逾期影响';
        } else {
          // warningText = `<span style="color: #CC1F15;">您已逾期${this.overdueDay}天，${this.isShowVPlusTips ? '' : '逾期会影响个人信用，'}请尽快还款</span>`;
          warningText = this.isShowVPlusTips ? ['HK03.HK03WA002', this.overdueDay] : ['HK03.HK03WA001', this.overdueDay];
        }
        break;
      default:
        break;
    }
    return { warningText, warningIcon, warningGuideText };
  }

  // 跳转逾期影响
  toOverDueEffect = () => {
    Util.router.push(`/pages/index/over-due-effect?overDue=${Number(this.overdueDay || 0) > 0 ? '1' : '0'}`);
  }

  // 跳转代扣绑定
  toDeduct = (type) => {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `${type === 'bank' ? 'Bank' : 'Alipay'}DeductOpen` });
    const redirectUrl = `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/index/index`;
    const miniRedirectUrl = '%2Frepayment%2Fpages%2Findex%2Findex';
    const deductUrl = isMiniProgram()
      ? `/usercenter/pages/bankcard/AddOrVerifyBankCard?action=repayment${type === 'alipay' ? '' : '&mtagc=31031.01.02'}&redirectUrl=${decodeURIComponent(miniRedirectUrl)}${type === 'alipay' ? '&_bankCardPageType=6' : ''}`
      : `${urlDomain}/${Madp.getChannel()}/usercenter/#/pages/bankcard/AddOrVerifyBankCard?action=repayment${type === 'alipay' ? '' : '&mtagc=31031.01.02'}&redirectUrl=${encodeURIComponent(redirectUrl)}${type === 'alipay' ? '&_bankCardPageType=6' : ''}`;
    Madp.setStorageSync('repayJumpOut', 'Y', 'SESSION');
    Util.router.push(deductUrl);
  }

  // 添加专属管家
  addSteward = () => {
    let stewardUrl = '';
    if (isMuapp() && Madp.getChannel() !== '3APP') {
      const currentChannel = Madp.getChannel();
      const isStEnv = getEnv().indexOf('st') !== -1; // 判断是否为st环境
      stewardUrl = `${urlDomain}/${currentChannel}/wcs-wecom-fe/#/pages/h5Tool/index?activityId=${isStEnv ? '33' : (currentChannel === '0APP' ? '53' : '43')}&mtagc=${currentChannel === '0APP' ? '31002.15.28' : '31002.15.30'}&t=add-wx`;
    } else if (isWechat() || (process.env.TARO_ENV === 'weapp') || (Madp.getChannel() === '3CUAPP') || (Madp.getChannel() === '3APP')) {
      stewardUrl = 'https://act.mucfc.com/na/*************/index.html?needLogin=1';
    }
    if (stewardUrl) Util.router.push({
      path: stewardUrl,
      query: {},
      useAppRouter: true,
    });
  }

  // 添加还款提醒
  addRepayTip = async () => {
    let repayTipUrl = '';
    if (isMuapp()) {
      repayTipUrl = `muapp://functionalExt/jumpMobileSetting?mtagc=${(Madp.getChannel() === '0APP') ? '31012.01.25' : '30046.14.09'}`;
    } else if (process.env.TARO_ENV === 'weapp') {
      this.onSubscribe();
      return;
    }
    if (repayTipUrl) Util.router.push(repayTipUrl);
  }

  // 还款订阅提醒
  async onSubscribe() {
    const { unSubscribedContentList } = this.state;
    const tmplIds = [];
    (unSubscribedContentList || []).forEach((element = {}) => {
      if (element.tplIdScene === 'subscribe' && element.tplId) {
        tmplIds.push(element.tplId);
      }
    });
    if (false || tmplIds.length === 0) {
      return;
    }
    // 本轮被订阅的模板id list
    const subscribedIds = [];
    const appId = getAppId();
    const channelCode = Madp.getChannel() || '';
    const finalUserId = this.userId;
    if (!finalUserId) {
      return;
    }

    requestSubscribeMessage({
      tmplIds,
      // 唤起的订阅消息模板的id的集合
      success: async (res) => {
        // 处理微信点击订阅 用户选择取消 也弹toast的问题
        let flag = false; // flag:有至少一个模板订阅成功
        for (let i = 0; i < tmplIds.length; i++) {
          if (res[tmplIds[i]] === '1') {
            flag = true;
            subscribedIds.push(tmplIds[i]); // 这次被订阅的模版
          }
        }

        console.log('接口调用成功的回调', res, flag);
        if (flag) {
          // 有至少一个模板订阅成功
          const beaconContent1 = {
            desc: '订阅成功',
            subBeaconId: 'SubscribeSuccess'
          };
          this.sendSubscribeBeacon(beaconContent1);
          const templateSubscribeInfoList = subscribedIds.map((item) => ({
            templateId: item,
            subscribeType: getTemplateType(item),
            subscribeCount: 1,
            subscribeStatus: '0'
          }));
          templateSubscribe({
            appId,
            userId: finalUserId,
            channelCode,
            templateSubscribeInfoList,
            operateType: '01'
          });
          Madp.showToast({
            title: '订阅成功',
            icon: 'success',
            duration: 2000
          });
          this.isShowRepayTip('success', unSubscribedContentList, subscribedIds);
        } else {
          // 取消订阅
          const beaconContent1 = {
            desc: '取消订阅',
            subBeaconId: 'SubscribeCancel'
          };
          this.sendSubscribeBeacon(beaconContent1);
          this.isShowRepayTip('cancel', unSubscribedContentList);
        }
      },
      fail: (res) => {
        // 支付宝用户取消订阅 不弹toast
        if (res && res.behavior === 'cancel') {
          const beaconContent1 = {
            desc: '取消订阅',
            subBeaconId: 'SubscribeCancel'
          };
          this.sendSubscribeBeacon(beaconContent1);
          return;
        }
        // 微信长期id取消订阅 不弹toast
        if (res && res.errMsg === 'requestSubscribeMessage:fail cancel') {
          const beaconContent1 = {
            desc: '微信用户取消订阅长期模板',
            subBeaconId: 'WeChatLongSubscribeCancel'
          };
          this.sendSubscribeBeacon(beaconContent1);
          return;
        }
        if (res && res.errorCode === 15) {
          // 当前有活跃的订阅授权窗口，
          console.log('当前有活跃的订阅授权窗口');
          return;
        }
        const beaconContent = {
          desc: '订阅失败',
          subBeaconId: 'SubscribeFail'
        };
        this.sendSubscribeBeacon(beaconContent);
        console.log('接口调用失败的回调', res);
        Madp.showModal({
          title: '订阅失败',
          content: '请检查通知开关是否开启或联系招联客服',
          cancelText: '暂不',
          confirmText: '去开启',
          success: (modalRes) => {
            if (modalRes.confirm) {
              this.goSetting();
            }
          }
        });
      },
      complete: (res) => {
        console.log('接口调用结束的回调', res);
      }
    }).then();
  }

  /**
   * 判断是否展示还款提醒入口（消息订阅）
   * 1、存在未订阅的模板，且未订阅的模板不是已被拒绝才展示还款提醒订阅入口。
   * 2、未订阅模板包括两种（压根就没有触发过订阅；触发订阅但拒绝了）
   * @param {*} type
   * @param {*} unSubscribedContentList
   * @param {*} subscribedIds
   */
  isShowRepayTip = async (type, unSubscribedContentList, subscribedIds) => {
    const { repayGuideInfo = {} } = this.state;
    // 需将订阅成功的模块从未订阅的模版中过滤出来
    const newUnSubscribedContentList = type === 'success' ? (unSubscribedContentList || []).filter((item) => !(subscribedIds || []).includes(item.tplId)) : unSubscribedContentList;
    const tplIdsInSetting = await getSetting();
    const hasUnrefusedSubscribedIds = hasUnrefused(newUnSubscribedContentList, tplIdsInSetting); // 是否有未被拒绝的订阅弹窗的模版（未被拒绝就能订阅）
    if (type === 'init') {
      return newUnSubscribedContentList && newUnSubscribedContentList.length > 0 && hasUnrefusedSubscribedIds;
    } else if ((unSubscribedContentList || []).length <= 0 || !hasUnrefusedSubscribedIds) {
      this.setState({
        unSubscribedContentList,
        repayGuideInfo: {
          ...repayGuideInfo,
          guideList: repayGuideInfo.guideList && repayGuideInfo.guideList.filter((item) => item.type !== 'Tip')
        }
      });
    }
  }

  goSetting() {
    // 跳转到消息设置页面
    if (mnp()) {
      mnp().openSetting();
    }
  }

  // 根据展位返回的模版ID，获取微信小程序尚未订阅的消息模版
  getUnSubscribedContentList = async (init) => {
    const finalUserId = this.userId;
    const appId = getAppId();
    const channelCode = Madp.getChannel() || '';

    // 查询已经订阅的消息模版
    const subscribedIdList = await getSubscribedIdList(finalUserId, appId, channelCode);
    const {
      subscribeBanner
    } = this.state;
    const {
      contentList = []
    } = (subscribeBanner || {}).dataObj || {};
    const unSubscribedContentList = FilterContentList(contentList, subscribedIdList);
    this.setState({
      unSubscribedContentList
    });

    if (init) return unSubscribedContentList;
  };

  sendSubscribeBeacon = (beaconContent) => {
    const { desc, subBeaconId } = beaconContent || {};
    dispatchTrackEvent({ target: this, event: 'EV', beaconId: `Subscribe${subBeaconId ? `.${subBeaconId}` : ''}`, beaconContent: { cus: { desc } } });
  }

  // 还款指南信息组装
  async getRepayGuideInfo(recommendData) {
    if (((recommendData || {}).recommendQuestions || []).length <= 0) return;
    const repayGuideInfo = {};
    repayGuideInfo.title = '还款指南';
    repayGuideInfo.recommendFaqs = recommendData.recommendQuestions;
    repayGuideInfo.engineTitle = '有问题咨询我';
    repayGuideInfo.guideList = [];
    // 支付宝自动代扣
    const { deductStatus } = this.state;
    if (!(deductStatus || {}).hasAlipayDeduct) {
      // 当前渠道支持签约支付宝代扣，才引导绑定支付宝
      if (this.signedAlipayWithholding === 'Y') {
        repayGuideInfo.guideList.push({
          type: 'Deduct',
          guideTitle: '支付宝自动还款',
          guideCall: async () => {
            // 因为协议名称需读配置，顾需提前获取协议信息
            await this.getContractList();
            this.setState({ showAutoRepayDrawer: true });
          },
        });
      }
    }
    // 指定渠道、环境开放，APP、微信、联通
    if (isMuapp() || isWechat() || (process.env.TARO_ENV === 'weapp') || (Madp.getChannel() === '3CUAPP')) {
      repayGuideInfo.guideList.push({
        type: 'Steward',
        guideTitle: '添加专属管家',
        guideCall: () => this.addSteward(),
      });
    }
    // app、微信小程序添加还款提醒
    if (isMuapp()) {
      repayGuideInfo.guideList.push({
        type: 'Tip',
        guideTitle: '添加还款提醒',
        guideCall: () => this.addRepayTip(),
      });
    } else if (process.env.TARO_ENV === 'weapp') {
      const unSubscribedContentList = await this.getUnSubscribedContentList(true);
      if (await this.isShowRepayTip('init', unSubscribedContentList)) {
        repayGuideInfo.guideList.push({
          type: 'Tip',
          guideTitle: '添加还款提醒',
          guideCall: () => this.addRepayTip(),
        });
      }
    }
    this.setState({
      repayGuideInfo,
    });
  }

  componentWillUnmount() {
    // 兜底清一下定时器
    if (this.buttonShowTimer) {
      clearTimeout(this.buttonShowTimer);
      this.buttonShowTimer = null;
    }
    // 中断发起的停留交互运营
    if (this.stayTimer) {
      this.stayTimer = null;
    }
  }

  get statusCardInfoNew() {
    const { repayStatus, totalAmount, courtCostBalance } = this.state;
    const { repayDate } = getStore('nearBills');
    let info = {};
    // 逾期打标，但是代还款为0的用户，不做展示
    if (repayStatus === repayStatusType.dueTagCust && Number(totalAmount) > 0) {
      const {
        warningIcon, warningText, warningGuideText
      } = this.getWarningInfo('dueTagCust');
      info = {
        title: '待还金额(元)',
        noTitleGuide: true,
        amount: totalAmount,
        warningIcon,
        warningText,
        warningGuideText,
        warningGuideCall: warningGuideText === '逾期影响' ? this.toOverDueEffect : () => { },
        btnText: '去还款',
        nextStep: Number(courtCostBalance) === Number(totalAmount)
          ? () => this.toRepaymentCourtOnly() : () => this.toRepayment(), // 识别仅剩法诉费场景
      };
    } else if (repayStatus === repayStatusType.inSeven) {
      const repayDateCollection = Util.getDateCollection(repayDate);
      const {
        warningIcon, warningText, warningGuideText
      } = this.getWarningInfo('inSeven');
      info = {
        title: `${repayDateCollection[1]}月${repayDateCollection[2]}日应还(元)`,
        titleCall: () => this.goNearPay(),
        amount: totalAmount,
        warningIcon,
        warningText,
        warningGuideText,
        warningGuideCall: warningGuideText === '去开通' ? () => this.toDeduct('bank') : () => { },
        btnText: '去还款',
        nextStep: () => this.toRepayment()
      };
    } else if (repayStatus === repayStatusType.inSevenMutil) {
      const {
        warningIcon, warningText, warningGuideText
      } = this.getWarningInfo('inSevenMutil');
      info = {
        title: '当前可还(元)',
        titleCall: () => this.goNearPay(),
        amount: totalAmount,
        warningIcon,
        warningText,
        warningGuideText,
        warningGuideCall: warningGuideText === '去开通' ? () => this.toDeduct('bank') : () => { },
        btnText: '去还款',
        nextStep: () => this.toRepayment()
      };
    } else if (repayStatus === repayStatusType.today) {
      const {
        warningIcon, warningText, warningGuideText
      } = this.getWarningInfo('today');
      info = {
        title: '今日应还(元)',
        titleCall: () => this.goNearPay(),
        amount: totalAmount,
        warningIcon,
        warningText,
        warningGuideText,
        warningGuideCall: warningGuideText === '去开通' ? () => this.toDeduct('bank') : () => { },
        btnText: '去还款',
        nextStep: () => this.toRepayment()
      };
    } else if (repayStatus === repayStatusType.outSeven) {
      const repayDateCollection = Util.getDateCollection(repayDate);
      const date = new Date(repayDateCollection.join('-'));
      const {
        warningIcon, warningText, warningGuideText
      } = this.getWarningInfo('outSeven');
      // 不能直接处理，因为date.setDate返回的是毫秒
      date.setDate(date.getDate() - 7);
      const canPayDateCollection = Util.getDateCollection(date);
      info = {
        title: `${repayDateCollection[1]}月${repayDateCollection[2]}日应还(元)`,
        titleCall: () => this.goFuturePay(repayDateCollection.join('-')),
        amount: totalAmount,
        warningIcon,
        warningText,
        warningGuideText,
        warningGuideCall: warningGuideText === '去开通' ? () => this.toDeduct('bank') : () => { },
        btnText: `${canPayDateCollection[1]}月${canPayDateCollection[2]}日后可还`,
        disabledBtn: true
      };
    } else if (repayStatus === repayStatusType.overDue) {
      const {
        warningIcon, warningText, warningGuideText
      } = this.getWarningInfo('overDue');
      info = {
        title: '待还金额(元)',
        titleCall: () => this.goNearPay(),
        amount: totalAmount,
        warningIcon,
        warningText,
        warningGuideText,
        warningGuideCall: warningGuideText === '逾期影响' ? this.toOverDueEffect : () => { },
        btnText: '去还款',
        nextStep: () => this.toRepayment()
      };
    } else if (repayStatus === repayStatusType.empty && Number(courtCostBalance) > 0) {
      // 仅剩法诉费待还
      info = {
        title: '待还金额(元)',
        noTitleGuide: true,
        amount: courtCostBalance,
        btnText: '去还款',
        nextStep: () => this.toRepaymentCourtOnly()
      };
    }
    return info;
  }

  get getExtraParam() {
    const {
      repayStatus
    } = this.state;
    return {
      hasBill: Number(this.advancedBillCounts) > 0,
      overdue: repayStatus === repayStatusType.overDue
        || repayStatus === repayStatusType.dueTagCust,
      dueTagCust: repayStatus === repayStatusType.dueTagCust,
      overSeven: repayStatus === repayStatusType.outSeven,
      needLogin: 1
    };
  }

  /**
   * @description: 保存客服组件参数
   * @param { Boolean } showChat 开始加载客服入口
   */
  setExtraParam(showChat) {
    const {
      repayStatus
    } = this.state;
    setStore({
      chatEntryExtraParam: {
        hasBill: Number(this.advancedBillCounts) > 0,
        overdue: repayStatus === repayStatusType.overDue
          || repayStatus === repayStatusType.dueTagCust,
        dueTagCust: repayStatus === repayStatusType.dueTagCust,
        overSeven: repayStatus === repayStatusType.outSeven,
        needLogin: 1
      }
    });
    this.setState({
      showChat: !!showChat
    });
    // 发送客服组件参数准备完毕的消息
    Madp.eventCenter.trigger('CHAT_ENTRY_EXTRAPARAM');
  }

  opActivityCouponDialogCallback(type) {
    if (type === 'hideDialog') {
      const repayStatus = getStore('repayStatus');
      const status = repayStatus;
      const userStatus = this.transformStatus(status);
      const { publicDetainDialog = {} } = this.state;
      if (publicDetainDialog && publicDetainDialog.dataObj && publicDetainDialog.dataObj.contentList && publicDetainDialog.dataObj.contentList.length > 0) {
        refresh({
          pageId: RepaymentIndexPageId,
          publicDetainDialog: {
            isOpen: true,
            handler: (data, targetUrl, next) => {
              this.detainPopWindowHandler(data, targetUrl, next, userStatus);
            },
          },
          isRequest: false
        });
        this.showPublicDetainDialog = true;
      }
    }
  }

  // 逾期天数
  get overdueDay() {
    let overdueDay = 0;
    const {
      isDueTagCust
    } = getStore('nearBills');
    // 区分打标和非打标用户
    if (isDueTagCust === 'Y') {
      (this.nearBills.repayBillList || []).forEach((bill) => {
        if (overdueDay < bill.overdueDays) {
          overdueDay = bill.overdueDays;
        }
      });
    } else {
      (this.nearBills.repayBillList || []).forEach((bill) => {
        if (overdueDay < bill.displayOverdueDays) {
          overdueDay = bill.displayOverdueDays;
        }
      });
    }
    return overdueDay;
  }

  // 转让客户跳转交易记录
  jumpTradeRecords = () => {
    Madp.setStorageSync('repayJumpOut', 'Y', 'SESSION');
    Util.router.push(repayServiceColumnsUrls['Traderecords']);
  }

  protocalAction = async () => {
    const { contractInfo } = this.state;
    this.setState({
      contractInfo: {
        ...contractInfo,
        showContract: true,
      }
    });
  }

  getContractList = async () => {
    const { contractInfo } = this.state;
    if (contractInfo && contractInfo.list && contractInfo.list.length) return;
    // tbs没法区分新旧合同，现在先从ubs拿合同配置，等全部切3.0后再直接从tbs取（this.contractInfoList）
    const { ret, data } = await Dispatch.repayment.queryContractInfo({
      scene: 'CONFIG',
      contractConfScene: 'ADD_ALIPAY_ACCOUNT',
      interfaceVersion: '3.0'
    }) || {};
    if (ret !== '0') return;
    const { contractConfigInfoList = [] } = data || {};
    const contractList = [];
    let contractText = '';
    await Promise.all((contractConfigInfoList || []).map(async (contract) => {
      const { interfaceVersion, contractCustShortName, contractName } = contract || {};
      if (interfaceVersion === '3.0') {
        contractText = contractText ? `${contractText}、${contractCustShortName}` : contractCustShortName;
      } else {
        contractText = contractText ? `${contractText}、${contractName}` : contractName;
      }
      const contractContent = await this.getContractContent(contract) || {};
      contractList.push(contractContent);
    }));

    this.setState({
      contractInfo: {
        contractText,
        list: contractList,
      }
    });
  }

  getContractContent = async (contract) => {
    const {
      interfaceVersion, contractCategory,
      contractVersion, contractEdition,
      contractType, contractCode,
      needCustSignature, needCompanySignature,
      agreementTitle, agreementSummary,
      contractName, contractCustName,
      contractCustShortName,
    } = contract || {};

    let contractParams = {
      contractVersion,
      contractCategory,
    };

    const { custName = '' } = await getLoginInfo() || {};
    const date = new Date();
    const [year, month, day] = [date.getFullYear(), date.getMonth() + 1, date.getDate()];

    const contractData = {
      contractName,
      contractCustName,
      contractCustShortName,
      needCustSignature,
      needCompanySignature,
      agreementTitle,
      agreementSummary,
      // 支付扣款协议银行名称参考卡管理页面写死...
      bankName: 'XXXXXXXXX',
      accountName: custName,
      accountNo: '*****XXXX',
      yearNow: year,
      monthNow: month,
      dayNow: day,
    };

    if (interfaceVersion === '3.0') {
      contractParams = {
        contractCode,
        contractPreviewData: {
          baseContractInfo: {
            signDate: `${year}${`0${month}`.slice(-2)}${`0${day}`.slice(-2)}`,
          },
          bankCardInfo: {
            bankName: 'XXXXXXXXX',
            accountName: custName,
            accountNo: '*****XXXX',
          },
          needCustSignature,
          needCompanySignature,
        },
        ...contractParams,
      };
    } else {
      contractParams = {
        contractEdition,
        contractData,
        ...contractParams,
        ...(contractType ? { contractTypeList: [contractType] } : {}),
      };
    }

    const res = await Dispatch.repayment.queryContractInfo({
      scene: 'PREVIEW',
      interfaceVersion,
      ...contractParams,
    }) || {};
    const { contractList } = (res && res.data) || {};

    const contractContent = {
      title: contractCustName || contractName,
      htmlFile: ((contractList || [])[0] || {}).htmlFile || '',
    };

    return contractContent;
  }

  closeAgreementDrawer = () => {
    const { contractInfo } = this.state;
    this.setState({
      contractInfo: {
        ...contractInfo,
        showContract: false
      }
    });
  }

  // 剪切板
  setClipboardTextCall = async () => {
    // 如果有圣约弹窗，则展示圣约弹窗，不拷贝剪切板
    const { showCovenantBubble } = this.state;
    if (showCovenantBubble) {
      const eventCodeMap = {
        overdueRepay: EVENT_CODE_MAP.repayIndexOverDueOpen,
        normalRepay: EVENT_CODE_MAP.repayIndexOpen,
        advanceRepay: EVENT_CODE_MAP.repayIndexAdvanceOpen,
      };
      this.opOnPageEvent('opBubbleClick', eventCodeMap[this.repaySence]);
      return;
    }
    if (Madp.getChannel() !== '0APP') {
      await Madp.setClipboardText({
        text: `${clipboardUrl}&clickPosition=indexRandomReduce`,
        success: async () => {
          dispatchTrackEvent({
            target: this,
            event: EventTypes.EV,
            beaconId: 'RandomReduceClipSuccess',
          });
        },
        fail: () => {
          dispatchTrackEvent({
            target: this,
            event: EventTypes.EV,
            beaconId: 'RandomReduceClipFail',
          });
        }
      });
    }
  }

  // 是否展示V+高价值会员提示，展示V+会员需满足以下四个条件
  // a.待还账单均正常，无逾期借据
  // b.客户为V+会员
  // c.待还账单中的所有借据宽限期截止日(inteDate)-到期日(payDate)>=10天
  // d.当前系统日期在（min（待还账单中所有借据的到期日），max（待还账单中所有借据的宽限期截止日）]
  get isShowVPlusTips() {
    const { overDueTagFlag, repayBillList } = getStore('nearBills');
    if (!repayBillList || repayBillList.length <= 0) return false;
    const { isVPlus } = this.state;
    // 逾期打标或者逾期天数大于宽限期都认为不满足
    let realOverDueFlag = false;
    repayBillList.forEach((bill) => {
      if (bill.surplusDays < 0 && Util.timeMinus(bill.inteDate, bill.payDate, 'day', 'date') < -bill.surplusDays) {
        realOverDueFlag = true;
      }
    });
    if (realOverDueFlag || overDueTagFlag) return false;
    if (!isVPlus) return false;
    const timeGapOverTen = repayBillList.every((bill) => {
      return Util.timeMinus(bill.inteDate, bill.payDate, 'day', 'date') >= 10;
    });
    if (!timeGapOverTen) return false;
    let isBetPayAndInte = false;
    const nowDate = new Date().getTime();
    if (repayBillList && repayBillList.length > 0) {
      let minPayDate = repayBillList[0].payDate;
      let maxInteDate = repayBillList[0].inteDate;
      this.showInteDate = repayBillList[0].inteDate;
      repayBillList.forEach((bill, i) => {
        if (i > 0 && Util.timeMinus(minPayDate, bill.payDate, 'day', 'date') > 0) {
          minPayDate = bill.payDate;
        }
        if (i > 0 && Util.timeMinus(bill.inteDate, maxInteDate, 'day', 'date') > 0) {
          maxInteDate = bill.inteDate;
        }
        if (i > 0 && Util.timeMinus(this.showInteDate, bill.inteDate, 'day', 'date') > 0) {
          this.showInteDate = bill.inteDate;
        }
      });
      if (Util.timeMinus(nowDate, new Date(minPayDate.split('.').join('-')).getTime()) > 1
        && Util.timeMinus(nowDate, new Date(maxInteDate.split('.').join('-')).getTime()) <= 1
      ) {
        isBetPayAndInte = true;
      }
    }
    return isBetPayAndInte;
  }

  // 非打标用户逾期且超过宽限期
  get realOverDueFlag() {
    const { repayBillList } = getStore('nearBills');
    if (!repayBillList || repayBillList.length <= 0) return false;
    let realOverDueFlag = false;
    repayBillList.forEach((bill) => {
      if (bill.overdueDays > 0 && Util.timeMinus(bill.inteDate, bill.payDate, 'day', 'date') < bill.overdueDays) {
        realOverDueFlag = true;
      }
      if (bill) {
        if (bill.waivePayFineAmt && Number(bill.waivePayFineAmt) > 0) {
          this.waivePayAmt = Util.floatAdd(Number(this.waivePayAmt), Number(bill.waivePayFineAmt));
        }
        if (bill.waivePayInteAmt && Number(bill.waivePayInteAmt) > 0) {
          this.waivePayAmt = Util.floatAdd(Number(this.waivePayAmt), Number(bill.waivePayInteAmt));
        }
        if (bill.waivePayOnetimeFee && Number(bill.waivePayOnetimeFee) > 0) {
          this.waivePayAmt = Util.floatAdd(Number(this.waivePayAmt), Number(bill.waivePayOnetimeFee));
        }
        if (bill.waivePayPeriodFee && Number(bill.waivePayPeriodFee) > 0) {
          this.waivePayAmt = Util.floatAdd(Number(this.waivePayAmt), Number(bill.waivePayPeriodFee));
        }
        if (bill.waivePayPrepayFee && Number(bill.waivePayPrepayFee) > 0) {
          this.waivePayAmt = Util.floatAdd(Number(this.waivePayAmt), Number(bill.waivePayPrepayFee));
        }
      }
    });
    return realOverDueFlag;
  }

  get showRandomReduce() {
    let randomReduceFlag = false;
    const { showCovenantBubble, repayStatus, courtCostBalance } = this.state;
    if (showCovenantBubble) {
      return true;
    }
    if (repayStatus !== repayStatusType.dueTagCust
      && (repayStatus === repayStatusType.inSeven
        || repayStatus === repayStatusType.inSevenMutil
        || repayStatus === repayStatusType.today
        || repayStatus === repayStatusType.outSeven
        || repayStatus === repayStatusType.overDue)
      && !this.realOverDueFlag
      && this.randomReduceText
      && (Number(courtCostBalance || 0) <= 0)) {
      randomReduceFlag = true;
    }
    return randomReduceFlag;
  }

  get getCustStatus() {
    let custStatus = '';
    const repayStatus = getStore('repayStatus');
    // 首页头部卡片展示状态
    let formatStatus = repayStatusType.loading;
    if (this.advancedBillCounts && repayStatus === repayStatusType.loading) {
      formatStatus = repayStatusType.clear;
    } else if (repayStatus === repayStatusType.loading) {
      formatStatus = repayStatusType.empty;
    } else if (repayStatus) {
      formatStatus = repayStatus;
    }
    switch (formatStatus) {
      case repayStatusType.empty:
        custStatus = 'Empty';
        break;
      case repayStatusType.outSeven:
        custStatus = 'OutSeven';
        break;
      case repayStatusType.inSeven:
      case repayStatusType.inSevenMutil:
      case repayStatusType.today:
        custStatus = 'InSeven';
        break;
      case repayStatusType.clear:
        custStatus = 'Clear';
        break;
      case repayStatusType.overDue:
        custStatus = 'OverDue';
        break;
      case repayStatusType.dueTagCust:
        custStatus = 'DueTag';
        break;
      default:
        break;
    }
    const { overDueTagFlag, d07Flag } = getStore('nearBills');
    if (d07Flag && !overDueTagFlag && !this.realOverDueFlag) {
      custStatus = 'D07Tag';
    }
    const { negotiateRepayTaskBill } = this.indexStore;
    if (Object.keys(negotiateRepayTaskBill || {}).length > 0) {
      custStatus = 'Cousult';
    }
    return custStatus;
  }

  get availShowButtons() {
    const { pageAvailShowButtons, defaultAvailShowButtons } = this.state;
    return (pageAvailShowButtons || defaultAvailShowButtons);
  }

  render() {
    const {
      repayStatus, futurePlanList, deductStatus,
      showChat, courtCostBalance, transferStatus, availRepayWaiveFlag, repayGuideInfo, showAutoRepayDrawer, contractInfo, showUnicomBillTip
    } = this.state;

    const businessPluginHeaderData = {
      stateData: this.state
    };
    const businessPluginMiddleData = {
      stateData: this.state
    };
    const businessPluginFooterData = {
      stateData: this.state,
      themeColor
    };
    if (this.showRandomReduce) dispatchTrackEvent({
      target: this,
      event: EventTypes.SO,
      beaconId: 'ShowRandomReduce',
      beaconContent: { cus: { channel: Madp.getChannel() } }
    });
    const { negotiateRepayTaskBill } = this.indexStore;
    const transferDateFormat = `${((this.transferInfo || {}).transferDate || '').substring(0, 4)}年${((this.transferInfo || {}).transferDate || '').substring(4, 6)}月${((this.transferInfo || {}).transferDate || '').substring(6)}日`;
    const showFuturePlanFlag = (repayStatus !== repayStatusType.dueTagCust) && this.isPageDataReady && (!negotiateRepayTaskBill.showNegotiatePlan) && (futurePlanList.length > 0);
    const { deductTime } = deductStatus || {};
    const { overDueTagFlag } = getStore('nearBills');

    if (transferStatus) {
      return (
        <MUView>
          <MUNavBarWeapp
            className="loan-navbar"
            title="还款"
            leftArea={[
              {
                type: 'icon',
                value: 'back',
              }
            ]}
          />
          <MUView className="pages-bg repayment-index">
            <MUView className="transfer-cust">
              <MUView className="transfer-cust__content">
                <MUView className="content__title">{transferDateFormat} 已转让</MUView>
                <MUView className="content__amount">{Number((this.transferInfo || {}).transferTotalBalance || 0).toFixed(2)}元</MUView>
                <MUView className="content__explain">
                  因您长期逾期未还，您在招联的欠款已转至<MUText className="content__explain--orange">{(this.transferInfo || {}).transferOrgName}</MUText>(该公司依法成立，受当地金融监督管理局监管，监督电话<MUText className="brand-text">{(this.transferInfo || {}).regulationOrgPhoneNo}</MUText>)，如需协商还款请联系<MUText className="brand-text">{(this.transferInfo || {}).transferMerchantPhoneNo}</MUText>
                </MUView>
                <MUView className="content__description">
                  <MUText className="content__description--orange">若已完成还款，可忽略上述金额</MUText>
                </MUView>

              </MUView>
              <MUView className="transfer-cust__guide-trade">
                <MUView className="guide-trade__left">
                  <MUView className="guide-trade__left__img">
                    <MUImage src={tradeRecordsGuide} />
                  </MUView>
                  <MUView className="guide-trade__left__text">
                    借还记录
                  </MUView>
                </MUView>
                <MUView
                  className="guide-trade__right"
                  beaconId="TransferTradeRecords"
                  onClick={this.jumpTradeRecords}
                >
                  <MUIcon value="arrow-right" size={16} />
                </MUView>
              </MUView>
            </MUView>
          </MUView>
        </MUView>
      );
    }

    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="还款"
          leftArea={[
            {
              type: 'icon',
              value: 'back',
              onClick: this.beforeMiniRouteLeave
            }
          ]}
        />
        <MUView className="pages-bg repayment-index">
          <MUView className={isMuapp() ? 'repayment-index-content repayment-index-content--safe' : 'repayment-index-content'} style={`min-height: ${ChannelConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
            <BusinessPlugin moduleName="repayment" pageType="pageIndex" type="header" data={businessPluginHeaderData} />
            <MUView className="repayment-index-header">
              {/* goTo传参前加一个判空，因为goto会根据入参判断，为空时不展示，若直接传入包裹函数会让goto一直展示 */}
              <RepayStatusCardNew
                statusType={(negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}') ? 'consult' : repayStatus}
                availLoanFlag={this.availLoanFlag}
                availLoanWaiveFlag={this.availLoanWaiveFlag}
                statusTitle={this.statusCardInfoNew.title}
                titleCall={this.statusCardInfoNew.titleCall && (() => this.onStatusCardClick(this.statusCardInfoNew.titleCall, 'BillCount'))}
                noTitleGuide={this.statusCardInfoNew.noTitleGuide}
                statusAmount={this.statusCardInfoNew.amount}
                courtCostBalance={courtCostBalance}
                warningIcon={this.statusCardInfoNew.warningIcon}
                warningTextObj={this.statusCardInfoNew.warningText}
                overdueFlag={overDueTagFlag || Number(this.overdueDay || 0) > 0}
                warningGuideText={this.statusCardInfoNew.warningGuideText}
                warningGuideCall={this.statusCardInfoNew.warningGuideCall}
                showVPlusTips={this.isShowVPlusTips}
                showInteDate={this.showInteDate}
                disabledBtn={this.statusCardInfoNew.disabledBtn}
                btnText={this.statusCardInfoNew.btnText}
                nextStep={this.statusCardInfoNew.nextStep && (() => this.onStatusCardClick(this.statusCardInfoNew.nextStep, 'ToRepay'))}
                availRepayWaiveFlag={availRepayWaiveFlag && (Number(this.overdueDay || 0) > 0)}
                showRandomReduce={this.showRandomReduce && (!this.waivePayAmt || Number(this.waivePayAmt) <= 0)}
                randomReduceText={this.randomReduceText}
                showServiceBtn={Object.keys(this.recommendService || {}).length}
                recommendService={this.recommendService}
                serviceStep={this.statusCardInfoNew.serviceStep}
                negotiateRepayTaskBill={negotiateRepayTaskBill}
                consultStep={(scene) => this.toRepayment(scene)}
                trackPage="repayment.RepaymentIndex."
                custStatus={this.getCustStatus}
                availShowButtons={this.availShowButtons}
                setClipboardTextCall={() => this.setClipboardTextCall()}
              />
              {showFuturePlanFlag ? (
                <MUList className="repayment-index-list">
                  <MUListItem
                    beaconId="ToAdvanceRepay"
                    beaconContent={{ cus: { custStatus: this.getCustStatus } }}
                    className="repayment-index-list-futurePlan-listItem repay-entrance"
                    title="还款计划"
                    extraText={`剩余${futurePlanList.length}期`}
                    arrow="mu-icon-arrow-right"
                    hasBorder={false}
                    thumb={FuturePlan}
                    onClick={() => this.WaitPayPlanRef.show()}
                  />
                  <MUView className="repayment-future-plan">
                    <WaitPayPlan
                      ref={(ref) => { this.WaitPayPlanRef = ref; }}
                      planList={futurePlanList}
                      showWaitPayPlanDefault={Url.getParam('showRepayPlan') === '1' && Madp.getStorageSync('hideRepayPlan', 'SESSION') !== 'Y'}
                      trackPage="repayment.RepaymentIndex."
                      custStatus={this.getCustStatus}
                      deductTime={deductTime}
                    />
                  </MUView>
                </MUList>
              ) : null}
              {(this.isPageDataReady && negotiateRepayTaskBill.showNegotiatePlan) ? (
                <MUList className="repayment-index-list">
                  <MUListItem
                    beaconId="ToAdvanceRepay"
                    beaconContent={{ cus: { custStatus: this.getCustStatus } }}
                    className="repayment-index-list-futurePlan-listItem repay-entrance"
                    title="协商还计划"
                    extraText={`共${negotiateRepayTaskBill.repayPlanList.length}期`}
                    arrow="mu-icon-arrow-right"
                    hasBorder={false}
                    thumb={FuturePlan}
                    onClick={() => this.WaitPayPlanRef.show()}
                  />
                  <MUView className="repayment-future-plan">
                    <WaitPayPlan
                      ref={(ref) => { this.WaitPayPlanRef = ref; }}
                      planList={negotiateRepayTaskBill.repayPlanList}
                      scene="consult"
                      waiveRatio={negotiateRepayTaskBill.waiveRatio}
                      showWaitPayPlanDefault={Url.getParam('showRepayPlan') === '1' && Madp.getStorageSync('hideRepayPlan', 'SESSION') !== 'Y'}
                      trackPage="repayment.RepaymentIndex."
                      custStatus={this.getCustStatus}
                      deductTime={deductTime}
                    />
                  </MUView>
                </MUList>
              ) : null}
            </MUView>
            {this.businessColumns && this.businessColumns.length > 0 ? (
              <BusinessColumns businessColumns={this.businessColumns} havePrepayFeeRightsCoupon={this.havePrepayFeeRightsCoupon} custStatus={this.getCustStatus} />
            ) : null}
            {this.showDiversionInfoCard ? (
              <DiversionInfoCard
                diversionInfo={this.diversionBillInfo}
                themeColor={themeColor}
                trackPage="repayment.RepaymentIndex."
              />
            ) : (
              <BusinessPlugin moduleName="repayment" pageType="pageIndex" type="middle" data={businessPluginMiddleData} />
            )}
            {Object.keys(repayGuideInfo || {}).length ? (
              <RepayGuide
                repayGuideInfo={repayGuideInfo}
                entrance="HKYHKZN"
                themeColor={themeColor}
                trackPage="repayment.RepaymentIndex."
                custStatus={this.getCustStatus}
              />
            ) : null}
            <AutoRepayDrawer
              onlyBindAlipay
              showAutoRepayDrawer={showAutoRepayDrawer}
              agreeBindAlipay={() => this.toDeduct('alipay')}
              cancelBind={() => this.setState({ showAutoRepayDrawer: false })}
              viewContract={this.protocalAction}
              contractInfo={contractInfo}
            />
            <AgreementDrawer
              agreementViewProps={{
                type: 1,
                list: contractInfo.list,
                current: 0,
              }}
              show={contractInfo.showContract}
              totalCount={0}
              close={this.closeAgreementDrawer}
              submit={() => this.toDeduct('alipay')}
              height="67%"
            />
            <BusinessPlugin
              moduleName="repayment"
              pageType="pageIndex"
              type="footer"
              data={businessPluginFooterData}
              fns={{
                handlePrivilegeGuideDialogDuriedPoint: (type) => {
                  dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `APPSpecialRight${type}` });
                },
                // handlerActivityCouponDialog: (type) => {
                //   this.handleCoupon(type);
                // }
              }}
            />
          </MUView>
          {showChat && Util.showChatEntry() && <ChatEntry busiEntrance={Util.getBusiEntrance()} extraParam={this.getExtraParam} themeColor={themeColor} />}
          <MUModal
            isOpened={showUnicomBillTip}
            content={this.unicomBillTipText}
            beaconId="UnicomBillTipModal"
            closeOnClickOverlay={false}
            confirmText="修改还款"
            onConfirm={() => {
              this.setState({ showUnicomBillTip: false });
              this.goNearPay();
            }}
            cancelText="继续还款"
            onCancel={() => {
              this.setState({ showUnicomBillTip: false });
              this.toRepayment('', true);
            }}
          />
          <OpRepayment pageId={RepaymentIndexPageId} opEventKey="opPageEnter" />
          <OpRepayment pageId={RepaymentIndexPageId} opEventKey="opRepayClick" />
          <OpRepayment pageId={RepaymentIndexPageId} opEventKey="opPageLeave" />
          <OpRepayment pageId={RepaymentIndexPageId} opEventKey="opPageStay" />
          <OpRepayment pageId={RepaymentIndexPageId} opEventKey="opBubbleClick" />
        </MUView>
      </MUView>
    );
  }
}
