/* eslint-disable no-unused-expressions */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-empty */
/* eslint-disable indent */
/* eslint-disable react/sort-comp */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable max-len */

import { Component } from '@tarojs/taro';
import {
  MUView, MUModal, MUNavBarWeapp
} from '@mu/zui';
import Madp from '@mu/madp';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import { repayStatusType } from '@utils/constants';
import { getStore, setStore } from '@api/store';
import pageHoc from '@utils/pageHoc';

// import './index.scss';

@track({ event: EventTypes.PO }, {
  pageId: 'DispatchPage',
  dispatchOnMount: true,
})
// 放在track后面，不然track好像会被覆盖，报页面id未配置
@pageHoc({ title: '还款' })
export default class DispatchPage extends Component {
  config = {
    navigationBarTitleText: '还款',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  };

  constructor(props) {
    super(props);
    this.state = {
      showModal: false,
    };
    this.nearBills = {}; // 七天待还接口返回的数据
    this.allBills = {}; // 提前结清接口返回的数据
    this.repayPlanResumeDate = ''; // 剩余每月待还模块中，用于续传的日期
    this.nearBillList = []; // 近期待还list
    this.advancedBillList = []; // 全部待还list
    this.futurePlanList = []; // 未来期list
    this.totalAmount = 0; // 七天可还账单金额
    this.repayStatus = repayStatusType.loading, // 默认展示loading
      this.advancedBillCounts = 0, // 全部待还账单数
      this.statusBeaconPrefix = {
        [repayStatusType.empty]: 'Empty',
        [repayStatusType.inSeven]: 'InSeven',
        [repayStatusType.today]: 'InSeven',
        [repayStatusType.inSevenMutil]: 'InSeven',
        [repayStatusType.outSeven]: 'OutSeven',
        [repayStatusType.overDue]: 'OverDue',
        [repayStatusType.dueTagCust]: 'DueTag',
        [repayStatusType.clear]: 'Clear',
      };
    this.aliAppHome = {
      appId: '2021002195628455',
      path: '/pages/index/index'.replace('/repayment', ''),
    };
  }

  async componentDidMount() {
    await this.initData();
  }

  initData = async () => {
    await Promise.all([
      Dispatch.repayment.getNewUserInfo()
    ]);
    // 获取账单和借据
    await Promise.all([
      this.getNearBillsAndAmount(),
      this.getAdvancedBills()
    ]);
    // 依赖近期待还接口
    await this.getFuturePlans();
    // 获取首页状态
    const topCardStatus = this.getTopCardStatus();
    // 存储客服拓展参数，后续页面需要使用
    this.setExtraParam();
    // 分发，异常场景均跳还款首页
    const dispatchSuccess = await this.dispatch(topCardStatus);
    if (!dispatchSuccess) {
      this.goToIndex();
    }
  }

  dispatch = async () => {
    console.log('dispatch params: ', this.$router.params);
    // 支付宝还款小程序入口只支持支付宝支付，注：不要在store文件中初始化此变量，否则会被挟持set、get
    setStore({ onlyAlipay: true });
    // 逾期打标/多还款日，跳还款首页
    if ([repayStatusType.dueTagCust, repayStatusType.inSevenMutil].indexOf(this.repayStatus) !== -1) {
      return false;
    }

    const {
      billId,
      params: dataListStr,
    } = this.$router.params;
    const dataList = (dataListStr && JSON.parse(decodeURIComponent(dataListStr || '[]'))) || [];
    const { basicCustDto } = getStore('sessionInfo') || {};
    let { custId } = basicCustDto || {};
    if (!custId) {
      const { basicCustDto: newBasicCustDto } = await Dispatch.repayment.getNewUserInfo();
      const { custId: newCustId } = newBasicCustDto || {};
      custId = newCustId || '';
    }


    // 有billId，跳账单列表页
    if (billId) {
      const nowDate = new Date();
      const { repayDate } = this.nearBills;
      const uniRepayDay = (repayDate || '').replace(/\d{4}\.\d{2}\.(\d{2})/, `${nowDate.getFullYear()}/${nowDate.getMonth() + 1 > 9 ? nowDate.getMonth() + 1 : `0${nowDate.getMonth() + 1}`}/$1`);
      const billRepayDay = billId.replace(new RegExp(`${custId}_(\\d{4})(\\d{2})(\\d{2})`), '$1/$2/$3');
      if (
        !uniRepayDay
        || +(new Date(uniRepayDay)) < +(new Date(billRepayDay))
        || this.repayStatus === repayStatusType.outSeven && +(new Date(uniRepayDay)) === +(new Date(billRepayDay))
      ) {
        this.goFuturePay(billRepayDay.replace(/\//g, '-'));
      } else {
        this.goNearPay();
      }
      return true;
    }

    let dataType;
    if (dataList.length === 0) {
      return false;
    } else if (`${dataList[0].goTotalPay}` === '1') {
      this.goTotalPay();
      return true;
    } else if (dataList[0].billId) {
      dataType = 'bill';
    } else if (dataList[0].loanId) {
      dataType = 'loan';
    } else {
      return false;
    }
    let totalAlipayAmount = 0;
    let totalAmount = 0;
    let hasFeeLoan = false;
    if (dataType === 'bill') { // 近期待还
      let repayDayList = [];
      dataList.forEach(({ billId: newBillId, repayAmount }) => {
        const repayDay = newBillId.replace(new RegExp(`${customerId}_(\\d{4})(\\d{2})(\\d{2})`), '$1.$2.$3');
        totalAlipayAmount += +repayAmount;
        repayDayList.indexOf(repayDay) === -1 && repayDayList.push(repayDay);
      });
      this.nearBillList = this.nearBillList.filter(({ payDate }) => repayDayList.indexOf(payDate) !== -1);
      this.nearBillList.forEach(({ surplusPayTotalAmt, loanType }) => {
        totalAmount += +surplusPayTotalAmt;
        if (loanType === 'F') hasFeeLoan = true;
      });
    } else { // 提前结清
      let orderNoList = [];
      dataList.forEach(({ loanId, lastPrincipalAmount }) => {
        totalAlipayAmount += +lastPrincipalAmount;
        orderNoList.indexOf(loanId) === -1 && orderNoList.push(loanId);
      });
      this.advancedBillList = this.advancedBillList.filter(({ orderNo }) => orderNoList.indexOf(orderNo) !== -1);
      this.advancedBillList.forEach(({ surplusPayTotalAmt, loanType }) => {
        totalAmount += +surplusPayTotalAmt;
        if (loanType === 'F') hasFeeLoan = true;
      });
    }
    console.log('dispatch data: ', this.nearBillList, this.advancedBillList, totalAmount, totalAlipayAmount, hasFeeLoan);
    // 有费率借据，跳还款首页
    if (hasFeeLoan) {
      return false;
    }
    // 前后金额不一致，弹窗拦截
    if ((+totalAmount).toFixed(2) !== (+totalAlipayAmount).toFixed(2)) {
      this.showBlockModal();
      return true;
    }
    this.totalAmount = (+totalAmount).toFixed(2);
    // 跳还款支付页
    await this.toRepayment(dataType);
    return true;
  }

  goToIndex = () => {
    Util.router.replace({
      path: '/pages/index/index',
      query: {
        ...this.$router.param
      },
    });
  }

  showBlockModal = () => {
    this.setState({ showModal: true });
  }

  clickModal = () => {
    Madp.navigateToMiniProgram(this.aliAppHome);
  }

  getNearBillsAndAmount = async () => {
    const { agmType } = this.$router.params;
    const data = await Dispatch.repayment.getNearBills(
      { agmType },
      { setNearAmount: true }
    );
    this.nearBills = data;
    this.repayPlanResumeDate = data.resumeFirstDate;
    const repayStatus = getStore('repayStatus');
    if (repayStatus === repayStatusType.overDue) {
      setStore({ isOverdueCust: true });
    }
  }

  getAdvancedBills = async () => {
    this.allBills = await Dispatch.repayment.getAllBills();
    this.advancedBillList = this.allBills.advanceBillList;
    this.advancedBillCounts = this.allBills.advanceBillList.length || this.allBills.showBillList.length || 0;
  }

  getFuturePlans = async () => {
    const ret = await Dispatch.repayment.acquireFuturePlans({ beginDate: this.repayPlanResumeDate, queryType: '1' });
    let repayFutureBillDetailList = [];
    let resumeDate = '';
    if (ret.data) {
      ({ repayFutureBillDetailList, resumeDate } = ret.data);
    } else {
      ({ repayFutureBillDetailList, resumeDate } = ret);
    }
    this.repayPlanResumeDate = resumeDate;
    const futurePlan = repayFutureBillDetailList || [];
    // 由于可能返回的列表里，全是可见不可还账单
    // 需要根据每月可还金额判断是否全是可见不可还账单
    const canPayList = futurePlan.filter((plan) => plan.monthRepayAmt && Number(plan.monthRepayAmt));
    if (canPayList.length) {
      const { futurePlanList } = this;
      const updatedPlanList = futurePlanList.concat(canPayList.map((bill) => ({
        amount: bill.monthRepayAmt,
        date: Util.getDateCollection(bill.repayDate),
        canPay: bill.canPay === 'Y',
        billList: bill.monthDetailList,
      })));
      this.futurePlanList = updatedPlanList;
    }
  }

  getTopCardStatus = () => {
    const repayStatus = getStore('repayStatus');
    // 首页头部卡片展示状态
    let status = repayStatusType.loading;
    if (this.allBills.advanceBillList.length && repayStatus === repayStatusType.loading) {
      status = repayStatusType.clear;
    } else if (repayStatus === repayStatusType.loading) {
      status = repayStatusType.empty;
    } else if (repayStatus) {
      status = repayStatus;
    }
    this.nearBillList = this.nearBills.repayBillList;
    this.repayStatus = status;
    this.sendBeacon(this.statusBeaconPrefix[status], 'PO');
    return status;
  }

  setExtraParam = () => {
    const { repayStatus, advancedBillCounts } = this;
    setStore({
      chatEntryExtraParam: {
        hasBill: Number(advancedBillCounts) > 0,
        overdue: repayStatus === repayStatusType.overDue
          || repayStatus === repayStatusType.dueTagCust,
        dueTagCust: repayStatus === repayStatusType.dueTagCust,
        overSeven: repayStatus === repayStatusType.outSeven,
        needLogin: 1
      }
    });
    // 发送客服组件参数准备完毕的消息
    Madp.eventCenter.trigger('CHAT_ENTRY_EXTRAPARAM');
  }

  goTotalPay = () => {
    Util.router.replace('/pages/bill-list-all/index?isRedirect=1');
  }

  goNearPay = () => {
    const nearPayAddr = '/pages/bill-list-near/index?fromIndex=1';
    Util.router.replace({
      path: nearPayAddr,
      query: {
        isRedirect: 1,
      }
    });
  }

  goFuturePay = (repayDate) => {
    const { repayDate: uniRepayDate } = this.nearBills;
    if (this.repayStatus === repayStatusType.outSeven && uniRepayDate && repayDate === uniRepayDate.replace(/\./g, '-')) {
      // 由于未来待还判断和近期待还判断可见不可还账单方法不同，由近期待还查出的7天外数据，跳去账单页时，要把可见不可还账单重新拼接回去
      setStore({ futureBillList: (this.nearBills.repayBillList || []).concat(this.nearBills.showBillList || []) });
    } else {
      const [futurePlan = {}] = this.futurePlanList
        .filter(({ date }) => +(new Date(date.join('/'))) === +(new Date(repayDate.replace(/-/g, '/'))));
      setStore({ futureBillList: futurePlan.billList || [] });
    }
    const addrLink = repayDate ? `/pages/bill-list-future/index?fromDispatch=1&repayDate=${repayDate}` : '/pages/bill-list-future/index?fromDispatch=1';
    Util.router.replace(addrLink);
  }

  toRepayment = async (dataType) => {
    const { ret, errMsg, errCode } = await Dispatch.repayment.RepayPretreatment();
    if (ret === '0') {
      const { totalAmount, nearBillList, advancedBillList } = this;
      const selectedBillList = dataType === 'bill' ? nearBillList : advancedBillList;
      setStore({ selectedBillList });
      Util.router.replace({
        path: '/pages/express-repay/index',
        query: {
          amount: Number(totalAmount).toFixed(2),
          stages: selectedBillList.length,
          billType: dataType === 'bill' ? '7days' : 'total',
        }
      });
    } else {
      setTimeout(() => {
        Util.showErrorMsg(errMsg);
        dispatchTrackEvent({
          target: this,
          event: EventTypes.EV,
          beaconId: `${errCode === 'UMDP00065' ? 'OtherRepaymentRunning' : 'RepayPretreatmentSystemError'}`
        });
      }, 50);
    }
  }

  sendBeacon = (beaconId, eventType = 'EV', afterMsg = '', beforeMsg = '') => {
    if (!beaconId) return;
    dispatchTrackEvent({ target: this, event: EventTypes[eventType], beaconId: `${beforeMsg || ''}${beaconId}${afterMsg || ''}` });
  }

  render() {
    const { showModal } = this.state;
    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="还款"
          leftArea={[
            {
              type: 'icon',
              value: 'back'
            }
          ]}
        />
        <MUView className="dispatch-page">
          <MUModal
            type="default"
            className="dispatch-page__modal"
            beaconId="DispatchModal"
            isOpened={showModal}
            content="您的还款金额已发生变动，请返回上一页确认"
            confirmText="返回"
            closeOnClickOverlay={false}
            onConfirm={this.clickModal}
          />
        </MUView>
      </MUView>
    );
  }
}
