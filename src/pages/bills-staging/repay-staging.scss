@import '~@mu/zui/dist/style/mixins/index.scss';

// 兼容小程序，参考zui直接用了标签名，但开发工具会有warning
@include weappBottomLine("block-item", "mu-view", 30px);
@include weappBottomLine("bank-item", "mu-view", 30px);

.repay-staging {
  position: relative;
  width: 100vw;
  // min-height: 100vh;
  padding-bottom: 265px;
  box-sizing: border-box;
  background-color: #F3F3F3;

  &__card {
    position: relative;
    height: 240px;
    padding: 49px 30px;
    background-color: #FFFFFF;
    box-sizing: border-box;

    &__title {
      font-size: 26px;
      color: #808080;
      text-align: center;
      line-height: 32px;
      display: flex;
      align-items: center;
    }

    &__title-icon {
      margin-left: 10px;
      width: 32px;
      height: 32px;
    }

    &__num {
      font-size: 80px;
      color: #333333;
      line-height: 80px;
      margin-top: 30px;
      // font-weight: bolder;
    }

    &__link {
      position: absolute;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 28px;
      text-align: center;
      line-height: 30px;
      display: flex;
      align-items: center;
    }

    &__link-icon {
      margin-left: 15px;
    }
  }

  &__block {

    &__title {
      font-size: 26px;
      color: #808080;
      line-height: 26px;
      padding: 40px 30px 20px;
    }

    &__content {
      background-color: #FFFFFF;
      border-radius: 16px;
      margin: 0 20px;
      max-width: calc(100vw - 40px);
    }

    .block-item {
      padding: 0 30px;
      box-sizing: border-box;

      &--extend {
        padding-bottom: 15px;
      }

      .item {
        min-height: 96px;
        display: flex;
        align-items: center;
        justify-content: flex-start;

        &__label {
          font-size: 32px;
          color: #333333;
          line-height: 32px;
          margin-right: auto;
          font-weight: bolder;
        }

        &__content {
          text-align: right;

          &__main {
            font-size: 32px;
            color: #333333;
            line-height: 32px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
          }

          &__sub {
            font-size: 22px;
            color: #A6A6A6;
            line-height: 22px;
            margin-top: 12px;
          }

          &__img {
            width: 32px;
            height: 32px;
            margin-right: 10px;
            transform: scale(1.1);
          }

          &--highlight {
            color: #FF8800;
          }
        }

        &__icon {
          margin-left: 18px;
        }
      }

      .extend {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-items: flex-start;
        overflow: hidden;
        transition: max-height 0.2s linear;
        max-height: 248px;

        &--more {
          max-height: none;
        }

        &--hide {
          max-height: 0;
        }

        &__item {
          position: relative;
          flex: 0 0 auto;
          width: 207px;
          height: 110px;
          background: #F3F3F3;
          border-radius: 8px;
          padding: 24px;
          margin-right: 14px;
          margin-bottom: 15px;
          box-sizing: border-box;
          white-space: nowrap;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          &:nth-child(3n + 0) {
            margin-right: 0;
          }

          &--selected {
            background-image: url('./img/selected-bottom.png');
            background-repeat: no-repeat;
            background-size: 56px 56px;
            background-position: 100% 100%;
            background-color: #E1EBFF;

            .extend__item__main,
            .extend__item__sub {
              color: #3477FF;
            }
          }

          &--recommend::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 73px;
            height: 74px;
            background-image: url('https://file.mucfc.com/ebn/3/18/2023010/20231012143828c9a68f.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            background-position: center center;
          }

          &__main {
            font-size: 32px;
            color: #333333;
            line-height: 32px;
          }

          &__sub {
            font-size: 22px;
            color: #A6A6A6;
            line-height: 22px;
            margin-top: 12px;
          }
        }
      }

      .show-more {
        padding-top: 0;
        padding-bottom: 0;
        margin-bottom: 15px;
      }

      .tips {
        font-size: 22px;
        color: #3477FF;
        line-height: 22px;
        background-color: #E8F0FF;
        border-radius: 8px;
        padding: 20px;
        box-sizing: border-box;
        margin-bottom: 15px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      &:not(:last-child) {
        @include hairline-bottom-relative($left: 30px);
      }
    }
  }

  &__footer {
    font-size: 24px;
    color: #A6A6A6;
    text-align: center;
    line-height: 24px;
    margin: 24px 0 48px;

    &--nochat {
      margin-top: 72px;
    }
  }

  &__submit {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    height: 235px;
    background-color: #FFFFFF;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &__contract.repay-staging__submit__contract {
      padding: 0px 30px;
    }

    &__btn.at-button {
      width: 690px;
      height: 100px;
      font-size: 36px;
    }
  }

  &__bank-drawer {

    &__title {
      font-weight: bolder;
      position: relative;
      border-radius: 16px 16px 0 0;
      background-color: #FFFFFF;
      height: 100px;
      font-size: 36px;
      color: #333333;
      line-height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &__close {
      position: absolute;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
    }

    &__list {
      margin-top: 20px;

      .bank-item {
        background-color: #FFFFFF;
        height: 120px;
        padding: 30px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: flex-start;

        &__logo {
          width: 60px;
          height: 60px;
          transform: scale(1.1);
        }

        &__content {
          font-size: 32px;
          color: #333333;
          line-height: 32px;
          margin-left: 30px;
        }

        &__icon {
          margin-left: auto;

          &--unchecked {
            color: #CACACA;
          }
        }

        &:not(:last-child) {
          @include hairline-bottom-relative($left: 30px);
        }
      }
    }

    &__add {
      margin-top: 20px;
      width: 100vw;
      height: 120px;
      background-color: #FFFFFF;
      box-sizing: border-box;
      padding: 30px;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .bank-add {

        &__logo {
          margin-left: 6px;
        }

        &__content {
          margin-left: 36px;
          font-size: 32px;
          color: #333333;
          line-height: 32px;
        }

        &__icon {
          margin-left: auto;
        }
      }
    }
  }

  &__repay-drawer {

    &__title {
      position: relative;
      border-radius: 16px 16px 0 0;
      background-color: #FFFFFF;
      height: 258px;
      font-size: 36px;
      color: #333333;
      line-height: 36px;

      .title-text {
        font-weight: bolder;
        position: absolute;
        top: 32px;
        left: 50%;
        transform: translateX(-50%);
      }

      .title-desc {
        font-weight: bolder;
        position: absolute;
        top: 132px;
        left: 40px;
        font-size: 44px;
        color: #333333;
        line-height: 44px;
      }

      .title-subdesc {
        position: absolute;
        top: 196px;
        left: 40px;
        font-size: 22px;
        color: #808080;
        line-height: 22px;
      }
    }

    &__close {
      position: absolute;
      top: 32px;
      right: 30px;
    }

    &__content {
      height: calc(100% - 258px);
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;

      .content-desc {
        flex: 0 0 auto;
        padding: 40px;
        font-size: 26px;
        color: #808080;
        line-height: 26px;
        display: flex;
        align-items: center;
        justify-content: flex-start;

        &__icon {
          margin-left: 10px;
          width: 32px;
          height: 32px;
        }
      }

      .content-view {
        flex: 1 1 auto;
        padding: 0 40px 40px;

        .repay-item {
          height: 120px;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;

          &__left {
            width: 100px;

            &__title {
              font-weight: bolder;
              font-size: 28px;
              color: #333333;
              letter-spacing: 2px;
              line-height: 28px;
              margin-bottom: 10px;
            }

            &__desc {
              font-size: 24px;
              color: #808080;
              text-align: right;
              line-height: 28px;
            }
          }

          &__middle {
            margin: 0 50px 0 30px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;

            &__circle {
              width: 9px;
              height: 9px;
              background: #FFFFFF;
              border: 4px solid;
              border-radius: 50%;
            }

            &::after {
              content: '';
              margin-top: 4px;
              height: 90px;
              /* stylelint-disable-next-line */
              width: 1PX;
              background-color: #CACACA;
              display: block;
            }
          }

          &__right {

            &__title {
              font-weight: bolder;
              font-size: 28px;
              color: #333333;
              line-height: 28px;
              margin-bottom: 10px;
            }

            &__desc {
              font-size: 24px;
              color: #808080;
              line-height: 28px;
            }
          }
        }
      }
    }
  }

  &__modal.mu-modal {

    .mu-modal__container {
      transition: none;
    }
  }

  &__chat {
    display: flex;
    justify-content: center;
    margin-top: 48px;
  }

  .tipModal {
    z-index: 9999 !important;
  }

  .phone_modal_content{
      display: flex;
      flex-direction: column;
      width: 560px;
      height: 460px;
      background: #FFFFFF;
      border-radius: 16px;
      align-items: center;
      .topsection{
        height: 48px;
        width: 560px;
        margin-top: -45px;
        display: flex;
        background-color: #FFFFFF;
        justify-content: right;
        .close_img{
          margin-right: 24px;
          margin-top: 24px;
          height: 32px;
          width: 32px;
        }
      }
      .middlesection{
        display: flex;
        height: 131px;
        width: 560px;
        margin-top: 0px;
        background-color: #FFFFFF;
        justify-content: center; 
        align-items: center;   
        .icon_img {
            width: 134px;
            height: 131px;
          }
      }
      .textOne{
        margin-top: 40px;
        width: 560px;
        height: 54px;
        font-weight: 500;
        font-size: 36px;
        color: #333333;
        text-align: center;
        line-height: 54px;
      }
      .textTwo{
        margin-top: 20px;
        width: 560px;
          height: 42px;
          font-weight: 400;
          font-size: 28px;
          color: #808080;
          text-align: center;
          line-height: 42px;
      }
      .confirm-button{
        margin-top: 40px;
          width: 480px;
          height: 88px;
          background: #3477FF;
          border-radius: 44px;
      }
  }

  .loading-dialog .mu-dialog__overlay {
    background-color: rgba(0, 0, 0, 0.3);
  }
}
