/* eslint-disable react/no-unused-state */
/* eslint-disable eqeqeq */
/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
/* eslint-disable no-return-assign */
/* eslint-disable no-template-curly-in-string */
import Taro, { Component } from '@tarojs/taro';
import {
  MUView, MUImage,
  MUText, MUListItem,
  MUButton, MURadio,
  MUModal, MUIcon,
  MUDrawer, MUScrollView,
} from '@mu/zui';
import { MUSafeSmsCodeHalfWrap } from '@mu/safe-sms-shell';
import { MUTradePasswordEncryptedWrap } from '@mu/trade-password-encrypted-shell';
import Madp from '@mu/madp';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { isMuapp, isIOS, getCurrentPageUrlWithArgs, Url } from '@mu/madp-utils';
import appInfos from '@utils/app-config-infos';
import CustomConfig from '@config/index';
import Util from '@utils/maxin-util';
import { urlDomain } from '@utils/url_config';
import { getStore, setStore } from '@api/store';
import Dispatch from '@api/actions';
import pageHoc from '@utils/pageHoc';
import { CouponSelector } from '@mu/coupon-selector';
import { ChatEntry } from '@mu/chat-entry-component';
// import { AgreementDrawer } from '@mu/agreement';
import Protocol from '@components/protocol/index';
import { getBusinessFullName } from '@mu/business-basic';
import LoadingDialog from '@components/loading-dialog';
import './repay-staging.scss';
import cardTitleIcon from './img/tips-icon.png';
import closeImg from '@components/assets/img/close.png';
import iconImg from './img/icon.png';


// import '@mu/coupon-selector/dist/styles/index.scss';
// import '@components/loading-dialog/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@mu/coupon-selector/dist/styles/index.scss');
  require('@components/loading-dialog/index.scss');
}

const ModalType = {
  None: -1,
  PrincipalNotEnoughTips: 0,
  StageTips: 1,
  OverDueTips: 2,
  NoProduct: 3,
};

const themeColor = Util.getThemeColor(CustomConfig.theme);

@track({ event: EventTypes.PO }, {
  pageId: 'RepayStaging',
  dispatchOnMount: true,
})
@pageHoc({ title: '账单分期' })
export default class RepayStaging extends Component {
  constructor(props) {
    super(props);

    this.state = {
      openPwdTip: false,
      openSmsCodeTip: false,
      isShow: false,
      totalPrincipal: 0,
      totalInterest: 0,
      modalType: ModalType.None,
      loading: false,

      showTradePwdVerify: false,
      clearAndDisableInput: false, // 控制是否清空密码输入框
      showSmsVerify: false,
      pwdToken: '',
      smsToken: '',
      showPhoneModal: false,//无效手机号的弹窗引导

      showCouponSelector: false,
      couponObject: {},
      couponContentText: '',

      showExtend: false,
      showMore: false,
      selectedCntIndex: 0,
      recommendCntIndex: 0,
      cntInfoList: [],

      selectedBankIndex: 0,
      showBankDrawer: false,

      showRepayDrawer: false,
      channelAgreementPlanCfgDtoList: [{}],
      fullName: '招联消费金融股份有限公司',
      billInstallApplyInfo: {}, // 账单分期数据
      loadingCount: 10, // 设置倒计时时间
      isCheckedContract: '', // 表示合同组件是否同意勾选合同, 值为hasCheckedContract标识选中
      alreadyForceFlag: false, // 表示合同是否已经强读过
      hasJumpToResultPage: false, // 标志已跳转至结果页
    };

    this.transRefNo = '';

    this.accountInfo = {};
    this.limitInfo = {};
    this.tradeProduct = null;
    this.repayType = '';
    this.cntList = [];
    this.queryCouponCntIndex = 0;
    this.selectedCntDisable = false;
    this.selectedCouponDisable = true;
    this.selectedCoupon = null;

    this.nearBills = {};
    this.selectedBillList = [];

    this.configParams = {
      repayVerifyScene: 'SCENE_REPAYMENT',
      verifyScene: 'SCENE_STAGE_BILL',
      bindCardScene: 'beforeBillInstallment',
      repayScene: 'BILL_INSTALL',
      modalDesc: ['很抱歉，您的1期贷款本金不足${number}元，无法办理账单分期',
        '分期本金总额由您当期所选1期交易的贷款本金汇总得出',
        '若您未能按期还款，将对逾期贷款本金按照日利率${number}%记收罚息，逾期会对您个人征信造成影响，建议您按期还款',
        '产品定价信息缺失，暂时无法进行该服务',
      ],
      modalBtn: '我知道了',
      principalLimit: '100.00',
      footerText: `本服务由${appInfos.name}消费金融提供`,
      contractObj: {
        contractText: '账单分期还款服务单',
        beforeContractText: '我已阅读并同意',
        contracts: [
          { key: 'BILL', name: '账单分期还款服务单' },
        ],
      },
      totalPrincipalTitle: '分期本金总金额(元)',
      repayPlanTitle: '选择分期本金还款计划',
      paymentTitle: '选择当期利息支付方式',
      submitText: '立即分期',
      recommendCnt: 12,
      availableCouponText: '#${number}#张优惠券可用',
      unavailableCouponText: '#${number}#张优惠券，查看使用条件',
      selectedCouponText: '已省#${number}元#',
      repayWayDesList: [],
      prepayFeeDesc: {
        FT000: '温馨提示：按时还无违约金，提前还收当期手续费',
        PT002: '温馨提示：按时还无违约金，提前还加收${number}期手续费',
        NONE: '温馨提示：次日起可提前还款',
      },
      newProductCode: 'TXF001',
      oldProductCode: 'CPXYH01', // 已经废弃，中台已经切换
      creditType: 'D01',
      merchantId: '10000',
      certName: '身份证',
      awardTypeList: ['106'],
      couponParams: {},
    };
    this.resultRedirectUrl = Url.getParam('redirectUrl') ? encodeURIComponent(Url.getParam('redirectUrl')) : '';
    this.contractApplyList = []; // 设置合同信息
    this.contractInfos = [];
    this.finishLoading = false; // 标识轮询是否结束
    this.loadingFlag = ''; // 标识轮询的阶段，用于倒计时结束处理判断
    this.forceRepay = false; // 进入页面，建案接口或轮询案件详情接口就返回需要还款（applyStatus === '7'）
    this.forceRepayFinish = false; // 强制还款完成
    this.repayError = false; // 还款异常，只能修改银行卡
  }

  async componentDidMount() {
    // 初始化获取建案信息，建案状态
    await this.initBillStagingInfo();

    // 截屏或录屏时toast提示
    if (isMuapp() && isIOS()) {
      // eslint-disable-next-line func-names
      const callback = function (operation) {
        Madp.showToast({
          title: `发现${operation}操作，请注意个人信息安全`,
          icon: 'none'
        });
      };
      window.muapp.NotificationPlugin.registerNativeNoticeEvent('screenshot', () => callback('截屏'));
      window.muapp.NotificationPlugin.registerNativeNoticeEvent('screenrecord', () => callback('录屏'));
    }
  }

  componentWillUnmount() {
    // 兜底清空定时器
    clearTimeout(this.loadingTimer);
  }

  // 获取贷后服务建案信息
  initBillStagingInfo = async () => {
    const { data, ret, errCode, errMsg } = await Dispatch.repayment.postLoanApplyCase({ serviceType: '004', interfaceVersion: '1.0' }) || {};
    const {
      applyNo, applyStatus, applySourceType, contractApplyList, contractInfos, needFileList, billInstallPackageInfo
    } = data || {};
    const { billInstallApplyInfo } = billInstallPackageInfo || {};

    // 设置建案号
    this.applyNo = applyNo;

    // 判断当前服务建案状态，根据状态分发到不同状态对应的页面或内容
    const applyStatusInfo = {
      applyStatus, applySourceType, needFileList, contractApplyList, contractInfos, billInstallApplyInfo
    };
    // applyStatusFlag为true则中断当前页面渲染
    const applyStatusFlag = await this.applyStatusDispatchHandler(applyStatusInfo, ret, errCode, errMsg);
    if (applyStatusFlag || ret !== '0') {
      return;
    }

    // 设置合同信息
    this.contractApplyList = contractApplyList;
    this.contractInfos = contractInfos;

    // 初始化数据
    await this.initData();
    this.getNewFullName();
  }

  applyStatusDispatchHandler = async (applyStatusInfo, ret, errCode, errMsg) => {
    let {
      applyStatus, billInstallApplyInfo
    } = applyStatusInfo || {};

    const { totalRepayAmt } = billInstallApplyInfo || {};

    if (applyStatus === '3') {
      // 风控结果未出，跳转已提交审批页
      this.jumpToResultPage(4);
      return true;
    } else if (applyStatus === '6') {
      // 准入审核拒绝，跳转准入拒绝页
      this.jumpToResultPage(0, errCode === 'UMDP02727' ? '&duringNegotiate=1' : '');
      return true;
    } else if (applyStatus === '7') {
      // 存在待还利息，弹窗提示，引导先还款
      this.forceRepay = true;
      this.setState({
        billInstallApplyInfo,
      });
    } else if (applyStatus === '8') {
      // 还款处理中，跳转账单分期提交页
      this.jumpToResultPage(5, totalRepayAmt ? `&actualAmt=${totalRepayAmt}` : '');
      return true;
    } else if (applyStatus === '9') {
      // 办理成功，跳转办理成功页面
      this.jumpToResultPage(1, totalRepayAmt ? `&actualAmt=${totalRepayAmt}` : '');
      return true;
    } else if (applyStatus === '10') {
      // 办理失败，跳转办理失败页面
      this.jumpToResultPage(3, '&stagingFail=1');
      return true;
    } else if (ret !== '0') {
      // 系统异常
      this.jumpToResultPage(3, '&systemError=1');
      return true;
    }
  }

  getNewFullName = async () => {
    try {
      const res = await getBusinessFullName();
      const { data = '招联消费金融股份有限公司' } = res || {};
      this.setState({ fullName: data });
    } catch (error) {
    }
  }

  initData = async () => {
    // 注入客户端配置
    const config = (await Dispatch.repayment.getCommonConfig('repayStaging')) || {};
    Util.injectConfigParams(config, this.configParams);
    const { principalLimit, creditType } = this.configParams;

    // query
    const { selectedBill = 'N' } = this.$router.params;

    // init nearBills
    this.nearBills = getStore('nearBills');
    if (!Object.keys(this.nearBills).length) {
      this.nearBills = await Dispatch.repayment.getNearBills();
    }

    // init selectedBills
    const { repayBillList } = this.nearBills || {};
    this.selectedBillList = getStore('selectedBillList');
    if (selectedBill !== 'Y' || !(this.selectedBillList && this.selectedBillList instanceof Array && this.selectedBillList.length > 0)) {
      this.selectedBillList = (repayBillList || []).filter((o) => o.canInstall === 'Y' && o.limitType === creditType && !o.displayOverdueDays && !!o.surplusDays);
      // 如果建案接口返回强制还款的，以接口返回的借据号列表筛选出已选择的借据
      if (this.forceRepay && !this.forceRepayFinish) {
        const { billInstallApplyInfo } = this.state;
        const { orderNoList = [] } = billInstallApplyInfo || {};
        this.selectedBillList = this.selectedBillList.filter((o) => orderNoList.indexOf(o.orderNo) !== -1);
      }
      setStore({ selectedBillList: [...this.selectedBillList] });
    }

    // init 总 本金、利息
    let [totalPrincipal, totalInterest] = [0, 0];
    this.selectedBillList.forEach((o) => {
      totalPrincipal += +o.installTotalAmt;
      totalInterest += +o.surplusPayInteAmt;
    });
    totalPrincipal = totalPrincipal.toFixed(2);
    totalInterest = totalInterest.toFixed(2);

    // 优惠券已选ID
    const { awardNo: defaultCheckId = '' } = getStore('billsStagingInfo') || {};

    if (+totalPrincipal < +principalLimit) { // 本金不满足最小值，则展示拦截弹窗
      this.setState({ modalType: ModalType.PrincipalNotEnoughTips });
    } else { // 初始化业务数据，1、银行卡列表，2、分期信息列表（分期列表->设置推荐期数->查优惠券->选取优惠券->借款试算->设置优惠券文案）
      this.setBankCardList({ totalInterest });
      this.setCntList({ totalPrincipal, setIndex: true },
        () => this.setCouponObject({ totalPrincipal },
          () => {
            const { couponObject: { availableCouponDetailList } } = this.state;
            // eslint-disable-next-line max-len
            const [selectedCoupon = null] = (availableCouponDetailList || []).filter((o) => o && o.awardNo === defaultCheckId);
            Madp.setStorageSync('coupon_selector_default_checked', false, 'SESSION');
            this.setSelectedCoupon({ selectedCoupon },
              () => this.setCntInfoList({ totalPrincipal }, async () => {
                this.setCouponcontentText(
                  () => this.selectedCouponDisable = false
                );
              }));
          }));
    }

    this.setState({
      totalPrincipal,
      totalInterest,
      defaultCheckId,
      isShow: true,
    });
  }

  // 设置分期列表，初始化账户信息
  setCntList = async ({ totalPrincipal = '0.00', setIndex = false } = {}, cb = () => { }) => {
    const {
      recommendCnt, merchantId, creditType, newProductCode
    } = this.configParams;
    const { cnt: storeCnt = '' } = getStore('billsStagingInfo') || {};
    const { accountList: accountInfoList } = await Dispatch.repayment.getAccount({
      queryScene: '31'
    });
    const [accountInfo = {}] = accountInfoList || [];
    const [limitInfo = {}] = (accountInfo.limitInfoList || []).filter((o) => o.limitType === creditType);
    this.accountInfo = accountInfo;
    this.limitInfo = limitInfo;
    let recommendCntIndex = 0;
    let selectedCntIndex = 0;
    let [tradeProduct = null] = (limitInfo.tradeProductList || []).filter((o) => o.productCode === 'IXF002');
    if (!tradeProduct){
      [tradeProduct = null] = (limitInfo.tradeProductList || []).filter((o) => o.productCode === newProductCode);
    }
    if (!tradeProduct) {
      this.setState({ modalType: ModalType.NoProduct });
      return;
    }
    this.tradeProduct = tradeProduct;
    this.repayType = tradeProduct.paymentTypeCode || '';
    this.cntList = (tradeProduct.periodList || []).filter((o) => o != 1).sort((a, b) => a - b);
    if (setIndex) {
      // eslint-disable-next-line no-plusplus
      for (let i = 0, len = this.cntList.length; i < len; i++) {
        if (this.cntList[i] == storeCnt) selectedCntIndex = i;
        if (this.cntList[i] == recommendCnt) recommendCntIndex = i;
      }
      this.setState({
        selectedCntIndex: storeCnt ? selectedCntIndex : recommendCntIndex,
        recommendCntIndex,
      }, cb);
    } else cb();
  }

  // 设置优惠券信息
  setCouponObject = async ({ totalPrincipal = '0.00' } = {}, cb = () => { }) => {
    const { selectedCntIndex } = this.state;
    const { merchantId, awardTypeList, couponParams } = this.configParams;
    const { CouponApi } = this.couponSelectorRef;
    const couponParam = {
      // awardTransCode: '105', // 消费分期
      useScene: 'payConsume', // 交易场景类型
      requestSource: 'installment', // 分期
      customParams: {
        awardTypeList,
        querySource: '03',
        queryStatus: '003',
        checkRuleType: '03',
        queryRuleFlag: 'Y',
        queryUnAvailable: 'Y'
      },
      queryParams: {
        limitActiveFlag: this.limitInfo.status === 'Y' && +this.limitInfo.fixedLimit > 0 ? 'Y' : 'N',
        hasCredit: +this.limitInfo.fixedLimit > 0 ? 'Y' : 'N',
        topMerchantId: merchantId,
        merchantId,
        installCnt: +this.cntList[selectedCntIndex],
        installAmt: totalPrincipal,
        transAmt: totalPrincipal,
        discount: 'N',
        qryNotActivated: 'N',
        ...couponParams,
      },
    };
    const couponObject = await CouponApi.getUserTransCouponList(couponParam);
    couponObject.availableCouponDetailList = (couponObject.availableCouponDetailList || [])
      .filter((o) => o && o.reduceUnit !== 'day' && awardTypeList.indexOf(o.awardType) !== -1);
    this.queryCouponCntIndex = selectedCntIndex;
    this.setState({ couponObject }, cb);
  }

  // 选择优惠券
  setSelectedCoupon = ({ selectedCoupon = null }, cb = () => { }) => {
    setStore.commit('UPDATE_BILLS_STAGING_INFO', {
      awardNo: (selectedCoupon && selectedCoupon.awardNo) || '',
    });
    this.selectedCoupon = selectedCoupon;
    cb();
  }

  // 获取提前还款收费下限
  getPrepayFeeLower = (orderInfo) => {
    let prepayFeeLower;
    if (!orderInfo) {
      return;
    }
    const { diffPrepayFeeRuleInfoList } = orderInfo || {};
    if (diffPrepayFeeRuleInfoList) {
      diffPrepayFeeRuleInfoList.forEach((item) => {
        if (item.startCycle === 1) {
          prepayFeeLower = item.prepayFeeLower;
        }
      });
    } else {
      prepayFeeLower = orderInfo.prepayFeeLower;
    }

    return prepayFeeLower;
  }

  // advanceType（0-按提前还款本金比例FT004、2-按期数、3-按借款本金比例FT204、4-按提前还款本金*费率、5-按借款本金*费率、6-日利率*提前还款日到借款到期日之间的天数）
  getContractAdvanceData(data) {
    let param = {};
    param.frontCnt = data.prepayFeeType && data.prepayFeeType === 'PT002' ? '1' : ''; // 加收一期分期手续费
    param.cashAmtRate = data.prepayFeeType && data.prepayFeeType === 'FT204' ? Util.accMul(data.prepayFee, 100) : '';
    param.frontAmount = data.prepayFeeType && data.prepayFeeType === 'FT104' ? Number(data.prepayFee) : ''; // 提前还款违约金金额，01是固额
    param.frontRate = data.prepayFeeType && data.prepayFeeType === 'FT004' ? Util.accMul(data.prepayFee, 100) : ''; // 提前还款违约金百分比，08是比例

    if (Util.isNoEmptyObject(data.prepayfeeRateDesc)) {
      if (data.prepayFeeType && data.prepayFeeType === 'FT004') {
        param.frontRate = '';
        param.advanceType = 4;
      } else if (data.prepayFeeType && data.prepayFeeType === 'FT204') {
        param.cashAmtRate = '';
        param.advanceType = 5;
      }
      param = { ...param, ...data.prepayfeeRateDesc };
    } else if (param.frontCnt) {
      param.advanceType = 2;
    } else if (param.cashAmtRate) {
      param.advanceType = 3;
    } else if (param.frontAmount) {
      param.advanceType = 1;
      param.advanceMoney = param.frontAmount;
    } else if (param.frontRate) {
      param.advanceType = 0;
    }

    if (parseFloat(data.frontCnt) === parseFloat(data.totalInstallCnt)) { // 加收所有期时，提前还款违约金部分不填也不选
      param.advanceType = '';
    }

    // 提前还款类型为FT000时，添加advanceType为7的场景适配合同模板的修改
    if (data.prepayFeeType === 'FT000') {
      param.advanceType = 7;
    }

    return param;
  }

  // 借款试算并设置分期信息列表
  setCntInfoList = async ({ totalPrincipal = '0.00' } = {}, cb = () => { }) => {
    const { selectedCoupon } = this;
    let cntInfoList = [];
    const { orderInfoList: calculateList } = await Dispatch.repayment.loanTransTrial({
      transAmt: totalPrincipal,
      productCode: this.tradeProduct.productCode || '',
      loanType: 'F', // 价格类型，I利率， F费率
      trialType: '1', // 试算定价方式。1：按价格单明细，2：按价格档位
      priceDetailNo: this.tradeProduct.priceDetailNo || '', // 价格单明细编号
      paymentType: this.repayType, // 还款方式
      repayDateMode: '1', // 1：标准方式 2：交易指定
      trialGroupList: this.cntList.map((o, i) => ({
        installCnt: `${o}`,
        waiveDetailList: (selectedCoupon && this.queryCouponCntIndex === i) ? [Util.transformCouponItem(selectedCoupon, 'F')] : null,
      })),
      repayDay: this.accountInfo.repayDay || '',
      paymentHolidayPrivilege: this.tradeProduct.paymentHolidayPrivilege || '',
      prepayFeePrivilege: this.tradeProduct.prepayFeePrivilege || '',
      minLoanCyclePrivilege: this.tradeProduct.minLoanCyclePrivilege || '',
    });
    cntInfoList = (calculateList || []).map((o) => {
      const contractAdvanceData = this.getContractAdvanceData(o);
      return {
        cnt: o.totalInstallCnt,
        prepayFeeLower: this.getPrepayFeeLower(o),
        feeRate: Util.accMul(o.periodFeeRate, 100),
        yearInterestRate: Util.accMul(o.irrAnnualRate, 100),
        overDueRate: Util.accMul(o.penaltyRate, 100),
        lprRate: Util.accMul(o.lprFloatRate, 100),
        repayType: this.repayType,
        firstRepayDate: o.firstRepayDate,
        firstRepayAmt: o.firstInstallAmt,
        totalRepayFee: o.totalPeriodFee,
        totalRepayAmt: o.totalAmt,
        prepayFeeType: o.prepayFeeType || 'NONE',
        prepayFee: o.prepayFeeType === 'PT002' ? 1 : Util.accMul(o.prepayFee, 100),
        totalWaiveAmt: +o.totalWaiveAmt,
        repayPlanList: (o.repayPlanList || []).map((_o) => ({
          period: _o.installCnt,
          repayAmt: _o.payTotalAmt,
          repayFee: _o.payPeriodFee,
          repayPrincipal: _o.payPrincipalAmt,
          repayDate: _o.payDate,
        })).sort((a, b) => a.period - b.period),
        contractAdvanceData
      };
    });
    this.setState({ cntInfoList }, cb);
  }

  // 设置优惠券描述文案
  setCouponcontentText = (cb = () => { }) => {
    const { selectedCoupon } = this;
    const { couponObject } = this.state;
    const {
      availableCouponDetailList,
      unavailableCouponDetailList,
      appExclusiveCouponDetailList,
    } = couponObject;
    const { availableCouponText, unavailableCouponText, selectedCouponText } = this.configParams;
    let couponContentText = '';
    if ((availableCouponDetailList || []).length > 0) {
      dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'AvailableCoupons' });
      if (selectedCoupon) {
        couponContentText = selectedCouponText.replace(/\$\{number\}/g, this.selectedCntInfo.totalWaiveAmt);
      } else {
        couponContentText = availableCouponText.replace(/\$\{number\}/g, availableCouponDetailList.length);
      }
    } else if ((unavailableCouponDetailList || []).length > 0 || (appExclusiveCouponDetailList || []).length > 0) {
      dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'ConditionalCoupons' });
      couponContentText = unavailableCouponText.replace(/\$\{number\}/g, (unavailableCouponDetailList || []).length + (appExclusiveCouponDetailList || []).length);
    }
    this.setState({ couponContentText }, cb);
  }

  // 设置还款银行卡列表
  setBankCardList = async ({ totalInterest = '0.00' } = {}) => {
    await Dispatch.repayment.getBankCardsList({ transAmt: totalInterest });
    const { bankCardId = '' } = getStore('billsStagingInfo') || {};
    const bankCardList = (getStore('bankCards') || [])
      .filter(({ supplementSignFlag, satisfyLimitAmtStatus, cardType }) => cardType === '1' && satisfyLimitAmtStatus !== 'N' && supplementSignFlag !== 'Y')
      .sort((a, b) => (b.isDefault === 'Y' ? 1 : 0) - (a.isDefault === 'Y' ? 1 : 0));
    let selectedBankIndex = 0;
    bankCardList.forEach((o, i) => {
      if (o && o.bankCardId && o.bankCardId === bankCardId) selectedBankIndex = i;
    });
    this.setState({
      bankCardList,
      selectedBankIndex,
    });
  }

  // 去修改分期本金
  goModify = () => {
    if (this.repayError || (this.forceRepay && !this.forceRepayFinish)) {
      Util.toast('您有一笔进行中的分期办理，当天不可修改');
      return;
    }
    const { mapCode, redirectUrl = '' } = this.$router.params;
    const { principalLimit, creditType } = this.configParams;
    Util.router.push({
      path: '/pages/bills-staging/bill-list-stages',
      query: { principalLimit, creditType, mapCode, redirectUrl },
    });
  }

  // 还款错误码处理
  handleErrorCode = (errCode, errMsg) => {
    switch (errCode) {
      case 'UMDP01152': // 超过限额
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'BankCardOverLimit' });
        break;
      case 'UMDP01155': // 余额不足
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'BalanceNotEnough' });
        break;
      case 'UMDP01151': // 账户异常、密码异常
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'BankCardError' });
        break;
      default: break;
    }
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'SystemError' });
    Util.toast(errMsg || '系统繁忙，请稍后再试');
  }

  // 案件提交
  submitBillStagingCase = async () => {
    const { totalPrincipal, totalInterest, pwdToken, smsToken } = this.state;
    const { cnt, repayType, firstRepayDate } = this.selectedCntInfo;
    const { selectedCoupon } = this;

    let securityInfoList = [];
    if (pwdToken) {
      securityInfoList = [{
        securityType: '1',
        securityContent: pwdToken,
      }];
    } else if (smsToken) {
      securityInfoList = [{
        securityType: '0',
        securityContent: smsToken,
      }];
    }
    // 还款场景下，账单分期的可以不用传securityInfoList
    if (+totalInterest > 0) securityInfoList = null;

    const param = {
      applyNo: this.applyNo,
      billInstallApplyInfo: {
        productCode: this.tradeProduct.productCode || '',
        totalRepayAmt: totalPrincipal,
        orderNoList: (this.selectedBillList || []).map(({ orderNo }) => orderNo),
        installCnt: cnt,
        repayType,
        waiveDetailList: (selectedCoupon && selectedCoupon.awardNo) ? [{ waiveRefno: selectedCoupon.awardNo }] : null,
      },
      bizContent: this.transRefNo,
      securityInfoList,
    };
    const { data, errCode } = await Dispatch.repayment.postLoanSubmitCase(param) || {};
    const { applyStatus } = data || {};

    if (applyStatus === '3') {
      // 等待风控结果，倒计时轮询10s
      this.loadingDialog && this.loadingDialog.show((finishLoading) => {
        this.finishLoading = finishLoading;
        clearTimeout(this.loadingTimer);
        // 倒计时结束，风控结果未出，跳转已提交审批页
        if (this.loadingFlag === '1') {
          this.jumpToResultPage(4);
        } else {
          // 倒计时结束，风控结果出了，且需要还款，但还款结果在处理中，跳转账单分期提交页
          this.jumpToResultPage(5, totalPrincipal ? `&actualAmt=${totalPrincipal}` : '');
        }
      });
      this.loadingFlag = '1';
      if (this.finishLoading) return;
      await this.queryCustCaseDetail();
    } else if (applyStatus === '6') {
      // 准入或风险审核拒绝，跳转拒绝页
      let paramStr = '';
      if (errCode === 'UMDP02727') {
        paramStr = '&duringNegotiate=1';
      } else if (errCode === 'UMDP02428') {
        paramStr = '&riskCheckFail=1';
      }
      this.jumpToResultPage(0, paramStr);
    } else if (applyStatus === '7') {
      this.repayApply();
    } else if (applyStatus === '8') {
      // 还款处理中，跳转账单分期提交页
      this.jumpToResultPage(5, totalPrincipal ? `&actualAmt=${totalPrincipal}` : '');
    } else if (applyStatus === '9') {
      // 办理成功，跳转办理成功页面
      this.jumpToResultPage(1, totalPrincipal && firstRepayDate ? `&actualAmt=${totalPrincipal}&firstRepayDate=${firstRepayDate}` : '');
    } else if (applyStatus === '10') {
      // 办理失败，跳转办理失败页面
      this.jumpToResultPage(3, '&stagingFail=1');
    } else {
      // 不满足以上状态，兜底系统异常
      this.jumpToResultPage(3, '&systemError=1');
    }
  }

  // 查询案件详情
  queryCustCaseDetail = async () => {
    const { data, errCode } = await Dispatch.repayment.postLoanQueryCustCaseDetail({ applyNo: this.applyNo }) || {};
    const { applyStatus } = data || {};
    const { firstRepayDate } = this.selectedCntInfo;
    const { totalPrincipal } = this.state;

    if (applyStatus === '3') {
      if (this.finishLoading) return;
      this.loadingTimer = setTimeout(async () => {
        await this.queryCustCaseDetail();
      }, 1000);
    } else if (applyStatus === '6') {
      // 准入或风险审核拒绝，跳转拒绝页
      let paramStr = '';
      if (errCode === 'UMDP02727') {
        paramStr = '&duringNegotiate=1';
      } else if (errCode === 'UMDP02428') {
        paramStr = '&riskCheckFail=1';
      }
      this.finishLoading = true;
      this.loadingDialog && this.loadingDialog.hide();
      this.jumpToResultPage(0, paramStr);
    } else if (applyStatus === '7') {
      this.repayApply();
    } else if (applyStatus === '8') {
      // 还款处理中，跳转账单分期提交页
      this.finishLoading = true;
      this.loadingDialog && this.loadingDialog.hide();
      this.jumpToResultPage(5, totalPrincipal ? `&actualAmt=${totalPrincipal}` : '');
    } else if (applyStatus === '9') {
      // 办理成功，跳转办理成功页面
      this.finishLoading = true;
      this.loadingDialog && this.loadingDialog.hide();
      this.jumpToResultPage(1, totalPrincipal && firstRepayDate ? `&actualAmt=${totalPrincipal}&firstRepayDate=${firstRepayDate}` : '');
    } else if (applyStatus === '10') {
      // 办理失败，跳转办理失败页面
      this.finishLoading = true;
      this.loadingDialog && this.loadingDialog.hide();
      this.jumpToResultPage(3, '&stagingFail=1');
    } else {
      this.finishLoading = true;
      this.loadingDialog && this.loadingDialog.hide();
      clearTimeout(this.loadingTimer);
      // 不满足以上状态，兜底系统异常
      this.jumpToResultPage(3, '&systemError=1');
    }
  }

  // status = 0 为准入拒绝页, status = 1为成功, status = 2 为审核中, status = 3 为失败页, status = 4 为提交审批页, status = 5 为账单分期已提交页
  jumpToResultPage = (status, urlParamStr = '') => {
    const { hasJumpToResultPage } = this.state;
    // 防止出现倒计时结束已跳转兜底页面后，轮询案件详情或交易记录接口才出结果，又进行跳转的极端情况
    if (!hasJumpToResultPage) {
      const redirectUrlParam = this.resultRedirectUrl ? `&resultRedirectUrl=${this.resultRedirectUrl}` : '';
      Madp.redirectTo({ url: `/pages/bills-staging/repay-staging-result?status=${status}${urlParamStr}${redirectUrlParam}` });
      this.setState({
        hasJumpToResultPage: true
      });
    }
  }

  // 提交及身份验证前校验，包括必须字段及还款校验
  preSubmit = async () => {
    const { totalInterest, isCheckedContract } = this.state;
    if (!Object.keys(this.selectedCntInfo).length) {
      Util.toast('请先选择分期数');
      return;
    }
    // 判断当前是否存在合同且已经勾选合同
    if (!isCheckedContract && (this.contractApplyList && this.contractApplyList.length > 0 || this.contractInfos && this.contractInfos.length > 0)) {
      Util.toast('请先阅读并勾选服务单');
      return;
    }
    if (+totalInterest > 0) {
      if (!Object.keys(this.selectedBankCard).length) {
        Util.toast('请先选择或添加支付方式');
        return;
      }
      const { ret, errMsg, errCode } = await Dispatch.repayment.RepayPretreatment();
      if (ret !== '0') {
        setTimeout(() => {
          Util.toast(errMsg || '系统繁忙，请稍后再试');
          dispatchTrackEvent({
            target: this,
            event: EventTypes.EV,
            beaconId: `${errCode === 'UMDP00065' ? 'OtherRepaymentRunning' : 'RepayPretreatmentSystemError'}`,
          });
        }, 50);
        return;
      }
    }
    //在进行提交流程前，需要检查是否是空的手机号
    const {basicCustDto} = await Dispatch.repayment.getNewUserInfo();
    if (!basicCustDto.mobile || basicCustDto.virtualMobile) {
      this.setState({
        showPhoneModal: true
      })
      return;
    }

    // 如果是还款异常，或者建案接口返回需还款的情况，直接验证密码，无需验证动码
    if (this.repayError || (this.forceRepay && !this.forceRepayFinish)) {
      this.openTradeVerify();
    } else {
      this.openSmsVerify();
    }
  }

  repayApply = async () => {
    const { totalInterest, totalPrincipal, pwdToken, smsToken } = this.state;
    const { repayScene, merchantId } = this.configParams;
    const {
      bankCardId, bankCardNo, bankName, bankMobileNo, bankCustName, cardType: bankCardType, bankCardValidStatus, bankCardValidStatusExceptionReason
    } = this.selectedBankCard;
    let securityInfoList = [];
    if (pwdToken) {
      securityInfoList = [{
        securityType: '1',
        securityContent: pwdToken,
      }];
    } else if (smsToken) {
      securityInfoList = [{
        securityType: '0',
        securityContent: smsToken,
      }];
    }
    const parmas = {
      transRefNo: this.forceRepay && !this.forceRepayFinish ? Util.getTransRefNo() : this.transRefNo,
      repayMode: repayScene,
      currency: '156', // 代表人民币
      repayWay: 'BANK',
      repayDetailList: (this.selectedBillList || [])
        .filter((o) => +o.surplusPayInteAmt > 0)
        .map(({ orderNo, surplusPayInteAmt, installCnt }) => ({ orderNo, repayAmt: surplusPayInteAmt, instCnt: installCnt })),
      isRandomReduce: 'N',
      transTotalAmt: totalInterest,
      transCashAmt: totalInterest,
      merchantNo: merchantId,
      subMerchantNo: merchantId,
      securityInfoList,
      bankCardInfo: {
        bankCustName,
        bankCardNo,
        bankCardType,
        bankName,
        bankMobileNo,
        bankCardId,
        bankCardValidStatus,
        bankCardValidStatusExceptionReason
      },
      postLoanApplyInfo: {
        applyNo: this.applyNo
      }
    };
    const {
      data, ret, errMsg, errCode
    } = await Dispatch.repayment.normalRepayApply(parmas) || {};
    if (ret === '1') {
      this.repayError = true;
      this.finishLoading = true;
      clearTimeout(this.loadingTimer);
      this.loadingDialog && this.loadingDialog.hide();
      this.handleErrorCode(errCode, errMsg);
      return;
    }
    const { transSeqno } = data || {};
    if (data.transStatus === 'SUC') {
      // 办理成功，跳转办理成功页面
      this.finishLoading = true;
      this.loadingDialog && this.loadingDialog.hide();
      this.forceRepayFinish = true;
      // 还款成功不等价于账单分期成功，所以再查一次案件详情接口获取案件状态
      this.queryCustCaseDetail();
    } else if (data.transStatus === 'PROC') {
      if (!this.loadingFlag) {
        this.setState({
          loadingCount: 5
        }, () => {
          this.finishLoading = false;
          setTimeout(() => {
            // 轮询10s，查询提交结果
            this.loadingDialog && this.loadingDialog.show((finishLoading) => {
              this.finishLoading = finishLoading;
              this.forceRepayFinish = true;
              clearTimeout(this.loadingTimer);
              this.jumpToResultPage(5, totalPrincipal ? `&actualAmt=${totalPrincipal}` : '');
            });
          }, 100);
        });
      }
      this.loadingFlag = '2';
      await this.queryTransRecordSingle(transSeqno);
    } else {
      this.finishLoading = true;
      this.loadingDialog && this.loadingDialog.hide();
      this.forceRepayFinish = true;
      this.jumpToResultPage(3, '&repayFail=1');
    }
  }

  // 查询单笔交易记录
  queryTransRecordSingle = async (transSeqno) => {
    const { transRecord = {} } = await Dispatch.repayment.queryTransRecordSingle({ transSeqno }) || {};
    const { transStatus = 'PROC' } = transRecord || {};
    switch (transStatus) {
      case 'SUC':
        this.forceRepayFinish = true;
        this.finishLoading = true;
        this.loadingDialog && this.loadingDialog.hide();
        // 还款成功不等价于账单分期成功，所以再查一次案件详情接口获取案件状态
        this.queryCustCaseDetail();
        break;
      case 'FAIL':
        this.forceRepayFinish = true;
        this.finishLoading = true;
        this.loadingDialog && this.loadingDialog.hide();
        this.jumpToResultPage(3, '&repayFail=1');
        break;
      case 'PROC':
        if (this.finishLoading) return;
        this.loadingTimer = setTimeout(async () => {
          await this.queryTransRecordSingle(transSeqno);
        }, 1000);
        break;
      default:
        break;
    }
  }

  // 去绑卡
  addBankCard = () => {
    const { bindCardScene } = this.configParams;
    const [path, action, redirectUrl] = [
      process.env.TARO_ENV === 'h5' ? `${urlDomain}/${Madp.getChannel()}/usercenter/#/bankcard/bind-card` : '/usercenter/pages/bankcard/AddOrVerifyBankCard',
      bindCardScene,
      process.env.TARO_ENV === 'h5' ? getCurrentPageUrlWithArgs() : 'goBack',
    ];
    Util.router.push({ path, query: { action, redirectUrl } });
  }

  // 选择银行卡
  setSelectedBankCardIndex = (i) => {
    const { bankCardList } = this.state;
    setStore.commit('UPDATE_BILLS_STAGING_INFO', {
      bankCardId: bankCardList.length > i ? ((bankCardList[i] || {}).bankCardId || '') : '',
    });
    this.setState({ selectedBankIndex: i, showBankDrawer: false });
  }

  // 打开动码
  openSmsVerify = async () => {
    this.transRefNo = Util.getTransRefNo();
    this.setState({ showSmsVerify: true });
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'verifySms' });
  }

  // 动码验证成功
  onSmsVerifySuccess = async (type, token) => {
    const { totalInterest } = this.state;
    this.setState({
      showSmsVerify: false,
      smsToken: token,
    }, () => {
      if (type === 'ok') { // 非豁免
        dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'verifySms' });
      }
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'verifySmsSuccess' });
      // 需要还款就验证密码，否则验证动码就行了，直接提交案件
      if (Number(totalInterest) > 0) {
        this.openTradeVerify();
      } else {
        this.submitBillStagingCase();
      }
    });
  }

  // 打开交易密码
  openTradeVerify = async () => {
    const { data } = await Dispatch.repayment.getCustInfo() || {};
    const { passwordDto } = data || {};
    const { hasTradePwd } = passwordDto || {};
    if (hasTradePwd) {
      this.setState({
        clearAndDisableInput: false,
        showTradePwdVerify: true,
      });
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'verifyTradePwd' });
    } else {
      Util.toast('请先设置交易密码');
    }
  }

  // 验证交易密码成功
  onTradeVerifySuccess = async (token) => {
    this.setState({
      showTradePwdVerify: false,
      clearAndDisableInput: true, // 清除密码残留
      pwdToken: token,
    }, () => {
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'verifyTradePwdSuccess' });
      if (this.repayError || (this.forceRepay && !this.forceRepayFinish)) {
        this.repayApply();
      } else {
        this.submitBillStagingCase();
      }
    });
  }

  clickModal = async () => {
    const { modalType } = this.state;
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: `${Object.keys(ModalType)[modalType + 1]}ModalConfirm` });
    switch (modalType) {
      case ModalType.PrincipalNotEnoughTips:
      case ModalType.NoProduct:
        try {
          await Madp.closeWebView();
        } catch (e) {
          Madp.navigateBack({ delta: 1 });
        }
        break;
      case ModalType.StageTips:
      case ModalType.OverDueTips:
        this.setState({ modalType: ModalType.None });
        break;
      default: break;
    }
  }

  closePhoneModal(){
    this.setState({
      showPhoneModal: false
    })
  }

  getBlockItem({
    labelText = '', contentText = '',
    subContentText = '', iconValue = '',
    iconSize = '15', iconColor = '#CACACA',
    tipsText = '', contentImg = '',
    itemClick = () => { }, beaconId = 'BlockItem',
    highLightSign = '#', showExtend = false,
    extendList = [], selectedIndex = 0,
    recommendIndex = 0, extendClick = () => { },
    showMore = false, showMoreClick = () => { },
  } = {}) {
    return (
      <MUView className={`block-item ${(tipsText || (showExtend && extendList.length > 0)) ? 'block-item--extend' : ''}`}>
        <MUView className="item" onClick={itemClick} beaconId={beaconId}>
          <MUView className="item__label">{labelText}</MUView>
          <MUView className="item__content">
            <MUView className="item__content__main">
              {contentImg ? <MUImage className="item__content__img" src={contentImg} /> : null}
              {contentText.split(highLightSign).map((o, i) => <MUText className={`${i % 2 !== 0 ? 'item__content--highlight' : ''}`}>{o}</MUText>)}
            </MUView>
            {subContentText ? <MUView className="item__content__sub">{subContentText.split(highLightSign).map((o, i) => <MUText className={`${i % 2 !== 0 ? 'item__content--highlight' : ''}`}>{o}</MUText>)}</MUView> : null}
          </MUView>
          {iconValue ? <MUIcon className="item__icon" value={iconValue} size={iconSize} color={iconColor} /> : null}
        </MUView>
        {extendList.length > 0 ? (
          <MUView className={`extend ${!showExtend ? 'extend--hide' : ''} ${(showMore || selectedIndex > 5) ? 'extend--more' : ''}`}>
            {extendList.map((o, i) => (
              <MUView
                className={`extend__item ${selectedIndex === i ? 'extend__item--selected' : ''} ${recommendIndex === i ? 'extend__item--recommend' : ''}`}
                key={o.mainText}
                onClick={() => extendClick(i)}
                beaconId={`${beaconId}Extend`}
                beaconContent={{ cus: { mainText: o.mainText } }}
                style={this.getThemeStyle(selectedIndex, i)}
              >
                <MUView className="extend__item__main" style={`color: ${themeColor}`}>{o.mainText}</MUView>
                {o.subText ? <MUView className="extend__item__sub" style={`color: ${themeColor}`}>{o.subText}</MUView> : null}
              </MUView>
            ))}
          </MUView>
        ) : null}
        {(showExtend && extendList.length > 6) ? (
          <MUListItem
            className="show-more"
            title=""
            beaconId={`${beaconId}ShowMoreExtend`}
            extraText={(showMore || selectedIndex > 5) ? '收起' : '更多分期选择'}
            arrow={(showMore || selectedIndex > 5) ? 'mu-icon-arrow-up' : 'mu-icon-arrow-down'}
            onClick={showMoreClick}
            hasBorder={false}
          />
        ) : null}
        {tipsText ? <MUView className="tips" style={themeColor === '#E60027' ? 'color: #FE7F05;background-color: #FFEEDC;' : ''}>{tipsText}</MUView> : null}
      </MUView>
    );
  }

  getThemeStyle = (a, b) => {
    let themeStyle = '';
    if (themeColor === '#E60027') {
      themeStyle = 'background-color: rgba(230, 0, 39, 0.15);';
      if (a === b) {
        themeStyle = `background-image: url(${require('./img/red-selected-bottom.png')});background-color: rgba(230, 0, 39, 0.15);`;
      }
    }
    return themeStyle;
  }

  get showModal() {
    const { modalType } = this.state;
    return modalType !== ModalType.None;
  }

  get modalContent() {
    const { modalType } = this.state;
    const { modalDesc, principalLimit } = this.configParams;
    let text = '';
    switch (modalType) {
      case ModalType.PrincipalNotEnoughTips:
        text = modalDesc[modalType].replace(/\$\{number\}/g, +principalLimit);
        break;
      case ModalType.OverDueTips:
        text = modalDesc[modalType].replace(/\$\{number\}/g, this.selectedCntInfo.overDueRate);
        break;
      case ModalType.StageTips:
      case ModalType.NoProduct:
        text = modalDesc[modalType];
        break;
      default: break;
    }
    return text;
  }

  get bankCardList() {
    const { bankCardList } = this.state;
    return (bankCardList || []).map(({
      bankName, cardType, bankCardNoMask, bankImage
    }) => ({
      contentText: `${bankName || ''} ${{ 1: '储蓄卡', 2: '信用卡', 6: '招行一网通' }[cardType || '1']} (${(bankCardNoMask || '').substr(-4)})`,
      contentImg: bankImage,
    }));
  }

  get selectedCntInfo() {
    const { cntInfoList, selectedCntIndex } = this.state;
    return (cntInfoList || []).length > selectedCntIndex ? cntInfoList[selectedCntIndex] : {};
  }

  get selectedBankCard() {
    const { bankCardList, selectedBankIndex } = this.state;
    return (bankCardList || []).length > selectedBankIndex ? bankCardList[selectedBankIndex] : {};
  }

  get repayPlanList() {
    return (this.selectedCntInfo.repayPlanList || []).map(({
      period, repayAmt,
      repayDate, repayFee,
      repayPrincipal,
    }) => ({
      cntText: `第${period}期`,
      date: [repayDate.substr(4, 2) == 1 ? repayDate.substr(0, 4) : '', repayDate.substr(4).replace(/(\d{2})(\d{2})/, '$1-$2')],
      totalAmtText: `${repayAmt}元`,
      amtDetailText: `含本金${repayPrincipal}元，费用${repayFee}元`,
    }));
  }

  get repayWayDes() {
    const { repayWayDesList } = this.configParams;
    const [repayWayDes = {}] = (repayWayDesList || []).filter((o) => o.paymentType === this.selectedCntInfo.repayType);
    return repayWayDes;
  }

  get btnDisable() {
    const { loading, totalPrincipal } = this.state;
    const { principalLimit } = this.configParams;
    return !(
      !loading && +totalPrincipal >= +principalLimit
    );
  }


  openIconModal() {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'clickTipModal', target: this });
    this.setState({
      openSmsCodeTip: true
    });
  }

  closeIconModal() {
    this.setState({
      openSmsCodeTip: false
    });
  }

  openPwdIconModal() {
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'clickPwdTipModal', target: this });
    this.setState({
      openPwdTip: true
    });
  }

  closePwdIconModal() {
    this.setState({
      openPwdTip: false
    });
  }

  onConfirmBtn() {
    //跳转去安全中心修改手机号页面
    const redirectUrl = getCurrentPageUrlWithArgs();
    Madp.redirectTo({
      url: `${urlDomain}/${Madp.getChannel()}/safecenter/#/memeber/reset-phone/select?needLogoutAfterModifyPhone=0&redirectUrl=${redirectUrl}`
    });
  }

  render() {
    const {
      openPwdTip,
      openSmsCodeTip,
      isShow, modalType,
      totalPrincipal, totalInterest,
      cntInfoList, showExtend,
      selectedCntIndex, recommendCntIndex,
      showMore,
      showBankDrawer, selectedBankIndex,
      loading, showRepayDrawer,
      showCouponSelector, couponObject,
      showSmsVerify,
      showTradePwdVerify,
      clearAndDisableInput,
      couponContentText, defaultCheckId, fullName,
      loadingCount, billInstallApplyInfo,showPhoneModal
    } = this.state;

    const { totalRepayAmt, installCnt, repayType } = billInstallApplyInfo || {};

    const {
      modalBtn, footerText,
      repayPlanTitle, paymentTitle,
      totalPrincipalTitle, submitText,
      prepayFeeDesc,
      verifyScene, repayVerifyScene,
    } = this.configParams;

    // 强制还款时信息回显
    if (this.forceRepay && !this.forceRepayFinish && this.selectedCntInfo) {
      this.selectedCntInfo.cnt = installCnt;
      this.selectedCntInfo.repayType = repayType;
    }

    return isShow ? (
      <MUView className="repay-staging">
        {/* 卡片 */}
        <MUView className="repay-staging__card">
          <MUView className="repay-staging__card__title">
            {totalPrincipalTitle}
            <MUImage
              className="repay-staging__card__title-icon"
              src={cardTitleIcon}
              beaconId="StageTips"
              onClick={() => this.setState({ modalType: ModalType.StageTips })}
            />
          </MUView>
          {/* 如果建案接口返回applyStatus=7需要还款但未完成还款时，用接口返回的金额回填页面 */}
          <MUView className="repay-staging__card__num">{this.forceRepay && !this.forceRepayFinish && totalRepayAmt ? totalRepayAmt : totalPrincipal}</MUView>
          <MUView className="repay-staging__card__link brand-text" beaconId="GoModify" onClick={this.goModify}>
            修改
            <MUIcon className="repay-staging__card__link-icon" value="arrow-right" size="15" />
          </MUView>
        </MUView>

        {/* 还款计划 */}
        <MUView className="repay-staging__block">
          <MUView className="repay-staging__block__title">{repayPlanTitle}</MUView>
          <MUView className="repay-staging__block__content">
            {Object.keys(couponObject || {}).some((o) => (couponObject[o] || []).length > 0)
              ? this.getBlockItem({
                beaconId: 'Coupon',
                labelText: '优惠券',
                contentText: couponContentText,
                iconValue: 'arrow-right',
                itemClick: () => {
                  if (this.repayError || (this.forceRepay && !this.forceRepayFinish)) {
                    Util.toast('您有一笔进行中的分期办理，当天不可修改');
                    return;
                  }
                  dispatchTrackEvent({
                    target: this,
                    event: EventTypes.BC,
                    beaconId: ((couponObject || {}).availableCouponDetailList || []).length > 0
                      ? 'ClickAvailableCoupons'
                      : 'ClickConditionalCoupons',
                  });
                  this.setState({ showCouponSelector: true });
                },
              }) : null}
            {this.getBlockItem({
              beaconId: 'Reriod',
              labelText: '分几期',
              contentText: this.selectedCntInfo.cnt ? `${this.selectedCntInfo.cnt}期` : '',
              iconValue: showExtend ? 'arrow-up' : 'arrow-down',
              subContentText: this.selectedCntInfo.yearInterestRate ? `折合年化利率(单利)${this.selectedCntInfo.yearInterestRate}%` : '',
              extendList: (cntInfoList || []).map((o) => ({ mainText: `${o.cnt}期`, subText: `月费率${o.feeRate}%` })),
              showExtend,
              showMore,
              selectedIndex: selectedCntIndex,
              recommendIndex: recommendCntIndex,
              itemClick: () => {
                if (this.repayError || (this.forceRepay && !this.forceRepayFinish)) {
                  Util.toast('您有一笔进行中的分期办理，当天不可修改');
                  return;
                }
                this.setState((preState) => ({ showExtend: !preState.showExtend }));
              },
              extendClick: (i) => {
                if (this.selectedCntDisable) return;
                this.selectedCntDisable = true;
                setStore.commit('UPDATE_BILLS_STAGING_INFO', {
                  cnt: this.cntList.length > i ? this.cntList[i] : '',
                });
                this.setState({ selectedCntIndex: i }, () => this.setCouponObject({ totalPrincipal },
                  () => {
                    const { selectedCoupon: stateSelectedCoupon } = this;
                    const { couponObject: { availableCouponDetailList } } = this.state;
                    const selectedCoupon = stateSelectedCoupon && (availableCouponDetailList || []).some((o) => o && o.awardNo === stateSelectedCoupon.awardNo)
                      ? stateSelectedCoupon : null;
                    this.setSelectedCoupon({ selectedCoupon },
                      () => this.setCntInfoList({ totalPrincipal },
                        async () => {
                          this.setCouponcontentText(() => this.selectedCntDisable = false);
                        }
                      ));
                  }));
              },
              showMoreClick: () => selectedCntIndex <= 5 && this.setState((preState) => ({ showMore: !preState.showMore })),
            })}
            {this.getBlockItem({
              beaconId: 'ReapyPlan',
              labelText: '怎么还',
              contentText: this.repayWayDes.title || '',
              subContentText: this.selectedCntInfo.firstRepayDate && this.selectedCntInfo.firstRepayAmt
                ? `首期${this.selectedCntInfo.firstRepayDate.replace(/(\d{4})(\d{2})(\d{2})/, (o, $1, $2, $3) => { return $2 == 1 ? `${$1}年${+$2}月${+$3}日` : `${+$2}月${+$3}日`; })} 应还#${+this.selectedCntInfo.firstRepayAmt}#元`
                : '',
              iconValue: 'arrow-right',
              tipsText: this.selectedCntInfo.prepayFeeType
                ? (prepayFeeDesc[this.selectedCntInfo.prepayFeeType] || '').replace(/\$\{number\}/g, Number(this.selectedCntInfo.prepayFee || ''))
                : '',
              itemClick: () => {
                if (this.repayError || (this.forceRepay && !this.forceRepayFinish)) {
                  Util.toast('您有一笔进行中的分期办理，当天不可修改');
                  return;
                }
                this.setState({ showRepayDrawer: true });
              },
            })}
          </MUView>
        </MUView>

        {/* 支付方式 */}
        {Number(totalInterest) ? (
          <MUView className="repay-staging__block">
            <MUView className="repay-staging__block__title">{paymentTitle}</MUView>
            <MUView className="repay-staging__block__content">
              {this.getBlockItem({
                labelText: '分期前需要支付利息',
                contentText: `${totalInterest}元`,
              })}
              {this.getBlockItem({
                beaconId: 'RepayWay',
                labelText: '支付方式',
                contentText: `${this.selectedBankCard.bankName || ''}${this.selectedBankCard.bankCardNoMask ? `(${this.selectedBankCard.bankCardNoMask.substr(-4)})` : ''}`,
                iconValue: 'arrow-right',
                contentImg: this.selectedBankCard.bankImage || '',
                itemClick: () => this.setState({ showBankDrawer: true }),
              })}
            </MUView>
          </MUView>
        ) : null}

        {/* 客服 */}
        {Util.showChatEntry() ? <MUView className="repay-staging__chat"><ChatEntry busiEntrance={Util.getBusiEntrance()} themeColor={themeColor} /></MUView> : null}

        {/* footer */}
        <MUView className={`repay-staging__footer ${Util.showChatEntry() ? '' : 'repay-staging__footer--nochat'}`}>{footerText}</MUView>

        {/* 底部提交 */}
        <MUView className="repay-staging__submit">
          {
            <Protocol
              trackPrefix="repayment.RepayStaging"
              onChecked={(v) => { this.setState({ isCheckedContract: v }); }}
              contractApplyList={this.contractApplyList}
              contractInfoList={this.contractInfos}
              billExtendInfo={{ totalPrincipal, totalInterest, selectedCntInfo: this.selectedCntInfo, selectedBillList: this.selectedBillList, repayWayDes: this.repayWayDes }}
            />
          }
          <MUButton
            className="repay-staging__submit__btn"
            type="primary"
            beaconId="Submit"
            loading={loading}
            onClick={async () => {
              this.setState({ loading: true });
              await this.preSubmit();
              this.setState({ loading: false });
            }}
            disabled={this.btnDisable}
          >
            {submitText}
          </MUButton>
        </MUView>

        {/* 优惠券 */}
        <CouponSelector
          ref={(ref) => { this.couponSelectorRef = ref; }}
          defaultCheckId={defaultCheckId}
          isOpened={showCouponSelector}
          coupons={couponObject}
          onCouponSelected={(selectedCoupon) => this.selectedCouponDisable || this.setSelectedCoupon({ selectedCoupon },
            () => this.setCntInfoList({ totalPrincipal }, this.setCouponcontentText))}
          onClose={() => this.setState({ showCouponSelector: false })}
          beaconId="RepayStagingCouponSelector"
        />

        {/* 银行卡抽屉 */}
        <MUDrawer
          className="repay-staging__bank-drawer"
          beaconId="RepayStagingBankDrawer"
          placement="bottom"
          show={showBankDrawer}
          onClose={() => this.setState({ showBankDrawer: false })}
          contentStyle={{
            borderRadius: `${Taro.pxTransform(16)} ${Taro.pxTransform(16)} 0 0`,
            backgroundColor: '#F3F3F3',
          }}
        >
          <MUView className="repay-staging__bank-drawer__title">
            支付方式
            <MUIcon
              className="repay-staging__bank-drawer__close"
              value="close2"
              size="16"
              color="#A6A6A6"
              beaconId="BankDrawerClose"
              onClick={() => this.setState({ showBankDrawer: false })}
            />
          </MUView>
          <MUView className="repay-staging__bank-drawer__list">
            {
              this.bankCardList.map((o, i) => (
                <MUView
                  className="bank-item"
                  key={o.contentText}
                  onClick={() => this.setSelectedBankCardIndex(i)}
                  beaconId="BankCardItem"
                >
                  <MUImage className="bank-item__logo" src={o.contentImg} />
                  <MUView className="bank-item__content">{o.contentText}</MUView>
                  <MUIcon
                    className={`bank-item__icon ${selectedBankIndex === i ? 'brand-text' : 'bank-item__icon--unchecked'}`}
                    value={selectedBankIndex === i ? 'checked' : 'unchecked'}
                    size="20"
                  />
                </MUView>
              ))
            }
          </MUView>
          <MUView className="repay-staging__bank-drawer__add" beaconId="BankCardAdd" onClick={this.addBankCard}>
            <MUIcon className="bank-add__logo" value="add-cicle" size="24" />
            <MUView className="bank-add__content">添加银行卡</MUView>
            <MUIcon className="bank-add__icon" value="arrow-right" size="15" color="#CACACA" />
          </MUView>
        </MUDrawer>

        {/* 怎么还抽屉 */}
        <MUDrawer
          className="repay-staging__repay-drawer"
          beaconId="RepayStagingRepayDrawer"
          placement="bottom"
          height="75%"
          show={showRepayDrawer}
          onClose={() => this.setState({ showRepayDrawer: false })}
          contentStyle={{
            borderRadius: `${Taro.pxTransform(16)} ${Taro.pxTransform(16)} 0 0`,
            backgroundColor: '#F3F3F3',
          }}
        >
          <MUView className="repay-staging__repay-drawer__title">
            <MUView className="title-text">怎么还</MUView>
            <MUView className="title-desc">{this.repayWayDes.title || ''}</MUView>
            <MUView className="title-subdesc">{this.repayWayDes.des || ''}</MUView>
            <MUIcon
              className="repay-staging__repay-drawer__close"
              value="close2"
              size="16"
              color="#A6A6A6"
              beaconId="RepayDrawerClose"
              onClick={() => this.setState({ showRepayDrawer: false })}
            />
          </MUView>
          <MUView className="repay-staging__repay-drawer__content">
            <MUView className="content-desc">
              {this.selectedCntInfo.cnt && this.selectedCntInfo.totalRepayFee
                ? `借满${this.selectedCntInfo.cnt}期，总费用${this.selectedCntInfo.totalRepayFee}元`
                : ''}
              <MUImage
                className="content-desc__icon"
                src={cardTitleIcon}
                beaconId="OverDueTips"
                onClick={() => this.setState({ modalType: ModalType.OverDueTips })}
              />
            </MUView>
            <MUScrollView
              className="content-view"
              onScroll={(e) => e && e.preventDefault && e.preventDefault()}
              scrollY
            >
              {this.repayPlanList.map((o) => (
                <MUView className="repay-item" key={o.cntText}>
                  <MUView className="repay-item__left">
                    <MUView style={{ display: 'inline-block' }}>
                      <MUView className="repay-item__left__title">{o.cntText}</MUView>
                      {o.date.map((_o) => { return _o ? <MUView className="repay-item__left__desc">{_o}</MUView> : null; })}
                    </MUView>
                  </MUView>
                  <MUView className="repay-item__middle">
                    <MUView className="repay-item__middle__circle brand-border" />
                  </MUView>
                  <MUView className="repay-item__right">
                    <MUView className="repay-item__right__title">{o.totalAmtText}</MUView>
                    <MUView className="repay-item__right__desc">{o.amtDetailText}</MUView>
                  </MUView>
                </MUView>
              ))}
            </MUScrollView>
          </MUView>
        </MUDrawer>

        {/* 验证动码 */}
        <MUSafeSmsCodeHalfWrap
          title="同意并输入验证码"
          beaconId="SmsCode"
          scene={+totalInterest > 0 ? repayVerifyScene : verifyScene}
          withLoginStatus
          replaceMsg=""
          needOnePass={false}
          isOpened={showSmsVerify}
          bizContent={this.transRefNo}
          onTitleIconClicked={this.openIconModal.bind(this)}
          onClose={() => this.setState({ smsToken: '', showSmsVerify: false })}
          onOk={(token) => { this.onSmsVerifySuccess('ok', token); }}
          onOposcExempt={(token) => { this.onSmsVerifySuccess('exempt', token); }}
          onVerifyFail={() => Util.toast('验证码错误，请重新输入')}
          onSendSuccess={() => Util.toast('动码发送成功')}
        />

        {/* 验证交易密码 */}
        <MUTradePasswordEncryptedWrap
          title="同意并输入6位数字交易密码"
          onTitleIconClicked={this.openPwdIconModal.bind(this)}
          beaconId="PasswordEncrypted"
          scene={+totalInterest > 0 ? repayVerifyScene : verifyScene}
          verifyType="TRA"
          onNeedModifyPass={() => Madp.showModal({
            title: '升级6位数字交易密码',
            content: '为提供更便捷的支付体验，特邀您升级为6位数字的交易密码',
            confirmText: '立即升级',
            confirmColor: themeColor,
            showCancel: false,
            success: (res) => res.confirm && Util.externalJump('MODIFY_PWD'),
          })}
          onForgotPass={() => Util.externalJump('SET_PWD')}
          isOpened={showTradePwdVerify}
          onClose={() => this.setState({
            showTradePwdVerify: false,
            clearAndDisableInput: true, // 清除密码残留
          }
          )}
          onOk={(msg, token) => { this.onTradeVerifySuccess(token); }}
          onOverPwAttemptNum={() => {
            Util.toast('密码输入次数超限，请稍后再试');
            this.setState({
              showTradePwdVerify: false,
              clearAndDisableInput: true, // 清除密码残留
            });
          }}
          clearAndDisableInput={clearAndDisableInput}
        />

        {/* 公共弹窗 */}
        <MUModal
          className="repay-staging__modal"
          beaconId="RepayStagingModal"
          beaconContent={{ cus: { modalType: Object.keys(ModalType)[modalType + 1] } }}
          isOpened={this.showModal}
          content={this.modalContent}
          confirmText={modalBtn}
          closeOnClickOverlay={false}
          onConfirm={this.clickModal}
        />
        <MUModal
          beaconId="tipModal"
          className="tipModal"
          type="text"
          isOpened={openSmsCodeTip}
          title="关于个人信息处理规则说明"
          content={`在以下场景中，${fullName}需要核实您的验证码，以验证您的手机号码是否真实、有效：<br>（1）注册、登录招联平台或您需要销户、挂失、解除挂失；<br>（2）您需要申请授信、借款或使用招联借款用于消费；<br>（3）您需要修改您向招联提供的信息（如姓名、手机号码）；<br>（4）您需要启用、修改手势密码，或设置、修改数字交易密码，或挂失、解挂失；<br>（5）其他需要验证您手机号码是否真实、有效的场景。<br>验证码是您的重要个人信息，一旦泄露或处理不当可能危害您的财产安全，招联将根据法律法规要求并参照行业最佳实践为您的个人信息安全提供保障。`}
          confirmText="我知道了"
          onClose={this.closeIconModal.bind(this)}
          closeOnClickOverlay={false}
          onConfirm={this.closeIconModal.bind(this)}
        />
        <MUModal
          beaconId="pwdTipModal"
          className="pwdTipModal"
          type="text"
          isOpened={openPwdTip}
          title="关于个人信息处理规则说明"
          content={`在以下场景中，${fullName}需要收集、验证您输入的交易密码：<br>(1)您在招联设置或修改您的交易密码时；<br>(2)您在招联进行借款、消费、红包提现等交易时；<br>(3)您在招联查看或获取重要信息时，如账户解挂、合同下载等。<br>交易密码是您的重要个人信息，一旦泄露或处理不当可能危害您的财产安全，招联将根据法律法规要求并参照行业最佳实践为您的个人信息安全提供保障。`}
          confirmText="我知道了"
          onClose={this.closePwdIconModal.bind(this)}
          closeOnClickOverlay={false}
          onConfirm={this.closePwdIconModal.bind(this)}
        />
        {/* 进入结果页前倒计时 */}
        <LoadingDialog
          ref={(ref) => { this.loadingDialog = ref; }}
          countTime={loadingCount}
          serviceType="bills-staging"
        />
        <MUModal isOpened={showPhoneModal} beaconId="phoneModal">
          <MUView className="phone_modal_content">
            <MUView className="topsection">
            <MUImage src={closeImg} className="close_img" onClick={this.closePhoneModal.bind(this)}/>
            </MUView>

             <MUView className="middlesection">
            <MUImage src={iconImg} className="icon_img" />
            </MUView>
            <MUText  className="textOne">手机号已失效</MUText>
            <MUText className="textTwo">为确保您的资金安全，请先绑定手机号</MUText>
            <MUButton beaconId="openPayBtn" type="primary" className="confirm-button" onClick={this.onConfirmBtn.bind(this)}>去绑定手机号</MUButton>
          </MUView>
        </MUModal>
      </MUView>
    ) : null;
  }
}
