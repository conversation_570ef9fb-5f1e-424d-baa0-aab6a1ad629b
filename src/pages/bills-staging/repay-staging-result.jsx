/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUButton, MURichText, MUSlogan, MUIcon, MUText
} from '@mu/zui';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import { miniProgramChannel } from '@utils/constants';
import {
  Url,
  isMuapp
} from '@mu/madp-utils';
import classNames from 'classnames';
import pageHoc from '@utils/pageHoc';
import './repay-staging-result.scss';

@track({ event: EventTypes.PO }, {
  pageId: 'RepayStagingResult',
  dispatchOnMount: true,
})
@pageHoc({ title: '账单分期' })
export default class RepayStagingResult extends Component {
  config = {
    navigationBarTitleText: '账单分期',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);

    this.state = {
      statusCofig: {},
    };
  }

  async componentWillMount() {
    const status = Url.getParam('status');
    this.initStatusConfigData(status);
  }

  // status = 0 为准入拒绝页, status = 1为成功, status = 2 为审核中
  // status = 3 为失败页, status = 4 为申请提交页, status = 5 为账单分期已提交页
  initStatusConfigData = (status) => {
    let statusConfigData = {};
    let extraStatus = '';
    if (status === '0') {
      // 协商还期间
      const duringNegotiate = Url.getParam('duringNegotiate');
      const riskCheckFail = Url.getParam('riskCheckFail');
      if (duringNegotiate === '1') {
        statusConfigData = {
          statusIcon: 'warning',
          statusText: '暂不符合办理条件',
          statusTips: '您已办理“协商还”业务，不可申请此业务',
        };
        extraStatus = 'DuringNegotiateRefuse';
      } else if (riskCheckFail === '1') {
        // 风险审核拒绝
        statusConfigData = {
          statusIcon: 'warning',
          statusText: '办理失败',
          statusTips: '很抱歉，您暂不符合办理条件，请继续累计信用',
        };
        extraStatus = 'RiskCheckFailRefuse';
      } else {
        statusConfigData = {
          statusIcon: 'warning',
          statusText: '暂不符合办理条件',
          statusTips: '很抱歉，您暂无可办理的借据，可能原因如下，详情请咨询招联客服',
          textLeftShow: true,
          statusDetail: [
            '1、您的借据已逾期',
            '2、您的借据均为已分期的借据',
            '3、可办理账单分期的借据总本金低于100元',
            '4、您已办理过相关延期服务'
          ]
        };
        extraStatus = 'CommonRefuse';
      }
    } else if (status === '1') {
      statusConfigData = {
        statusIcon: 'success',
        statusText: '账单分期已成功',
        statusTips: this.initSuccessStatusInfo()
      };
    } else if (status === '2') {
      statusConfigData = {
        statusIcon: 'waiting',
        statusText: '审批中',
        statusTips: '预计3个工作日审批完成，请耐心等待',
      };
    } else if (status === '3') {
      const systemError = Url.getParam('systemError');
      const stagingFail = Url.getParam('stagingFail');
      const repayFail = Url.getParam('repayFail');
      if (systemError === '1') {
        extraStatus = 'SystemError';
      } else if (stagingFail === '1') {
        extraStatus = 'StagingFail';
      } else if (repayFail === '1') {
        extraStatus = 'RepayFail';
      }
      statusConfigData = {
        statusIcon: 'warning',
        statusText: '办理失败',
        statusTips: '抱歉，当前系统异常，可联系在线客服处理',
      };
    } else if (status === '4') {
      statusConfigData = {
        statusIcon: 'success',
        statusText: '已提交审批',
        statusTips: '结果将于<span id=yellow style="color: #3477FF;">3个工作日</span>内短信通知',
      };
    } else if (status === '5') {
      statusConfigData = {
        statusIcon: 'success',
        statusText: '账单分期已提交',
        statusTips: '账单分期结果将在5分钟内通过短信发送给您',
      };
    }

    this.setState({
      statusCofig: statusConfigData
    });

    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: 'RepayStagingResultStatus',
      beaconContent: { cus: { status, extraStatus, statusText: statusConfigData.statusText, statusTips: statusConfigData.statusTips } }
    });
  }

  initSuccessStatusInfo = () => {
    const firstRepayDate = Url.getParam('firstRepayDate');
    return `账单分期已完成${firstRepayDate ? `，首期还款日为${firstRepayDate.replace(/(\d{4})(\d{2})(\d{2})/, (date, year, month, day) => `${+month}月${+day}日`)}` : ''}`;
  }

  clickBackBtnHandler = () => {
    const redirectUrl = Url.getParam('resultRedirectUrl') ? decodeURIComponent(Url.getParam('resultRedirectUrl')) : '';
    const isMiniProgramChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;

    // 特别处理如果为招联app场景下，非还款场景进入的时候直接关闭webview
    if (isMuapp() && redirectUrl.indexOf('repayment/#/') === -1) {
      Madp.closeWebView();
      return;
    }

    // 存在redirectUrl时，跳转到redirectUr
    if (redirectUrl && redirectUrl !== 'undefined') {
      if (isMiniProgramChannel) {
        Madp.miniProgram.reLaunch({
          url: redirectUrl
        });
      } else {
        Madp.redirectTo({ url: redirectUrl });
      }
    } else {
      if (isMiniProgramChannel) {
        Madp.miniProgram.reLaunch({
          url: '/pages/index/index'
        });
      } else {
        const length = window && window.history && window.history.length;
        if (length === 1) {
          Madp.closeWebView();
        } else {
          Madp.navigateBack({ delta: length - 1 });
        }
      }
    }
  }

  render() {
    const status = Url.getParam('status');
    const actualAmt = Url.getParam('actualAmt');
    const { statusCofig } = this.state;
    const { statusIcon, statusText, statusTips, textLeftShow, statusDetail } = statusCofig || {};

    return (<MUView className="pages-bg">
      <MUView className="repay-staging-result">
        <MUView className="repay-staging-result__icon">
          <MUIcon value={statusIcon} size={70} />
        </MUView>
        <MUView className="repay-staging-result__text">{statusText}</MUView>
        {(status === '1' || status === '5') && actualAmt && (
          <MUView className="repay-staging-result__amount">
            <MUText className="repay-staging-result__amount--unit">￥</MUText>
            <MUText className="repay-staging-result__amount--number">{actualAmt}</MUText>
          </MUView>
        )}
        <MURichText className={classNames('repay-staging-result__tips', { 'repay-staging-result__tips--left': textLeftShow })} nodes={statusTips} />
        {(status === '0' && statusDetail) && (
          <MUView className="repay-staging-result__detail">
            {statusDetail.map((item) => <MUView className="repay-staging-result__detail--item">{item}</MUView>)}
          </MUView>
        )}
        <MUButton
          beaconId="RepayStagingResultBackBtn"
          className="repay-staging-result__btn"
          type="primary"
          onClick={() => {
            this.clickBackBtnHandler();
          }}
        >返回</MUButton>
        <MUSlogan className="repay-staging-result__slogan" onlyLogo />
      </MUView>
    </MUView>);
  }
}
