/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import { MUView } from '@mu/zui';
import { track, EventTypes } from '@mu/madp-track';
import { Url } from '@mu/madp-utils';
import BillList from '@components/bill-list';
import EmptySign from '@components/empty-sign';
import { getStore, setStore } from '@api/store';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import pageHoc from '@utils/pageHoc';

import './bill-list-stages.scss';

// import '@components/bill-list/index.scss';
// import '@components/list-item/index.scss';
// import '@components/empty-sign/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/bill-list/index.scss')
  require('@components/list-item/index.scss')
  require('@components/empty-sign/index.scss')

}

@track({ event: EventTypes.PO }, {
  pageId: 'BillListStages',
  dispatchOnMount: true,
})
@pageHoc({ title: '账单分期' })
export default class BillListStages extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isShow: false,
      selectedBillList: [],
      stagableList: [],
      unStagableList: []
    };

    this.nearBills = [];
  }

  async componentDidMount() {
    await this.initData();
  }

  // eslint-disable-next-line react/sort-comp
  initData = async () => {
    // query
    const { creditType = 'D01' } = this.$router.params;

    // init nearBills
    this.nearBills = getStore('nearBills');
    if (!Object.keys(this.nearBills).length) {
      this.nearBills = await Dispatch.repayment.getNearBills();
    }

    // init stagableList & unStagableList
    const { repayBillList = [] } = this.nearBills || {};
    const [stagableList, unStagableList] = [[], []];
    (repayBillList || []).forEach((o) => {
      if (o.canInstall === 'Y' && o.limitType === creditType && !o.displayOverdueDays && !!o.surplusDays) {
        stagableList.push(o);
      } else {
        unStagableList.push(o);
      }
    });

    // init selectedBills
    let selectedBillList = getStore('selectedBillList');
    if (!(selectedBillList && selectedBillList instanceof Array && selectedBillList.length > 0)) {
      selectedBillList = [...stagableList];
      setStore({ selectedBillList });
    } else {
      // 根据比值取stagableList里对象的地址，保证selectedBillList和stagableList同源
      const [selectedBillStrMap, stagableStrMap] = [selectedBillList.map((o) => JSON.stringify(o)), stagableList.map((o) => JSON.stringify(o))];
      selectedBillList = stagableList.filter((item, i) => selectedBillStrMap.some((_o) => _o === stagableStrMap[i]));
    }

    this.setState({
      selectedBillList,
      stagableList,
      unStagableList,
      isShow: true,
    });
  }

  handleSelect = (bill) => {
    // 由于存在同笔借据，两个账单同时展示的情况，不能单纯用orderNo作为区分
    // 故直接用账单本身判断是否勾选
    let { selectedBillList } = this.state;
    const { stagableList } = this.state;
    const billIndex = selectedBillList.indexOf(bill);
    if (billIndex > -1) {
      // 取消选中，如果反选的是任意逾期账单，则反选所有小于该逾期天数的账单
      const maxOverdue = Math.max(...selectedBillList.map((o) => o.displayOverdueDays));
      if (maxOverdue && bill.displayOverdueDays) {
        // 有延期账单，并且点选的账单有延期的前提下，反选所有小于该逾期天数的账单
        // 先把自己去掉，否则后续判断大于等于选中逾期天数时会被保留
        selectedBillList.splice(billIndex, 1);
        selectedBillList = selectedBillList.filter((o) => o.displayOverdueDays >= bill.displayOverdueDays);
      } else {
        selectedBillList.splice(billIndex, 1);
      }
    } else {
      // 选中，判断最长逾期是否选中
      const unSelectedBillList = stagableList.filter((o) => selectedBillList.indexOf(o) === -1);
      const unSelectedMaxOverdue = Math.max(...unSelectedBillList.map((o) => o.displayOverdueDays));
      if (unSelectedMaxOverdue && (!bill.displayOverdueDays || bill.displayOverdueDays < unSelectedMaxOverdue)) {
        Util.toast('需优先勾选最长逾期账单或全部逾期账单');
        return;
      }
      selectedBillList.push(bill);
    }
    // setStore({ selectedBillList });
    this.setState({ selectedBillList });
  }

  submitRepayment = (param) => {
    const { mapCode } = this.$router.params;
    const redirectUrl = Url.getParam('redirectUrl') ? decodeURIComponent(Url.getParam('redirectUrl')) : '';
    const { selectedBillList } = this.state;
    setStore({ selectedBillList });
    Util.router.replace({
      path: '/pages/bills-staging/repay-staging?_windowSecureFlag=1',
      query: {
        ...param,
        selectedBill: 'Y',
        mapCode,
        redirectUrl
      },
    });
  }

  get btnDisable() {
    const { principalLimit = 0 } = this.$router.params;
    const { selectedBillList } = this.state;
    let totalPrincipal = 0;
    selectedBillList.forEach((o) => {
      totalPrincipal += +o.installTotalAmt;
    });
    return !(+(totalPrincipal.toFixed(2)) >= +principalLimit);
  }

  render() {
    const {
      isShow, selectedBillList,
      stagableList, unStagableList,
    } = this.state;

    return isShow ? (
      <MUView className="bill-list-stages">
        {((stagableList && stagableList.length > 0) || (unStagableList && unStagableList.length > 0)) ? (
          <BillList
            billList={stagableList}
            billType="stages"
            selectedBillList={selectedBillList}
            readOnlyBillList={unStagableList}
            showOtherChannelRepayTips={false}
            onSelect={this.handleSelect}
            submitRepayment={this.submitRepayment}
            cancelAll={() => this.setState({ selectedBillList: [] })}
            selectAll={() => this.setState({ selectedBillList: [...stagableList] })}
            disabledBtn={this.btnDisable}
          />
        ) : <EmptySign emptyText="暂无可分期账单" />}
      </MUView>
    ) : null;
  }
}
