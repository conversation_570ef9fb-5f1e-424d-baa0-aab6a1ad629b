/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import {
  MUView, MUText,
} from '@mu/zui';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import pageHoc from '@utils/pageHoc';
import CustomConfig from '@config/index';
import Util from '@utils/maxin-util';
import { Url } from '@mu/madp-utils';
import Madp from '@mu/madp';
import { miniProgramChannel } from '@utils/constants';

import './index.scss';

const themeColor = Util.getThemeColor(CustomConfig.theme);

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      custType: Number(Url.getParam('overdueDays') || 0) > 0 ? '2' : '1',
    }
  }
}, {
  pageId: 'DiversionGuide',
  dispatchOnMount: true,
})
@pageHoc({ title: '第三方借据待还' })
export default class HomeList extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
    this.repayAmt = Url.getParam('repayAmt') || '';
    this.overdueDays = Url.getParam('overdueDays') || '';
  }

  config = {
    navigationBarTitleText: '第三方借据待还'
  }

  componentDidMount() {
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '第三方借据待还' });
  }

  render() {
    const { repayAmt, overdueDays } = this;

    return (
      <MUView className="pages-bg">
        <MUView style={`min-height: ${CustomConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
          <MUView className={`diversion-guide${themeColor === '#E60027' ? ' diversion-guide--unicom' : ''}`}>
            <MUView className="diversion-guide__info">
              <MUView className="diversion-guide__info__title">请前往招联金融官方应用 查账·还款</MUView>
              <MUView className="diversion-guide__info__tip">温馨提示：具体待还金额及还款详情请以招联金融官方应用展示为准</MUView>
            </MUView>
            <MUView className="diversion-guide__detail">
              <MUView className="diversion-guide__detail__bill">
                <MUView>
                  今日应还
                  {Number(overdueDays || 0) > 0 ? (
                    <MUText className="bill-status">已逾期</MUText>
                  ) : null}
                </MUView>
                {Number(repayAmt || 0) > 0 ? (
                  <MUView className={Number(overdueDays || 0) > 0 ? ' overdue-text' : ''}>￥{repayAmt}</MUView>
                ) : null}
              </MUView>
              <MUView className={`diversion-guide__detail__desc${Number(overdueDays || 0) > 0 ? ' overdue-text' : ''}`}>
                {Number(overdueDays || 0) > 0 ? '您有第三方借据已逾期，请尽快前往“招联金融官方应用”查账还款' : '您有第三方借据待还款，请按时前往“招联金融官方应用” 查账还款，避免逾期'}
              </MUView>
            </MUView>
            <MUView
              className={`diversion-guide__confirm${themeColor === '#E60027' ? ' diversion-guide__confirm--unicom' : ''}${Number(overdueDays || 0) > 0 ? ' overdue-btn' : ''}`}
              beaconId="Confirm"
              onClick={() => {
                if (miniProgramChannel.indexOf(Madp.getChannel()) > -1) {
                  Madp.miniProgram.navigateBack();
                } else {
                  Madp.navigateBack();
                }
              }}
            >我知道了</MUView>
          </MUView>
        </MUView>
      </MUView>
    );
  }
}
