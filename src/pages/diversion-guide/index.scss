@mixin std-text($size, $lineHeight, $weight, $color, $textAlign) {
  font-size: $size;
  line-height: $lineHeight;
  font-weight: $weight;
  color: $color;
  text-align: $textAlign;
}
.diversion-guide {
  background: linear-gradient(180deg, #ffffff 0%, #eef4ffb0 15%, #dae6ff99 55.00000000000001%, #d9e5ff99 74%, #f3f3f3 100%);
  &--unicom {
    background: linear-gradient(180deg, #ffffff 0%, #ffeeeeb0 15%, #ffdada99 59%, #ffd9d999 74%, #f3f3f3 100%);
  }
  &__info {
    padding: 73px 40px 40px;
    &__title {
      @include std-text(40px, 48px, 600, #333, left);
    }
    &__tip {
      margin-top: 20px;
      @include std-text(28px, 42px, 400, #808080, left);
    }
  }
  &__detail {
    margin: 0 20px 48px;
    padding: 0 20px 20px;
    border: 2px solid #fff;
    border-radius: 24px;
    background: linear-gradient(180deg, #eef3fc 0%, #ffffff 100%);
    &__bill {
      padding: 32px 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      @include std-text(32px, 34px, 600, #3d3d3d, left);
      .bill-status {
        margin-left: 10px;
        padding: 2px 10px;
        background: #cc1f15;
        border-radius: 4px;
        @include std-text(22px, 32px, 600, #fff, center);
      }
    }
    &__desc {
      padding: 20px;
      background: #f3f3f3;
      border-radius: 8px;
      @include std-text(24px, 36px, 400, #808080, left);
    }
  }
  &__confirm {
    margin: 0 40px;
    padding: 23px 0;
    background: #3477ff;
    border-radius: 50px;
    @include std-text(36px, 54px, 600, #fff, center);
    &--unicom {
      background: #e60027;
    }
  }
  .overdue-text {
    color: #cc1f15;
  }
  .overdue-btn {
    background: #cc1f15;
  }
}