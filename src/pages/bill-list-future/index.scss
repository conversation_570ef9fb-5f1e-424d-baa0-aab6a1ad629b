@import '../../components/weapp/index.scss';

.future-list {

  // 客服组件样式
  .chat-entry {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: -186px;
  }

  @supports (padding-bottom: constant(safe-area-inset-bottom)) {
    .chat-entry {
      margin-top: calc(-186px - constant(safe-area-inset-bottom));
    }
  }

  @supports (padding-bottom: env(safe-area-inset-bottom)) {
    .chat-entry {
      margin-top: calc(-186px - env(safe-area-inset-bottom));
    }
  }

  .future-list-content {
    width: 100%;
    min-height: 100vh; // 很关键

    &-header {
      background-color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 60px 0;

      .repay-date {
        font-size: 32px;
        line-height: 32px;
        color: #808080;
      }

      .repay-amount {
        margin-top: 30px;
        font-size: 100px;
        line-height: 100px;
        font-weight: bold;
        color: #333333;
        font-family: DIN Alternate;
      }

      .repay-desc {
        height: 120px;
        width: 670px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        padding: 20px 28px;
        margin-top: 30px;
        border-radius: 16px;
        color: #808080;
        font-size: 26px;
        line-height: 40px;
        background: #F9F9F9;

        &-highlight {
          display: inline;
          color: #FF8844;
        }
      }
    }
  }

  .theme-background-color {
    background: $color-brand;
  }
}

.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}