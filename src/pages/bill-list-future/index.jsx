/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import { <PERSON><PERSON><PERSON>iew, MUNavBarWeapp } from '@mu/zui';
import { ChatEntry } from '@mu/chat-entry-component';
import { track, EventTypes, dispatchTrackEvent, disableTrackAlert } from '@mu/madp-track';
import { Url } from '@mu/madp-utils';
import BillListNew from '@components/bill-list-new';
import EmptySign from '@components/empty-sign';
import { getStore, setStore } from '@api/store';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import pageHoc from '@utils/pageHoc';
import ChannelConfig from '@config/index';
import dayjs from 'dayjs';

import './index.scss';

// import '@components/bill-list/index.scss';
// import '@components/list-item/index.scss';
// import '@components/empty-sign/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/bill-list-new/index.scss');
  require('@components/list-item-new/index.scss');
  require('@components/empty-sign/index.scss');
}

const themeColor = Util.getThemeColor(ChannelConfig.theme);

disableTrackAlert();
@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      custStatus: Url.getParam('custStatus') || '',
    }
  }
}, {
  pageId: 'BillListFuture',
  dispatchOnMount: true,
})
@pageHoc({ title: '待还账单' })
export default class BillListFuture extends Component {
  config = {
    navigationBarTitleText: '待还账单',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      selectedBillList: [],
      futureBillList: [],
      readOnlyBillList: [],
      unicomBillList: [],
    };
    this.repayDate = Url.getParam('repayDate');
    this.deductTime = Url.getParam('deductTime');
    this.billType = Url.getParam('billType') || 'future';
  }

  get getformattedDateStr() {
    const dateCollection = Util.getDateCollection(this.repayDate);
    return dateCollection.length === 3 ? `${dateCollection[1]}月${dateCollection[2]}日` : '';
  }

  get earliestRepayDate() {
    const dateStr = this.repayDate ? dayjs(this.repayDate).subtract(7, 'day').format('MM月DD日') : '';
    return dateStr;
  }

  get deductTimeText() {
    const deductTimeText = (this.deductTime && Number((this.deductTime || '').substring(0, 2) || '') <= 12) ? `上午${this.deductTime}` : this.deductTime;
    return deductTimeText ? `还款日${deductTimeText}开始自动扣款，请预留足够金额` : '还款日上午开始自动扣款，请预留足够金额';
  }

  get totalRepaySum() {
    let totalRepaySum = 0;
    const { selectedBillList } = this.state;
    selectedBillList.forEach((bill) => {
      totalRepaySum += +bill.surplusPayTotalAmt || +bill.shouldReturnAmt || Number('0.00');
    });
    return totalRepaySum.toFixed(2);
  }

  async componentDidMount() {
    // 指定场景取缓存，其它场景默认取近期待还（未来第1期）
    let billList = [];
    if ((Url.getParam('fromIndex') === '1') || (Url.getParam('fromDispatch') === '1') || (this.billType === 'extend')) {
      billList = getStore('futureBillList') || [];
    } else {
      const data = await Dispatch.repayment.getNearBills();
      billList = ((data || {}).repayBillList || []).concat((data || {}).showBillList || []);
    }
    let futureBillList = [];
    const readOnlyBillList = [];
    billList.forEach((bill) => {
      if (bill.displayStatus === '2') {
        futureBillList.push(bill);
      } else if (bill.displayStatus === '0') {
        readOnlyBillList.push(bill);
      }
    });
    if (this.billType === 'extend') futureBillList = [...billList]; // 延后还（延长还款日）
    const unicomBillList = futureBillList.filter((bill) => bill.belongUnicomContractOrder === 'Y');
    if (readOnlyBillList.length) dispatchTrackEvent({ target: this, event: EventTypes.PO, beaconId: 'DisplayOnly' });
    this.setState({
      futureBillList,
      selectedBillList: futureBillList.slice(0),
      readOnlyBillList,
      unicomBillList
    });
  }

  render() {
    const { selectedBillList, futureBillList, readOnlyBillList, unicomBillList } = this.state;
    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="待还账单"
          leftArea={[
            {
              type: 'icon',
              value: 'back'
            }
          ]}
        />
        <MUView className="pages-bg future-list">
          <MUView className="future-list-content" style={`min-height: ${ChannelConfig.showMuNavBar ? 'calc(100vh - 100px)' : ''};`}>
            {(futureBillList && futureBillList.length > 0) ? (
              <MUView>
                <MUView className="future-list-content-header">
                  {this.getformattedDateStr ? (<MUView className="repay-date">{this.getformattedDateStr}应还(元)</MUView>) : null}
                  <MUView className="repay-amount">{this.totalRepaySum}</MUView>
                  {this.billType !== 'extend' && this.earliestRepayDate ? (<MUView className="repay-desc">
                    <MUView><MUView className="repay-desc-highlight">{this.earliestRepayDate}</MUView>起可主动还款</MUView>
                    <MUView>{this.deductTimeText}</MUView>
                  </MUView>) : null}
                </MUView>
                <BillListNew
                  billList={futureBillList}
                  billType={this.billType}
                  selectedBillList={selectedBillList}
                  readOnlyBillList={readOnlyBillList}
                  unicomBillList={unicomBillList}
                  earliestRepayDate={this.earliestRepayDate}
                  disabledSelect
                  disabledBtn
                  checkedIcon={this.billType !== 'extend'}
                  custStatus={Url.getParam('custStatus') || ''}
                />
              </MUView>
            ) : <EmptySign />}
          </MUView>
          {Util.showChatEntry() && <ChatEntry busiEntrance={Util.getBusiEntrance()} themeColor={themeColor} />}
        </MUView>
      </MUView>
    );
  }
}
