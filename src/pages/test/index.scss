.test-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #E1EAFF 1%, #FFFFFF 26%);
  position: relative;
  overflow: hidden;

  .header {
    padding: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .welcome-text {
      font-family: PingFangSC-Semibold;
      font-size: 40px;
      line-height: 60px;
      color: #000000;
    }

    .close-btn {
      width: 24px;
      height: 24px;
      background-color: #B3B3B3;
      mask-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M24 1.41176L13.4118 12L24 22.5882L22.5882 24L12 13.4118L1.41176 24L0 22.5882L10.5882 12L0 1.41176L1.41176 0L12 10.5882L22.5882 0L24 1.41176Z'/%3E%3C/svg%3E");
    }
  }

  .content {
    padding: 0 40px;
    position: relative;

    .deer-image {
      width: 240px;
      height: 240px;
      margin: 0 auto;
      background-image: url('https://image-resource.mastergo.com/84465026348141/84465026348143/361e88b221884e83d7e66f69b48d62c8.png');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }

    .rate-text {
      margin-top: 60px;
      font-family: PingFangSC-Semibold;
      font-size: 36px;
      line-height: 42px;
      color: #A9643C;
    }

    .period-text {
      margin-top: 16px;
      font-family: PingFangSC-Regular;
      font-size: 18px;
      line-height: 28px;
      color: #A9643C;
      opacity: 0.7;
      text-align: right;
    }

    .promo-text {
      margin-top: 40px;
      font-family: PingFangSC-Regular;
      font-size: 24px;
      line-height: 30px;
      color: #808080;
    }
  }

  .footer {
    position: absolute;
    bottom: 68px;
    left: 0;
    right: 0;
    padding: 0 40px;

    .evaluate-btn {
      background: linear-gradient(320deg, #FE800D 0%, #FFB51F 91%);
      box-shadow: inset 0px 1px 2px 0px rgba(172, 93, 30, 0.5303), 
                  1px 2px 2px 0px rgba(172, 67, 0, 0.3);
      height: 100px;
      border-radius: 50px;
    }
  }
}
