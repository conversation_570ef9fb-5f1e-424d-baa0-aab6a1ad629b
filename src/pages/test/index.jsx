import { <PERSON><PERSON><PERSON><PERSON><PERSON>, M<PERSON><PERSON>utton, MUText } from '@mu/zui';
import './index.scss';

const TestPage = () => {
  return (
    <MUDrawer 
      show={true}
      placement="bottom"
      height="100%"
      mask={false}
      contentStyle={{ background: 'transparent' }}
    >
      <div className="test-container">
        <div className="header">
          <MUText className="welcome-text">欢迎回到招联</MUText>
          <div className="close-btn" />
        </div>
        
        <div className="content">
          <div className="deer-image" />
          <MUText className="rate-text">低至4.8%年化利率(单利)</MUText>
          <MUText className="period-text">最高12期</MUText>
          <MUText className="promo-text">完成额度评估有机会获得借款限时优惠</MUText>
        </div>
        
        <div className="footer">
          <MUButton 
            type="primary" 
            full
            className="evaluate-btn"
          >
            立即评估
          </MUButton>
        </div>
      </div>
    </MUDrawer>
  );
};

export default TestPage;
