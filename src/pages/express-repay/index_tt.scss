.repayment-express .amountInput input {
  height: auto;
}

.coupon-selector {
  .left__content__main {
    position: unset !important;
    margin-top: 8px;
  }

  .left__content__sub {
    margin-top: -10px !important;
  }

  .content__detail .content__more {
    position: absolute;
    bottom: 4px;
  }

  .middle__content-desc-multi :nth-child(2) {
    display: inline-block !important;
    vertical-align: bottom;
  }

  .extend__detail__item-single .single {
    width: calc(100vw - 100px - 124px) !important;
  }
}

.mu-trade-password-dialog .mu-dialog__container {
  bottom: calc(430px + env(safe-area-inset-bottom));
}

.repay-check-out-warper .repay-check-out-select-repay-way {
  height: 125px;
}
