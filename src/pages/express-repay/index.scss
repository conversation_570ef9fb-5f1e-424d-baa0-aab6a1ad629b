@import '../../components/weapp/index.scss';

.repayment-express {
  /* stylelint-disable-next-line */
  font-family: 'PingFangSC-Regular';
  position: relative;

  .repayment-express-content.content-blur {
    filter: blur(15px);
  }

  .topView {
    background: #fff;
    margin-bottom: 20px;
    padding-bottom: 40px;
    &-court-cost-amt {
      margin: -10px auto 0;
      font-size: 28px;
      line-height: 28px;
      color: #a6a6a6;
      font-weight: 400;
      text-align: center;
    }
    &-repay-bubble {
      margin: -30px 0 10px;
    }
  }

  .sum-title {
    width: 100%;
    font-size: 30px;
    line-height: 30px;
    text-align: center;
    padding-top: 50px;
    color: #666;
  }

  .split {
    height: 24px;
  }

  .input-area {
    position: relative;

    .edit-img {
      position: absolute;
      width: 36px;
      height: 36px;
      bottom: 50px;
      left: 50%;
      pointer-events: none;
    }
  }

  .amountInput {
    text-align: center;
    color: #333;
    background-color: transparent;

    height: 100px;
    padding-top: 30px;
    padding-bottom: 40px;

    .at-input__container {
      height: 100px;
    }

    input:focus {
      outline: none;
    }

    &.at-input::after {
      border-bottom: 0;
    }
  }

  .amountInput input,
  .amountInput {
    line-height: 100px;
    font-size: 100px;
    font-weight: bold;
    font-family: "DIN Alternate";
    border: none;
    text-align: center;
  }

  .amountTip {
    position: relative;
    color: #dc132c;
    font-size: 28px;
    padding: 0 30px 40px;
    line-height: normal;
  }

  .amountTip::after {
    content: "";
    position: absolute;
    transform-origin: center;
    box-sizing: border-box;
    pointer-events: none;
    top: auto;
    left: 0.64rem;
    right: 0;
    bottom: 0;
    transform: scaleY(0.5);
    /* stylelint-disable-next-line */
    border-bottom: 1PX solid #E5E5E5;
  }

  &-content__reduction {
    margin: 0 20px;
    border-radius: 12px;
    overflow: hidden;
    .repayinfo {
      background: #FFF;
      display: flex;
      height: 100px;
      font-size: 32px;
      line-height: 32px;
      font-weight: 600;
      align-items: center;
      justify-content: space-between;
      padding: 0 30px;
      color: #333;
      &__title {
        display: flex;
        align-items: center;
        &__bubble {
          margin-left: 10px;
          padding: 2px 10px;
          border-radius: 4px;
          font-size: 22px;
          color: #3477FF;
          background: #E1EBFF;
          &--theme {
            color: #E60027;
            background: rgba(230, 0, 39, 0.15);
          }
        }
      }
      &__content {
        font-weight: normal;
      }
  
      .check_but {
        color: #3477FF;
        margin-left: 40px;
  
        &.disabled {
          color: #A6A6A6;
        }
      }
      .mu-icon {
        margin: 0 0 2px 10px;
      }
    }
  
    // .repayinfo-split {
    //   margin-left: 30px;
    //   transform: scaleY(0.5);
    //   /* stylelint-disable-next-line */
    //   border-bottom: 1PX solid #E5E5E5;
    // }
  
    .repay-wrapper-coupon-tip {
      margin-top: 0;
      background: #fff;
      overflow: hidden;
      .guide-coupon-choose {
        margin: 0 10px 10px 10px;
        padding: 24px 30px 24px 20px;
        border-radius: 12px;
        background: #FFF9EE;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &__left {
          display: flex;
          &__picture {
            width: 40px;
            height: 42px;
            font-size: 0;
            .taro-img, image {
              width: 100%;
              height: 100%;
            }
          }
          &__text {
            margin-left: 12px;
            font-size: 28px;
            line-height: 42px;
            color: #333;
            font-weight: 500;
            &--special {
              color: #FF8844;
            }
          }
        }
        &__right {
          display: flex;
          align-items: center;
          &__text {
            margin-right: 10px;
            font-size: 28px;
            line-height: 42px;
            color: #333;
          }
          &__picture {
            width: 28px;
            height: 28px;
            font-size: 0;
          }
        }
      }
    }
  
    .repay-wrapper-coupon {
      background: #FFFFFF;
      padding: 24px 30px;
      font-size: 32px;
      display: flex;
      align-items: center;
      justify-content: space-between;
  
      &-title {
        overflow: hidden;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: inherit;
        font-size: inherit;
        line-height: 1.6;
        width: 164px;
        min-width: 164px;
        font-weight: 600;
      }
  
      &-extra {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        line-height: 1.6;
  
        &-info {
          overflow: hidden;
          -o-text-overflow: ellipsis;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
          color: #a6a6a6;
          font-size: 32px;
          vertical-align: middle;
          box-sizing: border-box;
          display: inline-block;
        }
  
        &-icon {
          &-img {
            width: 26px;
            height: 26px;
            vertical-align: -4px;
          }
        }
      }
  
      .icon-container {
        display: flex;
        align-items: center;
        justify-content: center;
      }
  
      .text-color {
        color: #333333;
  
        .icon-style {
          width: 32px;
          height: 32px;
          margin-right: 10px;
        }
  
        .number-color {
          color: #FF8844;
        }
      }
  
      .useless-color {
        color: gray;
      }
    }
  }

  .avoidDupPayModal {
    .mu-modal__footer .at-modal__action .at-button {
      width: 100%;
    }

    .mu-modal__container {
      overflow: visible;
    }

    .dialog__infomation {
      width: calc(100% - 40px);
      position: absolute;
      display: flex;
      align-items: center;
      bottom: -48px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 20px;
      color: #ffffff;
      text-align: center;
      //line-height: 32px;
      z-index: 2001;

      &--tag {
        width: 68px;
        min-width: 68px;
        height: 32px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        font-size: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
      }

      // --兼容微信小程序
      >mu-view:nth-child(2) {
        flex: 1;
      }

      &--text {
        flex: 1;
        text-align: center;
        margin-right: 68px;
        // 保证当文字接近22字时一行显示完，优先级大于居中
        white-space: nowrap;
        line-height: 32px;
      }
    }
  }

  .repay-coupon-dialog {
    &-content {
      display: flex;
      flex-direction: column;
      align-items: center;

      &__wrap {
        text-align: center;
      }

      &-icon {
        margin-top: 12px;
        margin-bottom: 24px;
        display: block;
      }

      &-text {
        margin: 0 auto;
        font-size: 28px;
        line-height: 40px;
        width: 450px;
        color: #808080;
        margin-bottom: 20px;
      }

      &-tip {
        margin: 0 auto;
        font-size: 32px;
        line-height: 60px;
        width: 450px;
        text-align: center;
      }

      &-confirm {
        font-size: 36px;
        width: 450px;
      }

      &-cancel {
        margin: 0 auto;
        border: none;
        background-color: #FFF !important;
        color: #808080;
        font-size: 28px;
        width: 450px;
        margin-bottom: 20px;
        border-color: #FFF !important;
      }

      &__picture {
        margin: 20px auto 0;
        width: 120px;
        height: 120px;
        font-size: 0;
        .taro-img, image {
          width: 100%;
          height: 100%;
        }
      }

      &__title {
        margin-top: 46px;
        font-size: 36px;
        line-height: 54px;
        color: #333;
        font-weight: 600;
      }

      &__explain {
        margin: 20px 40px 40px;
        font-size: 28px;
        line-height: 42px;
        color: #808080;
        &--special {
          color: #FF8844;
        }
      }
    }
  }

  .highlight-text {
    color: #FF8844;
  }

  .chat-entry {
    position: fixed;
    bottom: calc(120px + constant(safe-area-inset-bottom));
    bottom: calc(120px + env(safe-area-inset-bottom));
    left: 0;
    right: 0;

    .chat-entry__bottom {
      margin: 36px 0;
    }

    display: flex;
    justify-content: center;
  }

  .invalidity-page {
    &-no-info {
      font-size: 50px;
      padding: 200px 120px 300px 120px;
      font-weight: 600;

      .go-index-text {
        color: #3477FF;
      }
    }

    &-logo-text {
      font-size: 80px;
      padding-top: 50px;
      text-align: center;
      font-weight: 600;
    }
  }

  .consult-overdue-modal {
    .mu-modal__container {
      padding: 64px 40px 0;
      width: 480px;
    }
    &__top {
      width: 120px;
      height: 120px;
      margin: 0 auto;
    }
    &__content {
      margin-top: 40px;
      text-align: center;
      &__title {
        height: 54px;
        font-size: 36px;
        line-height: 54px;
        color: #333;
        font-weight: 500;
      }
      &__explain {
        margin-top: 20px;
        font-size: 28px;
        line-height: 42px;
        color: #808080;
        font-weight: 400;
        &--point {
          color: #FE5B5E;
          font-weight: 500;
        }
      }
    }
    &__button {
      margin: 40px 0;
    }
  }

  .restaging-modal {
    .mu-modal__container {
      padding: 60px 40px 10px 40px;
      width: 480px;
    }

    &-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .img {
        width: 120px;
        height: 120px;
      }

      image {
        width: 120px;
        height: 120px;
      }

      img {
        width: 120px;
        height: 120px;
      }

      .title {
        margin-top: 30px;
        height: 54px;
        color: #333333;
        text-align: center;
        font-size: 36px;
        font-weight: 600;
        line-height: 54px;
      }

      .desc {
        margin-top: 20px;
        height: 84px;
        color: #808080;
        text-align: center;
        font-size: 28px;
        line-height: 42px;
      }

      .btn {
        width: 480px;
        height: 88px;
        margin-top: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 36px;
        color: #FFFFFF;
        background: #3477FF;
        border-radius: 8px;
      }


      .sub-btn {
        width: 140px;
        height: 42px;
        line-height: 42px;
        margin-top: 15px;
        padding: 10px 20px 0px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 28px;
        color: #808080;
      }

      .slogan {
        width: 206px;
        height: 44px;
        padding-top: 30px;
        padding-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 206px;
          height: 44px;
        }
  
        &-plus {
          width: 249px;
          
          img {
            width: 249px;
            height: 44px;
          }
        }
      }
    }
  }

  .extend-modal {
    .mu-modal__container {
      padding: 60px 40px 10px 40px;
      width: 480px;
    }

    &-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .img {
        width: 120px;
        height: 120px;
      }

      image {
        width: 120px;
        height: 120px;
      }

      img {
        width: 120px;
        height: 120px;
      }

      .title {
        margin-top: 40px;
        height: 54px;
        color: #333333;
        text-align: center;
        font-size: 36px;
        font-weight: 600;
        line-height: 54px;
      }

      .desc {
        margin-top: 20px;
        height: 84px;
        color: #808080;
        text-align: center;
        font-size: 28px;
        line-height: 42px;
      }

      .btn {
        width: 480px;
        height: 88px;
        margin-top: 40px;
        margin-bottom: 30px;
        background: #3477FF;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 36px;
        color: #FFFFFF;
      }

      .slogan {
        width: 206px;
        height: 44px;
        padding-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 206px;
          height: 44px;
        }
  
        &-plus {
          width: 249px;
          
          img {
            width: 249px;
            height: 44px;
          }
        }
      }
    }
  }

  .consult-repay-modal {
    .mu-modal__container {
      padding: 60px 40px 10px 40px;
      width: 480px;
    }

    &-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .img {
        width: 120px;
        height: 120px;
      }


      .title {
        margin-top: 40px;
        height: 54px;
        color: #333333;
        text-align: center;
        font-size: 36px;
        font-weight: 600;
        line-height: 54px;
      }

      .desc {
        margin-top: 20px;
        color: #808080;
        text-align: center;
        font-size: 28px;
        line-height: 42px;
      }

      .btn {
        width: 480px;
        height: 88px;
        margin-top: 40px;
        margin-bottom: 30px;
        background: #3477FF;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 36px;
        color: #FFFFFF;
      }
    }
  }

  .repay-detail {
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 40px 20px 0;
    background-color: #F3F3F3;
  
    &__title {
      width: 100%;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333333;
      font-size: 36px;
      font-weight: 600;
      line-height: 36px;
    }
  
    &__close {
      width: 40px;
      height: 40px;
      position: absolute;
      right: 30px;
      top: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  
    &__sum {
      height: 38px;
      padding: 32px 0 20px 20px;
      color: #333333;
      font-size: 26px;
      font-weight: 400;
      line-height: 38px;
  
      &--highlight {
        color: #FF8844;
      }
    }
  
    &-content {
      width: 100%;
      overflow-y: scroll;
      overflow-x: hidden;
  
      &-item {
        padding: 28px 20px 20px;
        margin-bottom: 12px;
        display: flex;
        flex-direction: column;
        background-color: #FFFFFF;
        border-radius: 16px;

        &-top {
          height: 32px;
          display: flex;
          align-items: flex-end;

          &__amount {
            margin-left: 20px;
            color: #333333;
            font-size: 30px;
            font-weight: 600;
            line-height: 32px;
            font-family: "PingFang SC";
          }

          .grap {
            color: #808080;
          }

          &__waive {
            color: #333333;
            font-size: 26px;
            font-weight: 400;
            line-height: 26px;

            &--highlight {
              color: #FF8844;
            }
          }
        }
  
        &__detail {
          margin: 15px 0 0 20px;
          color: #808080;
          font-size: 24px;
          font-weight: normal;
          line-height: 38px;
          font-family: "PingFang SC";
        }
  
        &__sub {
          width: 100%;
          height: 48px;
          display: flex;
          align-items: center;
          box-sizing: border-box;
          padding-left: 20px;
          margin-top: 15px;
          border-radius: 8px;
          color: #808080;
          font-size: 24px;
          font-weight: 400;
          line-height: 40px;
          opacity: 0.5;
          background-color: #F3F3F3;;
    
          &--merchant {
            margin-left: 10px;
          }
        }
      }

      &-tips {
        width: 100%;
        padding-top: 10px;
        color: #808080;
        font-size: 24px;
        font-weight: 400;
        opacity: 0.5;
      }
  
      &-placeholder {
        width: 100%;
        height: 50px;
      }
    }
  }
}

.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}