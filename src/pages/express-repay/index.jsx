/* eslint-disable react/sort-comp */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUInput, MUImage, MUModal, MUText, MUIcon, MUButton, MUNavBarWeapp
} from '@mu/zui';
import { CouponSelector } from '@mu/coupon-selector';
import { injectState } from '@mu/leda';
import { repaymentFn } from '@mu/business-plugin-utils';
import { opService, getPageConf, stayWithTime, getLoginInfo } from '@mu/business-basic';
import classNames from 'classnames';
import Statistic from '@components/statistic/index';
import CustomConfig from '@config/index';
import { ChatEntry } from '@mu/chat-entry-component';
import RepayCheckOut from '@components/repay-check-out/index';
import InvalidityModal from './components/InvalidityModal';
import { debounce, Url, isIOS } from '@mu/madp-utils';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import pageHoc from '@utils/pageHoc';
import {
  filterCoupon, getdefaultCheckCouponId, prepareRepayDetailList, is0100Bills,
  getSelectedBillCapitalsAndList, commonGetOverPayAmtRepayFlag, commonGetActualRepayAmt,
  transformNewCouponAPIRes, filterWaiveCoupons, getFeeInteRightInfo, filterQualCoupons,
} from '@utils/repay-util';
import Dispatch from '@api/actions';
import { getStore, setStore } from '@api/store';
import CouponTip from '@components/coupon-tip';
import Util from '@utils/maxin-util';
import { repayStatusType, sloganUrl, EVENT_CODE_MAP } from '@utils/constants';
import RepayBubble from '@components/repay-bubble/index';
import OverpayDrawer from '@components/overpay-drawer';
import RepayDetailDrawer from './components/repay-detail-drawer';
import { OpRepayment } from '@mu/op-comp';
import './index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@mu/coupon-selector/dist/styles/index.scss');
  require('@components/repay-modal/index.scss');
  require('@components/repay-bubble/index.scss');
  require('@components/repay-way/index.scss');
  require('@components/statistic/index.scss');
  require('@components/bottom-drawer/index.scss');
  require('@components/loading-dialog/index.scss');
  // 引用一次
  require('@components/repay-check-out/index.scss');
  require('@components/overpay-drawer/index.scss');
}

if (process.env.TARO_ENV === 'tt') {
  require('./index_tt.scss');
}

if (process.env.TARO_ENV === 'swan') {
  require('./index_swan.scss');
}

const editImg = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828520342.png';

const themeColor = Util.getThemeColor(CustomConfig.theme);
const { repayIndexFn } = repaymentFn || {};

const expressRepayPageId = repayIndexFn.getPageId() || 'b976e188-0491-4a97-8208-5c7459fbc0cd';

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: expressRepayPageId
    }
  }
}, {
  pageId: 'expressRepay',
  dispatchOnMount: true,
})
@pageHoc({ title: '还款' })
@injectState({
  pageId() {
    return expressRepayPageId;
  },
  getPageConf: () => getPageConf(expressRepayPageId, true),
  stateKeys: repayIndexFn.getLedaNameList()
})
export default class ExpressRepay extends Component {
  config = {
    navigationBarTitleText: '还款',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  };

  constructor(props) {
    super(props);
    this.state = {
      amount: Url.getParam('amount') || 0, // 当前还款额
      couponObject: { // 优惠券组件返回的数据
        availableCouponDetailList: [],
        appExclusiveCouponDetailList: [],
        unavailableCouponDetailList: []
      },
      selectedCoupon: null,
      showCouponSelector: false,
      settleWaiveAmt: '0.00', // 超限减免金额
      waiveTotalAmt: '0.00', // 优惠券减免金额
      courtCostAmt: '0.00', // 试算返回的法诉费金额
      repayTrialDetailList: [], // 试算返回的账单明细
      displaySettleWaiveAmt: null, // 超限减免金额
      displayLateFee: null, // 违约金减免（免提还资格券）
      displayAmount: null,
      showCounter: false, // 弹出收银台（仅用于控制订单页blur样式）

      waivePrepayFeeAmt: 0, // 提前还款手续费减免金额
      hasOverdueFlag: false, // 选中的账单是否有逾期
      isOverDueUserFlag: false, // 是否是逾期客户，只针对逾期3天以上的客户
      alldueOrOnTimeFlag: false, // 选中的账单全部是到期或逾期
      allCanPartPayFlag: false, // 选中的账单全部可以提前还款
      repayConfig: {}, // 客户端开发参数
      showInvalidityPage: false, // im场景是否展示失效页面
      isInitDone: false, // 初始化是否完成，以初始化时的试算结束做为完成标志
      showConsultOverdueModal: false,
      letEditable: false, // 可以输入金额
      showRestagingModel: false, // 再分期提示弹窗
      showRestagingModalSecond: 3, // 延后还弹窗自动倒计时秒数
      showExtendModel: false, // 延后还提示弹窗
      showExtendModalSecond: 3, // 延后还弹窗自动倒计时秒数
      overPayAmt: 0, // 溢缴款+线下转账已识别未入账
      overpayBalance: 0, // 溢缴款
      remitTotalAmount: 0, // 线下转账已识别未入账金额，属于溢缴款的一部分
      preRepayAmt: 0, // 预还款金额
      showOverpayDrawer: false, // 展示溢缴款说明
      guideCouponChoose: false, // 引导选券
      hitControl: false, // 击中C302管控
      showRepayDetailDrawer: false, // 展示还款明细
      showConsultRepayModel: false, // 协商还弹窗
      showConsultRepayModalSecond: 3, // 协商还
      showConsultErrorModel: false, // 协商还试算失败提示弹窗
      consultRepayErrorMsg: '', // 协商还试算失败提示弹窗文案
    };
    this.expressScene = Url.getParam('expressScene') || '';
    this.courtOnly = Url.getParam('courtOnly'); // 仅剩法诉费，无其他账单
    this.courtCostBalance = Url.getParam('courtCostBalance') || 0; /** 从账单页带入的初始法诉费金额 */
    this.isRepayAll = true; /** 是否部分还款，false即部分还款，部分提前还款不能用券 */
    this.billType = Url.getParam('billType') || '';
    this.billList = this.billType === 'total' ? getStore('allBills') : (this.billType === 'advanced-stage' ? {} : getStore('nearBills'));
    this.selectedBillList = getStore('selectedBillList');
    this.allBillList = []; // 全部可见可还借据
    this.immutableAmount = Url.getParam('amount') || 0;
    this.lastTrialTransTotalAmt = Url.getParam('amount') || 0;
    this.havePrepayFeeRightsCoupon = Url.getParam('havePrepayFeeRightsCoupon') || '0';
    this.selectedBillCapitals = {}; /** 账单涉及资方信息(0:100类型的资方)对象。{ "10000": 13123 } */
    this.isInitFinished = false; // 是否完成初始化，用于自动拉起收银台支付的判断
    this.directCallCounter = false;
    this.handleAmtChangeDebounce = debounce((val) => {
      this.handleAmtChange(Number(val).toFixed(2));
    }, 100, {
      leading: false, // 指定调用在节流开始前
      trailing: true // 指定调用在节流结束后
    });

    this.repayCheckOut = {
      show: () => { },
      hide: () => { },
      needToLoading: () => { },
    };
    this.needCallCounter = ''; // 从外部页面跳回来是否需要自动拉起收银台
    this.needExternalFillObj = false; // 用于小程序回来后退出页面时清除保存的数据
    /** 是否是还没获取银行卡 */
    this.firstgetBankCardList = true;
    this.randomReduceFlag = 'Y'; // 是否支持随机立减
    this.interestTotalAmt = '0'; // 随机立减总利息
    this.costTotalAmt = '0'; // 随机立减总期费用
    this.waivePayAmt = '0'; // 挂起减免总金额
    this.awardNo = Url.getParam('awardNo') || '';
    this.callbackUrl = Url.getParam('callbackUrl') || '';
    this.specialRepayScene = Url.getParam('specialRepayScene') || ''; // 特殊还款场景
    this.isStandardRepay = false; // 标识为正常还款场景
    this.repayBubbleParam = {};
    this.maxWaiveAmt = ''; // 协商还最大可减免息费
    this.totalThresholdAmt = ''; // 协商还结清金额
    this.extendRepayQualificationCoupon = {}; // 再分期资格券
    this.extendRepayFutureCoupon = {}; // 再分期未来期减免券
    this.hasPrepayFeeRightsCoupon = false, // 标识是否有免提还资格券
    this.prepayFeeRightsCoupon = {}; // 免提还资格券
    this.prepayFeeRightsObject = {}; // 免提还权益信息（用于传到收银台组件作为提交接口入参）
    this.isVplus = getStore('isVplus') || false;
    this.fromRestagingConfirm = Url.getParam('fromConfirm') || '';
    this.needIgnoreLeave = false; // 需要忽略挽留弹窗
    this.hasCompEdited = false; // 点击过收银台修改按钮
    /** 用户溢缴款 可用余额 */
    this.availableAmount = 0;
    // 0:100 借据隐藏支付宝
    this.closeAlipay0100BillFlag = false;
    this.originAvailCouponList = []; // 原始可用借据列表
    this.feeReduceMode = Url.getParam('feeReduceOldMode') || ''; // 息费减免模式，1为逾期
    this.firstSubmit = true; // 标识是否第一次触发交互式运营提交还款事件
    this.isSkipDetain = false; // 提前还款降价免息成功跳转首页时跳过挽留
    this.hasTriggerOpDetainDialog = false; // 标识还款交互运营退出挽留弹窗是否触发
    this.repaySence = ''; // 标识还款场景
    this.currentBillClear = Url.getParam('currentBillClear') || ''; // 当期账单结清标识
    this.loginInfo = {}; // 用户信息
  }

  async componentDidMount() {
    await this.fetchData();
    await this.getLoginInfo();
    this.initRepayData();
    this.waiveInit();
    // 处理部分ios机型返回页面不执行didshow的问题
    if (process.env.TARO_ENV === 'h5') {
      window.onpageshow = (event) => {
        if (
          event.persisted || (window.performance && (window.performance.navigation.type === 2 || window.performance.navigation.type === '2'))
        ) {
          if (isIOS()) {
            Madp.setNavigationBarTitle({ title: '还款' });
            if (!this.firstgetBankCardList) {
              this.repayCheckOut.hide();
              // 小程序绑卡回来需要延时一定时间才能自动拉起收银台
              setTimeout(() => {
                this.submitCheck();
              }, 500);
            }
            const wxNeedToLoading = Madp.getStorageSync('wxNeedToLoading', 'LOCAL');
            const zfbNeedToLoading = Madp.getStorageSync('zfbNeedToLoading', 'LOCAL');
            if (wxNeedToLoading === '1' || zfbNeedToLoading === '1') {
              Madp.setStorageSync('payNeedBreak', '1', 'LOCAL');
              this.repayCheckOut.needToLoading();
            }
          }
        }
      };
    }
    const sceneBeaconType = this.billType === 'advanced-stage' ? 'SCENE_SXF' : (this.billType === 'extend' ? 'BILL_EXTEND' : '');
    dispatchTrackEvent({
      event: EventTypes.BC,
      beaconId: 'EnterPage',
      target: this,
      beaconContent: { cus: { expressScene: this.expressScene, scene: sceneBeaconType, overdueDay: this.overdueDay, newScene: this.getNewScene, isStandardRepay: this.isStandardRepay } },
    });

    // 异步触发，保证不影响页面正常渲染，且能拿到最新的逾期天数和客户状态
    setTimeout(() => {
      this.initRepaySecene();
      this.handleOpPageStayEvent();
    }, 100);
  }

  // 判断标准还款：提前还、正常还、逾期还
  initRepaySecene = () => {
    const { overDueTagFlag, nearBillsTotalAmount } = getStore('nearBills');
    this.isStandardRepay = ['7days', 'total'].indexOf(this.billType) > -1 && this.expressScene !== '31' && this.specialRepayScene !== 'consult';
    if ((Number(this.overdueDay || 0) > 0 || overDueTagFlag) && Number(nearBillsTotalAmount || 0) > 0) {
      this.repaySence = 'overdueRepay';
    } else if (this.billType === 'total' && Number(this.overdueDay || 0) <= 0 && !overDueTagFlag) {
      this.repaySence = 'advanceRepay';
    } else {
      this.repaySence = 'normalRepay';
    }
  }

  // 交互运营-停留事件
  handleOpPageStayEvent = () => {
    this.stayTimer = null;
    if (!this.hasTriggerOpDetainDialog && this.isStandardRepay && typeof stayWithTime === 'function') {
      const eventCodeMap = {
        normalRepay: EVENT_CODE_MAP.expressRepayStay,
        advanceRepay: EVENT_CODE_MAP.expressRepayAdvanceStay,
        overdueRepay: EVENT_CODE_MAP.expressRepayOverdueStay,
      };
      this.stayTimer = stayWithTime(10000, async () => {
        if (this.stayTimer) {
          await this.opOnPageEvent('opPageStay', eventCodeMap[this.repaySence]);
        }
      });
    }
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '还款' });
    if (!isIOS()) {
      if (process.env.TARO_ENV !== 'h5') {
        this.needExternalFillObj = false;
      }
      // 人工客服挽留弹窗打开时不自动拉起收银台，否则两个弹窗会重叠
      if (!this.firstgetBankCardList && !this.hasTriggerOpDetainDialog) {
        this.repayCheckOut.hide();
        // 小程序绑卡回来需要延时一定时间才能自动拉起收银台
        setTimeout(() => {
          this.submitCheck();
        }, 500);
      }
      const wxNeedToLoading = Madp.getStorageSync('wxNeedToLoading', 'LOCAL');
      const zfbNeedToLoading = Madp.getStorageSync('zfbNeedToLoading', 'LOCAL');
      if (wxNeedToLoading === '1' || zfbNeedToLoading === '1') {
        Madp.setStorageSync('payNeedBreak', '1', 'LOCAL');
        this.repayCheckOut.needToLoading();
      }
    }
  }

  // 初始化用户信息
  getLoginInfo = async () => {
    const { custName = '', idNo = '', mobile = '' } = await getLoginInfo() || {};
    this.loginInfo = { custName, idNo, mobile };
  }

  componentWillUnmount() {
    const storageType = Madp.getChannel() === '3CMBAPP' ? 'LOCAL' : 'SESSION';
    Madp.removeStorageSync('expressRepayContracts', storageType);
    Madp.removeStorageSync('payNeedBreak', 'LOCAL');
    // 清空本次选中的银行卡
    setStore({
      selectedCard: {},
    });
    // 普通的关闭页面会清空
    if (!this.needExternalFillObj) {
      setStore({
        externalFillObj: {}
      });
    }
    // 在途停留事件取消
    if (this.stayTimer) {
      this.stayTimer = null;
    }
  }

  async fetchData() {
    await Promise.all([
      this.queryAdvanceBill(),
      this.initPageConfig(),
      this.overpayAccountInfo()
    ]);
    // 注意：im的场景已经接入了息费减免，如果后续快捷还款页要接入息费减免，限制条件加个||this.billType === 'fee-reduce'
    if (this.expressScene === '31') {
      await this.IMspecialInitBill();
    } else if (this.billType === 'advanced-stage') {
      await this.extendRepayCal(false);
    } else if (this.billType === 'extend') { // 延后还
      await this.extendRepayCalTrial();
    } else if (this.billType === 'preConsultRepay') { // 协商还
      await this.consultRepayCalTrial();
    } else {
      await this.initBill();
    }
    const { showInvalidityPage } = this.state;
    if (showInvalidityPage) {
      return null;
    }
    if (this.specialRepayScene === 'consult') {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'ConsultRepay',
      });
    }
  }

  async initBill() {
    // 判断前置页面是否携带待还账单信息，否则请求近期待还数据(与首页点击"去还款"等价)
    if (!this.immutableAmount || !this.billType || !this.selectedBillList || !this.selectedBillList.length) {
      const data = await Dispatch.repayment.getNearBills({ additionalQueryList: ['001'] }, { setNearAmount: true });
      const { repayBillList = [], nearBillsTotalAmount, courtCostBalance, isDueTagCust, negotiateRepayTaskBill } = data;
      this.billList = data;
      this.billType = '7days';
      this.selectedBillList = isDueTagCust === 'Y' ? repayBillList : repayBillList.filter((bill) => bill.usedToCalculate === 'Y');
      this.currentBillClear = repayBillList.filter((bill) => bill.belongUnicomContractOrder !== 'Y').length === this.selectedBillList.filter((bill) => bill.belongUnicomContractOrder !== 'Y').length ? 'Y' : '';
      this.courtCostBalance = Number(courtCostBalance) > 0 ? courtCostBalance : 0;
      this.courtOnly = Number(courtCostBalance) === Number(nearBillsTotalAmount) ? '1' : '0';
      if (negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}') {
        this.specialRepayScene = 'consult';
        // 协商还当期信息；当期应还金额，保留两位小数；当期是否最后一期，Y/N；下期还款日、还款金额；
        const {
          nextDueInstallNo, repayPlanList, totalShouldAmt
        } = negotiateRepayTaskBill;
        if (nextDueInstallNo && repayPlanList && repayPlanList.length > 0) {
          const { currentShouldAmt } = Object.assign(negotiateRepayTaskBill, { ...repayPlanList.filter((item) => Number(item.installNo) === Number(nextDueInstallNo))[0] });
          const taskShouldAmt = Number(currentShouldAmt || 0).toFixed(2);
          if (taskShouldAmt && Number(taskShouldAmt) > 0) {
            this.immutableAmount = Number(taskShouldAmt).toFixed(2);
          } else {
            this.immutableAmount = Number(totalShouldAmt).toFixed(2);
          }
        }
      } else {
        this.immutableAmount = Number(nearBillsTotalAmount).toFixed(2);
      }
      this.setState({
        // 当仅存在法诉费时，nearBillsTotalAmount与courtCostBalance相等，以它们的差值this.immutableAmount为结果页面展示金额为0.00，不对！
        amount: this.courtOnly === '1' ? Number(courtCostBalance).toFixed(2) : this.immutableAmount,
      });
    }
  }

  // 对im跳转过来的情况进行特殊的初始化处理
  async IMspecialInitBill() {
    let data = {};
    let billTotalRepayAmount = 0;
    if (this.billType === 'total') {
      data = await Dispatch.repayment.getAllBills();
      const { advanceBillList = [], advanceBillsTotalAmount } = data;
      this.selectedBillList = advanceBillList;
      billTotalRepayAmount = advanceBillsTotalAmount;
    } else if (this.billType === '7days' || this.billType === 'fee-reduce') {
      data = await Dispatch.repayment.getNearBills({ additionalQueryList: ['001'] }, { setNearAmount: true });
      const { repayBillList = [], isDueTagCust, nearBillsTotalAmount } = data;
      this.selectedBillList = isDueTagCust === 'Y' ? repayBillList : repayBillList.filter((bill) => bill.usedToCalculate === 'Y');
      billTotalRepayAmount = nearBillsTotalAmount;
    }
    this.billList = data;
    if (Number(billTotalRepayAmount) < this.immutableAmount) {
      // im场景传入金额大于借据可换金额，需要展示失效页
      this.setState({
        showInvalidityPage: true
      });
    }
    const { courtCostBalance } = data;
    this.courtCostBalance = Number(courtCostBalance) > 0 ? courtCostBalance : 0;
    this.setState({
      amount: this.immutableAmount
    });
  }

  initRepayData() {
    const selectedBills = this.selectedBillList;
    // 选中的账单是否逾期
    let hasOverdueFlag = false;
    // 选中的账单,逾期的数量
    let dueListLength = 0;
    // 选中的账单,逾期账单的数量。测试说要loanStatus判断就很神奇
    let dueLoanType = 0;
    /** 选中的账单,到期的数量 */
    let onTimeLength = 0;
    /** 选中的待还账单都可以修改金额 */
    let allCanPartPay = true;
    let waivePrepayFeeAmt = 0;
    // 超限减免金额累加值
    (selectedBills || []).forEach((bill) => {
      if (bill.surplusDays < 0) {
        hasOverdueFlag = true;
        dueListLength += 1;
      } else if (bill.surplusDays === 0 && bill.displayOverdueDays === 0) {
        onTimeLength += 1;
      }
      if (bill.canPartPayFlag !== 'Y') {
        allCanPartPay = false;
      }
      if (bill.loanStatus !== '01') {
        dueLoanType += 1;
      }
      // 累加超限减免金额
      if (bill.waiveChannelsPrepayFee && +bill.waiveChannelsPrepayFee > 0) {
        waivePrepayFeeAmt += +bill.waiveChannelsPrepayFee;
      }
    });
    this.selectedBillCapitals = getSelectedBillCapitalsAndList(this.selectedBillList);
    if (this.expressScene === '31' && dueLoanType === 0) {
      // im场景，用户未逾期，可能是误点进来的，要拦截
      this.setState({
        showInvalidityPage: true
      });
    }
    this.setState({
      hasOverdueFlag,
      alldueOrOnTimeFlag: (onTimeLength + dueListLength) === selectedBills.length,
      allCanPartPayFlag: allCanPartPay,
      isOverDueUserFlag: !!dueLoanType,
      waivePrepayFeeAmt: Number(waivePrepayFeeAmt).toFixed(2),
    });
  }

  // 查询全部可见可还借据
  queryAdvanceBill = async () => {
    const { advanceBillList = [] } = await Dispatch.repayment.getAllBills();
    this.allBillList = advanceBillList;
  }

  // 优惠信息初始化
  async waiveInit() {
    // 协商还办理前还款不查优惠券
    if (this.billType === 'preConsultRepay') {
      return;
    }
    // 若有免提还违约金资格券则优先使用，不再查询其他优惠券
    if (this.havePrepayFeeRightsCoupon === '1') {
      // 获取全部待还页缓存的免提还违约金资格券信息
      const prepayFeeRightsCouponList = getStore('prepayFeeRightsCouponList') || [];
      if (prepayFeeRightsCouponList && prepayFeeRightsCouponList.length > 0) {
        this.prepayFeeRightsCoupon = prepayFeeRightsCouponList[0];
        this.hasPrepayFeeRightsCoupon = true;
        dispatchTrackEvent({
          target: this,
          event: EventTypes.EV,
          beaconId: 'HavePrepayFeeRightsCoupon',
          beaconContent: { cus: { desc: 'FromStore' } }
        });
      } else {
        await this.getFeeRightsCouponList();
      }
      await this.afterWaiveInit();
    } else if (this.expressScene === '31' && !this.awardNo) {
      // im没传awardNo那就不查优惠卷
      await this.afterWaiveInit();
    } else if (this.billType === 'fee-reduce') {
      // 息费减免不查优惠券
      await this.afterWaiveInit();
    } else {
      if (this.specialRepayScene !== 'consult') {
        if (this.billType === 'advanced-stage') {
          await this.getTransCouponList();
        }
        // todo:建议后续优化为先回填数据再查卷;上线前才发现问题，做应急处理
        const {
          actualAmt, userSetAmt
        } = getStore('externalFillObj');
        if (actualAmt && Number(actualAmt) !== this.immutableAmount) {
          await this.getCouponList(userSetAmt || actualAmt, true, this.afterWaiveInit);
        } else {
          setTimeout(async () => {
            await this.getCouponList(null, true, this.afterWaiveInit);
          }, 0);
        }
      } else {
        await this.afterWaiveInit();
      }
    }
  }

  // 优惠信息确认后回调
  afterWaiveInit = async () => {
    this.externlFill();
    // 解决小程序多次从全部待还入口进入还款页面不展示微光卡
    if (Madp.getEnv() !== Madp.ENV_TYPE.WEB) {
      setTimeout(async () => {
        if (this.billType === 'advanced-stage') {
          await this.extendRepayCal(true, true);
        } else {
          const { amount } = this.state;
          await this.repayTransTrialApi(amount);
        }
        this.setState({
          isInitDone: true,
        });
      }, 500);
    } else {
      setTimeout(async () => {
        if (this.billType === 'advanced-stage') {
          await this.extendRepayCal(true, true);
        } else {
          const { amount } = this.state;
          await this.repayTransTrialApi(amount);
        }
        this.setState({
          isInitDone: true,
        });
      }, 100);
    }
  }

  /**
 * @description: 外部跳转回本页面，重新填充修改后的金额，重新选中优惠券
 */
  externlFill() {
    const {
      actualAmt, selectedCoupon, needCallCounter, userSetAmt,
    } = getStore('externalFillObj');
    // 回填微光卡数据
    if (selectedCoupon && selectedCoupon.awardNo) {
      this.setState({ selectedCoupon });
      const { chooseCoupon } = this.couponSelectorRef; // 该方法必须要注意时机
      setTimeout(() => {
        chooseCoupon(selectedCoupon.awardNo);
        this.onCouponSelected(selectedCoupon);
      }, 1000);
    } else if (actualAmt && (!selectedCoupon || Object.keys(selectedCoupon).length === 0)) {
      const { cleanCheck } = this.couponSelectorRef;
      cleanCheck && cleanCheck();
      this.setState({ selectedCoupon: null });
    }
    if (actualAmt && Number(actualAmt) !== this.immutableAmount) {
      this.setState({ amount: userSetAmt || actualAmt });
    }
    this.needCallCounter = needCallCounter || '';
    // 用完就删
    setStore({
      externalFillObj: {}
    });
  }

  async initPageConfig() {
    const repayConfig = await Dispatch.repayment.getMultipleCommonConfig(['repayPayment.config', 'repayMarketing.config']) || {};
    this.setState({
      repayConfig
    });
  }

  async overpayAccountInfo() {
    const { overpayBalance, remitTotalAmount, preRepayAmt, overpayControlDetailList } = await Dispatch.repayment.queryOverpayAccountInfo();
    const overpayControlFlag = (overpayControlDetailList || []).filter((item = {}) => item.controlCode === 'C601').length ? 'Y' : 'N';
    this.setState({
      overPayAmt: overpayControlFlag === 'Y' ? '0.00' : (parseFloat(overpayBalance || '0') + parseFloat(remitTotalAmount || '0')).toFixed(2),
      overpayBalance: overpayControlFlag === 'Y' ? '0.00' : parseFloat(overpayBalance || '0').toFixed(2),
      remitTotalAmount: overpayControlFlag === 'Y' ? '0.00' : parseFloat(remitTotalAmount || '0').toFixed(2),
      preRepayAmt: overpayControlFlag === 'Y' ? '0.00' : parseFloat(preRepayAmt || '0').toFixed(2),
    });
  }

  /**
 * 判断是否自动调起还款收银台，需满足条件如下：
 * 1. 无优惠券
 * 2. 不满足微光卡开卡
 * 3. 无超限减免
 * 4. 无法诉费
 * 注意：im场景不需要满足上诉条件，需要直接调起收银台
 */
  judgeCallCounter() {
    let shouldCallCounter = true;
    const {
      couponObject, settleWaiveAmt, courtCostAmt, preRepayAmt
    } = this.state;
    const { availableCouponDetailList = [], appExclusiveCouponDetailList = [], unavailableCouponDetailList = [] } = couponObject;
    // 优惠券判断
    if ([...availableCouponDetailList, ...appExclusiveCouponDetailList, ...unavailableCouponDetailList].length > 0) {
      shouldCallCounter = false;
    }
    if (this.hasPrepayFeeRightsCoupon) {
      shouldCallCounter = false;
    }
    // 超限减免判断
    if (Number(settleWaiveAmt) > 0) {
      shouldCallCounter = false;
    }
    // 法诉费判断
    if (Number(courtCostAmt) > 0 && this.specialRepayScene !== 'consult') {
      shouldCallCounter = false;
    }
    // 小招荷包判断
    if (this.getOverPayAmtRepayFlag() || Number(preRepayAmt || 0) > 0) {
      shouldCallCounter = false;
      dispatchTrackEvent({
        event: EventTypes.SO,
        beaconId: 'ShowOverRepayRow',
        target: this,
        beaconContent: { cus: { expressScene: this.expressScene, newScene: this.getNewScene, overdueDay: this.overdueDay } },
      });
    }
    // 协商还有减免
    if (this.specialRepayScene === 'consult' && this.maxWaiveAmt && Number(this.maxWaiveAmt) > 0) {
      shouldCallCounter = false;
    }
    // 从其他页面返回的情况也需要自动拉起收银台
    if (this.needCallCounter === '1') {
      shouldCallCounter = true;
    }
    // 注意：im的场景已经接入了息费减免，如果后续快捷还款页要接入息费减免，限制条件加个||this.billType === 'fee-reduce'
    if (this.expressScene === '31') {
      shouldCallCounter = true;
    }
    if (this.billType === 'total') {
      shouldCallCounter = false;
    }
    if (shouldCallCounter) {
      this.directCallCounter = true;
      this.submitCheck();
      dispatchTrackEvent({
        event: EventTypes.EV,
        beaconId: 'AutoSubmitRepay',
        target: this,
        beaconContent: { cus: { expressScene: this.expressScene, newScene: this.getNewScene } },
      });
    }
    this.setState({ letEditable: true }, () => {
      if (this.canEditable) {
        dispatchTrackEvent({
          event: EventTypes.SO,
          beaconId: 'CanEditable',
          target: this,
          beaconContent: { cus: { expressScene: this.expressScene, newScene: this.getNewScene } },
        });
      }
    });
    dispatchTrackEvent({
      event: EventTypes.BC,
      beaconId: shouldCallCounter ? 'DirectSubmit' : 'NoDirectSubmit',
      target: this,
      beaconContent: { cus: { expressScene: this.expressScene, scene: this.billType === 'advanced-stage' ? 'SCENE_SXF' : '' } },
    });
  }

  /**
 * @description: 中途离开页面前，暂存修改金额、优惠券、微光卡状态
 */
  saveExternalFillData() {
    const {
      amount, selectedCoupon, displayAmount,
    } = this.state;
    setStore({
      externalFillObj: {
        actualAmt: amount,
        selectedCoupon,
        userSetAmt: displayAmount || amount,
        needCallCounter: '1',
      }
    });
    this.needExternalFillObj = true;
  }

  /** 还款挽留：不想还款时
 *  当本页发生路由变化时，做不到页面关闭时激活挽留
 */
  async beforeRouteLeave(from, to, next) {
    // 近期待还跳转支付页，支付页点击返回回到首页后，跳转外部模块再次返回到首页会执行window.onpageshow回调，导致调用submitCheck后出现异常，所以这里跳出时清除
    window.onpageshow = () => { };

    // // 2、还款loading弹窗打开状态禁止点击左上角返回
    const { showLoadingDialog, finishLoading } = this.repayCheckOut || {};
    if (showLoadingDialog && !finishLoading) {
      // 增加标识，结果页出现双页面的问题
      Madp.setStorageSync('needRefresh', 'Y', 'SESSION');
      next(false);
      return null;
    }

    const { showInvalidityPage } = this.state;

    if (showInvalidityPage || this.hasTriggerOpDetainDialog || (this.repaySence === 'advanceRepay') || this.isSkipDetain) {
      next(true);
      return null;
    }
    const fns = {
      setStore,
      themeColor
    };
    let triggerType = '';
    if (this.repaySence === 'advanceRepay' && this.billType === 'total' && ['/pages/index/index', '/index'].includes(to.path)) {
      triggerType = 'expressRepayAdvanceExit';
    } else if (this.repaySence === 'normalRepay' && ['/pages/index/index', '/index', '/pages/bill-list-near/index', '/pages/bill-list-all/index'].includes(to.path)) {
      triggerType = 'expressRepayExit';
    } else if (this.repaySence === 'overdueRepay' && ['/pages/index/index', '/index', '/pages/bill-list-near/index', '/pages/bill-list-all/index'].includes(to.path)) {
      triggerType = 'expressRepayOverdueExit';
    }
    if (this.isStandardRepay && triggerType) {
      this.hasTriggerOpDetainDialog = true;
      const opRes = await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP[triggerType]);
      if (opRes) { // 击中运营点：关闭其他半屏弹窗，避免重叠
        next(false);
        this.repayCheckOut.hide();
        this.setState({
          showCounter: false,
          showRepayDetailDrawer: false,
        });
        // 增加标识，解决弹窗确认后继续还款，跳转结果页出现双页面的问题
        Madp.setStorageSync('needRefresh', 'Y', 'SESSION');
      } else { // 未击中运营点：触发普通挽留弹窗
        if (repayIndexFn.beforeRouteLeaveHandler) {
          repayIndexFn.beforeRouteLeaveHandler(from, to, next, fns, this);
        } else {
          Madp.navigateBack();
          // 这里来清空绑卡信息 离开也需要清一次
          Madp.removeStorageSync('BANK_CARD_INFO_DATA', 'SESSION');
        }
      }
    } else if (repayIndexFn.beforeRouteLeaveHandler) {
      if (this.billType === 'preConsultRepay') { // 协商还试算失败，返回首页,不触发挽留弹窗
        next(true);
        Madp.removeStorageSync('BANK_CARD_INFO_DATA', 'SESSION');
      } else {
        repayIndexFn.beforeRouteLeaveHandler(from, to, next, fns, this);
      }
    } else {
      next(true);
      // 这里来清空绑卡信息 离开也需要清一次
      Madp.removeStorageSync('BANK_CARD_INFO_DATA', 'SESSION');
    }
  }

  // 微信自定义导航栏挽留弹框
  beforeMiniRouteLeave = async () => {
    // 在途停留事件取消
    if (this.stayTimer) {
      this.stayTimer = null;
    }
    if (this.hasTriggerOpDetainDialog) { // 交互运营挽留弹窗触发中，再次点击返回直接退出
      Madp.navigateBack();
      return;
    }
    let triggerType = '';
    if (this.repaySence === 'advanceRepay') {
      triggerType = 'expressRepayAdvanceExit';
    } else if (this.repaySence === 'normalRepay') {
      triggerType = 'expressRepayExit';
    } else if (this.repaySence === 'overdueRepay') {
      triggerType = 'expressRepayOverdueExit';
    }
    if (this.isStandardRepay && triggerType) {
      this.hasTriggerOpDetainDialog = true;
      const opRes = await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP[triggerType]);
      if (opRes) { // 击中运营点：关闭其他半屏弹窗，避免重叠
        this.repayCheckOut.hide();
        this.setState({
          showCounter: false,
          showRepayDetailDrawer: false,
        });
      } else { // 未击中运营点：触发普通挽留弹窗
        this.showDetainModal();
      }
    } else if (this.billType !== 'total') {
      // 提前还款不做挽留
      this.showDetainModal();
    } else {
      Madp.navigateBack();
    }
  }

  /* 获取逾期挽留弹窗所需参数，含以下3种类型：
    * 客户提前还款（走全部待还this.billType === 'total'）
        1、overDueFormBillListAll: 未打标逾期且有逾期天数（打标用户无全部待还入口）
    * 非全部待还-提前还款流程（不走全部待还）
        2、overDueTagAndNoOverdueDay: 逾期打标C401+ (GKSJ032 || GKSJ033) + 逾期天数为0
        3、overDueNotFormBillListAll: 逾期且有逾期天数
  */
  opGetOverdueRetainDialogData = () => {
    const { displayAmount, amount } = this.state;
    const { overDueTagFlag, nearBillsTotalAmount } = getStore('nearBills'); // 同还款首页待还金额（可见可还逾期+到期借据总金额）
    let overDueDialogType = '';
    let btnAmount = '';

    if (this.billType === 'total') {
      overDueDialogType = 'overDueFormBillListAll';
      // 从全部待还页进入展示的金额（逾期&到期金额 < 进入提前还款时的账单金额（displayAmount） ？ 逾期待还金额 : 继续还款）
      btnAmount = Number(nearBillsTotalAmount) < Number(displayAmount || amount) ? Number(nearBillsTotalAmount) : '';
    } else if (overDueTagFlag && Number(this.overdueDay) === 0) {
      overDueDialogType = 'overDueTagAndNoOverdueDay';
    } else {
      overDueDialogType = 'overDueNotFormBillListAll';
    }

    // 待还金额（逾期&到期金额&含法诉费）
    return {
      overDueDialogType,
      repaySence: this.billType,
      overdueDay: this.overdueDay,
      btnAmount,
      remainingAmount: nearBillsTotalAmount,
      overdueRetainDialogConfirmCallback: (callbackAmount) => {
        // 逾期客服弹窗点击确认后的逻辑
        if (Number(callbackAmount || 0) > 0) {
          // 修改金额后重新进行试算
          this.setState({ amount: Number(callbackAmount).toFixed(2), displayAmount: Number(callbackAmount).toFixed(2) }, () => {
            this.handleAmtChangeDebounce(Number(callbackAmount));
          });
        } else {
          this.submitCheck();
        }
      }
    };
  }

  opAdvanceRepayReductionDialogData = () => {
    const { repayConfig: { quotaType, productCode } = {} } = this.state;
    const { custName = '', idNo = '', mobile = '' } = this.loginInfo || {};
    return {
      selectedBillList: this.selectedBillList,
      repayConfig: { quotaType, productCode },
      sessionInfo: { maskRealName: custName, maskMobile: mobile, maskIdNo: idNo },
      updateIsSkipDetain: () => {
        this.isSkipDetain = true;
      },
    };
  }

  // 交互式运营-圣约检查
  async opOnPageEvent(eventName, interactionEventCode) {
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            pageId: expressRepayPageId,
            interactionEventCode,
            opGetOverdueRetainDialogData: this.opGetOverdueRetainDialogData,
            opAdvanceRepayReductionDialogData: this.opAdvanceRepayReductionDialogData,
          },
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve(true);
      }
    });
  }

  // 微信小程序普通挽留弹窗
  showDetainModal = () => {
    Madp.showModal({
      content: '请确认是否放弃还款？',
      confirmText: '继续还款',
      cancelText: '确认',
      confirmColor: themeColor,
      success(res) {
        if (res.cancel) {
          Madp.navigateBack();
        }
      }
    });
  }

  submit = () => {
    const {
      selectedCoupon, waiveTotalAmt: totalWaiveAmt,
      settleWaiveAmt, repayConfig,
      virtualFuncSwitch, courtCostAmt,
      couponObject, repayTrialDetailList, isOverDueUserFlag,
      displayLateFee, overPayAmt, remitTotalAmount, preRepayAmt
    } = this.state;
    const {
      extendRepayQualificationCoupon, extendRepayFutureCoupon,
      hasPrepayFeeRightsCoupon, prepayFeeRightsObject, availableAmount, closeAlipay0100BillFlag
    } = this;
    const repayCheckOutParam = {
      repayConfig, // 客户端参数
      selectedCoupon, // 用户选择的优惠券
      totalWaiveAmt, // 优惠券金额
      transTotalAmt: this.transTotalAmt, // 还款总金额，这个里面包含法诉费
      totalCanPayAmt: this.getActualRepayAmt, // 减掉优惠券和超限的金额
      actualRepayAmt: this.actualRepayAmt, // 客户需支付的金额
      settleWaiveAmt, // 超限金额
      repayDetailList: this.selectedBillList, // 借据信息
      repayMode: this.repayMode,
      is0100Bills: is0100Bills(repayTrialDetailList), // 0:100借据属性
      virtualFuncSwitch, // 虚拟展位参数
      couponObject, // 优惠券接口返回参数
      repayTrialDetailList, // 试算返回的借据信息
      editable: this.editable,
      expressScene: this.expressScene,
      directCallCounter: this.directCallCounter, // 是否直接调起收银台
      selectedBillCapitals: this.selectedBillCapitals,
      isOverDueUserFlag: isOverDueUserFlag, // 是否是逾期客户，只针对逾期3天以上的客户
      isDueTagCust: this.isDueTagCust, // 是否为打标用户
      callbackUrl: this.callbackUrl,
      courtCostAmt, // 法诉费
      specialRepayScene: this.specialRepayScene, // 协商还
      extendRepayQualificationCoupon, // 再分期资格券
      extendRepayFutureCoupon, // 再分期未来期减免券
      prepayFeeRightsObject: hasPrepayFeeRightsCoupon && displayLateFee > 0 ? prepayFeeRightsObject : {}, // 免提还权益信息（提交还款时减免金额大于0才传）
      overPayAmt, // 溢价款+在途转账
      remitTotalAmount, // 在途转账
      preRepayAmt, // 预还款金额
      availableAmount, // 溢价款+在途转账可用金额，未叠加预还款金额修正
      closeAlipay0100BillFlag, // 0:100借据隐藏支付宝
      overPayAmtRepayFlag: this.getOverPayAmtRepayFlag(), // 使用溢缴款+在途转账资金还款标识
      xiaozhaoAmount: this.xiaozhaoAmount, // 溢价款+在途转账可用金额，叠加预还款金额修正
      preRepayUsedAmount: this.preRepayUsedAmount, // 预还款使用金额
      newScene: this.getNewScene, // 埋点场景newScene
      repaySence: this.repaySence, // 还款场景
    };
    // 超限叠加法诉费的场景下，若法诉费未结清，总待还金额需要减去法诉费，否则提交还款中台试算会与前端的试算不一致（原因是该场景下试算入参从逻辑上从应还金额变为实还金额）
    const { courtCostBalance } = this.billList;
    if (Number(settleWaiveAmt) > 0 && Number(courtCostBalance) > 0 && Number(courtCostAmt) < Number(courtCostBalance) && Number(courtCostAmt) > 0) {
      repayCheckOutParam.transTotalAmt = this.lastTrialTransTotalAmt;
    }
    this.setState({ showCounter: true });
    this.repayCheckOut.show(repayCheckOutParam);
    this.directCallCounter = false;
    this.firstgetBankCardList = false;
  }

  /**
   * 点击立即还款
   * 1、先判断是否满足交互式运营事件触发条件：已提交预审、标准还款提前还款场景、第一次触发、不存在在途交易
   * 2、满足时触发交互式运营事件逻辑，不满足时直接拉起收银台
   */
  submitRepay = async () => {
    try {
      const pendingRepayTransInfo = getStore('pendingRepayTransInfo') || {};
      if (this.firstSubmit && this.isStandardRepay && JSON.stringify(pendingRepayTransInfo) === '{}') {
        this.firstSubmit = false;
        const eventCodeMap = {
          normalRepay: EVENT_CODE_MAP.expressRepayClick,
          advanceRepay: EVENT_CODE_MAP.expressRepayAdvanceClick,
          overdueRepay: EVENT_CODE_MAP.expressRepayOverdueClick,
        };
        await this.opOnPageEvent('opRepayClick', eventCodeMap[this.repaySence]);
        this.submitCheck();
      } else {
        this.submitCheck();
      }
    } catch (error) {
      this.submitCheck();
    }
  }

  submitCheck() {
    const { repayTrialDetailList } = this.state;
    if (is0100Bills(repayTrialDetailList) === 'minxBill') {
      const billCount = Object.keys(this.selectedBillCapitals).length;
      Madp.showModal({
        title: '温馨提示',
        content: `受支付机构影响，本次还款将拆成${billCount}笔扣款，请稍后留意还款结果通知`,
        confirmText: '我知道了',
        confirmColor: themeColor,
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            this.submit();
          }
        }
      });
    } else {
      this.submit();
    }
  }


  async handleAmtChange(val, guideChoose) {
    if (val === this.immutableAmount) {
      this.isRepayAll = true;
    }
    if (this.specialRepayScene !== 'consult') {
      await this.getCouponList(val, guideChoose);
    }
    setTimeout(() => {
      // 这里是为了处理一个仅微信会出现的问题,改完金额之后查卷不可用但试算返回可用的情况
      if (process.env.TARO_ENV === 'weapp') {
        // 先提前看看卷还能不能用
        const { selectedCoupon, couponObject } = this.state;
        const { availableCouponDetailList } = couponObject;
        if (selectedCoupon
          && (availableCouponDetailList && availableCouponDetailList
            .findIndex((i) => i.awardNo === selectedCoupon.awardNo) === -1)) {
          // 已选券未在可用券列表中，清空已选券
          this.clearCoupon();
        }
        setTimeout(() => {
          this.repayTransTrialApi(val);
        }, 200);
      } else {
        this.repayTransTrialApi(val);
      }
    }, 100);
  }

  // 校验改完的金额是否合规
  checkAmountChange(val) {
    const {
      waivePrepayFeeAmt,
    } = this.state;
    if (val === '' || Number(val) === 0) {
      // 支付宝小程序重设金额值，避免一直显示输入值
      this.setState({
        amount: this.immutableAmount
      });
      this.handleAmtChange(this.immutableAmount);
      return false;
    }
    if (+val > +this.immutableAmount) {
      if (this.specialRepayScene === 'consult') {
        if (+val > Number(this.totalThresholdAmt)) {
          if (Number(this.maxWaiveAmt) > 0) {
            // 弹窗
            this.setState({ showConsultOverdueModal: true }, () => {
              this.repayBubbleParam = {};
            });
          } else {
            Util.toast('无法超额还款！');
            this.setState({
              amount: this.totalThresholdAmt,
              displayAmount: this.totalThresholdAmt
            });
            this.handleAmtChange(this.totalThresholdAmt);
          }
          return false;
        }
      } else {
        Util.toast('无法超额还款！');
        this.resetAmount();
        return false;
      }
    }
    // 非指定金额还款时，限制还款金额大于 10 元
    if (+val < 10 && this.isDueTagCust !== 'Y') {
      Util.toast('亲，还款金额不能小于 10 元哦');
      dispatchTrackEvent({
        target: this, event: EventTypes.PO, beaconId: 'RepayAmtCannotLow10Tip', beaconContent: { cus: val }
      });
      this.resetAmount();
      return false;
    }
    if (this.courtOnly === '1') return true; // 仅还法诉费时，不校验账单
    if (+val < waivePrepayFeeAmt + +this.selectedBillList[0].surplusPayOnetimeFeeAmt) {
      const minimumAmt = +waivePrepayFeeAmt + +this.selectedBillList[0].surplusPayOnetimeFeeAmt;
      Util.toast(`亲，还款金额不能小于${minimumAmt.toFixed(2)}元哦~`);
      this.resetAmount();
      return false;
    }
    return true;
  }

  resetAmount() {
    this.setState({
      amount: this.immutableAmount
    });
    // 为了小程序样式优化不得不将延时时间从1s改到3s
    // 防止toast被接口加载图标遮挡，导致接口调用结束后toast重新出现，就视觉上来说toast就像出现了两次
    const delayTime = process.env.TARO_ENV !== 'h5' ? 3000 : 1000;
    setTimeout(() => {
      this.handleAmtChange(this.immutableAmount);
    }, delayTime);
  }

  async repayTransTrialApi(amtInput) {
    // 仅剩法诉费未还, 无账单，无法提交试算
    if (this.courtOnly === '1') {
      return this.setState({
        displayAmount: null,
        editing: false,
        amount: amtInput,
        courtCostAmt: amtInput,
      }, () => {
        if (!this.isInitFinished) {
          if (this.billType !== 'extend') {
            this.judgeCallCounter();
          }
          this.isInitFinished = true;
        }
      });
    }
    if (this.expressScene !== '31' && this.billType === 'fee-reduce') {
      const repayTrialDetailList = getStore('repayTrialDetailList') || []; // 息费减免试算后的账单明细
      return this.setState({
        editing: false,
        amount: amtInput,
        repayTrialDetailList,
      }, () => {
        if (!this.isInitFinished) {
          this.judgeCallCounter();
          this.isInitFinished = true;
        }
      });
    }
    const { selectedCoupon } = this.state;
    const trialAmt = selectedCoupon && selectedCoupon.awardAmtType === '4' ? this.immutableAmount : amtInput;
    const transTotalAmt = this.specialRepayScene === 'consult' ? trialAmt : this.getTrialTransTotalAmt(trialAmt);

    // 免提还权益信息
    const prepayFeeRightsObject = {
      prepayFeeRightsFlag: this.hasPrepayFeeRightsCoupon ? 'Y' : 'N',
      prepayFeeRightDetail: this.hasPrepayFeeRightsCoupon ? {
        ...getFeeInteRightInfo(this.prepayFeeRightsCoupon || {})
      } : null
    };
    this.prepayFeeRightsObject = prepayFeeRightsObject;
    // 可以修改金额大，还款类型应该是AMT吧？
    const params = {
      repayMode: this.repayMode,
      transTotalAmt,
      repayDetailList: prepareRepayDetailList(this.selectedBillList, this.billType),
      feeInteRightList: this.specialRepayScene === 'consult' ? []
        : (selectedCoupon && selectedCoupon.awardNo
          ? [{ ...getFeeInteRightInfo(selectedCoupon) }] : []),
      ...prepayFeeRightsObject
    };
    const {
      data: repayTransTrialResult, errCode, errMsg
    } = await Dispatch.repayment.repayTransTrial(params, true);
    const {
      result,
      settleWaiveAmt,
      totalPayPrepayFee, // 提前还款手续费
      totalCanWaiveAmt: waiveTotalAmt,
      shouldRepayAmt,
      courtCostAmt,
      repayTrialDetailList,
      negotiateRepayTrialDetail,
      trialWaiveResultList,
      insufficientRemainAmt, // 入账后剩余金额
    } = repayTransTrialResult || {};
    // 协商还还款
    if (this.expressScene !== '31' && this.billType === '7days' && negotiateRepayTrialDetail && JSON.stringify(negotiateRepayTrialDetail) !== '{}') {
      const {
        maxWaiveAmt, totalThresholdAmt, trailResult, taskDiffAmt, taskThresholdAmt, realPaidAmt, taskAmt, expectWaiveAmt, settleWaiveAmt: consultSettleWaiveAmt, surplusTotalAmt, settleFlag
      } = negotiateRepayTrialDetail;
      this.maxWaiveAmt = maxWaiveAmt;
      this.totalThresholdAmt = totalThresholdAmt;
      // 有息费减免
      if (maxWaiveAmt && Number(maxWaiveAmt) > 0) {
        if (trailResult === '0') {
          this.repayBubbleParam = {
            type: 'fillTop',
            contentArr: [
              { contentText: `再还${Number(taskDiffAmt).toFixed(2)}元`, contentColor: 'red' },
              { contentText: `，享${totalThresholdAmt === taskThresholdAmt ? Number(taskThresholdAmt || 0).toFixed(2) : Util.floatMinus(taskThresholdAmt, realPaidAmt).toFixed(2)}元抵还${totalThresholdAmt === taskThresholdAmt ? Number(taskAmt || 0).toFixed(2) : Util.floatMinus(taskAmt, realPaidAmt).toFixed(2)}元`, contentColor: '' },
            ],
          };
        } else {
          this.repayBubbleParam = {
            type: 'fillTop',
            contentArr: [
              { contentText: '已减', contentColor: '' },
              { contentText: `${(settleFlag === 'Y' ? Util.floatMinus(surplusTotalAmt, transTotalAmt) : Number(expectWaiveAmt)).toFixed(2)}元`, contentColor: 'red' },
              { contentText: `息费，仅需${Number(transTotalAmt).toFixed(2)}抵还${(settleFlag === 'Y' ? Number(surplusTotalAmt) : Util.floatAdd(transTotalAmt, expectWaiveAmt)).toFixed(2)}元`, contentColor: '' },
            ],
          };
        }
      } else {
        if (trailResult === '0') {
          this.repayBubbleParam = {
            type: 'fillTop',
            contentArr: [
              { contentText: `再还${Number(taskDiffAmt).toFixed(2)}元`, contentColor: 'red' },
              { contentText: '，可达到本月协商还约定金额', contentColor: '' },
            ],
          };
        } else {
          this.repayBubbleParam = {};
        }
      }
      // 试算失败
      if (result !== 'SUC') {
        if (errCode === 'UMDP02897') {
          this.setState({ hitControl: true });
          setTimeout(() => {
            Util.toast(errMsg || '您当前账户状态异常，无法完成还款，如有疑问请联系在线客服！');
          }, 100);
        } else {
          setTimeout(() => {
            Util.toast(errMsg || '系统繁忙，请退出后重新进入');
          }, 100);
        }
        return;
      }

      this.setState({
        amount: Number(trialAmt).toFixed(2),
        displayAmount: Number(trialAmt).toFixed(2),
        settleWaiveAmt: consultSettleWaiveAmt,
        displaySettleWaiveAmt: consultSettleWaiveAmt,
        repayTrialDetailList: repayTrialDetailList || [],
        editing: false,
      }, () => {
        if (!this.isInitFinished || this.hasCompEdited) {
          if (this.billType !== 'extend') {
            this.judgeCallCounter();
          }
          this.isInitFinished = true;
        }
      });
    } else {
      if (this.expressScene === '31' && result === 'FAIL') {
        // im场景试算失败，展示失效页
        this.setState({
          showInvalidityPage: true
        });
        return;
      }
      // 已选券但试算优惠为0或试算失败，清空
      if (selectedCoupon && selectedCoupon.awardNo && (!waiveTotalAmt || Number(waiveTotalAmt) === 0)) {
        if (this.billType === 'fee-reduce') {
          // 博弈的场景试算返回0优惠也不清空卷
        } else {
          setTimeout(() => {
            Util.toast('优惠券无法使用');
          }, 100);
          dispatchTrackEvent({
            target: this,
            event: EventTypes.EV,
            beaconId: 'CouponTrialFaill',
            beaconContent: { cus: repayTransTrialResult }
          });
          return this.clearCoupon(true, amtInput);
        }
      } else if (result !== 'SUC') {
        if (errCode === 'UMDP02897') {
          this.setState({ hitControl: true });
          setTimeout(() => {
            Util.toast(errMsg || '您当前账户状态异常，无法完成还款，如有疑问请联系在线客服！');
          }, 100);
        } else {
          Madp.showModal({
            title: '温馨提示',
            content: errMsg || '系统繁忙，请退出后重新进入',
            confirmText: '返回首页',
            confirmColor: themeColor,
            showCancel: false,
            success: (res) => {
              if (res.confirm) {
                this.isSkipDetain = true;
                const url = '%2Fpages%2Findex%2Findex';
                Madp.redirectTo({
                  url: decodeURIComponent(url),
                });
              }
            }
          });
        }
        return;
      }
      // 提前还款 && 部分还款 修改金额后禁止使用提前结清的优惠券,216还是可以用的
      if (this.isRepayAll && this.isDueTagCust !== 'Y' && settleWaiveAmt && Number(settleWaiveAmt) === 0 && amtInput !== this.immutableAmount && this.billType === 'total') {
        this.isRepayAll = false;
        // 重新过滤下卷
        return this.handleAmtChange(amtInput);
      }
      const { courtCostBalance } = this.billList;
      // 当输入金额小于应还金额，无论超限减免金额是否大于0，都用应还金额shouldRepayAmt重置输入金额
      if (Number(trialAmt) <= Number(shouldRepayAmt) && Number(settleWaiveAmt) > 0) {
        this.setState({
          displaySettleWaiveAmt: Number(settleWaiveAmt).toFixed(2),
          displayAmount: null,
        });
      } else if (Number(courtCostBalance) > 0 && Number(settleWaiveAmt) > 0 && Number(courtCostBalance) >= Number(courtCostAmt)) {
        if (Number(courtCostBalance) === Number(courtCostAmt)) {
          this.setState({ displaySettleWaiveAmt: null, displayAmount: Number(trialAmt).toFixed(2) });
        } else {
          this.setState({ displayAmount: Number(trialAmt).toFixed(2) });
        }
      } else {
        this.setState({ displaySettleWaiveAmt: null, displayAmount: null });
      }
      if (repayTrialDetailList && repayTrialDetailList.length > 0) {
        // 每次试算结果叠加前应重置为0
        this.interestTotalAmt = '0'; // 随机立减总利息
        this.costTotalAmt = '0'; // 随机立减总期费用
        repayTrialDetailList.forEach((repayTrialItem) => {
          if (repayTrialItem) {
            if (repayTrialItem.totalPayInteAmt && Number(repayTrialItem.totalPayInteAmt) > 0) {
              this.interestTotalAmt = Util.floatAdd(Number(this.interestTotalAmt), Number(repayTrialItem.totalPayInteAmt));
            }
            if (repayTrialItem.totalPayPeriodFee && Number(repayTrialItem.totalPayPeriodFee) > 0) {
              this.costTotalAmt = Util.floatAdd(Number(this.costTotalAmt), Number(repayTrialItem.totalPayPeriodFee));
            }
          }
        });
      }
      // 根据实还金额重算溢缴款的使用额度
      this.lastTrialTransTotalAmt = transTotalAmt;

      this.setState({
        amount: shouldRepayAmt,
        displayLateFee: (Number((trialWaiveResultList && trialWaiveResultList.filter((item) => item.waiveScene === '04')[0] || {}).actualWaiveAmt) || 0).toFixed(2), // 可减免的违约金
        settleWaiveAmt,
        waiveTotalAmt,
        insufficientRemainAmt,
        repayTrialDetailList: repayTrialDetailList || [],
        courtCostAmt,
        totalPayPrepayFee,
        editing: false,
      }, () => {
        const { showInvalidityPage } = this.state;
        if ((!this.isInitFinished && !showInvalidityPage) || this.hasCompEdited) {
          if (this.billType !== 'extend') {
            this.judgeCallCounter();
          }
          this.isInitFinished = true;
        }
      });
    }
  }

  // 再分期试算
  async extendRepayCal(setWaive = true, needTip = false) {
    const { extendPackageInfo, firstPhasePeriods, extendInstallTotalCnt, expectRepayAmtPercent } = getStore('advancedStageInfo');
    const { selectedCoupon } = this.state;
    const param = {
      orderNoList: this.selectedBillList,
      extendPackageList: [extendPackageInfo],
      calMode: setWaive ? '01' : '',
      expireAwardNo: setWaive ? (selectedCoupon && selectedCoupon.awardNo) : '',
      futureAwardNo: setWaive ? (this.extendRepayFutureCoupon && this.extendRepayFutureCoupon.awardNo) : '',
      firstPhasePeriods,
      extendInstallTotalCnt,
      expectRepayAmtPercent,
    };
    const { data } = await Dispatch.repayment.queryReinstallRepayInfo(param) || {};
    const { extendCalInfoList } = data || {};
    if ((extendCalInfoList || []).length > 0) {
      const { duePayTotalAmt, dueTotalInteFeeWaiveAmt } = extendCalInfoList[0];
      this.immutableAmount = Number(duePayTotalAmt || 0).toFixed(2);
      this.setState({
        amount: duePayTotalAmt,
        waiveTotalAmt: setWaive ? dueTotalInteFeeWaiveAmt : ''
      }, () => {
        if (needTip) {
          // 从再分期还款确认页过来的不再弹窗
          if (this.fromRestagingConfirm === '1') return;
          const { showRestagingModalSecond } = this.state;
          this.countDownToCloseRestagingModal(showRestagingModalSecond);
          this.setState({ showRestagingModel: true });
        }
      });
    }
  }

  // 倒计时三秒后关闭弹窗
  countDownToCloseRestagingModal = (seconds) => {
    this.timer = setInterval(() => {
      seconds -= 1;
      this.setState({
        showRestagingModalSecond: seconds
      });
      if (seconds <= 0) {
        clearInterval(this.timer);
        this.restagingModalClose();
      }
    }, 1000);
  }

  // 延后还提示弹窗关闭
  restagingModalClose = () => {
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: 'RestagingRepayModelClose', target: this });
    const { amount } = this.state;
    this.setState({ showRestagingModel: false }, () => {
      if (amount && Number(amount) > 0) {
        this.judgeCallCounter();
      } else {
        this.directCallCounter = true;
        dispatchTrackEvent({
          event: EventTypes.BC,
          beaconId: 'DirectSubmit',
          target: this,
          beaconContent: { cus: { expressScene: this.expressScene, scene: this.billType === 'advanced-stage' ? 'SCENE_SXF' : '' } },
        });
        this.submitCheck();
      }
      this.isInitFinished = true;
    });
  }

  // 延后还（延长还款日）试算
  async extendRepayCalTrial() {
    const { delayRepayAdjustInfo: { orderNoList, adjustCnt, needRepayType } = {} } = getStore('extendRepayInfo') || {};
    const param = {
      orderNoList,
      adjustCnt,
      needRepayType,
    };
    const [delayRepayCalData = {}, getNearBillsData = {}] = await Promise.all([
      Dispatch.repayment.delayRepayCal(param),
      Dispatch.repayment.getNearBills({ additionalQueryList: ['001'] }, { setNearAmount: true })
    ]);
    const { data } = delayRepayCalData || {};
    const { duePayTotalAmt = 0, needRepayOrderNoList = [] } = data || {};
    const { repayBillList = [], courtCostBalance } = getNearBillsData || {};
    const needPayRepayBillList = repayBillList && repayBillList.length > 0 && repayBillList.filter(repayBillItem => needRepayOrderNoList.includes(repayBillItem.orderNo));
    this.billList = needPayRepayBillList;
    this.selectedBillList = needPayRepayBillList;
    this.courtCostBalance = Number(courtCostBalance) > 0 ? courtCostBalance : 0;
    this.courtOnly = Number(courtCostBalance) === Number(duePayTotalAmt) ? '1' : '0';

    this.immutableAmount = Number(duePayTotalAmt || 0).toFixed(2);
    this.setState({
      amount: duePayTotalAmt || 0,
    }, () => {
      const { amount } = this.state;
      if (amount && Number(amount) > 0) {
        const { showExtendModalSecond } = this.state;
        this.countDownToClose(showExtendModalSecond);
        this.setState({ showExtendModel: true });
      } else {
        Madp.showModal({
          title: '温馨提示',
          content: '您的办理信息存在实时变动，烦请退出，重新从服务入口进入',
          confirmText: '退出重进',
          confirmColor: themeColor,
          showCancel: false,
          success(res) {
            if (res.confirm) {
              Madp.redirectTo({ url: '/pages/bill-extend/list?repaymentFlag=Y' });
            }
          }
        });
      }
    });
  }

  // 倒计时三秒后关闭弹窗
  countDownToClose = (seconds) => {
    this.timer = setInterval(() => {
      seconds -= 1;
      this.setState({
        showExtendModalSecond: seconds
      });
      if (seconds <= 0) {
        clearInterval(this.timer);
        this.extendModalClose();
      }
    }, 1000);
  }

  // 延后还提示弹窗关闭
  extendModalClose = () => {
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: 'ExtendRepayModelClose', target: this });
    const { amount } = this.state;
    this.setState({ showExtendModel: false }, () => {
      if (amount && Number(amount) > 0 && this.isInitFinished === true) {
        this.judgeCallCounter();
      }
    });
  }

  getLoginInfo = async () => {
    const { userIdHash, custIdHash } = await getLoginInfo() || {};
    this.loginInfoHash = { userHashNo: userIdHash, custHashNo: custIdHash };
  }
  // 协商还进行试算
  async consultRepayCalTrial() {
    // 1.获取案件申请号
    const applyNo = Url.getParam('applyNo') || '';

    // 2.获取协商还试算数据: preRepayAmt-办理前首付还款金额-180; preWaiveAmt-办理前减免金额-20; preRepayTotalAmt-办理前还款总金额-200
    const negotiateRepayTrialData = await Dispatch.repayment.queryNegotiateRepayTrial({ applyNo, ...this.loginInfoHash });
    const { data, ret, errMsg } = negotiateRepayTrialData || {};
    const { preRepayAmt, preWaiveAmt } = data || {};

    // 账单或应还金额
    this.immutableAmount = (Number(preRepayAmt || 0) + Number(preWaiveAmt || 0)).toFixed(2);
    this.consultPreWaiveAmt = Number(preWaiveAmt || 0).toFixed(2);
    // 3. 还款试算，得到还款入账的借据信息，贷款用于校验
    const params = {
      repayMode: this.repayMode,
      transTotalAmt: preRepayAmt
    };
    // 协商还试算失败，弹窗提示(如果用户在其他地方还款后，待还总金额小于门槛金额，则无法进行协商还办理)
    if (ret !== '0') {
      this.setState({
        showConsultErrorModel: true,
        consultRepayErrorMsg: errMsg || '您的欠款信息有变更，无法继续办理协商还，如需办理请联系人工客服95786协助处理',
        amount: this.immutableAmount || 0
      });
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'trialFail',
        beaconContent: {
          cus: {
            trialErrMsg: errMsg
          }
        }
      });
      return;
    }
    const { data: repayTransTrialResult } = await Dispatch.repayment.repayTransTrial(params, true);
    const { repayTrialDetailList } = repayTransTrialResult || {};


    this.setState({
      repayTrialDetailList: repayTrialDetailList,
      amount: this.immutableAmount || 0,
    }, () => {
      const { amount } = this.state;
      if (amount && Number(amount) > 0) {
        const { showConsultRepayModalSecond } = this.state;
        const needShowConsultRepayModel = Url.getParam('needShowConsultRepayModel') || '';
        // 二次确认页不弹倒计时弹窗
        if (needShowConsultRepayModel === 'N') {
          this.consultRepayModalClose();
          this.setState({ isInitDone: true });
        } else {
          // 协商还办理前还款，初始化完成，弹窗+立即还款按钮可点击
          this.consultCountDown(showConsultRepayModalSecond);
          this.setState({ showConsultRepayModel: true, isInitDone: true });
        }
      } else {
        Madp.showModal({
          title: '温馨提示',
          content: '您的办理信息存在实时变动，烦请退出，重新从服务入口进入',
          confirmText: '退出重进',
          confirmColor: themeColor,
          showCancel: false,
          success(res) {
            if (res.confirm) {
              Madp.redirectTo({ url: '/pages/consult-repay-apply/index?repaymentFlag=Y' });
            }
          }
        });
      }
    });
  }

  // 倒计时三秒后关闭弹窗
  consultCountDown = (seconds) => {
    this.consultTimer = setInterval(() => {
      seconds -= 1;
      this.setState({
        showConsultRepayModalSecond: seconds
      });
      if (seconds <= 0) {
        clearInterval(this.consultTimer);
        this.consultRepayModalClose();
      }
    }, 1000);
  }

  consultRepayModalClose = (needCallCounter = false) => {
    // 如直接点击去还款，先清楚定时器, 否则会再执行一次该方法
    clearInterval(this.consultTimer);
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: 'ConsultRepayModalClose', target: this });
    const { amount } = this.state;
    this.setState({ showConsultRepayModel: false }, () => {
      // 有优惠和小招荷包扣款的时候，不自动拉起收银台
      if ((this.consultPreWaiveAmt > 0 || this.xiaozhaoAmount > 0) && !needCallCounter) {
        return;
      } else {
        if (amount && Number(amount) > 0) {
          this.directCallCounter = true;
          this.submitCheck();
        }
      }
    });
  };

  clearCoupon(shouldTrial, trialAmt) {
    const { cleanCheck } = this.couponSelectorRef;
    cleanCheck && cleanCheck();
    this.setState({ selectedCoupon: null });
    if (shouldTrial) {
      setTimeout(() => {
        this.repayTransTrialApi(trialAmt);
      }, 0);
    }
  }

  /**
 * @description: 还款接入新优惠券查询接口，请求优惠券列表参数配置
 */
  getWaiveCouponParam(val) {
    const { amount } = this.state;
    let repayType = this.billType === '7days' ? 'normalPay' : 'prepay';
    if (this.isD07Tag) repayType = 'prepay'; // D07且未逾期打标的用户，视为提前还款
    return {
      repayType,
      repayDetailList: this.selectedBillList,
      repayTotalAmt: val || amount || this.immutableAmount,
      currentBillClear: this.currentBillClear,
      amtHasChanged: Number(val || 0) > 0 && Number(this.immutableAmount || 0) > 0 && (Number(val) !== Number(this.immutableAmount)),
    };
  }

  async getCouponList(val, defaultChoose = false, callback) {
    if (this.courtOnly === '1') { // 仅剩法诉费无借据，不能查券
      if (callback && typeof callback === 'function') await callback();
      return;
    }
    let couponObject = {
      availableCouponDetailList: [],
      appExclusiveCouponDetailList: [],
      unavailableCouponDetailList: []
    };
    let filteredCouponObj = {};
    // 注意：im的场景已经接入了息费减免，如果后续快捷还款页要接入息费减免，限制条件改为this.billType === 'fee-reduce'
    if (this.expressScene === '31' && this.billType === 'fee-reduce') {
      const { awardDetailList = [] } = await Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }) || {};
      const { usableQualConponList } = filterQualCoupons(awardDetailList || [], this.selectedBillList) || {};
      couponObject.availableCouponDetailList = (usableQualConponList || []).filter((item) => (item.awardType === '216' && item.subUseSceneCode === 'SSA02'));
    } else {
      try {
        const { awardDetailList = [] } = await Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }) || {};
        const formatAwardDetailList = (awardDetailList || []).map((item) => transformNewCouponAPIRes(item));
        couponObject = await filterWaiveCoupons(formatAwardDetailList, this.getWaiveCouponParam(val));
      } catch (error) {
        dispatchTrackEvent({
          target: this, event: EventTypes.EV, beaconId: 'CouponApiError', beaconContent: { cus: error }
        });
      }
      filteredCouponObj = this.filterCoupon(
        couponObject,
        this.billType === '7days' && this.isDueTagCust !== 'Y' && !this.isD07Tag,
        this.getSpecialCouponScene
      );
    }
    if (this.billType === 'advanced-stage' && filteredCouponObj && filteredCouponObj.availableCouponDetailList) {
      filteredCouponObj.availableCouponDetailList = filteredCouponObj.availableCouponDetailList.filter((couponItem) => (couponItem.awardType === '216' && couponItem.awardAmtType === '1'));
    }
    const { amount } = this.state;
    this.setState({
      couponObject: filteredCouponObj,
      amount: val || amount, // 提前存一下金额，避免couponObject更改引发CouponTip预计节省金额计算使用旧的amount值
    }, () => {
      // 执行默认选券逻辑
      if (defaultChoose) {
        this.handleCouponListResult(callback);
      }
      const { couponObject: newCouponObject, amount: newAmount } = this.state;
      const { availableCouponDetailList, unavailableCouponDetailList } = newCouponObject || {};
      if ((this.originAvailCouponList || []).length <= 0) {
        this.originAvailCouponList = availableCouponDetailList || [];
      } else {
        if ((availableCouponDetailList || []).length <= 0) {
          const guideGouponList = (unavailableCouponDetailList || []).filter((item) => (
            item.awardType === '216'
            && Number((item || {}).transAmt || '0.00') > Number(newAmount || '0.00')
            && Number((item || {}).transAmt || '0.00') <= Number(this.immutableAmount || '0.00')
          ));
          if (guideGouponList.length) {
            this.setState({ guideCouponChoose: true });
            dispatchTrackEvent({
              target: this,
              event: EventTypes.SO,
              beaconId: 'GuideCouponChoose',
              beaconContent: { cus: { expressScene: this.expressScene, newScene: this.getNewScene, overdueDay: this.overdueDay } },
            });
          } else {
            this.setState({ guideCouponChoose: false });
          }
        } else {
          this.setState({ guideCouponChoose: false });
        }
      }
    });
  }

  // 再分期资格券查询
  async getTransCouponList() {
    const { awardDetailList = [] } = await Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }) || {};
    const { usableQualConponList } = filterQualCoupons(awardDetailList) || {};
    this.extendRepayQualificationCoupon = (usableQualConponList || []).filter((item) => item.awardType === '217')[0];
    this.extendRepayFutureCoupon = (usableQualConponList || []).filter((item) => item.awardType === '305')[0];
  }

  // 查询梯度减免资格券
  async getFeeRightsCouponList() {
    const { awardDetailList = [] } = await Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }) || {};
    const { usableQualConponList } = filterQualCoupons(awardDetailList || [], [], Number(this.overdueDay || 0) >= 1) || {};
    const prepayFeeRightsCouponList = (usableQualConponList || []).filter((item) => (item.awardType === '306'));
    this.hasPrepayFeeRightsCoupon = prepayFeeRightsCouponList && prepayFeeRightsCouponList.length > 0;
    this.prepayFeeRightsCoupon = prepayFeeRightsCouponList && prepayFeeRightsCouponList[0];
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: this.hasPrepayFeeRightsCoupon ? 'HavePrepayFeeRightsCoupon' : 'NoPrepayFeeRightsCoupon',
      beaconContent: { cus: {} }
    });
  }

  // 获取优惠券列表后，根据当前已选券情况默认选中或清空已选券
  async handleCouponListResult(callback) {
    const { selectedCoupon, couponObject } = this.state;
    const { availableCouponDetailList, appExclusiveCouponDetailList } = couponObject;
    let isCleared = false; // 本次是否清空优惠券
    if (selectedCoupon
      && (availableCouponDetailList && availableCouponDetailList
        .findIndex((i) => i.awardNo === selectedCoupon.awardNo) === -1)) {
      // 已选券未在可用券列表中，清空已选券
      this.clearCoupon();
      isCleared = true;
    }
    const defaultCoupon = getdefaultCheckCouponId([...availableCouponDetailList, ...appExclusiveCouponDetailList], this.awardNo);
    if (this.expressScene === '31' && this.awardNo && defaultCoupon && this.awardNo !== defaultCoupon.awardNo) {
      // 没有查找到需要使用的优惠卷，展示失效页
      this.setState({
        showInvalidityPage: true
      });
    }
    if ((!selectedCoupon || isCleared)
      && defaultCoupon) {
      this.chooseCoupon(defaultCoupon.awardNo);
      this.setState({
        selectedCoupon: defaultCoupon
      }, async () => {
        if (callback && typeof callback === 'function') await callback();
      });
    } else {
      if (callback && typeof callback === 'function') await callback();
    }
  }

  chooseCoupon(awardNo) {
    if (awardNo) {
      const { chooseCoupon } = this.couponSelectorRef;
      if (process.env.TARO_ENV === 'weapp') {
        setTimeout(() => { // 自动选券要一定时间延迟
          chooseCoupon(awardNo);
        }, 100);
      } else {
        chooseCoupon(awardNo);
      }
    }
  }

  /**
 * @description: 优惠券列表前端过滤：1. 216券及结清券特殊判断
 * @param { Object } 优惠券信息对象 原始信息
 * @param { is7day: Boolean } 是否近期待还（近期代还需要判断借据是否结清，以识别是否能使用216结清券）
 * @param { isOnly216: Boolean } 是否仅能使用216券
 * @return { Object } 筛选处理过后的优惠券信息对象信息
 */
  filterCoupon(CouponObj, is7day, isOnly216) {
    // 1. 216券及结清券特殊判断
    const allCouponObj = filterCoupon(CouponObj, is7day, isOnly216, this.selectedBillList);
    return allCouponObj;
  }

  async onCouponSelected(coupon) {
    this.setState({ selectedCoupon: coupon }, () => {
      setTimeout(() => {
        if (this.billType === 'advanced-stage') {
          this.extendRepayCal();
        } else {
          const { amount, displayAmount } = this.state;
          this.repayTransTrialApi(displayAmount || amount);
        }
      }, 0);
    });
  }

  // 判断试算是否需要加法诉费金额
  getTrialTransTotalAmt(trialAmt) {
    let trialTransTotalAmt = parseFloat(trialAmt).toFixed(2);
    return Number(trialTransTotalAmt).toFixed(2);
  }

  // 更新忽略挽留标志
  updateIgnoreLeave = () => {
    this.needIgnoreLeave = true;
  }

  // 点击编辑按钮
  afterEditClick = () => {
    if (process.env.TARO_ENV === 'h5') {
      try {
        const aimEl = (document.querySelectorAll('.amountInput > .at-input__container > .at-input__input') || [])[0] || {};
        if (aimEl.setSelectionRange) {
          aimEl.focus();
          const { amount } = this.state;
          aimEl.setSelectionRange((amount || '').length, (amount || '').length);
        } else {
          const range = aimEl.createTextRange() || {};
          range.collapse(false);
          range.select();
        }
      } catch (error) {
        console.log(error);
      }
    }
    this.hasCompEdited = true;
    // this.setState({
    //   letEditable: true,
    //   editing: true,
    // });
  }

  getOverPayAmtRepayFlag() {
    const { repayTrialDetailList, overPayAmt } = this.state; // 溢缴款
    const not0100List = (repayTrialDetailList || []).filter((item) => (!item.debtorSplitDetailList)) || [];
    const not0100AmountList = not0100List.length && not0100List.map((e) => Number(e.canPayAmt));
    const not0100Amt = not0100AmountList && not0100AmountList.length && not0100AmountList.reduce((prev, cur) => prev + cur).toFixed(2) || 0; // 空数组 reduce会报错
    const normalBillFlag = (repayTrialDetailList || []).some((item) => !item.debtorSplitDetailList);
    const {
      overPayAmtRepayFlag,
      availableAmount,
      closeAlipay0100BillFlag
    } = commonGetOverPayAmtRepayFlag(normalBillFlag, overPayAmt, this.getActualRepayAmt, this.billType, not0100Amt, is0100Bills(repayTrialDetailList), repayTrialDetailList, true);
    this.availableAmount = availableAmount;
    this.closeAlipay0100BillFlag = closeAlipay0100BillFlag;
    return overPayAmtRepayFlag;
  }

  /**
   * 是否为部分提前还款
   */
  get isPartRepayAdvanced() {
    return this.billType === 'total' && !this.isRepayAll;
  }


  /**
 * 只能使用特殊优惠券的优惠券场景
 */
  get getSpecialCouponScene() {
    const { hasOverdueFlag } = this.state;
    return this.isDueTagCust === 'Y' || hasOverdueFlag || this.isPartRepayAdvanced || this.billType === 'fee-reduce';
  }

  get isD07Tag() {
    let isD07Tag = false;
    const { repayControlDetailList } = this.billList;
    (repayControlDetailList || []).forEach((item = {}) => {
      if (item.controlCode === 'C401' && item.eventCode === 'GKSJ011') isD07Tag = true;
    });
    return isD07Tag;
  }

  /**
 * 是否为打标用户
 * 若仅打D07标，则isDueTagCust不返回Y，也即仅对101和C06标返回Y
 * @return { Boolean }
 */
  get isDueTagCust() {
    const { repayControlDetailList } = this.billList;
    if ((repayControlDetailList || []).length === 0) return 'N';
    let overDueFlag = false;
    (repayControlDetailList || []).forEach((item = {}) => {
      if (item.controlCode === 'C401' && (item.eventCode === 'GKSJ032' || item.eventCode === 'GKSJ033' || item.eventCode === 'GKSJ003')) overDueFlag = true;
    });
    if (overDueFlag) {
      return 'Y';
    } else {
      return 'N';
    }
  }


  get repayMode() {
    let repayMode = 'ADVANCE';
    if (this.courtOnly === '1') {
      return 'AMT';
    }
    if (this.billType === '7days') {
      if (this.billList.isDueTagCust === 'Y') {
        repayMode = 'AMT';
        return repayMode;
      }
      repayMode = 'PERIOD';
      return repayMode;
    } else if ((this.billType === 'fee-reduce' && this.feeReduceMode === '1') || this.billType === 'preConsultRepay') {
      return 'AMT';
    } else if (this.billType === 'extend') {
      return 'PERIOD';
    }
    return repayMode;
  }

  get isDisabledRepayBtn() {
    const {
      editing, isInitDone, hitControl
    } = this.state;
    if (editing || !isInitDone || hitControl) {
      return true;
    }
    return false;
  }

  // 总应还金额
  get transTotalAmt() {
    const { amount } = this.state;
    if (this.billType === 'preConsultRepay') {
      return (Number(amount || 0) - Number(this.consultPreWaiveAmt || 0)).toFixed(2);
    }
    return amount;
  }

  // 未扣减溢缴款
  get getActualRepayAmt() {
    const { settleWaiveAmt, waiveTotalAmt } = this.state;
    const calculateAmt = (Number(this.transTotalAmt || 0) - Number(settleWaiveAmt || 0) - Number(waiveTotalAmt || 0)).toFixed(2);
    return calculateAmt > 0 ? Util.numToStr(calculateAmt) : Util.numToStr(0);
  }

  // 扣减溢缴款后 commonGetOverPayAmtRepayFlag
  get actualRepayAmt() {
    const {
      repayTrialDetailList, overPayAmt, settleWaiveAmt, preRepayAmt
    } = this.state;
    const not0100AmountList = (repayTrialDetailList || []).filter((item) => !item.debtorSplitDetailList).map((e) => Number(e.canPayAmt));
    const not0100Amt = not0100AmountList.length && not0100AmountList.reduce((prev, cur) => prev + cur).toFixed(2); // 空数组 reduce会报错
    const commonNeedRepayAmt = commonGetActualRepayAmt(
      this.getActualRepayAmt, overPayAmt, settleWaiveAmt, preRepayAmt,
      not0100Amt, this.transTotalAmt, this.getActualRepayAmt,
      this.availableAmount, false, is0100Bills(repayTrialDetailList),
      this.getOverPayAmtRepayFlag(),
    );

    return parseFloat(commonNeedRepayAmt).toFixed(2);
  }

  get showDisplaySettleWaiveAmt() {
    const { displaySettleWaiveAmt } = this.state;
    return Number(displaySettleWaiveAmt) > 0 ? displaySettleWaiveAmt : 0;
  }

  /**
 * 还款金额是否可编辑
 */
  get editable() {
    const {
      selectedCoupon, alldueOrOnTimeFlag, allCanPartPayFlag, isOverDueUserFlag,
    } = this.state;
    // im场景不可编辑金额
    if (this.expressScene === '31') {
      return false;
    }
    // 息费减免场景不可编辑金额
    if (this.billType === 'fee-reduce') {
      return false;
    }
    // 再分期还款不可编辑金额
    if (this.billType === 'advanced-stage') {
      return false;
    }
    // 延后还还款不可编辑金额
    if (this.billType === 'extend') {
      return false;
    }

    // 协商还场景
    if (this.billType === 'preConsultRepay') {
      return false;
    }
    // 账单总金额小于10别修改了
    if (Number(this.immutableAmount) < 10) {
      return false;
    }
    // 仅还法诉费时，支持修改金额
    if (this.courtOnly === '1') {
      return true;
    }
    // 息费减免券，减免结清类型，不可修改还款金额
    if (selectedCoupon && selectedCoupon.awardAmtType === '4' && selectedCoupon.awardType === '216') {
      return false;
    }
    if (this.billList.isDueTagCust === 'Y') {
      // 逾期打标可修改
      return true;
    } else if (this.billList.isDueTagCust !== 'Y' && this.billType === 'total' && isOverDueUserFlag) {
      // 提前还款,有逾期客户（loanStatus!=01），可以修改
      return true;
    } else if (this.billList.isDueTagCust !== 'Y' && this.billType === '7days' && alldueOrOnTimeFlag) {
      // 近期待还, 若勾选的账单全部都属于已到期或已逾期
      return true;
    } else {
      // 借据提前还款，都支持部分还款可修改
      return allCanPartPayFlag;
    }
  }

  /**
   * 获取客户级的逾期天数（全部可见可还借据）
   * 这里的逾期天数主要用作支付方式的判断：若客户存在一笔可见可还的借据逾期天数 ≥ 渠道技参配置天数，则按渠道技参中的“逾期还款方式”展示
   */
  get overdueDay() {
    let overdueDay = 0;
    const repayStatus = getStore('repayStatus');
    // 区分打标和非打标用户
    if (repayStatus === repayStatusType.dueTagCust) {
      (this.allBillList || []).forEach((bill) => {
        if (overdueDay < bill.overdueDays) {
          overdueDay = bill.overdueDays;
        }
      });
    } else {
      (this.allBillList || []).forEach((bill) => {
        if (overdueDay < bill.displayOverdueDays) {
          overdueDay = bill.displayOverdueDays;
        }
      });
    }
    return overdueDay;
  }

  // 非打标用户逾期且超过宽限期
  get realOverDueFlag() {
    const { repayBillList } = getStore('nearBills');
    if (!repayBillList || repayBillList.length <= 0) return false;
    let realOverDueFlag = false;
    repayBillList.forEach((bill) => {
      if (bill.overdueDays > 0 && Util.timeMinus(bill.inteDate, bill.payDate, 'day', 'date') < bill.overdueDays) {
        realOverDueFlag = true;
      }
      if (bill) {
        if (bill.waivePayFineAmt && Number(bill.waivePayFineAmt) > 0) {
          this.waivePayAmt = Util.floatAdd(Number(this.waivePayAmt), Number(bill.waivePayFineAmt));
        }
        if (bill.waivePayInteAmt && Number(bill.waivePayInteAmt) > 0) {
          this.waivePayAmt = Util.floatAdd(Number(this.waivePayAmt), Number(bill.waivePayInteAmt));
        }
        if (bill.waivePayOnetimeFee && Number(bill.waivePayOnetimeFee) > 0) {
          this.waivePayAmt = Util.floatAdd(Number(this.waivePayAmt), Number(bill.waivePayOnetimeFee));
        }
        if (bill.waivePayPeriodFee && Number(bill.waivePayPeriodFee) > 0) {
          this.waivePayAmt = Util.floatAdd(Number(this.waivePayAmt), Number(bill.waivePayPeriodFee));
        }
        if (bill.waivePayPrepayFee && Number(bill.waivePayPrepayFee) > 0) {
          this.waivePayAmt = Util.floatAdd(Number(this.waivePayAmt), Number(bill.waivePayPrepayFee));
        }
      }
    });
    return realOverDueFlag;
  }

  get showRandomReduce() {
    let randomReduceFlag = false;
    const repayStatus = getStore('repayStatus');
    const { courtCostAmt } = this.state;
    if (repayStatus !== repayStatusType.dueTagCust
      && (repayStatus === repayStatusType.inSeven
        || repayStatus === repayStatusType.inSevenMutil
        || repayStatus === repayStatusType.today
        || repayStatus === repayStatusType.outSeven
        || repayStatus === repayStatusType.overDue)
      && !this.realOverDueFlag
      && (Number(courtCostAmt || 0) <= 0)) {
      randomReduceFlag = true;
    }
    return randomReduceFlag;
  }

  // 是否 不可以选择优惠卷
  get isCouponNotChangeable() {
    const { selectedCoupon } = this.state;
    return (this.expressScene === '31' && selectedCoupon && selectedCoupon.awardType === '216');
  }

  get canEditable() {
    const { letEditable } = this.state;
    if (this.editable && letEditable) {
      return true;
    }
    return false;
  }

  get xiaozhaoAmount() {
    const { preRepayAmt } = this.state;
    if (Number(preRepayAmt) > Number(this.getActualRepayAmt)) {
      return 0;
    } else if ((Number(this.availableAmount) + Number(preRepayAmt)).toFixed(2) > Number(this.getActualRepayAmt)) {
      return (Number(this.getActualRepayAmt) - Number(preRepayAmt)).toFixed(2);
    } else {
      return Number(this.availableAmount);
    }
  }

  get preRepayUsedAmount() {
    const { preRepayAmt } = this.state;
    if (Number(preRepayAmt) > Number(this.getActualRepayAmt)) {
      return Number(this.getActualRepayAmt);
    } else {
      return Number(preRepayAmt);
    }
  }

  get getNewScene() {
    let newScene = '';
    switch (this.billType) {
      case '7days':
      case 'total':
        if (this.isDueTagCust || (Number(this.overdueDay || '0') > 0)) {
          newScene = 'overDuePay';
        } else {
          newScene = this.billType === '7days' ? 'onTimePay' : 'advancePay';
        }
        break;
      case 'advanced-stage':
        newScene = 'restagingPay';
        break;
      case 'extend':
        newScene = 'delayPay';
        break;
      case 'fee-reduce':
        newScene = 'stepReducePay';
        break;
      case 'consult':
        newScene = 'consultPay';
        break;
      case 'preConsultRepay':
        newScene = 'consultPay';
      default:
        break;
    }
    return newScene;
  }

  get isC402Control() {
    let isC402Control = false;
    const { repayControlDetailList } = this.billList;
    (repayControlDetailList || []).forEach((item) => {
      // TODO：待确认是否需要交管控事件类型eventCode
      // if (item.controlCode === 'C402' && item.eventCode === 'GKSJ011') isC402Control = true;
      if (item.controlCode === 'C402') isC402Control = true;
    });
    return isC402Control;
  }

  render() {
    const {
      amount, editing, courtCostAmt, showCouponSelector, couponObject,
      selectedCoupon, waiveTotalAmt,
      displayAmount, hasOverdueFlag, showCounter,
      waivePrepayFeeAmt, virtualCustormerBubble, showInvalidityPage, showConsultOverdueModal, showRestagingModel,
      showExtendModel, showExtendModalSecond, displayLateFee, showRestagingModalSecond,
      overpayBalance, remitTotalAmount, preRepayAmt, showOverpayDrawer, guideCouponChoose,
      showRepayDetailDrawer, repayTrialDetailList, totalPayPrepayFee,
      insufficientRemainAmt, virtualBankTag, settleWaiveAmt,
      showConsultRepayModel, showConsultRepayModalSecond, showConsultErrorModel, consultRepayErrorMsg
    } = this.state;

    const randomReduceText = virtualCustormerBubble && virtualCustormerBubble.dataObj && virtualCustormerBubble.dataObj.contentList && virtualCustormerBubble.dataObj.contentList[0] && virtualCustormerBubble.dataObj.contentList[0].title || '';
    const randomReduceSwitch = virtualCustormerBubble && virtualCustormerBubble.dataObj && virtualCustormerBubble.dataObj.contentList && virtualCustormerBubble.dataObj.contentList[0] && virtualCustormerBubble.dataObj.contentList[0].targetUrl || '';
    const randomReduceFlag = (this.showRandomReduce && randomReduceText
      && (!selectedCoupon || JSON.stringify(selectedCoupon) === '{}')
      && (!waivePrepayFeeAmt || Number(waivePrepayFeeAmt) <= 0) && (!this.waivePayAmt || Number(this.waivePayAmt) <= 0)
      && this.billType === '7days'
      && (Number(this.interestTotalAmt || 0) > 0 || Number(this.costTotalAmt) > 0)
      && Number(settleWaiveAmt || 0) <= 0) ? 'Y' : 'N';

    const isShowOverRepayRow = (this.getOverPayAmtRepayFlag() || Number(preRepayAmt) > 0);

    // 息费减免需还账单总额 = 还款金额（输入金额） + 优惠金额（可减免息费 + 可免违约金）
    const { waiveAmount } = getStore('feeReduceInfo') || {};
    const feeReduceTotalAmt = (this.expressScene !== '31' && this.billType === 'fee-reduce') ? `还款金额：${(Util.floatAdd(Number(this.immutableAmount || 0), Number(waiveAmount || 0))).toFixed(2)}元` : '';

    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="还款"
          beaconId="NavBarWeapp.Click"
          leftArea={[
            {
              type: 'icon',
              value: 'back',
              onClick: this.beforeMiniRouteLeave
            }
          ]}
        />
        <MUView className="pages-bg repayment-express">
          <MUView className={`repayment-express-content ${showCounter && 'content-blur'}`}>
            <MUView className="topView">
              <MUView className="sum-title">{(this.expressScene !== '31' && this.billType === 'fee-reduce') ? '支付' : '还款'}金额(元)</MUView>
              <MUView className="input-area">
                {this.canEditable ? <MUInput
                  editable
                  className="amountInput"
                  beaconId="amountInput"
                  beaconContent={{ cus: { repayMode: this.specialRepayScene === 'consult' ? 'consultRepay' : '', expressScene: this.expressScene, newScene: this.getNewScene } }}
                  type="digit"
                  onFocus={() => { this.setState({ editing: true }); }}
                  onBlur={(val) => {
                    const inputNum = Number(val);
                    if (Number.isNaN(inputNum)) {
                      Util.toast('请输入正确的数字');
                      this.resetAmount();
                      return this.immutableAmount;
                    }
                    this.setState({ amount: val, displayAmount: val }, () => {
                      if (!this.checkAmountChange(val)) return this.immutableAmount;
                      this.setState({ amount: val });
                      this.handleAmtChangeDebounce(val);
                    });
                  }}
                  value={displayAmount || amount}
                /> : <MUView className="amountInput">{displayAmount || amount}</MUView>}
                {this.canEditable && !editing && amount !== '' && <MUImage className="edit-img" src={editImg} style={{ transform: `translateX(${`${amount}`.length * 15}px)` }} />}
              </MUView>
              {(Number(courtCostAmt || 0) > 0) ? (
                <MUView className="topView-court-cost-amt">{`(含司法处置费${Number(courtCostAmt || 0).toFixed(2)}元)`}</MUView>
              ) : null}
              {/* 仅在标准还款下展示 */}
              {this.isStandardRepay && Number(courtCostAmt || 0) <= 0 && Number(totalPayPrepayFee || 0) > 0 && this.overdueDay <= 0 && !this.isC402Control ? (
                <MUView className="topView-court-cost-amt">{`(含提前还款违约金${Number(totalPayPrepayFee || 0).toFixed(2)}元)`}</MUView>
              ) : null}
              {this.repayBubbleParam && JSON.stringify(this.repayBubbleParam) !== '{}' ? (
                <MUView className="topView-repay-bubble">
                  <RepayBubble
                    repayBubbleParam={this.repayBubbleParam}
                  />
                </MUView>
              ) : null}
              {feeReduceTotalAmt ? (
                <MUView className="topView-court-cost-amt">{feeReduceTotalAmt}</MUView>
              ) : null}
            </MUView>
            <MUView className="repayment-express-content__reduction">
              {!showInvalidityPage && this.billType !== 'preConsultRepay' && <CouponTip
                trackPrefix="repayment.expressRepay"
                couponObject={couponObject}
                waiveTotalAmt={Number(waiveTotalAmt) > 0 ? waiveTotalAmt : ''}
                isOverdue={this.isDueTagCust === 'Y' || hasOverdueFlag}
                isCannotCoupon={this.isPartRepayAdvanced || this.billType === 'fee-reduce'}
                isHideCouponColumn={this.billType === 'fee-reduce'}
                isSpecialCouponScene={this.getSpecialCouponScene}
                selectBankTransferFlag={this.isCouponNotChangeable || false}
                selectedCoupon={selectedCoupon}
                onClick={() => this.setState({ showCouponSelector: true })}
                settleWaiveAmt={this.showDisplaySettleWaiveAmt}
                getRrialParam={() => ({
                  repayMode: this.repayMode,
                  transTotalAmt: String(parseFloat(amount).toFixed(2)),
                  repayDetailList: prepareRepayDetailList(this.selectedBillList, this.billType),
                })}
                specialRepayScene={this.specialRepayScene}
                ignoreTrial={this.billType === 'advanced-stage'}
                hasPrepayFeeRightsCoupon={this.hasPrepayFeeRightsCoupon}
                guideCouponChoose={guideCouponChoose}
                getVirtualRrialParam={() => ({
                  repayMode: this.repayMode,
                  transTotalAmt: String(parseFloat(this.immutableAmount || '0.00').toFixed(2)),
                  repayDetailList: prepareRepayDetailList(this.selectedBillList, this.billType),
                })}
                guideCouponChooseCallBack={(val, guideChoose) => this.handleAmtChange(val, guideChoose)}
                expressScene={this.expressScene}
                newScene={this.getNewScene}
                overdueDay={this.overdueDay}
              />}
              {/* 协商还还款息费优惠金额 */}
              {Number(this.consultPreWaiveAmt) > 0 && this.billType === 'preConsultRepay' ? (
                <MUView className="repayinfo">
                  <MUView className="repayinfo__title">还款优惠</MUView>
                  <MUView
                    className="repayinfo__content"
                    beaconId="ShowOverpayDrawer"
                    beaconContent={{ cus: { expressScene: this.expressScene, newScene: this.getNewScene, overdueDay: this.overdueDay } }}
                  >
                    <MUText className="highlight-text">-{Number(this.consultPreWaiveAmt || '0.00').toFixed(2)}元</MUText>
                  </MUView>
                </MUView>
              ) : null}
              {Number(this.showDisplaySettleWaiveAmt) > 0 && this.billType !== 'preConsultRepay' && <MUView className="repayinfo">
                <MUView className="repayinfo__title">随机立减</MUView>
                <MUView className="repayinfo__content">
                  <MUText className="highlight-text">-{this.showDisplaySettleWaiveAmt}元</MUText>
                </MUView>
              </MUView>}
              {this.hasPrepayFeeRightsCoupon && this.billType !== 'preConsultRepay' && <MUView className="repayinfo" beaconId="PrepayFeeRightsCoupon">
                <MUView className="repayinfo__title">
                  违约金减免
                  {Number(displayLateFee) > 0 ? <MUView className={`repayinfo__title__bubble${themeColor === '#E60027' ? ' repayinfo__title__bubble--theme' : ''}`}>已全免</MUView> : null}
                </MUView>
                {Number(displayLateFee) > 0 ? <MUView className="repayinfo__content"><MUText className="highlight-text">-{Number(displayLateFee || '0.00').toFixed(2)}元</MUText></MUView> : <MUView className="repayinfo__content">无违约金</MUView>}
              </MUView>}
              {isShowOverRepayRow ? (
                <MUView className="repayinfo">
                  <MUView className="repayinfo__title">小招荷包</MUView>
                  <MUView
                    className="repayinfo__content"
                    beaconId="ShowOverpayDrawer"
                    beaconContent={{ cus: { expressScene: this.expressScene, newScene: this.getNewScene, overdueDay: this.overdueDay } }}
                    onClick={() => this.setState({ showOverpayDrawer: true })}
                  >
                    <MUText className="highlight-text">{`-${Util.floatAdd(Number(this.xiaozhaoAmount || '0.00'), Number(this.preRepayUsedAmount || '0.00')).toFixed(2)}元`}</MUText>
                    <MUIcon value="arrow-right" size={14} color="#CACACA" />
                  </MUView>
                </MUView>
              ) : null}
              {/* 仅在标准还款下展示 */}
              {this.isStandardRepay && this.billType !== 'preConsultRepay' && repayTrialDetailList && repayTrialDetailList.length > 0 && this.courtOnly !== '1' ? <MUView className="repayinfo">
                <MUView className="repayinfo__title">还款明细</MUView>
                <MUView className="repayinfo__content" onClick={() => this.setState({ showRepayDetailDrawer: true })} beaconId="ShowRepayDetail">
                  <MUText>共{repayTrialDetailList.length}笔</MUText>
                  <MUIcon value="arrow-right" size={14} color="#CACACA" />
                </MUView>
              </MUView> : null}
            </MUView>
          </MUView>
          {/* 还款按钮 博弈减免使用原按钮 标准场景使用吸底栏展示实还金额 */}
          <Statistic
            trackPrefix="repayment.expressRepay"
            disabled={this.isDisabledRepayBtn}
            amount={this.actualRepayAmt}
            submitRepay={() => {
              // this.submitCheck();
              this.submitRepay();
              dispatchTrackEvent({
                event: EventTypes.BC,
                beaconId: 'SubmitRepay',
                target: this,
                beaconContent: { cus: { expressScene: this.expressScene, scene: this.billType === 'advanced-stage' ? 'SCENE_SXF' : '', newScene: this.getNewScene, overdueDay: this.overdueDay, awardNo: (selectedCoupon || {}).awardNo || '' } },
              });
            }}
            // courtCostBalance={this.courtOnly === '1' ? amount : courtCostAmt}
            beaconId="repaySum"
            specialRepayScene={this.specialRepayScene}
          />
          {/* 优惠券选择组件 */}
          <CouponSelector
            ref={(ref) => { this.couponSelectorRef = ref; }}
            isOpened={showCouponSelector}
            coupons={couponObject}
            onCouponSelected={(data) => this.onCouponSelected(data)}
            onClose={() => { this.setState({ showCouponSelector: false }); }}
            beaconId="RepaymentCouponSelector"
          />
          {Util.showChatEntry() && <ChatEntry busiEntrance={Util.getBusiEntrance()} themeColor={themeColor} />}
          <OverpayDrawer
            showOverpayDrawer={showOverpayDrawer}
            title="小招荷包"
            overpayBalance={Number(overpayBalance || '0.00').toFixed(2)}
            remitTotalAmount={Number(remitTotalAmount || '0.00').toFixed(2)}
            preRepayAmt={Number(preRepayAmt || '0.00').toFixed(2)}
            onClose={() => this.setState({ showOverpayDrawer: false })}
            onTopCloseClick={() => this.setState({ showOverpayDrawer: false })}
          />
          <RepayCheckOut
            ref={(ref) => { ref && (this.repayCheckOut = ref); }}
            onClose={() => this.setState({ showCounter: false })}
            onSaveExternalFillData={() => this.saveExternalFillData()}
            overdueDay={this.overdueDay}
            randomReduceFlag={randomReduceFlag}
            randomReduceText={randomReduceText}
            interestTotalAmt={this.interestTotalAmt}
            costTotalAmt={this.costTotalAmt}
            selectedBillList={this.selectedBillList}
            randomReduceSwitch={randomReduceSwitch}
            updateIgnoreLeave={this.updateIgnoreLeave}
            afterEditClick={this.afterEditClick}
            virtualBankTag={virtualBankTag}
          />
          {/* 还款明细 */}
          <RepayDetailDrawer
            showRepayDetailDrawer={showRepayDetailDrawer}
            onClose={() => this.setState({ showRepayDetailDrawer: false })}
            repayTrialDetailList={repayTrialDetailList}
            courtCostBalance={courtCostAmt}
            insufficientRemainAmt={insufficientRemainAmt}
          />
          <InvalidityModal
            isOpened={showInvalidityPage}
            onConfirm={() => {
              Util.router.replace('/pages/index/index');
              this.setState({
                showInvalidityPage: false
              });
            }}
            onCancel={() => {
              if (this.callbackUrl) {
                Util.router.replace(this.callbackUrl);
              }
              this.setState({
                showInvalidityPage: false
              });
            }}
          />
          {/* 协商还输入金额超限 */}
          <MUModal
            className="consult-overdue-modal"
            beaconId="ConsultOverDueModel"
            isOpened={showConsultOverdueModal}
            closeOnClickOverlay={false}
          >
            <MUView className="consult-overdue-modal--wrap">
              <MUView className="consult-overdue-modal__top">
                <MUIcon className="repay-cannot-extend-tips-icon" value="tip" size="60" />
              </MUView>
              <MUView className="consult-overdue-modal__content">
                <MUView className="consult-overdue-modal__content__title">温馨提示</MUView>
                <MUView className="consult-overdue-modal__content__explain">
                  可为您减免{Number(this.maxWaiveAmt).toFixed(2)}元息费，仅需
                  <MUText className="consult-overdue-modal__content__explain--point">{Number(this.totalThresholdAmt).toFixed(2)}</MUText>
                  元，即可抵还{Util.floatAdd(this.maxWaiveAmt, this.totalThresholdAmt).toFixed(2)}元
                </MUView>
              </MUView>
              <MUView className="consult-overdue-modal__button">
                <MUButton
                  type="primary"
                  onClick={() => {
                    this.setState({ showConsultOverdueModal: false, amount: this.totalThresholdAmt, displayAmount: this.totalThresholdAmt });
                    this.handleAmtChangeDebounce(this.totalThresholdAmt);
                  }}
                >好的</MUButton>
              </MUView>
            </MUView>
          </MUModal>
          {/* 再分期办理提示 */}
          <MUModal
            className="restaging-modal"
            beaconId="RestagingRepayModel"
            isOpened={showRestagingModel}
            closeOnClickOverlay={false}
          >
            <MUView className="restaging-modal-container">
              <MUImage className="img" src="https://file.mucfc.com/ebn/3/0/202312/20231227152741411abe.png" />
              <MUView className="title">再分期资格已审批通过</MUView>
              <MUView className="desc">{amount && Number(amount) > 0 ? `需先还${amount}元到期欠款，还款成功后立即办理成功` : '您可继续完成再分期办理，是否确认办理？'}</MUView>
              <MUView className="btn" style={{ backgroundColor: themeColor }} onClick={this.restagingModalClose}>
                {amount && Number(amount) > 0 ? `去还款(${showRestagingModalSecond}s...)` : '继续办理'}
              </MUView>
              {amount && Number(amount) <= 0 && (
                <MUView
                  className="sub-btn"
                  onClick={() => {
                    dispatchTrackEvent({ event: EventTypes.BC, beaconId: 'RestagingRepayModelCancel', target: this });
                    this.setState({ showRestagingModel: false });
                    Madp.closeWebView().then().catch(() => {
                      Madp.navigateBack({ delta: window.history.length - 1 });
                    });
                  }}
                >暂不办理</MUView>
              )}
              <MUImage className={classNames('slogan', { 'slogan-plus': this.isVplus })} src={this.isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
            </MUView>
          </MUModal>
          {/* 延后还办理提示 */}
          <MUModal
            className="extend-modal"
            beaconId="ExtendRepayModel"
            isOpened={showExtendModel}
            closeOnClickOverlay={false}
          >
            <MUView className="extend-modal-container">
              <MUImage className="img" src="https://file.mucfc.com/ebn/3/0/202312/20231227152741411abe.png" />
              <MUView className="title">延后还资格已审批通过</MUView>
              <MUView className="desc">{`需先还清${amount}元到期欠款，还款成功后立即办理成功`}</MUView>
              <MUView className="btn" style={{ backgroundColor: themeColor }} onClick={this.extendModalClose}>{`去还款(${showExtendModalSecond}s...)`}</MUView>
              <MUImage className={classNames('slogan', { 'slogan-plus': this.isVplus })} src={this.isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
            </MUView>
          </MUModal>
          {/* 协商还办理提示 */}
          <MUModal
            className="consult-repay-modal"
            beaconId="ConsultRepayModel"
            isOpened={showConsultRepayModel}
            closeOnClickOverlay={false}
          >
            <MUView className="consult-repay-modal-container">
              <MUImage className="img" src="https://file.mucfc.com/ebn/3/0/202312/20231227152741411abe.png" />
              <MUView className="title">协商还资格已审批通过</MUView>
              <MUView className="desc">{`需先还清${(Number(this.immutableAmount || 0) - Number(this.consultPreWaiveAmt || 0)).toFixed(2)}元到期欠款，还款成功后立即办理成功`}</MUView>
              <MUView className="btn" style={{ backgroundColor: themeColor }} onClick={() => this.consultRepayModalClose(true)}>{`去还款${Number(showConsultRepayModalSecond) > 0 ? `(${showConsultRepayModalSecond}s)` : ''}`}</MUView>
            </MUView>
          </MUModal>
          {/* 协商还试算失败提示弹窗 */}
          <MUModal
            className="consult-repay-modal"
            beaconId="ConsultRepayModel"
            isOpened={showConsultErrorModel}
            closeOnClickOverlay={false}
          >
            <MUView className="consult-repay-modal-container">
              <MUImage className="img" src="https://file.mucfc.com/ebn/3/0/202312/20231227152741411abe.png" />
              <MUView className="desc">{consultRepayErrorMsg}</MUView>
              <MUView className="btn" style={{ backgroundColor: themeColor }} onClick={() => Madp.navigateBack()}>返回首页</MUView>
            </MUView>
          </MUModal>
          {/* 交互式运营组件 */}
          <OpRepayment pageId={expressRepayPageId} opEventKey="opRepayClick" />
          <OpRepayment pageId={expressRepayPageId} opEventKey="opPageLeave" />
          <OpRepayment pageId={expressRepayPageId} opEventKey="opPageStay" />
        </MUView>
      </MUView>
    );
  }
}
