/* eslint-disable max-len */
/* eslint-disable react/jsx-closing-bracket-location */
/* eslint-disable operator-linebreak */
/* eslint-disable no-nested-ternary */
import Taro, { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import {
  MU<PERSON>rawer, MUIcon, MUView, MURichText, MUText
} from '@mu/zui';
import Util from '@utils/maxin-util';
import { merchantNoList } from '@utils/constants';

export default class RepayDetailDrawer extends Component {
  static propTypes = {
    showRepayDetailDrawer: PropTypes.bool,
    repayTrialDetailList: PropTypes.array,
    courtCostBalance: PropTypes.string,
    insufficientRemainAmt: PropTypes.string,
    onClose: PropTypes.func,
  }

  static defaultProps = {
    showRepayDetailDrawer: false,
    repayTrialDetailList: [],
    courtCostBalance: '',
    insufficientRemainAmt: '',
    onClose: () => { },
  }

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  getAmoutDetail = (item) => {
    const {
      totalPayPrincipalAmt, loanType, totalPayInteAmt,
      totalPayPeriodFee, totalPayFineAmt, totalPayPrepayFee
    } = item || {};

    const needGrapStyle = this.needGrapStyle(item);
    const amountDetail = [`含本金<span style=${needGrapStyle ? '' : '"color: #000000"'}>${Number(totalPayPrincipalAmt || 0)}</span>元`];
    if (loanType === 'I') {
      amountDetail.push(`+利息<span style=${needGrapStyle ? '' : '"color: #000000"'}>${Number(totalPayInteAmt || 0)}</span>元`);
    } else {
      amountDetail.push(`+分期手续费<span style=${needGrapStyle ? '' : '"color: #000000"'}>${Number(totalPayPeriodFee || 0)}</span>元`);
    }
    if (Number(totalPayFineAmt || 0) > 0) {
      amountDetail.push(`+罚息<span style=${needGrapStyle ? '' : '"color: #000000"'}>${Number(totalPayFineAmt || 0)}</span>元`);
    }
    if (Number(totalPayPrepayFee || 0) > 0) {
      amountDetail.push(`+提前还款违约金<span style=${needGrapStyle ? '' : '"color: #000000"'}>${Number(totalPayPrepayFee || 0)}</span>元`);
    }

    return amountDetail.join('');
  }

  // 当还款金额、优惠金额、超限减免金额和为0时，需置灰展示
  needGrapStyle(item) {
    const { canPayAmt, canWaiveAmt, limitWaiveAmt } = item || {};
    return Number(Util.floatAdd(canPayAmt, Util.floatAdd(canWaiveAmt, limitWaiveAmt)) || 0) <= 0;
  }

  // 判断借据是否全部是招联的借据，如果是的，则不展示“来自XXXX”
  get isAllZL() {
    let isAllZL = true;
    const zlMerchantNo = ['10000', '10001'];
    const { repayTrialDetailList = [] } = this.props;
    const flattedBillList = (repayTrialDetailList || []).flat(2);
    flattedBillList.forEach((item) => {
      if (zlMerchantNo.indexOf(item.merchantNo) <= -1) {
        isAllZL = false;
      }
    });
    return isAllZL;
  }

  get sumDesc() {
    const { repayTrialDetailList, courtCostBalance } = this.props;
    if (Number(courtCostBalance || 0) > 0) {
      return `共<span style="color: #FF8844">${repayTrialDetailList.length}</span>笔，以及司法处置费<span style="color: #FF8844">${Number(courtCostBalance || 0).toFixed(2)}</span>元`;
    } else {
      return `共<span style="color: #FF8844">${repayTrialDetailList.length}</span>笔`;
    }
  }

  render() {
    const {
      showRepayDetailDrawer, onClose, repayTrialDetailList, insufficientRemainAmt
    } = this.props;

    return (<MUDrawer
      show={showRepayDetailDrawer}
      beaconId="RepayDetailDrawer"
      placement="bottom"
      height={Taro.pxTransform(1000)}
      mask
      onClose={onClose}
    >
      <MUView className="repay-detail">
        <MUView className="repay-detail__title">还款明细</MUView>
        <MUView className="repay-detail__close" onClick={onClose}>
          <MUIcon value="close2" size="20" color="#CACACA" />
        </MUView>
        <MUView className="repay-detail__sum"><MURichText nodes={this.sumDesc} /></MUView>
        <MUView className="repay-detail-content" style={{ height: Taro.pxTransform(1000 - 166) }}>
          {repayTrialDetailList && repayTrialDetailList.map((item) => (<MUView className="repay-detail-content-item">
            <MUView className="repay-detail-content-item-top">
              <MUView className={`repay-detail-content-item-top__amount ${this.needGrapStyle(item) ? 'grap' : ''}`}>还款金额{Number(item.canPayAmt || 0)}元</MUView>
              {Number(Util.floatAdd(item.canWaiveAmt, item.limitWaiveAmt) || 0) > 0 ?
                <MUView className="repay-detail-content-item-top__waive">
                  （已省<MUText className="repay-detail-content-item-top__waive--highlight">{Number(Util.floatAdd(item.canWaiveAmt, item.limitWaiveAmt) || 0)}</MUText>元）
                </MUView> : null}
            </MUView>
            <MUView className="repay-detail-content-item__detail"><MURichText nodes={this.getAmoutDetail(item)} /></MUView>
            {item.loanDate && item.businessType && item.installAmt ? (<MUView className="repay-detail-content-item__sub">
              {`${Util.getDateCollection(item.loanDate).join('-')} ${item.businessType}${item.installAmt}元`}
              {!this.isAllZL && item.merchantName && merchantNoList.indexOf(item.merchantNo) <= -1 && (
                <MUView className="repay-detail-content-item__sub--merchant">
                  来自{item.merchantName && item.merchantName.slice(0, 5)}
                </MUView>
              )}
            </MUView>) : null}
          </MUView>))}
          {Number(insufficientRemainAmt || 0) > 0 ? <MUView className="repay-detail-content-tips">温馨提示：本次还款将产生多余还款金额入小招荷包溢缴款，建议按账单金额还款，如有疑问可咨询客服95786</MUView> : null}
          <MUView className="repay-detail-content-placeholder" />
        </MUView>
      </MUView>
    </MUDrawer>);
  }
}
