/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import {
  MUView,
  MUModal,
  MUIcon,
  MUButton,
} from '@mu/zui';

/**
 * 失效拦截弹窗
 */
export default function InvalidityModal({
  isOpened,
  onConfirm,
  onCancel,
}) {
  return (
    <MUModal
      closeOnClickOverlay={false}
      beaconId="InvalidityModal"
      isOpened={isOpened}
      className="repay-coupon-dialog"
    >
      <MUView className="repay-coupon-dialog-content">
        <MUIcon
          value="tip"
          size={72}
          className="repay-coupon-dialog-content-icon"
        />
        <MUView className="repay-coupon-dialog-content-tip">
          温馨提示
        </MUView>
        <MUView className="repay-coupon-dialog-content-text">
          您的欠款信息有变更，请返回上页重新输入”我要还款“，或前往还款页面查询最新欠款信息
        </MUView>
        <MUButton
          type="primary"
          className="repay-coupon-dialog-content-confirm"
          beaconId="InvalidityModalConfirm"
          onClick={() => {
            onConfirm && onConfirm();
          }}
        >
          去还款
        </MUButton>
        <MUButton
          type="secondary"
          className="repay-coupon-dialog-content-cancel"
          beaconId="InvalidityModalCancel"
          onClick={() => {
            onCancel && onCancel();
          }}
        >
          返回上页
        </MUButton>
      </MUView>
    </MUModal>
  );
}
