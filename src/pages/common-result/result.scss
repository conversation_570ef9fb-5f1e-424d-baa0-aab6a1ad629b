@import '../../components/weapp/index.scss';

.common-result {
  height: 100vh;
  box-sizing: border-box;
  background-color: #fff;
  text-align: center;

  &-padding {
    padding: 98px 50px 50px;
  }

  &-order-padding {
    padding: 58px 30px;
  }

  .result-icon {
    width: 100%;
  }

  .result-title {
    margin-top: 40px;
    font-size: 40px;
    font-weight: bold;
    color: #333333;
  }

  .result-sub-title {
    margin-top: 36px;
    font-size: 28px;
    color: #888888;
  }

  .back-btn {
    margin-top: 50px;
    width: 360px;
    height: 88px;
    font-size: 35px;
  }

  .order-success-content {
    font-size: 28px;
    color: #888888;
    text-align: center;
    line-height: 42px;
    font-weight: 400;

    .order-text {
      color: #FF8800;
    }
  }

  .order-success-btns {
    display: flex;
    // padding: 0 30px;
    justify-content: space-between;

    .back-btn {
      margin-top: 50px;
      width: 330px;
      height: 88px;
      font-size: 36px;
      font-weight: 500;
    }

    .gobackPage-btn {
      background: #F3F3F3;
      margin-top: 50px;
      width: 330px;
      height: 88px;
      font-size: 36px;
      font-weight: 500;
    }
  }

}

.highlight {
  color: #FE5A5F;
}
.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}

.pages-slogan {
  margin-top: -96px !important;
}

.head-slogan-img {
  width: 172px;
  height: auto;
  margin: 0 auto;
  margin-bottom: 12px;
}

.pages-head {
  font-size: 0;
}

.slogan-padding {
  padding-top: 50px;
}

.extend-fail-bill-content {
  text-align: left;
}
