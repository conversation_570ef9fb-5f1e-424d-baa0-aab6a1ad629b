/* eslint-disable indent */
/* eslint-disable no-param-reassign */
/* eslint-disable arrow-body-style */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable react/sort-comp */
/* eslint-disable prefer-destructuring */
import Taro, { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUButton, MUIcon, MUText, MUNavBarWeapp, MUSlogan, MUImage
} from '@mu/zui';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import {
  isMuapp, isAlipay, isWechat, isUnicom
} from '@mu/madp-utils';
import { miniProgramChannel } from '@utils/constants';

import './result.scss';

@track({ event: EventTypes.PO }, {
  pageId: 'CommonResult',
  dispatchOnMount: true,
})
@pageHoc({ title: '结果' })
export default class CommonResult extends Component {
  config = {
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }
  constructor() {
    // eslint-disable-next-line prefer-rest-params
    super(...arguments);
    this.state = {
      type: '',
      resultCode: '',
      resultInfo: {},
      isAfter23: (new Date()).getHours() >= 23, // 产品要求，23点之后，预约还款提示时间需要从23时改为24时
    };
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
  }

  componentDidMount() {
    this.getResultInfo();
  }

  componentDidShow() {
    const { resultInfo } = this.state;
    if (resultInfo.navTitle) {
      Madp.setNavigationBarTitle({ title: resultInfo.navTitle });
    }
  }

  // 判断当前的webview是否在小程序环境中，已经在madp-util中了(改这里的时候它还没上生产)，20240110之后可以使用madp-util的isWebViewInMicroApp
  isWebViewInMicroApp() {
    if (process.env.TARO_ENV === 'h5') {
      const ua = navigator.userAgent.toLowerCase();
      // 抖音toutiaomicroapp 百度baiduboxapp
      return (
        /miniprogram/.test(ua)
        || /toutiaomicroapp/.test(ua)
        || /baiduboxapp/.test(ua)
      );
    }
    return false;
  }

  getResultInfo() {
    let { type, mainTitle = '', subTitle = '', resultCode } = this.$router.params;
    mainTitle = decodeURIComponent(mainTitle);
    subTitle = decodeURIComponent(subTitle);
    let info = {
      iconType: 'success',
      content: '提交成功',
      subContent: '',
      btnContext: '返回',
      customizedGoback: false,
    };
    switch (type) {
      case 'transfer-done': {
        info = {
          ...info,
          iconType: 'success',
          content: '还款成功',
          subContent: '还款成功，请前往“我的”页面查看交易记录',
          navTitle: '申诉结果'
        };
        break;
      }
      case 'transfer-pending': {
        info = {
          ...info,
          iconType: 'waiting',
          content: '申诉处理中',
          subContent: '预计1个小时处理完毕，如有疑问请联系客服',
          navTitle: '申诉结果'
        };
        break;
      }
      case 'transfer-cash-pending': {
        info = {
          ...info,
          iconType: 'waiting',
          content: '申诉处理中',
          subContent: '预计1-3个工作日处理完毕，如有疑问请联系客服',
          navTitle: '申诉结果'
        };
        break;
      }
      case 'transfer-unknown': {
        info = {
          ...info,
          iconType: 'tip',
          content: '未识别到还款',
          subContent: '请确认申请信息是否准确',
          navTitle: '申诉结果'
        };
        break;
      }
      case 'transfer-fail': {
        info = {
          ...info,
          iconType: 'warning',
          content: '还款异常',
          subContent: '存在还款异常，详情请咨询客服',
          navTitle: '申诉结果'
        };
        break;
      }
      case 'reserve-cancel': {
        info = {
          ...info,
          iconType: 'tip',
          content: '预约已取消',
          subContent: '线下转账还款，按默认规则还款'
        };
        break;
      }
      case 'reserve-done': {
        info = {
          ...info,
          iconType: 'success',
          content: '预约成功',
          navTitle: '提交结果'
        };
        break;
      }
      case 'fee-reduce-success': {
        info = {
          ...info,
          iconType: 'success',
          content: '还款及减免申请已提交',
          subContent: '处理结果请以短信通知为准',
          navTitle: '提交成功'
        };
        break;
      }
      case 'fee-reduce-fail': {
        info = {
          ...info,
          iconType: 'warning',
          content: '还款及减免处理失败',
          subContent: '详情请咨询客服',
          navTitle: '处理失败'
        };
        break;
      }
      case 'payday-modify': {
        info = {
          ...info,
          iconType: 'success',
          content: '还款日修改成功'
        };
        break;
      }
      case 'advanced-stage-success': {
        info = {
          ...info,
          iconType: 'success',
          content: '省心分提交成功',
          subContent: '请留意最新的账单信息',
        };
        break;
      }
      case 'advanced-stage-fail': {
        info = {
          ...info,
          iconType: 'warning',
          content: '省心分提交失败',
          subContent: '请重新提交或过段时间再试',
        };
        break;
      }
      case 'extend-fail': {
        if (resultCode) {
          dispatchTrackEvent({
            event: EventTypes.EV,
            beaconId: 'ExtendFail',
            target: this,
            beaconContent: { cus: { resultCode } }
          });
        } else {
          dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'ExtendFail', target: this });
        }
        info = {
          ...info,
          iconType: 'warning',
          content: mainTitle || '申请提交失败',
          subContent: subTitle || '',
        };
        break;
      }
      case 'extend-success': {
        info = {
          ...info,
          iconType: 'success',
          content: mainTitle || '申请提交成功',
          subContent: subTitle || '',
        };
        break;
      }
      case 'submitForm-success': {
        info = {
          ...info,
          iconType: 'success',
          content: '申请已提交办理',
          subContent: '办理结果将在三个工作日内以短信形式通知',
        };
        break;
      }
      case 'adjust-success': {
        info = {
          ...info,
          iconType: 'success',
          content: '提交成功',
          subContent: '系统处理中，请留意短信通知',
          navTitle: '延长诚信保护期',
          customizedGoback: () => {
            Util.router.push('/index');
          }
        };
        break;
      }
      case 'order-success': {
        info = {
          ...info,
          iconType: 'success',
          content: '预约成功',
          subContent: '',
          btnContext: '预约详情',
          navTitle: '提交结果',
          customizedGoback: () => {
            dispatchTrackEvent({ target: this, event: EventTypes.CK, beaconId: 'CheckTransferInfoDetail' });
            if (this.miniChannelFlag) {
              Taro.navigateTo({
                url: '/pages/transfer/guide'
              });
            } else {
              Util.router.replace('/pages/transfer/guide');
            }
          }
        };
        break;
      }
      case 'collect-success': {
        info = {
          ...info,
          iconType: 'success',
          content: '提交成功',
          subContent: '审核结果将通过短信发送，请留意通知',
          btnContext: '返回',
          navTitle: '贷后服务关怀申请',
          customizedGoback: () => {
            dispatchTrackEvent({ target: this, event: EventTypes.CK, beaconId: 'CollectSuccessBack' });
            if (process.env.TARO_ENV === 'h5') {
              if (isMuapp() || isAlipay() || isWechat() || isUnicom()) {
                try {
                  Madp.closeWebView();
                } catch (e) {
                  wx.miniProgram.navigateBack({ delta: 1 });
                }
              } else if (this.isWebViewInMicroApp()) {
                Madp.miniProgram.reLaunch({
                  url: '/repayment/pages/index/index'
                });
              } else {
                Madp.navigateBack({
                  delta: 1
                });
              }
            }
          }
        };
        break;
      }
      case 'collect-fail': {
        info = {
          ...info,
          iconType: 'warning',
          content: '提交失败',
          subContent: '抱歉，材料提交失败，请稍后再试',
          btnContext: '返回',
          navTitle: '贷后服务关怀申请',
          customizedGoback: () => {
            dispatchTrackEvent({ target: this, event: EventTypes.CK, beaconId: 'CollectFailBack' });
            if (process.env.TARO_ENV === 'h5') {
              if (isMuapp() || isAlipay() || isWechat() || isUnicom()) {
                Madp.closeWebView();
              } else if (this.isWebViewInMicroApp()) {
                Madp.miniProgram.reLaunch({
                  url: '/repayment/pages/index/index'
                });
              } else {
                Madp.navigateBack({
                  delta: 1
                });
              }
            }
          }
        };
        break;
      }
      default: { break; }
    }
    this.setState({ resultInfo: info, type, resultCode });
    if (info.navTitle) {
      Madp.setNavigationBarTitle({ title: info.navTitle });
    }
  }

  goback() {
    const { resultInfo } = this.state;
    const ret = Util.checkFinishAction();
    if (resultInfo.customizedGoback && typeof resultInfo.customizedGoback === 'function') {
      resultInfo.customizedGoback();
    } else if (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'alipay') {
      // 返回小程序首页，避免compose的时候路由前面加上模块名
      const pages = 'pages';
      Util.router.replace(`/${pages}/index/index`);
    } else if (this.isWebViewInMicroApp()) {
      const pages = 'pages';
      Madp.miniProgram.reLaunch({
        url: `/${pages}/index/index`
      });
    } else if (ret.redirect) {
      Util.router.replace(ret.redirect);
    } else if (Madp.getChannel() === '3WYDAPP') {
      Util.router.replace('/pages/index/index');
    } else {
      Madp.closeWebView();
    }
  }

  // 结果页面跳去未来期账单页面，放在这里
  async goToBill() {
    this.gobackPage();
  }

  gobackPage() {
     Util.router.replace('/pages/index/index?needBack=1');
  }

  render() {
    const { isAfter23, type, resultInfo, resultCode } = this.state;
    /** 小程序原生涉及到jsx的内容要放在render里面 */
    let subContent = null;
    let iconSize = 100;
    const useResultInfo = type === 'submitForm-success'
      || type === 'adjust-success'
      || type === 'collect-success'
      || type === 'collect-fail'
      || (type === 'extend-fail' && resultCode !== 'UMDP02427')
      || type === 'extend-success';
    const typeFlag = type === 'submitForm-success' || type === 'reserve-done'
      || type === 'payday-modify' || type === 'order-success' || type === 'adjust-success'
      || type === 'collect-success' || type === 'collect-fail' || type === 'extend-fail'
      || type === 'extend-success';
    const sloganFlag = type === 'payday-modify' || type === 'advanced-stage-success'
      || type === 'adjust-success' || type === 'extend-success';
    if (type === 'reserve-done') {
      subContent = <MUView>请于<span className="highlight">当日</span>完成转账。<MUView>过期预约失效，将按默认转账还款规则还款</MUView></MUView>;
    } else if (type === 'payday-modify') {
      subContent = (
        <MUView>
          请留意最新的
          <MUText
            className="brand-text"
            beaconId="paydayModifyResultGoBill"
            onClick={() => this.goToBill()}
          >
            待还账单
          </MUText>
        </MUView>
      );
    } else if (type === 'order-success') {
      dispatchTrackEvent({ target: this, event: EventTypes.PO, beaconId: 'OrderSuccessResult' });
      subContent = (
        <MUView className="order-success-content">
          <MUView>请于<MUText className="order-text">今日{isAfter23 ? 24 : 23}:00</MUText>前完成转账</MUView>
          <MUView>过期预约失效，将按我司默认还款顺序还款</MUView>
          <MUView>如需查看或取消预约，请点击前往预约详情</MUView>
        </MUView>
      );
      iconSize = 70;
    }
    if (type === 'extend-fail') {
      if (resultCode === 'UMDP02427') { // 为了样式对这种情况特殊处理
        subContent = (
          <MUView>
            <MUView>很抱歉，您暂无可办理延期的借据，可能原因如下</MUView>
            <MUView>1、借款逾期天数超过办理限制</MUView>
            <MUView>2、按月计息方式的借款不支持延期还款</MUView>
            <MUView>3、借款已过最后一期应还款日</MUView>
            <MUView>4、借款已办理过相关延期服务</MUView>
          </MUView>
        );
      }
    }
    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="结果"
          leftArea={[
            {
              type: 'icon',
              value: 'back'
            }
          ]}
        />
        <MUView className={`pages-bg common-result common-result${type === 'order-success' ? '-order' : ''}-padding ${sloganFlag ? 'slogan-padding' : ''}`}>
          <MUView className="pages-head">
            {sloganFlag ? <MUImage className="head-slogan-img" mode="widthFix" src="https://file.mucfc.com/bos/3/0/202304/20230427114002edca83.png" /> : null}
            <MUView className="result-icon">
              <MUIcon value={resultInfo.iconType} size={iconSize} />
            </MUView>
          </MUView>
          <MUView className="result-title">{resultInfo.content}</MUView>
          {typeFlag ? <MUView className={`result-sub-title ${type === 'extend-fail' ? 'extend-fail-bill-content' : ''}`}>
            {useResultInfo ? resultInfo.subContent : subContent}</MUView> : null}
          <MUView className={type === 'order-success' ? 'order-success-btns' : ''}>
            {type === 'order-success' ? (
              <MUButton type="secondary" className="gobackPage-btn" beaconId="BackBtn" onClick={() => this.gobackPage()}>
                返回
              </MUButton>
            ) : null}
            <MUButton className="back-btn" beaconId="BackBtn" onClick={() => this.goback()} type="primary">
              {resultInfo.btnContext}
            </MUButton>
          </MUView>
        </MUView>
        {sloganFlag ? <MUSlogan className="pages-slogan" onlyLogo /> : null}
      </MUView>
    );
  }
}
