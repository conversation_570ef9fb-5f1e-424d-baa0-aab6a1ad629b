/* eslint-disable max-len */
/* eslint-disable eqeqeq */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable import/first */
import { Component } from '@tarojs/taro';
import { MUView, MURichText, MUNavBar } from '@mu/zui';
import Madp from '@mu/madp';
import { track } from '@mu/madp-track';
import { isCmb } from '@mu/madp-utils';
import './index.scss';
import './contract.scss';
import Dispatch from '@api/actions';
import contractApi from './contract_utils/contractApi';
import { STORE } from './store';
import getCurrentDateTimeInFormat from './utils';
@track({}, {
  pageId: 'ProofPreviewPage',
  dispatchOnMount: true,
})

export default class ProofPreviewPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loanNo: '',
      htmlContents: ''
    };
  }

  componentWillMount() {
    const { loanNo } = this.$router.params;
    this.setState({ loanNo });
  }

  async componentDidMount() {
    // 获取校验接口请求的合同类型
    let contractCode = '';
    const { type } = this.$router.params;
    if (type === 'settle') {
      contractCode = STORE.SettleProofContractCode;
    } else {
      contractCode = STORE.LoanProofContractCode;
    }
    const contractPreviewData = this.contractPreviewDataHandle();

    const { ret, data } = await Dispatch.repayment.queryContractInfo({
      scene: 'PREVIEW',
      interfaceVersion: '3.0',
      contractCode,
      contractPreviewData,
    }) || {};
    const { contractList } = data || {};
    if (ret === '0') {
      const htmlTemplate = await contractApi.handleResult(contractList && contractList[0], {}, {});
      const htmlFile = await contractApi.getHtmlBodyContent(htmlTemplate);
      this.setState({
        htmlContents: htmlFile
      });
    }
  }


  /* 合同预览接口入参处理
   * @returns
   *{
   *  markedPriceInfo: { loanList: []},
   *  pccLoanInfo: {},
   *  baseContractInfo:  {}
   *}
   */
  contractPreviewDataHandle = () => {
    const { selectSnow: snowInfo = {} } = STORE;
    const { subSettLoanInfoList = [], acctType, snowballNo, loanDate, busiType, loanAmt, acctBalance, acctCredLine } = snowInfo;
    const { date, dateTime } = getCurrentDateTimeInFormat();

    let result = {
      pccLoanInfo: {
        pccLoanNo: snowballNo,
        pccAcctType: acctType,
        pccLoanDate: this.removeHyphensFromDate(loanDate),
        pccLoanStatus: busiType,
        pccAmount: loanAmt,
        pccActivationDate: this.removeHyphensFromDate(loanDate),
        pccLoanBalance: acctBalance,
        pccAcctCredLine: acctCredLine
      },
      baseContractInfo: {
        signDate: date,
        dateNow: dateTime
      }
    };

    if (acctType === 'R4') {
      // 有已结清的才传
      result.markedPriceInfo = {
        loanList: ((subSettLoanInfoList || []).filter(subIousInfo => subIousInfo && subIousInfo.settleDate)).map(subIousInfo => ({
          loanNo: subIousInfo.loanNo,
          period: subIousInfo.installTotalCnt,
          loanStatus: subIousInfo.loanStatus,
          amount: subIousInfo.loanAmt,
          loanDate: this.removeHyphensFromDate(subIousInfo.loanDate),
          settleDate: this.removeHyphensFromDate(subIousInfo.settleDate)
        }))
      };
    }
    return result;
  }


   removeHyphensFromDate = (date) => {
     if (typeof date !== 'string') {
       return;
     }
     return date.replace(/-/g, '');
   }

   componentDidShow() {
     const { type } = this.$router.params;
     if (type === 'settle') {
       Madp.setNavigationBarTitle({ title: '结清证明预览' });
     } else {
       Madp.setNavigationBarTitle({ title: '贷款证明预览' });
     }
   }

   render() {
     const { htmlContents, type } = this.state;
     let naviBar;
     if (isCmb()) {
       naviBar = (
         <MUNavBar
           beaconId="NavBar"
           title={type === 'settle' ? '贷款结清证明' : '贷款证明'}
           leftArea={[
             {
               type: 'icon',
               value: 'back',
             }
           ]}
         />
       );
     }
     return (
       <MUView className="proofPreviewPage">
         {naviBar}
         <MURichText nodes={htmlContents} />
       </MUView>
     );
   }
}
