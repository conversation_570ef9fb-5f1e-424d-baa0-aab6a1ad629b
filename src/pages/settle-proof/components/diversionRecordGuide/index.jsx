import { Component } from '@tarojs/taro';
import {
  MUView, MUImage
} from '@mu/zui';
import PropTypes from 'prop-types';
import Madp from '@mu/madp';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('./index.scss');
}

export default class DiversionRecordGuide extends Component {
  static propTypes = {
    diversionRecordGuideInfo: PropTypes.object,
    single: PropTypes.bool,
    trackPageId: PropTypes.string,
  }

  static defaultProps = {
    diversionRecordGuideInfo: {},
    single: false,
    trackPageId: '',
  };

  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  constructor(props) {
    super(props);
  }

  componentDidMount() {
    const { trackPageId, diversionRecordGuideInfo } = this.props;
    const { merchantNo, merchantShortName } = diversionRecordGuideInfo || {};
    dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPageId}DiversionRecordGuide`, beaconContent: { cus: { merchantNo, merchantShortName } } });
  }

  launchCall = () => {
    const { diversionRecordGuideInfo, trackPageId } = this.props;
    const { merchantNo, merchantShortName, merchantPhoneNo } = diversionRecordGuideInfo || {};
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPageId}DiversionRecordClick`, beaconContent: { cus: { merchantNo, merchantShortName, merchantPhoneNo } } });
    Madp.makePhoneCall({
      phoneNumber: merchantPhoneNo,
    });
  }

  render() {
    const { diversionRecordGuideInfo, single } = this.props;
    const { logoUrl, productName, merchantShortName } = diversionRecordGuideInfo || {};

    return (
      <MUView className={`diversion-record-guide${single ? ' diversion-record-guide--single' : ''}`}>
        <MUView className="diversion-record-guide__name">
          <MUView className="diversion-record-guide__name__img">
            <MUImage src={logoUrl} />
          </MUView>
          <MUView className="diversion-record-guide__name__text">{productName}</MUView>
        </MUView>
        <MUView className="diversion-record-guide__contact" onClick={this.launchCall}>
          <MUView className="diversion-record-guide__contact__text">联系{merchantShortName}客服</MUView>
        </MUView>
      </MUView>
    );
  }
}
