/* eslint-disable eqeqeq */
/* eslint-disable import/first */
import { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import {
  MUView, MUImage, MUButton
} from '@mu/zui';
import './index.scss';
import selected from '../../imgs/selected.png';
import unselected from '../../imgs/unselected.png';

const propTypes = {
  isSelectedAll: PropTypes.bool.isRequired,
  selectedItemAmount: PropTypes.number.isRequired,
  onClick: PropTypes.func.isRequired,
  clickRadio: PropTypes.func.isRequired,
  type: PropTypes.string.isRequired,
};

export default class SettleIOUPageBottom extends Component {
  constructor(props) {
    super(props);
    PropTypes.checkPropTypes(propTypes, props, 'prop', 'SettleIOUPageBottom');
  }

  componentDidMount() {
  }

  handleChange() {
    const { clickRadio } = this.props;
    clickRadio();
  }

  render() {
    const {
      selectedItemAmount, isSelectedAll, type, onClick
    } = this.props;

    return (
      <MUView className="SettleIOUPageBottom">
        <MUImage
          className="leftImage"
          beaconId="bottomImg"
          src={isSelectedAll ? selected : unselected}
          onClick={this.handleChange.bind(this)}
        />
        <MUView className="center-content">
          <MUView className="allIn">全选</MUView>
          <MUView className="secondary-desc">{`已选${selectedItemAmount}条${type == 0 ? '结清证明' : '贷款证明'}`}</MUView>
        </MUView>
        <MUButton
          className="rightButton"
          beaconId="bottomBut"
          disabled={selectedItemAmount === 0}
          onClick={onClick}
        >
          去开具
        </MUButton>
      </MUView>
    );
  }
}

SettleIOUPageBottom.propTypes = propTypes;
