.SettleIOUPageBottom {
  position: fixed;
  bottom: 0;
  background-color: #fff;
  height: 120px;
  width: 100%;
  display: flex;

  .leftImage {
    width: 40px;
    height: 40px;
    margin: auto 20px auto 30px;
  }

  .center-content {
    flex: 1;
    margin: auto 0;

    .allIn {
      font-size: 32px;
    }

    .secondary-desc {
      font-size: 22px;
      color: #808080;
    }
  }

  .rightButton {
    width: 180px;
    height: 120px;
    background-color: #3477ff;
    color: #fff;
    font-size: 30px;

    &.at-button {
      border-radius: 0;
    }
  }
}
