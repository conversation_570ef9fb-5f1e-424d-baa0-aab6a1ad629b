import { Component } from '@tarojs/taro';
import { M<PERSON><PERSON>iew, MUText, MUImage, MUButton ,MUModal} from '@mu/zui';
import PropTypes from 'prop-types';
import warningIcon from '../imgs/icon_transfer_prof.png';
import './TransferModal.scss';
import Madp from '@mu/madp';
import { urlDomain } from '@utils/url_config';

export default class TransferModal extends Component {
  static propTypes = {
    visible: PropTypes.bool,
    onClose: PropTypes.func,
    phoneNumber: PropTypes.string,
    transferOrgName: PropTypes.string
  }

  static defaultProps = {
    visible: false,
    phoneNumber: '',
    transferOrgName: '',
    onClose: () => {}
  }

  constructor(props) {
    super(props);
    this.state = {
      isVisible: props.visible
    };
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.visible !== nextProps.visible) {
      this.setState({
        isVisible: nextProps.visible
      });
    }
  }

  handleClose = () => {
    this.setState({
      isVisible: false
    }, () => {
      this.props.onClose();
    });
  }

  handleCallService = () => {
    if (Madp.getChannel() === '3APP') {
      return;
    }
    Madp.makePhoneCall({ phoneNumber: this.props.phoneNumber });
  }

  handleCustService = () => {
    this.setState({
      isVisible: false
    }, () => {
      this.props.onClose();
      Madp.navigateTo({
        url: `${urlDomain}/${Madp.getChannel()}/csp/#/?busiEntrance=AMCZRKH&_needLogin=1`
      });
    });
  }

  render() {
    const { isVisible } = this.state;
    const { phoneNumber,transferOrgName } = this.props;

    if (!isVisible) return null;

    return (
        <MUModal
        isOpened={isVisible}
        closeOnClickOverlay={false}
        beaconId="TransferNoticeModal"
      >
        <MUView className='modal-container'>
          <MUImage 
            className='warning-icon'
            src={warningIcon}
            mode='aspectFit'
          />
          
          <MUView className='modal-title'>
            不支持开具
          </MUView>
          
          <MUView className='modal-content'>
            您的借据中包含"已转让"的借据，在招联不支持开具结清证明。如您已结清欠款，请联系{transferOrgName}（电话
            <MUText className='phone-number' onClick={this.handleCallService} beaconId="TransferModalPhoneNumber">
              {phoneNumber}
            </MUText>
            ）开具结清证明
          </MUView>

          <MUButton
            className='confirm-btn'
            onClick={this.handleClose}
            beaconId="TransferModalConfirm"
          >
            我知道了
          </MUButton>

          <MUText
            className='service-link'
            onClick={this.handleCustService}
            beaconId="TransferModalCustomerService"
          >
            咨询人工客服
          </MUText>
        </MUView>
      </MUModal>
    );
  }
} 