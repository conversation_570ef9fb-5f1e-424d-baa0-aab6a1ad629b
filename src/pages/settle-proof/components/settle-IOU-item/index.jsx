/* eslint-disable eqeqeq */
import { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import {
  MUView, MUImage
} from '@mu/zui';
import Madp from '@mu/madp';
import './index.scss';
import selected from '../../imgs/selected.png';
import unselected from '../../imgs//unselected.png';

const propTypes = {
  date: PropTypes.string.isRequired,
  money: PropTypes.string.isRequired,
  isSelected: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
  clickRadio: PropTypes.func.isRequired,
  type: PropTypes.string.isRequired,
  grayed: PropTypes.string.isRequired,
  invalidReasonCode: PropTypes.string.isRequired,
  transferAction: PropTypes.func.isRequired//触发资产转让状态时，不可开立的场景进行弹窗
};

export default class SettleIOUItem extends Component {
  constructor(props) {
    super(props);
    PropTypes.checkPropTypes(propTypes, props, 'prop', 'SettleIOUItem');
  }

  componentDidMount() {
  }

  handleChange() {
    const { clickRadio, grayed } = this.props;
    if (!grayed) {
      clickRadio();
    }
  }

  clickView() {
    const { grayed ,invalidReasonCode, transferAction} = this.props;
    if (invalidReasonCode && invalidReasonCode === "03") {
      transferAction();
    }else{
      if (grayed) {
        Madp.showModal({
          content: '暂不支持开具，如有疑问可联系人工客服95786',
          confirmText: '我知道了',
          confirmColor: '#3477ff',
          showCancel: false,
        });
      }
    }
  }

  goPreview() {
    const { onClick, grayed } = this.props;
    if (!grayed) {
      onClick();
    }
  }

  render() {
    const {
      date, money, isSelected, type, grayed
    } = this.props;
    return (
      <MUView className={`SettleIOUItem ${type == 0 ? '' : 'SettleIOUItem_withoutDate'} ${grayed ? 'SettleIOUItem_gray' : ''}`} beaconId="itemCheck" onClick={this.clickView.bind(this)}>
        <MUImage
          className="leftImage"
          beaconId="itemImg"
          src={isSelected ? selected : unselected}
          onClick={this.handleChange.bind(this)}
        />
        <MUView className="SettleIOUItem-content">
          <MUView className="SettleIOUItem-content_top">
            <MUView className="centerArea">
              <MUView className="description">人行账户标识</MUView>
              <MUView className="money">{money}</MUView>
            </MUView>
            <MUView className="rightButton" beaconId="itemPre" onClick={this.goPreview.bind(this)}>预览</MUView>
          </MUView>
          {type == 0
         && (
           <MUView className="SettleIOUItem-content_bottom">
             {/* <hr className="underLine" /> */}
             <MUView className="date">{`结清时间:${date}`}</MUView>
           </MUView>
         )}
        </MUView>
      </MUView>
    );
  }
}


SettleIOUItem.propTypes = propTypes;
