.SettleIOUItem {
  background-color: #fff;
  margin: 20px 20px 0;
  border-radius: 6px;
  height: 200px;
  display: flex;

  .leftImage {
    width: 40px;
    height: 40px;
    margin: 80px 30px;
  }

  &-content {
    width: 610px;

    &_top {
      height: 140px;
      width: 100%;
      position: relative;

      .centerArea {
        padding: 50px 0 10px;

        .description {
          font-size: 24px;
          color: #A6A6A6;
          line-height: 24px;
        }

        .money {
          margin-top: 20px;
          font-size: 22px;
          color: #333333;
          line-height: 36px;
        }

      }

      .rightButton {
        border: 2px solid #3477FF;
        border-radius: 30px;
        width: 120px;
        height: 60px;
        position: absolute;
        right: 20px;
        top: 60px;
        // transform: translateY(-50%);
        font-size: 26px;
        color: #3477FF;
        text-align: center;
        line-height: 60px;
      }
    }

    &_bottom {
      height: 60px;

      .date {
        height: 100%;
        margin: auto 0;
        font-size: 24px;
        color: #A6A6A6;
        line-height: 55px;
      }

      .underLine {
        color: #E6EBF0;
      }
    }

  }

  &_withoutDate {
    height: 140px;

    .leftImage {
      margin: 50px 30px;
    }

    .content_top {

      .centerArea {
        padding: 30px 0;
      }

      .rightButton {
        top: 40px;
      }

    }
  }

  &_gray {
    .SettleIOUItem-content_top .centerArea .money {
      color: #A6A6A6;
    }

    .SettleIOUItem-content_top .rightButton {
      border-color: #A6A6A6;
      color: #A6A6A6;
    }
  }
}
