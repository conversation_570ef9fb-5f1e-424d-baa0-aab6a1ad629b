/* eslint-disable eqeqeq */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import {
  MUView, MUButton, MUInput, MUDialog, MUNavBar
} from '@mu/zui';
import Madp from '@mu/madp';
import Dispatch from '@api/actions';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { Validator, isMuapp, isAlipay, isWechat, isCmb } from '@mu/madp-utils';
import { ChatEntry } from '@mu/chat-entry-component';
import './index.scss';

const isH5 = process.env.TARO_ENV === 'h5';
@track({}, {
  pageId: 'VerifyEmailPage',
  dispatchOnMount: true,
})

export default class VerifyEmailPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      popupShow: false,
      email: ''
    };
    this.loanNoList = [];
    this.type = '';
    this.redirectUrl = '';
    this.isChatChn = false; // 是否显示客服入口
    if (!isH5 || isAlipay() || isMuapp() || isWechat()) {
      this.isChatChn = true;
    }
  }

  componentDidMount() {
    const { loanNoList, showType, redirectUrl, email: lastEmail } = this.$router.params;
    this.redirectUrl = redirectUrl;
    if (loanNoList) {
      const loanNoListString = decodeURIComponent(loanNoList);
      this.loanNoList = JSON.parse(loanNoListString);
    }
    this.type = showType;
    // 从localStorage获取上次使用的邮箱
    if (lastEmail) {
      this.setState({
        email: decodeURIComponent(lastEmail)
      });
    }
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '发送证明' });
  }

  // 更新邮箱
  emailhandleChange(value) {
    this.setState({
      email: value
    });
  }

  // 检查邮箱格式
  checkInfo() {
    const { email } = this.state;
    const emailValue = email.trim();
    if (emailValue && emailValue.length > 32) {
      Madp.showToast({
        title: '请填写地址长度不超过32个字符的邮箱',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    if (!Validator.isEmail(emailValue)) {
      Madp.showToast({
        title: '请输入正确的邮箱',
        icon: 'none',
        duration: 2000
      });
      return false;
    }
    dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'applySettleSubButClick', target: this });
    this.setState({
      popupShow: true
    });
  }

  close() {
    this.setState({
      popupShow: false
    });
  }

  async sure() {
    const { pageType } = this.$router.params;
    let applySettleType = '';
    if (this.type === '2') { // 无贷款证明参数
      applySettleType = 'NO_LOAN_PROOF';
    } else { // 0-结清证明；1-贷款证明
      applySettleType = 'LOAN_PROOF';
    }
    const { email } = this.state;
    const emailTrim = email ? email.trim() : '';

    const { ret, errCode } = await Dispatch.repayment.settlementOrNonLoanCertApply({
      applySettleType,
      email: emailTrim,
      loanNoList: this.loanNoList
    }) || {};
    if (ret === '0') {
      if (errCode === 'COM00000') {
        dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'applySettleSubButSucessClick', target: this });
      } else {
        dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'applySettleSubButfailClick', target: this });
      }
      Madp.navigateTo({
        url: `/pages/settle-proof/SettleResultPage?showType=${this.type}&email=${emailTrim}&pageType=${pageType}&result=success&redirectUrl=${this.redirectUrl}`
      });
      return;
    }
    Madp.navigateTo({
      url: `/pages/settle-proof/SettleResultPage?showType=${this.type}&email=${email}&pageType=${pageType}&redirectUrl=${this.redirectUrl}&result=fail`
    });
  }

  render() {
    const { popupShow, email } = this.state;
    let naviBar;
    if (isCmb()) {
      naviBar = (
        <MUNavBar
          beaconId="NavBar"
          title="发送证明"
          leftArea={[
            {
              type: 'icon',
              value: 'back',
            }
          ]}
        />
      );
    }
    return (
      <MUView className="verifyEmailPage">
        {naviBar}
        <MUView className="lead-tip">请填写正确的接收邮箱地址</MUView>
        <MUInput
          beaconId="email-input"
          className="email_input"
          type="text"
          value={email}
          onChange={this.emailhandleChange.bind(this)}
        />
        <MUButton beaconId="sendButton" className="sendButton" type="primary" onClick={this.checkInfo.bind(this)}>
          发送
        </MUButton>
        {this.isChatChn && (
          <ChatEntry
            extraParam={{ needLogin: 1 }}
            busiEntrance={isH5 ? '' : encodeURIComponent('repayment/#/pages/settle-proof/VerifyEmailPage')}
          />
        )}
        <MUDialog
          title="确认邮箱地址"
          beaconId="popup"
          isOpened={popupShow}
          onClose={this.close.bind(this)}
          buttons={[
            {
              label: '修改邮箱',
              onClick: this.close.bind(this)
            },
            {
              label: '确认无误',
              onClick: this.sure.bind(this)
            }
          ]}
        >
          {email}
        </MUDialog>
      </MUView>
    );
  }
}
