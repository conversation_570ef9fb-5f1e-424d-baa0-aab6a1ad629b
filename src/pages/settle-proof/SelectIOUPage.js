/* eslint-disable max-len */
/* eslint-disable react/no-access-state-in-setstate */
/* eslint-disable import/first */
/* eslint-disable eqeqeq */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import {
  MUView, MUText, MUPullToRefresh, MUImage, MUNavBar
} from '@mu/zui';
import Madp from '@mu/madp';
import { track } from '@mu/madp-track';
import { ChatEntry } from '@mu/chat-entry-component';
import { isMuapp, isAlipay, isWechat, isCmb } from '@mu/madp-utils';
import './index.scss';
import Dispatch from '@api/actions';
import noRecordImg from './imgs/no_record_img.png';
import SettleIOUItem from './components/settle-IOU-item';
import SettleIOUPageBottom from './components/settle-IOU-page-bottom';
import { STORE } from './store';
import TransferModal from './components/TransferModal.jsx';
import RepaymentGlobalStore from '@repayment/store/rootStore';
import { inject } from '@tarojs/mobx';
const pageSize = 10;
const isH5 = process.env.TARO_ENV === 'h5';


@track({}, {
  pageId: 'SelectIOUPage',
  dispatchOnMount: true,
})
@inject(() => ({ repaymentGlobalStore: new RepaymentGlobalStore() }))

export default class SelectIOUPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      itemList: [], // 展示项数组
      noMoreFlag: true, // 还有没有更多记录
      selectedAll: false, // 底部导航栏是否选中
      selectedAmount: 0,
      itemSelected: [], // 每一项是否被选中
      hasSettle: true, // 是否有结清雪球
      hasLoan: false, // 是否有贷款借据
      show: '0', // 0展示结清证明，1展示贷款证明
      showTransferModal: false, // 展示资产转让弹窗
    };
    this.redirectUrl = '';
    this.type = '';
    this.totalList = [];
    this.isChatChn = false; // 是否显示客服入口
    if (!isH5 || isAlipay() || isMuapp() || isWechat()) {
      this.isChatChn = true;
    }
    this.indexStore = props.repaymentGlobalStore.indexStore;
  }

  async componentDidMount() {
    // type代表用户有没有结清雪球或贷款借据，show代表页面展示的是什么证明
    const { type, show, redirectUrl } = this.$router.params;
    this.redirectUrl = redirectUrl;
    this.type = type;
    this.setState({ show }, () => {
      if (type) {
        switch (type) {
          case 'loanProof':
            this.setState({
              hasSettle: false,
              hasLoan: true
            });
            break;
          case 'settleAndLoanProof':
            this.setState({
              hasSettle: true,
              hasLoan: true
            });
            break;
          case 'settleProof':
            this.setState({
              hasSettle: true,
              hasLoan: false
            });
            break;
          default:
            this.setState({
              hasSettle: false,
              hasLoan: false
            });
        }
      }
      // 只有在没有展示的是结清证明列表且没有结清证明的情况下不调用接口
      if (this.state.show != 0 || this.state.hasSettle) {
        this.askList();
      }

      if (this.state.show == 0) {
        Madp.setNavigationBarTitle({ title: '结清证明' });
      } else {
        Madp.setNavigationBarTitle({ title: '贷款证明' });
      }
    });
  }

  componentDidShow() {
    const { show } = this.$router.params;
    if (show == 0) {
      Madp.setNavigationBarTitle({ title: '结清证明' });
    } else {
      Madp.setNavigationBarTitle({ title: '贷款证明' });
    }
  }

  askList = async () => {
    const scene = this.state.show == 0 ? 'SETTLE' : 'LOAN';
    const { ret, data } = await Dispatch.repayment.settlementOrNonLoanCertApplyQuerySnowballList({ scene }) || {};
    if (ret === '0') {
      const { settLoanInfoList } = data || {};
      this.totalList = settLoanInfoList;
      const hasInvalidItem = settLoanInfoList.some(item =>
        item.invalidReasonCode === '03'
        && item.valid === false
      );
      // 判断返回的数据列表中有满足资产转让的子项那就请求资产转让详情接口，获取机构名称和联系电话
      if (hasInvalidItem) {
        await this.indexStore.getTransferDetail();
        this.transferInfo = this.indexStore.assetTransferInfo || [];
      }
      this.requestList();
    }
  }

  async requestList() {
    if (this.totalList.length > this.state.itemList.length + pageSize) {
      this.setState({ noMoreFlag: false });
    } else {
      this.setState({ noMoreFlag: true });
    }
    const tempList = this.state.itemList.concat(this.totalList.slice(this.state.itemList.length, this.state.itemList.length + pageSize)) || [];
    this.setState({
      itemList: tempList
    }, () => {
      let tempSelected = new Array(tempList.length).fill(false);
      this.setState({
        itemSelected: this.state.itemSelected.concat(tempSelected)
      }, () => {
        // 赋值后释放
        tempSelected = null;
      });
    });
  }

  preview(item, index) {
    // 存储当前选择预览的雪球信息
    STORE.selectSnow = item;
    Madp.navigateTo({
      url: `/pages/settle-proof/ProofPreviewPage?type=${this.state.show == 0 ? 'settle' : 'loan'}&loanNo=${this.state.itemList[index].snowballNo}`
    });
  }

  // 获取结清项或贷款项(有点恶心的代码，必须要先把MUPullToRefresh组件隐藏，要不会一直有那个加载中的文本，这次需求紧，下次有空让平台组的同事修复下)
  getRecords = () => {
    if (!this.state.noMoreFlag) {
      this.requestList();
      this.setState({ noMoreFlag: true }, () => {
        if (this.totalList.length > this.state.itemList.length) {
          this.setState({ noMoreFlag: false });
        } else {
          this.setState({ noMoreFlag: true });
        }
      });
    }
  }

  goNext() {
    const { email = '' } = this.$router.params;
    const selectedLoanNo = [];
    const tempSelected = this.state.itemSelected;
    const tempList = this.state.itemList;
    tempSelected.forEach((item, index) => {
      if (item === true) {
        selectedLoanNo.push(tempList[index].snowballNo);
      }
    });
    const jsonString = JSON.stringify(selectedLoanNo);
    Madp.navigateTo({
      url: `/pages/settle-proof/VerifyEmailPage?loanNoList=${encodeURIComponent(jsonString)}&showType=${this.state.show}&pageType=${this.type}&email=${email}&redirectUrl=${this.redirectUrl}`,
    });
  }

  gotoLoanRecoed() {
    Madp.navigateTo({
      url: `/pages/settle-proof/SelectIOUPage?type=${this.type}&show=1&redirectUrl=${this.redirectUrl}`,
    });
  }

  // 底部导航栏点击选中
  bottomSelectedAll() {
    this.setState({
      selectedAll: !this.state.selectedAll
    }, () => {
      let temp;
      if (this.state.selectedAll) {
        temp = [];
        let tempAmount = 0;
        this.state.itemList.forEach((item) => {
          if (item && item.valid) {
            tempAmount += 1;
          } else {
            this.setState({ selectedAll: false });
          }
          temp.push(item.valid);
        });
        this.setState({ selectedAmount: tempAmount });
      } else {
        temp = new Array(this.state.itemList.length).fill(false);
        this.setState({ selectedAmount: 0 });
      }
      this.setState({
        itemSelected: temp
      }, () => {
        // 赋值后释放
        temp = null;
      });
    });
  }

  // 每一项点击选中
  clickItem(index) {
    const temp = this.state.itemSelected || [];
    if (temp[index]) {
      this.setState({
        selectedAmount: this.state.selectedAmount > 0 ? this.state.selectedAmount - 1 : this.state.selectedAmount
      });
    } else {
      this.setState({
        selectedAmount: this.state.selectedAmount + 1
      });
    }
    temp[index] = !temp[index];
    this.setState({
      itemSelected: temp
    }, () => {
      // 如果数组每个值都为true，那么设isSelectedAll为true
      if (this.state.itemSelected.every((value) => value === true)) {
        this.setState({
          selectedAll: true
        });
      } else {
        this.setState({
          selectedAll: false
        });
      }
    });
  }

  render() {
    const {
      itemList, noMoreFlag, selectedAll, selectedAmount, itemSelected, hasLoan, show, showTransferModal
    } = this.state;
    let desc;
    if (show == 0) {
      desc = '可根据征信报告上的账户标识码选择开具';
    } else {
      desc = '可开具以下未结清的合并借款记录的贷款证明：';
    }
    const showList = itemList.map((item, index) => (
      <SettleIOUItem
        date={item && item.busiDate}
        money={item && item.snowballNo}
        type={show}
        grayed={item && !item.valid}
        invalidReasonCode={item && item.invalidReasonCode}
        isSelected={itemSelected && itemSelected[index]}
        onClick={this.preview.bind(this, item, index)}
        transferAction={() => {
          this.setState({
            showTransferModal: true,
          });
        }}
        clickRadio={this.clickItem.bind(this, index)}
      />
    ));
    const loanRecordArea = hasLoan && show == 0 ? (
      // <MUView className="loanRecord" beaconId="loanRecord" onClick={this.gotoLoanRecoed.bind(this)}>未结清的贷款记录</MUView>
      <MUView className="loanRecordText" beaconId="loanRecord" onClick={this.gotoLoanRecoed.bind(this)}>暂未结清的合并借款记录</MUView>
    ) : (<MUView className="bottom-addMargin" />);
    let naviBar;
    if (isCmb()) {
      naviBar = (
        <MUNavBar
          beaconId="NavBar"
          title={show == 0 ? '结清证明' : '贷款证明'}
          leftArea={[
            {
              type: 'icon',
              value: 'back',
            }
          ]}
        />
      );
    }
    return (
      <MUView className="selectIOUPage">
        {naviBar}
        {
          itemList && itemList.length <= 0 ? (
            <MUView className="empty-sign">
              <MUImage src={noRecordImg} className="no_record_img" />
              <MUView className="no_record_text">暂无记录</MUView>
              {loanRecordArea}
              {this.isChatChn && <ChatEntry extraParam={{ needLogin: 1 }} busiEntrance={isH5 ? '' : encodeURIComponent('repayment/#/pages/settle-proof/SelectIOUPage')} />}
            </MUView>
          ) : (
            <MUView className="item-content">
              <MUView className="desc">
                {desc}
              </MUView>
              {
                noMoreFlag ? (
                  <MUView>
                    {showList}
                    {loanRecordArea}
                  </MUView>
                )
                  : (
                    <MUPullToRefresh direction="up" onRefresh={this.getRecords}>
                      {showList}
                      <MUText className="pull-up-desc">上拉加载更多</MUText>
                    </MUPullToRefresh>
                  )
              }
              {this.isChatChn && <ChatEntry extraParam={{ needLogin: 1 }} busiEntrance={isH5 ? '' : encodeURIComponent('repayment/#/pages/settle-proof/SelectIOUPage')} />}
              <SettleIOUPageBottom
                isSelectedAll={selectedAll}
                selectedItemAmount={selectedAmount}
                clickRadio={this.bottomSelectedAll.bind(this)}
                type={show}
                onClick={this.goNext.bind(this)}
              />
            </MUView>
          )
        }
        <TransferModal
          beaconId="TransferModal"
          visible={showTransferModal}
          onClose={() => {
            this.setState({ showTransferModal: false });
          }}
          transferOrgName={this.transferInfo && this.transferInfo.transferOrgName || ''}
          phoneNumber={this.transferInfo && this.transferInfo.transferMerchantPhoneNo || ''}
        />
      </MUView>
    );
  }
}
