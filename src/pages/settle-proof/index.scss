.homePage {
  min-height: 100vh;

  &--white {
    background-color: #ffffff;
  }

  .tips-block {
    background-color: #ffffff;
    padding: 30px;
    margin-bottom: 50px;

    .mainTip {
      color: #333;
      font-size: 26px;
      line-height: 26px;
      margin-bottom: 8px;
    }

    .secondaryTips {
      color: #808080;
      font-size: 26px;
      line-height: 39px;
    }
  }

  .preview {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    width: 499px;
    height: 385px;
  }

  .preview_desc {
    margin-top: 20px;
    text-align: center;
    font-size: 26px;
    color: #808080;
    line-height: 26px;
  }

  .applyButton {
    margin: 80px 30px 0;
  }

  .empty-sign {
    padding-top: 100px;
    text-align: center;
    background: #fff;

    .no_record_img {
      width: 284px;
      height: 284px;
    }

    .no_record_text {
      padding: 80px 0 60px;
      font-size: 32px;
      color: #333;
      font-weight: 600;
      line-height: 32px;
    }

    .no_record_back {
      width: 240px;
      height: 72px;
      border-radius: 36px;
      font-size: 28px;
      line-height: 28px;
    }
  }

  .diversion-contact {
    margin: 20px;
    padding: 24px 0;
    background: #fff;
    border-radius: 16px;
    &__title {
      padding: 0 0 20px 30px;
      font-size: 32px;
      line-height: 48px;
      color: #000;
      font-weight: 600;
    }

    &__explain {
      padding-top: 16px;
      font-size: 24px;
      line-height: 36px;
      color: #a6a6a6;
      font-weight: 400;
      text-align: center;
    }
    
  }

  .chat-entry {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 30px;
  }
}

.checkPsdPage {
  .mainText {
    color: #333;
    font-size: 48px;
    text-align: center;
    margin-top: 72px;
  }

  .secondaryText {
    color: #808080;
    font-size: 28px;
    line-height: 28px;
    text-align: center;
    margin: 30px 0;
  }

  .setPasswdField {
    margin: 20px 75px;

    .forget-tip {
      margin-top: 20px;
      color: #3477FF;
      font-size: 24px;
      text-align: right;
    }
  }
}

.noLoanProofPreviewPage {
  margin: 60px 30px 0;

  .text {
    color: #808080;
    font-size: 28px;
    line-height: 28px;
  }

  .rich-text {
    background-color: #ffffff;
    margin: 50px auto;
    height: 800px;
  }

  .chat-entry {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 30px;
  }
}

.selectIOUPage {
  // margin-bottom: 200px;

  .empty-sign {
    margin-top: 240px;
    text-align: center;

    .no_record_img {
      width: 240px;
      height: 240px;
    }

    .no_record_text {
      margin: 60px 0 0;
      font-size: 32px;
      color: #808080;
      line-height: 32px;
    }
  }

  .item-content {

    .desc {
      margin: 10px 20px;
      font-size: 20px;
      color: #808080;
    }

    .pull-up-desc {
      margin-top: 50px;
      padding-bottom: 180px;
      display: block;
      font-size: 27px;
      text-align: center;
      color: #a6a6a6;
    }
  }

  .loanRecord {
    margin: 60px auto;
    border: 2px solid #CACACA;
    border-radius: 34px;
    width: 280px;
    height: 64px;
    text-align: center;
    color: #808080;
    font-size: 27px;
    line-height: 64px;
  }

  .bottom-addMargin {
    padding-bottom: 180px;
  }

  .loanRecordText {
    margin: 60px auto 0;
    padding-bottom: 180px;
    color: #3477FF;
    font-size: 27px;
    text-align: center;
  }

  .chat-entry {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 30px;
  }
}

.verifyEmailPage {
  .lead-tip {
    font-size: 26px;
    color: #808080;
    letter-spacing: 0;
    line-height: 26px;
    margin: 40px 0 0 30px;
  }

  .email_input {
    margin-top: 20px;
  }

  .sendButton {
    margin: 40px 30px 0;
  }

  .mu-dialog__footer {
    border-top: 1px solid #a6a6a6;

    .mu-dialog__action {
      .at-button.at-button--normal.at-button--default {
        background-color: #fff !important;
      }
    }
  }

  .chat-entry {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 30px;
  }
}

.settleResultPage {
  background-color: white;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  padding-bottom: 120px;

  .success-top-area {
    background-color: #ffffff;

    .center-text {
      text-align: center;
      margin: 0 100px;
    }

  }

  .under_line {
    margin: 0 30px;
  }

  .success-bottom-faq {
      width: auto;
      margin: 48px 20px 0 20px;
      padding: 24px 30px 40px;
      border-radius: 16px;
      border: 2px solid #FFFFFF;
      background: linear-gradient(180deg, #eef3fc 0%, #ffffff 100%);
      
  
      &__title {
        height: 48px;
        margin-bottom: 30px;
        color: #333333;
        font-size: 32px;
        font-weight: 600;
        line-height: 48px;
      }
  
      .faq-content {
        &__subTitle {
          height: 48px;
          display: flex;
          align-items: center;
  
          &--icon {
            height: 36px;
            width: 36px;
            margin-right: 14px;
          }

          &--text {
            display: inline;
            font-size: 28px;
            font-weight: 600;
            font-family: "PingFang SC";
            line-height: 48px;
          }
        }
  
        &__main {
          margin-left: 50px;
          margin-top: 12px;
          opacity: 1;
          color: #808080;
          text-align: left;
          font-size: 24px;
          font-weight: 400;
          font-family: "PingFang SC";
         line-height: 36px;
        }
      }
  }

  .successButton {
    background-color: #fff !important;
    color: #3477FF ;
    border: none;
    margin-top: 36px;
  }

  .settle-result-top {
  background-color: #FFFFFF;
  padding: 60px 0;

  .status-img-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    .status-img {
      width: 120px;
      height: 120px;
    }
  }

  .status-text {
    margin-top: 40px;
    font-weight: 500;
    font-size: 40px;
    color: #333333;
    line-height: 60px;
    text-align: center;
  }

  .status-tips {
    margin: 26px 50px 0;
    color: #888888;
    font-size: 28px;
    text-align: center;
    line-height: 42px;
  }

  .result-btn {
    margin: 50px 30px 0 30px;
  }
}

}





.pageSlogan {
  margin-top: -96px !important;
}
