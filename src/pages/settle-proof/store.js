import Madp from '@mu/madp';

const STORE_KEY = 'settleOrLoan_proof_data';

const RECORD_DEFAULTS = {
  // // 人行贷款信息, 结清证明使用
  // pccLoanInfoDTO: {
  //   pccLoanNo: '', // 人行贷款信息,
  //   pccAcctType: '',
  //   pccLoanDate: '',
  //   pccLoanStatus: '',
  //   pccAmount: '',
  //   pccActivationDate: '', // 仅R1需要
  //   pccLoanBalance: '', // 仅R1需要
  //   acctCredLine: '', // 仅R1需要
  // },
  // // 贷款定价信息,贷款证明使用
  // markedPriceInfo: {
  //   loanNo: '',
  //   period: '',
  //   amount: '',
  //   loanDate: '',
  //   settleDate: '',
  // },
  selectSnow: {}, // 选择预览的雪球
  LoanProofContractCode: '', // 贷款证明合同码
  SettleProofContractCode: '', // 结清证明合同码
  NoloanProofContractCode: '' // 无贷款证明合同码
};

const RECORD_STORE = {
  ...RECORD_DEFAULTS
};

Object.keys(RECORD_STORE).forEach((key) => {
  Object.defineProperty(RECORD_STORE, key, {
    set(val) {
      let data = Madp.getStorageSync(STORE_KEY, 'SESSION');
      if (!data) data = {};
      data[key] = val;
      Madp.setStorageSync(STORE_KEY, data, 'SESSION');
    },
    get() {
      let data = Madp.getStorageSync(STORE_KEY, 'SESSION');
      if (!data) data = {};
      if (data[key] === undefined) {
        data[key] = RECORD_DEFAULTS[key];
      }
      return data[key];
    }
  });
});

export const clearStore = () => {
  try {
    Madp.removeStorageSync(STORE_KEY, 'SESSION');
  } catch (e) {
    try {
      Madp.clearStorageSync();
    } catch (err) {}
  }
};
export const STORE = RECORD_STORE;

export default RECORD_DEFAULTS;
