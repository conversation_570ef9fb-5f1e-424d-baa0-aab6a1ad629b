/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
/* eslint-disable eqeqeq */
/* eslint-disable no-return-assign */
import { Component } from '@tarojs/taro';
import {
  MUView, MUButton, MUModal, MUDialog, MUNavBar, MUImage
} from '@mu/zui';
import Madp from '@mu/madp';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import Viewer from 'viewerjs';
import 'viewerjs/src/index.scss';
import Dispatch from '@api/actions';
import newPreview from './imgs/newSettlePreview.png';
import { MUTradePasswordEncryptedWrap } from '@mu/trade-password-encrypted-shell';
import { urlDomain } from '@utils/url_config';
import noRecordImg from './imgs/no_record_img.png';
import { ChatEntry } from '@mu/chat-entry-component';
import { isMuapp, isAlipay, isWechat, isCmb } from '@mu/madp-utils';
import Utility from '@utils/Utility';
import { STORE } from './store';

import DiversionRecordGuide from './components/diversionRecordGuide';

import './index.scss';

const isH5 = process.env.TARO_ENV === 'h5';

@track(
  {},
  {
    pageId: 'SettleHomePage',
    dispatchOnMount: true
  }
)
export default class SettleHomePage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      repayPopup: false, // 引导还款的弹窗
      hasRecord: false, // 是否有还款记录的弹窗
      openTradePwd: false, // 交易密码组件是否打开
      dialogText: '', // 密码错误超限弹窗内容
      dialogTitle: '', // 密码错误超限弹窗标题
      isMUDialogOpened: false, // 密码错误超限弹窗是否打开
      hasApply: false, // 是否已授信
      isPersonalInfoClick: false,
      showPage: false,
      fullName: '招联消费金融股份有限公司',
    };
    this.result = '';
    this.redirectUrl = '';
    this.hasTraPsd = false; // 是否已设置交易密码
    this.isChatChn = false; // 是否显示客服入口
    if (!isH5 || isAlipay() || isMuapp() || isWechat()) {
      this.isChatChn = true;
    }
    this.needShowDiversionMerchantInfo = false; // 是否需要展示导流商户信息
    this.diversionMerchantInfoList = []; // 导流商户信息列表
  }

  async componentDidMount() {
    const { redirectUrl } = this.$router.params;
    this.redirectUrl = redirectUrl;
    await this.getNewFullName();
    let custInfo;
    try {
      custInfo = await Dispatch.repayment.getNewUserInfo();
    } catch (ignore) {
      return;
    }
    const { identified } = custInfo && custInfo.basicCustDto || {};
    const { loginSuccess } = custInfo || {};
    const { hasTradePwd } = custInfo && custInfo.passwordDto || {};
    this.hasTraPsd = hasTradePwd;
    if (!loginSuccess) {
      const host = urlDomain;
      const channel = Madp.getChannel();
      const params = '/loginregister/#/auth?mgpAuth=1&redirectUrl=';
      const currentUrl = encodeURIComponent(window.location.href);
      const result = `${host}/${channel}${params}${currentUrl}`;
      Madp.redirectTo({ url: result });
    } else {
      if (!identified) {
        this.setState({
          showPage: true,
        });
        return;
      }
      this.check();
    }
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '结清证明' });
  }

  checkResult = (result) => {
    switch (result) {
      // 1年内无结清或贷款雪球，直接提示用户
      case '6':
        this.setState({
          hasRecord: true
        });
        break;
      // 已借款未结清，引导去还款
      case '2':
        this.setState({
          repayPopup: true
        });
        break;
      default:
        break;
    }
  };


  // 调用接口判断用是否能开具证明
  check = async () => {
    const [{ ret, data }, { data: channelParams }] = await Promise.all([
      Dispatch.repayment.checkSettlementOrNonLoanCertApply() || {},
      Dispatch.repayment.getChannelParams({
        channelParamKeyList: ['showFlowCardSwitch'],
        paramTypeList: ['CHANNEL_PARAM']
      }),
    ]);
    const showFlowCardSwitch = ((channelParams || {}).channelParamMap || {}).showFlowCardSwitch || '';
    if (ret === '0') {
      const { applyCheckResult, contractInfoList, needDiversionMerchantInfo, diversionMerchantInfoList } = data || {};
      // 存储合同编码，在预览合同接口中作为入参
      (contractInfoList || []).length > 0 && contractInfoList.forEach(contractInfo => {
        const { contractType, contractCode } = contractInfo || {};
        switch (contractType) {
          case 'KHFWL_KHDKZM':
            STORE.LoanProofContractCode = contractCode;
            break;
          case 'KHFWL_KHJQZM':
            STORE.SettleProofContractCode = contractCode;
            break;
          case 'KHFWL_KHWDKZM':
            STORE.NoloanProofContractCode = contractCode;
            break;
        }
      });
      this.needShowDiversionMerchantInfo = showFlowCardSwitch === 'ALL_OPEN' && needDiversionMerchantInfo === 'Y';
      this.diversionMerchantInfoList = diversionMerchantInfoList || [];
      dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'EnterPage', beaconContent: { cus: { checkResult: applyCheckResult, custType: this.needShowDiversionMerchantInfo ? '1' : '0' } } });
      if (!applyCheckResult || applyCheckResult === '0') {
        this.setState({
          showPage: true,
        });
        return;
      }
      this.setState({ hasApply: true, showPage: true }, () => {
        this.test = new Viewer(this.image, {
          url: 'data-original',
          toolbar: false,
          button: false,
          navbar: false,
          title: false
        });
        this.result = applyCheckResult;
        this.checkResult(this.result);
      });
    }
  };

  // 29799 更改司名
  getNewFullName = async () => {
    const fullCompanyName = await Utility.getFullCompanyName();
    this.setState({ fullName: fullCompanyName });
  };

  async apply() {
    // 先把不符合条件的用户筛掉
    if (this.result == '2' || this.result == '6') {
      this.checkResult(this.result);
    } else if (!this.hasTraPsd) {
      this.modalInfo('您还未设置交易密码，暂不支持开结清证明');
    } else {
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'applySettleInfoClick', target: this });
      this.setState({
        openTradePwd: true,
      });
    }
  }

  settleApply() {
    // 已授信未借款,可开无贷款证明
    if (this.result === '1') {
      Madp.navigateTo({
        url: `/pages/settle-proof/NoLoanProofPreviewPage?redirectUrl=${this.redirectUrl}`
      });
    }
    // 1年内无结清雪球，但有结清借据
    if (this.result === '3') {
      Madp.navigateTo({
        url: `/pages/settle-proof/SelectIOUPage?type=loanProof&show=0&redirectUrl=${this.redirectUrl}`
      });
    }
    // 1年内有结清雪球，且有未结清的合并借款
    if (this.result === '4') {
      Madp.navigateTo({
        url: `/pages/settle-proof/SelectIOUPage?type=settleAndLoanProof&show=0&redirectUrl=${this.redirectUrl}`
      });
    }
    // 1年内有结清雪球，且无未结清的合并借款
    if (this.result === '5') {
      Madp.navigateTo({
        url: `/pages/settle-proof/SelectIOUPage?type=settleProof&show=0&redirectUrl=${this.redirectUrl}`
      });
    }
  }

  closePwd() {
    this.setState({
      openTradePwd: false
    });
  }

  toSetPwd() {
    const redirectUrl = encodeURIComponent(window.location.href);
    const channel = Madp.getChannel();
    const host = urlDomain;
    const url = `${host}/${channel}/safecenter/#/member/reset-password/now-phone-check?redirectUrl=${redirectUrl}`;
    Madp.navigateTo({
      url
    });
  }

  // 跳去还款链接
  goRepay() {
    const channel = Madp.getChannel();
    const host = urlDomain;
    Madp.navigateTo({
      url: `${host}/${channel}/repayment/#/pages/bill-list-all/index`
    });
  }

  // 关闭引导还款弹窗
  closeRepayPopup() {
    this.setState({
      repayPopup: false
    });
  }

  // 关闭无记录弹窗
  closeRecordPopup() {
    this.setState({
      hasRecord: false
    });
    // 有导流借据不退出页面
    if (!this.needShowDiversionMerchantInfo) {
      Madp.closeWebView();
    }
  }

  onTitleIconClicked = () => {
    this.setState({
      isPersonalInfoClick: true
    });
  }

  onConfirmPasswordModal = () => {
    this.setState({ isPersonalInfoClick: false });
  }

  // 弹窗提示
  modalInfo(text) {
    // 没有交易密码
    Madp.showModal({
      title: '设置数字交易密码',
      content: text || '当前操作需要使用数字交易密码，请先设置',
      confirmColor: '#3477ff',
      confirmText: '去设置',
      cancelText: '暂不设置',
      success: (res) => {
        if (res && res.confirm) {
          const channel = Madp.getChannel();
          const host = urlDomain;
          const redirectUrl = encodeURIComponent(`${host}/${channel}/repayment/#/pages/settle-proof/SettleHomePage`);
          const resultUrl = `${host}/${channel}/safecenter/#/member/reset-password/now-phone-check?redirectUrl=${redirectUrl}`;
          Madp.navigateTo({
            url: resultUrl
          });
        } else if (res && res.cancel) {
          try {
            Madp.closeWebView();
          } catch (error) {
            console.log(error, 'error');
          }
        }
      }
    });
  }

  confirmDialog() {
    this.setState({
      isMUDialogOpened: false
    });
  }

  noRecordBack() {
    Madp.navigateBack().then().catch(() => {
      Madp.closeWebView();
    });
  }

  jumpSettleDiversion = () => {
    const { diversionMerchantInfoList } = this;
    if (diversionMerchantInfoList.length > 0) {
      Madp.navigateTo({
        url: `/pages/settle-proof/DiversionGuidePage?diversionMerchantInfoList=${JSON.stringify(diversionMerchantInfoList)}`
      });
    }
  }

  render() {
    const {
      repayPopup,
      hasRecord,
      openTradePwd,
      dialogText,
      dialogTitle,
      isMUDialogOpened,
      hasApply,
      showPage,
      fullName,
    } = this.state;
    const { needShowDiversionMerchantInfo, diversionMerchantInfoList } = this;
    if (!showPage) {
      return <MUView />;
    }
    let naviBar;
    if (isCmb()) {
      naviBar = (
        <MUNavBar
          beaconId="NavBar"
          title="结清证明"
          leftArea={[
            {
              type: 'icon',
              value: 'back'
            }
          ]}
        />
      );
    }
    return hasApply ? (
      <MUView className="homePage">
        {naviBar}
        <MUView className="tips-block">
          <MUView className="mainTip">须知:</MUView>
          <MUView className="secondaryTips">
            <MUView>1、已结清超过一年的借款记录不支持开具；</MUView>
            <MUView>2、结清全部借款后，人行征信记录将在次月3个工作日内更新；</MUView>
            <MUView>3、结清证明按人行记录开具，不支持按借据开具；</MUView>
            <MUView>4、证明以电子文档形式提供，与纸质版同样具有法律效力；</MUView>
            <MUView>5、如需纸质版，您可通过邮箱附件下载打印。</MUView>
            {needShowDiversionMerchantInfo ? (
              <MUView>6、仅支持为贷款机构是招联金融的借款开具结清证明，如需开具第三方机构贷款结清证明，可<span style="color: #3477ff" onClick={this.jumpSettleDiversion}>联系相应第三方客服</span>。</MUView>
            ) : null}
          </MUView>
        </MUView>
        {/* MUImage平台组做了封装，即使通过ref也无法获取元素 */}
        <img
          className="preview"
          alt=""
          src={newPreview}
          data-original={newPreview}
          ref={(el) => (this.image = el)}
        />
        <MUView className="preview_desc">点击图片预览</MUView>
        <MUButton
          beaconId="applyButton"
          className="applyButton"
          type="primary"
          onClick={this.apply.bind(this)}
        >
          立即申请
        </MUButton>
        {this.isChatChn && <ChatEntry extraParam={{ needLogin: 1 }} busiEntrance={isH5 ? '' : encodeURIComponent('repayment/#/pages/settle-proof/SettleHomePage')} />}
        <MUModal
          beaconId="repayPopup"
          isOpened={repayPopup}
          content="您暂无可开具结清证明的记录，建议先还清借款再操作"
          confirmText="去还款"
          cancelText="我知道了"
          onConfirm={this.goRepay.bind(this)}
          onCancel={this.closeRepayPopup.bind(this)}
          onClose={this.closeRepayPopup.bind(this)}
        />
        <MUModal
          beaconId="recordPopup"
          isOpened={hasRecord}
          content="近一年内无可开具结清证明的借款记录"
          cancelText="我知道了"
          onCancel={this.closeRecordPopup.bind(this)}
          onClose={this.closeRecordPopup.bind(this)}
        />
        <MUTradePasswordEncryptedWrap
          beaconId="inputPwd"
          title="同意并输入6位数字交易密码"
          scene="SCENE_SETTLE_PROOF"
          verifyType="TRA"
          isOpened={openTradePwd}
          onTitleIconClicked={this.onTitleIconClicked}
          onTitleIcon
          needFpPayment
          onOk={this.settleApply.bind(this)}
          onClose={this.closePwd.bind(this)}
          leftText=""
          onForgotPass={this.toSetPwd.bind(this)}
          onOverPwAttemptNum={() => {
            this.setState({
              dialogText: '密码错误次数过多已被锁定，如有疑问请联系在线客服',
              dialogTitle: '温馨提示',
              isMUDialogOpened: true
            });
            this.closePwd();
          }}
        />
        <MUDialog
          beaconId="CommonDialog"
          title={dialogTitle}
          isOpened={isMUDialogOpened}
          buttons={[
            {
              label: '我知道了',
              onClick: () => {
                this.confirmDialog();
              }
            }
          ]}
        >
          {dialogText}
        </MUDialog>
        <MUModal
          type="text"
          beaconId="isPasswordModal"
          isOpened={this.state.isPersonalInfoClick}
          title="关于个人信息处理规则说明"
          content={`在以下场景中，${fullName}需要收集、使用您的交易密码:<br>（1）您在招联设置或修改您的交易密码时；<br>（2）您在招联进行借款、消费、红包提现等交易时；<br>（3）您在招联查看或获取重要信息时，如账户解挂、合同下载等；<br>交易密码是您的重要个人信息，一旦泄露或处理不当可能危害您的财产安全，招联将根据法律法规要求并参照行业最佳实践为您的个人信息安全提供保障。`}
          onConfirm={() => this.onConfirmPasswordModal()}
          confirmText="我知道了"
          closeOnClickOverlay={false}
        />
      </MUView>
    ) : (
      <MUView className={`homePage${!needShowDiversionMerchantInfo ? ' homePage--white' : ''}`}>
        {naviBar}
        <MUView className="empty-sign">
          <MUImage src={noRecordImg} className="no_record_img" />
          <MUView className="no_record_text">暂无招联额度记录</MUView>
          {!needShowDiversionMerchantInfo ? (
            <MUButton className="no_record_back" type="primary" onClick={() => this.noRecordBack()}>
              返回首页
            </MUButton>
          ) : null}
        </MUView>
        {needShowDiversionMerchantInfo ? (
          <MUView className="diversion-contact">
            <MUView className="diversion-contact__title">智选专区-第三方机构贷款</MUView>
            <MUView className="diversion-contact__record">
              {diversionMerchantInfoList.map((item) => (
                <DiversionRecordGuide key={item.merchantNo} diversionRecordGuideInfo={item} trackPageId="repayment.SettleHomePage." />
              ))}
            </MUView>
            <MUView className="diversion-contact__explain">
              <MUView>暂不支持自助开具第三方机构贷款结清证明</MUView>
              <MUView>如有需要可联系其客服开具</MUView>
            </MUView>
          </MUView>
        ) : null}
        {this.isChatChn && <ChatEntry extraParam={{ needLogin: 1 }} busiEntrance={isH5 ? '' : encodeURIComponent('repayment/#/pages/settle-proof/SettleHomePage')} />}
      </MUView>
    );
  }
}
