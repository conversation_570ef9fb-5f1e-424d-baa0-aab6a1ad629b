import fetch from '@utils/mgp-fetch';
import Template7 from 'template7/dist/template7.min.js';
import T7HelperRegister from './t7';
import paramHandler from './paramHandler';
import { WeappTemplate, getHtmlBodyContent } from './weappHandler';

T7HelperRegister(Template7);

const isWeapp = process.env.TARO_ENV === 'weapp';
const QueryContractInfo = 'mucfc.user.contract.queryContractInfo';
const QueryHtmlTemplate = 'mucfc.user.contract.queryHtmlTemplate';
const ContractDownload = 'mucfc.user.contract.contractDownload';

const contractApi = {};

/**
 * 签约后的查询接口
 */
contractApi.queryContractInfo = async (data) => {
  const ret = await fetch(QueryContractInfo, { data });
  return ret;
};

/**
 * 签约前的合同接口，无需登陆态,但是需要前端将数据结合进去
 */
contractApi.queryHtmlTemplate = async (data) => {
  const ret = await fetch(QueryHtmlTemplate, { data });
  return ret;
};

/**
 * 合同下载
 */
contractApi.contractDownload = async (data) => {
  const ret = await fetch(ContractDownload, { data });
  return ret;
};

/**
 * 处理要填充到合同里面的参数
 */
contractApi.setAgreementData = async (ret, opt, contractData) => {
  let agreementData = { ...contractData };
  if (ret && ret.contractData) {
    // 若根据合同号查得合同信息，则填充参数从中台返回无需处理
    agreementData = ret.contractData;
    agreementData = JSON.parse(agreementData);
  } else {
    // 否则填充参数从外部传入，需统一处理
    agreementData = await paramHandler(agreementData, opt);
  }
  return agreementData;
};

/**
 * 用queryHtmlTemplate接口返回的模板，前端要自行渲染（填充数据）
 * h5用Template7，weapp用WeappTemplate
 */
contractApi.handleResult = async (ret, opt, contractData) => {
  opt = opt || {};
  contractData = contractData || {};
  // 合同填充参数
  const agreementData = await contractApi.setAgreementData(ret, opt, contractData);
  // weapp不支持Template7,使用WeappTemplate
  let afterHtml;
  if (isWeapp) {
    const weHtml = new WeappTemplate(agreementData);
    const htmlBlocks = weHtml.stringToBlocks(ret.htmlFile);
    afterHtml = weHtml.computeBlocks(htmlBlocks);
  } else {
    const temp = Template7.compile(ret.htmlFile);
    afterHtml = temp(agreementData);
  }
  return afterHtml;
};

/**
 * h5: 签约后接口返回的合同自带样式，要去除
 * weapp: 小程序要对合同格式进行调整
 */
contractApi.getHtmlBodyContent = async (htmlFile) => getHtmlBodyContent(htmlFile);


export default contractApi;
