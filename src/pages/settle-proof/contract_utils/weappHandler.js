/* eslint-disable */
const isWeapp = process.env.TARO_ENV === 'weapp';

/**
 * 小程序的合同调整样式
 * rich-text 不支持的标签太多了，合同没必要那么秀呐~
 */
function resiezStyle(htmlContents) {
  // 给第一个 div 添加类 限制小程序类样式只在小程序中生效
  htmlContents = `<div   class="weapp-class">${htmlContents}</div>`;
  // 给p标签添加类 方便设置样式
  htmlContents = htmlContents.replace(/<p[^>]*>/gi, (item) => {
    if (item.indexOf('class') > 0) {
      let classNames = item.split('class="');
      item = `<p   class="p-weapp ${classNames[1]}`;
      // if (item.indexOf('text-no-indent') > 0) {
      //   item = `<p style="text-indent: 0;" class="p-weapp ` + classNames[1];
      // }
    } else {
      item = '<p class="p-weapp" >';
    }
    return item;
  });
  let afterHtml = htmlContents;
  afterHtml = afterHtml.replace(/<input[^>]*>\r\n/gi, (item) => {
    if (item.indexOf('checked') > 0) {
      item = '<p class="input-weapp-checked"></p>';
    } else {
      item = '<p class="input-weapp"></p>';
    }
    return item;
  });
  // 给个人借款合同 设置表格样式，添加类名
  // 神奇的空格，不去掉空格会异常换行
  afterHtml = afterHtml.replace(/ {2}/gi, '').replace(/<table[^>]*>/gi,
    '<p class="weapp-table">').replace(/<tbody[^>]*>/gi, '<p class="weapp-tbody">');
  // 表格一行1格2格4格
  afterHtml = afterHtml.replace(/<tr>[^>]*>([\s\S]+?)<\/tr>/gi,
    (tr_item) => {
      let tr_item_counts = tr_item.match(/<td[^>]*>([\s\S]+?)<\/td>/gi).length;
      let count = 1;
      let weapp_tr = tr_item.replace(/<tr[^>]*>/gi, '<p  class="weapp-tr">').replace(/<td[^>]*>/gi,
        (item) => {
          return `<p  class="weapp-td-${tr_item_counts}-${count++}">`;
        }
      );
      return weapp_tr;
    }
  );
  afterHtml = afterHtml.replace(/<\/table>|<\/tbody>|<\/tr>|<\/td>/gi,
    '</p>');

  return afterHtml;
}


/**
 * 返回的合同，小程序rich-text组件不支持 html、head、meta、style、body标签，
 * 现在只提出body里面的内容
 */
function getHtmlBodyContent(htmlTemplate) {
  let reg = /<body>[^>]*>([\s\S]+?)<\/body>/;
  let arr = reg.exec(htmlTemplate);
  let result = htmlTemplate;
  // 去除第一个换行
  if (arr) {
    result = arr[1].replace('\n\t', '');
    result = `<div class="contract_third">${result}</div>`;
  }
  if (!isWeapp) { return result; }
  // 小程序格式调整
  result = resiezStyle(result);
  return result;
}

/**
 * @description: 在template7不能在小程序使用时，使用该方法将数据填充到模板中，返回完整的合同/协议 （不够完善，合同增添新语法会出错）
 * @version: V1.0
 * @date: 2020-11-25
 * @author: ✨dlCai✨
 */
class WeappTemplate {
  constructor(data) {
    this.json = data;
    this.quoteSingleRexExp = new RegExp('\'', 'g');
    this.quoteDoubleRexExp = new RegExp('"', 'g');
  }

  helperToSlices(string) {
    let { quoteDoubleRexExp } = this;
    let { quoteSingleRexExp } = this;
    let helperParts = string.replace(/[{}#}]/g, '').trim().split(' ');
    let slices = [];
    let shiftIndex;
    let i;
    let j;
    for (i = 0; i < helperParts.length; i += 1) {
      let part = helperParts[i];
      let blockQuoteRegExp = (void 0);
      let openingQuote = (void 0);
      if (i === 0) { slices.push(part); } else if (part.indexOf('"') === 0 || part.indexOf('\'') === 0) {
        blockQuoteRegExp = part.indexOf('"') === 0 ? quoteDoubleRexExp : quoteSingleRexExp;
        openingQuote = part.indexOf('"') === 0 ? '"' : '\'';
        // Plain String
        if (part.match(blockQuoteRegExp).length === 2) {
          // One word string
          slices.push(part);
        } else {
          // Find closed Index
          shiftIndex = 0;
          for (j = i + 1; j < helperParts.length; j += 1) {
            part += ` ${helperParts[j]}`;
            if (helperParts[j].indexOf(openingQuote) >= 0) {
              shiftIndex = j;
              slices.push(part);
              break;
            }
          }
          if (shiftIndex) { i = shiftIndex; }
        }
      } else if (part.indexOf('=') > 0) {
        // Hash
        let hashParts = part.split('=');
        let hashName = hashParts[0];
        let hashContent = hashParts[1];
        if (!blockQuoteRegExp) {
          blockQuoteRegExp = hashContent.indexOf('"') === 0 ? quoteDoubleRexExp : quoteSingleRexExp;
          openingQuote = hashContent.indexOf('"') === 0 ? '"' : '\'';
        }
        if (hashContent.match(blockQuoteRegExp).length !== 2) {
          shiftIndex = 0;
          for (j = i + 1; j < helperParts.length; j += 1) {
            hashContent += ` ${helperParts[j]}`;
            if (helperParts[j].indexOf(openingQuote) >= 0) {
              shiftIndex = j;
              break;
            }
          }
          if (shiftIndex) { i = shiftIndex; }
        }
        let hash = [hashName, hashContent.replace(blockQuoteRegExp, '')];
        slices.push(hash);
      } else {
        // Plain variable
        slices.push(part);
      }
    }
    return slices;
  }
  stringToBlocks(string) {
    let blocks = [];
    let i;
    let j;
    if (!string) { return []; }
    let stringBlocks = string.split(/({{[^{^}]*}})/);
    for (i = 0; i < stringBlocks.length; i += 1) {
      let block = stringBlocks[i];
      if (block === '') { continue; } // 空元素跳过
      if (block.indexOf('{{') < 0) { // 简单元素
        blocks.push({
          'type': 'plain',
          'content': block,
        });
      } else { // 语法元素
        if (block.indexOf('{/') >= 0) { // 结尾元素
          continue;
        }
        block = block.replace(/{{([#/])*([ ])*/, '{{$1').replace(/([ ])*}}/, '}}');
        // console.log('block', block)
        if (block.indexOf('{#') < 0 && block.indexOf(' ') < 0 && block.indexOf('else') < 0) {
          // 简单值
          blocks.push({
            'type': 'variable',
            'contextName': block.replace(/[{}]/g, ''),
          });
          continue;
        }
        // Helpers
        let helperSlices = this.helperToSlices(block);
        let helperName = helperSlices[0];
        let isPartial = helperName === '>';
        let helperContext = [];
        let helperHash = {};
        for (j = 1; j < helperSlices.length; j += 1) {
          let slice = helperSlices[j];
          if (Array.isArray(slice)) {
            // Hash
            helperHash[slice[0]] = slice[1] === 'false' ? false : slice[1];
          } else {
            helperContext.push(slice);
          }
        }

        if (block.indexOf('{#') >= 0) {
          // Condition/Helper
          let helperContent = '';
          let elseContent = '';
          let toSkip = 0;
          let shiftIndex = (void 0);
          let foundClosed = false;
          let foundElse = false;
          let depth = 0;
          for (j = i + 1; j < stringBlocks.length; j += 1) {
            if (stringBlocks[j].indexOf('{{#') >= 0) {
              depth += 1;
            }
            if (stringBlocks[j].indexOf('{{/') >= 0) {
              depth -= 1;
            }
            if (stringBlocks[j].indexOf((`{{#${helperName}`)) >= 0) {
              helperContent += stringBlocks[j];
              if (foundElse) { elseContent += stringBlocks[j]; }
              toSkip += 1;
            } else if (stringBlocks[j].indexOf((`{{/${helperName}`)) >= 0) {
              if (toSkip > 0) {
                toSkip -= 1;
                helperContent += stringBlocks[j];
                if (foundElse) { elseContent += stringBlocks[j]; }
              } else {
                shiftIndex = j;
                foundClosed = true;
                break;
              }
            } else if (stringBlocks[j].indexOf('else') >= 0 && depth === 0) {
              foundElse = true;
            } else {
              if (!foundElse) { helperContent += stringBlocks[j]; }
              if (foundElse) { elseContent += stringBlocks[j]; }
            }
          }
          if (foundClosed) {
            if (shiftIndex) { i = shiftIndex; }
            if (helperName === 'raw') {
              blocks.push({
                'type': 'plain',
                'content': helperContent,
              });
            } else {
              blocks.push({
                'type': 'helper',
                'helperName': helperName,
                'contextName': helperContext,
                'content': helperContent,
                'inverseContent': elseContent,
                'hash': helperHash,
              });
            }
          }
        } else if (block.indexOf(' ') > 0) {
          if (isPartial) {
            helperName = '_partial';
            if (helperContext[0]) {
              if (helperContext[0].indexOf('[') === 0) { helperContext[0] = helperContext[0].replace(/[[\]]/g, ''); } else { helperContext[0] = `"${helperContext[0].replace(/"|'/g, '')}"`; }
            }
          }
          blocks.push({
            'type': 'helper',
            'helperName': helperName,
            'contextName': helperContext,
            'hash': helperHash,
          });
        }
      }
    }
    return blocks;
  }
  computeBlocks(blocks) {
    let resultString = '';
    if (!blocks) { return resultString; }
    for (let i = 0; i < blocks.length; i += 1) {
      let block = blocks[i];
      // console.log(i, block)
      // 简单元素直接添加
      if (block.type === 'plain') {
        // eslint-disable-next-line
        resultString += block.content;
        continue;
      }
      // 简单值 直接获取
      if (block.type === 'variable') {
        let variable = this.json[block.contextName] || '';// 不存在就返回空
        // console.log('variable', variable)
        resultString += variable;
        continue;
      }
      // 复杂的块
      if (block.type === 'helper') {
        if (block.helperName === 'if') { // if语句判断
          if (this.json[block.contextName[0]]) { // if第一个条件满足
            resultString += this.json[block.contextName[0]];
            continue;
            // 求第二个条件的结果 三种情况
          } else if (block.inverseContent.indexOf('{#') < 0 && block.inverseContent.indexOf('{{') < 0) { // 简单元素
            // console.log('helper 简单元素', block.inverseContent)
            resultString += block.inverseContent;
            continue;
          } else if (block.inverseContent.indexOf('{#') < 0 && block.inverseContent.indexOf('{{') >= 0) { // 简单值
            resultString += this.json[block.inverseContent.replace(/[{}]/g, '')] || '';// 去除{{ }}
            continue;
          } else { // 下一级的语句 递归大法好
            let nextBlocks = this.stringToBlocks(block.inverseContent);
            resultString += this.computeBlocks(nextBlocks);
          }
        }
        if (block.helperName === 'js_compare') { // js_compare语句判断
          let boolValue = this.chooseStatus(block.contextName[0]);
          let nextBlocks = this.stringToBlocks(boolValue ? block.content : block.inverseContent);
          resultString += this.computeBlocks(nextBlocks);
          continue;
        }
      }
    }
    return resultString;
  }
  // 现阶段只支持 或 （调研发现：现在只有或的情况...）
  chooseStatus(string) {
    // 只有或的
    // 分割出判断式子
    let stringBlocks = string.split(/`|"|\|\|/);
    for (let i = 0; i < stringBlocks.length; i += 1) {
      let block = stringBlocks[i];
      if (block === '') { continue; } // 空元素跳过
      let keyValue = block.split(/ |this.|=+|'/);
      let noNullkeyValue = keyValue.filter((item) => item !== '');
      // console.log(block,noNullkeyValue);
      if (this.json[noNullkeyValue[0]] === noNullkeyValue[1]) {
        return true;
      }
    }
    return false;
  }
}

export {
  getHtmlBodyContent,
  WeappTemplate
};