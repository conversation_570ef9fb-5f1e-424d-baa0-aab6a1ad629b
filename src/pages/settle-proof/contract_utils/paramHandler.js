/* eslint-disable */
import Madp from '@mu/madp';
/**
 * 本地存储管理
 * @param {String} type 存储器类型，包括'SESSION', 'LOCAL'
 */
function storageObject(type) {
  const t = type || 'SESSION';
  if (['SESSION', 'LOCAL'].indexOf(t) === -1) {
    console.error('无效的存储类型:', t);
  } else {
    return {
      type: t,
      getItem: function (key) {
        const value = Madp.getStorageSync(key, this.type) || '{}';
        return JSON.parse(value);
      },
      setItem: function (key, value) {
        Madp.setStorageSync(key, JSON.stringify(value), this.type);
      },
      removeItem: function (key) {
        Madp.removeStorageSync(key, this.type);
      },
      clear: function () {
        Madp.clearStorageSync();
      }
    };
  }
}

/**
 * 处理账单协议中参数较长的问题
 * 参考还款组件，跳转前会把信息缓存
 * @param {Object} params // 填充参数
 * @param {Object} opt // 合同要素
 */
function billAgreementHandler(params, opt) {
  const data = params;
  if (opt.contractType === 'BILL') {
    const storage = new storageObject();
    data.loanDetail = storage.getItem('loanDetail');
  }
  return data;
}

/**
 * 填充用户信息，当前日期（可选）
 * @param {Object} params
 */
function dateHandler(params) {
  const data = params;
  // agreementDate旧模板参数 || contractDate新合同参数 || yearNow新合同参数
  // 有就不处理，没有则处理，处理后使用与否合同模板决定
  if (data.agreementDate || data.contractDate || data.yearNow) return data;
  const date = new Date();
  const agreementDate = `${date.getFullYear()}年${(date.getMonth() + 1)}月${date.getDate()}日`;
  data.agreementDate = agreementDate;
  return data;
}

/**
 * 协议中包含期数的，都只填充值，单位已写在模板里
 * @param {Object} params
 */
function cntHandler(params) {
  const data = params;
  if (data.cnt) {
    data.cnt = (data.cnt).split('期')[0];
  }
  return data;
}

/**
 * 协议填充字段是对象的，无法通过链接带过来，写在缓存里
 * @param {Object} params
 */
function objHandler(params) {
  let data = params;
  try {
    const storage = new storageObject();
    const objData = storage.getItem('CONTRACT_OBJ_DATA');
    data = {
      ...data,
      ...objData
    };
  } catch (err) {
    console.log(err);
  } finally {
    return data;
  }
}

/**
 * 兼容旧模板，对传入参数进行统一处理
 * @param {Object} params // 填充参数
 * @param {Object} opt // 合同要素
 */
async function paramHandler(params, opt) {
  let data = params;
  data = billAgreementHandler(data, opt);
  data = dateHandler(data);
  data = cntHandler(data);
  data = objHandler(data);
  return data;
}

export default paramHandler;
