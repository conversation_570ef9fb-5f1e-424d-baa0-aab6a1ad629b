/* eslint-disable import/first */
/* eslint-disable eqeqeq */
/* eslint-disable no-return-assign */
import { Component } from '@tarojs/taro';
import {
  MUView, MUNavBar, MUNoticebar,
} from '@mu/zui';
import { track } from '@mu/madp-track';
import { isCmb, Url } from '@mu/madp-utils';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import Madp from '@mu/madp';
import Dispatch from '@api/actions';
import DiversionRecordGuide from './components/diversionRecordGuide';

import './index.scss';

const themeColor = Util.getThemeColor(channelConfig.theme);

@track({}, {
  pageId: 'DiversionGuidePage',
  dispatchOnMount: true,
})

export default class DiversionGuidePage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      diversionMerchantInfoList: JSON.parse(Url.getParam('diversionMerchantInfoList') || '[]'),
    };
  }

  // eslint-disable-next-line react/sort-comp
  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '结清证明' });
  }

  render() {
    let naviBar;
    if (isCmb()) {
      naviBar = (
        <MUNavBar
          beaconId="NavBar"
          title="无贷款证明"
          leftArea={[
            {
              type: 'icon',
              value: 'back',
            }
          ]}
        />
      );
    }
    const { diversionMerchantInfoList } = this.state;
    return (
      <MUView className="diversion-guide-page">
        {naviBar}
        <MUNoticebar icon="notice" className={themeColor === '#E60027' ? 'brand-selected' : ''} marquee>招联金融暂不支持自助开具第三方机构结清证明，您可联系相应第三方机构客服协助开具</MUNoticebar>
        {diversionMerchantInfoList.map((item) => (
          <DiversionRecordGuide key={item.merchantNo} diversionRecordGuideInfo={item} single trackPageId="repayment.DiversionGuidePage." />
        ))}
      </MUView>
    );
  }
}
