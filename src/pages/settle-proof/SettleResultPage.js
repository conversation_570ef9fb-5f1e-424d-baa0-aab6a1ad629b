/* eslint-disable no-nested-ternary */
/* eslint-disable eqeqeq */
/* eslint-disable react/destructuring-assignment */
import { Component } from '@tarojs/taro';
import {
  MUView, MUButton, MUNavBar, MUSlogan,
  MURichText, MUImage
} from '@mu/zui';
import Madp from '@mu/madp';
import { opService, getPageConf } from '@mu/business-basic';
import { OpRepayment } from '@mu/op-comp';
import './index.scss';
import { track, EventTypes } from '@mu/madp-track';
import { isCmb } from '@mu/madp-utils';
import { injectState } from '@mu/leda';
import FAQ_ICON from '@components/assets/img/faq_icon.png';
import ICON_REJECT from '@components/assets/img/icon_service_reject.png';
import { PROOF_TYPE_MAPPING, PROOF_TYPES } from './constants';
import { EVENT_CODE_MAP } from '@utils/constants';
import Util from '@utils/maxin-util';

const settleProofSuccessId = 'f2d28818-7307-444c-8272-e9c3d9177e29';

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: settleProofSuccessId
    }
  }
}, {
  pageId: 'SettleResultPage',
  dispatchOnMount: true, // 页面埋点需要这样写, 表示当页面组件挂载时立刻触发埋点事件
})
@injectState({
  pageId() {
    return settleProofSuccessId;
  },
  getPageConf: () => getPageConf(settleProofSuccessId, true),
  stateKeys: []
})
export default class SettleResultPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
    this.result = this.$router.params.result || '';
    this.pageType = this.$router.params.pageType || '';
    this.redirectUrl = this.$router.params.redirectUrl || '';
  }

  componentDidMount() {
    const { showType } = this.$router.params;
    this.setNavTitle();
    Util.pushUrlState('keepState');
    if (this.result === 'success' && showType == PROOF_TYPES.SETTLE) {
      this.handleOpPageEvent();
    }
  }

  componentDidShow() {
    this.setNavTitle();
  }

  async beforeRouteLeave(from, to, next, options) {
    if (from.path === to.path) {
      Util.closeOrBack();
    } else {
      next(true);
    }
  }

  // 交互式运营事件
  handleOpPageEvent = async () => {
    await this.opOnPageEvent('opPageEnter', EVENT_CODE_MAP.settleProofSuccess);
  }

  async opOnPageEvent(eventName, interactionEventCode) {
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: settleProofSuccessId,
          },
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        console.error(error);
        resolve(true);
      }
    });
  }


  setNavTitle = () => {
    const { showType } = this.$router.params;
    const mapping = PROOF_TYPE_MAPPING[showType] || {};
    Madp.setNavigationBarTitle({
      title: mapping.navTitle || '证明'
    });
  }

  closeWeb() {
    if (this.redirectUrl && this.redirectUrl !== 'undefined') {
      Madp.navigateTo({
        url: decodeURIComponent(this.redirectUrl)
      });
    } else {
      Madp.closeWebview();
    }
  }


  render() {
    const { email, showType } = this.$router.params;
    const successTitle = '已发送';
    const mapping = PROOF_TYPE_MAPPING[showType] || {};
    const emailTitle = `“招联${mapping.emailTitle || ''}证明”`;
    const richNode = this.result === 'success' ? (
      `邮箱：<span style="color: #FF8844">${email}</span><br />
        预计2分钟内发送至您的邮箱，邮件标题为${emailTitle}，请注意查收`
    ) : (
      '因证明开具异常，邮件发送失败，请稍后重试'
    );
    let naviBar;
    if (isCmb()) {
      naviBar = (
        <MUNavBar
          beaconId="NavBar"
          title={showType == 0 ? '结清证明' : (showType == 1 ? '贷款证明' : '无贷款证明')}
          leftArea={[
            {
              type: 'icon',
              value: 'back',
            }
          ]}
        />
      );
    }
    return (
      <MUView>
        <MUView className="settleResultPage">
          {naviBar}
          {this.result === 'success' ? (
            <MUView className="settle-result-top">
              <MUView className="status-img-container">
                <MUImage className="status-img" src="https://file.mucfc.com/zlh/3/0/202305/20230518202321a82936.png" />
              </MUView>
              <MUView className="status-text">{successTitle}</MUView>
              <MURichText className="status-tips" nodes={richNode} />
              <MUView className="success-bottom-faq">
                <MUView className="success-bottom-faq__title">您可能想问</MUView>
                <MUView className="faq-content">
                  <MUView className="faq-content__subTitle">
                    <MUImage className="faq-content__subTitle--icon" src={FAQ_ICON} />
                    <MUView className="faq-content__subTitle--text">未收到邮件，怎么办？</MUView>
                  </MUView>
                  <MUView className="faq-content__main">
                    <MURichText nodes="1、刷新邮箱并等待一段时间<br/>2、检查邮箱垃圾里是否有该邮件<br/>3、更改其他邮箱地址重新发送" />
                  </MUView>
                </MUView>

              </MUView>
              <MUView>
                <MUButton
                  beaconId="applySettlecloseWeb"
                  type="primary"
                  className="successButton"
                  onClick={this.closeWeb.bind(this)}
                >
                  返回首页
                </MUButton>
              </MUView>
            </MUView>
          ) : (
            <MUView className="settle-result-top">
              <MUView className="status-img-container">
                <MUImage className="status-img" src={ICON_REJECT} />
              </MUView>
              <MUView className="status-text">发送失败</MUView>
              <MURichText className="status-tips" nodes="因证明开具异常，邮件发送失败，请稍后重试" />
              <MUButton
                beaconId="retrySendButton"
                type="primary"
                className="result-btn"
                circle
                onClick={() => {
                  const { redirectUrl, pageType } = this.$router.params;
                  const params = new URLSearchParams({
                    ...mapping.jumpParams,
                    ...pageType === 'settleAndLoanProof' ? { type: 'settleAndLoanProof' } : {},
                    email,
                    redirectUrl: redirectUrl || ''
                  });
                  Madp.navigateTo({
                    url: `${mapping.jumpPath}?${params.toString()}`
                  });
                }}
              >
                重新发送
              </MUButton>
            </MUView>
          )}
        </MUView>
        <MUSlogan onlyLogo className="pageSlogan" />
        <OpRepayment pageId={settleProofSuccessId} opEventKey="opPageEnter" />
      </MUView>
    );
  }
}
