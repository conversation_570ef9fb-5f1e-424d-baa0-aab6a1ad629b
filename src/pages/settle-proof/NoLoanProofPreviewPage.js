/* eslint-disable import/first */
/* eslint-disable eqeqeq */
/* eslint-disable no-return-assign */
import { Component } from '@tarojs/taro';
import {
  MUView, MUButton, MURichText, MUNavBar
} from '@mu/zui';
import Madp from '@mu/madp';
import { track } from '@mu/madp-track';
import './index.scss';
import Dispatch from '@api/actions';
import { ChatEntry } from '@mu/chat-entry-component';
import { isMuapp, isAlipay, isWechat, isCmb } from '@mu/madp-utils';
import contractApi from './contract_utils/contractApi';
import { STORE } from './store';
import getCurrentDateTimeInFormat from './utils';
import './contract.scss';
import { PROOF_TYPES } from './constants';

const isH5 = process.env.TARO_ENV === 'h5';

@track({}, {
  pageId: 'NoLoanProofPreviewPage',
  dispatchOnMount: true,
})

export default class NoLoanProofPreviewPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      htmlContents: ''
    };
    this.isChatChn = false; // 是否显示客服入口
    if (!isH5 || isAlipay() || isMuapp() || isWechat()) {
      this.isChatChn = true;
    }
  }

  async componentDidMount() {
    const { dateTime } = getCurrentDateTimeInFormat();
    const { ret, data } = await Dispatch.repayment.queryContractInfo({
      scene: 'PREVIEW',
      interfaceVersion: '3.0',
      contractCode: STORE.NoloanProofContractCode,
      contractPreviewData: { baseContractInfo: { dateNow: dateTime } }
    }) || {};
    const { contractList } = data || {};
    if (ret === '0') {
      const htmlTemplate = await contractApi.handleResult(contractList && contractList[0], {}, {});
      const htmlFile = await contractApi.getHtmlBodyContent(htmlTemplate);
      this.setState({
        htmlContents: htmlFile
      });
    }
  }

  // eslint-disable-next-line react/sort-comp
  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '无贷款证明' });
  }

  gotoEmail() {
    const { email = '' } = this.$router.params || {};
    Madp.navigateTo({
      url: `/pages/settle-proof/VerifyEmailPage?email=${email}&showType=${PROOF_TYPES.NO_LOAN}`
    });
  }

  render() {
    const { htmlContents } = this.state;
    let naviBar;
    if (isCmb()) {
      naviBar = (
        <MUNavBar
          beaconId="NavBar"
          title="无贷款证明"
          leftArea={[
            {
              type: 'icon',
              value: 'back',
            }
          ]}
        />
      );
    }
    return (
      <MUView className="noLoanProofPreviewPage">
        {naviBar}
        <MUView className="text">由于您未进行过任何借款，无需开具结清证明，您可选择开具如下的无贷款证明：</MUView>
        <MURichText nodes={htmlContents} className="rich-text" />
        <MUButton type="primary" className="button" beaconId="bottomBut" onClick={this.gotoEmail.bind(this)}>
          发送至我的邮箱
        </MUButton>
        {this.isChatChn && (
          <ChatEntry
            extraParam={{ needLogin: 1 }}
            busiEntrance={isH5 ? '' : encodeURIComponent('repayment/#/pages/settle-proof/NoLoanProofPreviewPage')}
          />
        )}
      </MUView>
    );
  }
}
