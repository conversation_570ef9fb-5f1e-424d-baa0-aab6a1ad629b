export const PROOF_TYPES = {
  /**
     * 结清证明
     */
  SETTLE: 0,
  /**
     * 贷款证明
     */
  LOAN: 1,
  /**
     * 无贷款证明
     */
  NO_LOAN: 2,
};

export const PROOF_TYPE_MAPPING = {
  [PROOF_TYPES.SETTLE]: {
    navTitle: '结清证明',
    emailTitle: '贷款结清',
    jumpPath: '/pages/settle-proof/SelectIOUPage',
    jumpParams: { type: 'settleProof', show: 0 }
  },
  [PROOF_TYPES.LOAN]: {
    navTitle: '贷款证明',
    emailTitle: '贷款',
    jumpPath: '/pages/settle-proof/SelectIOUPage',
    jumpParams: { type: 'loanProof', show: 0 }
  },
  [PROOF_TYPES.NO_LOAN]: {
    navTitle: '无贷款证明',
    emailTitle: '无贷款',
    jumpPath: '/pages/settle-proof/NoLoanProofPreviewPage',
    jumpParams: {}
  }
};