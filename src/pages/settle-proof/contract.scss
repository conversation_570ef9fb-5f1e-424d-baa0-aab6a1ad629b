.contractDiv {
  min-height: 100%; //坑爹的uc，不支持vh
  min-height: calc(100vh);
  width: 100%;
  height: 100vh;
  -webkit-overflow-scrolling: touch;
  overflow-y: scroll;
}

.contract-view {
  background-color: #fff;
}

//重写taro解决ios上不能滑动
.taro-webview {
  position: relative;
  width: 100%;
  height: 100%;
  border: 0;
  margin: 0;
}

// 给表格第一行设置宽度
tr td:first-child {
  width: 35%;
}

.agreement-popup .navbar {
  display: block !important;
}

.agreement-popup .page-content {
  padding-top: 78px !important;
  /* px */
}

.agreement-content {
  padding: 0 30px;
  font-size: 32px;
  color: #333333;

  .text-no-indent {
    text-indent: 0;
  }

  .text-underline {
    text-decoration: underline;
  }

  .text-right {
    text-align: right;
  }

  .text-center {
    text-align: center;
    text-indent: 0;
  }

  p {
    padding-bottom: 20px;
    margin: 0;
    text-indent: 2em;

    &.font-l {
      font-size: 44px;
    }
  }

  .agreement-title {
    padding-top: 20px;
    text-align: center;
    text-indent: 0;
    font-weight: bold;
  }

  .checkbox {
    margin-right: 10px;
  }

  input[type=checkbox] {
    vertical-align: text-bottom;
    border: 0;
    margin: 0;
    width: 16PX;
    height: 16PX;
    -webkit-appearance: none;
    appearance: none;
    background: url("./imgs/icon_uncheck_2x.png") no-repeat;
    background-size: contain;
  }

  input[type=checkbox]:checked {
    background-image: url("./imgs/icon_check_2x.png");
  }
}

.agreement-table table {
  margin-bottom: 20px;
  border-collapse: collapse;
  border: 1PX solid #333333;

  td {
    padding: 10px;
    border: 1PX solid #333333;
    /* no */
  }
}

.union-loan-table {
  .agreement-table table {
    td {
      &:first-child {
        width: 30%;
      }

      &:last-child {
        width: 70%;
      }
    }
  }
}

.font-small {
  font-size: small;
}

.font-medium {
  font-size: medium;
}

.font-smaller {
  font-size: smaller;
}

.contract-content {
  min-height: 100%;
  min-height: calc(100vh);
  background-color: #fff;
}



// 以下是新合同接口的前置样式，由于小程序去除了样式，故而写一份在这里
// 小程序样式开始处
.weapp-class {
  .agreement-popup .navbar {
    display: block !important;
  }

  .agreement-popup .page-content {
    padding-top: 78px !important;
    /* px */
  }

  .agreement-content {
    padding: 0 30px;
    font-size: 32px;
    color: #333333;

    .text-no-indent {
      text-indent: 0;
    }

    .text-underline {
      text-decoration: underline;
    }

    .text-right {
      text-align: right;
    }

    .text-center {
      text-align: center;
      text-indent: 0;
    }

    p {
      padding-bottom: 20px;
      margin: 0;
      text-indent: 2em;

      &.font-l {
        font-size: 44px;
      }
    }

    .agreement-title {
      padding-top: 20px;
      text-align: center;
      text-indent: 0;
      font-weight: bold;
    }

    .checkbox {
      margin-right: 10px;
    }

    input[type=checkbox] {
      vertical-align: text-bottom;
      border: 0;
      margin: 0;
      width: 16px;
      height: 16px;
      -webkit-appearance: none;
      appearance: none;
      background: url("./imgs/icon_uncheck_2x.png") no-repeat;
      background-size: contain;
    }

    input[type=checkbox]:checked {
      background-image: url("./imgs/icon_check_2x.png");
    }
  }

  .agreement-table table {
    margin-bottom: 20px;
    border-collapse: collapse;
    border: 1PX solid #333333;

    td {
      padding: 10px;
      border: 1PX solid #333333;
      /* no */
    }
  }

  .union-loan-table {
    .agreement-table table {
      td {
        &:first-child {
          width: 30%;
        }

        &:last-child {
          width: 70%;
        }
      }
    }
  }

  .font-small {
    font-size: small;
  }

  .font-medium {
    font-size: medium;
  }

  .font-smaller {
    font-size: smaller;
  }

  .contract-content {
    min-height: 100%;
    min-height: calc(100vh);
    background-color: #fff;
  }

  p {
    max-height: 100%;
    -webkit-text-size-adjust: none;
  }



  .p-weapp {
    padding-bottom: 40px;
    margin: 0;
    text-indent: 2em;
  }


  // 以下是表格样式 
  .weapp-table {
    border-bottom: 1PX solid #333333;
    left: 0;
    right: 0;
    margin: auto;
    width: 96%;
    margin-bottom: 40px;
  }

  .weapp-tr {
    border-collapse: collapse;
    display: flex;
    width: 100%;
    border-top: 1PX solid #333333;
    border-left: 1PX solid #333333;
    box-sizing: border-box;
  }

  .weapp-td-1-1 {
    width: 100%;
    border-right: 1PX solid #333333;
    padding: 10px;
    display: -webkit-box;
    /* flex弹性布局 */
    -webkit-box-align: center;
    -webkit-box-pack: center;
  }

  .weapp-td-2-1 {
    width: 25%;
    border-right: 1PX solid #333333;
    padding: 10px 0;
    display: -webkit-box;
    /* flex弹性布局 */
    -webkit-box-align: center;
    -webkit-box-pack: center;
  }

  .weapp-td-2-2 {
    width: 75%;
    border-right: 1PX solid #333333;
    padding: 10px;
    display: -webkit-box;
    /* flex弹性布局 */
    -webkit-box-align: center;
    -webkit-box-pack: center;
  }

  .weapp-td-4-1,
  .weapp-td-4-2,
  .weapp-td-4-3,
  .weapp-td-4-4 {
    width: 25%;
    border-right: 1PX solid #333333;
    padding: 10px 0;
    display: -webkit-box;
    /* flex弹性布局 */
    -webkit-box-align: center;
    -webkit-box-pack: center;
  }

  // input 标签不显示的 替代样式
  .input-weapp {
    width: 32px;
    height: 32px;
    background: url("./imgs/icon_uncheck_2x.png") no-repeat;
    background-size: contain;
  }

  .input-weapp-checked {
    width: 32px;
    height: 32px;
    background: url("./imgs/icon_check_2x.png") no-repeat;
    background-size: cover;
  }

  .text-no-indent {
    text-indent: 0;
  }
}

// 小程序样式结尾处

a {
  word-wrap: break-word;
}

.contract_third{
  min-height: 100%; 
  min-height: calc(100vh);
  height: 100vh;
  -webkit-overflow-scrolling: touch;
  overflow-y: scroll;
  padding: 0 30px;
  padding-top: 60px;
  font-size: 32px;
  color: #333333;
  box-sizing: border-box;

  p {
    padding-bottom: 20px;
    margin: 0;
    text-indent: 2em;
  }

  ol {
    padding-left: 20px;
  }
}