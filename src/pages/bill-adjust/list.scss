@import '../../components/weapp/index.scss';

.notice-dialog {
  .mu-dialog__container {
    width: 640px;
    padding-bottom: 100px;
  }

  .content {
    padding: 0 40px;
  }

  &-content {
    color: #888;
    text-align: left;
    font-size: 32px;
    padding: auto 20px;
  }

  &-remark {
    color: #888;
    text-align: left;
    font-size: 28px;
  }

  &-btn {
    position: absolute;
    border-radius: 0;
    border: none;
    border-top: 1px solid #E5E5E5;
    left: 0;
    bottom: 0;
    width: 100%;
  }
}

.theme-background-color {
  background: $color-brand;
}

.notice-bar-theme {
  background-color: #FFEEDC;

  .at-noticebar__content-icon,
  .at-noticebar__content-text,
  .at-noticebar__more {
    color: #FE7F05;
  }
}
