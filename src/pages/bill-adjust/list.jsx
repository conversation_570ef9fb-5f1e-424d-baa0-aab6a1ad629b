/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
import Taro, { Component } from '@tarojs/taro';
import { MUView, MUNoticebar } from '@mu/zui';
import Madp from '@mu/madp';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { Url } from '@mu/madp-utils';
import { getStore, setStore } from '@api/store';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import { couponFilter } from '@utils/repay-util';
import { miniProgramChannel } from '@utils/constants';
// import { filterQualCoupons } from '@utils/repay-util';
import CustomConfig from '@config/index';
import pageHoc from '@utils/pageHoc';
import BillList from '@components/bill-list';
import EmptySign from '@components/empty-sign';
import RepayModal from '@components/repay-modal/index';
import './list.scss';

// import '@components/repay-modal/index.scss';
// import '@components/bill-list/index.scss';
// import '@components/list-item/index.scss';
// import '@components/empty-sign/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/repay-modal/index.scss');
  require('@components/bill-list/index.scss');
  require('@components/list-item/index.scss');
  require('@components/empty-sign/index.scss');
}

const themeColor = Util.getThemeColor(CustomConfig.theme);
@track({ event: EventTypes.PO }, {
  pageId: 'AdjustList',
  dispatchOnMount: true,
})
@pageHoc({ title: '延长诚信保护期' })
export default class AdjustList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showPage: false, // 页面是否展示
      awardInfoList: [], // // 优惠券信息
      selectedBillList: [],
      showNoticeDialog: false
    };
    this.adjustList = [];
    this.extendGracePeriodCount = 3; // 延长诚信保护期天数
    this.supplySuccess = Url.getParam('supplySuccess') || '';
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
  }

  async componentDidMount() {
    // 若是补充身份信息且成功回来的，则跳转到补充联系人页面
    if (this.supplySuccess === '1') {
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'UpdateIDCardSuccess' });
      if (this.miniChannelFlag) {
        Taro.redirectTo({
          url: '/pages/identity/contact?billType=adjust'
        });
      } else {
        Util.router.replace({
          path: '/pages/identity/contact',
          query: {
            billType: 'adjust',
          }
        });
      }
    }
    this.initEvent();
  }

  config = {
    navigationBarTitleText: '延长诚信保护期'
  }

  // 初始化事件 获取初始化数据
  // eslint-disable-next-line react/sort-comp
  async initEvent() {
    this.adjustList = await Dispatch.repayment.getAdjustBills();
    const selectedBillList = this.adjustList.filter(({ orderNo }) => orderNo);
    this.setState({ selectedBillList });
    this.getQueryCouponList();
  }

  // 获取优惠券信息
  async getQueryCouponList() {
    let couponList = await Dispatch.repayment.getCouponList(Util.getCouponQuery());
    // awardType  105   specialScene SSA04 博弈催收 延长宽限期资格券；awardType  303   specialScene SSA06 延长宽限期（客服机器人） 延长宽限期资格券
    couponList = couponFilter(couponList, [{ awardType: '105', specialScene: 'SSA04' }, { awardType: '303', specialScene: 'SSA06' }]);
    // const { awardDetailList = [] } = await Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }) || {};
    // let { usableQualConponList: couponList = [] } = filterQualCoupons(awardDetailList) || {};
    // couponList = couponList.filter((item) => ((item.awardType === '105' && item.subUseSceneCode === 'SSA04') || (item.awardType === '303' && item.subUseSceneCode === 'SSA06')));
    // console.log('enter couponList', couponList);
    // 资格券不存在，提示退出页面
    if (!couponList || couponList.length < 1) {
      Madp.showModal({
        content: '抱歉，您暂无延长诚信保护期资格，请按期还款。',
        showCancel: false,
        confirmText: '返回',
        confirmColor: themeColor,
        success: (res) => {
          if (res.confirm) {
            Madp.navigateBack();
          }
        }
      });
      return;
    }
    this.extendGracePeriodCount = couponList[0].extendGracePeriodCount || 3;
    // this.extendGracePeriodCount = couponList[0].awardAmt || 3;
    this.setState({
      showPage: true,
      awardInfoList: couponList
    }, () => {
      setStore({ awardInfoList: couponList }); // 存优惠券信息
    });
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '延长诚信保护期' });
  }

  handleSelect(bill) {
    // 由于存在同笔借据，两个账单同时展示的情况，不能单纯用orderNo作为区分
    // 故直接用账单本身判断是否勾选
    const { selectedBillList } = this.state;
    const billIndex = selectedBillList.indexOf(bill);
    if (billIndex > -1) {
      // 取消选中，同时取消选中同属一个订单号的账单
      const oriSelectdBillList = [...selectedBillList];
      oriSelectdBillList.forEach((item) => {
        // 遍历所有选中的账单（包含了当前取消的bill）
        if (item.orderNo === bill.orderNo) {
          // 订单号相同，获取其在实际选中账单中的index，删掉
          const cancelIndex = selectedBillList.indexOf(item);
          selectedBillList.splice(cancelIndex, 1);
        }
      });
    } else {
      // 选中，同时选中同属一个订单号的账单
      this.adjustList.forEach((item) => {
        // 订单号相同则加上（包含了当前点选的bill）
        if (item.orderNo === bill.orderNo) {
          selectedBillList.push(item);
        }
      });
    }
    this.setState({ selectedBillList });
  }

  async submitAdjust() {
    const { selectedBillList, awardInfoList } = this.state;
    const orderList = [];
    selectedBillList.forEach((bill) => {
      if (orderList.indexOf(bill.orderNo) === -1) {
        orderList.push(bill.orderNo);
      }
    });

    // 配置开关
    let adjustRepayInfo = {
      awardInfoList, // 券
      openScene: 'S01',
      functionType: awardInfoList && awardInfoList.length && awardInfoList[0].awardType, // 传券类型
      delayOrderList: orderList
    };
    setStore({ adjustRepayInfo });
    const { authInfoDetails } = await Dispatch.repayment.checkSupplyInfo('delay_repay');
    const isNeedSupplyID = !!authInfoDetails && authInfoDetails.filter((process) => process.authParamType === 'COMPENSATE_ID_INFO').length;
    if (isNeedSupplyID) {
      const supplyParams = {
        scene: 'SCENE_DELAY_GRACE',
        billType: 'adjust',
      };
      Util.gotoSupplyInfo(supplyParams);
    } else {
      // 不做身份证做补充联系人
      Util.router.push({
        path: '/pages/identity/contact',
        query: {
          type: 'adjust',
          billType: 'adjust'
        }
      });
    }
  }

  render() {
    const { showPage, selectedBillList, showNoticeDialog } = this.state;
    if (!showPage) {
      return <MUView />;
    }
    const noticeText = `所选账单的本期诚信保护期将延长至${this.extendGracePeriodCount}天。${this.extendGracePeriodCount}日内按正常利率计息，期间完成还款不影响个人征信；若${this.extendGracePeriodCount}日内未完成还款，将从次日开始计算罚息，并上报人行征信。延长诚信保护期仅对所选账单生效，请密切关注你的待还账单中，各笔账单的还款情况。`;
    return (
      <MUView className="pages-bg">
        <MUNoticebar
          beaconId="NoticeBar"
          marquee
          showMore
          single
          icon="notice"
          onTextClick={() => this.setState({ showNoticeDialog: true })}
          className={themeColor === '#E60027' ? 'notice-bar-theme' : ''}
        >
          {noticeText}
        </MUNoticebar>
        {this.adjustList && this.adjustList.length > 0 ? (
          <BillList
            billList={this.adjustList}
            billType="adjust"
            selectedBillList={selectedBillList}
            onSelect={(bill) => this.handleSelect(bill)}
            submitRepayment={() => this.submitAdjust()}
            cancelAll={() => this.setState({ selectedBillList: [] })}
            selectAll={() => this.setState({ selectedBillList: [...this.adjustList] })}
          />
        ) : <EmptySign emptyText="暂无可延长诚信保护期账单" />}
        <RepayModal
          title="延长诚信保护期"
          className="notice-dialog"
          isOpened={showNoticeDialog}
          closeOnClickOverlay={false}
          beaconId="NoticeDialog"
          confirmText="我知道了"
          onConfirm={() => this.setState({ showNoticeDialog: false })}
        >
          <MUView>
            <MUView className="notice-dialog-content">{noticeText}</MUView>
          </MUView>
        </RepayModal>
      </MUView>
    );
  }
}
