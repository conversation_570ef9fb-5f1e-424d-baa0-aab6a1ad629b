/* eslint-disable prefer-destructuring */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import {
  MUView, MUText, MUIcon, MUModal, MUButton, MUNavBarWeapp
} from '@mu/zui';
import { Url } from '@mu/madp-utils';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import Madp from '@mu/madp';
import Util from '@utils/maxin-util';
import pageHoc from '@utils/pageHoc';
import DetailItem from '@components/detail-item';
import { getStore } from '@api/store';
import Dispatch from '@api/actions';
import './index.scss';
import '@components/detail-item/index.scss';
import { urlDomain } from '@utils/url_config';

// import '@components/repay-modal/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@components/repay-modal/index.scss')
}

@track({ event: EventTypes.PO }, {
  pageId: 'RepayDetail',
  dispatchOnMount: true,
})
@pageHoc({ title: '账单详情' })
export default class RepayDetail extends Component {
  config = {
    navigationBarTitleText: '账单详情',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor() {
    super();
    this.billType = 'total';
  }

  state = {
    billDetail: {},
    repayBillPlanList: [],
    extendTips: false
  };

  // /*
  //   * 专享提前还款违约金减免渠道文案
  //   */
  // get saveStrategyChannelName() {
  //   const billDetail = getStore('currentBillDetail');
  //   const channelCode = this.allRepayOrders.channelCode;
  //   if (billDetail.canRepay === true
  //     && billDetail.waivePrepayReasonType === '0'
  //     && channelCode
  //     && billDetail.waivePrepayAmount > 0) { // 价格洼地可提前还款且可减免渠道且减免金额>0
  //     return `${this.channelMap[channelCode]}专享`;
  //   }
  //   return '';
  // },

  componentDidMount() {
    const billDetail = getStore('currentBillDetail');
    this.billType = Url.getParam('billType') || 'total';
    if (this.billType === 'total') dispatchTrackEvent({ target: this, event: EventTypes.PO, beaconId: 'Advanced' });
    else if (this.billType === '7days'
      || this.billType === 'future') dispatchTrackEvent({ target: this, event: EventTypes.PO, beaconId: 'Near' });
    if (this.billType === 'total' || this.billType === 'reserve-near' || this.billType === 'extend') {
      this.getListBillRepayPlan();
    } else if (this.billType === 'advanced-stage') {
      this.getStageBillRepayPlan();
    } else if (this.billType === 'total-modify') { // 变更还款日试算的
      this.getPepayPlanFromBill();
    }
    this.setState({
      billDetail,
    });
  }

  componentDidShow() {
    // 最好放在didshow里，不然跳转外部模块回来后title会变
    Madp.setNavigationBarTitle({ title: '账单详情' });
  }

  async getListBillRepayPlan() {
    const billDetail = getStore('currentBillDetail');
    const { orderInfos } = await Dispatch.repayment.queryRepayPlan({
      orderNo: billDetail.orderNo,
      queryType: '2'
    }) || {};
    // planStatus === 01表示已结清 无需展示
    this.setState({
      repayBillPlanList: orderInfos && orderInfos[0] && orderInfos[0].repayScheduleList
        ? orderInfos[0].repayScheduleList : [],
      // repayBillOneTimeFee: orderInfos && orderInfos[0] && orderInfos[0].oneTimeFee ? orderInfos[0].oneTimeFee : ''
    });
  }

  getStageBillRepayPlan() {
    const billDetail = getStore('currentBillDetail');
    const { extendedRepayPlanList } = billDetail;
    this.setState({
      repayBillPlanList: extendedRepayPlanList || [],
    });
  }

  /**
   * 根据列表类型确定不同的待还账单描述
   * @return { String }
   */
  get sumDesc() {
    const billDetail = getStore('currentBillDetail');
    if (!billDetail && !billDetail.loanDate) return '';
    let desc = '';
    if (this.billType === '7days' || this.billType === 'reserve-near' || this.billType === 'future') {
      desc = `${Util.dateFormatter(billDetail.payDate)}还第${billDetail.installCnt}/${billDetail.installTotalCnt}期`;
    } else if (this.billType === 'total' || this.billType === 'reserve-total' || this.billType === 'extend') {
      desc = `${Util.dateFormatter(billDetail.loanDate)}${billDetail.businessType}${billDetail.installTotalAmt}元`;
    } else if (this.billType === 'advanced-stage') {
      desc = `${Util.dateFormatter(billDetail.loanDate)}${billDetail.businessType}${billDetail.installTotalAmt}元`;
    } else if (this.billType === 'stages') {
      desc = `${Util.dateFormatter(billDetail.loanDate)}${billDetail.businessType}${billDetail.surplusPayTotalAmt}元`;
    }
    return desc;
  }

  get sumTitle() {
    if (['total', 'advanced-stage', 'reserve-total', 'extend'].includes(this.billType)) {
      return '全部应还(元)';
    }
    return '本期应还(元)';
  }

  get subDesc() {
    const billDetail = getStore('currentBillDetail');
    let desc = '';
    if (billDetail.displayOverdueDays > 0) {
      desc = '请及时还款，避免产生不良信用记录';
    }
    return desc;
  }

  get amount() {
    const billDetail = getStore('currentBillDetail');
    let showAmount = billDetail.surplusPayTotalAmt;
    if (this.billType === 'advanced-stage') showAmount = billDetail.extendedRepayTotalAmt;
    return showAmount;
  }

  getRepayPlanNodes() {
    const { repayBillPlanList, billDetail } = this.state;

    // 省心分不展示还款计划逾期
    return repayBillPlanList.map((plan, index) => (
      <DetailItem
        plan={plan}
        index={index + 1}
        listLength={repayBillPlanList.length}
        overdue={this.billType !== 'advanced-stage' && plan.surplusDays && plan.surplusDays < -3}
        title={plan.repayDate}
        loanType={billDetail.loanType}
        value={`${plan.surplusRepayAmt}元`}
        billType={this.billType}
        showCreditProductInfo="Y"
      />
    ));
  }

  /**
   * 变更还款日试算从借据中直接获取还款试算计划
   */
  getPepayPlanFromBill() {
    const billDetail = getStore('currentBillDetail');
    this.setState({
      repayBillPlanList: billDetail.repayPlanList,
    });
  }

  toLoanInfo = () => {
    const billDetail = getStore('currentBillDetail');
    const url = process.env.TARO_ENV === 'h5'
      ? `${urlDomain}/${Madp.getChannel()}/traderecords/#/pages/ious/index?orderNo=${billDetail.orderNo}`
      : `/traderecords/pages/ious/index?orderNo=${billDetail.orderNo}`;
    Madp.navigateTo({
      url
    });
  }


  render() {
    const {
      billDetail,
      repayBillPlanList,
      extendTips,
      // repayBillOneTimeFee
    } = this.state;

    // 分期手续费
    let stagesFee = billDetail.surplusPayInteAmt || '0.00';
    let originAmt = billDetail.surplusPayPrincipalAmt || '0.00';
    let periodFee = billDetail.surplusPayPeriodFeeAmt || '0.00';
    let fineAmt = billDetail.surplusPayFineAmt || '0.00';
    let oneTimeFee = billDetail.surplusPayOnetimeFeeAmt || '0.00';
    let payPrepayFee = billDetail.payPrepayFee || '0.00';
    let showPayFineAmt
      = Number(billDetail.surplusPayFineAmt || '0.00') + Number(billDetail.waivePayFineAmt || '0.00');
    let showPayOnetimeFeeAmt
      = Number(billDetail.surplusPayOnetimeFeeAmt || '0.00') + Number(billDetail.waivePayOnetimeFee || '0.00');
    let showPrepayFee = Number(billDetail.payPrepayFee || '0.00') + Number(billDetail.waivePayPrepayFee || '0.00');
    const showRepayPlacList = ['total', 'advanced-stage', 'reserve-total', 'total-modify', 'extend']
      .includes(this.billType);
    let businessType = billDetail.businessType;
    if (this.billType === 'advanced-stage') {
      // 省心分不展示息费减免的东西
      stagesFee = billDetail.extendedRepayInteAmt || '0.00';
      originAmt = billDetail.extendedRepayCapitalAmt || '0.00';
      periodFee = billDetail.extendedRepayFee || '0.00';
      fineAmt = billDetail.extendedRepayFineAmt || '0.00';
      oneTimeFee = billDetail.extendedRepayOnetimeFee || '0.00';
      showPayFineAmt = Number(billDetail.extendedRepayFineAmt || '0.00');
      showPayOnetimeFeeAmt = Number(billDetail.extendedRepayOnetimeFee || '0.00');
      payPrepayFee = '0.00'; // 省心分不展示提前还款手续费
      showPrepayFee = 0; // 省心分不展示提前还款手续费
      businessType = '省心分';
    } else if (this.billType === 'extend') {
      showPrepayFee = 0; // 延后还款日 不展示提前还款违约金
    }
    const loanType = billDetail.loanType;
    const dueDaysFlag = (this.billType !== 'total'
      && this.billType !== 'advanced-stage'
      && this.billType !== 'reserve-total')
      && billDetail.displayOverdueDays;
    const adjustFlag = (this.billType !== 'total'
      && this.billType !== 'advanced-stage'
      && this.billType !== 'reserve-total')
      && billDetail.displayOverdueDays > 3
      && billDetail.overdueDays === 0;

    return (
      <MUView>
        <MUNavBarWeapp
          className="loan-navbar"
          title="账单详情"
          leftArea={[
            {
              type: 'icon',
              value: 'back'
            }
          ]}
        />
      <MUView className="repay-detail">
        <MUView className="repay-detail-top">
          <MUView className="repay-detail-title">还款计划</MUView>
          <MUView className="repay-detail-header">
            {`${repayBillPlanList.filter((plan) => (plan.planStatus && plan.planStatus !== '01') || (plan.payFlag && plan.payFlag === '2')).length}期待还`}
            <MUView
              beaconId="clickBillDetail"
              className="header-right"
              onClick={this.toLoanInfo}
            >
              查看借据
              <View>
                <MUIcon className="detail-icon" value="jump-cicle" size="16" style={process.env.TARO_ENV !== 'weapp' && { 'margin-left': '10px' }} />
              </View>
            </MUView>
          </MUView>
        </MUView>
        {showRepayPlacList && repayBillPlanList && repayBillPlanList.length > 0 && (
          <MUView className="repay-detail-plan">
            {this.getRepayPlanNodes()}
          </MUView>
        )}
        <MUModal
          beaconId="RepayPlanDetail"
          isOpened={extendTips}
          onClose={() => this.setState({ extendTips: false })}
        >
          <MUView className="dialog">
            <MUView className="title">借据延期说明</MUView>
            <MUView className="content">
              <MUView className="repay-detail-modal-content">
                <MUView className="repay-detail-modal-content-top">
                  借据申请延期后，该借据所有未还清期数的还款日会延后一个月，同一笔借据只能延期一次。
                </MUView>
                <MUView>
                  <MUText>*借据延期后规则说明：</MUText>
                  <MUView>1、延期借据会根据新还款日重新生成还款计划；</MUView>
                  <MUView>2、新还款日前还清账单不影响征信。</MUView>
                </MUView>
              </MUView>
            </MUView>
            <MUView className="btns">

              <MUButton
                className="btn-confirm"
                type="primary"
                beaconId="RepayPlanDetailConConfirm"
                onClick={() => this.setState({ extendTips: false })}
              >
                我知道了
              </MUButton>
            </MUView>
          </MUView>
        </MUModal>
      </MUView>
      </MUView>
    );
  }
}
