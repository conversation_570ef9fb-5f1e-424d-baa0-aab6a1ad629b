@import '../../components/weapp/index.scss';

.repay-detail {
  min-height: 100vh;
  background-color: #fff;
  height: 100%;
  .repay-detail-top{
    background-color: #F3F3F3;
    // min-height: 100vh;
    padding: 48px 30px 40px 30px;
    .repay-detail-title {
      margin-bottom: 20px;
      width: 250px;
      height: 48px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 48px;
      color: #333333;
      line-height: 48px;
      text-align: left;
    }

    .repay-detail-header {
      display: flex;
      justify-content: space-between;
      font-size: 30px;
      line-height: 30px;
      color: #808080;
      .header-right {
        display: flex;
        align-items: center;
        .detail-icon {
          width: 30px;
          height: 30px;
          margin-left: 10px;
        }
      }
      
    }
  }
  
  .repay-detail-content {
    padding: 20px 30px;
    background-color: #fff;
    border-top: 1px solid #F1F1F1;

    .repay-detail-item {
      display: flex;
      justify-content: space-between;
      padding: 20px 0;
      color: #333333;
      font-size: 29px;
      line-height: 30px;
    }
  }

  .repay-detail-plan {
    margin-top: 20px;
    background-color: #fff;
    color: #333333;
    font-size: 32px;

    .repay-detail-plan-item {
      display: flex;
      font-size: 32px;
      height: 100px;
      padding: 0 30px;
      line-height: 100px;
      color: #808080;
      justify-content: space-between;
      border-bottom: 1px solid #F1F1F1;

      &:first-child {
        color: #333;
      }

      .item-right {
        display: flex;
        align-items: center;
      }

      .clock-icon {
        display: inline-block;
        width: 34px;
        height: 34px;
        margin-right: 10px;
        background-image: url('./img/ic_clock.png');
        background-size: contain;
      }

      &-icon {
        margin-left: 16px;
      }
    }
  }

  .dialog {
    padding: 0 40px;

    .title {
      font-size: 36px;
      font-weight: bold;
      text-align: center;
    }

    .content {
      margin-top: 30px;
      font-size: 28px;
      color: #808080;
    }

    .btns {
      margin: 30px 0;

      .btn-confirm {
        font-size: 36px;
        border-radius: 0;
      }

      .btn-cancel {
        font-size: 28px;
        color: #A6A6A6;
        border: none;
      }
    }
  }

  .repay-detail-modal {
    &-content {
      text-align: left;

      &-top {
        margin-bottom: 36px;
      }
    }
  }
}
.loan-navbar {
  .mu-nav-bar-weapp__center {
    font-weight: 400 !important;
  }
}
