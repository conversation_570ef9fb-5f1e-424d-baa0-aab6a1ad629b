import Madp from '@mu/madp';
import {
  MUView, MUInput, MUButton, MUModal, MUAuthCodeInput
} from '@mu/zui';
import { Component } from '@tarojs/taro';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import { startGeetest } from '@mu/geestest';
import pageHoc from '@utils/pageHoc';
import { isWechat, Validator, Url } from '@mu/madp-utils';
import { setStore } from '@api/store';
import {
  loginOut,
} from '../qrcode-repay/utils/requests';
import { platLogin } from '@utils/login';

import './index.scss';
@track({ event: EventTypes.PO }, {
  pageId: 'RepayForOthers',
  dispatchOnMount: true,
})
// 放在track后面，不然track好像会被覆盖，报页面id未配置
@pageHoc({ title: '代还款' })
export default class RepayForOthers extends Component {
  constructor(props) {
    super(props);
    this.state = {
      userPhoneNumInput: '',
      othersPhoneNumInput: '',
      authCode: '',
      token: '',
      isOpened: false
    };
  }

  config = {
    navigationBarTitleText: '代还款'
  };

  async componentDidMount() {
    if (isWechat()) {
      if (!Madp.getStorageSync('OthersRepay_WexinLogin', 'SESSION')) {
        await loginOut({ scene: 'logout' });
        Madp.setStorageSync('OthersRepay_WexinLogin', 1, 'SESSION');
        platLogin();
      }
    }
  }

  async getOthersInfor() {
    const { userPhoneNumInput, othersPhoneNumInput, token } = this.state;
    const ret = await Dispatch.repayment.offerRepayHelp({
      helperMobile: userPhoneNumInput, // 代还人手机号
      debtorMobile: othersPhoneNumInput,
      smsToken: token,
      scene: 'SCENE_REPAYMENT'
    });
    if (ret && ret.canHelp === 'N') {
      this.setState({
        isOpened: true
      });
    } else {
      const { debtorName } = ret || {};
      const alipay = Url.getParam('alipay') || '';
      setStore({
        repayOthersInfo: {
          userPhoneNumInput,
          othersPhoneNumInput,
          debtorName,
          alipay,
        }
      });
      Util.router.push({
        path: '/pages/repay-others/confirm',
        query: { alipay }
      });
    }
  }

  userPhoneNumInputAction = (val) => {
    this.setState({
      userPhoneNumInput: val
    });
  }

  othersPhoneNumInputAction = (val) => {
    this.setState({
      othersPhoneNumInput: val
    });
  }

  authCodeInputAction = (val) => {
    this.setState({
      authCode: val
    });
  }

  onAuthCodeButtonClickAction = () => {
    this.authCodeAction();
    dispatchTrackEvent({ event: EventTypes.BC, beaconId: 'getAuthCode', target: this });
  }


  onConfirmButtonClickAction = () => {
    dispatchTrackEvent({ target: this, event: EventTypes.BC, beaconId: 'ConfirmBtn' });
    if (!this.checkForm()) return;
    this.verifySms();
  }

  async verifySms() {
    const { authCode, token } = this.state;
    const { ret } = await Dispatch.repayment.verifySms({ code: authCode, token });
    if (ret === '0') {
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'verifyAuthCodeSuccess', target: this });
      this.getOthersInfor();
    }
  }

  checkForm() {
    const { userPhoneNumInput, othersPhoneNumInput, authCode } = this.state;
    if (!Validator.isMobilePhoneNumber(userPhoneNumInput)
      || !Validator.isMobilePhoneNumber(othersPhoneNumInput)) {
      Util.toast('手机号码有误');
      return false;
    }
    if (!Validator.isSmsCode(authCode)) {
      Util.toast('验证码格式有误');
      return false;
    }
    return true;
  }


  async authCodeAction() {
    const { userPhoneNumInput } = this.state;
    if (!Validator.isMobilePhoneNumber(userPhoneNumInput)) {
      Util.toast('手机号码有误');
      return;
    }
    // 获取验证码token
    const geestestRet = await startGeetest({ scene: 'SCENE_REPAYMENT' });
    if (geestestRet && geestestRet.verifyResult === 1 && geestestRet.token) {
      const { data: { token } } = await Dispatch.repayment.sendSms({
        behaviorToken: geestestRet.token,
        mobile: userPhoneNumInput,
        scene: 'SCENE_REPAYMENT',
        verifyType: 'SMP',
      }); // 获取验证码token
      if (this.authCodeInputRef.startCountAction) {
        this.authCodeInputRef.startCountAction();
      }
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: 'sendAuthCodeSuccess', target: this });
      Madp.showToast({
        title: '验证码发送成功',
        icon: 'none',
        duration: 2000
      });
      this.setState({
        token
      });
    } else {
      return Promise.reject(geestestRet);
    }
  }

  render() {
    const {
      userPhoneNumInput, othersPhoneNumInput, authCode, isOpened
    } = this.state;
    return (
      <MUView className="pages-bg repayment-for-others">
        <MUInput
          name="phoneNum"
          beaconId="userPhoneInput"
          title="我的手机号"
          type="number"
          placeholder="请输入本人手机号"
          value={userPhoneNumInput}
          onChange={this.userPhoneNumInputAction}
        />
        <MUAuthCodeInput
          beaconId="AuthCodeInput"
          authButonDisabled={userPhoneNumInput}
          authCodePlaceHolder="请输入短信验证码"
          value={authCode}
          customRef={(ref) => { this.authCodeInputRef = ref; }}
          onChange={this.authCodeInputAction}
          onAuthCodeButtonClick={this.onAuthCodeButtonClickAction}
        />
        <MUInput
          name="phoneNum"
          beaconId="othersPhoneInput"
          title="借款人手机号"
          type="number"
          placeholder="请输入借款人手机号"
          value={othersPhoneNumInput}
          onChange={this.othersPhoneNumInputAction}
        />
        <MUButton
          beaconId="NextButton"
          type="primary"
          className="bottomConfirmButton"
          disabled={!userPhoneNumInput || !othersPhoneNumInput || !authCode}
          onClick={this.onConfirmButtonClickAction}
        >
          下一步
        </MUButton>

        <MUModal
          className="repayOthersError"
          beaconId="RepayOthersError"
          isOpened={isOpened}
          content="借款人手机号输入有误，或您未被设置为借款人的联系人，请联系借款人确认后重试。"
          confirmText="我知道了"
          onConfirm={() => this.setState({
            isOpened: false
          })}
        />
      </MUView>
    );
  }
}
