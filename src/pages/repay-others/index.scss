@import '../../components/weapp/index.scss';

.repayment-for-others {
  padding-bottom: 150px;
  .topView {
    background: #fff;
    margin-bottom: 20px;
  }

  .at-input__title {
    width: 250px;
    max-width: 250px;
  }

  .bottomConfirmButton {
    margin: 25px 30px;
  }

  .title {
    width: 100%;
    font-size: 30px;
    text-align: center;
    padding-top: 90px;
    color: #333;
  }

  .amountInput {
    text-align: center;
    color: #333;
    padding-bottom: 30px;

    input {
      font-size: 60px;
      border: none;
      text-align: center;
    }

    input:focus {
      outline: none;
    }

    &.at-input::after {
      border-bottom: 0;
    }
  }

  .repay-other-bubble {
    position: relative;
    z-index: 1;
    margin-top: -32px;
    padding-bottom: 50px;
  }

  .amountTip {
    position: relative;
    color: #808080;
    font-size: 28px;
    padding: 0 30px 40px;
    line-height: normal;
  }

  // .amountTip::after{
  //   content: "";
  //   position: absolute;
  //   transform-origin: center;
  //   box-sizing: border-box;
  //   pointer-events: none;
  //   top: auto;
  //   left: 0.64rem;
  //   right: 0;
  //   bottom: 0;
  //   transform: scaleY(0.5);
  //   border-bottom: 1PX solid #E5E5E5;
  // }

  .fixNumBtn {
    display: inline-block;
    font-size: 28px;
    color: #3477FF;
  }

  .repay-way {
    width: auto;
    height: 70px;
    margin: 0 105px;
    padding: 0 0 60px;
    display: flex;
    flex-direction: row;
    align-items: center;

    &-btn {
      position: relative;
      flex: 1;
      background-color: #F3F3F3;
      border-radius: 8px;
      color: #808080;

      &__text {
        font-size: 32px;
        line-height: 70px;
        text-align: center;
      }

      &__ticked {
        width: 56px;
        height: 56px;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }

    &-btn:nth-of-type(1) {
      margin-right: 20px;
    }
  }

  .hide-view {
    display: none;
  }

  .repayInfo {
    text-align: right;
  }

  .tips {
    background-color: #fff;
    font-size: 28px;
    padding: 40px 30px;
    margin-top: 24px;

    .tip {
      margin-bottom: 0 0 10px;
      font-size: 26px;
      color: #808080;
    }

    .tipsTitle {
      line-height: 28px;
      margin-bottom: 24px;

      .tipsImage {
        width: 30px;
        height: 30px;
        margin-right: 8px;
      }
    }
  }

  .repayOthersModal {
    .mu-modal__content {
      text-align: unset;
    }
  }

  .at-input {
    margin-bottom: unset;
  }

  .fee-reduce-rule {
    background: #FFF;
    .rules-title {
      text-align: center;
      padding-top: 40px;
      margin-bottom: 46px;
      font-size: 28px;
      color: #808080;
      line-height: 28px;
    }
    .rules-li {
      padding: 0 30px 50px;
      display: flex;
      justify-content: space-between;
      font-size: 32px;
      line-height: 32px;
    
      .color-amt {
        color: #FF8844;
      }

      &:nth-child(2) {
        .color-amt {
          font-size: 48px;
        }
      }
    }
  }
  .repayinfo {
    display: flex;
    height: 100px;
    font-size: 32px;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    color: #333;
    background: #FFFFFF;
    .check_but {
      color: #3477FF;
      margin-left: 40px;

      &.disabled {
        color: #A6A6A6;
      }
    }
  }
  .repayinfo-split {
    margin-left: 30px;
    transform: scaleY(0.5);
    /* stylelint-disable-next-line */
    border-bottom: 1PX solid #E5E5E5;
  } 
  
  .tipModal-text__highlight {
    color: #FE5B5E;
  }
  .mu-modal__container {
    .title {
      padding-top: 20px;
    }
  }
}
