/* eslint-disable react/sort-comp */
/* eslint-disable max-len */
import Madp from '@mu/madp';
import {
  MUView, MUInput, MUModal, MUText, MUImage
} from '@mu/zui';
import { Component } from '@tarojs/taro';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import pageHoc from '@utils/pageHoc';
import classNames from 'classnames';
import {
  isMuapp,
  isWechat,
  Url,
  Validator,
  debounce,
} from '@mu/madp-utils';
import { wechatAppId } from '@utils/url_config';
import Util from '@utils/maxin-util';
import appInfos from '@utils/app-config-infos';
import {
  check216Coupon, getFeeReduceCoupon, getRepayWaiveTrialTrans, feeReduceTrialApi,
  getCouponList, repayTransTrialApi,
} from '../qrcode-repay/utils/requests';
import { getDisplaySettleWaiveAmt } from '@utils/payMethod';
import { CouponSelector } from '@mu/coupon-selector';
import Statistic from '@components/statistic/index';
import RepayBubble from '@components/repay-bubble/index';
import WaiveDetailInfo from '../qrcode-repay/components/waiveDetailInfo';
import CustomConfig from '@config';
import Dispatch from '@api/actions';
import { getStore } from '@api/store';
import ChannelConfig from '@config/index';
import { getFeeInteRightInfo } from '@utils/repay-util';
import { errCodeToMsgMap } from '@utils/constants';
import icTicked from '@components/assets/img/icon_tick.png';
import icTickedRed from '@components/assets/img/icon_tick_red.png';

import './index.scss';

// import '@mu/coupon-selector/dist/styles/index.scss';
// import '@components/statistic/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@mu/coupon-selector/dist/styles/index.scss');
  require('@components/statistic/index.scss');
  require('@components/overpay-drawer/index.scss');
}

const themeColor = Util.getThemeColor(CustomConfig.theme);
const btnGroup = ['还到期', '提前结清'];
const tipIcon = 'https://file.mucfc.com/zlh/3/0/202305/2023051820232141dca4.png';

@track({ event: EventTypes.PO }, {
  pageId: 'RepayForOthersConfirm',
  dispatchOnMount: true,
})
// 放在track后面，不然track好像会被覆盖，报页面id未配置
@pageHoc({ title: '代还款信息确认' })
export default class RepayForOthers extends Component {
  config = {
    navigationBarTitleText: '代还款信息确认'
  };
  constructor() {
    super();
    this.state = {
      repayName: '',
      debtorMobileNo: '',
      amount: '',
      isOpened: false,
      dueTagCust: 'N', // 逾期打标标记，101或C06标
      showRepayBtn: false, // 是否展示“还到期”“提前结清”按钮
      btnSelectedNum: 0, // 选中“还到期”或“提前结清”的下标
      couponObject: { // 优惠券组件返回的数据
        availableCouponDetailList: [],
        appExclusiveCouponDetailList: [],
        unavailableCouponDetailList: []
      },
      selectedCoupon: null,
      showCouponSelector: false,
      overpayInfo: {
        overPayAmtRepay: '0.00', // 溢缴款总金额
        availableAmount: '0.00', // 本次还款可用溢缴款
        remitTotalAmount: '0.00', // 在途转账
        preRepayAmt: '0.00', // 预还款
      },
      surplusTotalAmt: '0.00', // 总待还金额
      courtCostBalance: '0.00', // 入口查询的法诉费总金额
      surplusDueAmount: '0.00', // 到期总金额
      settleWaiveAmt: '0.00', // 超限减免金额
      totalWaiveAmt: '0.00', // 优惠券减免金额
      courtCostAmt: '0.00', // 试算返回的法诉费金额
      rulesList: [], // 博弈梯度优惠列表
      payoffAmt: '0.00', // 博弈减免还清金额
      feeReduceAmt: '0.00', // 博弈减免可减免金额（优惠减免 + 超限减免）
      repayTrialDetailList: [], // 试算返回的账单明细
      isFeeReduce: false,
      displaySettleWaiveAmt: null,
      displayAmount: null,
      isSupportNegotiateRepay: false, // 是否有协商还在途任务
      negotiateRepayTaskBill: {}, // 协商还在途任务
      negotiateRepayTrialDetail: {}, // 协商还试算结果
      showTipModal: false, // 协商还情况下 输入金额大于结清金额提示
      repayBubbleParam: {}, // 协商还气泡文案
      overdueFlag: false, // 是否逾期（主要用作优惠券接口入参）
    };
    this.lastTrialTransTotalAmt = '0.00';
    this.advanceBillList = []; // 全部账单
    this.feeReduceCouponList = []; // 博弈减免优惠券
    this.handleAmtChangeDebounce = debounce((val) => {
      this.handleAmtChange(val);
    }, 100, {
      leading: false, // 指定调用在节流开始前
      trailing: true // 指定调用在节流结束后
    });
    this.awardDetailList = []; // 新优惠券列表
    this.needRequeryAwardFlag = false; // 需要派券后重新查券
  }

  async componentDidMount() {
    const {
      debtorName: repayName,
      othersPhoneNumInput: debtorMobileNo,
      userPhoneNumInput: operatorMobileNo,
    } = getStore('repayOthersInfo') || {};
    this.setState({
      repayName,
      debtorMobileNo,
      operatorMobileNo,
    }, () => {
      this.getRepayAmount();
    });
  }

  async getRepayAmount() {
    const { debtorMobileNo, operatorMobileNo } = this.state;
    const response = await Dispatch.repayment.offerRepayAmount({
      debtorMobileNo,
      operatorMobileNo,
      additionalQueryList: ['001']
    });
    const {
      surplusPayDueTotalAmt: surplusTotalAmt = '0.00', // 待还账单总金额(含超限减免)
      allPrepayPayDueTotalAmt: allPrepayPayTotalAmt = '0.00', // 提前结清总金额(含超限减免)
      dueTagCust = 'N',
      advanceBillList, // 提前结清借据列表
      courtCostBalance,
      negotiateRepayTaskBill, // 协商还还款任务账单
      overdueDays, // 客户在途自营借据的最大逾期天数，若客户在途自营借据均未逾期（过还款日）则逾期天数为0
      overpayControlFlag, // 溢缴款管控
      awardDetailList,
    } = response || {};
    // 小招荷包余额处理
    let {
      custOverpayBalance, remitTotalAmount, preRepayAmt
    } = response || {};
    if (overpayControlFlag === 'Y') {
      custOverpayBalance = '0.00';
      remitTotalAmount = '0.00';
      preRepayAmt = '0.00';
    }
    this.awardDetailList = awardDetailList || [];
    if (advanceBillList && advanceBillList.length > 0) {
      await this.check216Coupon(debtorMobileNo, operatorMobileNo, this.awardDetailList); // 三方实时派券
    }
    const totalPayAmt = Util.floatAdd(+custOverpayBalance, Util.floatAdd(+remitTotalAmount, +preRepayAmt)) >= +allPrepayPayTotalAmt
      ? 0 : Util.floatMinus(+allPrepayPayTotalAmt, Util.floatAdd(+custOverpayBalance, Util.floatAdd(+remitTotalAmount, +preRepayAmt)));
    const overOrOnDueAmt = Util.floatAdd(+custOverpayBalance, Util.floatAdd(+remitTotalAmount, +preRepayAmt)) >= +surplusTotalAmt
      ? 0 : Util.floatMinus(+surplusTotalAmt, Util.floatAdd(+custOverpayBalance, Util.floatAdd(+remitTotalAmount, +preRepayAmt)));
    console.log('totalPayAmt:', { totalPayAmt, overOrOnDueAmt });
    if (+surplusTotalAmt === 0 && +allPrepayPayTotalAmt === 0) {
      return Madp.showModal({
        title: '温馨提示',
        content: '当前还款入口借款人暂无待还账单，如有疑问请联系在线客服',
        confirmText: '我知道了',
        confirmColor: themeColor,
        showCancel: false,
        success: () => {
          dispatchTrackEvent({
            target: this,
            event: EventTypes.EV,
            beaconId: 'NoTotalAmt',
          });
          setTimeout(() => {
            Madp.closeWebView();
          }, 500);
        },
      });
    } else if (+overOrOnDueAmt === 0 && +totalPayAmt === 0) {
      Madp.showModal({
        title: '温馨提示',
        content: '借款人的小招荷包余额足够偿还欠款，请联系在线客服',
        confirmText: '我知道了',
        confirmColor: themeColor,
        showCancel: false,
        success: () => {
          dispatchTrackEvent({
            target: this,
            event: EventTypes.EV,
            beaconId: 'EnoughCustOverpayBalance',
          });
          setTimeout(() => {
            Madp.closeWebView();
          }, 500);
        },
      });
    }
    // 除(逾期打标或无到期账单或有协商还任务)外的客户，展示“还到期”“提前结清”按钮
    if (!(dueTagCust === 'Y' || +surplusTotalAmt === 0
      || (negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}'))) {
      this.setState({
        showRepayBtn: true
      });
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: 'ShowRepayBtnPage',
      });
    } else {
      // 打标用户使用结清模式
      this.setState({
        btnSelectedNum: 1
      });
      dispatchTrackEvent({
        target: this,
        event: EventTypes.EV,
        beaconId: 'NotShowRepayBtnPage',
      });
    }
    this.advanceBillList = advanceBillList;
    this.lastTrialTransTotalAmt = allPrepayPayTotalAmt;
    // 协商还默认填充当期待还金额，若当期待还为0则填充结清金额
    if (negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}') {
      const { currentShouldAmt, totalShouldAmt } = negotiateRepayTaskBill;
      this.setState({
        amount: Number(currentShouldAmt) > 0 ? currentShouldAmt : totalShouldAmt
      });
    }
    this.setState({
      overpayInfo: {
        overPayAmtRepay: custOverpayBalance,
        availableAmount: '0.00',
        remitTotalAmount,
        preRepayAmt,
      },
      dueTagCust,
      overdueFlag: Number(overdueDays) >= 1,
      surplusDueAmount: surplusTotalAmt,
      surplusTotalAmt: allPrepayPayTotalAmt,
      courtCostBalance,
      negotiateRepayTaskBill,
      isSupportNegotiateRepay: Boolean(negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}'),
    }, () => {
      this.waiveInit();
    });
  }

  async check216Coupon(debtorMobileNo, operatorMobileNo, awardDetailList) {
    try {
      this.needRequeryAwardFlag = await check216Coupon(debtorMobileNo, operatorMobileNo, awardDetailList);
    } catch (e) {
      console.debug(e);
    }
  }

  // 优惠信息初始化
  async waiveInit() {
    const { debtorMobileNo, operatorMobileNo, isSupportNegotiateRepay, amount } = this.state;
    if (this.needRequeryAwardFlag) {
      const response = await Dispatch.repayment.offerRepayAmount({
        debtorMobileNo,
        operatorMobileNo,
        additionalQueryList: ['001']
      });
      const {
        awardDetailList,
      } = response || {};
      this.awardDetailList = awardDetailList || [];
    }
    const couponList = await getFeeReduceCoupon(this.awardDetailList, this.advanceBillList);
    // 有博弈减免券且无协商还任务，进入博弈场景
    if (couponList && couponList.length > 0 && !isSupportNegotiateRepay) {
      this.feeReduceCouponList = couponList;
      const { payoffAmt, rulesList } = await getRepayWaiveTrialTrans(
        couponList,
        this.advanceBillList,
        { debtorMobileNo, operatorMobileNo }
      );

      this.setState({
        isFeeReduce: true,
        showRepayBtn: false,
        payoffAmt,
        rulesList,
      });
      this.feeReduceCouponList = couponList;
    } else {
      this.getCouponList();
      if (isSupportNegotiateRepay) {
        this.getRepayBubbleParam();
        setTimeout(() => {
          this.repayTransTrialApi(amount);
        }, 100);
      }
    }
  }

  get getMaskedPhone() {
    const { debtorMobileNo } = this.state;
    return debtorMobileNo ? `${debtorMobileNo.substring(0, 3)}****${debtorMobileNo.substring(7)}` : '';
  }

  get isDisabledRepayBtn() {
    const {
      amount, surplusTotalAmt, isFeeReduce, payoffAmt,
      isSupportNegotiateRepay, negotiateRepayTrialDetail,
    } = this.state;
    const { totalThresholdAmt } = negotiateRepayTrialDetail || {};
    return !(
      amount
      && +amount > 0
      && (isSupportNegotiateRepay
        ? Number(amount) <= Number(totalThresholdAmt)
        : ((isFeeReduce ? Number(amount) <= Number(payoffAmt) : Number(amount) <= Number(surplusTotalAmt))))
      && Number(this.getActualRepayAmt) > 0
    );
  }

  get getActualRepayAmt() {
    const { amount, settleWaiveAmt, totalWaiveAmt, overpayInfo, isFeeReduce, isSupportNegotiateRepay } = this.state;
    const { availableAmount } = overpayInfo;
    if (isSupportNegotiateRepay) {
      return Util.numToStr(Number(amount || '0.00') - Number(settleWaiveAmt || '0.00') - Number(availableAmount || 0.00));
    }
    return isFeeReduce ? Util.floatMinus(amount, availableAmount)
      : Util.numToStr(Number(amount || '0.00') - Number(settleWaiveAmt || '0.00') - Number(totalWaiveAmt || '0.00') - Number(availableAmount || 0.00));
  }

  amountInputValidator = (val) => {
    if (!val || !Number(val)) {
      Util.toast('请输入大于0的金额');
      return false;
    } else if (Validator.isMoney(val)) {
      return true;
    } else {
      Util.toast('您输入的金额数不正确，小数点后最多只能有两位哦~');
      return false;
    }
  }

  async handleAmtChange(val) {
    if (!this.amountInputValidator(val)) return;
    this.setState({ editing: false });
    const { isFeeReduce } = this.state;
    if (isFeeReduce) {
      this.feeReduceTrialApi(val);
    } else {
      await this.getCouponList(val);
      setTimeout(() => {
        this.repayTransTrialApi(val);
      }, 100);
    }
  }

  // 博弈减免优惠试算
  async feeReduceTrialApi(amtInput) {
    const { rulesList, overpayInfo, debtorMobileNo, operatorMobileNo } = this.state;
    const {
      totalCanPayAmt,
      settleWaiveAmt,
      totalWaiveAmt,
      feeReduceAmt,
      repayTrialDetailList
    } = await feeReduceTrialApi(
      amtInput,
      this.feeReduceCouponList,
      this.advanceBillList,
      rulesList,
      { debtorMobileNo, operatorMobileNo }
    );
    const { overPayAmtRepay, remitTotalAmount, preRepayAmt } = overpayInfo;
    overpayInfo.availableAmount = Math.min(Util.floatAdd(Number(overPayAmtRepay || 0), Util.floatAdd(Number(remitTotalAmount || 0), Number(preRepayAmt || 0))), Number(totalCanPayAmt || 0)).toFixed(2);
    this.lastTrialTransTotalAmt = amtInput;
    this.setState({
      overpayInfo: {
        ...overpayInfo
      },
      amount: Number(totalCanPayAmt || 0) > 0 ? Math.max(Number(totalCanPayAmt), Number(amtInput)).toFixed(2) : '0.00',
      settleWaiveAmt: settleWaiveAmt || '0.00',
      totalWaiveAmt: totalWaiveAmt || '0.00',
      feeReduceAmt: feeReduceAmt || '0.00',
      repayTrialDetailList,
    });
  }

  // 普通还款试算
  async repayTransTrialApi(amtInput) {
    const { selectedCoupon, debtorMobileNo, operatorMobileNo, surplusTotalAmt, isSupportNegotiateRepay } = this.state;
    const trialAmt = selectedCoupon && selectedCoupon.awardAmtType === '4' ? surplusTotalAmt : amtInput;
    const {
      totalCanPayAmt,
      settleWaiveAmt,
      totalWaiveAmt,
      shouldRepayAmt,
      courtCostAmt,
      repayTrialDetailList,
      negotiateRepayTrialDetail, // 协商还试算详情
    } = await repayTransTrialApi(
      trialAmt,
      selectedCoupon,
      this.advanceBillList,
      { debtorMobileNo, operatorMobileNo }
    );
    // 已选券但试算优惠为0或试算失败，清空
    if (selectedCoupon && (!totalWaiveAmt || Number(totalWaiveAmt) === 0)) {
      setTimeout(() => {
        Util.toast('优惠券无法使用');
      }, 100);
      return this.clearCoupon(true, amtInput);
    }
    if (Number(trialAmt) < Number(shouldRepayAmt) && Number(courtCostAmt) === 0 && !isSupportNegotiateRepay) {
      const displaySettleWaiveAmt = getDisplaySettleWaiveAmt(trialAmt, totalWaiveAmt, totalCanPayAmt);
      this.setState({
        displaySettleWaiveAmt: Number(displaySettleWaiveAmt).toFixed(2),
        displayAmount: Number(trialAmt).toFixed(2),
      });
    } else {
      this.setState({ displaySettleWaiveAmt: null, displayAmount: null });
    }
    // 根据实还金额重算溢缴款的使用额度
    const { overpayInfo } = this.state;
    const { overPayAmtRepay, remitTotalAmount, preRepayAmt } = overpayInfo;
    overpayInfo.availableAmount = Math.min(Util.floatAdd(Number(overPayAmtRepay || 0), Util.floatAdd(Number(remitTotalAmount || 0), Number(preRepayAmt || 0))), Number(totalCanPayAmt || '0.00')).toFixed(2);
    this.lastTrialTransTotalAmt = trialAmt;
    if (isSupportNegotiateRepay) {
      this.setState({
        amount: Number(trialAmt || 0).toFixed(2),
        settleWaiveAmt: negotiateRepayTrialDetail && negotiateRepayTrialDetail.settleWaiveAmt || '0.00',
        totalWaiveAmt: totalWaiveAmt || '0.00',
        repayTrialDetailList,
        courtCostAmt,
        overpayInfo: { ...overpayInfo },
        negotiateRepayTrialDetail,
      }, () => {
        this.getRepayBubbleParamAfterTrail();
      });
    } else {
      this.setState({
        amount: Number(trialAmt || 0) > Number(surplusTotalAmt || 0) ? Number(trialAmt).toFixed(2) : shouldRepayAmt || Number(trialAmt || 0).toFixed(2) || '0.00',
        settleWaiveAmt: settleWaiveAmt || '0.00',
        totalWaiveAmt: totalWaiveAmt || '0.00',
        repayTrialDetailList,
        courtCostAmt,
        overpayInfo: { ...overpayInfo },
      });
    }
  }

  clearCoupon(shouldTrial, trialAmt) {
    const { cleanCheck } = this.couponSelectorRef;
    cleanCheck && cleanCheck();
    this.setState({ selectedCoupon: null }, () => {
      if (shouldTrial) {
        this.repayTransTrialApi(trialAmt);
      }
    });
  }

  onConfirmButtonClickAction = () => {
    const { amount } = this.state;
    this.setState({
      amount: parseFloat(amount).toFixed(2),
      isOpened: true
    });
  }

  // 修改还款金额为应还金额
  fixAmount(amt) {
    dispatchTrackEvent({
      target: this,
      event: EventTypes.BC,
      beaconId: 'OneClickModifyNoDistinctionBC',
    });
    this.handleAmtChangeDebounce(amt);
  }

  // 还款前校验接口, 要登陆态，不用这个接口了
  async submitRepayment() {
    this.preRepay();
  }

  async preRepay() {
    const alipay = Url.getParam('alipay') || '';
    this.setState({
      isOpened: false
    });
    const repayParams = this.handleRepayParams();
    repayParams.repayWay = 'WEIXIN-JSAPI';
    if (ChannelConfig.wxPay && isWechat()) {
      // wxpay
      repayParams.repayWay = 'WEIXIN-JSAPI';
      repayParams.appId = wechatAppId;
    } else if (ChannelConfig.wxPay && isMuapp()) {
      // wxpay sdk
      repayParams.repayWay = 'WEIXIN-APP';
      repayParams.appId = CustomConfig.appWechatAppId;
    } else if (alipay) {
      if (ChannelConfig.alipay && isMuapp()) {
        // alipay sdk
        repayParams.repayWay = 'ALIPAY-SDK';
      } else if (ChannelConfig.alipay && !isMuapp()) {
        // alipay
        repayParams.repayWay = 'ALIPAY-H5';
      }
    }
    const {
      data: payResult, errMsg, errCode,
    } = await Dispatch.repayment.offerRepayApply(repayParams);
    if (alipay && payResult && (payResult.alipayUrl || payResult.prepayId)) {
      this.alipaySDK(payResult);
    } else if (payResult && payResult.nonceStr) {
      this.wxPaySDK(payResult);
    } else {
      Util.toast(errMsg || errCodeToMsgMap[errCode] || '系统异常，请稍后重试');
    }
  }

  handleRepayParams() {
    const {
      amount, isFeeReduce, settleWaiveAmt, totalWaiveAmt, repayTrialDetailList, overpayInfo,
      operatorMobileNo, debtorMobileNo, courtCostAmt, courtCostBalance
    } = this.state;
    const { availableAmount, remitTotalAmount, preRepayAmt } = overpayInfo;
    let repayParams = {
      helpRepayInfo: {
        helperMobile: operatorMobileNo,
        debtorMobile: debtorMobileNo
      },
      securityInfoList: [],
      transRefNo: Util.getTransRefNo(),
      currency: '156', // 代表人民币
      repayMode: isFeeReduce || this.advanceBillList.length === 0 ? 'AMT' : 'ADVANCE',
      isRandomReduce: 'N',
      settleWaiveAmt: parseFloat(settleWaiveAmt).toFixed(2) || '0.00',
      totalWaiveAmt: parseFloat(totalWaiveAmt).toFixed(2) || '0.00', // 新还款 减免总金额
      transOverpayAmt: Util.floatMinus(Util.floatMinus(Number(availableAmount || '0.00'), Number(preRepayAmt || '0.00')), Number(remitTotalAmount || '0.00')) > 0
        ? Util.floatMinus(Util.floatMinus(Number(availableAmount || '0.00'), Number(preRepayAmt || '0.00')), Number(remitTotalAmount || '0.00')).toFixed(2) : '0.00',
      transRemitAmt: Util.floatMinus(Number(availableAmount || '0.00'), Number(preRepayAmt || '0.00')) > 0 ? ((Util.floatMinus(Number(remitTotalAmount || '0.00'), Util.floatMinus(Number(availableAmount || '0.00'), Number(preRepayAmt || '0.00'))) > 0
        ? Util.floatMinus(Number(availableAmount || '0.00'), Number(preRepayAmt || '0.00')).toFixed(2) : parseFloat(remitTotalAmount).toFixed(2)) || '0.00') : '0.00',
      transPreRepayAmt: (Util.floatMinus(Number(preRepayAmt || '0.00'), Number(availableAmount || '0.00')) > 0
        ? parseFloat(availableAmount).toFixed(2) : parseFloat(preRepayAmt).toFixed(2)) || '0.00',
    };
    // 博弈减免场景
    if (isFeeReduce) {
      repayParams = {
        ...repayParams,
        transTotalAmt: Util.numToStr(Number(amount) + Number(totalWaiveAmt)),
        transCashAmt: parseFloat(this.getActualRepayAmt).toFixed(2),
        repayTotalAmt: parseFloat(this.getActualRepayAmt).toFixed(2),

        totalAwardAmount: parseFloat(totalWaiveAmt || 0).toFixed(2),
        // awardInfoList: this.feeReduceCouponList,
        feeInteRightList: [{
          ...getFeeInteRightInfo((this.feeReduceCouponList || [])[0] || {}),
        }],
        repayDetailList: repayTrialDetailList.map((i) => ({ orderNo: i.orderNo, instCnt: i.instCnt }))
      };
    } else {
    // 非博弈减免场景
      const { selectedCoupon } = this.state;
      let transTotalAmt = parseFloat(amount).toFixed(2);
      // 超限叠加法诉费的场景下，若法诉费未结清，总待还金额需要减去法诉费，否则提交还款中台试算会与前端的试算不一致（原因是该场景下试算入参从逻辑上从应还金额变为实还金额）
      if (Number(settleWaiveAmt) > 0 && Number(courtCostBalance) > 0 && Number(courtCostAmt) < Number(courtCostBalance)) {
        transTotalAmt = this.lastTrialTransTotalAmt;
      }
      repayParams = {
        ...repayParams,
        transTotalAmt,
        transCashAmt: parseFloat(this.getActualRepayAmt).toFixed(2),
        repayDetailList: this.advanceBillList.map((i) => ({
          orderNo: i.orderNo,
          repayAmt: i.surplusPayTotalAmt,
          debtorSplitDetailList: i.debtorSplitDetails,
          instCnt: 0,
        })),
      };
      if (selectedCoupon && selectedCoupon.awardNo) {
        repayParams.feeInteRightList = [
          {
            ...getFeeInteRightInfo(selectedCoupon),
          }
        ];
      }
    }
    return repayParams;
  }

  /**
 * 支付宝还款
 */
  async alipaySDK(r) {
    if (isMuapp()) {
      const self = this;
      window.muapp.AliPayPlugin.alipayAction(r.alipayUrl || r.prepayId, async (params) => {
        const { result } = await Dispatch.repayment.alipayCallback(params);
        if (result === 'SUC') {
          self.toCommonResult('success');
        }
      });
      return;
    }
    if (r.alipayUrl) {
      // 支付宝中采用路由跳转的形式进行支付，结果页展示信息保留在缓存中
      // 另支付宝会清session，所以存到local
      const payResult = {
        type: this.billType,
        result: JSON.stringify(this.payResult),
      };
      Madp.setStorageSync('payResult', payResult, 'LOCAL');
      this.parseToDOM(r.alipayUrl);
    }
  }

  parseToDOM(str) {
    const div = document.createElement('div');
    if (typeof str === 'string') {
      div.innerHTML = str;
    }
    document.body.appendChild(div);
    div.getElementsByTagName('form')[0].submit();
  }

  /**
   * 微信还款
   */
  wxPaySDK(r) {
    const self = this;
    if (isWechat()) {
      Madp.chooseWXPay({
        timeStamp: r.timestamp,
        nonceStr: r.nonceStr,
        package: r.packageValue,
        signType: r.signType,
        paySign: r.sign,
        success: (result) => {
          dispatchTrackEvent({
            target: this,
            event: EventTypes.EV,
            beaconId: 'WXPaySuccess',
            beaconContent: { cus: result }
          });
          // 还款失败有可能也会走到这里
          self.toCommonResult('success');
        },
        fail: (result) => {
          Util.toast('微信支付失败');
          dispatchTrackEvent({
            target: this,
            event: EventTypes.EV,
            beaconId: 'WXPayFail',
            beaconContent: { cus: result }
          });
        },
        cancel: (result) => {
          Util.toast('微信支付取消');
          dispatchTrackEvent({
            target: this,
            event: EventTypes.EV,
            beaconId: 'WXPayCancel',
            beaconContent: { cus: result }
          });
        }
      });
    } else if (isMuapp()) {
      try {
        window.muapp.WeChatPayPlugin.wechatpayAction({
          appId: r.appId,
          nonceStr: r.nonceStr,
          packageValue: r.packageValue,
          partnerId: r.partnerId,
          prepayId: r.prepayId,
          sign: r.sign,
          timeStamp: r.timestamp,
        }, (result) => {
          if (result.resultStatus === '9000') {
            self.toCommonResult('success');
          } else {
            Util.toast('微信支付失败');
          }
        }, () => {
          Util.toast('微信支付失败');
        });
      } catch (error) {
        // error
      }
    } else {
      Util.toast('只有APP或Wechat渠道才可以使用微信还款！');
    }
  }

  toCommonResult(type) {
    Util.router.replace({
      path: '/pages/common-result/result',
      query: {
        type
      }
    });
  }

  chooseBtnItem = (index) => {
    this.setState({
      btnSelectedNum: index
    });
  }

  async getCouponList(val) {
    const { amount } = this.state;
    const couponObject = await getCouponList(this.awardDetailList, this.advanceBillList, val || amount);
    this.setState({
      couponObject: {
        ...couponObject,
        // TODO：过滤掉awardType: '216', specialScene: 'SSA08'的券
        availableCouponDetailList: (couponObject && couponObject.availableCouponDetailList || []).filter((item) => (!(item.awardType === '216' && item.subUseSceneCode === 'SSA08')))
      }
    }, () => {
      this.handleCouponListResult();
    });
  }

  // 获取优惠券列表后，根据当前已选券情况默认选中或清空已选券
  handleCouponListResult() {
    const { selectedCoupon, couponObject, isSupportNegotiateRepay } = this.state;
    const { availableCouponDetailList } = couponObject;
    const { chooseCoupon } = this.couponSelectorRef;
    let isCleared = false; // 本次是否清空优惠券
    if (selectedCoupon
      && (availableCouponDetailList && availableCouponDetailList
        .findIndex((i) => i.awardNo === selectedCoupon.awardNo) === -1)) {
      // 已选券未在可用券列表中，清空已选券
      this.clearCoupon();
      isCleared = true;
    }
    if ((!selectedCoupon || isCleared)
      && availableCouponDetailList && availableCouponDetailList.length > 0
      && !isSupportNegotiateRepay) {
      // 未选优惠券，且有可选券，且无协商还在途任务，默认选中第1张
      const defaultSelectCoupon = availableCouponDetailList[0];
      chooseCoupon(defaultSelectCoupon.awardNo);
      this.setState({
        selectedCoupon: defaultSelectCoupon
      });
    }
  }

  async onCouponSelected(coupon) {
    this.setState({ selectedCoupon: coupon }, () => {
      const { amount, displayAmount } = this.state;
      this.repayTransTrialApi(displayAmount || amount);
    });
  }

  renderFeeReduceAmountTip(payoffAmt) {
    const { amount, displayAmount, btnSelectedNum, isSupportNegotiateRepay } = this.state;
    if (!amount || !payoffAmt || isSupportNegotiateRepay) return; // 协商还不进行金额提示
    const totalRepayAmt = displayAmount || amount; // （输入的）还款金额
    const fixAmount = payoffAmt; // 一键修改的金额值
    const diff = Util.floatMinus(totalRepayAmt, payoffAmt); // 输入总金额 与 结清金额 的差值
    dispatchTrackEvent({
      target: this,
      event: EventTypes.PO,
      beaconId: 'OneClickModifyPO',
    });
    if (diff > 0) {
      return (
        <MUView className="amountTip">
          已超出{btnSelectedNum === 1 ? '结清' : '到期'}账单金额
          {diff}
          元，请修改还款金额。
          <MUView beaconId="OverFixAmount" className="fixNumBtn" style={`color: ${themeColor}`} onClick={() => this.fixAmount(fixAmount)}>
            一键修改
          </MUView>
        </MUView>
      );
    }
    if (diff < 0) {
      return (
        <MUView className="amountTip">
          还差
          {-diff}
          元{btnSelectedNum === 1 ? '可提前结清' : '可以结清已到期'}账单，请确认还款金额，也可
          <MUView beaconId="betweenFixAmount" className="fixNumBtn" style={`color: ${themeColor}`} onClick={() => this.fixAmount(fixAmount)}>
            一键修改
          </MUView>
        </MUView>
      );
    }
  }

  // 未修改金额时气泡展示（取自查询接口）
  getRepayBubbleParam = () => {
    let repayBubbleParam = {
      type: 'fillTop',
    };
    const { negotiateRepayTaskBill } = this.state;
    const {
      currentExpectWaiveAmt, // 当期计划减免金额
      currentTaskAmt, // 当期还款任务金额
      currentShouldAmt, // 当期结清金额
      payOffWaiveAmt, // 结清总减免金额
      payOffTotalAmt, // 剩余总待还金额
      totalShouldAmt, // 结清总金额
    } = negotiateRepayTaskBill || {};
    if ((currentExpectWaiveAmt && Number(currentExpectWaiveAmt) > 0)) { // 当期预计减免金额大于0，有息费减免情况
      if (currentShouldAmt && Number(currentShouldAmt) > 0) { // 当期结清金额大于0，此时未还款或已部分还款
        repayBubbleParam.contentArr = [
          { contentText: '已减', contentColor: '' },
          { contentText: `${currentExpectWaiveAmt}元`, contentColor: 'red' },
          { contentText: `息费，仅需${currentShouldAmt}元抵还${currentTaskAmt}元`, contentColor: '' },
        ];
      } else if (totalShouldAmt && Number(totalShouldAmt) > 0) { // 当期结清金额为0，结清总金额大于0
        repayBubbleParam.contentArr = [
          { contentText: '已减', contentColor: '' },
          { contentText: `${payOffWaiveAmt}元`, contentColor: 'red' },
          { contentText: `息费，仅需${totalShouldAmt}元抵还${payOffTotalAmt}元`, contentColor: '' },
        ];
      }
    }
    this.setState({
      repayBubbleParam
    });
    return;
  }

  // 修改金额后气泡展示（取自试算接口）
  getRepayBubbleParamAfterTrail() {
    let repayBubbleParam = {
      type: 'fillTop',
    };
    const { negotiateRepayTrialDetail, amount } = this.state;
    const {
      trailResult, // 试算结果, 0-未达门槛金额, 1-已达门槛金额
      taskAmt, // 任务金额
      taskThresholdAmt, // 任务门槛金额：任务金额*（1-减免比例）
      totalThresholdAmt, // 结清上限金额：总欠息费*（1-减免比例）
      maxWaiveAmt, // 最大可减免息费: 总欠息费*减免比例
      taskDiffAmt, // 达标相差金额, 试算结果为0时有值
      expectWaiveAmt, // 可减免息费, 试算结果为1时有值，基试算金额
      realPaidAmt = '0', // 实还金额
      surplusTotalAmt, // 结清金额
      settleFlag, // 结清标识
    } = negotiateRepayTrialDetail || {};
    if ((maxWaiveAmt && Number(maxWaiveAmt) > 0)) { // 有息费减免情况
      if (trailResult === '0') { // 未达门槛金额，此时为修改金额小于任务门槛金额
        repayBubbleParam.contentArr = [
          { contentText: `再还${taskDiffAmt}元`, contentColor: 'red' },
          { contentText: `，享${totalThresholdAmt === taskThresholdAmt ? Number(taskThresholdAmt || 0).toFixed(2) : Util.floatMinus(taskThresholdAmt, realPaidAmt)}元抵还${totalThresholdAmt === taskThresholdAmt ? Number(taskAmt || 0).toFixed(2) : Util.floatMinus(taskAmt, realPaidAmt)}元`, contentColor: '' },
        ];
      } else if ((amount && Number(amount)) > (totalThresholdAmt && Number(totalThresholdAmt))) { // 修改金额大于结清上限金额
        this.setState({
          showTipModal: true
        });
      } else if (settleFlag === 'Y') {
        repayBubbleParam.contentArr = [
          { contentText: '已减', contentColor: '' },
          { contentText: `${Util.floatMinus(surplusTotalAmt, amount).toFixed(2)}元`, contentColor: 'red' },
          { contentText: `息费，仅需${amount}元抵还${surplusTotalAmt}元`, contentColor: '' },
        ];
      } else {
        repayBubbleParam.contentArr = [
          { contentText: '已减', contentColor: '' },
          { contentText: `${expectWaiveAmt}元`, contentColor: 'red' },
          { contentText: `息费，仅需${amount}元抵还${Util.floatAdd(amount, expectWaiveAmt).toFixed(2)}元`, contentColor: '' },
        ];
      }
    } else { // 无息费减免
      if (trailResult === '0') { // 未达门槛金额，此时为修改金额小于任务门槛金额
        repayBubbleParam.contentArr = [
          { contentText: `再还${taskDiffAmt}元`, contentColor: 'red' },
          { contentText: '，可达到本月协商还约定金额', contentColor: '' },
        ];
      } else if ((amount && Number(amount)) > (totalThresholdAmt && Number(totalThresholdAmt))) {
        this.setState({
          showTipModal: true
        });
      }
    }
    this.setState({
      repayBubbleParam
    });
    return;
  }

  get modalContent() {
    const { negotiateRepayTrialDetail } = this.state;
    const {
      totalThresholdAmt, // 结清上限金额：总欠息费*（1-减免比例）
      maxWaiveAmt, // 最大可减免息费: 总欠息费*减免比例
      expectWaiveAmt, // 可减免息费, 试算结果为1时有值，基试算金额
      surplusTotalAmt, // 全部待还总金额
    } = negotiateRepayTrialDetail || {};
    if ((maxWaiveAmt && Number(maxWaiveAmt) > 0)) { // 有息费减免
      return (
        <MUView className="tipModal-text">
          <MUText>{`可为您减免${expectWaiveAmt}元息费，仅需`}</MUText>
          <MUText className="tipModal-text__highlight">{totalThresholdAmt}</MUText>
          <MUText>{`元，即可抵还${surplusTotalAmt}元`}</MUText>
        </MUView>
      );
    } else { // 无息费减免
      return (
        <MUView className="tipModal-text">
          <MUText>仅输入</MUText>
          <MUText className="tipModal-text__highlight">{totalThresholdAmt}</MUText>
          <MUText>元，即可结清贷款</MUText>
        </MUView>
      );
    }
  }

  modalTipConfirm() {
    const { negotiateRepayTrialDetail: { totalThresholdAmt } = {} } = this.state;
    this.setState({
      showTipModal: false,
      amount: totalThresholdAmt, // 结清上限金额
    }, () => {
      this.repayTransTrialApi(totalThresholdAmt);
    });
  }

  render() {
    const {
      repayName, debtorMobileNo, amount, isOpened, showRepayBtn, btnSelectedNum,
      overpayInfo, isFeeReduce, rulesList, payoffAmt, feeReduceAmt, showCouponSelector, couponObject,
      selectedCoupon, totalWaiveAmt, settleWaiveAmt, surplusTotalAmt, surplusDueAmount,
      operatorMobileNo, displayAmount, displaySettleWaiveAmt, isSupportNegotiateRepay, repayBubbleParam, showTipModal,
    } = this.state;
    const editable = !(selectedCoupon && selectedCoupon.awardAmtType === '4');
    return (
      <MUView className="pages-bg repayment-for-others">
        <MUView className="topView">
          <MUView className="title">还款金额(元)</MUView>
          <MUInput
            editable={editable}
            className="amountInput"
            beaconId="amountInput"
            type="digit"
            placeholder="点此输入还款金额"
            value={displayAmount || amount}
            onBlur={(val) => { this.handleAmtChangeDebounce(val); }}
          />
          {repayBubbleParam && JSON.stringify(repayBubbleParam) !== '{}'
            && repayBubbleParam.contentArr && JSON.stringify(repayBubbleParam.contentArr) !== '[]' ? (
              <MUView className="repay-other-bubble">
                <RepayBubble
                  repayBubbleParam={repayBubbleParam}
                />
              </MUView>
            ) : null}
          {this.renderFeeReduceAmountTip(isFeeReduce ? payoffAmt
            : (btnSelectedNum === 0 ? surplusDueAmount : surplusTotalAmt))}
          {showRepayBtn && (
            <MUView className="repay-way">
              {
                btnGroup.map((item, index) => (
                  <MUView
                    className={classNames('repay-way-btn', { 'brand-selected': btnSelectedNum === index })}
                    beaconId={`repayWayBtn${index}`}
                    onClick={() => this.chooseBtnItem(index)}
                  >
                    <MUView className="repay-way-btn__text">{item}</MUView>
                    <MUImage
                      className={btnSelectedNum === index ? 'repay-way-btn__ticked' : 'hide-view'}
                      src={themeColor === '#E60027' ? icTickedRed : icTicked}
                    />
                  </MUView>
                ))
              }
            </MUView>
          )}
        </MUView>
        <WaiveDetailInfo
          isFeeReduce={isFeeReduce}
          rulesList={rulesList}
          payoffAmt={payoffAmt}
          feeReduceAmt={feeReduceAmt}
          overpayInfo={overpayInfo}
          couponObject={couponObject}
          selectedCoupon={selectedCoupon}
          totalWaiveAmt={totalWaiveAmt}
          settleWaiveAmt={displaySettleWaiveAmt || settleWaiveAmt}
          onClickCoupon={() => { this.setState({ showCouponSelector: true }); }}
          trialParam={{ amount, advanceBillList: this.advanceBillList, userInfo: { debtorMobileNo, operatorMobileNo } }}
          isSupportNegotiateRepay={isSupportNegotiateRepay}
        />
        <MUView className="repayInfo">
          <MUInput beaconId="repayName" title="借款人姓名" value={repayName} />
          <MUInput beaconId="repayPhone" title="借款人手机号" value={this.getMaskedPhone} />
        </MUView>
        <MUView className="tips">
          <MUView className="tipsTitle">
            <MUImage className="tipsImage" src={tipIcon} />
            <MUText>说明：</MUText>
          </MUView>
          <MUView className="tip">1. 确认还款前请务必和好友再次确认，避免是诈骗行为；</MUView>
          <MUView className="tip">2. 如果发生退款，钱将退至借款人的溢缴款账户中。</MUView>
        </MUView>
        {/* 还款按钮 博弈减免使用原按钮 标准场景使用吸底栏展示实还金额 */}
        <Statistic
          disabled={this.isDisabledRepayBtn}
          amount={this.getActualRepayAmt}
          submitRepay={this.onConfirmButtonClickAction}
          beaconId="NextButton"
        />
        <MUModal
          className="repayOthersModal"
          beaconId="RepayOthersModal"
          isOpened={isOpened}
          content={`您将为${repayName}还款支付${this.getActualRepayAmt}元，还款成功后钱款将进入借款人在${appInfos.name}的账户，用于偿还他在${appInfos.name}待还款的借据。`}
          cancelText="取消"
          confirmText="确认还款"
          onConfirm={() => this.submitRepayment()}
          onCancel={() => this.setState({
            isOpened: false
          })}
        />
        {/* 优惠券选择组件 */}
        <CouponSelector
          ref={(ref) => { this.couponSelectorRef = ref; }}
          isOpened={showCouponSelector}
          coupons={couponObject}
          onCouponSelected={(data) => this.onCouponSelected(data)}
          onClose={() => this.setState({ showCouponSelector: false })}
          beaconId="QrcodeRepayCouponSelector"
        />
        {/* 输入金额大于结清金额时展示 */}
        <MUModal
          type="tip"
          beaconId="tipModal"
          isOpened={showTipModal}
          title="温馨提示"
          closeOnClickOverlay={false}
          content={this.modalContent}
          confirmText="好的"
          onConfirm={() => this.modalTipConfirm()}
        />
      </MUView>
    );
  }
}
