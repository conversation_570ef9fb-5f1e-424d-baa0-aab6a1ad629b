/* eslint-disable react/sort-comp */
import Taro, { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUButton, MURichText, MUText, MUModal, MUImage
} from '@mu/zui';
import { getStore } from '@api/store';
import { Url } from '@mu/madp-utils';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import { getLoginInfo } from '@mu/business-basic';
import Protocol from '@components/protocol/index';
import pageHoc from '@utils/pageHoc';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import { StandardService } from '@utils/constants';
import CustomConfig from '@config/index';

import './comfirm.scss';

const themeColor = Util.getThemeColor(CustomConfig.theme);


// 展示试算结果、透传合同信息、提交案件结果
@track({ event: EventTypes.PO }, {
  pageId: 'ConsultRepayApplyConfirm',
  dispatchOnMount: true,
})
@pageHoc({ title: '协商还' })
export default class ConsultRepayApplyConfirm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isCheckedContract: '', // 表示合同组件是否勾选同意合同, 值为hasCheckedContract标识选中
      trialData: {}, // 试算结果
      invalidDate: '', // 资格有效期至
      initProtocalData: {}, // 合同填充内容
      consultRepayErrorMsg: '', // 试算失败错误信息
      showConsultErrorModel: false // 试算失败错误弹窗
    };
    this.applyNo = Url.getParam('applyNo') || ''; // 案件申请号
    this.contractInfos = (getStore('consultReapayInfo').consultContractInfo || []).filter((item) => item.contractCode === 'DHYWBG_XSMYZDHK'); // 过滤掉贷后服务变更协议
    this.loginInfoHash = {}; // 用户信息 hash
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '协商还' });
  }

  async componentWillMount() {
    try {
      // 1、并发调用登录信息、试算接口和案件详情接口
      const [, invalidDate, trialResult] = await Promise.all([
        this.getLoginInfo(),
        this.queryCustCaseDetail(),
        this.queryNegotiateRepayTrial()
      ]);
      const { trialRet, trialData, trialErrMsg } = trialResult || {};

      if (trialRet === '0') {
        const { surplusTotalAmt, surplusTotalPrincipalAmt, surplusPayInteFeeAmt, surplusPayFineAmt, repayPlanList, preRepayAmt } = trialData || {};
        const length = repayPlanList && repayPlanList.length;
        if (length > 0) {
          const firstRepayDateInfo = repayPlanList && repayPlanList[0] || {};
          const lastRepayDateInfo = repayPlanList && repayPlanList[length - 1] || {};
          const { repayDate: lastRepayDate } = lastRepayDateInfo || {};
          // 2、将试算结果保存到组件状态中
          this.setState({
            trialData,
            invalidDate,
            initProtocalData: { // 合同信息
              taskTotalAmt: surplusTotalAmt,
              surplusPayPrincipalAmt: surplusTotalPrincipalAmt,
              surplusPayInteFeeAmt,
              surplusPayFineAmt,
              taskThresholdAmt: firstRepayDateInfo.taskThresholdAmt,
              lastRepayDate
            }
          }, () => {
            // 3、埋点上报
            const { judicialConfirmFlagFromApply, applySorce } = getStore('consultReapayInfo') || {};
            this.trackInfo = {
              applyNo: this.applyNo,
              judicial: judicialConfirmFlagFromApply,
              source: applySorce === 'fromC' ? 'C' : 'B', // 案件来源
              plan: `${length}+${Util.floatTimes(firstRepayDateInfo.expectWaiveAmt || 0, 100)}%`,
              prePay: Number(preRepayAmt) > 0 ? 'Y' : 'N'
            };
            dispatchTrackEvent({
              target: this,
              event: EventTypes.SO,
              beaconId: 'ConsultRepayApplyConfirm',
              beaconContent: {
                cus: {
                  ...this.trackInfo
                }
              }
            });
          });
        }
      } else {
        this.setState({
          consultRepayErrorMsg: trialErrMsg || '您的欠款信息有变更，无法继续办理协商还，如需办理请联系人工客服95786协助处理',
          showConsultErrorModel: true
        }, () => {
          dispatchTrackEvent({
            target: this,
            event: EventTypes.SO,
            beaconId: 'trialFail',
            beaconContent: {
              cus: {
                trialErrMsg
              }
            }
          });
        });
      }
    } catch (error) {
      console.error('协商还试算失败:', error);
      Madp.showToast({ title: '系统异常，请稍后再试' });
    }
  }

  // 协商还案件详情查询
  async queryCustCaseDetail() {
    const { data } = await Dispatch.repayment.postLoanQueryCaseDetail({
      applyNo: this.applyNo,
      ...this.loginInfoHash
    });
    // 需联调
    const { invalidDate } = data || {};
    return invalidDate;
  }

  // 试算接口，获取用户展示信息
  async queryNegotiateRepayTrial() {
    const { ret: trialRet, data: trialData, errMsg: trialErrMsg } = await Dispatch.repayment.queryNegotiateRepayTrial({
      applyNo: this.applyNo,
      ...this.loginInfoHash
    });
    return { trialRet, trialData, trialErrMsg };
  }

  // 存储客户信息
  getLoginInfo = async () => {
    const { userIdHash, custIdHash } = await getLoginInfo() || {};
    this.loginInfoHash = { userHashNo: userIdHash, custHashNo: custIdHash };
  }

  async submit() {
    const { isCheckedContract } = this.state;
    // 判断当前是否存在合同且已经勾选合同
    if (!isCheckedContract && (this.contractInfos && this.contractInfos.length > 0)) {
      Madp.showToast({ title: '请阅读并同意协议', icon: 'none' });
      return;
    }

    // 1. 准备提交参数
    const param = {
      serviceType: '006',
      applyNo: this.applyNo,
      ...this.loginInfoHash
    };

    // 2.提交案件，翻转状态
    const result = await Dispatch.repayment.postLoanServiceSubmitCase(param);
    const { data } = result || {};
    const { applyStatus } = data || {};

    // 3. 记录埋点
    dispatchTrackEvent({
      target: this,
      event: EventTypes.BC,
      beaconId: 'submitCaseSuCess',
      beaconContent: {
        cus: {
          ...this.trackInfo,
          applyStatus
        }
      }
    });

    // 4、直接在本页面分发，不去loading页分发
    this.applyStatusNext(applyStatus);
  }

  // 根据案件结果进行处理
  applyStatusNext(status) {
    switch (status) {
      case '150': // 案件审核拒绝
        this.toServicesReapyResult(4);
        break;
      case '200': // 审核通过，去还款；确认参数
        if (this.miniChannelFlag) {
          Taro.redirectTo({
            url: `/pages/express-repay/index?_windowSecureFlag=1&billType=billType=preConsultRepay&applyNo=${this.applyNo}&needShowConsultRepayModel=N`
          });
        } else {
          Util.router.replace({
            path: `/pages/express-repay/index?_windowSecureFlag=1&applyNo=${this.applyNo}`,
            query: {
              billType: 'preConsultRepay',
              needShowConsultRepayModel: 'N'
            }
          });
        }
        break;
      case '210': // 案件审核通过，业务办理成功
        this.toServicesReapyResult(1);
        break;
      case '220': // 案件审核通过，业务办理失败
        this.toServicesReapyResult(6);
        break;
      default: // 异常情况
        this.toServicesReapyResult(6);
        break;
    }
  }

  // 贷后服务标准化结果页
  toServicesReapyResult(status) {
    Util.router.replace({
      path: `/pages/standard-service-result/index?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}&serviceType=${StandardService.ConsultRepayApply}&status=${status}`,
    });
  }
  render() {
    const { trialData, initProtocalData, invalidDate, consultRepayErrorMsg, showConsultErrorModel } = this.state;
    // 办理前首付还款金额、办理前还款总金额、还款计划
    const { preRepayAmt, preRepayTotalAmt, preWaiveAmt, repayPlanList, repayDay } = trialData || {};
    // 分期数
    const installCnt = (repayPlanList || []).length;
    // 还款日，去掉前导零
    const formattedRepayDay = repayDay ? String(Number(repayDay)) : '';
    // 首次还款计划: 首次还款时间、任务金额、任务门槛金额、预计减免金额（任务门槛金额=任务金额-预计减免金额）
    const firstRepayPlan = (repayPlanList && repayPlanList[0]) || {};
    const { repayDate: firstRepayDate, taskAmt, taskThresholdAmt, expectWaiveAmt } = firstRepayPlan || {};
    // 格式化首次还款日期
    const formattedFirstRepayDate = firstRepayDate ? firstRepayDate.replace(/(\d{4})(\d{2})(\d{2})/, (match, year, month, day) => {
      return `${year}年${Number(month)}月${Number(day)}日`;
    }) : '';
    // 提取日期中的数字部分用于蓝色显示
    const formattedFirstRepayDateWithColor = formattedFirstRepayDate.replace(/(\d+)日$/, '<span style="color: #3477FF;">$1</span>日');
    // 资格有效期至
    const formattedInvalidDate = invalidDate ? invalidDate.replace(/(\d{4})(\d{2})(\d{2})/, '$1年$2月$3日') : '';

    return (<MUView className="consult-comfirm">
      <MUView className="consult-comfirm-top">
        <MUView className="consult-comfirm-top__title">您已获得协商还办理资格！</MUView>
        <MUView className="consult-comfirm-top__subtitle">资格有效期至<MUText className="subtitleLight">{formattedInvalidDate}</MUText>，过期失效</MUView>
        <MUView className="consult-comfirm-top__content">
          <MUView className="consult-comfirm-top__content--desc">请确认您的办理信息</MUView>
          <MUView className="consult-comfirm-top__content--list">
            <MUView className="consult-list-item">
              <MUView className="consult-list-title">月最低还款金额</MUView>
              <MUView className="consult-list-text">
                <MUText>{taskThresholdAmt}元</MUText>
                {Number(expectWaiveAmt) > 0 ? <MUText className="underline">{taskAmt}元</MUText> : null}
              </MUView>
            </MUView>
            <MUView className="consult-list-item">
              <MUView className="consult-list-title">分期数</MUView>
              <MUView className="consult-list-text">{installCnt}个月</MUView>
            </MUView>
            <MUView className="consult-list-item">
              <MUView className="consult-list-title">还款日</MUView>
              <MURichText className="consult-list-text" nodes={`每月<span style="color: #3477FF;">${formattedRepayDay}</span>日`} />
            </MUView>
            <MUView className="consult-list-item">
              <MUView className="consult-list-title">首次还款时间</MUView>
              <MURichText className="consult-list-text" nodes={`${formattedFirstRepayDateWithColor}`} />
            </MUView>
            {Number(preRepayAmt) > 0 ? <MUView className="consult-list-item">
              <MUView className="consult-list-title">办理前需还款金额</MUView>
              <MUView className="consult-list-text">
                <MUText className="light">{preRepayAmt}元</MUText>
                {Number(preWaiveAmt) > 0 ? <MUText className="underline">{preRepayTotalAmt}元</MUText> : null}
              </MUView>
            </MUView> : null}
          </MUView>
        </MUView>
      </MUView>
      <MUView className="consult-comfirm__footer">
        {this.contractInfos.length ? (
          <MUView className="footer__contract">
            <Protocol
              trackPrefix="repayment.negotiate"
              onChecked={(v) => { this.setState({ isCheckedContract: v }); }}
              contractInfoList={this.contractInfos}
              billExtendInfo={initProtocalData}
            />
          </MUView>
        ) : null}
        <MUView className="footer__button">
          <MUButton
            type="primary"
            beaconId="ExtendSubmit"
            onClick={() => this.submit()}
          >{Number(preRepayAmt) > 0 ? '确认办理，去还款' : '确认办理'}</MUButton>
        </MUView>
      </MUView>
      {/* 协商还试算失败提示弹窗 */}
      <MUModal
        className="consult-repay-modal"
        beaconId="ConsultRepayModel"
        isOpened={showConsultErrorModel}
        closeOnClickOverlay={false}
      >
        <MUView className="consult-repay-modal-container">
          <MUImage className="img" src="https://file.mucfc.com/ebn/3/0/202312/20231227152741411abe.png" />
          <MUView className="desc">{consultRepayErrorMsg}</MUView>
          <MUView className="btn" style={{ backgroundColor: themeColor }} onClick={() => Madp.navigateBack()}>返回首页</MUView>
        </MUView>
      </MUModal>
    </MUView>);
  }
}
