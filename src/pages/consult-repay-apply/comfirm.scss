.consult-comfirm {
  height: 100%;
  min-height: 100vh;
  position: relative;
  background-color: #f3f3f3;
  background-image: url('./img/comfirmBg.png');
  background-repeat: no-repeat;
  background-size: 100% auto;
  /* 使背景图片宽度适应元素宽度 */
  padding-top: 50px;


  &-top {
    position: relative;
    padding: 0px 20px;

    &__title {
      font-size: 48px;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 50px;
      margin-left: 16px;
    }

    &__subtitle {
      margin-top: 30px;
      font-size: 32px;
      color: #ffffff;
      font-weight: 400;
      line-height: 42px;
      margin-left: 16px;

      .subtitleLight {
        color: #FDAA1F;
      }
    }

    &__content {
      margin-top: 50px;
      padding: 30px 40px 40px 40px;
      background: #FFFFFF;
      border-radius: 12px;

      &--desc {
        font-size: 32px;
        font-weight: 600;
        color: #333333;
        line-height: 48px;
      }

      &--list {
        margin-top: 32px;

        .consult-list-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 28px;
          line-height: 42px;
          margin-top: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .consult-list-title {
            color: #666666;
          }

          .consult-list-text {
            color: #333333;
            font-weight: 600;
            display: flex;
            align-items: center;

            .underline {
              margin-left: 12px;
              color: #A6A6A6;
              text-decoration: line-through;
            }

            .light {
              color: #FF8844;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  &__footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background: #fff;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .footer__contract {
      border-top: 1PX solid #EEEEEE;
      padding: 0 24px;

      :global(.mu-radio__option) {
        margin: 24px 0;
        font-size: 26px;
        color: #666666;

        :global(.mu-radio__label) {
          color: #3477FF;
        }
      }
    }

    .footer__button {
      margin: 24px auto;
      width: 670px;
      height: 100px;
      .at-button { 
        border-radius: 316px;
      }

      :global(.mu-button) {
        background: #3477FF;
        height: 88px;
        font-size: 32px;
        font-weight: 500;
      }
    }
  }
}

.consult-repay-modal {
  .mu-modal__container {
    padding: 60px 40px 10px 40px;
    width: 480px;
  }

  &-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .img {
      width: 120px;
      height: 120px;
    }

    .title {
      margin-top: 40px;
      height: 54px;
      color: #333333;
      text-align: center;
      font-size: 36px;
      font-weight: 600;
      line-height: 54px;
    }

    .desc {
      margin-top: 20px;
      color: #808080;
      text-align: center;
      font-size: 28px;
      line-height: 42px;
    }

    .btn {
      width: 480px;
      height: 88px;
      margin-top: 40px;
      margin-bottom: 30px;
      background: #3477FF;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 36px;
      color: #FFFFFF;
    }
  }
}