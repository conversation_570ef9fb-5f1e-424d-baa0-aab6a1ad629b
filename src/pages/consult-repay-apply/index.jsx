import Taro, { Component } from '@tarojs/taro';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, MUNavBarWeapp, MUIcon, MUText, MUImage, MURichText, MURadio, MUCalendar, MUActionSheet, MUButton, MUModal, MUDialog
} from '@mu/zui';
import Madp from '@mu/madp';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import {
  throttle,
  Url
} from '@mu/madp-utils';
import Util from '@utils/maxin-util';
import ChannelConfig from '@config/index';
import pageHoc from '@utils/pageHoc';
import classNames from 'classnames';
import { inject } from '@tarojs/mobx';
import { urlDomain } from '@utils/url_config';
import { jumpToChatPage } from '@mu/chat-entry-component';
import WaRichtext from '@mu/wa-richtext';
// import { AgreementDrawer } from '@mu/agreement';
import Protocol from '@components/protocol/index';
import Dispatch from '@api/actions';
import { getStore, setStore } from '@api/store';
import { injectState } from '@mu/leda';
import { OpRepayment } from '@mu/op-comp';
import { opService, getPageConf, getLoginInfo, getLocale } from '@mu/business-basic';
import { EVENT_CODE_MAP, StandardService, miniProgramChannel } from '@utils/constants';
import { judicialMaterialList } from './common-utils';
import './index.scss';

const themeColor = Util.getThemeColor(ChannelConfig.theme);

// 默认页面展位id
const pageId = '5e9d9030-fc79-476b-a72d-8140088574dc';
@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: pageId
    }
  }
}, {
  pageId: 'ConsultRepayApply',
  dispatchOnMount: true,
})
@pageHoc({ title: '协商还' })
@injectState({
  pageId() {
    return pageId;
  },
  getPageConf: () => getPageConf(pageId, true),
  stateKeys: []
})
export default class ConsultRepayApply extends Component {
  config = {
    navigationBarTitleText: '协商还',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      showCalendarFlag: false, // 显示或隐藏日历对话框
      showPrepareTipsModelFlag: false, // 显示或隐藏待还总金额模态框内容
      selectProductCode: 0, // 选中的协商还套餐id 0--选中的为第一个，1--选中的为第二个
      prepareDate: '', // 当前选中的还款日期
      tempPrepareDate: '', // 临时选中的还款日期
      isCheckedContract: '', // 表示合同组件是否勾选同意合同, 值为hasCheckedContract标识选中
      alreadyForceFlag: false, // 表示合同是否已经强读过
      channelAgreementPlanCfgDtoList: [{}], // 合同数组
      negotiateRepayInfo: {}, // 协商还套餐信息
      dateInfoString: '', // 下次还款日：xx月xx日，用于页面底部显示
      initProtocalData: '', // 合同信息
      judicialConfirmationDailog: false // 司法确认弹窗
    };

    this.showPage = false; // 是否渲染主页面
    this.applyNo = ''; // 建案号
    this.contractInfos = []; // 3.0合同
    this.resultRedirectUrl = Url.getParam('redirectUrl') ? encodeURIComponent(Url.getParam('redirectUrl')) : '';
    this.opRetainOpened = ''; // // 是否已经成功展示过挽留弹窗
    this.repaymentFlag = Url.getParam('repaymentFlag') || ''; // 是否还款模块跳入标识
    this.loginInfoHash = {}; // 用户信息 hash
    this.supplySuccess = Url.getParam('supplySuccess'); // 补充身份证成功标识
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
  }


  async componentWillMount() {
    await this.getLoginInfo();
    // 若是补充身份信息且成功回来的，继续流程
    if (this.supplySuccess === '1') {
      await this.handleContinue();
      return;
    }
    // 检查是否办理按钮点击离开的办理页，是则直接返回首页
    const isclose = this.checkAndClosePage();
    if (isclose) {
      return;
    }
    await this.initApplyCase();
  }

  // 处理从补充身份证回来的流程接续
  handleContinue = async () => {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'UpdateIDCardSuccess' });
    const { applyNo: applyNoFromStore } = getStore('consultReapayInfo') || {};
    const applyNo = (applyNoFromStore || Url.getParam('applyNo')) || '';
    // 1、先调用补资料接口提交信息
    await Dispatch.repayment.postLoanAddCaseData({
      applyNo,
      ...this.loginInfoHash,
      applyMaterialList: [{ materialType: 'H01' }, { materialType: 'H02' }],
    });

    // 2. 提交案件信息，如需补资料在审核页（loading页）分发
    this.submitCase();
  }

  getLoginInfo = async () => {
    const { userIdHash, custIdHash } = await getLoginInfo() || {};
    this.loginInfoHash = { userHashNo: userIdHash, custHashNo: custIdHash };
  }

  checkAndClosePage = () => {
    let closeFlag = false;
    const leaveFlag = Madp.getStorageSync('consultApplyConfirmBtnLeave', 'SESSION');
    if (leaveFlag === 'Y') {
      Util.closeOrBack('consultApplyConfirmBtnLeave');
      closeFlag = true;
    }
    return closeFlag;
  };

  // 提供放置挽留弹窗的位置
  async beforeRouteLeave(from, to, next, options) {
    try {
      // 返回时，触发op交互事件：挽留弹窗
      if ((to.path === from.path) && this.opRetainOpened !== 'SUCCESS') {
        const opRes = await this.opOnPageEvent('opPageLeave', EVENT_CODE_MAP.consultRepayRetain);
        // 标记是否打开过，如选择优惠还款留在当前页，再次进入挽留逻辑，直接next(true),不再进入op
        this.opRetainOpened = opRes;
        // 如未正确打开op，也进行返回兜底
        if (opRes !== 'SUCCESS') {
          Util.closeOrBack();
        } else {
          // 如果成功打开op，则不继续导航,取消路由变化；解决二次进入后无法返回的问题
          next(false);
          return;
        }
      } else {
        if (from.path === to.path) {
          Util.closeOrBack();
        } else {
          next(true);
        }
      }
    } catch (error) {
      console.log('error:', error);
      if (from.path === to.path) {
        Util.closeOrBack();
      } else {
        next(true);
      }
    }
  }

  async opOnPageEvent(eventName, interactionEventCode) {
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: pageId,
            opRetainClose: Util.closeOrBack, // 退出方法
            trackPageId: 'consultRepay',
            pageCode: 'consultRepay' // op用于区分挽留弹窗
          },
          // 正确打开op后，op组件调用该方法返回'SUCCESS'
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve('ERROR');
      }
    });
  }


  initApplyCase = async () => {
    const { data, ret, errCode } = await Dispatch.repayment.postLoanCustApplyCase({ serviceType: '006', ...this.loginInfoHash }) || {};
    if (ret !== '0') {
      this.jumpToRefuse(errCode);
      return;
    }

    const { applyNo, applyStatus, applySourceType, intransitApplyStatus, needMaterialList, contractInfoList, custTypeAndFileTypeList, businessHandleDays, judicialConfirmFlag: judicialConfirmFlagFromApply } = data || {};
    // 设置建案号
    this.applyNo = applyNo;
    // 设置合同信息
    this.contractInfos = contractInfoList;
    // 设置案件来源：1-自助建案（以后都是5、6）；5-非逾期自助；6-逾期自助
    const typeAboutC = ['1', '5', '6'];
    this.applySourceType = typeAboutC.includes(applySourceType) ? 'fromC' : 'fromB';
    // 需要上传的资料列表
    this.needMaterialList = needMaterialList;
    this.custTypeAndFileTypeList = custTypeAndFileTypeList;
    // 资料审核业务办理天数
    this.businessHandleDays = businessHandleDays;
    let negotiateRepayInfo = {};
    if (applyStatus === '110' || intransitApplyStatus === '110') { // 建案成功再请求套餐信息
      negotiateRepayInfo = await this.queryPackageInfo();
    }
    const applyStatusInfo = { applyStatus, needMaterialList, judicialConfirmFlagFromApply };
    const applyStatusFlag = await this.applyStatusDispatch(applyStatusInfo, ret, errCode);
    if (applyStatusFlag || ret !== '0') { // applyStatusFlag为true表明不停留在办理页，已分发至对应页面
      return;
    }

    // 判断完当前页面的状态之后展示办理页面内容
    this.showPage = true;
    // 司法确认信息
    const { judicialConfirmFlag, confirmTermCount, surplusTotalAmt } = negotiateRepayInfo || {};
    this.judicialConfirmInfo = { judicialConfirmFlag, confirmTermCount };
    this.surplusTotalAmt = surplusTotalAmt;
    // 页面埋点需要上报
    this.judicialForTrack = judicialConfirmFlag;
    const { negotiateRepayPackages } = negotiateRepayInfo || {};
    // 设置初始化的还款日期
    const initSelectNegotiateRepay = negotiateRepayPackages && negotiateRepayPackages[0];
    const { repayDay, installCnt, waiveRate, preRepayRate, preRepayWaiveRate, installType, appointThresholdAmt } = initSelectNegotiateRepay || {};
    // 将套餐信息传给试算接口
    const trailParam = {
      installTermCount: installCnt, // 分期数
      preRepayAmtRate: preRepayRate, // 首付比例
      waiveRatio: Util.floatTimes(waiveRate || 0, 100), // 办理后每期息费的减免比例
      preRepayWaiveRate, // 办理前需还款部分的息费减免比例
      installType, // 分期类型
      repayDay, // 还款日
      appointThresholdAmt // 前n-1期门槛金额
    };
    const { formatFirstRepayDate: dateInfoString, lastRepayDate } = await this.getRepayDateInformation(trailParam);


    this.setState({
      negotiateRepayInfo,
      dateInfoString, // 下次还款日期
      tempPrepareDate: repayDay, // 临时选中的还款日期
      prepareDate: repayDay, // 当前选中的还款日期
      lastRepayDate // 最后一次还款日期
    }, () => {
      const { selectProductCode } = this.state;
      // 记录默认选中的产品
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'ConsultRepayApplyPage',
        beaconContent: {
          cus: {
            source: this.applySourceType === 'fromC' ? 'C' : 'B', // 案件来源
            judicial: this.judicialForTrack,
            productIndex: selectProductCode
          }
        }
      });
    });

    // 添加挽留弹窗：解决跨模块的问题
    if (process.env.TARO_ENV === 'h5') {
      Util.pushUrlState('keepState');
    }
  }

  // 套餐查询
  queryPackageInfo = async () => {
    const { data, ret } = await Dispatch.repayment.postLoanQueryPackageInfo({ serviceType: '006', ...this.loginInfoHash }) || {};
    if (ret !== '0') {
      this.jumpToRefuse();
      return;
    }
    return data;
  }

  // 根据不同的建案状态跳转到不同的页面
  applyStatusDispatch = (applyStatusInfo, ret, errCode) => {
    if (ret !== '0') {
      this.jumpToRefuse(errCode);
    }
    // 如果dispacthFlag为true则中断当前页面渲染
    let dispatchFlag = false;
    let { applyStatus, needMaterialList, judicialConfirmFlagFromApply } = applyStatusInfo || {};
    // 如页面分发，可用参数
    setStore({
      consultReapayInfo: {
        needMaterialList: this.needMaterialList,
        consultContractInfo: this.contractInfos,
        applyNo: this.applyNo, // 案件号,
        applyStatus: applyStatus, // 案件状态
        applySorce: this.applySourceType, // 申请来源
        aliasCustTypeAndFileTypeList: this.custTypeAndFileTypeList || [], // 身份和资料补充列表
        businessHandleDays: this.businessHandleDays, // 司法审核业务办理天数
        judicialConfirmFlagFromApply
      }
    });

    if (applyStatus === '120') { // 准入拒绝
      dispatchFlag = true;
      this.jumpToRefuse(errCode);
    } else if (applyStatus === '115') { // 待补充资料
      dispatchFlag = true;
      this.toInformationPage(this.applyNo);
    } else if (applyStatus === '130') { // 待人工审核
      dispatchFlag = true;
      if (needMaterialList && judicialMaterialList('APPLY')) { // 审核中状态，人工审核打回需要重新上传资料
        this.toInformationPage(this.applyNo);
      } else if (judicialConfirmFlagFromApply === 1 || judicialConfirmFlagFromApply === 2) { // 司法审批中，中台返回一个是否走司法审批的标识
        this.toServicesReapyResult(3);
      } else { // 兜底资料审批中
        this.toServicesReapyResult(2);
      }
    } else if (applyStatus === '140') { // 待风控审核
      dispatchFlag = true;
      this.toServicesReapyResult(2);
    } else if (applyStatus === '150') { // 审核拒绝
      this.toServicesReapyResult(4);
    } else if (applyStatus === '200') { // 审核通过，待还款，跳转至支付页
      dispatchFlag = true;
      if (this.miniChannelFlag) {
        Taro.redirectTo({
          url: `/pages/express-repay/index?_windowSecureFlag=1&billType=preConsultRepay&applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}`
        });
      } else {
        Util.router.replace({
          path: `/pages/express-repay/index?_windowSecureFlag=1&applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}`,
          query: {
            billType: 'preConsultRepay'
          }
        });
      }
    } else if (applyStatus === '160') { // 审核通过，用户在提交建案信息后有还款记录，导致还款要素变更; 跳转至确认信息页
      dispatchFlag = true;
      Madp.redirectTo({ url: `/pages/consult-repay-apply/comfirm?applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}` });
    } else if (applyStatus === '210') { // 审核通过，办理成功
      dispatchFlag = true;
      this.toServicesReapyResult(1);
    } else if (applyStatus === '220') { // 审核通过，办理失败
      dispatchFlag = true;
      this.toServicesReapyResult(6);
    }

    return dispatchFlag;
  }

  // 准入拒绝页跳转，需确认错误码
  jumpToRefuse = (errCode) => {
    let status = '';
    switch (errCode) {
      case 'UMDP03681': // 您已办理过{0}服务，请前往还款首页查看每月还款金额，按约还款
        status = '1';
        break;
      case 'UMDP03680': // 您的借据不满足{0}服务办理条件，如有其他需咨询的，可联系贷后客服协商
        status = '2';
        break;
      case 'UMDP02811 ': // 综合评估暂未通过，建议及时还款保持良好个人信用
        status = '3';
        break;
      default:
        status = '3'; // 准入拒绝-兜底异常
        break;
    }
    Madp.redirectTo({
      url: `/pages/standard-service-refuse/index?serviceType=${StandardService.ConsultRepayApply}&status=${status}&repaymentFlag=${this.repaymentFlag}&errCode=${errCode}`
    });
  }
  // 贷后服务标准化结果页: 审批中（资料审批/司法审批）、审批失败、审核通过办理成功、审核通过办理失败、已提交还款处理中
  toServicesReapyResult(status) {
    const { businessHandleDays: businessHandleDaysFromStore } = getStore('consultReapayInfo');
    const businessHandleDays = this.businessHandleDays || businessHandleDaysFromStore;
    Util.router.replace({
      path: `/pages/standard-service-result/index?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}&serviceType=${StandardService.ConsultRepayApply}&status=${status}&businessHandleDays=${businessHandleDays}`,
    });
  }

  judgeNegotiateRepayPackageVaild(negotiateRepayPackages) {
    let isUnVaild = false;
    // 协商还套餐数据不存在或者存在字段但是内容为空按照异常场景处理
    if (!(negotiateRepayPackages && negotiateRepayPackages.length >= 2)) {
      isUnVaild = true;
      return isUnVaild;
    }

    if (negotiateRepayPackages && negotiateRepayPackages.length >= 2) {
      negotiateRepayPackages.forEach(item => {
        const { waiveRatio } = item || {};
        if (waiveRatio == 0) {
          isUnVaild = true;
        }
      });
    }

    return isUnVaild;
  }

  // 跳转到im
  jumpToChatPageHandler = () => {
    // const busiEntrance = Util.getBusiEntrance();
    const busiEntrance = 'DHZJ01';
    jumpToChatPage({
      busiEntrance,
      extraParam: {
        initQuestion: '我想要办理协商还'
      }
    });
    dispatchTrackEvent({
      target: this,
      event: EventTypes.BC,
      beaconId: 'SelectToChat',
      beaconContent: {
        cus: {
          source: this.applySourceType === 'fromC' ? 'C' : 'B', // 案件来源
          judicial: this.judicialForTrack
        }
      } });
  }

  // 设置当前选中的协商还套餐, 因为不同套餐合同信息发生变更需要重新强读合同
  selectProductHandler = async (productCode) => {
    const { negotiateRepayInfo, selectProductCode, prepareDate } = this.state || {};
    // 如果点击当前选中的套餐，保持当前套餐选中不做合同是否强读处理
    if (selectProductCode === productCode) {
      return;
    }
    // 更换套餐需要重新强读
    this.setState({
      selectProductCode: productCode,
      alreadyForceFlag: false
    });
    // 获取当前选中的协商还套餐
    const initSelectNegotiateRepay = negotiateRepayInfo.negotiateRepayPackages && negotiateRepayInfo.negotiateRepayPackages[productCode] || {};
    const { installCnt, waiveRate, preRepayRate, preRepayWaiveRate, installType, appointThresholdAmt } = initSelectNegotiateRepay || {};
    const param = {
      installTermCount: installCnt, // 分期数
      waiveRatio: Util.floatTimes(waiveRate || 0, 100), // 办理后每期息费的减免比例
      preRepayWaiveRate, // 办理前还的钱的息费减免比例
      installType, // 分期类型
      repayDay: prepareDate, // 还款日
      preRepayAmtRate: preRepayRate, // 首付比例
      appointThresholdAmt // 前n-1期门槛金额
    };

    const { formatFirstRepayDate: dateInfoString, lastRepayDate } = await this.getRepayDateInformation(param);
    this.setState({
      dateInfoString,
      lastRepayDate
    });

    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'SelectProduct', beaconContent: { cus: { productIndex: productCode } } });
  }

  // 确认当前选中的还款日
  selectPrepayDateHandler = async () => {
    const { negotiateRepayInfo, selectProductCode } = this.state;
    const initSelectNegotiateRepay = negotiateRepayInfo.negotiateRepayPackages && negotiateRepayInfo.negotiateRepayPackages[selectProductCode] || {};
    const { tempPrepareDate } = this.state || {};
    const { installCnt, waiveRate, preRepayWaiveRate, preRepayRate, installType, appointThresholdAmt } = initSelectNegotiateRepay || {};
    const param = {
      installTermCount: installCnt,
      waiveRatio: Util.floatTimes(waiveRate || 0, 100),
      preRepayWaiveRate,
      preRepayAmtRate: preRepayRate,
      installType,
      repayDay: tempPrepareDate,
      appointThresholdAmt
    };

    const { formatFirstRepayDate: dateInfoString, lastRepayDate } = await this.getRepayDateInformation(param);

    this.setState({
      prepareDate: tempPrepareDate,
      showCalendarFlag: false,
      dateInfoString,
      lastRepayDate,
    });
    dispatchTrackEvent({
      target: this,
      event: EventTypes.BC,
      beaconId: 'SelectPrepayDate',
      beaconContent: {
        cus: {
          prepareDate: tempPrepareDate,
          sorce: this.applySourceType === 'fromC' ? 'C' : 'B', // 案件来源
          judicial: this.judicialForTrack,
          plan: `${installCnt}+${Util.floatTimes(waiveRate || 0, 100)}%`,
          dateInfoString,
          lastRepayDate
        } } });
  }

  // 通过试算接口获取修改还款之后的合同日期及页面展示使用的日期
  getRepayDateInformation = async (param) => {
    const { data, ret, errMsg } = await Dispatch.repayment.queryNegotiateRepayTrial(param);

    if (ret !== '0') {
      this.setState({
        consultRepayErrorMsg: errMsg || '您的欠款信息有变更，无法继续办理协商还，如需办理请联系人工客服95786协助处理',
        showConsultErrorModel: true
      }, () => {
        dispatchTrackEvent({
          target: this,
          event: EventTypes.SO,
          beaconId: 'trialFail',
          beaconContent: {
            cus: {
              errMsg
            }
          }
        });
      });
      return {};
    }

    const { surplusTotalAmt, surplusTotalPrincipalAmt, surplusPayInteFeeAmt, surplusPayFineAmt, repayPlanList } = data || {};
    const length = repayPlanList && repayPlanList.length;
    if (length <= 0) {
      return {};
    } else {
      const firstRepayDateInfo = repayPlanList && repayPlanList[0];
      const lastRepayDateInfo = repayPlanList && repayPlanList[length - 1];
      const { repayDate: firstRepayDate } = firstRepayDateInfo || {};
      const { repayDate: lastRepayDate } = lastRepayDateInfo || {};

      let firstRepayMonth = firstRepayDate && firstRepayDate.substring(4, 6);
      let firstRepayDay = firstRepayDate && firstRepayDate.substring(6, 8);
      firstRepayMonth = firstRepayMonth && parseInt(firstRepayMonth);
      firstRepayDay = firstRepayDay && parseInt(firstRepayDay);
      const formatFirstRepayDate = `${firstRepayMonth}月${firstRepayDay}日`;
      this.setState({
        initProtocalData: {
          taskTotalAmt: surplusTotalAmt,
          surplusPayPrincipalAmt: surplusTotalPrincipalAmt,
          surplusPayInteFeeAmt,
          surplusPayFineAmt,
          taskThresholdAmt: firstRepayDateInfo.taskThresholdAmt,
          lastRepayDate
        }
      });
      return { formatFirstRepayDate, lastRepayDate };
    }
  }

  preApplicationInspection = async () => {
    const { isCheckedContract, selectProductCode, negotiateRepayInfo, prepareDate } = this.state;

    // 1. 先检查合同是否已勾选
    if (!isCheckedContract && (this.contractInfos || []).length > 0) {
      Madp.showToast({ title: '请阅读并同意协议', icon: 'none' });
      return;
    }

    // 2. 获取当前选中的协商还套餐的分期数
    const { negotiateRepayPackages } = negotiateRepayInfo || {};
    const selectNegotiateRepay = negotiateRepayPackages && negotiateRepayPackages[selectProductCode] || {};
    const { installCnt, waiveRate } = selectNegotiateRepay || {};
    setStore({
      consultReapayInfo: {
        needMaterialList: this.needMaterialList,
        consultContractInfo: this.contractInfos,
        applyNo: this.applyNo, // 案件号,
        applyStatus: this.applyStatus, // 案件状态
        surplusTotalAmt: this.surplusTotalAmt, // 待还总金额
        selectNegotiateRepay, // 当前选中的协商还套餐
        selectprepareDate: prepareDate, // 用户选择的还款日
        applySorce: this.applySourceType, // 申请来源
        aliasCustTypeAndFileTypeList: this.custTypeAndFileTypeList || [], // 身份和资料补充列表
        businessHandleDays: this.businessHandleDays, // 司法审核业务办理天数
      }
    });

    // 3. 0: 不弹司法确认弹窗，1/2: 弹司法确认弹窗，3: 用户所选分期数>=司法确认天数，弹出司法确认弹窗，小于不弹
    let openJudicialDailog = false;
    const { judicialConfirmFlag, confirmTermCount } = this.judicialConfirmInfo;
    if (judicialConfirmFlag !== 0) {
      if (judicialConfirmFlag === 3) {
        openJudicialDailog = Number(installCnt) >= Number(confirmTermCount);
      } else {
        openJudicialDailog = true;
      }
    }
    if (openJudicialDailog) {
      // 需要司法确认,打开弹窗
      this.setState({ judicialConfirmationDailog: true });
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconId: 'ShowJudicialConfirmDialog',
        beaconContent: {
          cus: {
            judicial: this.judicialForTrack,
            sorce: this.applySourceType === 'fromC' ? 'C' : 'B', // 案件来源
            plan: `${installCnt}+${Util.floatTimes(waiveRate || 0, 100)}%`,
            confirmTermCount: `风控输出司法确认期数-${confirmTermCount}`
          } }
      });
    } else {
      // 不需要司法确认,直接进入身份、资料补充校验流程
      await this.checkIdentityAndMaterial(true);
    }
  }

  // 新增身份校验和材料检查方法
  checkIdentityAndMaterial = async () => {
    try {
      // 1. 检查是否需要补充身份信息
      const isNeedSupplyID = judicialMaterialList('ID_CARD');

      if (isNeedSupplyID) {
        // 需要补充身份证
        const supplyParams = {
          scene: 'SCENE_SXF',
          billType: 'consult-repay',
          applyNo: this.applyNo
        };
        //  记录离开标记
        Madp.setStorageSync('consultApplyConfirmBtnLeave', 'Y', 'SESSION');
        Util.gotoSupplyInfo(supplyParams);
        return;
      }

      // 2. 不需要补充，直接提交案件
      await this.submitCase();
    } catch (error) {
      console.error('身份校验和材料检查失败:', error);
      Madp.showToast({ title: '系统异常，请稍后重试', icon: 'none' });
    }
  }

  // 提交案件
  submitCase = async () => {
    // 1. 准备提交参数
    const { surplusTotalAmt, selectprepareDate, applyNo, selectNegotiateRepay } = getStore('consultReapayInfo') || {};
    const {
      installCnt,
      waiveRate,
      appointThresholdAmt,
      preRepayAmt,
      preRepayWaiveAmt,
      preRepayRate,
      preRepayWaiveRate,
      installType
    } = selectNegotiateRepay || {};

    const param = {
      serviceType: '006',
      applyNo,
      ...this.loginInfoHash,
      negotiateRepayApplyInfo: {
        installCnt, // 分期期数
        waiveRatio: Util.floatTimes(waiveRate || 0, 100), // 减免比例
        repayDay: selectprepareDate, // 还款日
        installType, // 分期模式
        appointAmt: appointThresholdAmt // 门槛金额
      },
      surplusTotalAmt, // 待还总金额
      preRepayAmt, // 办理前门槛
      preRepayWaiveAmt, // 办理首付减免金额
      preRepayAmtRate: preRepayRate, // 办理首付总金额还款比例
      preRepayWaiveAmtRate: preRepayWaiveRate, // 办理首付息费减免比例
    };
    // 2. 提交案件信息
    const result = await Dispatch.repayment.postLoanServiceSubmitCase(param);
    const { data } = result || {};
    const { applyStatus, needMaterialList, judicialConfirmFlag: judicialConfirmFlagFromApply, businessHandleDays } = data || {};
    // 更新 consultReapayInfo 中的 needMaterialList（透传给loading页面进行分发）
    const currentConsultReapayInfo = getStore('consultReapayInfo') || {};
    setStore({
      consultReapayInfo: {
        ...currentConsultReapayInfo,
        applyStatus,
        needMaterialList,
        judicialConfirmFlagFromApply,
        businessHandleDays
      }
    });
    this.businessHandleDays = businessHandleDays;
    // 3. 埋点
    dispatchTrackEvent({
      target: this,
      event: EventTypes.BC,
      beaconId: 'submitCaseSuCess',
      beaconContent: {
        cus: {
          applyStatus,
          applyNo: applyNo,
          judicial: judicialConfirmFlagFromApply,
          plan: `${installCnt}+${Util.floatTimes(waiveRate || 0, 100)}%`,
          repayDay: selectprepareDate,
          appointAmt: appointThresholdAmt
        }
      }
    });
    // 4. 记录从提交按钮离开的缓存
    Madp.setStorageSync('consultApplyConfirmBtnLeave', 'Y', 'SESSION');
    // 5. 分发页面
    this.applyStatusNext(applyNo, applyStatus, judicialConfirmFlagFromApply);
  }

  // 根据案件结果处理
  applyStatusNext = (applyNo, status, judicialConfirmFlagFromApply) => {
    const urlParam = this.resultRedirectUrl
      ? `applyNo=${applyNo}&status=1&resultRedirectUrl=${this.resultRedirectUrl}&repaymentFlag=${this.repaymentFlag}`
      : `applyNo=${applyNo}&status=1&repaymentFlag=${this.repaymentFlag}`;
    switch (status) {
      case '115': // 等待补充资料
        this.toInformationPage(applyNo);
        break;
      case '130': // 待人工审核: 判断是资料补充审核中（此场景为人工审核中被打回需补资料，此时仍是130，资料类型中有待补充内容）、司法审核中、还是待资料补充(需要看案件结果查询有没有返回)
        if (this.needMaterialList && judicialMaterialList('APPLY')) {
          this.toInformationPage(applyNo);
        } else if (judicialConfirmFlagFromApply === 1 || judicialConfirmFlagFromApply === 2) { // 司法审批中
          this.toServicesReapyResult(3);
        } else { // 资料审批中
          this.toServicesReapyResult(2);
        }
        break;
      case '140': // 待风控审核
        Madp.redirectTo({
          url: `/pages/consult-repay-apply/loading?${urlParam}`
        });
        break;
      case '150': // 案件审核拒绝
        this.toServicesReapyResult(4);
        break;
      case '160': // 案件审核通过，待确认要素变更
        Madp.redirectTo({
          url: `/pages/consult-repay-apply/comfirm?applyNo=${applyNo}&repaymentFlag=${this.repaymentFlag}`
        });
        break;
      case '200': // 审核通过，去还款
        if (this.miniChannelFlag) {
          Taro.redirectTo({
            url: `/pages/express-repay/index?_windowSecureFlag=1&billType=preConsultRepay&applyNo=${applyNo}&repaymentFlag=${this.repaymentFlag}`
          });
        } else {
          Util.router.replace({
            path: `/pages/express-repay/index?_windowSecureFlag=1&applyNo=${applyNo}&repaymentFlag=${this.repaymentFlag}`,
            query: {
              billType: 'preConsultRepay'
            }
          });
        }
        break;
      case '210': // 案件审核通过，业务办理成功
        this.toServicesReapyResult(1);
        break;
      case '220': // 案件审核通过，业务办理失败
        this.toServicesReapyResult(6);
        break;
      default: // 异常情况
        this.toServicesReapyResult(6);
        break;
    }
  }

  // 跳转资料补充页，如果是小程序则用Taro走h5
  toInformationPage = (applyNo) => {
    if (this.miniChannelFlag) {
      Taro.redirectTo({ url: `/pages/identity/information?applyNo=${applyNo}&serviceType=consult-repay&repaymentFlag=${this.repaymentFlag}` });
    } else {
      Madp.redirectTo({ url: `/pages/identity/information?applyNo=${applyNo}&serviceType=consult-repay&repaymentFlag=${this.repaymentFlag}` });
    }
  }

  // 处理司法确认弹窗的确认按钮点击
  handleJudicialConfirm = async () => {
    // 关闭弹窗
    this.setState({ judicialConfirmationDailog: false });
    // 进入身份校验和材料检查流程
    await this.checkIdentityAndMaterial(false);
  }

  // 处理司法确认弹窗的返回修改按钮点击
  handleJudicialModify = () => {
    this.setState({
      judicialConfirmationDailog: false
    });
  }


  closeCalendarDialogHandler = () => {
    const { prepareDate } = this.state;
    this.setState({
      showCalendarFlag: false,
      tempPrepareDate: prepareDate
    });
  }

  showPrepareTipsModelHandler = () => {
    this.setState({
      showPrepareTipsModelFlag: true
    });
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'AmountTitleIcon' });
  }

  // 协商还套餐展示：两种套餐、1种套餐样式不同
  negotiateRepayPackageListRender = () => {
    const { prepareDate, selectProductCode, dateInfoString, negotiateRepayInfo } = this.state || {};
    // 待还总金额、套餐信息
    const { surplusTotalAmt, negotiateRepayPackages } = negotiateRepayInfo || {};
    // 格式化当前选中的日期用于页面显示
    const outputDate = prepareDate && parseInt(prepareDate);
    // 获取当前选中的协商还套餐中减免比例、办理首付应还款总金额、办理首付款减免金额
    const selectNegotitateRepay = negotiateRepayPackages && negotiateRepayPackages[selectProductCode] || {};
    const { waiveRate: selectWaiveRatio, preRepayTotalAmt: selectPreRepayTotalAmt, preRepayWaiveAmt: selectPreRepayWaiveAmt, preRepayAmt: selectPreRepayAmt } = selectNegotitateRepay || {};
    let themeStyle = '';
    if (themeColor === '#E60027') {
      themeStyle = `color: ${themeColor}; background-color: rgba(230, 0, 39, 0.1);`;
    }
    let negotiateRepayPackageListContent = <MUView />;
    if (negotiateRepayPackages && negotiateRepayPackages.length === 1) {
      const { expectWaiveAmt, installCnt, taskAmt, taskThresholdAmt } = negotiateRepayPackages && negotiateRepayPackages[0] || {};
      negotiateRepayPackageListContent = (
        <MUView className="consult-repay-product-single">
          <MUView className="consult-repay-product-single-list">
            <MUView className="consult-repay-product-single-amountContent">
              <MUText className="consult-repay-product-single-title">待还总金额</MUText>
              <MUIcon
                className="amount-title-icon"
                value="info"
                size="16"
                color="#a6a6a6"
                onClick={() => {
                  this.showPrepareTipsModelHandler();
                }}
              />
            </MUView>
            <MUView className="consult-repay-product-single-num">￥{surplusTotalAmt}</MUView>
          </MUView>
          {Number(selectPreRepayTotalAmt) > 0 ? <MUView className="consult-repay-product-single-list">
            <MUView className="consult-repay-product-single-title">办理前需还款金额 </MUView>
            <MUView className="consult-repay-product-single-num">{selectPreRepayAmt}元{Number(selectPreRepayWaiveAmt) > 0 ? <MUText className="undeline">{selectPreRepayTotalAmt}元</MUText> : ''}</MUView>
          </MUView> : null}
          <MUView className="consult-repay-product-single-list">
            <MUView className="consult-repay-product-single-title">月最低还款金额</MUView>
            <MUView className="wavieContent">
              <MUView className="consult-repay-product-single-num">{taskThresholdAmt}元{Number(expectWaiveAmt) > 0 ? <MUText className="undeline">{taskAmt}元</MUText> : ''}</MUView>
              {Number(expectWaiveAmt) > 0 ? <MUView className="consult-repay-product-single-waive">每月享{expectWaiveAmt}元息费优惠</MUView> : null}
            </MUView>
          </MUView>
          <MUView className="consult-repay-product-single-list">
            <MUView className="consult-repay-product-single-title">分期数</MUView>
            <MUView className="consult-repay-product-single-num">{installCnt}个月</MUView>
          </MUView>
        </MUView>);
    }
    if (negotiateRepayPackages && negotiateRepayPackages.length > 1) {
      negotiateRepayPackageListContent = (
        <MUView className="consult-repay-product">
          {/* 办理前还款信息提示 */}
          <MUView className="consult-repay-amount">
            <MUView className="consult-repay-amount-top">
              <MUView className="amount-title">
                <MUText className="amount-title-text">待还总金额</MUText>
                <MUIcon
                  className="amount-title-icon"
                  value="info"
                  size="16"
                  color="#a6a6a6"
                  onClick={() => {
                    this.showPrepareTipsModelHandler();
                  }}
                />
              </MUView>
              <MUView>
                <MUView className="amount-content">
                  ￥{surplusTotalAmt}
                </MUView>
              </MUView>
            </MUView>
            {Number(selectPreRepayTotalAmt) > 0 ? <MUView className="consult-repay-amount-bottom">
              <MUText className="amount-tipInfo">申请通过后，需先<MUText className="amount-tipInfo amount-tipInfoLight">还款{selectPreRepayAmt}元</MUText>{Number(selectPreRepayWaiveAmt) > 0 ? <MUText className="amount-tipInfo amount-tipInfoUnderLine">{selectPreRepayTotalAmt}元</MUText> : ''}，完成后办理成功</MUText>
            </MUView> : null}
          </MUView>
          {/* 套餐 */}
          <MUView className="product-space-line" />
          <MUView className="product-radio">
            <MUView className="radio-title">怎么还</MUView>
            <MUView className="radio-container">
              {
                negotiateRepayPackages.map((item, index) => {
                  const { taskAmt, taskThresholdAmt, expectWaiveAmt, installCnt, waiveRate } = item || {};
                  return (
                    <MUView className={classNames('radio-item-container', { 'radio-item-container-second': index == 1 })}>
                      <MUView className={classNames('radio-item', { 'radio-item-selected': selectProductCode === index })} onClick={() => { this.selectProductHandler(index); }}>
                        <MUView className="radio-item-content">
                          <MUView className="radio-item-label">
                            总息费减{Util.floatTimes(waiveRate || 0, 100)}%
                          </MUView>
                          <MUView className="radio-item-content">
                            <MUView className="content-price">
                              每月
                              <MUView className="content-price-amount">{taskThresholdAmt}</MUView>
                              元
                            </MUView>

                            {Number(expectWaiveAmt) > 0 ? <MUView className="content-price-undeline">
                              {taskAmt}元
                            </MUView> : null}

                            {Number(expectWaiveAmt) > 0 ? <MURichText className="content-discount" nodes={`(减免<span id=yellow style="color: #FF890E;">${expectWaiveAmt}</span>元息费)`} /> : null}
                          </MUView>

                          <MUView className="content-period">{installCnt}个月</MUView>
                        </MUView>

                      </MUView>
                    </MUView>
                  );
                })
              }
            </MUView>
          </MUView>

          <MUView className="product-chat-entry">
            <MUView
              className="entry-text"
              onClick={() => {
                this.jumpToChatPageHandler();
              }}
            >
              按其他金额还款，去协商
            </MUView>
            <MUIcon className="entry-icon" value="arrow-right" size="12" color="#333333" />
          </MUView>

          <MUView className="product-space-line" />
        </MUView>);
    }

    return (
      <MUView className="consult-repay-productContent">
        <MUView>{negotiateRepayPackageListContent}</MUView>
        <MUView
          className={`${negotiateRepayPackages && negotiateRepayPackages.length > 1 ? 'product-date-pick-marginTop' : ''} product-date-pick`}
          onClick={() => {
            if (this.applySourceType === 'fromB') { // b端建案不支持选择还款日
              return;
            } else {
              this.setState({
                showCalendarFlag: true
              });
            }
          }}
        >
          <MUView className="pick-title">还款日</MUView>
          <MUView className="pick-area">
            <MUView className="pick-area-outputDate">
              <MUText className="pick-area-outputDate-text">每月{outputDate}日</MUText>
              {this.applySourceType === 'fromC' ? <MUIcon className="entry-icon" value="arrow-down" size="16" color="#cacaca" /> : null}
            </MUView>
            {dateInfoString && <MUView className="pick-area-date">
              <MURichText nodes={`最近一个还款日<span id=yellow style="color: #FF8844; opacity: 0.8;">${dateInfoString}<span>`} />
            </MUView>}
          </MUView>
        </MUView>

        {Number(selectWaiveRatio) > 0 ? <MUView className="product-tips">
          <MUView className="product-tips-content" style={themeStyle}>
            分期期间，新产生的息费减{Util.floatTimes(selectWaiveRatio || 0, 100)}%，累计至最后一个月还款
          </MUView>
        </MUView> : null}
      </MUView>
    );
  }

  // 司法提醒弹窗
  judicialConfirmation = () => {
    const { negotiateRepayInfo, judicialConfirmationDailog, selectProductCode } = this.state;
    const { negotiateRepayPackages } = negotiateRepayInfo || {};
    // 获取当前选中的协商还套餐
    const selectNegotitateRepay = negotiateRepayPackages && negotiateRepayPackages[selectProductCode] || {};
    const { installCnt, waiveRate } = selectNegotitateRepay || {};
    const trackInfo = {
      source: this.applySourceType === 'fromC' ? 'C' : 'B', // 案件来源
      judicial: this.judicialForTrack,
      plan: `${installCnt}+${Util.floatTimes(waiveRate || 0, 100)}%`,
    };
    // 提示文案
    const confirmText = ['确认申请，提交审批', '律所工作人员与您联系，确认还款方案', '如您同意，将与您签署调解协议书', '完成服务办理'];
    return (<MUDialog
      isOpened={judicialConfirmationDailog}
      mask
      placement="bottom"
      className="judicialConfirmation"
    >
      <MUView className="judicialConfirmation-title">确认申请后，请等待工作人员联系您</MUView>
      <MUView className="judicialConfirmation-close" onClick={() => { Util.closeOrBack('consultApplyConfirmBtnLeave'); }} />
      <MUView className="judicialConfirmation-content">
        <MURichText className="judicialConfirmation-content-tips" nodes={`因您选择的还款方案周期在${selectNegotitateRepay.installCnt}个月及以上，提交申请后<span id=yellow style="color: #FF8844;">需由律所的工作人员电话联系</span>您。`} />
        {confirmText.map((item, index) => (
          <MUView className="judicialConfirmation-content-confirmText">
            <MUView className="judicialConfirmationNum">{index + 1}</MUView>
            <MUView className="judicialConfirmationText">{item}</MUView>
          </MUView>
        ))}
      </MUView>
      <MUView className="judicialConfirmation-bottom">
        {negotiateRepayPackages && negotiateRepayPackages.length === 1
          ? <MUButton className="judicialConfirmation-bottom-singleBtn" beaconId="judicialDialogConfirmApplication" beaconContent={{ cus: { ...trackInfo } }} onClick={() => { this.handleJudicialConfirm(); }}>确认申请</MUButton>
          : <MUView className="judicialConfirmation-bottom-btn">
            <MUButton className="judicialConfirmationModify" beaconId="judicialDialogReturnToModify" beaconContent={{ cus: { ...trackInfo } }} onClick={() => { this.handleJudicialModify(); }}>返回修改</MUButton>
            <MUButton className="judicialConfirmationApply" beaconId="judicialDialogConfirmApplication" beaconContent={{ cus: { ...trackInfo } }} onClick={() => { this.handleJudicialConfirm(); }}>确认申请</MUButton>
          </MUView>}
        <MUView className="judicialConfirmationAbandon" beaconId="judicialDialogAbandoning" beaconContent={{ cus: { ...trackInfo } }} onClick={() => { Util.closeOrBack('consultApplyConfirmBtnLeave'); }}>放弃申请</MUView>
      </MUView>
    </MUDialog>);
  }

  render() {
    const {
      showCalendarFlag,
      showPrepareTipsModelFlag,
      tempPrepareDate,
      initProtocalData,
      negotiateRepayInfo,
      showConsultErrorModel,
      consultRepayErrorMsg
    } = this.state || {};
    const { negotiateRepayPackages } = negotiateRepayInfo || {};
    // 选中日期弹窗4*7规格的布局
    const row = [0, 1, 2, 3];
    const cell = [1, 2, 3, 4, 5, 6, 7];

    return this.showPage ? <MUView className="consult-repay-page">
      {/* 背景图部分 */}
      <MUView className="consult-repay-page-bgOne">
        <WaRichtext content={getLocale('XSH01.XSH01WA001')} />
      </MUView>
      <MUView className="consult-repay-page-bgTwo">
        <WaRichtext content={getLocale('XSH01.XSH01WA002')} />
      </MUView>
      {/* 协商还套餐展示 */}
      {negotiateRepayPackages && negotiateRepayPackages.length > 0 ? this.negotiateRepayPackageListRender() : null}

      {/* 合同 */}
      {negotiateRepayPackages && negotiateRepayPackages.length > 0 ? <MUView className="consult-repay-bottom">
        {
          <Protocol
            trackPrefix="repayment.ConsultRepayApply"
            onChecked={(v) => { this.setState({ isCheckedContract: v }); }}
            contractInfoList={(this.contractInfos || []).filter((item) => item.contractCode !== 'GRXXSQ_DHZLSQ')}
            billExtendInfo={initProtocalData}
          />
        }

        <MUView className="consult-repay-next">
          <MUView
            className="repay-btn"
            onClick={throttle(this.preApplicationInspection, 2000)}
            style={`background :${themeColor}`}
            beaconId="consultApplySubmitBtnClick"
          >
            提交申请
          </MUView>
        </MUView>
      </MUView> : <MUView />}

      {showCalendarFlag ? <MUActionSheet
        className="calendar-container"
        isOpened={showCalendarFlag}
        onClose={this.closeCalendarDialogHandler}
      >
        <MUView className="calendar-container-title">
          <MUView className="container-title">请选择还款日</MUView>
          <MUView
            className="container-btn"
            onClick={() => {
              this.closeCalendarDialogHandler();
            }}
          >
            <MUIcon value="close2" size="16" />
          </MUView>
        </MUView>

        <MUView className="modify-payday-table">
          {
            row.map((oneRow) => {
              return (
                <MUView className="one-line">
                  {cell.map((oneCell) => {
                    const formatDate = oneCell + oneRow * 7;
                    const date = formatDate > 10 ? `${formatDate}` : `0${formatDate}`;
                    let className = 'day-num ';
                    if (date === this.defaultPayday) {
                      className += 'defaultPayday';
                    } else if (tempPrepareDate === date) {
                      className += 'check-day';
                    }
                    return (
                      <MUView
                        className="one-item"
                        onClick={() => {
                          this.setState({
                            tempPrepareDate: date
                          });
                        }}
                      >
                        <MUView className={className} style={tempPrepareDate === date ? `background: ${themeColor}` : ''}>{formatDate}</MUView>
                      </MUView>
                    );
                  })}
                </MUView>
              );
            })
          }
        </MUView>

        <MUButton className="calendar-confirm-btn" type="primary" onClick={() => { this.selectPrepayDateHandler(); }}>确认</MUButton>
      </MUActionSheet> : null}

      <MUModal
        isOpened={showPrepareTipsModelFlag}
        content="该金额为当前待还金额，分期期间仍然会产生新的息费，具体情况以您借款时签署的合同为准。"
        confirmText="知道了"
        onClose={() => {
          this.setState({
            showPrepareTipsModelFlag: false
          });
        }}
        onConfirm={() => {
          this.setState({
            showPrepareTipsModelFlag: false
          });
        }}
      />

      {/* 司法确认弹窗 */}
      {this.judicialConfirmation()}
      {/* 协商还试算失败提示弹窗 */}
      <MUModal
        className="consult-repay-modal"
        beaconId="ConsultRepayModel"
        isOpened={showConsultErrorModel}
        closeOnClickOverlay={false}
      >
        <MUView className="consult-repay-modal-container">
          <MUImage className="img" src="https://file.mucfc.com/ebn/3/0/202312/20231227152741411abe.png" />
          <MUView className="desc">{consultRepayErrorMsg}</MUView>
          <MUView className="btn" style={{ backgroundColor: themeColor }} onClick={() => Madp.navigateBack()}>返回首页</MUView>
        </MUView>
      </MUModal>
      {/* 交互式运营组件 */}
      <OpRepayment pageId={pageId} opEventKey="opPageLeave" />
    </MUView> : <MUView />;
  }
}
