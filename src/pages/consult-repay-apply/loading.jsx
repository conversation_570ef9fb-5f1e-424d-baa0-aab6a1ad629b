/* eslint-disable react/sort-comp */
import Taro, { Component } from '@tarojs/taro';
import {
  MUView, MUImage, MUDialog
} from '@mu/zui';
import Madp from '@mu/madp';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { getLoginInfo } from '@mu/business-basic';
import { getStore, setStore } from '@api/store';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import pageHoc from '@utils/pageHoc';
import { miniProgramChannel, StandardService } from '@utils/constants';
import { Url } from '@mu/madp-utils';
import { judicialMaterialList } from './common-utils';
import './loading.scss';

const IMG_LOAD = require('./img/loading.png');

const POLLING_TIME = 10; // 页面倒计时时间

@track({ event: EventTypes.PO }, {
  pageId: 'ReInstallmentLoading',
  dispatchOnMount: true,
})
@pageHoc({ title: '协商还办理' })
export default class BillExtendLoading extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showPage: false,
      time: POLLING_TIME,
    };
    this.applyNo = Url.getParam('applyNo') || ''; // 案件申请号
    this.needManualKyc = Url.getParam('needManualKyc') === '1' || false; // 是否需要人工kyc
    this.timeCountDown = null; // 时间轮询定时器
    this.timeCasePolling = null; // 案件轮询定时器
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1; // 小程序渠道标识
    this.loginInfoHash = {}; // 用户信息 hash
    this.needMaterialList = (getStore('consultReapayInfo') || {}).needMaterialList || []; // 需要上传的资料列表
    this.repaymentFlag = Url.getParam('repaymentFlag') || ''; // 还款业务跳进的标识
  }

  async componentDidMount() {
    await this.getLoginInfo();
    await this.init();
  }

  getLoginInfo = async () => {
    const { userIdHash, custIdHash } = await getLoginInfo() || {};
    this.loginInfoHash = { userHashNo: userIdHash, custHashNo: custIdHash };
  }

  componentWillUnmount() {
    // 兜底清一下定时器
    if (this.timeCasePolling) {
      clearTimeout(this.timeCasePolling);
      this.timeCasePolling = null;
    }
  }

  init = async () => {
    this.show(() => {
      this.hide();
      clearTimeout(this.timeCasePolling);
      this.toServicesReapyResult(2);
    }); // 开始倒计时
    await this.queryCustCaseDetail();
    // 上报埋点
    const { judicialConfirmFlagFromApply, applySorce, selectNegotiateRepay } = getStore('consultReapayInfo') || {};
    const { installCnt, waiveRate } = selectNegotiateRepay || {};
    dispatchTrackEvent({
      target: this,
      event: EventTypes.SO,
      beaconId: 'BillExtendLoading',
      beaconContent: {
        cus: {
          judicial: judicialConfirmFlagFromApply,
          sorce: applySorce === 'fromC' ? 'C' : 'B', // 案件来源
          plan: `${installCnt}+${Util.floatTimes(waiveRate || 0, 100)}%`,
        }
      }
    });
  }

  show = (callback) => {
    const { time } = this.state;
    if (!this.timeCountDown && time && time > 0) {
      this.setState({
        showPage: true
      }, () => {
        this.timeCountDown = setInterval(() => {
          this.setState((preState) => ({
            time: preState.time - 1
          }), () => {
            // eslint-disable-next-line no-shadow
            const { time } = this.state;
            if (time < 1) {
              this.hide();
              if (typeof callback === 'function') {
                callback(true);
              }
            }
          });
        }, 1000);
      });
    }
  }

  hide = () => {
    this.setState({
      showPage: false
    }, () => {
      if (this.timeCountDown) {
        clearInterval(this.timeCountDown);
        this.timeCountDown = null;
        // 重置轮循弹窗状态
        this.setState({ time: POLLING_TIME });
      }
    });
  }

  // 协商还案件详情查询
  async queryCustCaseDetail() {
    const { ret, data } = await Dispatch.repayment.postLoanQueryCaseDetail({
      applyNo: this.applyNo,
      ...this.loginInfoHash
    });
    // 需联调
    const { applyStatus, negotiateRepayApplyInfo } = data || {};
    if (ret === '0') {
      setStore({ negotiateRepayApplyInfo });
      this.applyStatusNext(applyStatus);
    } else {
      this.setState({ showPage: false });
      clearTimeout(this.timeCasePolling);
      this.toServicesReapyResult(2);
    }
  }

  // 根据案件结果进行处理
  applyStatusNext(status) {
    const { judicialConfirmFlagFromApply } = getStore('consultReapayInfo') || {};
    switch (status) {
      case '140': // 等待风控结果，轮询
        this.timeCasePolling = setTimeout(() => {
          this.queryCustCaseDetail();
        }, 2000);
        break;
      case '115': // 等待补充资料
        this.hide();
        clearTimeout(this.timeCasePolling);
        this.toInformationPage(this.applyNo);
        break;
      case '130': // 待人工审核: 判断是资料补充审核中（此场景为人工审核中被打回需补资料，此时仍是130，资料类型中有待补充内容）、司法审核中、还是待资料补充(需要看案件结果查询有没有返回)
        this.hide();
        clearTimeout(this.timeCasePolling);
        if (this.needMaterialList && judicialMaterialList('APPLY')) {
          this.toInformationPage(this.applyNo);
        } else if (judicialConfirmFlagFromApply === 1 || judicialConfirmFlagFromApply === 2) { // 司法审批中
          this.toServicesReapyResult(3);
        } else { // 资料审批中
          this.toServicesReapyResult(2);
        }
        break;
      case '150': // 案件审核拒绝
        this.hide();
        clearTimeout(this.timeCasePolling);
        this.toServicesReapyResult(4);
        break;
      case '160': // 案件审核通过，待确认要素变更
        this.hide();
        clearTimeout(this.timeCasePolling);
        Madp.redirectTo({
          url: `/pages/consult-repay-apply/comfirm?applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}`
        });
        break;
      case '200': // 审核通过，去还款；确认参数
        this.hide();
        clearTimeout(this.timeCasePolling);
        if (this.miniChannelFlag) {
          Taro.redirectTo({
            url: `/pages/express-repay/index?_windowSecureFlag=1&billType=preConsultRepay&applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}`
          });
        } else {
          Util.router.replace({
            path: `/pages/express-repay/index?_windowSecureFlag=1&applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}`,
            query: {
              billType: 'preConsultRepay'
            }
          });
        }
        break;
      case '210': // 案件审核通过，业务办理成功
        this.toServicesReapyResult(1);
        break;
      case '220': // 案件审核通过，业务办理失败
        this.toServicesReapyResult(6);
        break;
      default: // 异常情况
        this.hide();
        clearTimeout(this.timeCasePolling);
        Madp.showToast({
          icon: 'none',
          title: '系统繁忙，请退出后重试',
          duration: 1500,
        });
        break;
    }
  }

  // 跳转资料补充页，如果是小程序则用Taro走h5
  toInformationPage = (applyNo) => {
    if (this.miniChannelFlag) {
      Taro.redirectTo({ url: `/pages/identity/information?applyNo=${applyNo}&serviceType=consult-repay&repaymentFlag=${this.repaymentFlag}` });
    } else {
      Madp.redirectTo({ url: `/pages/identity/information?applyNo=${applyNo}&serviceType=consult-repay&repaymentFlag=${this.repaymentFlag}` });
    }
  }

  // 贷后服务标准化结果页
  toServicesReapyResult(status) {
    // 资料审核业务办理天数
    const { businessHandleDays } = getStore('consultReapayInfo') || {};
    Util.router.replace({
      path: `/pages/standard-service-result/index?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}&serviceType=${StandardService.ConsultRepayApply}&status=${status}&businessHandleDays=${businessHandleDays}`,
    });
  }

  render() {
    const { showPage, time } = this.state;
    return (
      showPage && (
        <MUDialog
          className="loading"
          isOpened
          mask
          placement="bottom"
        >
          <MUView className="loading-page">
            <MUView className="loading-page_content">
              <MUImage className="img" src={IMG_LOAD} />
              <MUView className="count">{time}</MUView>
            </MUView>
            <MUView className="loading-page_title">申请审批中...</MUView>
            <MUView className="loading-page_desc">正在处理中，请稍加等待</MUView>
          </MUView>
        </MUDialog>
      )
    );
  }
}
