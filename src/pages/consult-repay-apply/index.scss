.consult-repay-page {
  padding: 20px;
  height: 100vh;

  background-image: url('./img/bg.png');
  background-repeat: no-repeat;
  background-size: 100% auto;

  &-bgOne {
    margin-top: 30px;
    margin-left: 10px;
  }

  &-bgTwo {
    width: 75%;
    margin-top: 29px;
    margin-bottom: 50px;
    margin-left: 10px;
  }

  .consult-repay-productContent {
    background-color: #ffffff;
    background-image: url('./img/pakageBG.jpg');
    background-repeat: no-repeat;
    background-size: 100% auto;
    border-radius: 16px;
    /* 使背景图片宽度适应元素宽度 */

    .consult-repay-amount {
      padding: 30px;
      padding-right: 40px;
      box-sizing: border-box;
      overflow: hidden;

      &-top {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        border-radius: 16px;

        .amount-title {
          display: flex;
          flex-direction: row;
          align-items: center;

          .amount-title-text {
            font-size: 28px;
            font-weight: 600;
            color: #333333;
            line-height: 42px;
          }

          .amount-title-icon {
            margin-left: 20px;
          }
        }

        .amount-content {
          font-size: 28px;
          font-weight: 600;
          color: #333333;
          letter-spacing: 0;
          line-height: 28px;
        }
      }

      &-bottom {
        display: flex;
        padding-top: 12px;

        .amount-tipInfo {
          color: #808080;
          font-size: 24px;
          font-weight: 400;
          font-family: "PingFang SC";
          line-height: 28px;
        }

        .amount-tipInfoLight {
          color: #ff8844;
        }

        .amount-tipInfoUnderLine {
          text-decoration: line-through;
        }
      }
    }

    .consult-repay-product {
      border-radius: 16px;

      .product-label {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 20px 0 32px;

        .label-name {
          width: 208px;
          height: 68px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          background-image: linear-gradient(95deg, #FFEBD2 0%, #ffcfa905 100%);
          border-radius: 0 0 34px 0;
          font-size: 32px;
          font-weight: 500;
          color: #333333;
          line-height: 32px;
        }

        .label-content {
          .label-content-item {
            width: 166px;
            height: 44px;
            margin-right: 30px;
          }
        }
      }

      .product-radio {
        padding: 30px 40px 30px 40px;

        .radio-title {
          font-size: 28px;
          font-weight: 600;
          color: #333333;
          line-height: 42px;
        }

        .radio-container {
          margin-top: 24px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;

          .radio-item-container {
            width: 305px;
            height: 280px;
            background: url("./img/radio-bg.png") center center no-repeat;
            background-size: cover;

            &.radio-item-container-second {
              margin-left: 20px;
            }
          }

          .radio-item {
            width: 305px;
            height: 280px;

            &.radio-item-selected {
              background: url("./img/product-select.png") center center no-repeat;
              background-size: cover;
            }
          }

          .radio-item-content {
            .radio-item-label {
              width: 180px;
              height: 40px;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              font-size: 24px;
              color: #fff;
              background: linear-gradient(95deg, #FFBC6A 0%, #FF8100 100%);
              border-radius: 16px 0 16px 0;
            }

            .radio-item-content {
              height: 170px;
              padding-top: 24px;
              box-sizing: border-box;

              .content-price {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                font-size: 28px;
                font-weight: 500;
                color: #333333;

                .content-price-amount {
                  font-weight: 600;
                  font-size: 48px;
                  color: #FF890E;
                  line-height: 48px;
                }
              }

              .content-price-undeline {
                margin-top: 10px;
                padding-right: 30px;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-end;
                font-weight: 400;
                font-size: 22px;
                color: rgba(148, 77, 24, 0.5);
                line-height: 22px;
                text-decoration: line-through;
              }

              .content-discount {
                margin-top: 18px;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                color: #944d18;
                font-size: 24px;
                font-weight: 400;
                line-height: 24px;
                letter-spacing: 0;

              }
            }

            .content-period {
              height: 70px;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              font-weight: 500;
              font-size: 30px;
              color: #944D18;
              text-align: center;
              line-height: 30px;
            }
          }
        }


      }

      .product-chat-entry {
        padding-right: 40px;
        padding-bottom: 30px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        opacity: 0.8;

        .entry-text {
          font-size: 26px;
          line-height: 42px;
          font-weight: 400;
          color: #333333;
          font-weight: 400;
        }

        .entry-icon {
          opacity: 0.8;
        }
      }
    }

    .consult-repay-product-single {
      display: flex;
      flex-direction: column;
      padding-top: 30px;

      &-list {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-bottom: 24px;
        box-sizing: border-box;
        margin-left: 40px;
        margin-right: 40px;
      }

      &-amountContent {
        display: flex;
        align-items: center;
      }

      .amount-title-icon {
        margin-left: 12px;
      }

      &-title {
        color: #333333;
        font-size: 28px;
        font-weight: 600;
        font-family: "PingFang SC";
        line-height: 42px;
      }

      &-num {
        color: #333333;
        font-size: 28px;
        font-weight: 400;
        font-family: "PingFang SC";
        line-height: 42px;
      }

      .wavieContent {
        text-align: right;
      }

      &-waive {
        color: #ff8844;
        font-size: 24px;
        font-weight: 400;
        font-family: "PingFang SC";
        line-height: 28px;
        margin-top: 12px;
      }

      .undeline {
        color: #a6a6a6;
        font-size: 28px;
        font-weight: 400;
        font-family: "PingFang SC";
        line-height: 42px;
        text-decoration: line-through;
        margin-left: 12px;
      }
    }

    .product-date-pick {
      display: flex;
      flex-direction: row;
      align-items: start;
      justify-content: space-between;
      padding: 0 40px 30px 40px;

      &-marginTop {
        margin-top: 30px;
      }

      .pick-title {
        font-size: 28px;
        font-weight: 600;
        color: #333333;
        line-height: 42px;
      }

      .pick-area {
        color: #333333;
        font-size: 28px;
        font-weight: 400;
        font-family: "PingFang SC";
        line-height: 42px;
      }


      .pick-area {
        display: flex;
        flex-direction: column;
        align-items: end;

        &-outputDate {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;

          &-text {
            color: #333333;
            font-size: 28px;
            font-weight: 500;
            line-height: 42px;
          }
        }

        &-date {
          margin-top: 12px;
          color: #808080;
          font-size: 24px;
          font-weight: 400;
          font-family: "PingFang SC";
          line-height: 28px;
        }


        .entry-icon {
          margin-left: 8px;
        }
      }
    }

    .product-tips {
      padding: 0 30px 30px 30px;

      .product-tips-content {
        width: 650px;
        height: 56px;
        text-align: center;
        background: rgba(243, 243, 243, 0.5);
        border-radius: 8px;
        font-weight: 400;
        font-size: 24px;
        color: #FF8844;
        line-height: 56px;
        box-sizing: border-box;
        opacity: 0.8;
      }
    }

    .product-space-line {
      height: 1px;
      background: #e5e5e5;
      margin: 0 30px;
    }

  }

  .consult-repay-bottom {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #fff;

    .extend-contract-checker {
      height: 92px;
      padding-left: 30px;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
    }

    .consult-repay-next {
      height: 160px;
      padding: 30px;
      box-sizing: border-box;
      border-top: 1px solid #e5e5e5;

      .repay-btn {
        width: 690px;
        height: 100px;
        border-radius: 316px;
        opacity: 1;
        background: #3477ff;

        color: #ffffff;
        text-align: center;
        font-size: 36px;
        font-weight: 600;
        font-family: "PingFang SC";
        line-height: 100px;
      }
    }
  }


  .calendar-container {
    .calendar-container-title {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      padding-top: 30px;
      font-size: 36px;
      font-weight: 500;
      color: #333333;
      position: relative;

      .container-btn {
        position: absolute;
        right: 30px;
        top: 24px;
        height: 80px;
      }
    }

    .modify-payday-table {
      width: 100%;
      padding: 0 4px;
      box-sizing: border-box;
      margin-top: 34px;

      .one-line {
        height: 92px;

        .one-item {
          width: 60px;
          height: 60px;
          display: inline-block;
          margin: 0 23px;
          font-size: 28px;
          text-align: center;
          line-height: 60px;

          .day-num {
            width: 60px;
            height: 60px;
            line-height: 60px;
            color: #333333;
            background: #f3f3f3;
            border-radius: 50%;
          }

          .defaultPayday {
            background: #F76F63;
            border-radius: 50%;
            color: #fff;
          }

          .check-day {
            border-radius: 50%;
            background: #3477FF;
            color: #fff;
          }
        }

      }

    }

    .calendar-confirm-btn {
      margin: 32px 30px 60px;
    }

  }

  .judicialConfirmation {
    .mu-dialog__container {
      position: absolute;
      transform: translateX(-50%);
      top: auto;
      bottom: 0;
      width: 100%;
    }

    .mu-dialog__content {
      padding: 40px !important;

    }

    &-title {
      font-size: 36px;
      font-weight: 600;
      font-family: "PingFang SC";
      line-height: 66px;
      text-align: left;
      margin-bottom: 24px;
    }

    &-close {
      position: absolute;
      top: 34px;
      right: 14px;
      box-sizing: border-box;
      padding: 30px;
    }

    &-close::before,
    &-close::after {
      content: "";
      position: absolute;
      top: 0;
      height: 32px;
      width: 2px;
      background-color: #A6A6A6;
    }

    &-close::before {
      transform: rotate(45deg);
    }

    &-close::after {
      transform: rotate(-45deg);
    }

    &-content {
      display: flex;
      flex-direction: column;

      &-tips {
        color: #808080;
        font-size: 28px;
        font-weight: 400;
        font-family: "PingFang SC";
        line-height: 42px;
        text-align: left;
        margin-bottom: 40px;
      }

      &-confirmText {
        display: flex;
        flex-direction: row;
        margin-bottom: 30px;
      }

      .judicialConfirmationNum {
        margin-right: 12px;
        width: 35px;
        height: 35px;
        background: #3477ff;
        border-radius: 50%;
        color: #ffffff;
        font-size: 24px;
        font-weight: 600;
        font-family: "PingFang SC";
        line-height: 35px;
      }

      .judicialConfirmationText {
        color: #333333;
        font-size: 28px;
        font-weight: 600;
        font-family: "PingFang SC";
        line-height: 42px;
      }
    }

    &-bottom {
      margin-top: 34px;

      &-singleBtn {
        width: 670px;
        height: 100px;
        border-radius: 50px;
        background: #3477ff;
        font-size: 36px;
        font-weight: 600;
        font-family: "PingFang SC";
        color: #ffffff;
        line-height: 100px;
      }

      &-btn {
        display: flex;
        font-size: 36px;
        font-weight: 600;
        font-family: "PingFang SC";

        .judicialConfirmationModify {
          width: 330px;
          height: 100px;
          border-radius: 50px;
          border: 2px solid #3477ff;
          color: #3477ff;
          line-height: 100px;
        }

        .judicialConfirmationApply {
          width: 320px;
          height: 100px;
          border-radius: 50px;
          border: 0 solid #979797;
          background: #3477ff;
          color: #ffffff;
        }
      }

      .judicialConfirmationAbandon {
        margin-top: 30px;
        color: #808080;
        text-align: center;
        font-size: 28px;
        font-weight: 400;
        font-family: "PingFang SC";
        line-height: 42px;
      }
    }
  }
}

.consult-repay-modal {
  .mu-modal__container {
    padding: 60px 40px 10px 40px;
    width: 480px;
  }

  &-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .img {
      width: 120px;
      height: 120px;
    }

    .title {
      margin-top: 40px;
      height: 54px;
      color: #333333;
      text-align: center;
      font-size: 36px;
      font-weight: 600;
      line-height: 54px;
    }

    .desc {
      margin-top: 20px;
      color: #808080;
      text-align: center;
      font-size: 28px;
      line-height: 42px;
    }

    .btn {
      width: 480px;
      height: 88px;
      margin-top: 40px;
      margin-bottom: 30px;
      background: #3477FF;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 36px;
      color: #FFFFFF;
    }
  }
}