import Util from '@utils/maxin-util';
import { getStore } from '@api/store';

// 判断是否有需要上传的资料类型
export const judicialMaterialList = (checkType = 'APPLY') => {
  let needFlag = false;
  // 身份证补充回来从store中获取，this中无needMaterialList
  const { needMaterialList = [] } = getStore('consultReapayInfo') || {};
  if (Util.isEmpty(needMaterialList)) {
    return needFlag;
  }
  // 定义不同类型的材料代码映射
  const MATERIAL_TYPE_MAP = {
    // 证明资料类型：依次为重大灾害证明、疾病证明、社会贡献证明、资产证明、失业证明、确诊证明
    APPLY: ['C02', 'C04', 'C06', 'C07', 'C08', 'C09', '999'],
    // 身份证类型
    ID_CARD: ['H01', 'H02']
  };
  // 获取需要检查的材料类型数组
  const materialTypeToCheck = MATERIAL_TYPE_MAP[checkType];

  if (!materialTypeToCheck) {
    console.warn(`未知的材料类型: ${checkType}`);
    return false;
  }

  // 待上传资料中是否有指定类型的材料且待上传, 0-待上传，1-已上传，2-待重传
  // 1. 身份证类型，只要有一个没传或者待重传，就需要去身份证补充
  if (checkType === 'ID_CARD') {
    needFlag = (needMaterialList || []).some(item => {
      return materialTypeToCheck.includes(item.materialType) && (item.materialStatus === '0' || item.materialStatus === '2');
    });
  } else {
    // 2. 证明资料类型，多选一：
    // 如果至少有一个材料是待重传(2)，则需要上传
    // 如果全部状态都是0，则需要上传
    const hasToReupload = (needMaterialList || []).some(item =>
      materialTypeToCheck.includes(item.materialType) && item.materialStatus === '2'
    );
    const allPending = (needMaterialList || []).every(item =>
      materialTypeToCheck.includes(item.materialType) && item.materialStatus === '0'
    );

    needFlag = hasToReupload || allPending;
  }

  return needFlag;
};