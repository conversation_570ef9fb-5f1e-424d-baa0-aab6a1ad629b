.loading {

  .mu-dialog__container {
    position: absolute;
    transform: translateX(-50%);
    top: auto;
    bottom: 0;
    width: 100%;


  }

  .mu-dialog__content {
    height: 800px;
    padding: 0px !important;
    background-image: url('./img/loading-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;

  }



  &-page {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;


    @keyframes img-loading {
      from {
        transform: translate(-50%, -50%) rotate(0deg);
      }

      to {
        transform: translate(-50%, -50%) rotate(360deg);
      }
    }

    &_content {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 200px;
      width: 120px;
      height: 120px;

      .img {
        position: absolute;
        left: 50%;
        top: 50%;
        height: 120px;
        width: 120px;
        opacity: 1;
        animation: img-loading 1s linear none 0s infinite;
        transform: translate(-50%, -50%) !important; // 提取为静态样式
      }

      .count {
        opacity: 1;
        color: #333333;
        text-align: center;
        font-size: 40px;
        font-weight: 400;
        line-height: 40px;
      }
    }

    &_title {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 40px;
      color: #333333;
      text-align: center;
      font-size: 40px;
      font-weight: 600;
      line-height: 60px;
    }

    &_desc {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 26px;
      color: #808080;
      text-align: center;
      font-size: 28px;
      height: 42px;
      line-height: 42px;
      font-weight: 400;
    }
  }
}