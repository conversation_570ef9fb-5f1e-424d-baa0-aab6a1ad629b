import './index.scss';

const Test1Page = () => {
  return (
    <div className="test1-container">
      {/* 顶部渐变背景 */}
      <div className="gradient-bg">
        <div className="header">
          <div className="back-arrow" />
          <div className="title">再分期</div>
        </div>
        
        <div className="deer-image" />
        
        <div className="error-message">
          抱歉，您暂不符合办理条件
        </div>
      </div>
      
      {/* 主要内容区域 */}
      <div className="content-card">
        <div className="error-detail">
          您已长时间逾期，需一次性偿还所有欠款，不满足本服务办理条件。如有其他需咨询的，可联系客服协商
        </div>
        
        <button className="contact-button">
          联系在线客服
        </button>
      </div>
      
      {/* FAQ区域 */}
      <div className="faq-section">
        <div className="faq-title">您可能想问</div>
        
        <div className="faq-item">
          <div className="faq-question">
            <span className="question-icon">问</span>
            办理再分期需要什么条件？
          </div>
          
          <div className="faq-answer">
            <div className="answer-item">1.办理再分期需要以下条件</div>
            <div className="answer-detail">
              a.借据剩余待还本金不低于100元
              b.借据的借款期限不太于36期
              c.借据的还款方式为"等额还款"或"本金按期均摊"
              d.在近90天内未办理过再分期服务
              e. 部分联合贷借据不支持办理再分期
            </div>
            
            <div className="answer-item">2.根据可办理时间</div>
            <div className="answer-detail">
              a.非借款当天，且未到借据最后一期应还款日
            </div>
            
            <div className="answer-item">3.可办理的客户需满足以下条件</div>
            <div className="answer-detail">
              a.仅对受邀客户，您可在还款页-再分期入口查看具体可办理的借据
              b.逾期超过60天以上，不可办理再分期
            </div>
          </div>
        </div>
      </div>
      
      {/* 返回首页按钮 */}
      <div className="home-button">
        返回首页
      </div>
      
      {/* 底部适配条 */}
      <div className="bottom-bar" />
    </div>
  );
};

export default Test1Page;
