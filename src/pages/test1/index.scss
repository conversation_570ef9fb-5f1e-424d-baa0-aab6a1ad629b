.test1-container {
  width: 100%;
  min-height: 100vh;
  background-color: #F3F3F3;
  position: relative;
  font-family: 'PingFang SC', sans-serif;
  
  .gradient-bg {
    width: 100%;
    height: 450px;
    background: linear-gradient(179deg, #FFFFFF 0%, rgba(238, 244, 255, 0.69) 52%, rgba(209, 224, 255, 0.6) 69%, rgba(217, 229, 255, 0.6) 84%, rgba(134, 174, 255, 0) 99%);
    position: relative;
    
    .header {
      display: flex;
      align-items: center;
      padding: 106px 0 0 20px;
      
      .back-arrow {
        width: 21px;
        height: 38px;
        background-color: #171717;
      }
      
      .title {
        margin-left: 321px;
        font-size: 36px;
        font-weight: 600;
        color: #333333;
        line-height: 50px;
      }
    }
    
    .deer-image {
      position: absolute;
      right: 530px;
      top: 200px;
      width: 140px;
      height: 165px;
      background-image: url('https://image-resource.mastergo.com/84465026348142/88842276172961/e8bc62d9aa438aa59615a7dc14f17c2e.png');
      background-size: contain;
      background-repeat: no-repeat;
    }
    
    .error-message {
      margin: 252px 0 0 60px;
      font-size: 40px;
      font-weight: 600;
      color: #333333;
      line-height: 60px;
      text-shadow: 0px 1px 2px 0px #286CDA;
    }
  }
  
  .content-card {
    width: 710px;
    height: 344px;
    margin: 336px auto 0;
    background: #FFFFFF;
    border-radius: 24px;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.06);
    padding: 20px;
    
    .error-detail {
      font-size: 28px;
      color: #333333;
      line-height: 42px;
      margin-bottom: 40px;
    }
    
    .contact-button {
      width: 630px;
      height: 100px;
      background: #3477FF;
      border-radius: 50px;
      border: none;
      font-size: 36px;
      font-family: 'PingFangSC-Medium';
      color: #FFFFFF;
      line-height: 36px;
      cursor: pointer;
    }
  }
  
  .faq-section {
    width: 710px;
    margin: 700px auto 0;
    background: linear-gradient(180deg, #EEF3FC 0%, #FFFFFF 20%);
    border-radius: 16px;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.06);
    padding: 20px;
    
    .faq-title {
      font-size: 32px;
      font-family: 'PingFangSC-Semibold';
      color: #333333;
      line-height: 48px;
      margin-bottom: 20px;
    }
    
    .faq-question {
      display: flex;
      align-items: center;
      font-size: 28px;
      font-weight: 600;
      color: #333333;
      line-height: 48px;
      
      .question-icon {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 36px;
        height: 36px;
        background: #3477FF;
        border-radius: 50%;
        color: #FFFFFF;
        font-size: 22px;
        margin-right: 14px;
      }
    }
    
    .answer-item {
      font-size: 24px;
      font-weight: 600;
      color: #333333;
      line-height: 48px;
      margin-top: 20px;
    }
    
    .answer-detail {
      font-size: 24px;
      color: #808080;
      line-height: 36px;
      margin-left: 20px;
    }
  }
  
  .home-button {
    width: 144px;
    height: 54px;
    margin: 1554px auto 0;
    font-size: 36px;
    font-weight: 600;
    color: #3477FF;
    line-height: 54px;
    text-align: center;
    cursor: pointer;
  }
  
  .bottom-bar {
    width: 100%;
    height: 68px;
    margin-top: 1754px;
    background: #F3F3F3;
    
    &::after {
      content: '';
      display: block;
      width: 240px;
      height: 10px;
      margin: 0 auto;
      background: #B3B3B3;
      border-radius: 5px;
    }
  }
}
