import { Component } from '@tarojs/taro';
import { WebView } from '@tarojs/components';
import { Url, isCmb } from '@mu/madp-utils';
import Madp from '@mu/madp';

import './index.scss';

export default class IframePage extends Component {
  state = {
    src: ''
  }

  componentWillMount() {
    const srcUrl = Url.getParam('pageUrl');
    this.setState({
      src: srcUrl,
    });
  }

  onLoad(e) {
    Madp.setNavigationBarTitle({ title: e.target.contentDocument.title });
    if (isCmb()) {
      // 修改webview加载其它使用原生导航栏的模块时，导致页面双导航栏的问题
      Madp.setNavigationBarUI('hideNavigationBar');
    }
  }

  render() {
    const { src } = this.state;
    return (
      <WebView className="web-view" src={src} onLoad={this.onLoad} frameBorder="0" />
    );
  }
}
