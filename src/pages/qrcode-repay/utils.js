import Madp from '@mu/madp';
import Util from '@utils/maxin-util';

export const alih5pay = (r) => {
  if (r && r.alipayUrl) {
    const div = document.createElement('div');
    div.innerHTML = r.alipayUrl;
    document.body.appendChild(div);
    div.getElementsByTagName('form')[0].submit();
  }
};

export const wxh5pay = (r) => {
  if (r && r.prepayId) {
    const url = `${r.prepayId}&redirect_url=${encodeURIComponent(`${window.location.origin + window.location.pathname}#/pages/qrcode-repay/success`)}`;
    window.location.href = url;
  }
};

export const wxjspay = (r, callback) => {
  Madp.chooseWXPay({
    timeStamp: r.timestamp,
    nonceStr: r.nonceStr,
    package: r.packageValue,
    signType: r.signType,
    paySign: r.sign,
    success: (result) => {
      Util.router.replace({
        path: '/pages/qrcode-repay/success',
        query: {}
      });
    },
    fail: (result) => {
      if (result === 'cancel') {
        callback(r, 'wechat');
      } else {
        Madp.showToast({ title: '微信支付失败', icon: 'none', duration: 2000 });
      }
    },
    cancel: (result) => {
      Madp.showToast({ title: '微信支付取消', icon: 'none', duration: 2000 });
    }
  });
};
