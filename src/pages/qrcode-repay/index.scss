@import '../../components/weapp/index.scss';

.repayment-for-others-qrcode {
  /* stylelint-disable-next-line */
  font-family: 'PingFangSC-Regular';

  .logo {
    display: block;
    width: 190px;
    height: 44px;
    padding: 30px 0 0 30px;
  }

  .topView {
    background: #fff;
  }

  .at-input__title {
    width: 250px;
    max-width: 250px;
  }

  .bottomConfirmButton {
    margin: 25px 30px;
  }

  .title {
    width: 100%;
    font-size: 30px;
    text-align: center;
    padding-top: 20px;
    color: #666;
  }

  .split {
    height: 24px;
  }

  .input-area {
    position: relative;

    .edit-img {
      position: absolute;
      width: 30px;
      height: 30px;
      bottom: 60px;
      left: 50%;
      pointer-events: none;
    }
  }

  .amountInput {
    text-align: center;
    color: #333;
    background-color: transparent;

    input {
      font-size: 80px;
      border: none;
      text-align: center;
    }

    input:focus {
      outline: none;
    }

    &.at-input::after {
      border-bottom: 0;
    }
  }

  .qrcode-repay-bubble {
    margin-top: -32px;
    padding-bottom: 50px;
  }

  .amountTip {
    position: relative;
    color: #dc132c;
    font-size: 28px;
    padding: 0 30px 40px;
    line-height: normal;
  }

  .amountTip::after {
    content: "";
    position: absolute;
    transform-origin: center;
    box-sizing: border-box;
    pointer-events: none;
    top: auto;
    left: 0.64rem;
    right: 0;
    bottom: 0;
    transform: scaleY(0.5);
    /* stylelint-disable-next-line */
    border-bottom: 1PX solid #E5E5E5;
  }

  .fixNumBtn {
    display: inline-block;
    font-size: 28px;
    color: #3477FF;
  }

  .repayinfo {
    background: #FFF;
    display: flex;
    height: 100px;
    font-size: 32px;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    color: #333;

    .check_but {
      color: #3477FF;
      margin-left: 40px;

      &.disabled {
        color: #A6A6A6;
      }
    }
  }

  .repayinfo-split {
    margin-left: 30px;
    transform: scaleY(0.5);
    /* stylelint-disable-next-line */
    border-bottom: 1PX solid #E5E5E5;
  }

  .tips {
    background-color: #fff;
    font-size: 28px;
    padding: 40px 10px 40px 30px;
    margin-top: 24px;

    .tip {
      margin-bottom: 0 0 10px;
      font-size: 26px;
      color: #808080;

      &.red {
        color: #FE5A5F;
      }
    }

    .tipsTitle {
      line-height: 28px;
      margin-bottom: 24px;

      .tipsImage {
        width: 30px;
        height: 30px;
        margin-right: 8px;
      }
    }
  }

  .pay-way-title {
    font-size: 26px;
    height: 26px;
    margin-left: 30px;
    padding: 20px 0;
    margin-top: 20px;
    color: #808080;
  }

  .pay-way-item {
    height: 70px;
    display: flex;
    padding: 35px 30px;
    align-items: center;

    &:active {
      background-color: #eee;
    }

    &-icon {
      width: 70px;
      height: 70px;
      margin-right: 30px;

      .image {
        width: 70px;
        height: 70px;
      }
    }

    &-content {
      flex: 1;
      height: 70px;
      line-height: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &-title {
        font-size: 32px;
        color: #333;
      }

      &-desc {
        font-size: 24px;
        color: #a6a6a6;
      }
    }

    &-checkbox {
      width: 40px;
      height: 40px;

      &-icon {
        display: block;
      }
    }
  }

  .pay-way-item-split {
    height: 1px;
    background: #E6EBF0;
  }

  .repayOthersModal {
    .mu-modal__content {
      text-align: unset;
    }
  }

  .at-input {
    margin-bottom: unset;
  }

  .mucfc-info {
    width: 690px;
    height: 415px;
    margin: 30px;
    padding-bottom: 120px;
  }
}

.name-check-modal {
  .name-line {
    margin: 28px 0 24px;
    display: flex;
    height: 50px;
    align-items: center;
    justify-content: center;
  }

  .name-input {
    width: 48px;
    height: 48px;
    line-height: 48px;
    /* stylelint-disable-next-line */
    border: 1PX solid #808080;
    padding: 0;
    margin-right: 10px;

    input {
      padding: 0;
    }
  }
}

.fee-reduce-rule {
  background: #FFF;
  .rules-title {
    text-align: center;
    padding-top: 40px;
    margin-bottom: 46px;
    font-size: 28px;
    color: #808080;
    line-height: 28px;
  }
  .rules-li {
    padding: 0 30px 50px;
    display: flex;
    justify-content: space-between;
    font-size: 32px;
    line-height: 32px;
  
    .color-amt {
      color: #FF8844;
    }
  
    &:nth-child(2) {
      .color-amt {
        font-size: 48px;
      }
    }
  }
}

.tipModal-text__highlight {
  color: #FE5B5E;
}
