/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import { useState, useEffect } from '@tarojs/taro';
import CouponTips from '../couponTips';
import { repayTransTrialApi } from '../../utils/requests';
import {
  MUView,
  MUText,
  MUIcon
} from '@mu/zui';
import OverpayDrawer from '@components/overpay-drawer';

import './index.scss';

/**
 * 优惠券提示组件
 */
export default function WaiveDetailInfo({
  isFeeReduce,
  rulesList,
  payoffAmt,
  feeReduceAmt,
  overpayInfo,
  couponObject,
  selectedCoupon,
  totalWaiveAmt,
  settleWaiveAmt,
  isSupportNegotiateRepay,
  onClickCoupon,
  trialParam,
}) {
  const [canWaiveAmt, setCanWaiveAmt] = useState(0);
  // 未选优惠券时，试算预计省金额
  useEffect(async () => {
    const { availableCouponDetailList } = couponObject;
    if (!selectedCoupon
        && availableCouponDetailList && availableCouponDetailList.length > 0) {
      const { amount, advanceBillList, userInfo } = trialParam;
      const { totalWaiveAmt: waiveAmt } = await repayTransTrialApi(amount, availableCouponDetailList[0], advanceBillList, userInfo);
      setCanWaiveAmt(waiveAmt);
    }
  }, [selectedCoupon]);

  const { overPayAmtRepay, availableAmount, remitTotalAmount, preRepayAmt } = overpayInfo;
  const showOverPay = Number(overPayAmtRepay || '0.00') > 0 || Number(remitTotalAmount || '0.00') > 0 || Number(preRepayAmt || '0.00') > 0;
  const [showOverpayDrawer, setShowOverpayDrawer] = useState(false);
  return isFeeReduce ? (
    <MUView>
      <MUView className="fee-reduce-rule">
        <MUView className="rules-title">- 还款减免规则 -</MUView>
        {
          rulesList.map((rule = {}) => Number(rule.waiveAmt) > 0 && (
            <MUView className="rules-li">
              <MUView className="left">还{rule.repayAmt}元<MUText>{rule.repayAmt === payoffAmt ? '' : '以上'}</MUText></MUView>
              <MUView className="right">减免<MUText className="color-amt">{rule.waiveAmt}</MUText>元</MUView>
            </MUView>
          ))
        }
      </MUView>
      <MUView className="repayinfo bottom-split top-split">
        <MUView>可减免金额</MUView>
        <MUView>{feeReduceAmt}元</MUView>
      </MUView>
      {/* 溢缴款 */}
      {showOverPay ? (
        <MUView className="repayinfo top-split">
          <MUView>小招荷包</MUView>
          <MUView
            className="repayinfo__content"
            beaconId="ShowOverpayDrawer"
            onClick={() => setShowOverpayDrawer(true)}
          >
            <MUText className="highlight-text">{`-${Number(availableAmount).toFixed(2)}元`}</MUText>
            <MUIcon value="arrow-right" size={14} color="#CACACA" />
          </MUView>
        </MUView>
      ) : null}
      <MUView className="repayinfo-split" />
      <OverpayDrawer
        showOverpayDrawer={showOverpayDrawer}
        title="小招荷包"
        overpayBalance={Number(overPayAmtRepay || '0.00').toFixed(2)}
        remitTotalAmount={Number(remitTotalAmount || '0.00').toFixed(2)}
        preRepayAmt={Number(preRepayAmt || '0.00').toFixed(2)}
        onClose={() => setShowOverpayDrawer(false)}
        onTopCloseClick={() => setShowOverpayDrawer(false)}
      />
    </MUView>
  )
    : (
      <MUView>
        {/* 优惠券展示栏 */}
        <CouponTips
          couponObject={couponObject}
          selectedCoupon={selectedCoupon}
          totalWaiveAmt={totalWaiveAmt}
          canWaiveAmt={canWaiveAmt}
          onClick={() => { onClickCoupon && onClickCoupon(); }}
          isSupportNegotiateRepay={isSupportNegotiateRepay}
        />
        {Number(settleWaiveAmt) > 0 && <MUView className="repayinfo top-split">
          <MUView>随机立减</MUView>
          <MUView className="highlight-text">-{settleWaiveAmt}元</MUView>
        </MUView>}
        {/* 溢缴款 */}
        {showOverPay > 0 ? (
          <MUView className="repayinfo top-split">
            <MUView>小招荷包</MUView>
            <MUView
              className="repayinfo__content"
              beaconId="ShowOverpayDrawer"
              onClick={() => setShowOverpayDrawer(true)}
            >
              <MUText className="highlight-text">{`-${Number(availableAmount).toFixed(2)}元`}</MUText>
              <MUIcon value="arrow-right" size={14} color="#CACACA" />
            </MUView>
          </MUView>
        ) : null}
        <MUView className="repayinfo-split" />
        <OverpayDrawer
          showOverpayDrawer={showOverpayDrawer}
          title="小招荷包"
          overpayBalance={Number(overPayAmtRepay || '0.00').toFixed(2)}
          remitTotalAmount={Number(remitTotalAmount || '0.00').toFixed(2)}
          preRepayAmt={Number(preRepayAmt || '0.00').toFixed(2)}
          onClose={() => setShowOverpayDrawer(false)}
          onTopCloseClick={() => setShowOverpayDrawer(false)}
        />
      </MUView>
    );
}
