/* eslint-disable react/prop-types */
/* eslint-disable max-len */
import { useState, useEffect } from '@tarojs/taro';
import {
  MUView,
  MUText,
  MUIcon,
} from '@mu/zui';
import './index.scss';

/**
 * 优惠券提示组件
 */
export default function CouponTips({
  couponObject = {},
  onClick,
  selectedCoupon,
  totalWaiveAmt,
  canWaiveAmt,
  isSupportNegotiateRepay,
}) {
  // 0:无券隐藏, 1:有券已选, 2: 有券未选, 3: 有券不可用，4：有协商还账单且有券
  const [tipType, setTipType] = useState(0);
  const {
    availableCouponDetailList = [],
    appExclusiveCouponDetailList = [],
    unavailableCouponDetailList = []
  } = couponObject;

  useEffect(() => {
    if (isSupportNegotiateRepay && (availableCouponDetailList.length || unavailableCouponDetailList.length || appExclusiveCouponDetailList.length)) {
      setTipType(4);
    } else if (selectedCoupon) {
      setTipType(1);
    } else if (availableCouponDetailList.length) {
      setTipType(2);
    } else if (unavailableCouponDetailList.length || appExclusiveCouponDetailList.length) {
      setTipType(3);
    } else {
      setTipType(0);
    }
  }, [selectedCoupon, couponObject]);

  const handleClick = () => {
    if (tipType > 0 && tipType !== 4) {
      onClick && onClick();
    }
  };
  return (
    tipType > 0 && (
      <MUView onClick={handleClick} className="repayinfo">
        <MUView>优惠券</MUView>
        <MUView className="repay-wrapper-coupon-extra">
          {tipType === 1 && <MUView>已省<MUText className="highlight-text">{totalWaiveAmt}</MUText>元</MUView>}
          {tipType === 2 && <MUView>未选优惠{canWaiveAmt && <MUText>，预计省<MUText className="highlight-text">{canWaiveAmt}</MUText>元</MUText>}</MUView>}
          {tipType === 3 && (
            <MUView>
              <MUText className="highlight-text">
                {unavailableCouponDetailList.length + appExclusiveCouponDetailList.length}
              </MUText>
              张还款券，查看使用条件
            </MUView>
          )}
          {tipType === 4 && <MUView className="grey-text">协商还不支持使用</MUView>}
          {
            tipType !== 4 && (
              <MUView className="repay-wrapper-coupon-extra-icon">
                <MUIcon value="arrow-right" size={14} color="#CACACA" />
              </MUView>
            )
          }
        </MUView>
      </MUView>
    )
  );
}
