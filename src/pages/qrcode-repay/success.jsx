import { Component } from '@tarojs/taro';
import {
  MUView, MUImage,
} from '@mu/zui';
import pageHoc from '@utils/pageHoc';
import { track, EventTypes } from '@mu/madp-track';
import successImg from './img/success.png';
import './success.scss';


@track({ event: EventTypes.PO }, {
  pageId: 'qrcodeRepaySuccess',
  dispatchOnMount: true,
})
@pageHoc({ title: '还款结果' })
export default class QrcodeRepay extends Component {
  render() {
    return (
      <MUView className="qrcode-repay-success">
        <MUImage src={successImg} className="success-img" />
        <MUView>还款已提交</MUView>
      </MUView>
    );
  }
}
