import Madp from '@mu/madp';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import {
  filterQualCoupons, getFeeInteRightInfo, transformNewCouponAPIRes, filterWaiveCoupons
} from '@utils/repay-util';

// 查询用户是否有216券
export async function check216Coupon(debtorMobileNo, operatorMobileNo, awardDetailList) {
  // 过滤掉梯度减免资格券-提前还款场景（awardType === '216' && specialScene === 'SSA08'），目前仅用在线上还款
  const couponList = (awardDetailList || []).filter((item) => (!(item.awardType === '216' && item.subUseSceneCode === 'SSA08'))) || [];
  // 无216券，尝试调用三方派券接口
  if (!couponList || couponList.length === 0) {
    await Dispatch.repayment.take216Coupon({ debtorMobileNo, operatorMobileNo });
    return true;
  }
  return false;
}

// 获取博弈减免券
export async function getFeeReduceCoupon(awardDetailList, advanceBillList) {
  if (advanceBillList.length === 0) return [];
  let { usableQualConponList: couponList = [] } = await filterQualCoupons(awardDetailList, advanceBillList) || {};
  couponList = couponList.filter((item) => (item.awardType === '216' && item.subUseSceneCode === 'SSA02'));
  return couponList;
}

// 获取梯度试算结果
export async function getRepayWaiveTrialTrans(feeReduceCouponList, advanceBillList, authInfo) {
  // 客群接口获取三个梯度
  const params = {
    ...authInfo,
    waiveCustGroup: feeReduceCouponList && feeReduceCouponList.length && feeReduceCouponList[0].waiveRatioTag,
    orderNos: advanceBillList.length ? advanceBillList.map((item) => item.orderNo) : [],
    canWaiveOrderNos: advanceBillList.length ? advanceBillList.filter((item) => item.canUseManualCoupon === 'Y').map((e) => e.orderNo) : [],
    discountGradientList: ((feeReduceCouponList || [])[0] || {}).discountGradientList || [],
  };
  const { actualRepayWaiveDetails, payoffAmt, payoffWaiveAmt } = await Dispatch.repayment.repayWaiveTrialTransSpecial({ ...params });

  return {
    payoffAmt,
    rulesList: actualRepayWaiveDetails || [],
    payoffWaiveAmt: payoffWaiveAmt || '0.00',
  };
}

// 博弈减免试算
export async function feeReduceTrialApi(amtInput, feeReduceCouponList, advanceBillList, rulesList, authInfo) {
  const repayAmt = Util.numToStr(amtInput); // 输入的还款金额
  let waiveAmt = '0';
  rulesList.some((item) => {
    if (Number(repayAmt) >= Number(item.repayAmt)) {
      waiveAmt = item.waiveAmt;
      return item;
    }
  });
  const param = {
    ...authInfo,
    waiveAmt,
    transTotalAmt: repayAmt,
    repayMode: 'AMT', // 后台说跟旧息费减免特权的 INTEFEE_WAIVE 区分开,
    // awardInfoList: feeReduceCouponList,
    feeInteRightList: [{
      ...getFeeInteRightInfo((feeReduceCouponList || [])[0] || {}),
    }],
    repayDetailList: advanceBillList.length ? advanceBillList.map((i) => ({
      orderNo: i.orderNo,
      debtorSplitDetailList: i.debtorSplitDetails,
      repayAmt: i.surplusPayTotalAmt,
      instCnt: i.installTotalCnt
    })) : [],
  };
  const {
    result, repayTrialDetailList = [], settleWaiveAmt, totalCanWaiveAmt, desc, totalCanPayAmt
  } = await Dispatch.repayment.repayTransTrialSpecial(param);
  if (result === 'SUC') {
    const newRepayTrialDetailList = repayTrialDetailList.map((i) => {
      const target = advanceBillList && advanceBillList.find((j) => j.orderNo === i.orderNo);
      return {
        ...i,
        instCnt: (target && target.installTotalCnt) || 0
      };
    });
    return {
      totalCanPayAmt,
      settleWaiveAmt, // 可减免金额
      totalWaiveAmt: totalCanWaiveAmt,
      feeReduceAmt: (Util.floatAdd(Number(settleWaiveAmt || 0), Number(totalCanWaiveAmt || 0))).toFixed(2),
      repayTrialDetailList: newRepayTrialDetailList,
    };
  } else {
    desc && Madp.showToast({
      title: desc,
      icon: 'none',
      duration: 2000
    });
    return {};
  }
}


// 优惠券列表
export async function getCouponList(awardDetailList, advanceBillList, repayTotalAmt) {
  if (advanceBillList.length === 0) return {};
  const formatAwardDetailList = awardDetailList.map((item) => transformNewCouponAPIRes(item));
  const couponObject = await filterWaiveCoupons(formatAwardDetailList, {
    repayType: 'prepay',
    repayDetailList: advanceBillList,
    repayTotalAmt: Number(repayTotalAmt || 0).toFixed(2)
  });
  return couponObject;
}

// 修改金额、优惠券试算
export async function repayTransTrialApi(amtInput, selectedCoupon, advanceBillList, authInfo) {
  const repayAmt = Util.numToStr(amtInput); // 输入的还款金额
  const param = {
    ...authInfo,
    transTotalAmt: repayAmt,
    repayMode: advanceBillList.length === 0 ? 'AMT' : 'ADVANCE',
    feeInteRightList: selectedCoupon && selectedCoupon.awardNo
      ? [{ ...getFeeInteRightInfo(selectedCoupon) }] : [],
    repayDetailList: advanceBillList.length ? advanceBillList.map((i) => ({
      orderNo: i.orderNo,
      debtorSplitDetailList: i.debtorSplitDetails,
      repayAmt: i.surplusPayTotalAmt,
      instCnt: i.installTotalCnt
    })) : [],
  };
  const {
    result, repayTrialDetailList = [], settleWaiveAmt, totalCanWaiveAmt, totalCanPayAmt, shouldRepayAmt, desc, courtCostAmt, negotiateRepayTrialDetail
  } = await Dispatch.repayment.repayTransTrialSpecial(param);
  if (result === 'SUC') {
    const newRepayTrialDetailList = (repayTrialDetailList || []).map((i) => {
      const target = advanceBillList && advanceBillList.find((j) => j.orderNo === i.orderNo);
      return {
        ...i,
        instCnt: (target && target.installTotalCnt) || 0
      };
    });
    return {
      shouldRepayAmt, // 应还金额
      totalCanPayAmt, // 实还金额
      settleWaiveAmt, // 超限减免金额
      totalWaiveAmt: totalCanWaiveAmt, // 优惠券减免金额
      courtCostAmt, // 法诉费金额
      repayTrialDetailList: newRepayTrialDetailList,
      negotiateRepayTrialDetail, // 协商还试算详情
    };
  } else {
    desc && Madp.showToast({
      title: desc,
      icon: 'none',
      duration: 2000
    });
    return {};
  }
}

// 退出登录
export async function loginOut(param) {
  await Dispatch.repayment.loginOut(param);
}