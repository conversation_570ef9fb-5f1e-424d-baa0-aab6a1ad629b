/* eslint-disable react/sort-comp */
/* eslint-disable max-len */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUText, MUIcon, MUInput, MUImage, MUModal
} from '@mu/zui';
import { CouponSelector } from '@mu/coupon-selector';
import Statistic from '@components/statistic/index';
import RepayBubble from '@components/repay-bubble/index';
import WaiveDetailInfo from './components/waiveDetailInfo';
import {
  getFeeReduceCoupon, getRepayWaiveTrialTrans, feeReduceTrialApi,
  getCouponList, repayTransTrialApi, loginOut,
} from './utils/requests';
import { getDisplaySettleWaiveAmt } from '@utils/payMethod';
import { isAlipay, isWechat, Validator, debounce, Url } from '@mu/madp-utils';
import pageHoc from '@utils/pageHoc';
import { wechatAppId, wechatH5AppId, urlDomain } from '@utils/url_config';
import { track, EventTypes } from '@mu/madp-track';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import actions from '@api/actions';
import { platLogin } from '@utils/login';
import { getFeeInteRightInfo } from '@utils/repay-util';
import alipayImg from './img/zhifubao.png';
import wxpayImg from './img/weixin.png';
import mucfcImg from './img/mucfc.png';
import logoImg from './img/logo.png';

import './index.scss';

import {
  alih5pay, wxh5pay, wxjspay
} from './utils';

// import '@mu/coupon-selector/dist/styles/index.scss';
// import '@components/statistic/index.scss';

if (['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV)) {
  require('@mu/coupon-selector/dist/styles/index.scss');
  require('@components/statistic/index.scss');
  require('@components/overpay-drawer/index.scss');
}

const themeColor = Util.getThemeColor(channelConfig.theme);
const editImg = 'https://file.mucfc.com/zlh/3/0/202305/20230518201828520342.png';

@track({ event: EventTypes.PO }, {
  pageId: 'qrcodeRepay',
  dispatchOnMount: true,
})
@pageHoc({ title: '招联金融还款' })
export default class QrcodeRepay extends Component {
  constructor() {
    super();
    this.state = {
      token: '',
      userMobileNo: '', // 加密手机号 用于查券
      amount: 0, // 当前还款额
      payWay: isAlipay() ? 'alijs' : (isWechat() ? 'wxjs' : 'alih5'),
      name: '', // 用户姓名
      phone: '', // 用户手机号
      inputName: '', // 输入的姓名第一个字
      isShowNameCheckModal: false,
      isCheckedName: false,
      dueFlag: 'N', // 逾期标志,是否有逾期账单
      dueTagCust: 'N', // 逾期打标标记，101或C06标
      bizContent: '',
      couponObject: { // 优惠券组件返回的数据
        availableCouponDetailList: [],
        appExclusiveCouponDetailList: [],
        unavailableCouponDetailList: []
      },
      selectedCoupon: null,
      showCouponSelector: false,
      overpayInfo: {
        overPayAmtRepay: '0.00', // 溢缴款总金额
        availableAmount: '0.00', // 本次还款可用溢缴款
        remitTotalAmount: '0.00', // 在途转账
        preRepayAmt: '0.00', // 预还款
      },
      surplusTotalAmt: '0.00', // 总待还金额
      courtCostBalance: '0.00', // 入口查询的法诉费总金额
      settleWaiveAmt: '0.00', // 超限减免金额
      totalWaiveAmt: '0.00', // 优惠券减免金额
      courtCostAmt: '0.00', // 试算返回的法诉费金额
      rulesList: [], // 博弈梯度优惠列表
      payoffAmt: '0.00', // 博弈减免还清金额
      feeReduceAmt: '0.00', // 博弈减免可减免金额（优惠减免 + 超限减免）
      repayTrialDetailList: [], // 试算返回的账单明细
      isFeeReduce: false,
      displaySettleWaiveAmt: null,
      displayAmount: null,
      isSupportNegotiateRepay: false, // 是否有协商还在途任务
      negotiateRepayTaskBill: {}, // 协商还在途任务
      negotiateRepayTrialDetail: {}, // 协商还试算结果
      showTipModal: false, // 协商还情况下 输入金额大于结清金额提示
      repayBubbleParam: {}, // 协商还气泡文案
      overdueFlag: false, // 是否逾期（主要用作优惠券接口入参）
    };
    this.lastTrialTransTotalAmt = '0.00';
    this.advanceBillList = []; // 全部账单
    this.feeReduceCouponList = []; // 博弈减免优惠券
    this.handleAmtChangeDebounce = debounce((val) => {
      this.handleAmtChange(val);
    }, 100, {
      leading: false, // 指定调用在节流开始前
      trailing: true // 指定调用在节流结束后
    });
    this.awardDetailList = []; // 新优惠券列表
  }

  async componentDidMount() {
    if (isWechat()) {
      if (Madp.getChannel() !== '0WEC') {
       // window.location.href = `${window.location.origin}/0WEC/repayment/${window.location.hash}`;
        Madp.navigateTo({ url: `${window.location.origin}/0WEC/repayment/${window.location.hash}` });
      } else if (!Madp.getStorageSync('QrcodeRepay_WexinLogin', 'SESSION')) {
        await loginOut({ scene: 'logout' });
        Madp.setStorageSync('QrcodeRepay_WexinLogin', 1, 'SESSION');
        platLogin();
      } else {
        this.fetchData();
      }
    } else if (isAlipay()) {
      if (Madp.getChannel() !== '0ZFB') {
      //  window.location.href = `${window.location.origin}/0ZFB/repayment/${window.location.hash}`;
        Madp.navigateTo({ url: `${window.location.origin}/0ZFB/repayment/${window.location.hash}` });
      } else {
        this.fetchData();
      }
    } else {
      this.fetchData();
    }
  }

  async fetchData() {
    const token = Url.getParam('token') || '';
    this.setState({ token });
    const { ret, data, errMsg } = await actions.repayment.qrcodeRepayQuery({ token });
    if (ret !== '0') {
      Madp.showModal({
        content: errMsg,
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: themeColor,
      });
      return;
    }
    const {
      surplusPayDueTotalAmt, // 原始到期待还总金额（未减去超限减免金额）
      allPrepayPayDueTotalAmt, // 原始提前还款总金额
      custOverpayBalance, // 客户溢缴款余额
      collectDebtAmt, // 催款金额
      collectDebtType, // 催款类型
      customerName, // 客户姓名掩码
      mobileNumber, // 客户手机号掩码
      dueFlag, // 逾期借据标识
      dueTagCust, // 客户逾期标识
      bizContent, // 业务参数
      advanceBillList, // 提前结清借据列表
      encryptMobileNumber, // 客户手机号密串
      courtCostBalance, // 法诉费
      negotiateRepayTaskBill, // 协商还还款任务账单
      overdueDays, // 客户在途自营借据的最大逾期天数，若客户在途自营借据均未逾期（过还款日）则逾期天数为0
      remitTotalAmount, // 在途转账金额
      preRepayAmt, // 预还款金额
      overpayControlFlag, // 溢缴款管控
      awardDetailList,
    } = data;
    this.advanceBillList = advanceBillList;
    this.lastTrialTransTotalAmt = allPrepayPayDueTotalAmt;
    this.awardDetailList = awardDetailList || [];
    // 协商还默认填充当期待还金额，若当期待还为0则填充结清金额
    if (negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}') {
      const { currentShouldAmt, totalShouldAmt } = negotiateRepayTaskBill;
      this.setState({
        amount: Number(currentShouldAmt) > 0 ? currentShouldAmt : totalShouldAmt
      });
    } else {
      this.setState({
        amount: ({
          OVERDUE: surplusPayDueTotalAmt,
          ALL: allPrepayPayDueTotalAmt,
          APPOINT: Math.min(+collectDebtAmt, +allPrepayPayDueTotalAmt),
        })[collectDebtType],
      });
    }
    this.setState({
      name: customerName,
      phone: mobileNumber,
      bizContent,
      overpayInfo: {
        overPayAmtRepay: overpayControlFlag === 'Y' ? '0.00' : custOverpayBalance,
        availableAmount: '0.00',
        remitTotalAmount: overpayControlFlag === 'Y' ? '0.00' : remitTotalAmount,
        preRepayAmt: overpayControlFlag === 'Y' ? '0.00' : preRepayAmt,
      },
      dueFlag,
      dueTagCust,
      overdueFlag: Number(overdueDays) >= 1,
      surplusTotalAmt: allPrepayPayDueTotalAmt,
      userMobileNo: encryptMobileNumber,
      courtCostBalance,
      negotiateRepayTaskBill,
      isSupportNegotiateRepay: Boolean(negotiateRepayTaskBill && JSON.stringify(negotiateRepayTaskBill) !== '{}'),
    }, () => {
      this.waiveInit();
    });
  }

  // 优惠信息初始化
  async waiveInit() {
    const { token, amount, isSupportNegotiateRepay } = this.state;
    const couponList = await getFeeReduceCoupon(this.awardDetailList, this.advanceBillList);
    // 有博弈减免券且没有协商还任务账单，进入博弈场景
    if (couponList && couponList.length > 0 && !isSupportNegotiateRepay) {
      this.feeReduceCouponList = couponList;
      const { payoffAmt, rulesList } = await getRepayWaiveTrialTrans(
        couponList,
        this.advanceBillList,
        { covenientRepayToken: token }
      );

      this.setState({
        isFeeReduce: true,
        amount: payoffAmt,
        payoffAmt,
        rulesList,
      }, () => {
        this.feeReduceTrialApi(payoffAmt);
      });
    } else {
      await this.getCouponList();
      if (isSupportNegotiateRepay) {
        this.getRepayBubbleParam();
      }
      setTimeout(() => {
        this.repayTransTrialApi(amount);
      }, 100);
    }
  }

  submit = () => {
    Madp.showModal({
      content: '即将发起支付，请在付款时确认收款方为“招联金融”。\n还款前，请务必和借款人再次确认，避免是诈骗行为。',
      showCancel: false,
      confirmText: '我知道了',
      confirmColor: themeColor,
      success: this.doSubmit
    });
  }

  doSubmit = async () => {
    const { payWay } = this.state;
    const repayParams = this.handleRepayParams();
    if (isWechat()) {
      repayParams.repayWay = 'WEIXIN-JSAPI';
      repayParams.appId = wechatAppId;
    } else if (isAlipay()) {
      repayParams.repayWay = 'ALIPAY-H5';
    } else if (payWay === 'wxh5') {
      repayParams.repayWay = 'WEIXIN-H5';
      repayParams.appId = wechatH5AppId;
    } else {
      repayParams.repayWay = 'ALIPAY-H5';
    }
    Madp.showLoading();
    const res = await actions.repayment.qrcodeRepaySubmit(repayParams);
    switch (repayParams.repayWay) {
      case 'ALIPAY-H5':
        alih5pay(res);
        break;
      case 'WEIXIN-JSAPI':
        wxjspay(res, this.closeWXOrZFBPay);
        break;
      case 'WEIXIN-H5':
        wxh5pay(res);
        break;
      default:
    }
    Madp.hideLoading();
  }

  handleRepayParams() {
    const { amount, isFeeReduce, settleWaiveAmt, totalWaiveAmt, repayTrialDetailList, overpayInfo, bizContent, courtCostAmt, courtCostBalance } = this.state;
    const { availableAmount, remitTotalAmount, preRepayAmt } = overpayInfo;
    const payResultPageUrl = `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/qrcode-repay/success`;
    let repayParams = {
      payResultPageUrl,
      convenientRepayInfo: {
        token: this.$router.params.token,
        bizContent,
      },
      securityInfoList: [],
      transRefNo: Util.getTransRefNo(),
      currency: '156', // 代表人民币
      repayMode: isFeeReduce || this.advanceBillList.length === 0 ? 'AMT' : 'ADVANCE',
      isRandomReduce: 'N',
      settleWaiveAmt: parseFloat(settleWaiveAmt).toFixed(2) || '0.00',
      totalWaiveAmt: parseFloat(totalWaiveAmt).toFixed(2) || '0.00', // 新还款 减免总金额
      transOverpayAmt: Util.floatMinus(Util.floatMinus(Number(availableAmount || '0.00'), Number(preRepayAmt || '0.00')), Number(remitTotalAmount || '0.00')) > 0
        ? Util.floatMinus(Util.floatMinus(Number(availableAmount || '0.00'), Number(preRepayAmt || '0.00')), Number(remitTotalAmount || '0.00')).toFixed(2) : '0.00',
      transRemitAmt: Util.floatMinus(Number(availableAmount || '0.00'), Number(preRepayAmt || '0.00')) > 0 ? ((Util.floatMinus(Number(remitTotalAmount || '0.00'), Util.floatMinus(Number(availableAmount || '0.00'), Number(preRepayAmt || '0.00'))) > 0
        ? Util.floatMinus(Number(availableAmount || '0.00'), Number(preRepayAmt || '0.00')).toFixed(2) : parseFloat(remitTotalAmount).toFixed(2)) || '0.00') : '0.00',
      transPreRepayAmt: (Util.floatMinus(Number(preRepayAmt || '0.00'), Number(availableAmount || '0.00')) > 0
        ? parseFloat(availableAmount).toFixed(2) : parseFloat(preRepayAmt).toFixed(2)) || '0.00',
    };
    // 博弈减免场景
    if (isFeeReduce) {
      repayParams = {
        ...repayParams,
        transTotalAmt: Util.numToStr(Number(amount) + Number(totalWaiveAmt)),
        transCashAmt: parseFloat(this.getActualRepayAmt).toFixed(2),
        repayTotalAmt: parseFloat(this.getActualRepayAmt).toFixed(2),

        totalAwardAmount: parseFloat(totalWaiveAmt || 0).toFixed(2),
        // awardInfoList: this.feeReduceCouponList,
        feeInteRightList: [{
          ...getFeeInteRightInfo((this.feeReduceCouponList || [])[0] || {}),
        }],
        repayDetailList: (repayTrialDetailList || []).map((i) => ({ orderNo: i.orderNo, instCnt: i.instCnt }))
      };
    } else {
      // 非博弈减免场景
      const { selectedCoupon } = this.state;
      let transTotalAmt = parseFloat(amount).toFixed(2);
      // 超限叠加法诉费的场景下，若法诉费未结清，总待还金额需要减去法诉费，否则提交还款中台试算会与前端的试算不一致（原因是该场景下试算入参从逻辑上从应还金额变为实还金额）
      if (Number(settleWaiveAmt) > 0 && Number(courtCostBalance) > 0 && Number(courtCostAmt) < Number(courtCostBalance)) {
        transTotalAmt = this.lastTrialTransTotalAmt;
      }
      repayParams = {
        ...repayParams,
        transTotalAmt,
        transCashAmt: parseFloat(this.getActualRepayAmt).toFixed(2),
        repayDetailList: this.advanceBillList.map((i) => ({
          orderNo: i.orderNo,
          repayAmt: i.surplusPayTotalAmt,
          debtorSplitDetailList: i.debtorSplitDetails,
          instCnt: 0,
        })),
      };
      if (selectedCoupon && selectedCoupon.awardNo) {
        repayParams.feeInteRightList = [
          {
            ...getFeeInteRightInfo(selectedCoupon)
          }
        ];
      }
    }
    return repayParams;
  }

  // 微信或支付宝关单（包括预支付和非预支付模式）
  closeWXOrZFBPay = async (r, mode) => {
    await actions.repayment.orderClose({
      originalTransRefNo: r.transSeqno, // 还款流水号
      repayCloseMode: '01', // 关单模式，01-在线关单(预下单、非预下单模式关单)；02-批扣关单
    });
    Madp.showToast({
      title: mode === 'wechat' ? '微信取消' : '支付宝取消',
      icon: 'none',
      duration: 2000
    });
  }

  amountInputValidator = (val) => {
    if (!val || !Number(val)) {
      Util.toast('请输入大于0的金额');
      return false;
    } else if (Validator.isMoney(val)) {
      return true;
    } else {
      Util.toast('您输入的金额数不正确，小数点后最多只能有两位哦~');
      return false;
    }
  }

  async handleAmtChange(val) {
    if (!this.amountInputValidator(val)) return;
    this.setState({ editing: false });
    const { isFeeReduce } = this.state;
    if (isFeeReduce) {
      this.feeReduceTrialApi(val);
    } else {
      await this.getCouponList(val);
      setTimeout(() => {
        this.repayTransTrialApi(val);
      }, 100);
    }
  }

  // 博弈减免优惠试算
  async feeReduceTrialApi(amtInput) {
    const { rulesList, overpayInfo, token } = this.state;
    const {
      settleWaiveAmt,
      totalWaiveAmt,
      feeReduceAmt,
      totalCanPayAmt,
      repayTrialDetailList
    } = await feeReduceTrialApi(
      amtInput,
      this.feeReduceCouponList,
      this.advanceBillList,
      rulesList,
      { covenientRepayToken: token }
    );
    const { overPayAmtRepay, remitTotalAmount, preRepayAmt } = overpayInfo;
    overpayInfo.availableAmount = Math.min(Util.floatAdd(Number(overPayAmtRepay || '0.00'), Util.floatAdd(Number(remitTotalAmount || '0.00'), Number(preRepayAmt || '0.00'))), Number(totalCanPayAmt || '0.00')).toFixed(2);
    this.lastTrialTransTotalAmt = amtInput;
    this.setState({
      overpayInfo: {
        ...overpayInfo
      },
      amount: Number(totalCanPayAmt || 0) > 0 ? Math.max(Number(totalCanPayAmt || 0), Number(amtInput || 0)).toFixed(2) : '0.00',
      settleWaiveAmt: settleWaiveAmt || '0.00',
      totalWaiveAmt: totalWaiveAmt || '0.00',
      feeReduceAmt: feeReduceAmt || '0.00',
      repayTrialDetailList,
    });
  }

  // 普通还款试算
  async repayTransTrialApi(amtInput) {
    const { selectedCoupon, token, surplusTotalAmt, isSupportNegotiateRepay } = this.state;
    const trialAmt = selectedCoupon && selectedCoupon.awardAmtType === '4' ? surplusTotalAmt : amtInput;
    const {
      totalCanPayAmt,
      settleWaiveAmt,
      totalWaiveAmt,
      shouldRepayAmt,
      courtCostAmt,
      repayTrialDetailList,
      negotiateRepayTrialDetail, // 协商还试算详情
    } = await repayTransTrialApi(trialAmt, selectedCoupon, this.advanceBillList, { covenientRepayToken: token });
    // 已选券但试算优惠为0或试算失败，清空
    if (selectedCoupon && (!totalWaiveAmt || Number(totalWaiveAmt) === 0)) {
      setTimeout(() => {
        Util.toast('优惠券无法使用');
      }, 100);
      return this.clearCoupon(true, amtInput);
    }
    if (Number(trialAmt) < Number(shouldRepayAmt) && Number(courtCostAmt) === 0 && !isSupportNegotiateRepay) {
      const displaySettleWaiveAmt = getDisplaySettleWaiveAmt(trialAmt, totalWaiveAmt, totalCanPayAmt);
      this.setState({
        displaySettleWaiveAmt: Number(displaySettleWaiveAmt).toFixed(2),
        displayAmount: Number(trialAmt).toFixed(2),
      });
    } else {
      this.setState({ displaySettleWaiveAmt: null, displayAmount: null });
    }
    // 根据实还金额重算溢缴款的使用额度
    const { overpayInfo } = this.state;
    const { overPayAmtRepay, remitTotalAmount, preRepayAmt } = overpayInfo;
    overpayInfo.availableAmount = Math.min(Util.floatAdd(Number(overPayAmtRepay || 0), Util.floatAdd(Number(remitTotalAmount || 0), Number(preRepayAmt || 0))), Number(totalCanPayAmt || 0)).toFixed(2);
    this.lastTrialTransTotalAmt = trialAmt;
    if (isSupportNegotiateRepay) {
      this.setState({
        amount: Number(trialAmt || 0).toFixed(2),
        settleWaiveAmt: negotiateRepayTrialDetail && negotiateRepayTrialDetail.settleWaiveAmt || '0.00',
        totalWaiveAmt: totalWaiveAmt || '0.00',
        repayTrialDetailList,
        courtCostAmt,
        overpayInfo: { ...overpayInfo },
        negotiateRepayTrialDetail,
      }, () => {
        this.getRepayBubbleParamAfterTrail();
      });
    } else {
      this.setState({
        amount: Number(trialAmt || 0) > Number(surplusTotalAmt || 0) ? Number(trialAmt || 0).toFixed(2) : shouldRepayAmt || Number(trialAmt || 0).toFixed(2) || '0.00',
        settleWaiveAmt: settleWaiveAmt || '0.00',
        totalWaiveAmt: totalWaiveAmt || '0.00',
        repayTrialDetailList,
        courtCostAmt,
        overpayInfo: { ...overpayInfo },
      });
    }
  }

  clearCoupon(shouldTrial, trialAmt) {
    const { cleanCheck } = this.couponSelectorRef;
    cleanCheck && cleanCheck();
    this.setState({ selectedCoupon: null }, () => {
      if (shouldTrial) {
        this.repayTransTrialApi(trialAmt);
      }
    });
  }

  async check(surName) {
    const { bizContent } = this.state;
    const res = await actions.repayment.qrcodeRepayCheck({ surName, token: this.$router.params.token, bizContent });
    return res.ret === '0';
  }

  checkName = async () => {
    const { inputName, name } = this.state;
    if (!inputName || inputName.length > 1) {
      Util.toast('请输入姓名第一个字');
      this.focusNameInput();
      return;
    }
    if (await this.check(inputName)) {
      this.setState({
        isShowNameCheckModal: false,
        isCheckedName: true,
        name: inputName + name.substr(1)
      }, () => {
        Util.toast('姓名校验成功');
      });
    } else {
      this.setState({
        isShowNameCheckModal: false,
        inputName: '',
      });
      Madp.showModal({
        content: '姓名校验不通过，为避免资金损失，请核实后重新验证。如有疑问请联系招联工作人员。',
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: themeColor,
      });
    }
  }

  get isDisabledRepayBtn() {
    const {
      amount, isCheckedName, surplusTotalAmt, isFeeReduce, payoffAmt,
      isSupportNegotiateRepay, negotiateRepayTrialDetail,
    } = this.state;
    const { totalThresholdAmt } = negotiateRepayTrialDetail || {};
    return !isCheckedName
      || !(
        amount
        && +amount > 0
        && (isSupportNegotiateRepay
          ? Number(amount) <= Number(totalThresholdAmt)
          : ((isFeeReduce ? Number(amount) <= Number(payoffAmt) : Number(amount) <= Number(surplusTotalAmt))))
        && Number(this.getActualRepayAmt) > 0
      );
  }

  get getActualRepayAmt() {
    const { amount, settleWaiveAmt, totalWaiveAmt, overpayInfo, isFeeReduce, isSupportNegotiateRepay } = this.state;
    const { availableAmount } = overpayInfo;
    if (isSupportNegotiateRepay) {
      return Util.numToStr(Number(amount || '0.00') - Number(settleWaiveAmt || '0.00') - Number(availableAmount || '0.00'));
    }
    return isFeeReduce ? Util.floatMinus(amount || '0.00', availableAmount || '0.00')
      : Util.numToStr(Number(amount || '0.00') - Number(settleWaiveAmt || '0.00') - Number(totalWaiveAmt || '0.00') - Number(availableAmount || '0.00'));
  }

  focusNameInput = () => {
    if (process.env.TARO_ENV === 'h5') {
      const input = document.querySelector('.name-input input');
      if (input && input.focus) input.focus();
    }
  }

  fixAmount(amt) {
    this.handleAmtChangeDebounce(amt);
  }

  async getCouponList(val) {
    const { amount } = this.state;
    const couponObject = await getCouponList(this.awardDetailList, this.advanceBillList, val || amount);
    this.setState({
      couponObject: {
        ...couponObject,
        // TODO：过滤掉awardType: '216', specialScene: 'SSA08'的券
        availableCouponDetailList: (couponObject && couponObject.availableCouponDetailList || []).filter((item) => (!(item.awardType === '216' && item.subUseSceneCode === 'SSA08')))
      }
    }, () => {
      this.handleCouponListResult();
    });
  }

  // 获取优惠券列表后，根据当前已选券情况默认选中或清空已选券
  handleCouponListResult() {
    const { selectedCoupon, couponObject, isSupportNegotiateRepay } = this.state;
    const { availableCouponDetailList } = couponObject;
    const { chooseCoupon } = this.couponSelectorRef;
    let isCleared = false; // 本次是否清空优惠券
    if (selectedCoupon
      && (availableCouponDetailList && availableCouponDetailList
        .findIndex((i) => i.awardNo === selectedCoupon.awardNo) === -1)) {
      // 已选券未在可用券列表中，清空已选券
      this.clearCoupon();
      isCleared = true;
    }
    if ((!selectedCoupon || isCleared)
      && availableCouponDetailList && availableCouponDetailList.length > 0
      && !isSupportNegotiateRepay) {
      // 未选优惠券，且有可选券，且无协商还在途任务，默认选中第1张
      const defaultSelectCoupon = availableCouponDetailList[0];
      chooseCoupon(defaultSelectCoupon.awardNo);
      this.setState({
        selectedCoupon: defaultSelectCoupon
      });
    }
  }

  async onCouponSelected(coupon) {
    this.setState({ selectedCoupon: coupon }, () => {
      const { amount, displayAmount } = this.state;
      this.repayTransTrialApi(displayAmount || amount);
    });
  }

  renderFeeReduceAmountTip(payoffAmt) {
    const { amount, displayAmount, isSupportNegotiateRepay } = this.state;
    if (!amount || !payoffAmt || isSupportNegotiateRepay) return; // 协商还不进行金额提示
    const totalRepayAmt = displayAmount || amount; // （输入的）还款金额
    const fixAmount = payoffAmt; // 一键修改的金额值
    const diff = Util.floatMinus(totalRepayAmt, payoffAmt); // 输入总金额 与 结清金额 的差值
    if (diff > 0) {
      return (
        <MUView className="amountTip">
          已超出结清账单金额
          {diff}
          元，请修改还款金额。
          <MUView beaconId="OverFixAmount" className="fixNumBtn" style={`color: ${themeColor}`} onClick={() => this.fixAmount(fixAmount)}>
            一键修改
          </MUView>
        </MUView>
      );
    }
    if (diff < 0) {
      return (
        <MUView className="amountTip">
          还差
          {-diff}
          元可提前结清账单，请确认还款金额，也可
          <MUView beaconId="betweenFixAmount" className="fixNumBtn" style={`color: ${themeColor}`} onClick={() => this.fixAmount(fixAmount)}>
            一键修改
          </MUView>
        </MUView>
      );
    }
  }

  // 未修改金额时气泡展示（取自查询接口）
  getRepayBubbleParam = () => {
    let repayBubbleParam = {
      type: 'fillTop',
    };
    const { negotiateRepayTaskBill } = this.state;
    const {
      currentExpectWaiveAmt, // 当期计划减免金额
      currentTaskAmt, // 当期还款任务金额
      currentShouldAmt, // 当期结清金额
      payOffWaiveAmt, // 结清总减免金额
      payOffTotalAmt, // 剩余总待还金额
      totalShouldAmt, // 结清总金额
    } = negotiateRepayTaskBill || {};
    if ((currentExpectWaiveAmt && Number(currentExpectWaiveAmt) > 0)) { // 当期预计减免金额大于0，有息费减免情况
      if (currentShouldAmt && Number(currentShouldAmt) > 0) { // 当期结清金额大于0，此时未还款或已部分还款
        repayBubbleParam.contentArr = [
          { contentText: '已减', contentColor: '' },
          { contentText: `${currentExpectWaiveAmt}元`, contentColor: 'red' },
          { contentText: `息费，仅需${currentShouldAmt}元抵还${currentTaskAmt}元`, contentColor: '' },
        ];
      } else if (totalShouldAmt && Number(totalShouldAmt) > 0) { // 当期结清金额为0，结清总金额大于0
        repayBubbleParam.contentArr = [
          { contentText: '已减', contentColor: '' },
          { contentText: `${payOffWaiveAmt}元`, contentColor: 'red' },
          { contentText: `息费，仅需${totalShouldAmt}元抵还${payOffTotalAmt}元`, contentColor: '' },
        ];
      }
    }
    this.setState({
      repayBubbleParam
    });
    return;
  }

  // 修改金额后气泡展示（取自试算接口）
  getRepayBubbleParamAfterTrail() {
    let repayBubbleParam = {
      type: 'fillTop',
    };
    const { negotiateRepayTrialDetail, amount } = this.state;
    const {
      trailResult, // 试算结果, 0-未达门槛金额, 1-已达门槛金额
      taskAmt, // 任务金额
      taskThresholdAmt, // 任务门槛金额：任务金额*（1-减免比例）
      totalThresholdAmt, // 结清上限金额：总欠息费*（1-减免比例）
      maxWaiveAmt, // 最大可减免息费: 总欠息费*减免比例
      taskDiffAmt, // 达标相差金额, 试算结果为0时有值
      expectWaiveAmt, // 可减免息费, 试算结果为1时有值，基试算金额
      realPaidAmt = '0', // 实还金额
      surplusTotalAmt, // 结清金额,
      settleFlag, // 超限减免标识
    } = negotiateRepayTrialDetail || {};
    if ((maxWaiveAmt && Number(maxWaiveAmt) > 0)) { // 有息费减免情况
      if (trailResult === '0') { // 未达门槛金额，此时为修改金额小于任务门槛金额
        repayBubbleParam.contentArr = [
          { contentText: `再还${taskDiffAmt}元`, contentColor: 'red' },
          { contentText: `，享${totalThresholdAmt === taskThresholdAmt ? Number(taskThresholdAmt || 0).toFixed(2) : Util.floatMinus(taskThresholdAmt, realPaidAmt)}元抵还${totalThresholdAmt === taskThresholdAmt ? Number(taskAmt || 0).toFixed(2) : Util.floatMinus(taskAmt, realPaidAmt)}元`, contentColor: '' },
        ];
      } else if ((amount && Number(amount)) > (totalThresholdAmt && Number(totalThresholdAmt))) { // 修改金额大于结清上限金额
        this.setState({
          showTipModal: true
        });
      } else if (settleFlag === 'Y') { // 超限减免金额大于0时，结清金额保持不变，动态更新减免息费
        repayBubbleParam.contentArr = [
          { contentText: '已减', contentColor: '' },
          { contentText: `${Util.floatMinus(surplusTotalAmt, amount).toFixed(2)}元`, contentColor: 'red' },
          { contentText: `息费，仅需${amount}元抵还${surplusTotalAmt}元`, contentColor: '' },
        ];
      } else {
        repayBubbleParam.contentArr = [
          { contentText: '已减', contentColor: '' },
          { contentText: `${expectWaiveAmt}元`, contentColor: 'red' },
          { contentText: `息费，仅需${amount}元抵还${Util.floatAdd(amount, expectWaiveAmt).toFixed(2)}元`, contentColor: '' },
        ];
      }
    } else { // 无息费减免
      if (trailResult === '0') { // 未达门槛金额，此时为修改金额小于任务门槛金额
        repayBubbleParam.contentArr = [
          { contentText: `再还${taskDiffAmt}元`, contentColor: 'red' },
          { contentText: '，可达到本月协商还约定金额', contentColor: '' },
        ];
      } else if ((amount && Number(amount)) > (totalThresholdAmt && Number(totalThresholdAmt))) {
        this.setState({
          showTipModal: true
        });
      }
    }
    this.setState({
      repayBubbleParam
    });
    return;
  }

  get modalContent() {
    const { negotiateRepayTrialDetail } = this.state;
    const {
      totalThresholdAmt, // 结清上限金额：总欠息费*（1-减免比例）
      maxWaiveAmt, // 最大可减免息费: 总欠息费*减免比例
      expectWaiveAmt, // 可减免息费, 试算结果为1时有值，基试算金额
      surplusTotalAmt, // 全部待还总金额
    } = negotiateRepayTrialDetail || {};
    if ((maxWaiveAmt && Number(maxWaiveAmt) > 0)) { // 有息费减免
      return (
        <MUView className="tipModal-text">
          <MUText>{`可为您减免${expectWaiveAmt}元息费，仅需`}</MUText>
          <MUText className="tipModal-text__highlight">{totalThresholdAmt}</MUText>
          <MUText>{`元，即可抵还${surplusTotalAmt}元`}</MUText>
        </MUView>
      );
    } else { // 无息费减免
      return (
        <MUView className="tipModal-text">
          <MUText>仅输入</MUText>
          <MUText className="tipModal-text__highlight">{totalThresholdAmt}</MUText>
          <MUText>元，即可结清贷款</MUText>
        </MUView>
      );
    }
  }

  modalTipConfirm() {
    const { negotiateRepayTrialDetail: { totalThresholdAmt } = {} } = this.state;
    this.setState({
      showTipModal: false,
      amount: totalThresholdAmt, // 结清上限金额
    }, () => {
      this.repayTransTrialApi(totalThresholdAmt);
    });
  }

  render() {
    const {
      amount, editing, isShowNameCheckModal, isCheckedName, name, phone, payWay, inputName,
      overpayInfo, isFeeReduce, rulesList, payoffAmt, feeReduceAmt, showCouponSelector, couponObject,
      selectedCoupon, totalWaiveAmt, settleWaiveAmt, surplusTotalAmt, token,
      displayAmount, displaySettleWaiveAmt, isSupportNegotiateRepay, repayBubbleParam, showTipModal,
    } = this.state;
    const editable = !(selectedCoupon && selectedCoupon.awardAmtType === '4');
    return (
      <MUView className="pages-bg repayment-for-others-qrcode">
        <MUView className="topView">
          <MUImage src={logoImg} className="logo" />
          <MUView className="title">还款金额(元)</MUView>
          <MUView className="input-area">
            <MUInput
              editable={editable}
              className="amountInput"
              beaconId="amountInput"
              type="digit"
              onFocus={() => { this.setState({ editing: true }); }}
              onBlur={(val) => { this.handleAmtChangeDebounce(val); }}
              placeholder="点此输入还款金额"
              value={displayAmount || amount}
            />
            {editable && !editing && amount !== '' && <MUImage className="edit-img" src={editImg} style={{ transform: `translateX(${`${amount}`.length * 15}px)` }} />}
          </MUView>
          {repayBubbleParam && JSON.stringify(repayBubbleParam) !== '{}'
            && repayBubbleParam.contentArr && JSON.stringify(repayBubbleParam.contentArr) !== '[]' ? (
              <MUView className="qrcode-repay-bubble">
                <RepayBubble
                  repayBubbleParam={repayBubbleParam}
                />
              </MUView>
            ) : null}
          {this.renderFeeReduceAmountTip(isFeeReduce ? payoffAmt : surplusTotalAmt)}
        </MUView>
        <MUView className="split" />
        <WaiveDetailInfo
          isFeeReduce={isFeeReduce}
          rulesList={rulesList}
          payoffAmt={payoffAmt}
          feeReduceAmt={feeReduceAmt}
          overpayInfo={overpayInfo}
          couponObject={couponObject}
          selectedCoupon={selectedCoupon}
          totalWaiveAmt={totalWaiveAmt}
          settleWaiveAmt={displaySettleWaiveAmt || settleWaiveAmt}
          onClickCoupon={() => { this.setState({ showCouponSelector: true }); }}
          trialParam={{ amount, advanceBillList: this.advanceBillList, userInfo: { covenientRepayToken: token } }}
          isSupportNegotiateRepay={isSupportNegotiateRepay}
        />
        {
          name && phone && (
            <MUView className="topView">
              <MUView className="repayinfo">
                <MUView>借款人姓名</MUView>
                <MUView>
                  <MUText>{name}</MUText>
                  <MUText
                    className={`check_but ${isCheckedName ? 'disabled' : ''}`}
                    beaconId="checkFirstName"
                    onClick={() => {
                      if (!isCheckedName) {
                        this.setState({ isShowNameCheckModal: true }, this.focusNameInput);
                      }
                    }}
                  >
                    点击验证
                  </MUText>
                </MUView>
              </MUView>
              <MUView className="repayinfo-split" />
              <MUView className="repayinfo">
                <MUView>借款人手机号</MUView>
                <MUView>{phone}</MUView>
              </MUView>
            </MUView>
          )
        }
        <MUView className="tips">
          <MUView className="tipsTitle">
            <MUText>说明</MUText>
          </MUView>
          <MUView className="tip">1、本页面用于招联金融还款，请先验证借款人姓名再还款。</MUView>
          <MUView className="tip">2、如果发生退款，钱将退至借款人的账户中。</MUView>
          <MUView className="tip red">3、支付时请确认收款方为“招联金融”，谨防诈骗！</MUView>
        </MUView>
        <MUView className="pay-way-title">支付方式</MUView>
        <MUView className="topView">
          {
            isAlipay() && (
              <MUView className="pay-way-item">
                <MUView className="pay-way-item-icon">
                  <MUImage className="image" src={alipayImg} />
                </MUView>
                <MUView className="pay-way-item-content">
                  <MUView className="pay-way-item-content-title">支付宝支付</MUView>
                  <MUView className="pay-way-item-content-desc">支持支付宝账户，多家银行快捷支付</MUView>
                </MUView>
                <MUView className="pay-way-item-checkbox">
                  <MUIcon className="pay-way-item-checkbox-icon" value="checked" size={20} color={themeColor} />
                </MUView>
              </MUView>
            )
          }
          {
            isWechat() && (
              <MUView className="pay-way-item">
                <MUView className="pay-way-item-icon">
                  <MUImage className="image" src={wxpayImg} />
                </MUView>
                <MUView className="pay-way-item-content">
                  <MUView className="pay-way-item-content-title">微信支付</MUView>
                  <MUView className="pay-way-item-content-desc">推荐微信用户使用</MUView>
                </MUView>
                <MUView className="pay-way-item-checkbox">
                  <MUIcon className="pay-way-item-checkbox-icon" value="checked" size={20} color={themeColor} />
                </MUView>
              </MUView>
            )
          }
          {
            !isWechat() && !isAlipay() && (
              <MUView>
                <MUView
                  className="pay-way-item"
                  beaconId="payWeyAlih5"
                  onClick={() => {
                    if (payWay !== 'alih5') {
                      this.setState({ payWay: 'alih5' });
                    }
                  }}
                >
                  <MUView className="pay-way-item-icon">
                    <MUImage className="image" src={alipayImg} />
                  </MUView>
                  <MUView className="pay-way-item-content">
                    <MUView className="pay-way-item-content-title">支付宝支付</MUView>
                    <MUView className="pay-way-item-content-desc">支持支付宝账户，多家银行快捷支付</MUView>
                  </MUView>
                  <MUView className="pay-way-item-checkbox">
                    {
                      payWay === 'alih5'
                        ? <MUIcon className="pay-way-item-checkbox-icon" value="checked" size={20} color={themeColor} />
                        : <MUIcon className="pay-way-item-checkbox-icon" value="unchecked" size={20} color="#E5E5E5" />
                    }
                  </MUView>
                </MUView>
                <MUView className="pay-way-item-split" />
                <MUView
                  className="pay-way-item"
                  beaconId="payWeyWxh5"
                  onClick={() => {
                    if (payWay !== 'wxh5') {
                      this.setState({ payWay: 'wxh5' });
                    }
                  }}
                >
                  <MUView className="pay-way-item-icon">
                    <MUImage className="image" src={wxpayImg} />
                  </MUView>
                  <MUView className="pay-way-item-content">
                    <MUView className="pay-way-item-content-title">微信支付</MUView>
                    <MUView className="pay-way-item-content-desc">推荐微信用户使用</MUView>
                  </MUView>
                  <MUView className="pay-way-item-checkbox">
                    {
                      payWay === 'wxh5'
                        ? <MUIcon className="pay-way-item-checkbox-icon" value="checked" size={20} color={themeColor} />
                        : <MUIcon className="pay-way-item-checkbox-icon" value="unchecked" size={20} color="#E5E5E5" />
                    }
                  </MUView>
                </MUView>
              </MUView>
            )
          }
        </MUView>
        {/* 还款按钮 博弈减免使用原按钮 标准场景使用吸底栏展示实还金额 */}
        <Statistic
          disabled={this.isDisabledRepayBtn}
          amount={this.getActualRepayAmt}
          submitRepay={this.submit}
          beaconId="repaySum"
        />
        <MUImage src={mucfcImg} className="mucfc-info" />
        {
          isShowNameCheckModal && (
            <MUModal
              isOpened
              beaconId="nameCheckModal"
              content={(
                <MUView className="name-check-modal">
                  <MUView>
                    请补全借款人姓名第一个字
                  </MUView>
                  <MUView className="name-line">
                    <MUInput
                      className="name-input"
                      value={inputName}
                      beaconId="firstNameInput"
                      onChange={(v) => { this.setState({ inputName: v }); }}
                    />
                    <MUText>
                      {name.substr(1)}
                    </MUText>
                  </MUView>
                </MUView>
              )}
              title="确认借款人身份"
              isConfirmDisabled={!inputName}
              cancelText="取消"
              onCancel={() => { this.setState({ isShowNameCheckModal: false }); }}
              confirmText="确定"
              onConfirm={this.checkName}
            />
          )
        }
        {/* 优惠券选择组件 */}
        <CouponSelector
          ref={(ref) => { this.couponSelectorRef = ref; }}
          isOpened={showCouponSelector}
          coupons={couponObject}
          onCouponSelected={(data) => this.onCouponSelected(data)}
          onClose={() => this.setState({ showCouponSelector: false })}
          beaconId="QrcodeRepayCouponSelector"
        />
        {/* 输入金额大于结清金额时展示 */}
        <MUModal
          type="tip"
          beaconId="tipModal"
          isOpened={showTipModal}
          title="温馨提示"
          closeOnClickOverlay={false}
          content={this.modalContent}
          confirmText="好的"
          onConfirm={() => this.modalTipConfirm()}
        />
      </MUView>
    );
  }
}
