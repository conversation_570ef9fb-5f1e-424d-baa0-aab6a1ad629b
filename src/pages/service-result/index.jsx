/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUImage, MUButton, MURichText, MUSlogan
} from '@mu/zui';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import { miniProgramChannel, sloganUrl } from '@utils/constants';
import { Url, isMuapp } from '@mu/madp-utils';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import ChannelConfig from '@config/index';
import classNames from 'classnames';
import { getStore } from '@api/store';
import { jumpToChatPage } from '@mu/chat-entry-component';
import { urlDomain } from '@utils/url_config.js';
import Dispatch from '@api/actions';
import { injectState } from '@mu/leda';
import { QuestionPopup, EntryRate } from '@mu/survey';

import SERVICE_FAIL_IMG from '@components/assets/img/service-fail.png';
import INFO_IMAGE from '@components/assets/img/xyh_info.png';
import './index.scss';

const SUCCESS_IMAGE = 'https://file.mucfc.com/zlh/3/0/202305/20230518202321a82936.png';
const PENDING_IMAGE = 'https://file.mucfc.com/zlh/3/0/202305/2023051820221669f9b7.png';
const themeColor = Util.getThemeColor(ChannelConfig.theme);

const ledaPageId = '7a82e754-94a2-4393-a8ed-85737fbe9175';

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: ledaPageId
    }
  }
}, {
  pageId: 'ServiceResult',
  dispatchOnMount: true,
})
@pageHoc({ title: Url.getParam('serviceType') === 'extend' ? '延后还办理' : '贷后服务关怀申请' })
@injectState({
  pageId: ledaPageId,
  stateKeys: ['bannerWithTitle']
})
export default class ServiceResult extends Component {
  config = {
    navigationBarTitleText: this.serviceType === 'extend' ? '延后还办理' : '贷后服务关怀申请',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);

    this.state = {
      statusCofig: {},
      refKycComp: {},
    };
    this.serviceType = Url.getParam('serviceType') || '';
    this.quickQuestionRef = {};
    this.isExtendSuccess = false;
    this.bannerInfo = {};
  }

  async componentWillMount() {
    // status = 1为成功, status = 2 为审核中, status = 3 为失败页
    const status = Url.getParam('status');
    const subStatus = Url.getParam('subStatus');
    const errMsg = Url.getParam('errMsg');
    const isOverdueCust = getStore('isOverdueCust');
    let statusConfigData = {};

    if (this.serviceType === 'stopUrging') {
      if (status === '1') {
        statusConfigData = {
          statusImage: SUCCESS_IMAGE,
          statusText: '办理成功',
          statusTips: '',
          statusTipsSub: '',
          guideBtn: '',
        };
      } else if (status === '2') {
        statusConfigData = {
          statusImage: PENDING_IMAGE,
          statusText: '已提交审批',
          statusTips: `结果将于<span id=yellow style="color: ${themeColor};">3个工作日</span>内短信通知`,
          statusTipsSub: '',
          guideBtn: '',
        };
      } else if (status === '3') {
        // subStatus: 1为准入拒绝 2为风控拒绝 其余兜底拒绝
        if (subStatus === '1') {
          statusConfigData = {
            statusImage: SERVICE_FAIL_IMG,
            statusText: '暂不符合办理条件',
            textLeftShow: !errMsg,
            statusTips: errMsg || '很抱歉，您暂无可办理的借据，可能原因如下，详情请咨询招联客服。',
            statusTipsSub: errMsg ? '' : '1、无在途借据 <br/>2、您的借据期限已达上限<br/>3、除等额还款、本金按期均摊外，其他还款方式不支持办理<br/>4、您的借据已过最后一期应还款日<br/>5、您已办理过相关延期服务<br/>6、您的借据不支持还款计划变更',
            guideBtn: isOverdueCust && '仍有疑问，咨询在线客服>>',
          };
        } else if (subStatus === '2') {
          statusConfigData = {
            statusImage: SERVICE_FAIL_IMG,
            statusText: '办理失败',
            statusTips: '很抱歉，您暂不符合办理条件，请继续累计信用',
            statusTipsSub: '',
            guideBtn: isOverdueCust && '仍有疑问，咨询在线客服>>',
          };
        } else {
          statusConfigData = {
            statusImage: SERVICE_FAIL_IMG,
            statusText: '办理失败',
            statusTips: '抱歉，当前系统异常，请稍后重试',
            statusTipsSub: '',
          };
        }
      }
    } else if (this.serviceType === 'extend') {
      if (status === '1') {
        this.isCancel = Url.getParam('isCancel') === 'true';
        if (this.isCancel) {
          statusConfigData = {
            statusImage: INFO_IMAGE,
            statusText: '已取消',
            statusTips: '若您仍需办理，可再次进入延后还功能入口办理',
            primaryBtn: '查看还款计划'
          };
          dispatchTrackEvent({
            target: this,
            event: EventTypes.PO,
            beaconId: 'XyhCancelShow'
          });
        } else {
          const statusTips = await this.initSuccessStatusInfo();
          statusConfigData = {
            statusImage: SUCCESS_IMAGE,
            statusText: '办理成功',
            statusTips,
            primaryBtn: this.isOverdue || this.isDue ? '立即还款' : '查看还款计划',
            guideBtn: this.isOverdue || this.isDue ? '查看还款计划' : ''
          };
          this.isExtendSuccess = true;
          this.showRetainKyc = true;
          dispatchTrackEvent({
            target: this,
            event: EventTypes.PO,
            beaconId: this.isOverdue || this.isDue ? 'XyhSuccessSurplusShow' : 'XyhSuccessNoSurplusShow'
          });
        }
      } else if (status === '2') {
        // subStatus: 1为客户还需做人工kyc
        if (subStatus === '1') {
          statusConfigData = {
            statusImage: PENDING_IMAGE,
            statusText: '已提交审批',
            statusTips: '<span style="color: #FF0000;">3个工作日</span>内，会有电话联系您</br>请留意<span style="color: #FF0000;">接听95786的来电</span>',
            statusTipsSub: '',
            guideBtn: '',
          };
        } else if (subStatus === '2') {
          statusConfigData = {
            statusImage: PENDING_IMAGE,
            statusText: '还款处理中',
            statusTips: '请等待还款结果，若还款失败，需您重新操作还款',
            statusTipsSub: '',
            guideBtn: '',
          };
        } else {
          statusConfigData = {
            statusImage: PENDING_IMAGE,
            statusText: '已提交审批',
            statusTips: `结果将于<span id=yellow style="color: ${themeColor};">3个工作日</span>内短信通知`,
            statusTipsSub: '',
            guideBtn: '',
          };
        }
      } else if (status === '3') {
        // subStatus: 1为准入拒绝 2为风控拒绝 其余兜底拒绝
        if (subStatus === '1') {
          statusConfigData = {
            statusImage: SERVICE_FAIL_IMG,
            statusText: '暂不符合办理条件',
            textLeftShow: !errMsg,
            statusTips: errMsg || '抱歉，您当前不支持办理延后还，可能原因如下',
            statusTipsSub: errMsg ? '' : '1、无在途利率借据 <br/>2、借据办理延后还或再分期的次数已超过2次<br/>3、借据距离上次办理完延后还或再分期后，还未进行过第一期的还款<br/>4、借据不支持还款计划变更<br/>5、借款逾期天数已超过70天',
            primaryBtn: '联系我的客服'
          };
        } else if (subStatus === '2') {
          statusConfigData = {
            statusImage: SERVICE_FAIL_IMG,
            statusText: '办理失败',
            statusTips: '很抱歉，您暂不符合办理条件，请继续累计信用',
            statusTipsSub: '',
            primaryBtn: '联系我的客服'
          };
        } else {
          statusConfigData = {
            statusImage: SERVICE_FAIL_IMG,
            statusText: '办理失败',
            statusTips: '抱歉，当前系统异常，请稍后重试',
            statusTipsSub: '',
            primaryBtn: '联系我的客服'
          };
        }
        dispatchTrackEvent({
          target: this,
          event: EventTypes.PO,
          beaconId: 'XyhFailShow'
        });
      }
    }

    this.setState({
      statusCofig: statusConfigData
    });
    dispatchTrackEvent({ target: this, event: EventTypes.SO, beaconId: 'ServiceStatus', beaconContent: { cus: { serviceType: this.serviceType, status, subStatus, statusTips: statusConfigData.statusTips, statusTipsSub: statusConfigData.statusTipsSub } } });
  }

  beforeRouteLeave(from, to, next) {
    // 清理缓存，解决招行渠道跳转问题
    Madp.removeStorageSync('ServiceResultRedirectUrl', 'LOCAL');
    next(true);
  }

  // 返回按钮处理
  clickBackBtnHandler = () => {
    const status = Url.getParam('status');
    const isOverdueCust = getStore('isOverdueCust');
    // 拒绝页面增强引导联系客服:未逾期客户直接进入在线客服；逾期客户直接进入信用管家。
    if (this.serviceType === 'extend' && status === '3') {
      if (isOverdueCust) {
        jumpToChatPage({
          busiEntrance: 'YQYHHJJ',
          extraParam: {
            needLogin: 1
          }
        });
      } else {
        jumpToChatPage({
          busiEntrance: 'WYYHHJJ',
          extraParam: {
            needLogin: 1
          }
        });
      }
      return;
    }

    const redirectUrlFromUrl = Url.getParam('resultRedirectUrl') ? decodeURIComponent(Url.getParam('resultRedirectUrl')) : '';
    const serviceRedirectUrlFromLocal = Madp.getStorageSync('ServiceResultRedirectUrl', 'LOCAL') ? decodeURIComponent(Madp.getStorageSync('ServiceResultRedirectUrl', 'LOCAL')) : '';
    const redirectUrl = redirectUrlFromUrl || serviceRedirectUrlFromLocal || '';
    const isMiniProgramChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
    const ret = Util.checkFinishAction() || {};

    // 特别处理如果为招联app场景下，非还款场景进入的时候直接关闭webview
    if (isMuapp() && redirectUrl.indexOf('repayment/#/') === -1) {
      Madp.closeWebView();
      return;
    }

    // 存在redirectUrl时，跳转到redirectUrl
    if (redirectUrl && redirectUrl !== 'undefined' || ret.redirect) {
      if (isMiniProgramChannel) {
        Madp.miniProgram.reLaunch({
          url: redirectUrl || ret.redirect
        });
      } else {
        Madp.redirectTo({ url: redirectUrl || ret.redirect });
      }
    } else if (Madp.getChannel() === '3CMBAPP') {
      const cmbRedirectUrl = `${urlDomain}/${Madp.getChannel()}/ibfcmb/#/pages/index/index`;
      // Madp.redirectTo({ url: cmbRedirectUrl });
      Madp.reLaunch({ url: cmbRedirectUrl }).then().catch(() => {
        Madp.closeWebView();
      });
    } else {
      if (isMiniProgramChannel) {
        Madp.miniProgram.reLaunch({
          url: '/repayment/pages/index/index'
        });
      } else {
        const length = window && window.history && window.history.length;
        if (length === 1) {
          if (Madp.getChannel() === '0WAP') {
            window.close();
          } else {
            Madp.closeWebView().then().catch(() => {
              Madp.navigateBack();
            });
          }
        } else {
          Madp.navigateBack({ delta: length - 1 });
        }
      }
    }
  }

  // 成功跳还款页或失败跳客服
  subBtnHandler = () => {
    const status = Url.getParam('status');
    if (status === '1') { // 成功跳还款首页
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ToRepayPlan', beaconContent: { cus: { status } } });
      const isMiniProgramChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
      if (isMiniProgramChannel) {
        Madp.miniProgram.reLaunch({
          url: '/repayment/pages/index/index'
        });
      } else {
        Util.router.replace({
          path: '/pages/index/index',
        });
      }
    }
  }

  // 成功时展示下期还款时间及金额
  initSuccessStatusInfo = () => {
    return new Promise(async (resolve) => {
      let data = await Dispatch.repayment.getNearBills({}, { setNearAmount: true });
      const { repayBillList, isDueTagCust, dueRepayInfo, surplusTotalAmt, showStatus } = data;

      this.isOverdue = false; // 是否逾期
      this.isDue = false; // 是否到期
      if (isDueTagCust === 'Y') { // 肯定逾期
        this.surplusTotalAmt = dueRepayInfo && dueRepayInfo.duePayTotalAmt ? Number(dueRepayInfo.duePayTotalAmt).toFixed(2) : 0;
        this.isOverdue = true;
      } else {
        this.surplusTotalAmt = surplusTotalAmt ? Number(surplusTotalAmt).toFixed(2) : 0;
        if (this.surplusTotalAmt > 0) { // 进一步检查是否逾期
          for (let i = 0; i < repayBillList.length; i += 1) {
            if (repayBillList[i].surplusDays < 0) {
              this.isOverdue = true;
              break;
            }
          }
        }
      }

      if (this.surplusTotalAmt > 0) {
        if (this.isOverdue) { // 逾期
          resolve(`<span id=yellow style="color: #FE5A5F;">当前仍有${this.surplusTotalAmt}元逾期或到期，请尽快还款！<br/>诚信价值千金，失信寸步难行！</span>`);
        } else if (+showStatus === 1 || +showStatus === 2) { // 到期
          this.isDue = true;
          resolve(`<span id=yellow style="color: #FE5A5F;">当前仍有${this.surplusTotalAmt}元逾期或到期，请尽快还款！<br/>诚信价值千金，失信寸步难行！</span>`);
        } else { // 非逾期、非到期，展示下期还款时间及金额
          let { nearBillsTotalAmount, repayDate } = data || {};
          if (!(nearBillsTotalAmount && repayDate)) {
            data = await Dispatch.repayment.acquireFuturePlans({ queryType: '1' });
            if (data) {
              const { repayFutureBillDetailList } = data.data || {};
              if (repayFutureBillDetailList && repayFutureBillDetailList.length) {
                const futureBill = repayFutureBillDetailList[0] || {};
                nearBillsTotalAmount = futureBill.monthRepayAmt;
                repayDate = futureBill.repayDate;
                if (typeof repayDate === 'string') {
                  repayDate = repayDate.replace(/\./g, '');
                }
              }
            }
          }
          let month;
          let day;
          if (repayDate) {
            if (repayDate.length === 8) {
              month = repayDate && repayDate.substring(4, 6);
              day = repayDate && repayDate.substring(6, 8);
            } else {
              month = repayDate && repayDate.substring(5, 7);
              day = repayDate && repayDate.substring(8, 10);
            }
          }
          month = month && parseInt(month);
          day = day && parseInt(day);
          resolve(`${month && day ? `${month}月${day}日，` : ''}需还款<span id=yellow style="color: #FF6A00;">${nearBillsTotalAmount}</span>元，请按时还款`);
        }
      } else {
        resolve('');
      }
    });
  }

  // 触发问卷
  showQuestionPopup = () => {
    const { refKycComp } = this.state;
    if (refKycComp && refKycComp.showPopup && refKycComp.surveySerialNo) {
      refKycComp.showPopup({
        key: 'QuestionPopup', // 问卷组件唯一识别 key
        surveySerialNo: refKycComp.surveySerialNo, //  问卷id
        isShow: true, // 是否直接显示弹窗, 默认true
      });
    }
  }

  // 获取banner展位数据，lui中banner无法自定义点击事件，所以自己封装实现点击后触发kyc
  getBannerData = () => {
    const { bannerWithTitle } = this.state;
    const bannerUrl = bannerWithTitle && bannerWithTitle.dataObj && bannerWithTitle.dataObj.contentList && bannerWithTitle.dataObj.contentList[0] && bannerWithTitle.dataObj.contentList[0].imgUrl;
    const bannerTitle = bannerWithTitle && bannerWithTitle.dataObj && bannerWithTitle.dataObj.contentList && bannerWithTitle.dataObj.title;
    const bannerTitleColor = bannerWithTitle && bannerWithTitle.dataObj && bannerWithTitle.dataObj.contentList && bannerWithTitle.dataObj.titleColor;
    return {
      showBanner: !!bannerUrl,
      bannerUrl,
      bannerTitle,
      bannerTitleColor
    };
  }

  render() {
    const status = Url.getParam('status');
    const subStatus = Url.getParam('subStatus');
    const isVplus = getStore('isVplus') || false; // 是否为v+会员
    const { statusCofig } = this.state;
    const { statusImage, statusText, statusTips, statusTipsSub, primaryBtn = '返回', guideBtn, textLeftShow = false } = statusCofig || {};
    const { showBanner, bannerUrl, bannerTitle, bannerTitleColor } = this.getBannerData();

    return (
      <MUView className="pages-bg">
        <MUView className="service-result" style={{ backgroundColor: this.isExtendSuccess ? '' : '#FFFFFF' }} >
          <MUView className="service-result-top">
            {this.serviceType === 'extend' && <MUImage className={classNames('slogan', { 'slogan-plus': isVplus })} src={isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />}
            <MUView className="status-img-container"><MUImage className="status-img" src={statusImage} /></MUView>
            <MUView className="status-text">{statusText}</MUView>
            <MURichText className={classNames('status-tips', { 'status-tips_left': textLeftShow })} nodes={statusTips} />
            {statusTipsSub && (<MURichText className="status-tips-sub" nodes={statusTipsSub} />)}
            <MUButton
              beaconId="ServiceResultBackBtn"
              beaconContent={{ cus: { serviceType: this.serviceType, status, subStatus } }}
              className="result-btn"
              type="primary"
              onClick={() => {
                if (primaryBtn === '查看还款计划') {
                  dispatchTrackEvent({
                    target: this,
                    event: EventTypes.BC,
                    beaconId: this.isCancel ? 'XyhCancelPlanBtn' : 'XyhSuccessNoSurplusPlanBtn'
                  });
                  this.subBtnHandler();
                } else if (primaryBtn === '立即还款') {
                  dispatchTrackEvent({
                    target: this,
                    event: EventTypes.BC,
                    beaconId: 'XyhSuccessSurplusRepayBtn'
                  });
                  Madp.redirectTo({
                    url: '/pages/express-repay/index?_windowSecureFlag=1'
                  });
                } else { // primaryBtn === '返回'
                  this.clickBackBtnHandler();
                }
              }}
            >{primaryBtn}</MUButton>
            {guideBtn && (
              <MUView
                className="guide-btn"
                beaconId="ServiceGuideBtn"
                onClick={() => {
                  if (Url.getParam('serviceType') === 'extend' && !this.isCancel && (this.isOverdue || this.isDue)) {
                    dispatchTrackEvent({
                      target: this,
                      event: EventTypes.BC,
                      beaconId: 'XyhSuccessSurplusPlanBtn'
                    });
                  }
                  this.subBtnHandler();
                }}
              >
                {guideBtn}
              </MUView>
            )}
          </MUView>
          {/* 为你推荐banner */}
          {this.isExtendSuccess && showBanner && (<MUView className="result-banner">
            <MUView className="result-banner-content">
              {bannerTitle && <MUView className="result-banner-content-title" style={{ color: bannerTitleColor }}>{bannerTitle}</MUView>}
              <MUImage className="result-banner-content-img" beaconId="BannerClick" onClick={this.showQuestionPopup} src={bannerUrl} />
            </MUView>
          </MUView>)}
          {/* 星星评分 */}
          {this.isExtendSuccess && !showBanner && (<MUView className="repay-result-entryrate">
            <EntryRate
              key="EntryRate"
              componentKey="EntryRate"
              ledaPageId={ledaPageId} // 模版id, 必填
              pageId="repayment.ServiceResult"
            />
          </MUView>)}
          {/* 中断KYC */}
          {this.isExtendSuccess && (<MUView className="loan-result-page_quick-question">
            <QuestionPopup
              key="QuestionPopup"
              componentKey="QuestionPopup"
              ledaPageId={ledaPageId}
              pageId="repayment.ServiceResult"
              getRef={(params) => {
                this.setState({
                  refKycComp: params
                });
              }}
            />
          </MUView>)}
          <MUView className={`repay-result-container${this.isExtendSuccess ? '' : '-absolute'}`}>
            <MUSlogan className="result-slogan" onlyLogo />
          </MUView>
        </MUView>
      </MUView>
    );
  }
}
