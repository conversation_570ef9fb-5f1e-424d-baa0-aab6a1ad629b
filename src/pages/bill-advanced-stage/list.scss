.re-installment {
  .at-drawer__content {
    border-radius: 16px 16px 0 0;
  }

  &-amount {
    position: relative;;
    padding: 70px 30px 27px;
    background: #FFFFFF;

    .slogan {
      width: 206px;
      height: 44px;
      position: absolute;
      left: 0;
      top: 0;

      &-plus {
        width: 249px;
      }
    }

    .intro {
      position: absolute;
      right: 0;
      top: 0;
      width: 190px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26px;
      color: #3477FF;
      background: #E1EBFF;
      border-radius: 0 0 0 16px;

      &-icon {
        margin-left: 5px;
      }
    }

    .title {
      color: #333333;
      line-height: 28px;
      font-weight: 600;
      font-size: 28px;
    }

    .display-change {
      height: 60px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding-top: 30px;

      .display {
        height: 60px;
        font-weight: 500;
        color: #333333;
        line-height: 36px;
        font-size: 36px;

        &-amount {
          line-height: 60px;
          font-size: 60px;
          margin-left: 6px;
          font-weight: 700;
        }
      }

      .change {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 26px;
        color: #808080;
        letter-spacing: 0;
        text-align: right;
        line-height: 26px;

        &-icon {
          margin-left: 5px;
        }
      }
    }

    .tip {
      color: #3477FF;
      font-size: 22px;
      font-weight: 400;
      line-height: 32px;
      letter-spacing: 0;
      text-align: left;
      margin-top: 30px;
      padding: 8px 0 8px 20px;
      border-radius: 8px;
      background-color: #E1EBFF;
      display: flex;
      align-items: center;

      &-red {
        color: #CC1F15;
      }

      &-orange {
        color: #FF890E;
      }

      .mu-icon {
        padding-left: 8px;
      }
    }
  }

  &-block {
    margin: 20px;
    padding: 30px;
    border-radius: 16px;
    background: #fff;

    .block-item {
      line-height: 32px;
      padding-bottom: 68px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 32px;
      color: #333;
      font-weight: 500;

      &__left {
        display: flex;
        align-items: center;
        justify-content: center;

        &--title {
          height: 32px;
          line-height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #333333;
          text-align: left;
          font-size: 28px;
          font-weight: 600;

          &--icon {
            margin-left: 10px;
          }
        }

        &--edit {
          margin-left: 14px;
        }
      }

      &__right {
        display: flex;
        flex-direction: column;
        color: #333333;
        text-align: right;
        font-size: 28px;
        font-weight: 400;

        &--interest {
          &--line {
            color: #A6A6A6;
            text-decoration: line-through;
          }
        }

        &--desc {
          height: 32px;
          color: #a6a6a6;
          text-align: right;
          font-size: 22px;
          font-weight: 400;
          line-height: 32px;

          &--light {
            color: #FF8844;
          }
        }

        &--main {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          height: 32px;
          color: #333333;
          text-align: right;
          font-size: 28px;
          font-weight: 400;
          line-height: 32px;

          &--text {
            color: #FF8844;
          }

          &--icon {
            margin-left: 10px;
          }
        }

        &--sub {
          height: 32px;
          color: #a6a6a6;
          text-align: right;
          font-size: 22px;
          font-weight: 400;
          line-height: 32px;

          &--light {
            color: #FF8844;;
          }
        }
      }

      &__selected {
        display: flex;
        align-items: center;

        &__guide {
          margin-left: 10px;
          font-size: 0;

          .icon-accordion-down {
            transform: rotate(90deg);
          }
        }

        .select-place-holder {
          color: #cacaca;
        }
      }

      &__title {
        height: 32px;
        line-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333333;
        text-align: left;
        font-size: 28px;
        font-weight: 600;

        &--number {
          color: #3477FF;
        }

        &--tip {
          width: max-content;
          height: 32px;
          margin-left: 10px;
          padding: 0 15px;
          color: #FFFFFF;
          text-align: right;
          font-size: 20px;
          font-weight: 400;
          background: #FF8844;
          border-radius: 16px 16px 16px 0;
        }

        &--icon {
          width: 40px;
          height: 42px;
          margin-right: 10px;
        }
      }

      &__period {
        display: flex;
        align-items: center;
        justify-content: center;

        &--number {
          height: 32px;
          margin-right: 10px;
          color: #333333;
          text-align: right;
          font-size: 28px;
          font-weight: 400;
          line-height: 32px;
        }
      }
      
      &__desc {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        font-weight: 400;
        font-family: "PingFang SC";

        &--main {
          height: 32px;
          color: #333333;
          text-align: right;
          font-size: 28px;
          line-height: 32px;
        }

        &--sub {
          height: 32px;
          color: #a6a6a6;
          text-align: right;
          font-size: 22px;
          line-height: 32px;
        }
      }

      &__light {
        height: 32px;
        color: #FF8844;
        text-align: right;
        font-size: 28px;
        font-weight: 400;
        line-height: 32px;
      }

      &__plan {
        display: flex;
        align-items: center;

        &--main {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          color: #333333;
          text-align: right;
          font-size: 28px;
          font-weight: 400;
          line-height: 32px;

          &--text {
            color: #FF8844;
          }
        }

        &--sub {
          height: 32px;
          color: #a6a6a6;
          text-align: right;
          font-size: 22px;
          font-weight: 400;
          line-height: 32px;

          &--light {
            color: #FF8844;;
          }
        }

        &--icon {
          margin-left: 10px;
        }
      }
    }

    .nopadding {
      padding-bottom: 0px;
    }

    .padding30 {
      padding-bottom: 30px;
    }

    .padding50 {
      padding-bottom: 50px;
    }

    .padding70 {
      padding-bottom: 70px;
    }

    .novertical {
      align-items: flex-start;
    }

    .period-area {
      margin-bottom: 60px;
      overflow: hidden;

      &__scroll {
        display: flex;
      }

      &__item {
        flex: 0 0 auto;
        width: 180px;
        height: 72px;
        margin-right: 20px;
        border-radius: 8px;
        background: #f3f3f3;
        text-align: center;
        font-size: 32px;
        line-height: 72px;
        color: #333;
        font-weight: 400;
        position: relative;

        &--checked {
          width: 56px;
          height: 56px;
          position: absolute;
          right: 0;
          bottom: 0;
        }
      }

      &__item:last-child {
        margin-right: 0;
      }
    }

    .small-bottom {
      margin-bottom: 40px;
    }

    .amout-area {
      width: 655px;
      height: 182px;
      position: relative;

      &__before {
        width: max-content;
        height: 70px;
        position: absolute;
        left: 80px;
        top: 8px;
        display: flex;
        flex-direction: column;
        border-radius: 10px;
        border: 0 solid #979797;
        background: #EAF1FF;

        &--text {
          width: 60px;
          height: 26px;
          line-height: 26px;
          color: #FFFFFF;
          text-align: center;
          font-size: 16px;
          font-weight: 600;
          border-radius: 10px 0;
          background: #759ef0;
        }

        &--number {
          height: 26px;
          margin-top: 10px;
          padding: 0 20px;
          color: #3477FF;
          text-align: center;
          font-size: 18px;
          font-weight: 400;
          line-height: 26px;
        }
      }

      &__after {
        width: max-content;
        height: 70px;
        position: absolute;
        left: 357px;
        top: 48px;
        display: flex;
        flex-direction: column;
        border-radius: 10px;
        border: 0 solid #3477FF;
        background: #3477FF;

        &--text {
          width: 60px;
          height: 26px;
          line-height: 26px;
          color: #3477FF;
          text-align: center;
          font-size: 16px;
          font-weight: 600;
          border-radius: 10px 0;
          background: #EAF1FF;
        }

        &--number {
          // width: 125px;
          height: 26px;
          padding: 0 20px;
          margin-top: 10px;
          color: #FFFFFF;
          text-align: center;
          font-size: 18px;
          font-weight: 400;
          line-height: 26px;
        }
      }

      &__before1 {
        width: max-content;
        height: 70px;
        position: absolute;
        left: 80px;
        top: 8px;
        display: flex;
        flex-direction: column;
        border-radius: 10px;
        border: 0 solid #3477FF;
        background: #3477FF;

        &--text {
          width: 60px;
          height: 26px;
          line-height: 26px;
          color: #3477FF;
          text-align: center;
          font-size: 16px;
          font-weight: 600;
          border-radius: 10px 0;
          background: #EAF1FF;
        }

        &--number {
          // width: 125px;
          height: 26px;
          padding: 0 20px;
          margin-top: 10px;
          color: #FFFFFF;
          text-align: center;
          font-size: 18px;
          font-weight: 400;
          line-height: 26px;
        }
      }

      &__after1 {
        width: max-content;
        height: 70px;
        position: absolute;
        left: 357px;
        top: 48px;
        display: flex;
        flex-direction: column;
        border-radius: 10px;
        border: 0 solid #979797;
        background: #EAF1FF;

        &--text {
          width: 60px;
          height: 26px;
          line-height: 26px;
          color: #FFFFFF;
          text-align: center;
          font-size: 16px;
          font-weight: 600;
          border-radius: 10px 0;
          background: #759ef0;
        }

        &--number {
          height: 26px;
          margin-top: 10px;
          padding: 0 20px;
          color: #3477FF;
          text-align: center;
          font-size: 18px;
          font-weight: 400;
          line-height: 26px;
        }
      }

      &__bg {
        width: 655px;
        height: 182px;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }

    .slider-area {
      width: 655px;
      height: 70px;
      margin-top: 20px;

      .mu-slider {
        padding: 0;

        .mu-slider__marks {
          margin-top: 30px;
        }

        .mu-slider__marks__item__scale {
          margin-top: -30px;
        }
      }
    }
  }

  &-plan {
    margin: 20px;
    height: 700px;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 33px 30px 0;

    .plan-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: 1;

      &-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .main {
          font-weight: 500;
          font-size: 28px;
          color: #333333;
          line-height: 32px;
        }

        .sub {
          margin-left: 14px;
          padding: 8px 10px;
          background-color: rgba(52, 119, 255, 0.1);
          border-radius: 8px;
          font-size: 22px;
          color: #3477FF;
          line-height: 22px;
        }
      }

      &-right {
        display: flex;
        justify-content: center;
        align-items: center;

        .title {
          margin-right: 12px;
          font-size: 32px;
          color: #3477FF;
          line-height: 32px;
        }

        .arrow {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    .plan-desc {
      box-sizing: border-box;
      height: 75px;
      margin-top: 29px;
      color: #808080;
      font-size: 24px;
      line-height: 30px;
      text-align: left;

      &__orange {
        color: #FF890E;
      }
    }

    .plan-content {
      height: 550px;
      overflow-y: auto;
    }
  }

  &-plan-drawer {
    &-title {
      margin: 40px 0 20px 0;
      color: #333333;
      text-align: center;
      font-size: 36px;
      font-weight: 600;
      line-height: 36px;
    }

    &-closeImg {
      position: absolute;
      right: 30px;
      top: 15px;

      image {
        width: 32px;
        height: 32px;
      }

      img {
        width: 32px;
        height: 32px;
      }
    }

    &-bubble {
      height: 56px;
      border-radius: 28px;
      border: 1PX solid #e9e9e9;
      margin: 0 20px;
      display: inline-block;
    
      &-text {
        text-align: left;
        margin: 15px 30px 15px;
        font-size: 26px;
        font-weight: 400;
        line-height: 26px;

        &-highlight {
          color: #FF8844;
        }
      }
    }

    &-main {
      margin: 40px 0 0 20px;
    }
  }

  &-space {
    height: 252px;
    width: 100%;
  }

  &-bottom {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #fff;
    z-index: 9;

    .extend-contract-checker {
      height: 92px;
      padding-left: 20px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
    }

    .confirm-button {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      border-top: 1px solid #e5e5e5;
      box-sizing: border-box;
      padding: 30px 0;
    }
  }

  .small-padding {
    padding: 20px 30px;
  }

  .discounts {
    // height: 100px;
    // width: 710px;
    // box-sizing: border-box;
    background: linear-gradient(to bottom right, #FFFDFC, #FFF7F3);
    border: 1px solid #FFF7F3;
  }

  .icon-modal {
    &-content {
      margin-bottom: 130px;
      padding: 20px 40px;
      color: #808080;
      font-size: 28px;
      line-height: 42px;
      text-align: left;

      &__subTitle {
        margin: 20px 0;
        font-weight: 500;
        font-size: 30px;
        line-height: 44px;
        color: #333333;
      }

      &__bold {
        color: #333333;
        font-weight: 500;
      }

      &__orange {
        color: #FF890E;
        font-weight: 500;
      }
    }

    &-tel {
      margin-bottom: 120px;
      padding: 0 40px 30px;
      font-weight: 500;
      font-size: 26px;
      color: #A6A6A6;
      line-height: 36px;
    }

    &-slogan {
      width: 206px;
      height: 44px;
      margin: 0 auto;
      padding-bottom: 30px;
      display: flex;
      align-items: center;
      justify-content: center;

      &-plus {
        width: 249px;
      }
    }

    .repay-modal-btn {
      bottom: 105px;
    }
  }

  .repay-modal__blue {
    .mu-modal__container {
      padding-top: 0;
    }
  }

  .RepayModal-dialog-container {
    .mu-modal__container {
      padding-top: 0;
    }
  }

  .at-drawer__content {
    overflow: hidden;
  }

  .dialog-standard-modify {
    .mu-dialog__content {
      padding: 0;
    }
  }
}