/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
import Taro, { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUImage, MUButton, MUText
} from '@mu/zui';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import { injectState } from '@mu/leda';
import { sloganUrl, miniProgramChannel } from '@utils/constants';
import pageHoc from '@utils/pageHoc';
import Dispatch from '@api/actions';
import './confirm.scss';
import { Url } from '@mu/madp-utils';
import { getStore } from '@api/store';
import QUALIFIED_IMG from './img/qualified.png';
import FOOTER_IMG from './img/footer_slogan.png';

function isValidPrice(price) {
  const strippedPrice = typeof price === 'string' ? price.trim() : price;
  return strippedPrice !== '' && !Number.isNaN(+strippedPrice) && +strippedPrice >= 0;
}

@track({ event: EventTypes.PO }, {
  pageId: 'ReInstallmentConfirm',
  dispatchOnMount: true,
})
@pageHoc({ title: '再分期' })
@injectState({
})
export default class ReinstallmentConfirm extends Component {
  config = {
    navigationBarTitleText: '再分期',
    navigationStyle: process.env.TARO_ENV === 'weapp' ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);

    this.state = {
      showPage: false,
      duePayTotalAmt: '',
      needRepay: false,
    };
    this.isVplus = getStore('isVplus') || false;
    this.orderNoList = getStore('selectedBillList') || {};
    this.advancedStageInfo = getStore('advancedStageInfo') || {};
    this.applyNo = Url.getParam('applyNo') || this.advancedStageInfo.applyNo || '';
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
  }

  componentDidMount() {
    Promise.all([
      this.initReInstallCalInfo(),
    ]).then(([initReInstallCalInfoStates]) => {
      this.setState({
        ...initReInstallCalInfoStates,
      }, () => {
        this.initPageDate();
      });
    }).catch((ignore) => {});
  }

  initPageDate = () => {
    const { duePayTotalAmt } = this.state;
    if (isValidPrice(duePayTotalAmt) && Number(duePayTotalAmt) === 0) {
      this.setState({
        showPage: true,
      });
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'NoNeedRepay' });
    } else if (isValidPrice(duePayTotalAmt) && Number(duePayTotalAmt) > 0) {
      this.setState({
        needRepay: true,
        showPage: true,
      });
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'NeedRepay' });
    }
  }

  // 再分期试算
  initReInstallCalInfo() {
    return new Promise(async (resolve, reject) => {
      const { extendPackageInfo, extendInstallTotalCnt, firstPhasePeriods, expectRepayAmtPercent } = this.advancedStageInfo;
      const { ret, errCode, errMsg, data } = await Dispatch.repayment.queryReinstallRepayInfo({
        orderNoList: this.orderNoList,
        extendPackageList: [extendPackageInfo],
        firstPhasePeriods, // 第一阶段总期数（选择的前N期数）
        extendInstallTotalCnt, // 展期合并新借据总期数（选择的分期数）
        expectRepayAmtPercent, // 第一段期供比例（计算后的前N期月还款比例）
      }) || {};
      if (ret === '0') {
        const { extendCalInfoList = [] } = data || {};
        const selectExtendCalInfo = extendCalInfoList && extendCalInfoList[0] || {}; // 默认选中第一个
        const { duePayTotalAmt } = selectExtendCalInfo || {};
        if (isValidPrice(duePayTotalAmt)) {
          resolve({ duePayTotalAmt });
        } else {
          const urlParam = 'status=3&subStatus=3';
          Madp.redirectTo({
            url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}`
          });
          reject(new Error('duePayTotalAmt值不合法'));
        }
      } else {
        let urlParam = 'status=3';
        if (errCode === 'UMDP02724') { // UMDP02724 不符合办理条件；需前端特殊处理展示
          urlParam = `${urlParam}&subStatus=1`;
        } else if (errMsg) {
          urlParam = `${urlParam}&subStatus=1&errMsg=${errMsg}`;
        }
        Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
        return;
      }
    });
  }

  async submit(isCancel = false) {
    const { ret, errMsg, data } = await Dispatch.repayment.postLoanAddData({
      applyNo: this.applyNo,
      addInfoScene: '01',
      updateScene: isCancel ? '01' : '00'
    }, {});
    const { applyStatus } = data || {};

    let urlParam = '';
    if (ret === '0') {
      if (isCancel && applyStatus === '6') { // 取消办理且成功
        urlParam = '&status=1&isCancel=true';
      } else if (!isCancel && applyStatus === '9') { // 同意办理且成功
        urlParam = `&status=1&isCancel=false&applyNo=${this.applyNo}`;
      } else {
        urlParam = '&status=3&subStatus=3';
      }
    } else {
      urlParam = `&status=3&subStatus=1&errMsg=${errMsg}`;
    }
    Madp.redirectTo({
      url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}`
    });
  }

  goToRepay() {
    const url = `/pages/express-repay/index?_windowSecureFlag=1&billType=advanced-stage&applyNo=${this.applyNo}&fromConfirm=1`;
    if (this.miniChannelFlag) {
      Taro.redirectTo({ url });
    } else {
      Madp.redirectTo({ url });
    }
  }

  render() {
    const { showPage, needRepay, duePayTotalAmt } = this.state;

    return showPage ? (
      <MUView className="reinstallment-confirm">
        <MUView className="reinstallment-confirm-main">
          <MUView className="reinstallment-confirm-slogan">
            {this.isVplus
              ? (<MUImage className="reinstallment-confirm-slogan-vplus-img" src={sloganUrl.middleVplus} />)
              : (<MUImage className="reinstallment-confirm-slogan-img" src={sloganUrl.middle} />)}
          </MUView>
          <MUView className="reinstallment-confirm-qualified">
            <MUImage className="reinstallment-confirm-qualified-img" src={QUALIFIED_IMG} />
          </MUView>
          <MUView className="reinstallment-confirm-text">
            <MUView className="reinstallment-confirm-text-title">
              您已获得办理资格
            </MUView>
            <MUView className="reinstallment-confirm-text-subtitle">
              {needRepay ? (
                <MUText>
                  还<MUText className="reinstallment-confirm-text-subtitle-highlight">{duePayTotalAmt}元</MUText>后，即可完成办理
                </MUText>
              ) : '请确认是否同意办理'}
            </MUView>
          </MUView>
          {needRepay ? (
            <MUView className="reinstallment-confirm-btn">
              <MUButton beaconId="GoToRepay" type="primary" onClick={() => this.goToRepay()}>去还款</MUButton>
            </MUView>
          ) : (
            <MUView className="reinstallment-confirm-btns">
              <MUButton className="reinstallment-confirm-btns-1" beaconId="Cancel" type="secondary" onClick={() => this.submit(true)}>取消办理</MUButton>
              <MUButton className="reinstallment-confirm-btns-2" beaconId="Agree" type="primary" onClick={() => this.submit(false)}>同意办理</MUButton>
            </MUView>
          )}
        </MUView>
        <MUImage className="reinstallment-confirm-footer" src={FOOTER_IMG} />
      </MUView>
    ) : null;
  }
}
