.reinstallment-bills-detail {
	.bill-details {
		background-color: #F3F3F3;
		overflow-y: auto;
	
		&-content {
			&-area {
				background-color: #fff;
	
				.main {
					border-radius: 16px;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 30px;
	
					&-title {
						margin-top: 60px;
						font-size: 32px;
						color: #808080;
						text-align: center;
						line-height: 32px;
					}
	
					&-value {
						margin: 30px 0 24px;
						font-weight: 600;
						font-size: 100px;
						color: #333333;
						line-height: 80px;
					}
	
					&-desc {
						margin-top: 24px;
						margin-bottom: 60px;
						font-size: 28px;
						color: #808080;
						text-align: center;
						line-height: 28px;
					}
				}
	
				.line {
					height: 1px;
					width: 100%;
					background: #e5e5e5;
				}
	
				.sub {
					padding: 0 30px 40px;
					border-radius: 0 0 16px 16px;
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					align-items: center;
					font-size: 32px; 
					color: #333333;
					line-height: 45px;

	
					&-icon {
						margin-left: 10px;
						display: inline-block;

						.mu-icon {
							height: 40px;
						}
					}

					&-line {
            color: #A6A6A6;
            text-decoration: line-through;
          }
				}
			}
	
			&-plan {
				background-color: #F3F3F3;
				margin: 20px;
	
				.info-title {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 32px;
					color: #A6A6A6;
					line-height: 32px;
					padding: 20px 10px 20px;

					&-left {
						color: #1A1A1A;
						font-weight: 600;
					}
				}
	
				.info-list {
					background-color: #fff;
					border-radius: 16px;
				}
	
				.info-placeholder {
					margin-top: 10px;
					height: 1px;
				}
			}
		}
	}
	
	.details-modal {
		&-content {
			margin-bottom: 130px;
			padding: 20px 40px;
			color: #808080;
			font-size: 28px;
      line-height: 42px;
      text-align: left;

			&__subTitle {
				margin: 20px 0;
				font-weight: 500;
				font-size: 30px;
				line-height: 44px;
				color: #333333;
			}
		}
	
		&-slogan {
			width: 206px;
			height: 44px;
			margin: 0 auto;
			padding-bottom: 30px;
			display: flex;
			align-items: center;
			justify-content: center;
	
			&-plus {
				width: 249px;
			}
		}
	
		.repay-modal-btn {
			bottom: 105px;
		}

		.mu-modal__container {
			padding-top: 0px;
		}
	}
}
