/* eslint-disable react/sort-comp */
import Taro, { Component } from '@tarojs/taro';
import {
  MUView, MUImage, MUSlogan
} from '@mu/zui';
import Madp from '@mu/madp';
import { track, EventTypes } from '@mu/madp-track';
import { getStore, setStore } from '@api/store';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import pageHoc from '@utils/pageHoc';
import { miniProgramChannel } from '@utils/constants';
import { Url } from '@mu/madp-utils';
import './loading.scss';

import IMG_LOAD from './img/loading.png';

const POLLING_TIME = 10; // 页面倒计时时间

@track({ event: EventTypes.PO }, {
  pageId: 'ReInstallmentLoading',
  dispatchOnMount: true,
})
@pageHoc({ title: '再分期办理' })
export default class BillExtendLoading extends Component {
  config = {
    navigationBarTitleText: '再分期办理',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      showPage: false,
      time: POLLING_TIME,
    };
    this.billType = Url.getParam('billType') || 'advanced-stage';
    this.applyNo = Url.getParam('applyNo') || ''; // 案件申请号
    this.needManualKyc = Url.getParam('needManualKyc') === '1' || false; // 是否需要人工kyc
    this.timeCountDown = null; // 时间轮询定时器
    this.timeCasePolling = null; // 案件轮询定时器
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1; // 小程序渠道标识
  }

  async componentDidMount() {
    await this.init();
  }

  componentWillUnmount() {
    // 兜底清一下定时器
    if (this.timeCasePolling) {
      clearTimeout(this.timeCasePolling);
      this.timeCasePolling = null;
    }
  }

  init = async () => {
    this.show(() => {
      this.hide();
      clearTimeout(this.timeCasePolling);
      Util.router.replace({
        path: `/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=2${this.needManualKyc ? '&subStatus=1' : ''}&repaymentFlag=${this.repaymentFlag}`,
      });
    }); // 开始倒计时
    await this.queryCustCaseDetail();
  }

  show = (callback) => {
    const { time } = this.state;
    if (!this.timeCountDown && time && time > 0) {
      this.setState({
        showPage: true
      }, () => {
        this.timeCountDown = setInterval(() => {
          this.setState((preState) => ({
            time: preState.time - 1
          }), () => {
            // eslint-disable-next-line no-shadow
            const { time } = this.state;
            if (time < 1) {
              this.hide();
              if (typeof callback === 'function') {
                callback(true);
              }
            }
          });
        }, 1000);
      });
    }
  }

  hide = () => {
    this.setState({
      showPage: false
    }, () => {
      if (this.timeCountDown) {
        clearInterval(this.timeCountDown);
        this.timeCountDown = null;
        // 重置轮循弹窗状态
        this.setState({ time: POLLING_TIME });
      }
    });
  }

  // 再分期案件详情查询
  async queryCustCaseDetail() {
    const { applyNo } = getStore('advancedStageInfo');
    const { ret, data } = await Dispatch.repayment.queryCustCaseDetail({
      applyNo: applyNo || this.applyNo,
      ignoreLoading: true
    });
    const { applyStatus, contractApplyList } = data || {};
    if (ret === '0') {
      setStore({ contractApplyList });
      this.applyStatusNext(applyStatus);
    } else {
      this.setState({ showPage: false });
      clearTimeout(this.timeCasePolling);
      Util.router.replace({
        path: '/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=3',
      });
    }
  }

  // 根据案件结果进行处理
  applyStatusNext(status) {
    const { applyNo } = getStore('advancedStageInfo') || {};

    switch (status) {
      case '3': // 等待风控结果，轮询
        this.timeCasePolling = setTimeout(() => {
          this.queryCustCaseDetail();
        }, 2000);
        break;
      case '4': // 人工，需补充资料（正常是不会进入）
        this.hide();
        clearTimeout(this.timeCasePolling);
        if (this.miniChannelFlag) {
          Taro.redirectTo({
            url: `/pages/identity/information?_windowSecureFlag=1&serviceType=${this.billType}&applyNo=${applyNo || this.applyNo}&repaymentFlag=${this.repaymentFlag}`
          });
        } else {
          Util.router.replace({
            path: `/pages/identity/information?_windowSecureFlag=1&repaymentFlag=${this.repaymentFlag}`,
            query: {
              serviceType: this.billType,
              applyNo: applyNo || this.applyNo
            }
          });
        }
        break;
      case '6': // 风控拒绝，办理失败
        this.hide();
        clearTimeout(this.timeCasePolling);
        Util.router.replace({
          path: '/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=3&subStatus=2',
        });
        break;
      case '7': // 审核通过，去还款信息确认页面
        this.hide();
        clearTimeout(this.timeCasePolling);
        if (this.miniChannelFlag) {
          Taro.redirectTo({
            url: `/pages/express-repay/index?_windowSecureFlag=1&billType=${this.billType}`
          });
        } else {
          Util.router.replace({
            path: '/pages/express-repay/index?_windowSecureFlag=1',
            query: {
              billType: this.billType
            }
          });
        }
        break;
      case '8': // 提交完成，审核中
        this.hide();
        clearTimeout(this.timeCasePolling);
        Util.router.replace({
          path: '/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=2&subStatus=2',
        });
        break;
      case '9': // 提交完成，办理成功
        this.hide();
        clearTimeout(this.timeCasePolling);
        Util.router.replace({
          path: '/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=1',
        });
        break;
      case '10': // 提交完成，办理失败
        this.hide();
        clearTimeout(this.timeCasePolling);
        Util.router.replace({
          path: '/pages/bill-advanced-stage/result?_windowSecureFlag=1&status=3&subStatus=3',
        });
        break;
      default: // 异常情况
        this.hide();
        clearTimeout(this.timeCasePolling);
        Madp.showToast({
          icon: 'none',
          title: '系统繁忙，请退出后重试',
          duration: 1500,
        });
        break;
    }
  }

  render() {
    const { showPage, time } = this.state;
    return (
      showPage && (
        <MUView className="loading-page">
          <MUView className="loading-page_content">
            <MUImage className="img" src={IMG_LOAD} />
            <MUView className="count">{time}</MUView>
          </MUView>
          <MUView className="loading-page_title">申请审批中...</MUView>
          <MUView className="loading-page_desc">请耐心等待</MUView>
          <MUView className="loading-page_slogan">
            <MUSlogan onlyLogo />
          </MUView>
        </MUView>
      )
    );
  }
}
