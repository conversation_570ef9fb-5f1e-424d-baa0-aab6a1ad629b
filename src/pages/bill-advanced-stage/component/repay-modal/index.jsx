/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import {
  MUModal, MUView, MUText
} from '@mu/zui';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import './index.scss';

export default class RepayModal extends Component {
  static propTypes = {
    beaconId: PropTypes.string,
    isOpened: PropTypes.bool,
    title: PropTypes.string,
    subTitle: PropTypes.string,
    className: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
    onClose: PropTypes.func,
    children: PropTypes.node,
    btnText: PropTypes.string,
  }

  static defaultProps = {
    beaconId: 'CommonModal',
    isOpened: false,
    title: '',
    subTitle: '',
    className: '',
    onClose: () => { },
    children: undefined,
    btnText: '我知道了',
  }

  state = {
    _isOpened: false
  }

  modalInfo = {
    title: '',
    subTitle: '',
    className: '',
    src: '',
    onClose: () => { },
    btnText: '',
  }

  componentDidMount() {
    const {
      isOpened, title, subTitle, className, onClose, btnText,
    } = this.props;
    if (isOpened) {
      this.modalInfo = {
        title, subTitle, className, onClose, btnText
      };
      this.setState({ _isOpened: true });
    }
  }

  componentWillReceiveProps(nextProps) {
    const {
      title, subTitle, className, onClose, btnText,
    } = nextProps;
    const nextModalInfo = {
      title, subTitle, className, onClose, btnText,
    };
    if (!nextProps.isOpened) {
      this.setState({ _isOpened: false });
      this.resetModalInfo();
    } else if (JSON.stringify(this.modalInfo) !== JSON.stringify(nextModalInfo)) {
      this.modalInfo = nextModalInfo;
      this.setState({ _isOpened: true });
    }
  }

  resetModalInfo() {
    this.modalInfo = {
      title: '',
      subTitle: '',
      className: '',
      onClose: () => { },
      btnText: '',
    };
  }

  render() {
    const { _isOpened } = this.state;
    const { children, beaconId } = this.props;
    const { title, subTitle, className, onClose, btnText } = this.modalInfo;
    return _isOpened && (
      <MUModal
        beaconId={beaconId}
        isOpened={_isOpened}
        closeOnClickOverlay={false}
        className={classNames('repay-modal repay-modal__blue', className)}
      >
        {title && (
          <MUView className="repay-modal-header">
            <MUView className="repay-modal-header-holder" />
            <MUText className="repay-modal-header-title">{title}</MUText>
            {subTitle && (
              <MUView className="repay-modal-header-sub">{subTitle}</MUView>
            )}
          </MUView>
        )}
        {children}
        {
          btnText && (
            <MUView
              className="repay-modal-btn at-button--primary"
              beaconId={`${beaconId}.Close`}
              onClick={() => { onClose && onClose(); }}
            >
              {btnText}
            </MUView>
          )
        }
      </MUModal>
    );
  }
}
