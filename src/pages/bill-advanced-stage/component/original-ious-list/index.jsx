import Taro, { Component } from '@tarojs/taro';
import {
  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, MUIcon,
} from '@mu/zui';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { sloganUrl } from '@utils/constants';
import { debounce } from '@mu/madp-utils';
import RepayModal from '../repay-modal/index';
import ListItem from '../list-item/index';
import OriginalBillDetail from '../original-bill-detail/index';
import Util from '@utils/maxin-util';
import fold from '../../img/fold.png';
import './index.scss';

export default class OriginalIousList extends Component {
  static propTypes = {
    allBillList: PropTypes.array,
    orderInfoListCanExtend: PropTypes.array,
    orderInfoListCanNotExtend: PropTypes.array,
    selectedOrderInfoList: PropTypes.array,
    isVplus: PropTypes.bool,
    needRepayType: PropTypes.string,
    onSelect: PropTypes.func,
    selectAll: PropTypes.func,
    cancelAll: PropTypes.func,
    onConfirm: PropTypes.func,
  };

  static defaultProps = {
    allBillList: [],
    orderInfoListCanExtend: [],
    orderInfoListCanNotExtend: [],
    selectedOrderInfoList: [],
    isVplus: false,
    needRepayType: '',
    onSelect: () => { },
    selectAll: () => { },
    cancelAll: () => { },
    onConfirm: () => { },
  }

  constructor(props) {
    super(props);
    this.state = {
      isOpened: false,
      isUnOptIntroOpened: false, // 不可再分期借据说明弹窗是否打开
      isShowAll: false, // 不可再分期借据展示全部
      orderNo: '', // 点击的借据条目编码
    };
  }

  getRepayHeight = () => (`${Taro.pxTransform(1059 - 120)}`)

  show() {
    this.setState({
      isOpened: true
    }, () => {
      const { isOpened } = this.state || {};
      if (isOpened) {
        dispatchTrackEvent({
          target: this,
          beaconId: 'RepayPlanInfo',
          event: EventTypes.SO,
          beaconContent: {
            cus: {}
          }
        });
      }
    });
  }

  hide() {
    Util.handleTouchScroll(false, 'original-detail-item-frozen');
    this.setState({
      isOpened: false
    }, () => {
      const { isOpened } = this.state || {};
      if (!isOpened) {
        dispatchTrackEvent({
          target: this,
          beaconId: 'RepayPlanClose',
          event: EventTypes.BC,
          beaconContent: {
            cus: {}
          }
        });
      }
    });
  }

  // 不可再分期说明弹窗
  unOptIntroHandler = () => {
    this.setState({
      isUnOptIntroOpened: true
    });
    dispatchTrackEvent({
      target: this,
      beaconId: 'UnOptIntroOpened',
      event: EventTypes.BC,
      beaconContent: {
        cus: {}
      }
    });
  }

  // 借据是否选中
  onItemSelect = debounce((item) => {
    const { onSelect } = this.props;
    onSelect(item);
  }, 400, { leading: true, trailing: false })

  // 跳转借据详情
  onItemJump = (item) => {
    this.setState({
      orderNo: item && item.orderNo
    });
    this.OriginalBillDetail.show();
  }

  // 判断借据是否全部是招联的借据，如果是的，则不展示“来自XXXX”
  get isAllZL() {
    let isAllZL = true;
    const zlMerchantNo = ['10000', '10001'];
    const { allBillList } = this.props;
    allBillList.forEach((item) => {
      if (zlMerchantNo.indexOf(item.merchantNo) <= -1) {
        isAllZL = false;
      }
    });
    return isAllZL;
  }

  // 是否选中了所有可分期借据
  get getAllSelected() {
    const { selectedOrderInfoList, orderInfoListCanExtend } = this.props;
    return (selectedOrderInfoList && selectedOrderInfoList.length) === (orderInfoListCanExtend && orderInfoListCanExtend.length) && orderInfoListCanExtend.length > 0;
  }

  // 全选按钮点击处理
  allOrigSelectHandler = debounce(() => {
    const { selectAll, cancelAll } = this.props;
    if (this.getAllSelected) {
      // 当前全选了，再点取消所有选中
      cancelAll();
    } else {
      // 当前没全选，再点全选
      selectAll();
    }
  }, 400, { leading: true, trailing: false });

  // 点确认按钮，确认将进行分期的借据
  confirmLoanList = () => {
    const { onConfirm } = this.props;
    this.setState({
      isOpened: false
    });
    onConfirm && onConfirm();
  }

  // 展开按钮处理
  unfoldHandle = () => {
    const { isShowAll } = this.state;
    this.setState({
      isShowAll: !isShowAll
    });
  }

  render() {
    const { isOpened, isUnOptIntroOpened, isShowAll, orderNo } = this.state;
    const {
      orderInfoListCanExtend, orderInfoListCanNotExtend, selectedOrderInfoList, isVplus, needRepayType
    } = this.props;

    return (
      <MUView>
        <MUDrawer
          beaconId="OldLoanIousDrawer"
          show={isOpened}
          placement="bottom"
          height={Taro.pxTransform(1059)}
          onClose={() => { this.hide(); }}
        >
          {/* 原始借据列表 */}
          <MUView className="list">
            <MUView className="list-top">
              <MUView className="list-top-holder" />
              <MUView className="list-top-center">
                <MUText className="list-top-center-title">借据列表</MUText>
              </MUView>
              <MUView beaconId="OldLoanListDrawerClose" className="list-top-closeImg" onClick={() => { this.hide(); }} >
                <MUIcon className="list-top-closeImg-close" value="close2" size={18} color="#A6A6A6" />
              </MUView>
            </MUView>
            <MUView className="list-content">
              <MUView className="list-content-details" style={{ height: this.getRepayHeight() }}>
                {/* 可再分期列表 */}
                {
                  orderInfoListCanExtend && orderInfoListCanExtend.length > 0 ? (
                    <MUView>
                      <MUView className="optional-title">再分期借据({orderInfoListCanExtend.length}笔)</MUView>
                      {
                        orderInfoListCanExtend.map((orderItem, index) => (
                          <ListItem
                            scene="current-new"
                            item={orderItem || {}}
                            isAllZL={this.isAllZL}
                            checked={selectedOrderInfoList && selectedOrderInfoList.indexOf(orderItem) > -1}
                            disabled={orderItem.orderExtendBill === 'N'}
                            onItemCheck={(item) => this.onItemSelect(item)}
                            // jumpClick={(item) => this.onItemJump(item)}
                            isOptional
                          />
                        ))
                      }
                    </MUView>
                  ) : null
                }
                {/* 不可再分期列表 */}
                {
                  orderInfoListCanNotExtend && orderInfoListCanNotExtend.length > 0 ? (
                    <MUView>
                      <MUView className="not-optional">
                        <MUView className="not-optional-title">
                          <MUText>{`不可再分期借据(共${orderInfoListCanNotExtend.length}笔)`}</MUText>
                          <MUText className="not-optional-title-icon">
                            <MUIcon
                              beaconId="CanNotExtendDesc"
                              value="info"
                              color="#A6A6A6"
                              size={18}
                              onClick={() => {
                                this.unOptIntroHandler();
                              }}
                            />
                          </MUText>
                        </MUView>
                        {
                          orderInfoListCanNotExtend.length > 1 && (
                            <MUView className="not-optional-btn" onClick={() => this.unfoldHandle()}>
                              <MUText>{isShowAll ? '收起' : '展开'}</MUText>
                              <MUImage className={classNames('not-optional-btn-img', isShowAll ? 'img-reset' : 'img-rotate')} src={fold} />
                            </MUView>
                          )
                        }
                      </MUView>
                      {
                        orderInfoListCanNotExtend.slice(0, isShowAll ? undefined : 1).map((orderItem, index) => (
                          <ListItem
                            scene="current-new"
                            item={orderItem || {}}
                            isAllZL={this.isAllZL}
                            disabled
                            needRepayType={needRepayType}
                            // jumpClick={(item) => this.onItemJump(item)}
                          />
                        ))
                      }
                    </MUView>
                  ) : null
                }
              </MUView>
            </MUView>
            {/* <MUView className="list-bottom">
              <MUView className="list-bottom-icon" onClick={this.allOrigSelectHandler}>
                <MUIcon className="item-icon" value={this.getAllSelected ? 'checked' : 'unchecked'} size={20} color={themeColor || '#3477FF'} />
              </MUView>
              <MUView className="list-bottom-text">已选再分期借据：<MUText className="list-bottom-text__blue brand-text">{selectedOrderInfoList.length || '0'}</MUText>笔</MUView>
              <MUView className="list-bottom-btn brand-bg" beaconId="ConfirmChangeIousList" onClick={() => this.confirmLoanList()}>确定</MUView>
            </MUView> */}
          </MUView>
        </MUDrawer>
        {/* 原始借据详情 */}
        <OriginalBillDetail
          ref={(ref) => { this.OriginalBillDetail = ref; }}
          orderNo={orderNo}
        />
        <RepayModal
          className="list-modal"
          title="不可再分期借据说明"
          subTitle="可能原因如下，详情请咨询招联客服"
          isOpened={isUnOptIntroOpened}
          onClose={() => {
            Util.handleTouchScroll(true, 'original-detail-item-frozen');
            this.setState({ isUnOptIntroOpened: false });
          }}
        >
          <MUView className="list-modal-content">
            <MUView>1、您的借据期限已达上限</MUView>
            <MUView>2、除等额还款、本金按期均摊外，其他还款方式不支持办理</MUView>
            <MUView>3、您的借据已过最后一期应还款日</MUView>
            <MUView>4、您已办理过相关延期服务</MUView>
            <MUView>5、您的借据不支持还款计划变更</MUView>
          </MUView>
          <MUImage className={classNames('list-modal-slogan', { 'list-modal-slogan-plus': isVplus })} src={isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
        </RepayModal>
      </MUView>
    );
  }
}
