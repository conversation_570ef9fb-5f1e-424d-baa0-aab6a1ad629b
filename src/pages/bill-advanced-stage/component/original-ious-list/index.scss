.list {
  background-color: #F3F3F3;
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;

  &-top {
    height: 100px;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 8px;

    &-holder {
      width: 105px;
    }

    &-center {
      flex: 1;
      display: flex;
      font-size: 36px;
      color: #333333;
      justify-content: center;
      font-weight: bold;
    }

    &-closeImg {
      padding-right: 30px;
      margin: auto 0;
      height: 100px;
      width: 75px;
      position: relative;
      background-color: transparent;

      &-close {
        display: block;
        width: 32px;
        height: 32px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      image {
        width: 32px;
        height: 32px;
      }

      img {
        width: 32px;
        height: 32px;
      }
    }
  }

  &-content {
    display: block;
    height: 100% - 100;

    &-details {
      margin-left: 30px;
      // margin-right: 20px;
      display: block;
      overflow-x: hidden;
      overflow-y: auto;

      .optional-title {
        margin: 38px auto 22px 0px;
        height: 32px;
        color: #1a1a1a;
        font-size: 32px;
        font-weight: 600;
        line-height: 32px;
      }

      .not-optional {
        margin: 40px 30px 22px 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 32px;
        color: #1a1a1a;
        font-size: 32px;
        font-weight: 600;
        line-height: 32px;

        &-title {
          display: flex;
          justify-content: flex-start;
          align-items: center;

          &-icon {
            position: relative;
            top: 1px;
            left: 10px;
          }
        }

        &-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 5px;
          height: 28px;
          color: #a6a6a6;
          font-size: 28px;
          font-weight: 400;
          line-height: 28px;

          &-img {
            margin-left: 12px;
            width: 26px;
            height: 15px;
          }

          image {
            width: 26px;
            height: 15px;
          }

          img {
            width: 26px;
            height: 15px;
          }

          .img-rotate {
            transform: rotate(180deg);
            transition: transform .5s;
          }

          .img-reset {
            transform: rotate(0deg);
            transition: transform .5s;
          }
        }
      }
    }
    
    /* width */
    &-details::-webkit-scrollbar {
      width: 10px;
    }
  
    /* Track */
    &-details::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
  
    /* Handle */
    &-details::-webkit-scrollbar-thumb {
      background: #888;
    }
  }

  &-bottom {
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-icon {
      width: 90px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-text {
      flex: 1;
      color: #333333;
      font-size: 32px;
      line-height: 32px;
      text-align: left;

      &__blue {
        color: #3477FF;
        font-weight: 500;
      }
    }

    &-btn {
      margin-right: 30px;
      padding: 26px 62px;
      background: #3477FF;
      border-radius: 50px;
      font-weight: 600;
      font-size: 28px;
      color: #FFFFFF;
      text-align: center;
      line-height: 28px;
    }
  }

  &-modal {
    &-content {
      margin: 0 40px 168px;
      font-size: 28px;
      color: #808080;
      line-height: 42px;
    }

    &-slogan {
      width: 206px;
      height: 44px;
      margin: 0 auto;
      padding-bottom: 30px;
      display: flex;
      align-items: center;
      justify-content: center;

      &-plus {
        width: 249px;
      }
    }

    .repay-modal-btn {
      bottom: 105px;
    }
  }
}
