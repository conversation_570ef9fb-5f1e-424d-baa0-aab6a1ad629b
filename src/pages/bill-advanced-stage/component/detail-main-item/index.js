// eslint-disable-next-line no-unused-vars
import Taro, { Component } from '@tarojs/taro';
import { MUView, MUText } from '@mu/zui';
import PropTypes from 'prop-types';
import './index.scss';

export default class DetailMainItem extends Component {
    static propTypes = {
      title: PropTypes.string,
      content: PropTypes.string,
    }

    static defaultProps = {
      title: '',
      content: '',
    }

    render() {
      const { title, content } = this.props;
      return (
        <MUView className="detail-main-item">
          <MUText className="detail-main-item-title">{title}</MUText>
          <MUText className="detail-main-item-content">{content}</MUText>
        </MUView>
      );
    }
}
