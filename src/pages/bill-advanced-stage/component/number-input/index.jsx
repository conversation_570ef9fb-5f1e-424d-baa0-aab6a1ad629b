import { Component } from '@tarojs/taro';
import {
  MUText, MUView, MUIcon
} from '@mu/zui';
import PropTypes from 'prop-types';

import './index.scss';

export default class RepayPlan extends Component {
  static propTypes = {
    title: PropTypes.string,
    value: PropTypes.string,
    unit: PropTypes.string,
    max: PropTypes.string,
    min: PropTypes.string,
    tip: PropTypes.string,
    condition: PropTypes.string,
    onSubtraction: PropTypes.func,
    onAddition: PropTypes.func,
  };

  static defaultProps = {
    title: '',
    value: '',
    unit: '',
    max: '',
    min: '',
    tip: '',
    condition: '',
    onSubtraction: () => { },
    onAddition: () => { },
  }

  constructor(props) {
    super(props);
    this.state = {

    };
  }

  onSubtraction = () => {
    const { onSubtraction } = this.props;
    onSubtraction && onSubtraction();
  }

  onAddition = () => {
    const { onAddition } = this.props;
    onAddition && onAddition();
  }

  shake = () => {
    const shakeElem = document.querySelector('.number-input__container--condition');
    if (shakeElem) {
      shakeElem.classList.add('shake');
      setTimeout(() => { shakeElem.classList.remove('shake'); }, 800);
    }
  }

  render() {
    const {
      title,
      value,
      unit,
      tip,
      condition
    } = this.props;

    return (
      <MUView className="number-input">
        {title ? (<MUView className="number-input__title">{title}</MUView>) : null}
        <MUView className="number-input__container">
          {tip ? (<MUView className="number-input__container--tip">
            <MUText className="number-input__container--tip--text">{tip}</MUText>
            <MUIcon className="number-input__container--tip--arrow" value="caret-down" size="14" color="#FF8844" />
          </MUView>) : null}
          <MUView className="number-input__container--sub" beaconId="SubClick" onClick={this.onSubtraction}>
            <MUView className="number-input__container--sub--line" />
          </MUView>
          <MUView className="number-input__container--value">
            <MUText className="number-input__container--value--number">{value}</MUText>
            <MUText className="number-input__container--value-unit">{unit}</MUText>
          </MUView>
          <MUView className="number-input__container--add" beaconId="AddClick" onClick={this.onAddition}>
            <MUIcon className="number-input__container--add--icon" value="plus" size="14" color="#333333" />
          </MUView>
          {condition ? (<MUView className="number-input__container--condition">{condition}</MUView>) : null}
        </MUView>
      </MUView>
    );
  }
}
