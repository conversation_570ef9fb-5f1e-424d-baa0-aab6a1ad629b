.number-input {
  display: flex;

  &__title {
    width: fit-content;
    height: 48px;
    line-height: 48px;
    padding-right: 10px;
    color: #333333;
    text-align: left;
    font-size: 28px;
    font-weight: 400;
  }

  &__container {
    height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
    opacity: 1;
    border: 1px solid #cacaca;
    background: #f3f3f3;
    position: relative;

    &--tip {
      width: max-content;
      height: 32px;
      padding: 0 20px;
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      left: 50%;
      top: -45px;
      transform: translateX(-50%);
      border-radius: 22px;
      background-color: #FF8844;

      &--text {
        height: 22px;
        line-height: 22px;
        color: #FFFFFF;
        text-align: center;
        font-size: 20px;
        font-weight: 400;
      }

      &--arrow {
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 0;
      }
    }

    &--sub {
      width: 60px;
      height: 48px;
      display: flex;
      justify-content: center;
      align-items: center;

      &--line {
        width: 20px;
        height: 4px;
        margin-top: 5px;
        border-radius: 2px;
        opacity: 1;
        border: 0 solid #979797;
        background: #333333;
      }
    }

    &--value {
      // width: 92px;
      height: 28px;
      margin: 0 30px;
      color: #333333;
      text-align: center;
      font-size: 28px;
      font-weight: 600;
      line-height: 28px;
    }

    &--add {
      width: 60px;
      height: 48px;
      display: flex;
      justify-content: center;
      align-items: center;

      .mu-icon {
        font-weight: 600;
      }
    }

    &--condition {
      width: max-content;
      height: 33px;
      position: absolute;
      right: 0;
      bottom: -40px;
      color: #a6a6a6;
      text-align: right;
      font-size: 22px;
      font-weight: 400;
      line-height: 33px;
    }
  }

  .shake {
    animation: shake 800ms ease-in-out;
  }

  @keyframes shake { /* 垂直抖动，核心代码 */
      10%, 90% { transform: translate3d(0, -1px, 0); }
      20%, 80% { transform: translate3d(0, +2px, 0); }
      30%, 70% { transform: translate3d(0, -4px, 0); }
      40%, 60% { transform: translate3d(0, +4px, 0); }
      50% { transform: translate3d(0, -4px, 0); }
  }
}