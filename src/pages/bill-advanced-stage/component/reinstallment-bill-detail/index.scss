.bill-details {
  background-color: #F3F3F3;
  height: 100%;

  &-top {
    height: 100px;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 8px;

    &-holder {
      width: 105px;
    }

    &-center {
      flex: 1;
      display: flex;
      font-size: 36px;
      color: #333333;
      justify-content: center;
      font-weight: bold;
    }

    &-closeImg {
      padding-right: 30px;
      margin: auto 0;
      height: 100px;
      width: 75px;
      position: relative;
      background-color: transparent;

      &-close {
        display: block;
        width: 32px;
        height: 32px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      image {
        width: 32px;
        height: 32px;
      }

      img {
        width: 32px;
        height: 32px;
      }
    }
  }

  &-content {
    height: 100% - 100;
    margin: 20px;
    overflow-y: scroll;

    &-area {
      background-color: #fff;

      .main {
        height: 300px;
        border-radius: 16px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;

        &-title {
          margin-top: 56px;
          font-size: 32px;
          color: #808080;
          text-align: center;
          line-height: 32px;
        }

        &-value {
          margin-top: 30px;
          font-weight: 600;
          font-size: 80px;
          color: #333333;
          line-height: 80px;
        }

        &-desc {
          margin-top: 24px;
          margin-bottom: 50px;
          font-size: 28px;
          color: #808080;
          text-align: center;
          line-height: 28px;
        }
      }

      .line {
        height: 1px;
        width: 100%;
        background: #e5e5e5;
      }

      .sub {
        padding: 28px 30px 27px;
        border-radius: 0 0 16px 16px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        font-size: 32px;
        color: #333333;
        line-height: 45px;

        &-icon {
          margin-left: 10px;
        }
      }

      .second {
        padding-left: 0;
        margin-left: 30px;
        border-top: 1px solid #e5e5e5;
      }
    }

    &-plan {
      background-color: #F3F3F3;

      .info-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 28px;
        color: #A6A6A6;
        line-height: 28px;
        padding: 40px 10px 20px;
      }

      .info-list {
        background-color: #fff;
        border-radius: 16px;
      }

      .info-placeholder {
        margin-top: 10px;
        height: 1px;
      }
    }
  }
}

.details-modal {
  &-content {
    padding:  10px 40px 20px;
    font-size: 28px;
    color: #333333;
    line-height: 42px;
  }

  &-tel {
    margin-bottom: 128px;
    padding: 0 40px 30px;
    font-weight: 500;
    font-size: 26px;
    color: #A6A6A6;
    line-height: 36px;
  }

  &-slogan {
    width: 206px;
    height: 44px;
    margin: 0 auto;
    padding-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    &-plus {
      width: 249px;
    }
  }

  .repay-modal-btn {
    bottom: 105px;
  }
}