import Taro, { Component } from '@tarojs/taro';
import {
  <PERSON><PERSON><PERSON>t, M<PERSON><PERSON>iew, MU<PERSON>raw<PERSON>, MUIcon, MUImage
} from '@mu/zui';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import Util from '@utils/maxin-util';
import { sloganUrl } from '@utils/constants';
import DetailItem from '../detail-item/index';
import RepayModal from '../repay-modal/index';
import './index.scss';

export default class ReinstallmentBillingDetail extends Component {
  static propTypes = {
    reOrderDetail: PropTypes.object,
    extendMode: PropTypes.string,
    isVplus: PropTypes.bool,
  };

  static defaultProps = {
    reOrderDetail: {},
    extendMode: '',
    isVplus: false,
  }

  constructor(props) {
    super(props);
    this.state = {
      isOpened: false,
      isShowRepayIntro: false,
    };
  }

  getRepayHeight = () => (`${Taro.pxTransform(1000 - 130)}`)

  show() {
    this.setState({
      isOpened: true
    }, () => {
      const { isOpened } = this.state || {};
      if (isOpened) {
        dispatchTrackEvent({
          target: this,
          beaconId: 'ReinstallmentBillDetailShow',
          event: EventTypes.SO,
          beaconContent: {
            cus: {}
          }
        });
      }
    });
  }

  hide() {
    this.setState({
      isOpened: false
    }, () => {
      Util.handleTouchScroll(true, 'reinstallment-frozen');
      const { isOpened } = this.state || {};
      if (!isOpened) {
        dispatchTrackEvent({
          target: this,
          beaconId: 'ReinstallmentBillDetailClose',
          event: EventTypes.BC,
          beaconContent: {
            cus: {}
          }
        });
      }
    });
  }

  // 展示还款方式说明弹窗
  showRepayIntro = () => {
    this.setState({
      isShowRepayIntro: true
    });
  }

  render() {
    const { isOpened, isShowRepayIntro } = this.state;
    const { reOrderDetail, extendMode, isVplus } = this.props;

    return (
      <MUView>
        <MUDrawer
          beaconId="ReDetailDrawer"
          show={isOpened}
          placement="bottom"
          height={Taro.pxTransform(1000)}
          onClose={() => { this.hide(); }}
        >
          <MUView className="bill-details">
            <MUView className="bill-details-top">
              <MUView className="bill-details-top-holder" />
              <MUView className="bill-details-top-center">
                <MUText className="bill-details-top-center-title">账单详情</MUText>
              </MUView>
              <MUView beaconId="ReDetailDrawerClose" className="bill-details-top-closeImg" onClick={() => { this.hide(); }} >
                <MUIcon className="bill-details-top-closeImg-close" value="close2" size={18} color="#A6A6A6" />
              </MUView>
            </MUView>
            <MUView className="bill-details-content" style={{ height: this.getRepayHeight() }}>
              <MUView className="bill-details-content-area">
                <MUView className="main">
                  <MUView className="main-title">再分期待还金额(元)</MUView>
                  <MUView className="main-value">{reOrderDetail.extensionPayTotalAmt}</MUView>
                  <MUView className="main-desc">{`(本金${reOrderDetail.extensionPayTotalPrincipalAmt}元 + ${reOrderDetail.loanType === 'I' ? '利息' : '期费用'}${reOrderDetail.loanType === 'I' ? reOrderDetail.extensionPayTotalInteAmt : reOrderDetail.extensionPayTotalPeriodFee}元)`}</MUView>
                </MUView>
                <MUView className="line" />
                {
                  reOrderDetail.loanType === 'F' && (
                    <MUView className="sub">
                      <MUView>年利率(单利)</MUView>
                      <MUView>{Util.stringToPersent(reOrderDetail.extensionYearRate, 4)}</MUView>
                    </MUView>
                  )
                }
                {
                  extendMode === '01' && (
                    <MUView className="sub second">
                      <MUView>还款方式</MUView>
                      <MUView beaconId="ReDetailRepayDesc" onClick={() => { this.showRepayIntro(); }}>前期少还，到期结清<MUIcon className="sub-icon" value="info" size={18} color="#A6A6A6" /></MUView>
                    </MUView>
                  )
                }
              </MUView>
              <MUView className="bill-details-content-plan">
                <MUView className="info-title">
                  <MUView>再分期后还款信息</MUView>
                  <MUView>{`${reOrderDetail.extensionLoanRepayPlan && reOrderDetail.extensionLoanRepayPlan.length || '0'}期待还`}</MUView>
                </MUView>
                {
                  reOrderDetail.extensionLoanRepayPlan && reOrderDetail.extensionLoanRepayPlan.length > 0 ? (
                    <MUView className="info-list" >
                      {
                        reOrderDetail.extensionLoanRepayPlan.map((reDetailItem) => <DetailItem item={reDetailItem} loanType={reOrderDetail.loanType} />)
                      }
                    </MUView>
                  ) : null
                }
                <MUView className="info-placeholder" />
              </MUView>
            </MUView>
          </MUView>
        </MUDrawer>
        <RepayModal
          className="details-modal"
          title="还款方式说明"
          subTitle="    "
          isOpened={isShowRepayIntro}
          onClose={() => {
            Util.handleTouchScroll(true, 'reinstallment-frozen');
            this.setState({ isShowRepayIntro: false });
          }}
        >
          <MUView className="details-modal-content">
            <MUView>本次再分期办理，采用的还款方式为“前期少还，到期结清”，即前N-1期每月的还款金额较少，剩余的本金在最后一期结清，详见“再分期后还款计划”。 </MUView>
          </MUView>
          <MUView className="details-modal-tel">如有疑问，可联系客服95786咨询。</MUView>
          <MUImage className={classNames('details-modal-slogan', { 'details-modal-slogan-plus': isVplus })} src={isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
        </RepayModal>
      </MUView>
    );
  }
}
