import Taro, { Component } from '@tarojs/taro';
import {
  MUText, MUView, MUDrawer, MUIcon
} from '@mu/zui';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import ListItem from '../list-item/index';
import ReinstallmentBillDetail from '../reinstallment-bill-detail';
import './index.scss';

export default class ReinstallmentIousList extends Component {
  static propTypes = {
    orderInfoList: PropTypes.array,
    extendMode: PropTypes.string,
    isVplus: PropTypes.bool,
  };

  static defaultProps = {
    orderInfoList: [],
    extendMode: '',
    isVplus: false,
  }

  constructor(props) {
    super(props);
    this.state = {
      isOpened: false,
      reinstallmentItemInfo: {}
    };
  }

  // 判断借据是否全部是招联的借据，如果是的，则不展示“来自XXXX”
  get isAllZL() {
    let isAllZL = true;
    const zlMerchantNo = ['10000', '10001'];
    const { orderInfoList } = this.props;
    orderInfoList.forEach((item) => {
      if (zlMerchantNo.indexOf(item.merchantNo) <= -1) {
        isAllZL = false;
      }
    });
    return isAllZL;
  }

  getRepayHeight = () => (`${Taro.pxTransform(1000 - 130)}`)

  show() {
    this.setState({
      isOpened: true
    }, () => {
      const { isOpened } = this.state || {};
      if (isOpened) {
        dispatchTrackEvent({
          target: this,
          beaconId: 'ReinstallmentIousListShow',
          event: EventTypes.SO,
          beaconContent: {
            cus: {}
          }
        });
      }
    });
  }

  hide() {
    Util.handleTouchScroll(false, 'reinstallment-frozen');
    this.setState({
      isOpened: false
    }, () => {
      const { isOpened } = this.state || {};
      if (!isOpened) {
        dispatchTrackEvent({
          target: this,
          beaconId: 'ReinstallmentIousListClose',
          event: EventTypes.BC,
          beaconContent: {
            cus: {}
          }
        });
      }
    });
  }

  // 跳转再分期借据详情
  onItemJump = (item) => {
    this.setState({
      reinstallmentItemInfo: item
    });
    this.ReinstallmentBillDetail.show();
  }

  render() {
    const { isOpened, reinstallmentItemInfo } = this.state;
    const { orderInfoList, extendMode, isVplus } = this.props;

    return (
      <MUView>
        <MUDrawer
          beaconId="ReListDrawer"
          show={isOpened}
          placement="bottom"
          height={Taro.pxTransform(1000)}
          onClose={() => { this.hide(); }}
        >
          <MUView className="list">
            <MUView className="list-top">
              <MUView className="list-top-holder" />
              <MUView className="list-top-center">
                <MUText className="list-top-center-title">再分期借据</MUText>
              </MUView>
              <MUView beaconId="ReListDrawerClose" className="list-top-closeImg" onClick={() => { this.hide(); }} >
                <MUIcon className="list-top-closeImg-close" value="close2" size={18} color="#A6A6A6" />
              </MUView>
            </MUView>
            <MUView className="list-content">
              <MUView className="list-content-detail" style={{ height: this.getRepayHeight() }}>
                <MUView className="optional-title">再分期借据还款信息</MUView>
                {
                  orderInfoList.map((orderItem, index) => (
                    <ListItem
                      scene="future"
                      item={orderItem}
                      isAllZL={this.isAllZL}
                      jumpClick={(item) => this.onItemJump(item)}
                      extendMode={extendMode}
                    />
                  ))
                }
                <MUView className="optional-desc">不满足再分期办理条件的借据，需按原还款计划完成还款</MUView>
              </MUView>
            </MUView>
          </MUView>
        </MUDrawer>
        <ReinstallmentBillDetail
          ref={(ref) => { this.ReinstallmentBillDetail = ref; }}
          reOrderDetail={reinstallmentItemInfo}
          extendMode={extendMode}
          isVplus={isVplus}
        />
      </MUView>
    );
  }
}
