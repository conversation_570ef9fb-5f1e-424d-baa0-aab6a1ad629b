.list {
  background-color: #F3F3F3;
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;

  &-top {
    height: 100px;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 8px;

    &-holder {
      width: 105px;
    }

    &-center {
      flex: 1;
      display: flex;
      font-size: 36px;
      color: #333333;
      justify-content: center;
      font-weight: bold;
    }

    &-closeImg {
      padding-right: 30px;
      margin: auto 0;
      height: 100px;
      width: 75px;
      position: relative;
      background-color: transparent;

      &-close {
        display: block;
        width: 32px;
        height: 32px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      image {
        width: 32px;
        height: 32px;
      }

      img {
        width: 32px;
        height: 32px;
      }
    }
  }

  &-content {
    display: block;
    height: 100% - 100;

    mu-scroll-view {
      overflow: hidden;
    }

    &-detail {
      margin-left: 20px;
      margin-right: 20px;
      display: block;
      overflow-y: scroll;

      .optional-title {
        margin: 40px auto 20px 10px;
        font-size: 28px;
        color: #A6A6A6;
        line-height: 28px;
      }

      .optional-desc {
        margin: 40px auto 20px 10px;
        font-size: 26px;
        color: #A6A6A6;
        line-height: 26px;
      }
    }
  }
}