// eslint-disable-next-line no-unused-vars
import Taro, { useEffect } from '@tarojs/taro';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { MUView, MUModal } from '@mu/zui';
import './index.scss';

export default function CustomModal({ children, beaconId, pageId, isOpened }) {
  useEffect(() => {
    if (isOpened === true) {
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${pageId}.${beaconId}` });
    }
  }, [isOpened]);
  return (
    (process.env.TARO_ENV === 'h5' ? (
      <MUView style={{ visibility: isOpened ? 'visible' : 'hidden' }} className={`custom-modal ${isOpened && 'custom-modal-bg__active'}`}>
        <MUView className="custom-modal-overlay" />
        <MUView className="custom-modal-content-wrapper">
          {children}
        </MUView>
      </MUView>
    ) : <MUModal beaconId={beaconId} isOpened={isOpened} closeOnClickOverlay={false}>{children}</MUModal>)
  );
}