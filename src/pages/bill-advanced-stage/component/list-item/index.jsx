import { Component } from '@tarojs/taro';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import channelConfig from '@config/index';
import {
  MUView, MUIcon, MUText
} from '@mu/zui';
import { merchantNoList } from '@utils/constants';
import './index.scss';

const themeColor = channelConfig.theme;

const EXTEND_MODE_NUM = {
  OLD: '00',
  NEW: '01'
};

export default class ListItem extends Component {
  static propTypes = {
    item: PropTypes.object,
    scene: PropTypes.string, // 当前借据列表需要展示选择按钮，未来期借据不需要
    checked: PropTypes.bool, // 按钮是否选中
    onItemCheck: PropTypes.func, // 按钮的选择操作
    disabled: PropTypes.bool, // 当前按钮不可选
    isAllZL: PropTypes.bool, //  是否全部商户都是招联金融
    jumpClick: PropTypes.func, // 跳转借据详情页
    extendMode: PropTypes.string, // 再分期模式
    isOptional: PropTypes.bool, // 是否为再分期借据
    needRepayType: PropTypes.string, // 再分期办理还款条件
  }

  static defaultProps = {
    item: {},
    scene: 'current', // 当前分期列表(current)或再分期的分期列表（future)
    checked: false,
    onItemCheck: () => { },
    disabled: false,
    isAllZL: false,
    jumpClick: () => { },
    extendMode: '',
    isOptional: false,
    needRepayType: '',
  }

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  getItemOption(item) {
    const { isAllZL, scene, extendMode, isOptional, needRepayType } = this.props;
    let option = {};
    if (!Object.keys(item).length) return option;
    const showMerchant = !isAllZL && item.merchantName && merchantNoList.indexOf(item.merchantNo) <= -1;
    if (scene === 'current') {
      option = {
        isOverDue: Boolean(item.displayOverdueDays),
        title: '剩余待还',
        amount: `${item.surplusPayTotalAmt}元`,
        subTitle: '',
        remainAmtOrCnt: `剩余${item.surplusInstallCnt || (item.installTotalCnt - item.installCnt + 1)}/${item.installTotalCnt}期`,
        subDesc: `${Util.getDateCollection(item.loanDate).join('-')} ${item.businessType} ${item.installTotalAmt}元 ${showMerchant ? `来自${item.merchantName.slice(0, 5)}` : ''}`,
      };
    } else if (scene === 'current-new') { // 借据列表
      option = {
        isOverDue: Number(item.overdueDays) > 0,
        title: isOptional ? '可办理本金' : '待还本金',
        amount: isOptional ? `${item.extendedPrincipalAmt}元` : `${Util.floatAdd(item.origNormalBalance, item.origOverdueBalance).toFixed(2)}元`,
        subTitle: Number(item.duePayPrincipalAmt) > 0 ? '办理前需还款本金' : '',
        remainAmtOrCnt: Number(item.duePayPrincipalAmt) > 0 ? `${item.duePayPrincipalAmt}元` : '',
        subTitle2: isOptional ? '待还本金' : `${Number(item.duePayPrincipalAmt) > 0 ? '办理前需还款本金' : ''}`,
        remainAmtOrCnt2: isOptional ? `${Util.floatAdd(item.origNormalBalance, item.origOverdueBalance).toFixed(2)}元` : `${Number(item.duePayPrincipalAmt) > 0 ? `${item.duePayPrincipalAmt}元` : ''}`,
        subDesc: `${Util.getDateCollection(item.loanDate).join('-')} ${item.businessType === 'D00' ? '借款' : '消费'} ${item.installTotalAmt}元 ${showMerchant ? `来自${item.merchantName.slice(0, 5)}` : ''}`,
      };
      // 仅还可展期借据（needRepayType=1）时：不可展期借据不展示办理前需还款本金
      if (needRepayType === '1') {
        option.subTitle2 = '';
        option.remainAmtOrCnt2 = '';
      }
    } else if (scene === 'future-new') { // 待还账单
      let month;
      let day;
      let busiType;
      if (item.payDate && item.payDate.length === 8) {
        month = item.payDate.substring(4, 6);
        if (month[0] === '0') {
          month = month[1];
        }
        day = item.payDate.substring(6, 8);
      }
      if (item.isExtendPlan === 'E') {
        busiType = '再分期借据';
      } else if (item.businessType === 'D00') {
        busiType = '借款';
      } else {
        busiType = '消费';
      }
      option = {
        isOverDue: item.isOverdue === 'Y',
        title: `${month && day ? `${month}月${day}日` : ''}应还`,
        payDate: Number(item.extensionInstallTotalCnt) - Number(item.extensionPaidInstallCnt),
        amount: `${item.surplusPayTotalAmt}元`,
        subTitle: `剩余${Number(item.extensionInstallTotalCnt) - Number(item.extensionPaidInstallCnt)}/${item.extensionInstallTotalCnt}期`,
        // remainAmtOrCnt: extendMode === EXTEND_MODE_NUM.OLD ? `剩余待还${item.extensionPayTotalAmt}元` : `最后一期应还${item.extLastPlanPayTotalAmt}元`,
        remainAmtOrCnt: `第${item.installCnt}/${item.installTotalCnt}期`,
        subDesc: `${Util.getDateCollection(item.loanDate).join('-')} ${busiType}${item.installTotalAmt}元`,
        isNewLabel: item.isExtendPlan === 'E',
        isReinstallmentLabel: item.isExtendPlan === 'Y',
        isWaiveAllLabel: Number(item.stopInteFeeAmt) > 0
      };
    } else { // scene === 'future'
      option = {
        isOverDue: false, // 一般情况再分期后的借据都不会为逾期状态
        title: '每期应还',
        preCnts: Number(item.extensionInstallTotalCnt) - Number(item.extensionPaidInstallCnt),
        amount: `${item.expectRepayAmt}元`,
        subTitle: `剩余${Number(item.extensionInstallTotalCnt) - Number(item.extensionPaidInstallCnt)}/${item.extensionInstallTotalCnt}期`,
        remainAmtOrCnt: extendMode === EXTEND_MODE_NUM.OLD ? `剩余待还${item.extensionPayTotalAmt}元` : `最后一期应还${item.extLastPlanPayTotalAmt}元`,
        subDesc: `${Util.getDateCollection(item.loanDate).join('-')} ${item.businessType === 'D00' ? '借款' : '消费'} ${item.installTotalAmt}元 ${showMerchant ? `来自${item.merchantName.slice(0, 5)}` : ''}`,
      };
    }
    return option;
  }

  render() {
    const { item = {}, scene, checked, disabled, onItemCheck, jumpClick, extendMode, isOptional } = this.props;
    const itemOption = this.getItemOption(item);

    console.log('scene', scene, 'isOptional', isOptional, 'itemOption.remainAmtOrCnt', itemOption.remainAmtOrCnt, 'itemOption.remainAmtOrCnt2', itemOption.remainAmtOrCnt2)

    return (
      <MUView
        className={classNames(
          'detail-item',
          {
            'detail-item-current-new': scene === 'current-new',
            'detail-item-current-new-2-sub': scene === 'current-new' && isOptional && itemOption.remainAmtOrCnt && itemOption.remainAmtOrCnt2,
            'detail-item-current-new-1-sub': scene === 'current-new' && !itemOption.remainAmtOrCnt && itemOption.remainAmtOrCnt2,
            'detail-item-current-new-0-sub': scene === 'current-new' && !isOptional && !itemOption.remainAmtOrCnt2,
            'detail-item-future-new': scene === 'future-new',
          }
        )}
        onClick={scene === 'future-new' ? () => jumpClick(item) : () => {}}
      >
        <MUView className="detail-item-top">
          {scene === 'current' ? (
            <MUView beaconId="SelectClick" onClick={() => onItemCheck(item)}>
              {
                disabled ? (
                  <MUIcon className="item-icon" value="unchecked" size={18} color="rgba(52,119,255, 0.1)" />
                ) : (
                  <MUIcon className="item-icon" value={checked ? 'checked' : 'unchecked'} size={18} color={themeColor || '#3477FF'} />
                )
              }
            </MUView>
          ) : <MUView className="blank-placeholder" />}
          <MUView className={'item-detail'}>
            <MUView className="item-detail-left">
              <MUView className="item-detail-left-title">
                {(scene === 'current' || scene === 'future') && (
                  scene === 'current' || (scene === 'future' && (extendMode === EXTEND_MODE_NUM.OLD || Number(itemOption.preCnts) < 2)) ? itemOption.title : (
                    (
                      <MUText>
                        前
                        <MUText className="title-blue brand-text">{Number(itemOption.preCnts) - 1}</MUText>
                        期 {itemOption.title}
                      </MUText>
                    )
                  )
                )}
                {scene === 'current-new'
                  ? (
                    <MUText>
                      {itemOption.title}
                    </MUText>
                  ) : null}
                {scene === 'future-new'
                  ? (
                    <MUText>
                      {itemOption.title}
                    </MUText>
                  ) : null}
                {
                  itemOption.isOverDue && (
                    <MUText className="item-detail-left-title__label">逾期借据</MUText>
                  )
                }
                {
                  itemOption.isNewLabel && (
                    <MUText className="item-detail-left-title__label item-detail-left-title__label-green">新</MUText>
                  )
                }
                {
                  itemOption.isReinstallmentLabel && !itemOption.isWaiveAllLabel && (
                    <MUText className="item-detail-left-title__label item-detail-left-title__label-blue">参与再分期</MUText>
                  )
                }
                {
                  itemOption.isWaiveAllLabel && (
                    <MUText className="item-detail-left-title__label item-detail-left-title__label-orange">还款享息费全免</MUText>
                  )
                }
              </MUView>
              {scene === 'future' || scene === 'current' || (scene === 'current-new' && isOptional) && itemOption.subTitle
                && <MUView className="item-detail-left-date">{itemOption.subTitle}</MUView>}
              {scene === 'current-new' && (isOptional || (!isOptional && itemOption.remainAmtOrCnt2))
                && <MUView className="item-detail-left-date">{itemOption.subTitle2}</MUView>}
            </MUView>
            <MUView
              className="item-detail-right"
              beaconId="JumpItemDetail"
              onClick={(event) => jumpClick(item, event)}
            >
              <MUView className={`item-detail-right-money ${scene === 'future-new' ? 'item-detail-right-money-future-new' : ''}`}>
                <MUView>{itemOption.amount}</MUView>
                <MUView>
                  <MUIcon
                    className={scene === 'future-new' ? 'item-detail-right-money-future-new-icon' : ''}
                    value={scene === 'future-new' ? 'arrow-right' : ''}
                    size={16}
                    color="#CACACA"
                  />
                </MUView>
              </MUView>
              {scene === 'future' || scene === 'current' || (scene === 'current-new' && isOptional) && itemOption.remainAmtOrCnt
                && <MUView className="item-detail-right-remain">{itemOption.remainAmtOrCnt}</MUView>}
              {scene === 'current-new' && (isOptional || (!isOptional && itemOption.remainAmtOrCnt2)) && itemOption.remainAmtOrCnt2
                && <MUView className="item-detail-right-remain">{itemOption.remainAmtOrCnt2}</MUView>}
            </MUView>

          </MUView>
        </MUView>

        <MUView className={'detail-item-bottom'}>
          <MUView className={classNames(
            'detail-item-bottom-left',
            { interval: scene === 'future' || scene === 'future-new' || scene === 'current-new' }
          )}
          >
            {itemOption.subDesc}
          </MUView>
          {scene === 'future-new' ? (
            <MUView className="detail-item-bottom-right">
              {itemOption.remainAmtOrCnt}
            </MUView>
          ) : null}
        </MUView>
      </MUView>
    );
  }
}
