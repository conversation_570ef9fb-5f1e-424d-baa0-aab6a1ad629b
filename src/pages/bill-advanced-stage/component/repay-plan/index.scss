.plan-content-detail {
  display: block;
  overflow-y: auto;

  &-item {
    line-height: 1;
    height: 112px;
    display: flex;
    flex-direction: row;
    margin-top: 3px;

    &-higher {
      height: 132px;
    }

    &-left {
      width: 95px;
      margin-bottom: 10px;
      margin-right: 10px;
      text-align: right;
      display: flex;
      flex-direction: column;

      &-index {
        margin-bottom: 11px;
        height: 28px;
        color: #333333;
        font-size: 28px;
        font-weight: 600;
        line-height: 28px;
      }
      
      &-date {
        color: #808080;
        font-size: 24px;
        font-weight: 400;
        line-height: 24px;
      }
    }

    &-left-indrawer {
      font-size: 28px;
      color: #333333;
      display: flex;
      flex-direction: column;
      text-align: right;
      width: 95px;
    }

    &-left-normal {
      width: 160px;
      font-size: 28px;
      color: #333333;
      display: flex;
      flex-direction: column;
      text-align: left;
    }

    &-mid {
      margin-left: 30px;
      margin-right: 30px;
      display: flex;
      flex-direction: column;
      align-items: center;

      &-dot {
        height: 28px;
        width: 18px;
        display: flex;
        justify-content: center;
        align-items: center;

        &-circle {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          border: 4px solid #3477FF;
        }

        &-arrow {
          position: absolute;
          right: 0;
          top: 0;
        }
      }

      &-line {
        height: 90px;
        width: 2px;
        background-color: #CACACA;
      }

      &-line-higher {
        height: 112px;
      }
    }

    &-right {
      display: flex;
      flex: 1;
      flex-direction: column;

      &-amt {
        font-size: 28px;
        color: #333333;
      }

      &-interest {
        font-size: 24px;
        color: #A6A6A6;
        margin-top: 10px;

        &-highlight {
          color: #FF8844;
        }

        &-delete{
          text-decoration:line-through;
        }
      }
    }

    &-extra {
      .mu-icon {
        vertical-align: top;
      }
    }
  }

  &-holder {
    height: 20px;
  }
}

.hide-view {
  display: none;
}

/* width */
.plan-content-detail::-webkit-scrollbar {
  width: 10px;
}

/* Track */
.plan-content-detail::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
.plan-content-detail::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
// .plan-content-detail::-webkit-scrollbar-thumb:hover {
//   background: #555;
// }