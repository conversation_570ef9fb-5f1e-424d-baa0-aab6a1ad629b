import { Component } from '@tarojs/taro';
import {
  MUText, MUView, MUIcon
} from '@mu/zui';
import Madp from '@mu/madp';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import { setStore } from '@api/store';
import {
  EventTypes, dispatchTrackEvent
} from '@mu/madp-track';

import './index.scss';

export default class RepayPlan extends Component {
  static propTypes = {
    repayPlanAfterExtend: PropTypes.array,
    surplusTotalinteFeeWaiveAmt: PropTypes.string,
    showFeeWaiveTip: PropTypes.bool,
    useInDrawer: PropTypes.bool,
  };

  static defaultProps = {
    repayPlanAfterExtend: [],
    surplusTotalinteFeeWaiveAmt: '',
    showFeeWaiveTip: false,
    useInDrawer: false,
  }

  constructor(props) {
    super(props);
    this.state = {

    };
  }

  handleScrollToLower = () => {
    // 滚到最底下
    Madp.pageScrollTo({ scrollTop: 9999, duration: 300 });
  };

  // 还款计划列表高度
  getRepayHeight = () => {
    const listHeight = document
      && document.getElementsByClassName('at-drawer__content')
      && document.getElementsByClassName('at-drawer__content')[0]
      && document.getElementsByClassName('at-drawer__content')[0].offsetHeight;
    return `${listHeight - 110}px`;
  }

  getRepayDate(date) {
    return `${date.substring(0, 4)}/${date.substring(4, 6)}/${date.substring(6, 8)}`;
  }

  render() {
    const {
      repayPlanAfterExtend,
      surplusTotalinteFeeWaiveAmt,
      showFeeWaiveTip,
      useInDrawer
    } = this.props;

    const repayPlanListLength = repayPlanAfterExtend && repayPlanAfterExtend.length;
    let showedYear = [];

    return (
      <MUView
        className="plan-content-detail"
        style={useInDrawer ? `max-height: ${this.getRepayHeight()}` : ''}
      >
        {repayPlanListLength > 0
          ? repayPlanAfterExtend.map((repayPlanItem, index) => {
            const { payDate } = repayPlanItem;
            const year = payDate.substring(0, 4);
            const day = `${payDate.substring(4, 6)}-${payDate.substring(6, 8)}`;
            let yearHasShown = true;
            if (showedYear.indexOf(year) < 0) {
              yearHasShown = false;
              showedYear.push(year);
            }

            return (
              <MUView
                className={`plan-content-detail-item ${useInDrawer && showFeeWaiveTip ? 'plan-content-detail-item-higher' : ''}`}
                key={payDate}
              >
                {useInDrawer ? (<MUView className="plan-content-detail-item-left-indrawer">
                  <MUView className="plan-content-detail-item-left-index">第{index + 1}期</MUView>
                  {!yearHasShown ? (<MUView className="plan-content-detail-item-left-date">{year}</MUView>) : null}
                  <MUView className="plan-content-detail-item-left-date">{day}</MUView>
                </MUView>) : (<MUView className="plan-content-detail-item-left-normal">
                  {this.getRepayDate(repayPlanItem.payDate)}
                </MUView>)}
                <MUView className="plan-content-detail-item-mid">
                  <MUView className="plan-content-detail-item-mid-dot">
                    <MUView className="plan-content-detail-item-mid-dot-circle brand-border" />
                  </MUView>
                  <MUView className={repayPlanListLength !== (index + 1)
                    ? `plan-content-detail-item-mid-line ${useInDrawer && showFeeWaiveTip ? 'plan-content-detail-item-mid-line-higher' : ''}` : 'hide-view'}
                  />
                </MUView>
                <MUView className="plan-content-detail-item-right">
                  <MUText className="plan-content-detail-item-right-amt">{`${repayPlanItem.surplusPayTotalAmt}元`}</MUText>
                  <MUText className="plan-content-detail-item-right-interest">
                    {`含本金${repayPlanItem.surplusPayPrincipalAmt}元`}
                    {Number(surplusTotalinteFeeWaiveAmt) > 0 && Number(repayPlanItem.surplusPayInteAmt) > 0 && Number(repayPlanItem.inteFeeWaiveAmt) > 0
                      ? (<MUText>
                        ，息费
                        <MUText className="plan-content-detail-item-right-interest-highlight">
                          {repayPlanItem.surplusPayInteAmt}元
                        </MUText>
                        <MUText className="plan-content-detail-item-right-interest-delete">
                          {(Util.floatAdd(Number(repayPlanItem.inteFeeWaiveAmt), Number(repayPlanItem.surplusPayInteAmt))).toFixed(2)}元
                        </MUText>
                      </MUText>
                      ) : null}
                    {((Number(surplusTotalinteFeeWaiveAmt) > 0 && Number(repayPlanItem.surplusPayInteAmt) > 0 && Number(repayPlanItem.inteFeeWaiveAmt) <= 0) || (Number(surplusTotalinteFeeWaiveAmt) <= 0 && Number(repayPlanItem.surplusPayInteAmt) > 0)) ? (<MUText>
                      ，息费
                      {repayPlanItem.surplusPayInteAmt}元
                    </MUText>) : null}
                  </MUText>
                  {
                    (showFeeWaiveTip && Number(repayPlanItem.stopFeeAmt || 0) > 0) ? (
                      <MUText className="plan-content-detail-item-right-interest">
                        还款时，预计减免<MUText className="plan-content-detail-item-right-interest-highlight">{`${repayPlanItem.stopFeeAmt}元`}</MUText>
                      </MUText>
                    ) : null
                  }
                </MUView>
                <MUView
                  className="plan-content-detail-item-extra"
                  style={{ marginRight: useInDrawer ? '16px' : 0 }}
                  onClick={() => {
                    dispatchTrackEvent({
                      event: EventTypes.BC,
                      beaconId: `repayment.ReInstallmentApply.${useInDrawer ? 'ReInstallmentPlanDrawerItemClicked' : 'ReInstallmentPlanItemClicked'}`,
                    });
                    setStore({ repayPlanItem });
                    Madp.navigateTo({
                      url: '/pages/bill-advanced-stage/bills-to-repay',
                    });
                  }}
                >
                  <MUIcon value="arrow-right" size={13} color="#CACACA" />
                </MUView>
              </MUView>
            );
          }) : null}
        <MUView className="plan-content-detail-holder" />
      </MUView>
    );
  }
}
