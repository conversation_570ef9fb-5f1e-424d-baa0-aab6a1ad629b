import { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import Util from '@utils/maxin-util';
import RepayModal from '@components/repay-modal/index';
import {
  MUView, MUIcon, MUText
} from '@mu/zui';
import './index.scss';

export default class ListItem extends Component {
  static propTypes = {
    needLabel: PropTypes.bool,
    needFroze: PropTypes.bool,
    item: PropTypes.object,
    loanType: PropTypes.string
  }

  static defaultProps = {
    needLabel: true,
    needFroze: true,
    item: {},
    loanType: ''
  }

  constructor(props) {
    super(props);
    this.state = {
      showDetailModal: false, // 借款详情弹窗
    };
  }

  onDetailClick() {
    this.setState({ showDetailModal: true });
  }

  render() {
    const { item = {}, loanType = '', needFroze = true, needLabel = true } = this.props;
    const { showDetailModal } = this.state;

    const line1Title = '本金';
    const line1Desc = item.surplusPayPrincipalAmt;
    const line2Title = loanType === 'I' ? '利息' : '期费用';
    const line2Desc = loanType === 'I' ? item.surplusPayInteAmt : item.surplusPayPeriodFee;
    const line3Title = '罚息';
    const line3Desc = item.surDefaultInterestAmt;

    return (
      <MUView>
        <MUView className={`info-list-item ${needLabel ? '' : 'info-list-item-nolabel'}`}>
          <MUView className="item-left">
            {needLabel ? <MUView className="label">未还</MUView> : null}
            <MUView className="date">{Util.dateFormatter(item.payDate, 'dateslash')}</MUView>
          </MUView>
          <MUView className="item-value" onClick={() => this.onDetailClick()} beaconId="DetailItemClick">
            <MUText>{`${item.surplusPayTotalAmt}元`}</MUText>
            <MUIcon className="item-value__icon" value="info" size={16} color="#A6A6A6" />
          </MUView>
        </MUView>
        <RepayModal
          title="金额说明"
          beaconId="RepayDetailModal"
          isOpened={showDetailModal}
          closeOnClickOverlay={false}
          confirmText="我知道了"
          onConfirm={() => {
            if (needFroze) {
              Util.handleTouchScroll(true, 'reinstallment-frozen');
            }
            this.setState({ showDetailModal: false });
          }}
        >
          <MUView className="modal-content">
            <MUView className="modal-content-item">
              <MUText>{line1Title}</MUText>
              <MUText>{line1Desc}元</MUText>
            </MUView>
            {Number(line2Desc) > 0 ? (
              <MUView className="modal-content-item">
                <MUText>{line2Title}</MUText>
                <MUText>{line2Desc}元</MUText>
              </MUView>
            ) : null}
            {Number(line3Desc) > 0 ? (
              <MUView className="modal-content-item">
                <MUText>{line3Title}</MUText>
                <MUText>{line3Desc}元</MUText>
              </MUView>
            ) : null}
          </MUView>
        </RepayModal>
      </MUView>
    );
  }
}
