.info-list-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-left: 30px;
  padding: 31px 30px 28px 0;
  color: #808080;
  font-size: 30px;
  line-height: 30px;
  border-bottom: 1px solid #F3F3F3;

  &-nolabel {
    margin-left: 10px;
  }

  .item-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .label {
      width: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1PX solid #808080;
      border-radius: 6px;
      padding-top: 5px;
      padding-bottom: 5px;
    }

    .date {
      margin-left: 20px;
    }
  }

  .item-value {
    &__icon {
      margin-left: 10px;
    }
    
    .mu-icon {
      height: 35px;
    }
  }
}

.RepayModal-dialog {
  .title {
    font-weight: 500;
    margin: 49px auto 20px;
    font-size: 36px;
    color: #333333;
    text-align: left;
    line-height: 36px;
    text-align: center;
    box-sizing: border-box;
  }

  .modal-content {
    &-item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      color: #333333;
      font-size: 32px;
      line-height: 2em;
    }
  }
}