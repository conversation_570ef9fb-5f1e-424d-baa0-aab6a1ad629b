/* eslint-disable max-len */
/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  MUView, MUImage, MUButton, MURichText, MUSlogan
} from '@mu/zui';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import { injectState } from '@mu/leda';
import { miniProgramChannel, sloganUrl } from '@utils/constants';
import { Url, isMuapp } from '@mu/madp-utils';
import pageHoc from '@utils/pageHoc';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import ChannelConfig from '@config/index';
import classNames from 'classnames';
import { getStore } from '@api/store';
import { jumpToChatPage } from '@mu/chat-entry-component';
import { urlDomain } from '@utils/url_config.js';
import { QuestionPopup, EntryRate } from '@mu/survey';
import INFO_IMAGE from '@components/assets/img/xyh_info.png';
import './result.scss';

import REINSTALL_FAIL_IMG from './img/reinstallment-fail.png';
const SUCCESS_IMAGE = 'https://file.mucfc.com/zlh/3/0/202305/20230518202321a82936.png';
const PENDING_IMAGE = 'https://file.mucfc.com/zlh/3/0/202305/2023051820221669f9b7.png';
const themeColor = Util.getThemeColor(ChannelConfig.theme);

const ledaPageId = 'e76866f7-75f3-4506-a401-5b94d655217c';

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: ledaPageId
    }
  }
}, {
  pageId: 'ReInstallmentResult',
  dispatchOnMount: true,
})
@pageHoc({ title: '再分期办理' })
@injectState({
  pageId: ledaPageId,
  stateKeys: ['bannerWithTitle']
})
export default class ReinstallmentResult extends Component {
  config = {
    navigationBarTitleText: '再分期办理',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);

    this.state = {
      statusCofig: {},
      refKycComp: {},
    };
    this.isVplus = getStore('isVplus') || false;
    this.quickQuestionRef = {};
    this.bannerInfo = {};
  }

  async componentWillMount() {
    // status = 1为成功, status = 2 为审核中, status = 3 为失败页
    const status = Url.getParam('status');
    const subStatus = Url.getParam('subStatus');
    const errMsg = Url.getParam('errMsg');
    let statusConfigData = {};

    if (status === '1') {
      this.isCancel = Url.getParam('isCancel') === 'true';
      if (this.isCancel) {
        statusConfigData = {
          statusImage: INFO_IMAGE,
          statusText: '已取消',
          statusTips: '若您仍需办理，可再次进入再分期功能入口办理',
          mainBtn: '查看还款计划'
        };
      } else {
        const tips = await this.initSuccessStatusInfo();
        statusConfigData = {
          statusImage: SUCCESS_IMAGE,
          statusText: '办理成功',
          statusTips: tips,
          statusTipsSub: '',
          mainBtn: this.isOverdue || this.isDue ? '立即还款' : '返回',
          guideBtn: this.isOverdue || this.isDue ? '' : '去查看还款计划>>',
        };
      }
    } else if (status === '2') {
      // subStatus: 1为客户还需做人工kyc
      if (subStatus === '1') {
        statusConfigData = {
          statusImage: PENDING_IMAGE,
          statusText: '已提交审批',
          statusTips: '<span style="color: #FF0000;">3个工作日</span>内，会有电话联系您</br>请留意<span style="color: #FF0000;">接听95786的来电</span>',
          statusTipsSub: '',
          guideBtn: '',
        };
      } else if (subStatus === '2') {
        statusConfigData = {
          statusImage: PENDING_IMAGE,
          statusText: '还款处理中',
          statusTips: '请等待还款结果，若还款失败，需您重新操作还款',
          statusTipsSub: '',
          guideBtn: '',
        };
      } else {
        statusConfigData = {
          statusImage: PENDING_IMAGE,
          statusText: '已提交审批',
          statusTips: `结果将于<span id=yellow style="color: ${themeColor};">3个工作日</span>内短信通知`,
          statusTipsSub: '',
          guideBtn: '',
        };
      }
    } else if (status === '3') {
      // subStatus: 1为准入拒绝 2为风控拒绝 其余兜底拒绝
      if (subStatus === '1') {
        statusConfigData = {
          statusImage: REINSTALL_FAIL_IMG,
          statusText: '暂不符合办理条件',
          textLeftShow: !errMsg,
          statusTips: errMsg || '很抱歉，您暂无可办理的借据，可能原因如下，详情请咨询招联客服。',
          statusTipsSub: errMsg ? '' : '1、无在途借据 <br/>2、您的借据期限已达上限<br/>3、除等额还款、本金按期均摊外，其他还款方式不支持办理<br/>4、您的借据已过最后一期应还款日<br/>5、您已办理过相关延期服务<br/>6、您的借据不支持还款计划变更',
          mainBtn: '联系我的客服',
        };
      } else if (subStatus === '2') {
        statusConfigData = {
          statusImage: REINSTALL_FAIL_IMG,
          statusText: '办理失败',
          statusTips: '很抱歉，您暂不符合办理条件，请继续累计信用',
          statusTipsSub: '',
          mainBtn: '联系我的客服',
        };
      } else {
        statusConfigData = {
          statusImage: REINSTALL_FAIL_IMG,
          statusText: '办理失败',
          statusTips: '抱歉，当前系统异常，请稍后重试',
          statusTipsSub: '',
          mainBtn: '联系我的客服',
        };
      }
    }

    this.setState({
      statusCofig: statusConfigData
    });
    dispatchTrackEvent({
      target: this,
      event: EventTypes.EV,
      beaconId: 'ReinstallmentStatus',
      beaconContent: { 
        cus: {
          status,
          subStatus: subStatus || '',
          statusTips: statusConfigData.statusTips,
          statusTipsSub: statusConfigData.statusTipsSub,
          isCancel: !!this.isCancel
        }
      }
    });
  }

  beforeRouteLeave(from, to, next) {
    // 清理缓存，解决招行渠道跳转问题
    Madp.removeStorageSync('ReinstallmentResultRedirectUrl', 'LOCAL');
    // 解决结果页返回还款首页，数据未更新问题，失败页返回不做处理
    const { status } = (from && from.params) || {};
    if ((status === '1' || status === '2') && (to.path === '/pages/index/index' || to.path === '/index')) {
      Madp.setStorageSync('ADVANCED_STAGE_REPAY', 'Y', 'SESSION');
      next(true);
    } else {
      next(true);
    }
  }

  // 成功时展示下期还款时间及金额
  // initSuccessStatusInfo0 = async () => {
  //   const data = await Dispatch.repayment.getNearBills({}, { setNearAmount: true });
  //   const { repayDate } = data || {};
  //   const nearBillsTotalAmount = getStore('nearBillsTotalAmount');
  //   const month = repayDate && repayDate.substring(5, 7);
  //   let day = repayDate && repayDate.substring(8, 10);
  //   day = day && parseInt(day);

  //   return `${month}月${day}日 需还款<span id=yellow style="color: ${themeColor};">${nearBillsTotalAmount}</span>元，请按时还款`;
  // }

  // 成功时展示下期还款时间及金额
  initSuccessStatusInfo = () => {
    return new Promise(async (resolve) => {
      let data = await Dispatch.repayment.getNearBills({}, { setNearAmount: true });
      const { repayBillList, isDueTagCust, dueRepayInfo, surplusTotalAmt, showStatus } = data;

      this.isOverdue = false; // 是否逾期
      this.isDue = false; // 是否到期
      if (isDueTagCust === 'Y') { // 肯定逾期
        this.surplusTotalAmt = dueRepayInfo && dueRepayInfo.duePayTotalAmt ? Number(dueRepayInfo.duePayTotalAmt).toFixed(2) : 0;
        this.isOverdue = true;
      } else {
        this.surplusTotalAmt = surplusTotalAmt ? Number(surplusTotalAmt).toFixed(2) : 0;
        if (this.surplusTotalAmt > 0) { // 进一步检查是否逾期
          for (let i = 0; i < repayBillList.length; i += 1) {
            if (repayBillList[i].surplusDays < 0) {
              this.isOverdue = true;
              break;
            }
          }
        }
      }

      if (this.surplusTotalAmt > 0) {
        if (this.isOverdue) { // 逾期
          resolve(`<span id=yellow style="color: #FE5A5F;">当前仍有${this.surplusTotalAmt}元逾期，请尽快还款！<br/>诚信价值千金，失信寸步难行！</span>`);
        } else if (+showStatus === 1 || +showStatus === 2) { // 到期
          this.isDue = true;
          resolve(`<span id=yellow style="color: #FE5A5F;">当前仍有${this.surplusTotalAmt}元到期，请尽快还款！<br/>避免影响个人信用</span>`);
        } else { // 非逾期、非到期，展示下期还款时间及金额
          let { nearBillsTotalAmount, repayDate } = data || {};
          if (!(nearBillsTotalAmount && repayDate)) {
            data = await Dispatch.repayment.acquireFuturePlans({ queryType: '1' });
            if (data) {
              const { repayFutureBillDetailList } = data.data || {};
              if (repayFutureBillDetailList && repayFutureBillDetailList.length) {
                const futureBill = repayFutureBillDetailList[0] || {};
                nearBillsTotalAmount = futureBill.monthRepayAmt;
                repayDate = futureBill.repayDate;
                if (typeof repayDate === 'string') {
                  repayDate = repayDate.replace(/\./g, '');
                }
              }
            }
          }
          let month;
          let day;
          if (repayDate) {
            if (repayDate.length === 8) {
              month = repayDate && repayDate.substring(4, 6);
              day = repayDate && repayDate.substring(6, 8);
            } else {
              month = repayDate && repayDate.substring(5, 7);
              day = repayDate && repayDate.substring(8, 10);
            }
          }
          month = month && parseInt(month);
          day = day && parseInt(day);
          resolve(`${month && day ? `${month}月${day}日，` : ''}需还款<span id=yellow style="color: ${themeColor};">${nearBillsTotalAmount}</span>元，请按时还款`);
        }
      } else {
        resolve('');
      }
    });
  }

  // 判断当前的webview是否在小程序环境中，已经在madp-util中了(改这里的时候它还没上生产)，20240110之后可以使用madp-util的isWebViewInMicroApp
  isWebViewInMicroApp() {
    if (process.env.TARO_ENV === 'h5') {
      const ua = navigator.userAgent.toLowerCase();
      // 抖音toutiaomicroapp 百度baiduboxapp
      return (
        /miniprogram/.test(ua)
        || /toutiaomicroapp/.test(ua)
        || /baiduboxapp/.test(ua)
      );
    }
    return false;
  }

  // 返回按钮处理
  clickMainBtnHandler = (mainBtn = '') => {
    const status = Url.getParam('status');
    if (mainBtn === '查看还款计划') {
      this.subBtnHandler();
      return;
    } else if (mainBtn === '立即还款') {
      Madp.setStorageSync('ADVANCED_STAGE_REPAY', 'Y', 'SESSION');
      Madp.redirectTo({
        url: '/pages/express-repay/index?_windowSecureFlag=1'
      });
      return;
    }

    const isOverdueCust = getStore('isOverdueCust');
    // 拒绝页面增强引导联系客服: 未逾期客户直接进入在线客服；逾期客户直接进入信用管家。
    if (status === '3') {
      if (isOverdueCust) {
        jumpToChatPage({
          busiEntrance: 'YQZFQJJ',
          extraParam: {
            needLogin: 1
          }
        });
      } else {
        jumpToChatPage({
          busiEntrance: 'WYZFQJJ',
          extraParam: {
            needLogin: 1
          }
        });
      }
      return;
    }

    const redirectUrlFromUrl = Url.getParam('resultRedirectUrl') ? decodeURIComponent(Url.getParam('resultRedirectUrl')) : '';
    const reinstallmentRedirectUrlFromLocal = Madp.getStorageSync('ReinstallmentResultRedirectUrl', 'LOCAL') ? decodeURIComponent(Madp.getStorageSync('ReinstallmentResultRedirectUrl', 'LOCAL')) : '';
    const redirectUrl = redirectUrlFromUrl || reinstallmentRedirectUrlFromLocal || '';
    const isMiniProgramChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;

    // 特别处理如果为招联app场景下，非还款场景进入的时候直接关闭webview
    if (isMuapp() && redirectUrl.indexOf('repayment/#/') === -1) {
      Madp.closeWebView();
      return;
    }

    // 存在redirectUrl时，跳转到redirectUrl
    if (redirectUrl && redirectUrl !== 'undefined') {
      if (isMiniProgramChannel) {
        Madp.miniProgram.reLaunch({
          url: redirectUrl
        });
      } else {
        Madp.redirectTo({ url: redirectUrl });
      }
    } else if (Madp.getChannel() === '3CMBAPP') {
      const cmbRedirectUrl = `${urlDomain}/${Madp.getChannel()}/ibfcmb/#/pages/index/index`;
      // Madp.redirectTo({ url: cmbRedirectUrl });
      Madp.reLaunch({ url: cmbRedirectUrl }).then().catch(() => {
        Madp.closeWebView();
      });
    } else {
      if (isMiniProgramChannel) {
        Madp.miniProgram.reLaunch({
          url: '/repayment/pages/index/index'
        });
      } else {
        const length = window && window.history && window.history.length;
        if (length === 1) {
          Madp.closeWebView();
        } else {
          Madp.navigateBack({ delta: length - 1 });
        }
      }
    }
  }

  // 成功跳还款页或失败跳客服
  subBtnHandler = () => {
    const status = Url.getParam('status');
    if (status === '1') { // 成功跳还款首页
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ToRepayPlan', beaconContent: { cus: { status } } });
      const isMiniProgramChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
      if (isMiniProgramChannel) {
        Madp.miniProgram.reLaunch({
          url: '/repayment/pages/index/index'
        });
      } else {
        Util.router.replace({
          path: '/pages/index/index',
        });
      }
    }
  }

  // 触发问卷
  showQuestionPopup = () => {
    const { refKycComp } = this.state;
    if (refKycComp && refKycComp.showPopup && refKycComp.surveySerialNo) {
      refKycComp.showPopup({
        key: 'QuestionPopup', // 问卷组件唯一识别 key
        surveySerialNo: refKycComp.surveySerialNo, //  问卷id
        isShow: true, // 是否直接显示弹窗, 默认true
      });
    }
  }

  // 获取banner展位数据，lui中banner无法自定义点击事件，所以自己封装实现点击后触发kyc
  getBannerData = () => {
    const { bannerWithTitle } = this.state;
    const bannerUrl = bannerWithTitle && bannerWithTitle.dataObj && bannerWithTitle.dataObj.contentList && bannerWithTitle.dataObj.contentList[0] && bannerWithTitle.dataObj.contentList[0].imgUrl;
    const bannerTitle = bannerWithTitle && bannerWithTitle.dataObj && bannerWithTitle.dataObj.contentList && bannerWithTitle.dataObj.title;
    const bannerTitleColor = bannerWithTitle && bannerWithTitle.dataObj && bannerWithTitle.dataObj.contentList && bannerWithTitle.dataObj.titleColor;
    return {
      showBanner: !!bannerUrl,
      bannerUrl,
      bannerTitle,
      bannerTitleColor
    };
  }

  render() {
    const status = Url.getParam('status');
    const subStatus = Url.getParam('subStatus');
    const { statusCofig } = this.state;
    const { statusImage, statusText, statusTips, statusTipsSub, mainBtn = '返回', guideBtn, textLeftShow = false } = statusCofig || {};
    const { showBanner, bannerUrl, bannerTitle, bannerTitleColor } = this.getBannerData();
    let mainBtnBeaconId;
    let guideBtnBeaconId;
    if (mainBtn === '查看还款计划') {
      mainBtnBeaconId = 'ReInstallmentCancelPlanBtn';
    } else if (mainBtn === '立即还款') {
      mainBtnBeaconId = 'ReInstallmentRepayBtn';
    } else { // '返回'
      mainBtnBeaconId = 'ReinstallmentResultBackBtn';
    }

    if (status === '1' && !this.isCancel) {
      guideBtnBeaconId = 'ReinstallmentSuccessPlanBtn';
    } else {
      guideBtnBeaconId = 'ReinstallmentGuideBtn';
    }

    return (
      <MUView className="pages-bg">
        <MUView className="reinstallment-result" style={{ backgroundColor: status === '1' ? '' : '#FFFFFF' }}>
          <MUView className="reinstallment-result-top">
            <MUImage className={classNames('slogan', { 'slogan-plus': this.isVplus })} src={this.isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
            <MUView className="status-img-container"><MUImage className="status-img" src={statusImage} /></MUView>
            <MUView className="status-text">{statusText}</MUView>
            <MURichText className={classNames('status-tips', { 'status-tips_left': textLeftShow })} nodes={statusTips} />
            {statusTipsSub && (<MURichText className="status-tips-sub" nodes={statusTipsSub} />)}
            <MUButton
              beaconId={mainBtnBeaconId}
              beaconContent={{ cus: { status, subStatus } }}
              className="result-btn"
              type="primary"
              onClick={() => {
                this.clickMainBtnHandler(mainBtn);
              }}
            >{mainBtn}</MUButton>
            {guideBtn && (
              <MUView
                className="guide-btn"
                beaconId={guideBtnBeaconId}
                onClick={() => {
                  this.subBtnHandler();
                }}
              >
                {guideBtn}
              </MUView>)
            }
          </MUView>
          {/* 为你推荐banner */}
          {status === '1' && showBanner && (<MUView className="result-banner">
            <MUView className="result-banner-content">
              {bannerTitle && <MUView className="result-banner-content-title" style={{ color: bannerTitleColor }}>{bannerTitle}</MUView>}
              <MUImage className="result-banner-content-img" beaconId="BannerClick" onClick={this.showQuestionPopup} src={bannerUrl} />
            </MUView>
          </MUView>)}
          {/* 星星评分 */}
          {status === '1' && !showBanner && (<MUView className="repay-result-entryrate">
            <EntryRate
              key="EntryRate"
              componentKey="EntryRate"
              ledaPageId={ledaPageId} // 模版id, 必填
              pageId="repayment.ReInstallmentResult"
            />
          </MUView>)}
          {/* 中断KYC */}
          <MUView className="loan-result-page_quick-question">
            <QuestionPopup
              key="QuestionPopup"
              componentKey="QuestionPopup"
              ledaPageId={ledaPageId}
              pageId="repayment.ReInstallmentResult"
              getRef={(params) => {
                this.setState({
                  refKycComp: params
                });
              }}
            />
          </MUView>
          <MUView className={`repay-result-container${status === '1' ? '' : '-absolute'}`}>
            <MUSlogan className="result-slogan" onlyLogo />
          </MUView>
        </MUView>
      </MUView>
    );
  }
}
