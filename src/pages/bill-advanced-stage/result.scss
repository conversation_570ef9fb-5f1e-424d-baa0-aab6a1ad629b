.reinstallment-result {
  height: 100%;
  min-height: 100vh;
  background: #F3F3F3;

  &-top {
    background-color: #FFFFFF;
    padding: 40px 0;

    .slogan {
      width: 206px;
      height: 44px;
      padding-bottom: 35px;
      left: 50%;
      transform: translateX(-50%);
  
      &-plus {
        width: 249px;
      }
    }
  
    .status-img-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
  
      .status-img {
        width: 120px;
        height: 120px;
      }
    }
  
    .status-text {
      margin-top: 40px;
      font-weight: 500;
      font-size: 40px;
      color: #333333;
      line-height: 60px;
      text-align: center;
    }
  
    .status-tips {
      margin: 26px 50px 0;
      color: #888888;
      font-size: 28px;
      text-align: center;
      line-height: 42px;
  
      &_left {
        text-align: left;
      }
    }
  
    .status-tips-sub {
      margin: 30px 50px 0;
      font-size: 28px;
      color: #808080;
      line-height: 42px;
    }

    .result-btn {
      margin: 50px 30px 0 30px;
    }
  
    .guide-btn {
      margin-top: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28px;
      color: #808080;
      text-align: center;
      line-height: 42px;
    }
  }

  .result-banner {
    width: 100%;
    padding: 0 20px;
    margin-top: 20px;
    box-sizing: border-box;

    &-content {
      width: 100%;
      // height: 100%;
      padding: 25px 30px 20px;
      box-sizing: border-box;
      border-radius: 16px;
      background-color: #FFFFFF;
  
      &-title {
        width: auto;
        height: 40px;
        padding-bottom: 20px;
        font-weight: 500;
        font-size: 26px;
        color: #333333;
        letter-spacing: 0;
        text-align: left;
        line-height: 40px;
      }
  
      &-img {
        width: 100%;
        height: 174px;
      }
    }
  }

  .repay-result-entryrate {
    height: 100%;
    height: 232px;
    padding: 0 20px;
    margin-top: 20px;
  }

  .repay-result-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 100px;
  }

  .repay-result-container-absolute {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 100px;
    position: absolute;
    left: 0;
    bottom: 0;
  }
}