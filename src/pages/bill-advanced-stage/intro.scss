.intro-bg {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  // width: 100vw;
  height: 100vh;

  &-blue {
    width: 100%;
    height: 540px;
    background-image: url("./img/bg_blue.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-attachment: local;
  }

  &-content {
    margin: 30px 20px;
    position: relative;

    &-img {
      background-color: #fff;
      padding: 60px 20px 10px;
      border-radius: 16px;

      .slogan {
        width: 206px;
        height: 44px;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);

        &-plus {
          width: 249px;
        }
      }

      .img_top {
        width: 100%;
        height: 238px;
        object-fit: cover;
      }

      .img_middle {
        width: 100%;
        height: 1022px;
        object-fit: cover;
      }

      .img_bottom {
        width: 100%;
        height: 582px;
        object-fit: cover;
      }
    }

    &-blank {
      width: 100%;
      height: 180px;
      background-color: #f3f3f3;
    }

    &-btn {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #fff;
      padding: 30px;

      .btn {
        width: 670px;
      }
    }
  }
}