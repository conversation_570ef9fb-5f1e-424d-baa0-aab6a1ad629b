import { Component } from '@tarojs/taro';
import {
  MU<PERSON>iew, MUIcon, MUImage, MUText
} from '@mu/zui';
import { track, EventTypes } from '@mu/madp-track';
import pageHoc from '@utils/pageHoc';
import classNames from 'classnames';
import Util from '@utils/maxin-util';
import { sloganUrl } from '@utils/constants';
import { Url } from '@mu/madp-utils';
import { getLoginInfo } from '@mu/business-basic';
import DetailItem from './component/detail-item/index';
import RepayModal from './component/repay-modal/index';
import { injectState } from '@mu/leda';
import { getStore } from '@api/store';
import Dispatch from '@api/actions';
import './reinstallment-bill-detail.scss';

@track({ event: EventTypes.PO }, {
  pageId: 'ReinstallmentBillingDetail',
  dispatchOnMount: true,
})
  @pageHoc({ title: '借据详情' })
  @injectState({
    // pageId: ledaPageId,
    // stateKeys: ['bannerWithTitle']
  })
export default class ReinstallmentBillingDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShowRepayIntro: false,
      orderInfo: {},
      isExtendPlan: Url.getParam('isExtendPlan') || '',
      isVplus: getStore('isVplus') || false // 是否为v+会员
    };
  }

  async componentDidMount() {
    const orderNo = Url.getParam('orderNo') || '';
    const allOrderInfoList = getStore('allOrderInfoList') || [];
    if (orderNo) {
      const filteredList = allOrderInfoList.filter(item => item.orderNo === orderNo);
      if (filteredList.length > 0) { // 可展期借据、合并后新借据
        this.setState({
          orderInfo: filteredList[0]
        });
      } else { // 不可展期借据明细
        const loginInfo = await getLoginInfo();
        const { userIdHash, custIdHash } = loginInfo || {};
        const result = await Dispatch.repayment.queryDetailNew({ orderNo, queryScene: '02', userHashNo: userIdHash, custHashNo: custIdHash });
        const { data } = result || {};
        const { loanInfoDetail } = data || {};
        const {
          surplusTotalPrincipalAmt, // 剩余待还本金
          irrAnnualRate,
          feeRate,
          installTotalCnt, // 分期总期数
          paymentTypeShortName, // 还款方式描述
          loanType,
          repaymentPlanList
        } = loanInfoDetail || {};
        const extensionLoanRepayPlan = (repaymentPlanList || []).map(
          item => {
            const {
              repayDate,
              surplusRepayAmt, // 剩余应还总金额
              surplusPrincipalAmt, // 待还本金
              surplusInteAmt, // 待还利息
              // 利息与期费率合并字段，surplusPeriodFee
              surplusFineAmt // 罚息
            } = item;
            return {
              ...item,
              payDate: repayDate,
              surplusPayTotalAmt: surplusRepayAmt,
              surplusPayPrincipalAmt: surplusPrincipalAmt,
              surplusPayInteAmt: surplusInteAmt,
              surplusPayPeriodFee: surplusInteAmt,
              surDefaultInterestAmt: surplusFineAmt
            };
          }
        );

        this.setState({
          orderInfo: {
            extensionPayTotalPrincipalAmt: surplusTotalPrincipalAmt,
            extensionYearRate: irrAnnualRate,
            extensionPeriodFeeRate: Number(installTotalCnt) > 0 ? Number(feeRate) / Number(installTotalCnt) : 0,
            extensionPrincipalType: paymentTypeShortName,
            loanType,
            extensionLoanRepayPlan,
          }
        });
      }
    }
  }

  config = {
    navigationBarTitleText: '借据详情',
    navigationStyle: process.env.TARO_ENV === 'weapp' ? 'custom' : 'default'
  }

  // 展示还款方式说明弹窗
  showRepayIntro = () => {
    this.setState({
      isShowRepayIntro: true
    });
  }

  render() {
    const { isShowRepayIntro, orderInfo, isExtendPlan, isVplus } = this.state;
    const {
      extensionPayTotalPrincipalAmt,
      extensionYearRate,
      extensionPeriodFeeRate,
      extensionPrincipalType,
      extensionPrincipalCode,
      loanType,
      extensionLoanRepayPlan,
    } = orderInfo;
    const filterdExtensionLoanRepayPlan = extensionLoanRepayPlan
      ? extensionLoanRepayPlan.filter((item) => item.planStatus !== '01') : [];

    return (
      <MUView className="reinstallment-bills-detail">
        <MUView className="bill-details">
          <MUView className="bill-details-content">
            <MUView className="bill-details-content-area">
              <MUView className="main">
                <MUView className="main-title">{isExtendPlan === 'E' || isExtendPlan === 'Y' ? '再分期还款' : '待还'}本金(元)</MUView>
                <MUView className="main-value">{extensionPayTotalPrincipalAmt}</MUView>
                {/* <MUView className="main-desc">
                  {`本金${extensionPayTotalPrincipalAmt}元+${loanType === 'I' ? '利息' : '期费用'}
                  ${loanType === 'I' ? extensionPayTotalInteAmt : extensionPayTotalPeriodFee}元`}
                </MUView> */}
              </MUView>
              {
                Number(extensionYearRate) > 0 ? (
                  <MUView className="sub">
                    <MUView>年利率(单利)</MUView>
                    {/* 因价格试算问题，calMode试算模式是00套餐减免信息试算，合同落库是01是优惠券减免信息试算，预览与落库利率不一致，不展示划线价格 */}
                    {/* {(Number(extensionWaivedYearRate) > 0 && (Number(extensionWaivedYearRate) < Number(extensionYearRate))) ? (
                      <MUView>{Util.stringToPersent(extensionWaivedYearRate, 4)}<MUText className="sub-line">{Util.stringToPersent(extensionYearRate, 4)}</MUText></MUView>
                    ) : (<MUView>{Util.stringToPersent(extensionYearRate, 4)}</MUView>)} */}
                    <MUView>{Util.stringToPersent(extensionYearRate, 4)}</MUView>
                  </MUView>
                ) : null
              }
              {
                loanType === 'F' && Number(extensionPeriodFeeRate) > 0 ? (
                  <MUView className="sub">
                    <MUView>期费率</MUView>
                    <MUView>{Util.stringToPersent(extensionPeriodFeeRate, 4)}</MUView>
                  </MUView>
                ) : null
              }
              {
                extensionPrincipalType ? (
                  <MUView className="sub">
                    <MUView>还款方式</MUView>
                    <MUView
                      beaconId="ReDetailRepayDesc"
                      onClick={extensionPrincipalCode === 'PS200' ? () => { this.showRepayIntro(); } : () => {}}
                    >
                      {extensionPrincipalType}
                      {extensionPrincipalCode === 'PS200'
                        ? (<MUView className="sub-icon">
                          <MUIcon value="info" size={18} color="#A6A6A6" />
                        </MUView>
                        ) : null}
                    </MUView>
                  </MUView>
                ) : null
              }
            </MUView>
            <MUView className="bill-details-content-plan">
              <MUView className="info-title">
                <MUView className="info-title-left">还款计划</MUView>
                <MUView>{`${filterdExtensionLoanRepayPlan.length || '0'}期待还`}</MUView>
              </MUView>
              {
                filterdExtensionLoanRepayPlan.length > 0 ? (
                  <MUView className="info-list" >
                    {
                      filterdExtensionLoanRepayPlan
                        .map((reDetailItem) =>
                          (<DetailItem
                            needLabel={false}
                            needFroze={false}
                            item={reDetailItem}
                            loanType={loanType}
                          />)
                        )
                    }
                  </MUView>
                ) : null
              }
              <MUView className="info-placeholder" />
            </MUView>
          </MUView>
        </MUView>
        <RepayModal
          className="details-modal"
          title="还款方式说明"
          beaconId="RepayWayExplainDialog"
          isOpened={isShowRepayIntro}
          onClose={() => {
            this.setState({ isShowRepayIntro: false });
          }}
        >
          <MUView className="details-modal-content">
            <MUView className="details-modal-content__subTitle">前期少还 到期结清</MUView>
            <MUView>前期月还款金额较少，后期每月还款金额较多，直至结清</MUView>
          </MUView>
          <MUImage className={classNames('details-modal-slogan', { 'details-modal-slogan-plus': isVplus })} src={isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
        </RepayModal>
      </MUView>
    );
  }
}
