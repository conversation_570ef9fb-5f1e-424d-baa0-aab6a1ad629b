import { Component } from '@tarojs/taro';
import {
  MUView, MUButton
} from '@mu/zui';
import Madp from '@mu/madp';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import pageHoc from '@utils/pageHoc';
import ListItem from './component/list-item/index';
import { injectState } from '@mu/leda';
import { getStore } from '@api/store';
import './bills-to-repay.scss';

@track({ event: EventTypes.PO }, {
  pageId: 'ReInstallmentBillsToRepay',
  dispatchOnMount: true,
})
@pageHoc({ title: '待还账单' })
@injectState({
  // pageId: ledaPageId,
  // stateKeys: ['bannerWithTitle']
})
export default class ReinstallmentBillsToRepay extends Component {
  constructor(props) {
    super(props);
    this.state = {
      repayPlanItem: getStore('repayPlanItem')
    };
  }

  config = {
    navigationBarTitleText: '待还账单',
    navigationStyle: process.env.TARO_ENV === 'weapp' ? 'custom' : 'default'
  }

  render() {
    const { repayPlanItem = {} } = this.state;
    const { payDate, surplusPayTotalAmt, extendRepayPlans = [] } = repayPlanItem;
    let month;
    let day;
    if (payDate && payDate.length === 8) {
      month = payDate.substring(4, 6);
      if (month[0] === '0') {
        month = month[1];
      }
      day = payDate.substring(6, 8);
    }

    return (
      <MUView className="reinstallment-bills-to-repay">
        <MUView className="reinstallment-bills-to-repay-top">
          <MUView className="reinstallment-bills-to-repay-top-first">
            {month && day ? `${month}月${day}日应还(元)` : ''}
          </MUView>
          <MUView className="reinstallment-bills-to-repay-top-second">
            {surplusPayTotalAmt}
          </MUView>
          <MUView className="reinstallment-bills-to-repay-blank" />
        </MUView>

        <MUView className="reinstallment-bills-to-repay-list">
          <MUView className="reinstallment-bills-to-repay-list-content">
            <MUView className="reinstallment-bills-to-repay-list-content-detail">
              {
                extendRepayPlans.map((orderItem, index) => (
                  <MUView>
                    <ListItem
                      scene="future-new"
                      item={orderItem}
                      jumpClick={(item, event) => {
                        if (event) {
                          event.stopPropagation();
                        }
                        Madp.navigateTo({
                          url: `/pages/bill-advanced-stage/reinstallment-bill-detail?orderNo=${item.orderNo}&isExtendPlan=${item.isExtendPlan}`,
                        });
                      }}
                    />
                    {index === extendRepayPlans.length - 1 ? null : <MUView className="reinstallment-bills-to-repay-blank" /> }
                  </MUView>
                ))
              }
            </MUView>
          </MUView>
        </MUView>


        <MUView className="reinstallment-bills-to-repay-footer">
          <MUView className="reinstallment-bills-to-repay-blank" />
          <MUView className="reinstallment-bills-to-repay-footer-btn">
            <MUButton
              type="primary"
              onClick={() => {
                Madp.navigateBack();
              }}
              full
              beaconId="ReInstallmentBillsToRepayBack"
            >
              返回
            </MUButton>
          </MUView>
        </MUView>

      </MUView>
    );
  }
}
