/* eslint-disable no-plusplus */
/* eslint-disable camelcase */
/* eslint-disable react/sort-comp */
import Taro, { Component } from '@tarojs/taro';
import {
  MUView, MUIcon, MUText, MURadio, MUButton, MUImage, MUDrawer, MUScrollView, MUSlider
} from '@mu/zui';
import Madp from '@mu/madp';
import {
  track, EventTypes, dispatchTrackEvent
} from '@mu/madp-track';
import { throttle, Url, getCurrentPageUrlWithArgs } from '@mu/madp-utils';
import Util from '@utils/maxin-util';
import { filterQualCoupons } from '@utils/repay-util';
import pageHoc from '@utils/pageHoc';
import classNames from 'classnames';
import Protocol from '@components/protocol/index';
import Dispatch from '@api/actions';
import { getStore, setStore } from '@api/store';
import RepayPlan from './component/repay-plan/index';
import RepayModal from './component/repay-modal/index';
import OriginalIousList from './component/original-ious-list/index';
import NumberInput from './component/number-input';
import MyPicker from '@components/my-picker';
import { miniProgramChannel, sloganUrl, EVENT_CODE_MAP } from '@utils/constants';
import { urlDomain } from '@utils/url_config.js';
import { injectState } from '@mu/leda';
import { OpRepayment } from '@mu/op-comp';
import { opService, getPageConf } from '@mu/business-basic';

import './list.scss';

import checkBlue from './img/check_blue.png';
import amoutChange from './img/amout_change.png';
import huiIcon from './img/hui_icon.png';
import vPlusSloganTopLeft from './img/top_left_v.png';
import sloganTopLeft from './img/top_left.png';

// 处理微信小程序内嵌H5返回空白的问题
if (process.env.TARO_ENV === 'h5') {
  window.onpageshow = (event) => {
    if (
      event.persisted || (window.performance && (window.performance.navigation.type === 2 || window.performance.navigation.type === '2'))
    ) {
      if (Madp.getStorageSync('SXF_BACK', 'SESSION') === 'Y') {
        Madp.setStorageSync('SXF_BACK', '', 'SESSION');
        Madp.miniProgram.navigateBack();
      }
    }
  };
}
// 默认页面展位id
const pageId = 'c9fa22b9-47e3-4de1-b705-9813b552e408';

@track({
  event: EventTypes.PO,
  beaconContent: {
    cus: {
      pageId: pageId
    }
  }
}, {
  pageId: 'ReInstallmentApply',
  dispatchOnMount: true,
})
@pageHoc({ title: '再分期办理' })
@injectState({
  pageId() {
    return pageId;
  },
  getPageConf: () => getPageConf(pageId, true),
  stateKeys: []
})
export default class ReInstallmentApply extends Component {
  config = {
    navigationBarTitleText: '再分期办理',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  constructor(props) {
    super(props);
    this.state = {
      extendPackageList: [], // 分期还套餐数据
      channelAgreementPlanCfgDtoList: [{}], // 合同数组
      isCheckedContract: '', // 表示合同组件是否同意勾选合同, 值为hasCheckedContract标识选中
      alreadyForceFlag: false, // 表示合同是否已经强读过
      selectExtendCalInfo: {}, // 选中的分期还套餐试算结果信息
      repayPlanAfterExtend: [], // 试算后的新还款计划列表(选中的还款方案对应的列表)
      isReInstallmentPlanDrawerOpen: false, // 新还款计划弹窗是否打开
      showPeriodPicker: false, // 显示期数选择
      showPeriodPickeSlider: false, // 展开期数选择
      pickerOptions: [], // 期数选择范围
      showCommonModal: false, // 显示弹窗
      commonModalInfo: {}, // 弹窗内容
      selectedCnt: '', // 选中延期期数
      selectedPreCnt: '', // 选中的前N期数
      sliderValue: 0, // slider选择器的值
      numberInputValue: 0, // 输入计数器的值
      setExpectTotalRepayAmt: '', // 设置的前N期月还款金额（第一阶段期供金额）
    };
    this.showPage = false; // 是否渲染主页面
    this.applyNo = ''; // 建案号
    this.contractApplyList = []; // 所需的合同信息
    this.contractInfos = []; // 3.0合同
    this.orderInfoListCanExtend = [], // 可展期借据明细
    this.orderInfoListAfterMerge = [], // 合并后新借据明细
    this.originalOrderInfoListCanNotExtend = [], // 原始的不可展期借据明细（用于借据列表展示）
    this.originalOrderInfoListCanExtend = [], // 原始的可展期借据明细（用于借据列表展示）
    this.billType = 'advanced-stage';
    this.extendMode = ''; // 再分期模式，00：未来期按原还款方式展期 01：未来期按分段还款方式展期
    this.needRepayType = ''; // 再分期办理还款条件，1：仅还可展期借据 需还所有借据
    this.mergeOrderFlag = ''; // 借据合并标记，Y合并借据，N不允许合并借据
    this.waiveInteFeeFlag = ''; // 办理后是否停息停费，Y停息停费，N不停息停费
    this.extendModeAndScene = '', // 记录再分期模式和场景
    this.waiveRate = ''; // 未来期减免比例
    this.isOverdueCust = false; // 是否逾期客户
    this.resultRedirectUrl = Url.getParam('finishRedirect') ? encodeURIComponent(Url.getParam('finishRedirect')) : '';
    this.miniChannelFlag = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
    this.supplySuccess = Url.getParam('supplySuccess');
    this.resultId = Url.getParam('resultId') || ''; // 问卷结果
    this.isVplus = false; // 是否为v+会员
    this.awardNo = ''; // 选中的券编码
    this.needFileList = []; // 待补充资料列表
    this.adjustCntList = []; // 可选延期期数
    this.adjustPreCntList = []; // 可选前N期期数
    this.pickerType = ''; // 记录picker的触发类型
    this.expectRepayAmtPercent = ''; // 记录第一段期供比例
    this.originalMaxExtendAmt = ''; // 滑动选择器最大值
    this.originalMinExtendAmt = ''; // 滑动选择器最小值
    this.custTypeAndFileTypeList = []; // 待补充身份和资料信息列表
    this.opRetainOpened = ''; // // 是否已经成功展示过挽留弹窗
    this.repaymentFlag = Url.getParam('repaymentFlag') || ''; // 是否还款模块跳入的
  }

  componentDidShow() {
    Madp.setNavigationBarTitle({ title: '再分期办理' });
  }

  async componentDidMount() {
    // 若是补充身份信息且成功回来的，则跳转到补充联系人页面
    if (this.supplySuccess === '1') {
      this.handleContinue();
      return;
    }

    // 检查是否办理按钮点击离开的办理页，是则直接返回首页
    const isclose = this.checkAndClosePage();
    if (isclose) {
      return;
    }

    // 获取账单信息、客户逾期信息
    this.initAllBillsInfo();
    // 办理场景相关信息查询，名单可以和cdp标签一起查
    /*
     * v+会员(cif标签)，HIGH_VALUE_CUST=H
     * 微光卡(cdp标签)，wg_card_can_opn_type in (fdz_zsk,gx_zsk)且lst_wg_card_exp_dt距今剩余天数大于等于0天
     * 信用不负期待(cdp名单)，l_fd_v9zk_zx='Y'
     * 风险标签(cdp标签)，
     * 217-SSA01，人工作业；217-SSA02，博弈
    */
    const [cdpData, cifData, couponList, dispatchFlag] = await Promise.all([
      this.getTagContext({ tagType: 0, userSheetCodeList: ['l_fd_v9zk_zx', 'l_zdsj_dlypb'], tagCodeList: ['wg_card_can_opn_typ', 'lst_wg_card_exp_dt'] }),
      this.getTagContext({ tagType: 2, tagCodeList: ['HIGH_VALUE_CUST'] }),
      this.getTransCouponList(),
      this.resultId ? this.handleKycBack() : (() => {})()
    ]);
    this.judgeIsVplus(cifData);
    this.applyScene = this.getApplyScene(cdpData, cifData, couponList);
    if (this.resultId && dispatchFlag) {
      return;
    }

    // 正常进入页面将需要回退标志位置为‘’，解决从介绍页返回会意外退出页面的问题
    Madp.setStorageSync('SXF_BACK', '', 'SESSION');
    Madp.setStorageSync('doKycExit', '', 'SESSION');

    // 清空缓存再分期数据
    setStore({ selectedBillList: [], advancedStageInfo: {} });
    this.initData();
  }

  checkAndClosePage = () => {
    let closeFlag = false;
    const leaveFlag = Madp.getStorageSync('ReinstallmentConfirmBtnLeave', 'SESSION');
    if (leaveFlag === 'Y') {
      Util.closeOrBack('ReinstallmentConfirmBtnLeave');
      closeFlag = true;
    }
    return closeFlag;
  };

  // 提供放置挽留弹窗的位置
  async beforeRouteLeave(from, to, next, options) {
    const opEvent = this.mergeOrderFlag === 'Y' ? EVENT_CODE_MAP.billAdvancedMergeRetain : EVENT_CODE_MAP.billAdvancedStageRetain;
    try {
      // 返回时，逾期触发op交互事件：挽留弹窗
      if ((to.path === from.path) && this.isOverdueCust && this.opRetainOpened !== 'SUCCESS') {
        const opRes = await this.opOnPageEvent('opPageLeave', opEvent);
        // // 标记是否打开过，如选择优惠还款留在当前页，再次进入挽留逻辑，直接next(true),不再进入op
        this.opRetainOpened = opRes;
        // // 如未正确打开op，也进行返回兜底
        if (opRes !== 'SUCCESS') {
          Util.closeOrBack();
        } else {
          // 如果成功打开op，则不继续导航,取消路由变化；解决二次进入后无法返回的问题
          next(false);
          return;
        }
      } else {
        if (from.path === to.path) {
          Util.closeOrBack();
        } else {
          next(true);
        }
      }
    } catch (error) {
      console.log('error:', error);
      if (from.path === to.path) {
        Util.closeOrBack();
      } else {
        next(true);
      }
    }
  }

  async opOnPageEvent(eventName, interactionEventCode) {
    return new Promise((resolve) => {
      try {
        opService.process({
          eventName,
          data: {
            interactionEventCode,
            pageId: pageId,
            opRetainClose: () => { Util.closeOrBack(); }, // 退出方法
            trackPageId: this.mergeOrderFlag === 'Y' ? 'billAdvancedMerge' : 'billAdvanced',
            pageCode: 'reInstallment' // op用于区分挽留弹窗
          },
          callback: (res) => {
            resolve(res);
          }
        });
      } catch (error) {
        resolve('ERROR');
      }
    });
  }

  // 处理从补充身份证回来的流程接续
  handleContinue = () => {
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'UpdateIDCardSuccess' });
    const { applyNo: applyNoFromStore } = getStore('advancedStageInfo') || {};
    const applyNo = (applyNoFromStore || Url.getParam('applyNo')) || '';
    if (this.miniChannelFlag) {
      Taro.redirectTo({
        url: `/pages/identity/contact?billType=${this.billType}&applyNo=${applyNo}&repaymentFlag=${this.repaymentFlag}`
      });
    } else {
      Util.router.replace({
        path: `/pages/identity/contact?repaymentFlag=${this.repaymentFlag}`,
        query: {
          billType: this.billType,
          applyNo,
        }
      });
    }
  }

  // 问卷作答回来，先提交kyc作答资料，并存下问卷答题结果
  handleKycBack = async () => {
    let dispatchFlag = false;
    const { fileType, questionnaireId, applyNo } = getStore('advancedStageIApplyMaterial') || {};
    Madp.setStorageSync('doKycBack', 'Y', 'SESSION');
    const { ret } = await Dispatch.repayment.postLoanAddData({
      applyNo,
      addInfoScene: '00',
      applyMaterialList: [{
        fileType,
        questionnaireId,
        fileResult: this.resultId,
      }],
    }, {});

    if (ret !== '0') {
      let urlParam = this.resultRedirectUrl ? `status=3&resultRedirectUrl=${this.resultRedirectUrl}` : 'status=3';
      Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
      dispatchFlag = true;
    }
    return dispatchFlag;
  }

  initData = () => {
    // 初始化获取建案信息，建案状态
    this.initConsultRepayInfo();
  }

  // 办理标签查询
  getTagContext = async (paramObj) => {
    const { data } = await Dispatch.repayment.getTagContext({
      cacheType: 1,
      sourceModule: 'RBF',
      ...paramObj
    });
    return data || {};
  }

  // 判断是否为v+会员
  judgeIsVplus = (cifData) => {
    const { tagMap } = cifData || {};
    setStore({ isVplus: false }); // 清缓存
    if (tagMap && tagMap.HIGH_VALUE_CUST === 'H') {
      this.isVplus = true;
      setStore({ isVplus: true });
    }
  }

  // 办理资格券查询，217-SSA02博弈催收、217-SSA01作业催收 SSA07不展示
  async getTransCouponList() {
    const { awardDetailList = [] } = await Dispatch.repayment.getRepayCouponList({ querySceneList: ['3'] }) || {};
    const { usableQualConponList } = filterQualCoupons(awardDetailList) || {};
    const allCoupon = (usableQualConponList || []).filter((item) => item.awardType === '217');
    if (allCoupon && allCoupon.length > 0) {
      this.awardNo = (allCoupon[0] && allCoupon[0].awardNo) || '';
    }
    return {
      SSA01Coupon: allCoupon.filter((item) => item.subUseSceneCode === 'SSA01'),
      SSA02Coupon: allCoupon.filter((item) => item.subUseSceneCode === 'SSA02'),
    };
  }

  // 办理场景确认
  // 1重大事件（不涉及）
  // 2V+
  // 3微光卡
  // 4信用不负期待
  // 5逾期客户名单(客户名下有延后还资格券，且券场景=催收博弈场景)
  // 6风险名单(本次先不处理)
  // 7作业办理名单(客户名下有延后还资格券，且券场景=作业场景)
  // 99其他
  getApplyScene = (cdpData = {}, cifData = {}, couponList) => {
    const { userSheetMap = {}, tagMap: cdpTagMap = {} } = cdpData || {};
    const { tagMap: cifTagMap = {} } = cifData || {};
    const { SSA01Coupon = [], SSA02Coupon = [] } = couponList || {};
    let applyScene = '';
    if (cifTagMap && cifTagMap.HIGH_VALUE_CUST === 'H') {
      applyScene = '2'; // V+会员
    } else if (cdpTagMap && ['fdz_zsk', 'gx_zsk'].includes(cdpTagMap.wg_card_can_opn_typ) && cdpTagMap.lst_wg_card_exp_dt >= 0) {
      applyScene = '3'; // 微光卡
    } else if (userSheetMap && userSheetMap.l_fd_v9zk_zx && userSheetMap.l_fd_v9zk_zx.codeVal === 'Y') {
      applyScene = '4'; // 信用不负期待
    } else if (SSA02Coupon && SSA02Coupon.length > 0) {
      applyScene = '5'; // 逾期客户名单
    } else if (SSA01Coupon && SSA01Coupon.length > 0) {
      applyScene = '7'; // 作业办理名单
    }

    return applyScene || '99';
  }

  initConsultRepayInfo = async () => {
    // 获取再分期贷后服务建案信息
    const param = {
      serviceType: '002',
      applyScene: this.applyScene,
      interfaceVersion: '1.0'
    };
    // 查询建案信息； 查询全部账单，可再分期借据，不可再分期借据
    // eslint-disable-next-line no-unused-vars
    const result = await Dispatch.repayment.applyConsultRepayCase(param);
    const { data, ret, errCode, errMsg } = result || {};
    const {
      applyNo, applyStatus, applySourceType, contractApplyList, contractInfos, needFileList, extendPackageInfo, applyInfoDone, transitApplyCase, custTypeAndFileTypeList,
    } = data || {};
    const { extendPackageList, extendApplyInfo } = extendPackageInfo || {};
    const { extendMode, needRepayType, mergeOrderFlag, waiveInteFeeFlag, futureRepayPlanWaiveDetail } = extendPackageList && extendPackageList[0] || {};
    const { waiveRate } = futureRepayPlanWaiveDetail || {};
    // 提前存储waiveRate、合并办理标识，跳去问卷页需要传参
    this.waiveRate = waiveRate;
    this.mergeOrderFlag = mergeOrderFlag;
    // 停息停费和未来期打折正常不会同时满足，如果出现则引导去异常页（中台处理了，前端也兜底处理下）
    if (waiveInteFeeFlag === 'Y' && waiveRate > 0) {
      dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'RiskConfigError', beaconContent: { cus: { desc: '同时存在停费停费和未来期打折' } } });
      let urlParam = this.resultRedirectUrl ? `status=3&resultRedirectUrl=${this.resultRedirectUrl}` : 'status=3';
      Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
      return;
    }

    // 判断当前服务建案状态，根据状态分发到不同状态对应的页面或内容
    const applyStatusInfo = {
      applyStatus, applySourceType, applyNo, needFileList, contractApplyList, contractInfos, extendPackageList, extendApplyInfo, applyInfoDone, transitApplyCase, custTypeAndFileTypeList,
    };
    const applyStatusFlag = this.applyStatusDispatchHandler(applyStatusInfo, ret, errCode, errMsg);
    if (applyStatusFlag || ret !== '0') {
      return;
    }

    // 设置建案号
    this.applyNo = applyNo;
    // 设置合同信息
    this.contractApplyList = contractApplyList;
    this.contractInfos = contractInfos;
    // 设置待补充资料
    this.needFileList = needFileList;
    // 设置再分期模式
    this.extendMode = extendMode;
    // 设置再分期办理还款条件
    this.needRepayType = needRepayType;
    // 设置借据合并标记
    this.mergeOrderFlag = mergeOrderFlag;
    // 设置办理后是否停息停费标记
    this.waiveInteFeeFlag = waiveInteFeeFlag;
    // 设置未来期减免比例（减免比例大于0，说明是未来期打折）
    this.waiveRate = waiveRate;
    // 设置待补充身份和资料信息列表
    this.custTypeAndFileTypeList = custTypeAndFileTypeList;
    // 再分期试算
    const reInstallDispatchFlag = await this.initReInstallCalInfo(extendPackageList);
    // 判断完当前页面的状态之后展示主页面内容
    this.showPage = true;
    this.dispatchEntryRecord(); // 进入页面的特性埋点上报
    this.setState({
      extendPackageList,
    });

    /**
     * 注意push路由的时机：不停留在当前页的不添加路由
     * 错误案例：
     * redirectTo跳到失败页：关闭办理页去失败页；（但此时路由里还有一个办理页）；
     * 返回上一页：失败页重新会进入办理页，不符合办理规则；又进入失败页，导致退不出去死循环
     */
    if (process.env.TARO_ENV === 'h5' && !reInstallDispatchFlag) {
      Util.pushUrlState('keepState');
    }
  }

  // 查询全部账单，可再分期借据，不可再分期借据
  initAllBillsInfo = async () => {
    this.allBills = await Dispatch.repayment.getAllBills() || [];
    let isOverdueCust = false; // 是否为逾期客户

    this.allBills.advanceBillList && this.allBills.advanceBillList.length > 0 && this.allBills.advanceBillList.forEach((bill) => {
      if (bill.displayOverdueDays) {
        isOverdueCust = true;
      }
    });
    this.isOverdueCust = isOverdueCust; // 更新逾期客户字段
    setStore({ isOverdueCust });
  }

  getExtraParam = (extendPackageList, initialMaxExtendPeriod = '') => {
    const { selectedCnt, selectedPreCnt } = this.state;
    const { expectRepayAmtPercent } = extendPackageList && extendPackageList[0] || {};
    const orderNoList = (this.orderInfoListCanExtend || []).map((orderItem) => orderItem.orderNo);
    const needPayOrderNoList = orderNoList.concat((this.originalOrderInfoListCanNotExtend || []).map((orderItem) => orderItem.orderNo));
    let param = {};
    if (this.mergeOrderFlag === 'Y') {
      if (!this.extendModeAndScene && !selectedCnt) {
        // 多笔合一：第一、第二次试算（必有）
        if (!initialMaxExtendPeriod) {
          // 第一次试算
          param = {
            orderNoList: this.needRepayType === '1' ? orderNoList : needPayOrderNoList,
            extendPackageList: extendPackageList,
            firstPhasePeriods: null, // 第一阶段总期数（选择的前N期数）
            extendInstallTotalCnt: null, // 展期合并新借据总期数（选择的分期数）
            expectRepayAmtPercent: '100', // 第一段期供比例（计算后的前N期月还款比例）
          };
        } else {
          // 返回了最大期限，说明是第二次试算，自动填充最大值
          param = {
            orderNoList: this.needRepayType === '1' ? orderNoList : needPayOrderNoList,
            extendPackageList: extendPackageList,
            firstPhasePeriods: (Number(initialMaxExtendPeriod) > 1 ? `${initialMaxExtendPeriod - 1}` : '0'), // 第一阶段总期数（N-1）
            extendInstallTotalCnt: initialMaxExtendPeriod, // 展期合并新借据总期数（取最大值）
            expectRepayAmtPercent: expectRepayAmtPercent, // 第一段期供比例（取最大比例）
          };
          // 记录第一段期供比例，默认取最大值
          this.expectRepayAmtPercent = expectRepayAmtPercent;
        }
      } else if (this.extendModeAndScene && selectedCnt && selectedPreCnt) {
        // 多笔合一：第N次试算（或有）
        param = {
          orderNoList: this.needRepayType === '1' ? orderNoList : needPayOrderNoList,
          extendPackageList: extendPackageList,
          firstPhasePeriods: selectedPreCnt, // 第一阶段总期数（选择的前N期数）
          extendInstallTotalCnt: selectedCnt, // 展期合并新借据总期数（选择的分期数）
          expectRepayAmtPercent: this.expectRepayAmtPercent, // 第一段期供比例（计算后的前N期月还款比例）
        };
      }
    } else {
      // 逐笔办理needRepayType=2时，期供比例改为100时，联合贷借据会由不可展变为可展，导致试算报错，沟通后前端不传orderNoList
      if (!this.extendModeAndScene) {
        // 逐笔办理：第一次试算
        param = {
          orderNoList: this.needRepayType === '1' ? orderNoList : null,
          extendPackageList: extendPackageList,
          expectRepayAmtPercent: expectRepayAmtPercent || null, // 第一段期供比例（计算后的前N期月还款比例）
        };
      } else {
        // 逐笔办理：第N次试算
        param = {
          orderNoList: this.needRepayType === '1' ? orderNoList : null,
          extendPackageList: extendPackageList,
          expectRepayAmtPercent: this.expectRepayAmtPercent || null, // 第一段期供比例（计算后的前N期月还款比例）
        };
      }
    }
    return param;
  }

  // 再分期试算
  initReInstallCalInfo = async (extendPackageList = [], initialMaxExtendPeriod = '') => {
    let dispatchFlag = false;
    const { selectedCnt, selectedPreCnt } = this.state;
    const param = this.getExtraParam(extendPackageList, initialMaxExtendPeriod);
    const { ret, errCode, errMsg, data } = await Dispatch.repayment.queryReinstallRepayInfo(param) || {};
    if (ret === '0') {
      const { extendCalInfoList = [] } = data || {};
      const selectExtendCalInfo = extendCalInfoList && extendCalInfoList[0] || {}; // 默认选中第一个
      const {
        orderInfoListCanExtend,
        orderInfoListCanNotExtend,
        maxExtendPeriod,
        repayPlanAfterExtend,
        orderInfoListAfterMerge,
      } = selectExtendCalInfo || {};

      this.orderInfoListCanExtend = orderInfoListCanExtend;
      this.orderInfoListAfterMerge = orderInfoListAfterMerge;

      setStore({ // 将可展期借据明细、合并后新借据明细 全部合起来
        allOrderInfoList: orderInfoListCanExtend.concat(orderInfoListAfterMerge || []) || []
      });

      // 多笔合一：选择的分期数为空，且第一次试算的返回分期数，则期数M为最大展期期数；前N期数为M-1；前N期期供比例为100，发起第二次试算
      if (this.mergeOrderFlag === 'Y' && !selectedCnt && maxExtendPeriod && !initialMaxExtendPeriod) {
        // 多笔合一，存下第一次试算返回的原始数据
        this.originalOrderInfoListCanExtend = orderInfoListCanExtend;
        this.originalOrderInfoListCanNotExtend = orderInfoListCanNotExtend;
        await this.initReInstallCalInfo(extendPackageList, maxExtendPeriod);
        return;
      }

      if (!this.showPage) {
        if (this.mergeOrderFlag === 'N') {
          // 逐笔办理，存下第一次试算返回的原始数据
          this.originalOrderInfoListCanExtend = orderInfoListCanExtend;
          this.originalOrderInfoListCanNotExtend = orderInfoListCanNotExtend;
        }
        // 初始化模式场景（后续不再改变）
        this.initExtendModeAndScene(extendPackageList, selectExtendCalInfo);
        // 初始slider传参marks（后续不再改变）
        this.getSliderMarks(selectExtendCalInfo);
      }

      // 逐笔办理needRepayType=2时，期供比例改为100时，联合贷借据会由不可展变为可展，现试算不传借据号，所以可展与不可展借据取实时返回的。一堆兼容。。。
      if (this.mergeOrderFlag === 'N' && this.needRepayType === '2') {
        this.originalOrderInfoListCanExtend = orderInfoListCanExtend;
        this.originalOrderInfoListCanNotExtend = orderInfoListCanNotExtend;
      }

      // 初始化分期数
      this.getAdjustCntList(selectExtendCalInfo);
      this.setState({
        selectedCnt: selectedCnt || maxExtendPeriod,
        selectedPreCnt: selectedPreCnt || (Number(maxExtendPeriod) > 1 ? `${maxExtendPeriod - 1}` : '0'),
        selectExtendCalInfo,
        repayPlanAfterExtend,
      });
    } else {
      let urlParam = 'status=3';
      if (errCode === 'UMDP02724') { // UMDP02724 不符合办理条件；需前端特殊处理展示
        urlParam = `${urlParam}&subStatus=1`;
      } else if (errMsg) {
        urlParam = `${urlParam}&subStatus=1&errMsg=${errMsg}`;
      }
      dispatchFlag = true;
      Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
      return dispatchFlag;
    }
    return dispatchFlag;
  }

  initExtendModeAndScene = (extendPackageList, selectExtendCalInfo) => {
    const { maxExtendAmt, minExtendAmt } = selectExtendCalInfo || {};
    const modeAndSceneDescMap = {
      11: '多笔合一：金额上下限一致',
      12: '多笔合一：选择多笔/单笔',
      21: '逐笔办理：金额上下限一致',
      22: '逐笔办理：金额上下限不一致',
    };

    // 多笔合一模式
    if (this.mergeOrderFlag === 'Y') {
      if (maxExtendAmt === minExtendAmt) {
        this.extendModeAndScene = '11'; // 金额上下限一致
      } else {
        this.extendModeAndScene = '12'; // 选择多笔/选择单笔
      }
    } else {
      if (maxExtendAmt === minExtendAmt) {
        this.extendModeAndScene = '21'; // 金额上下限一致
      } else {
        this.extendModeAndScene = '22'; // 金额上下限不一致
      }
    }
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ExtendModeAndScene', beaconContent: { cus: { extendModeAndSceneDesc: modeAndSceneDescMap[this.extendModeAndScene] } } });
  }

  // 进入页面上报再分期页面打开埋点，带特性值
  dispatchEntryRecord() {
    dispatchTrackEvent({
      target: this,
      event: EventTypes.PO,
      beaconId: 'ReinstallmentPageShow',
      beaconContent: { cus: { mergeOrderFlag: this.mergeOrderFlag, waiveInteFeeFlag: this.waiveInteFeeFlag, isOverdueCust: this.isOverdueCust } }
    });
  }

  // 根据不同的建案状态跳转到不同的页面
  applyStatusDispatchHandler(applyStatusInfo, ret, errCode, errMsg) {
    // 如果dispacthFlag为true则中断当前页面渲染
    let dispatchFlag = false;
    let {
      applyStatus, applyNo, contractApplyList, contractInfos, extendApplyInfo, needFileList, applyInfoDone, transitApplyCase, custTypeAndFileTypeList,
    } = applyStatusInfo || {};
    const { orderNoList, extendPackage, extendInstallTotalCnt, firstPhasePeriods, expectRepayAmtPercent } = extendApplyInfo || {};
    // 存储合同信息，解决小程序，从问卷回来后办理再分期跳转资料授权无合同问题
    setStore({ contractApplyList, contractInfos });
    // 在途服务案件拒绝
    const {
      applyNo: originApplyNo, canClose
    } = transitApplyCase || {};
    if (originApplyNo && canClose === 'Y') {
      dispatchFlag = true;
      setStore({ transitApplyCase });
      Madp.redirectTo({
        url: `/pages/service-pending/index?serviceType=advanced-stage&repaymentFlag=${this.repaymentFlag}`,
      });
      return dispatchFlag;
    }

    // 接口报错，用于兜底逻辑处理，正常场景不会触发该场景
    if (ret !== '0') {
      dispatchFlag = true;
      let urlParam = this.resultRedirectUrl ? `status=3&resultRedirectUrl=${this.resultRedirectUrl}` : 'status=3';
      // 准入拒绝使用返回码方式
      if (errCode === 'UMDP02724') { // UMDP02724 不符合办理条件；需前端特殊处理展示
        urlParam = `${urlParam}&subStatus=1`;
      } else if (errMsg) {
        urlParam = `${urlParam}&subStatus=1&errMsg=${errMsg}`;
      }
      Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
      return dispatchFlag;
    }

    let needManualKyc = false; // 标识是否需要人工kyc
    if (applyStatus === '1') {
      needFileList && needFileList.forEach((item) => {
        // 需要自助kyc
        if (item.fileType === 'K01' && this.resultId === '') {
          dispatchFlag = true;
          // 存下文件类型和问券ID，后面提交案件时需要
          const advancedStageIApplyMaterial = {
            applyNo,
            fileType: item.fileType,
            questionnaireId: item.questionnaireId,
          };
          setStore({ advancedStageIApplyMaterial });
          dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'NeedSelfKyc', beaconContent: { cus: {} } });
          const redirectUrl = getCurrentPageUrlWithArgs();
          // 缓存标识是否已做kyc，用于解决iPhone13在问卷填写页面点击返回时无法回到还款首页（直接退出）的问题
          Madp.setStorageSync('doKycExit', 'Y', 'SESSION');
          Madp.redirectTo({
            url: `${urlDomain}/${Madp.getChannel()}/survey/#/pages/questionnaire/index?surveySerialNo=${item.questionnaireId}&useTitle=再分期办理问卷&needNav=1&redirectUrl=${encodeURIComponent(redirectUrl)}&benefitPoint=${Number(this.waiveRate) > 0 ? this.waiveRate : ''}&busiType=${this.mergeOrderFlag === 'Y' ? '010' : '011'}&isOverdue=${this.isOverdueCust ? 'Y' : 'N'}&repaymentFlag=${this.repaymentFlag}`
          });
        }
      });

      // 已经提交过案件服务信息，但未完成补充资料
      if (applyInfoDone === 'Y') {
        dispatchFlag = true;
        const advancedStageInfo = {
          applyNo: this.applyNo,
          needFileList,
          custTypeAndFileTypeList,
        };
        setStore({ advancedStageInfo, contractApplyList, contractInfos });
        const urlParam = `applyNo=${applyNo}&serviceType=advanced-stage&applyInfoDone=1&repaymentFlag=${this.repaymentFlag}`;
        Madp.setStorageSync('ReinstallmentResultRedirectUrl', this.resultRedirectUrl, 'LOCAL');
        if (this.miniChannelFlag) {
          Taro.redirectTo({
            url: `/pages/identity/information?${urlParam}&repaymentFlag=${this.repaymentFlag}`
          });
        } else {
          Madp.redirectTo({ url: `/pages/identity/information?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
        }
      }
    } else if (applyStatus === '3') { // 跳转再分期审核页
      dispatchFlag = true;
      const urlParam = this.resultRedirectUrl ? `status=2&resultRedirectUrl=${this.resultRedirectUrl}` : 'status=2';
      Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
    } else if (applyStatus === '4') { // 待完成补充待办，跳转资料补充页
      dispatchFlag = true;
      const advancedStageInfo = {
        applyNo: this.applyNo,
        needFileList,
        custTypeAndFileTypeList,
      };
      setStore({ advancedStageInfo, contractApplyList, contractInfos });
      const urlParam = `applyNo=${applyNo}&serviceType=advanced-stage&oldMode=1`;
      Madp.setStorageSync('ReinstallmentResultRedirectUrl', this.resultRedirectUrl, 'LOCAL');
      if (this.miniChannelFlag) {
        Taro.redirectTo({
          url: `/pages/identity/information?${urlParam}&repaymentFlag=${this.repaymentFlag}`
        });
      } else {
        Madp.redirectTo({ url: `/pages/identity/information?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
      }
    } else if (applyStatus === '5') { // 跳转至分期还审核页（需展示人工kyc差异文案）
      needFileList && needFileList.forEach((item) => {
        // 需要人工kyc（仅做记录，用于结果页展示）
        if (item.fileType === 'K02') {
          needManualKyc = true;
          dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'NeedManualKyc', beaconContent: { cus: {} } });
        }
      });
      dispatchFlag = true;
      const urlParam = this.resultRedirectUrl ? `status=2${needManualKyc ? '&subStatus=1' : ''}&resultRedirectUrl=${this.resultRedirectUrl}` : `status=2${needManualKyc ? '&subStatus=1' : ''}`;
      Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
    } else if (applyStatus === '8') { // 跳转至分期还审核页
      dispatchFlag = true;
      const urlParam = this.resultRedirectUrl ? `status=2&subStatus=2&resultRedirectUrl=${this.resultRedirectUrl}` : 'status=2&subStatus=2';
      Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
    } else if (applyStatus === '9') { // 跳转至再分期成功页
      dispatchFlag = true;
      const urlParam = this.resultRedirectUrl ? `status=1&resultRedirectUrl=${this.resultRedirectUrl}` : 'status=1';
      Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
    } else if (applyStatus === '6') { // 返回applyStatus 6--风控审核拒绝
      dispatchFlag = true;
      const urlParam = this.resultRedirectUrl ? `status=3&subStatus=2&resultRedirectUrl=${this.resultRedirectUrl}` : 'status=3&subStatus=2';
      Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}` });
    } else if (applyStatus === '10') { // 10--审核通过且业务办理失败
      dispatchFlag = true;
      const urlParam = this.resultRedirectUrl ? `status=3&subStatus=3&resultRedirectUrl=${this.resultRedirectUrl}` : 'status=3&subStatus=3';
      Madp.redirectTo({ url: `/pages/bill-advanced-stage/result?${urlParam}&repaymentFlag=${this.repaymentFlag}&repaymentFlag=${this.repaymentFlag}` });
    } else if (applyStatus === '7') { // 7--审核通过待业务提交，跳转去还款确认页
      dispatchFlag = true;
      setStore({
        selectedBillList: orderNoList,
        advancedStageInfo: {
          applyNo,
          extendPackageInfo: extendPackage,
          extendInstallTotalCnt,
          firstPhasePeriods,
          expectRepayAmtPercent
        }
      });
      Madp.redirectTo({
        url: `/pages/bill-advanced-stage/confirm?applyNo=${this.applyNo}`,
      });
    }
    return dispatchFlag;
  }

  getSubmitExtraParam = () => {
    const { extendPackageList, selectedCnt, selectedPreCnt } = this.state;
    const { expectRepayAmtPercent } = extendPackageList && extendPackageList[0] || {};
    if (this.extendModeAndScene === '21') {
      return {};
    } else if (this.extendModeAndScene === '22') {
      return {
        expectRepayAmtPercent: this.expectRepayAmtPercent || expectRepayAmtPercent,
      };
    } else {
      return {
        firstPhasePeriods: selectedPreCnt, // 第一阶段总期数（选择的前N期数）
        extendInstallTotalCnt: selectedCnt, // 展期合并新借据总期数（选择的分期数）
        expectRepayAmtPercent: this.expectRepayAmtPercent, // 第一段期供比例（计算后的前N期月还款比例）
      };
    }
  }

  // 确认办理分期还
  confirmReinstallmentHandler = async () => {
    const { isCheckedContract, extendPackageList } = this.state;
    // 判断当前是否存在合同且已经勾选合同
    if (!isCheckedContract && (this.contractApplyList && this.contractApplyList.length > 0 || this.contractInfos && this.contractInfos.length > 0)) {
      Madp.showToast({ title: '请阅读并同意协议', icon: 'none' });
      return;
    }
    const orderNoList = (this.orderInfoListCanExtend || []).map((orderItem) => orderItem.orderNo);
    const needPayOrderNoList = orderNoList.concat((this.originalOrderInfoListCanNotExtend || []).map((orderItem) => orderItem.orderNo));
    setStore({
      selectedBillList: this.needRepayType === '1' ? orderNoList : needPayOrderNoList,
      advancedStageInfo: {
        applyNo: this.applyNo,
        extendPackageInfo: extendPackageList[0],
        awardNo: this.awardNo,
        needFileList: this.needFileList,
        custTypeAndFileTypeList: this.custTypeAndFileTypeList,
        ...this.getSubmitExtraParam(),
      }
    });
    const { authInfoDetails } = await Dispatch.repayment.checkSupplyInfo('extend_repay');
    const isNeedSupplyID = !!authInfoDetails && authInfoDetails.filter((process) => process.authParamType === 'COMPENSATE_ID_INFO').length;
    const params = { billType: this.billType, applyNo: this.applyNo };
    Madp.setStorageSync('ReinstallmentResultRedirectUrl', this.resultRedirectUrl, 'LOCAL');
    // 记录从再分期办理按钮离开的场景
    Madp.setStorageSync('ReinstallmentConfirmBtnLeave', 'Y', 'SESSION');
    if (isNeedSupplyID) {
      const supplyParams = {
        scene: 'SCENE_SXF',
        billType: 'advanced-stage',
        applyNo: this.applyNo,
      };
      Util.gotoSupplyInfo(supplyParams);
    } else {
      // 不做身份证做补充联系人
      if (this.miniChannelFlag) {
        Taro.redirectTo({
          url: `/pages/identity/contact?billType=${this.billType}&applyNo=${this.applyNo}&repaymentFlag=${this.repaymentFlag}`
        });
      } else {
        Util.router.replace({
          path: `/pages/identity/contact?repaymentFlag=${this.repaymentFlag}`,
          query: params
        });
      }
    }
  }

  // 修改借据信息
  changeLoanInfo = () => {
    this.OriginalIousList.show();
    dispatchTrackEvent({ target: this, event: EventTypes.EV, beaconId: 'ShowLoanInfoDrawer' });
  }

  jumpReinstallment = () => {
    Util.router.push('/pages/bill-advanced-stage/intro');
  }

  // 获取分期数的选择范围
  getAdjustCntList = (selectExtendCalInfo) => {
    const {
      maxExtendPeriod, // 最大展期期限
      minExtendPeriod, // 最小展期期限
    } = selectExtendCalInfo || {};
    const adjustCntList = [];
    for (let i = Number(maxExtendPeriod); i >= Number(minExtendPeriod); i--) {
      adjustCntList.push(i.toString());
    }
    this.adjustCntList = adjustCntList;
    return adjustCntList;
  }

  // 获取前N期的选择范围
  getAdjustPreCntList = (selectedCnt) => {
    const adjustPreCntList = [];
    for (let i = selectedCnt - 1; i >= 1; i--) {
      adjustPreCntList.push(i.toString());
    }
    this.adjustPreCntList = adjustPreCntList;
    return adjustPreCntList;
  }

  /**
   * 展示期数选择picker
   * @param {*} type
   * type = 1 为选择分期数
   * type = 2 为选择前N期数
   */
  showPeriodPicker = (type) => {
    const { selectExtendCalInfo, selectedCnt } = this.state;
    this.pickerType = type;
    const cntList = type === '1' ? this.getAdjustCntList(selectExtendCalInfo) : this.getAdjustPreCntList(selectedCnt);
    const options = (cntList || []).map((item) => {
      return {
        text: `${item}期`,
        value: item
      };
    });
    this.setState({
      showPeriodPicker: true,
      pickerOptions: options,
    });
  }

  onChoosePeridConfirm = (index) => {
    const { selectedCnt, selectedPreCnt, extendPackageList, minExtendAmt } = this.state;
    const { expectRepayAmtPercent } = extendPackageList && extendPackageList[0] || {};

    // 重复选择同一期数，直接关闭
    if ((this.pickerType === '1' && this.adjustCntList[index] === selectedCnt) || (this.pickerType === '2' && this.adjustPreCntList[index] === selectedPreCnt)) {
      this.setState({
        showPeriodPicker: false,
      });
      return;
    }
    if (this.pickerType === '1') {
      // 选择分期数后，要重新设置前N期的期数范围和默认值（分期数减1），以及重置月还款为最小值，比例为最小期供比例，输入计数器的值重置为0
      this.getAdjustPreCntList(this.adjustCntList[index]);
      this.setState({
        showPeriodPicker: false,
        selectedCnt: this.adjustCntList[index],
        selectedPreCnt: this.adjustPreCntList[0],
        setExpectTotalRepayAmt: minExtendAmt,
        numberInputValue: 0,
      }, async () => {
        this.expectRepayAmtPercent = expectRepayAmtPercent;
        await this.initReInstallCalInfo(extendPackageList);
      });
    } else {
      this.setState({
        showPeriodPicker: false,
        selectedPreCnt: this.adjustPreCntList[index],
      }, async () => {
        await this.initReInstallCalInfo(extendPackageList);
      });
    }
  }

  onChoosePeridCancel = () => {
    this.pickerType = '';
    this.setState({
      showPeriodPicker: false,
    });
  }

  doSubtraction = async () => {
    const { selectExtendCalInfo, extendPackageList, numberInputValue } = this.state;
    const newNumberInputValue = numberInputValue - 1;

    // 超出范围，抖动提示
    if (newNumberInputValue < 0) {
      this.numberInputRef && this.numberInputRef.shake();
      return;
    }

    const {
      maxExtendAmt, // 最大展期期供金额
      minExtendAmt, // 最小展期期供金额
    } = selectExtendCalInfo || {};

    // 计算出每次递减步长
    const step = ((maxExtendAmt - minExtendAmt) / 10);
    let newExpectTotalRepayAmt = '';
    // 步数小于1时四舍五入保留2位小数，否则向上取整
    newExpectTotalRepayAmt = step < 1 ? Util.floatAdd(Number(minExtendAmt), Number(newNumberInputValue * step)).toFixed(2) : Math.ceil(Util.floatAdd(Number(minExtendAmt), Number(newNumberInputValue * step)));

    // 校正：如果最终月还款接近最小期供，但因为误差实际与最小期供不相等，则自动校正为最小期供
    if (Math.abs(newExpectTotalRepayAmt - minExtendAmt) < ((maxExtendAmt - minExtendAmt) * 0.05)) {
      newExpectTotalRepayAmt = minExtendAmt;
    }

    // 兜底校正边界金额
    if (Number(newExpectTotalRepayAmt) > Number(maxExtendAmt)) {
      newExpectTotalRepayAmt = maxExtendAmt;
    } else if (Number(newExpectTotalRepayAmt) < Number(minExtendAmt)) {
      newExpectTotalRepayAmt = minExtendAmt;
    }

    // 更新计数器值
    this.setState({
      setExpectTotalRepayAmt: newExpectTotalRepayAmt,
      numberInputValue: newNumberInputValue,
    });

    // 计算第一段期期供比例（不做舍入，直接取小数点后四位）
    this.expectRepayAmtPercent = (Math.floor(((newExpectTotalRepayAmt / maxExtendAmt) * Math.pow(10, 6))) / Math.pow(10, 4)).toFixed(4);

    // 调试算接口
    await this.initReInstallCalInfo(extendPackageList);
  }

  doAddition = async () => {
    const { selectExtendCalInfo, extendPackageList, numberInputValue } = this.state;
    const newNumberInputValue = numberInputValue + 1;

    // 超出范围，抖动提示
    if (newNumberInputValue > 10) {
      this.numberInputRef && this.numberInputRef.shake();
      return;
    }
    const {
      maxExtendAmt, // 最大展期期供金额
      minExtendAmt, // 最小展期期供金额
    } = selectExtendCalInfo || {};

    // 计算出每次递增步长
    const step = ((maxExtendAmt - minExtendAmt) / 10);
    let newExpectTotalRepayAmt = '';
    // 步数小于1时四舍五入保留2位小数，否则向上取整
    newExpectTotalRepayAmt = step < 1 ? Util.floatAdd(Number(minExtendAmt), Number(newNumberInputValue * step)).toFixed(2) : Math.ceil(Util.floatAdd(Number(minExtendAmt), Number(newNumberInputValue * step)));

    // 校正：如果最终月还款接近最大期供，但因为误差实际与最大期供不相等，则自动校正为最大期供
    if (Math.abs(maxExtendAmt - newExpectTotalRepayAmt) < ((maxExtendAmt - minExtendAmt) * 0.05)) {
      newExpectTotalRepayAmt = maxExtendAmt;
    }

    // 兜底校正边界金额
    if (Number(newExpectTotalRepayAmt) > Number(maxExtendAmt)) {
      newExpectTotalRepayAmt = maxExtendAmt;
    } else if (Number(newExpectTotalRepayAmt) < Number(minExtendAmt)) {
      newExpectTotalRepayAmt = minExtendAmt;
    }

    // 更新计数器值
    this.setState({
      setExpectTotalRepayAmt: newExpectTotalRepayAmt,
      numberInputValue: newNumberInputValue,
    });

    // 计算第一段期期供比例（不做舍入，直接取小数点后四位）
    this.expectRepayAmtPercent = (Math.floor(((newExpectTotalRepayAmt / maxExtendAmt) * Math.pow(10, 6))) / Math.pow(10, 4)).toFixed(4);

    // 调试算接口
    await this.initReInstallCalInfo(extendPackageList);
  }

  getSliderMarks = (selectExtendCalInfo) => {
    const {
      minExtendAmt, // 最大展期期限
      maxExtendAmt, // 最小展期期限
    } = selectExtendCalInfo || {};
    this.originalMaxExtendAmt = maxExtendAmt;
    this.originalMinExtendAmt = minExtendAmt;
    const sliderMarks = [];
    for (let i = 0; i <= 10; i++) {
      if (i === 0) {
        sliderMarks.push({
          value: i,
          label: `${minExtendAmt}元`
        });
      } else if (i === 10) {
        sliderMarks.push({
          value: i,
          label: `${maxExtendAmt}元`
        });
      } else {
        sliderMarks.push({
          value: i,
          label: ''
        });
      }
    }
    this.sliderMarks = sliderMarks;
  }

  onSliderChange = async (value) => {
    const { sliderValue, extendPackageList } = this.state;
    if (value === sliderValue) {
      // 相等时，不做后面试算。但必须重新setState，不然slide不会自动吸附
      this.setState({
        sliderValue: value,
      });
      return;
    }

    // 使用第一次有效试算返回的期供金额，否则比例为100时，返回的期供上下限金额都为0
    const { originalMaxExtendAmt, originalMinExtendAmt } = this;

    // 计算出每次递增步长
    const step = ((originalMaxExtendAmt - originalMinExtendAmt) / 10);
    let newExpectTotalRepayAmt = originalMinExtendAmt;
    if (value === 0) {
      newExpectTotalRepayAmt = originalMinExtendAmt;
    } else if (value === 10) {
      newExpectTotalRepayAmt = originalMaxExtendAmt;
    } else {
      // 步数小于1时四舍五入保留2位小数，否则向上取整
      newExpectTotalRepayAmt = step < 1 ? Util.floatAdd(Number(originalMinExtendAmt), Number(step * value)).toFixed(2) : Math.ceil(Util.floatAdd(Number(originalMinExtendAmt), Number(step * value)));
    }

    if (Math.abs(originalMaxExtendAmt - newExpectTotalRepayAmt) <= ((originalMaxExtendAmt - originalMinExtendAmt) * 0.05)) {
      // 校正：如果最终月还款接近最大期供，但因为误差实际与最大期供不相等（理论上不会有这个误差），则自动校正为最大期供
      newExpectTotalRepayAmt = originalMaxExtendAmt;
    } else if (Math.abs(newExpectTotalRepayAmt - originalMinExtendAmt) <= ((originalMaxExtendAmt - originalMinExtendAmt) * 0.05)) {
      // 校正：如果最终月还款接近最小期供，但因为误差实际与最小期供不相等（理论上不会有这个误差），则自动校正为最小期供
      newExpectTotalRepayAmt = originalMinExtendAmt;
    }

    // 兜底校正边界金额
    if (Number(newExpectTotalRepayAmt) > Number(originalMaxExtendAmt)) {
      newExpectTotalRepayAmt = originalMaxExtendAmt;
    } else if (Number(newExpectTotalRepayAmt) < Number(originalMinExtendAmt)) {
      newExpectTotalRepayAmt = originalMinExtendAmt;
    }

    this.setState({
      sliderValue: value,
      setExpectTotalRepayAmt: newExpectTotalRepayAmt,
    });

    // 计算第一段期期供比例（不做舍入，直接取小数点后四位）
    this.expectRepayAmtPercent = (Math.floor(((newExpectTotalRepayAmt / originalMaxExtendAmt) * Math.pow(10, 6))) / Math.pow(10, 4)).toFixed(4);

    // 调试算接口
    await this.initReInstallCalInfo(extendPackageList);
  }

  /**
   * 组装弹窗内容
   * @param { 弹窗类型 } type
   * 0：还款金额说明
   * 1：利率说明
   * 2：还款方式说明（等额还款和前期少还，到期结清）
   * 3：专享优惠说明
   */
  showModal = (type) => {
    let commonModalInfo = {};
    if (type === 0) {
      const { selectExtendCalInfo } = this.state;
      const { duePayTotalAmt, duePayPrincipalAmt, dueTotalInteFeeWaiveAmt } = selectExtendCalInfo || {};
      // 总需还，包括可展和非可展
      let needPayTotalAmt = 0;
      if (duePayTotalAmt && Number(duePayTotalAmt) > 0) {
        needPayTotalAmt = Number(duePayTotalAmt);
      }
      let canExtendDuePayTotalAmt = 0; // 可展总需还
      let canExtendDuePayPrincipalAmt = 0; // 可展总需还本金
      (this.orderInfoListCanExtend || []).forEach((item) => {
        canExtendDuePayTotalAmt = Util.floatAdd(canExtendDuePayTotalAmt, Number((item || {}).duePayTotalAmt || 0));
        /* eslint-disable-next-line */
        canExtendDuePayPrincipalAmt = Util.floatAdd(canExtendDuePayPrincipalAmt, Number((item || {}).duePayPrincipalAmt || 0));
      });
      const payAmtExplainContent = [
        [`办理前需还款总金额为${needPayTotalAmt.toFixed(2)}元`],
        ['共包含：'],
        [`办理的借据本金${canExtendDuePayPrincipalAmt.toFixed(2)}元，息费${Util.floatMinus(canExtendDuePayTotalAmt, canExtendDuePayPrincipalAmt).toFixed(2)}元`],
      ];
      if (Util.floatMinus(duePayPrincipalAmt, canExtendDuePayPrincipalAmt) > 0) {
        payAmtExplainContent.push([`未办理的借据本金${Util.floatMinus(duePayPrincipalAmt, canExtendDuePayPrincipalAmt).toFixed(2)}元，息费${Util.floatMinus(Util.floatMinus(needPayTotalAmt, canExtendDuePayTotalAmt), Util.floatMinus(duePayPrincipalAmt, canExtendDuePayPrincipalAmt)).toFixed(2)}元`]);
      }
      if (dueTotalInteFeeWaiveAmt && Number(dueTotalInteFeeWaiveAmt) > 0) {
        payAmtExplainContent.push([`另外，本次共为您减免${dueTotalInteFeeWaiveAmt}元息费`]);
      }

      commonModalInfo = {
        title: '还款金额说明',
        beaconId: 'PayAmtExplainDialog',
        content: payAmtExplainContent,
      };
    } else if (type === 1) {
      commonModalInfo = {
        title: '利率说明',
        beaconId: 'InterestExplainDialog',
        content: [
          ['办理业务的借据会合并为一笔新的再分期借据', '，以其中年利率最低的借据价格（若使用借款优惠券，则为使用券前的价格），作为新再分期借据的年利率。'],
          ['若存在更低的价格，是办理时为您提供的优惠价格，若逾期优惠会立即失效']
        ],
      };
    } else if (type === 2) {
      const { extensionPrincipalCode } = this.orderInfoListAfterMerge && this.orderInfoListAfterMerge[0] || {};
      // 等额还款
      if (extensionPrincipalCode === 'PS010') {
        commonModalInfo = {
          title: '还款方式说明',
          subTitle: '等额还款',
          beaconId: 'RepayWayExplainDialog',
          content: [
            ['每月等额还本付息']
          ],
        };
      } else {
        commonModalInfo = {
          title: '还款方式说明',
          subTitle: '前期少还 到期结清',
          beaconId: 'RepayWayExplainDialog',
          content: [
            ['前期月还款金额较少，后期每月还款金额较多，直至结清']
          ],
        };
      }
    } else if (type === 3) {
      const { selectExtendCalInfo } = this.state;
      const { surplusTotalinteFeeWaiveAmt } = selectExtendCalInfo || {};
      // 逐步办理未来期打折-专项优惠说明
      if (['21', '22'].includes(this.extendModeAndScene) && Number(this.waiveRate) > 0 && Number(surplusTotalinteFeeWaiveAmt) > 0) {
        commonModalInfo = {
          title: '专享优惠说明',
          beaconId: 'DiscountExplainDialog',
          content: [
            ['仅办理的借据可享受息费减免，办理页面上未来还款计划，已按照优惠后的计划进行展示，具体内容可自行查看。'],
            ['该优惠逾期后会失效，请按时还款，避免逾期。']
          ],
        };
      } else {
        commonModalInfo = {
          title: '专享优惠说明',
          subTitle: '还款时，可减免全部息费',
          subTitleColor: '#FF8844',
          beaconId: 'DiscountExplainDialog',
          content: [
            ['该优惠仅限于您本次办理业务的相关借据，在操作还款时，可减免当次还款所涉及的全部息费。'],
            ['未办理借据无法享受该项优惠。']
          ],
        };
      }
    }

    this.setState({
      showCommonModal: true,
      commonModalInfo,
    });
  }

  render() {
    const {
      repayPlanAfterExtend, selectExtendCalInfo, showPeriodPickeSlider,
      isReInstallmentPlanDrawerOpen, showPeriodPicker, showCommonModal, commonModalInfo,
      selectedCnt, selectedPreCnt, pickerOptions, sliderValue, setExpectTotalRepayAmt
    } = this.state || {};

    const { extendModeAndScene, adjustCntList, waiveInteFeeFlag, waiveRate } = this;

    const { title: commonModalTitle, subTitle: commonModalSubTitle, subTitleColor: commonModalSubTitleColor, beaconId: commonModalBeaconId, content: commonModalContent } = commonModalInfo || {};

    const {
      maxExtendAmt, // 最大展期期供金额
      minExtendAmt, // 最小展期期供金额
      secondPhaseRepayAmt, // 第二阶段期供金额
      duePayTotalAmt, // 到期应还还总金额
      extensionTotalPrincipalAmt, // 总展期本金
      dueTotalInteFeeWaiveAmt, // 到期息费减免总额
      surplusTotalinteFeeWaiveAmt, // 展期试算后还款计划表待还总息费优惠金额
      surplusTotalPayInteAmtAfterExtend, // 展期试算后还款计划表待还总息费
      originFirstPhaseRepayAmt, // 可展借据原始首月还款期供
      extendFirstPhaseRepayAmt, // 可展借据展期后首月还款期供
      stopFeeOrderNum, // 停息停费借据数
    } = selectExtendCalInfo || {};

    // 展期后年化利率、展期后折扣年化利率
    const { extensionYearRate, extensionWaivedYearRate, extensionPrincipalType } = this.mergeOrderFlag === 'Y' ? (this.orderInfoListAfterMerge && this.orderInfoListAfterMerge[0] || {}) : (this.orderInfoListCanExtend && this.orderInfoListCanExtend[0] || {});
    // 展期后首期还款日和金额
    const { payDate, surplusPayTotalAmt, stopFeeAmt } = repayPlanAfterExtend && repayPlanAfterExtend[0] || {};
    // 比办理前降低金额
    const reduceAmt = (Number(originFirstPhaseRepayAmt || 0) - Number(setExpectTotalRepayAmt || minExtendAmt || 0)).toFixed(2);

    // 总需还，包括可展和非可展
    let needPayTotalAmt = 0;
    if (duePayTotalAmt && Number(duePayTotalAmt) > 0) {
      needPayTotalAmt = Number(duePayTotalAmt);
    }

    // 办理页不展示贷后资料授权书
    const contractApplyList = (this.contractApplyList && this.contractApplyList.filter((item) => item.contractType !== 'POST_INFO_AUTH')) || [];
    const contractInfoList = (this.contractInfos && this.contractInfos.filter((item) => item.contractCode !== 'GRXXSQ_DHZLSQ')) || [];

    return this.showPage ? (
      <MUView className="re-installment">
        <MUView className="re-installment-amount">
          <MUImage className={classNames('slogan', { 'slogan-plus': this.isVplus })} src={this.isVplus ? vPlusSloganTopLeft : sloganTopLeft} />
          <MUView className="intro" beaconId="ReinstallmentIntroClick" onClick={() => { this.jumpReinstallment(); }}>了解再分期 <MUIcon className="intro-icon" value="jump-cicle" size="15" color="#3477FF" /></MUView>
          <MUView className="title">再分期总金额</MUView>
          <MUView
            className="display-change"
            beaconId="ChangeLoanIousList"
            onClick={() => {
              this.changeLoanInfo();
            }}
          >
            <MUView className="display">
              ￥
              <MUText className="display-amount">{extensionTotalPrincipalAmt}</MUText>
            </MUView>
            <MUView className="change">
              查看借据
              <MUIcon className="change-icon" value="jump-cicle" size="14" color="#808080" />
            </MUView>
          </MUView>
          {Number(needPayTotalAmt) > 0 && (
            <MUView
              beaconId="ShowPayAmtExplain"
              onClick={() => this.showModal(0)}
            >
              <MUView className="tip">
                <MUText>申请通过后，需先还到期欠款<MUText className="tip-red">{needPayTotalAmt.toFixed(2)}元</MUText></MUText>
                {
                  dueTotalInteFeeWaiveAmt && Number(dueTotalInteFeeWaiveAmt) > 0 && (
                    <MUText>，息费立减<MUText className="tip-orange">{dueTotalInteFeeWaiveAmt}元</MUText></MUText>
                  )
                }
                <MUIcon value="info" size="14" color="#3477FF" />
              </MUView>
            </MUView>)}
        </MUView>
        {/* 多笔合一：选择单笔、多笔 */}
        {extendModeAndScene === '12' ? (<MUView className="re-installment-block">
          <MUView className={`block-item ${showPeriodPickeSlider ? 'padding30' : ''}`}>
            <MUView className="block-item__title">分期数</MUView>
            {adjustCntList.length === 1 ? (
              <MUView className="block-item__period">
                <MUView className="block-item__period--number">{selectedCnt}期</MUView>
              </MUView>
            ) : null}
            {adjustCntList.length > 1 && adjustCntList.length <= 6 ? (
              <MUView className="block-item__period" beaconId="ShowCntPicker" onClick={() => this.setState({ showPeriodPickeSlider: !showPeriodPickeSlider })}>
                {!showPeriodPickeSlider ? <MUView className="block-item__period--number">{selectedCnt}期</MUView> : null}
                <MUIcon className="block-item__period--icon" value={showPeriodPickeSlider ? 'arrow-up' : 'arrow-down'} size="14" color="#CACACA" />
              </MUView>
            ) : null}
            {adjustCntList.length > 6 ? (
              <MUView className="block-item__period" beaconId="ShowCntPicker" onClick={() => this.showPeriodPicker('1')}>
                <MUView className="block-item__period--number">{selectedCnt}期</MUView>
                <MUIcon className="block-item__period--icon" value="arrow-right" size="14" color="#CACACA" />
              </MUView>
            ) : null}
          </MUView>
          {showPeriodPickeSlider && adjustCntList.length > 1 && adjustCntList.length <= 6 ? (
            <MUView className="period-area">
              <MUScrollView
                className="period-area__scroll"
                scrollX
              >
                {adjustCntList.map((item, index) => (
                  <MUView
                    className={`period-area__item${selectedCnt === item ? ' brand-selected' : ''}`}
                    beaconId="SelectedCnt"
                    onClick={() => {
                      if (selectedCnt !== item) {
                        this.pickerType = '1';
                        this.onChoosePeridConfirm(index);
                      }
                    }}
                  >
                    <MUView>{item}期</MUView>
                    {selectedCnt === item ? (<MUImage className="period-area__item--checked" src={checkBlue} />) : null}
                  </MUView>
                ))}
              </MUScrollView>
            </MUView>
          ) : null}
          <MUView className="block-item padding70">
            <MUView className="block-item__left">
              <MUView className="block-item__left--title">前{selectedPreCnt}期</MUView>
              <MUIcon className="block-item__left--edit" beaconId="ShowPreCntPicker" onClick={() => this.showPeriodPicker('2')} value="edit" size="16" color="#3477FF" />
            </MUView>
            <NumberInput
              ref={(ref) => { this.numberInputRef = ref; }}
              title="月还款约"
              value={setExpectTotalRepayAmt || minExtendAmt}
              unit="元"
              max={maxExtendAmt}
              min={minExtendAmt}
              // step={100}
              tip={reduceAmt > 0 ? `比办理前降低${reduceAmt}元` : ''}
              condition={`需在${minExtendAmt}-${maxExtendAmt}元之间`}
              onSubtraction={this.doSubtraction}
              onAddition={this.doAddition}
            />
          </MUView>
          <MUView className="block-item nopadding novertical">
            <MUView className="block-item__title">后{selectedCnt - selectedPreCnt}期</MUView>
            <MUView className="block-item__desc">
              <MUView className="block-item__desc--main">月还款约{secondPhaseRepayAmt}元</MUView>
              <MUView className="block-item__desc--sub">具体以还款计划为准</MUView>
            </MUView>
          </MUView>
        </MUView>) : null}
        {/* 多笔合一、逐笔办理：首月金额上下限一致 */}
        {['11', '21'].includes(extendModeAndScene) ? (<MUView className="re-installment-block">
          {extendModeAndScene === '11' ? (<MUView className="block-item padding30">
            <MUView className="block-item__title">分期数</MUView>
            {adjustCntList.length === 1 ? (
              <MUView className="block-item__period">
                <MUView className="block-item__period--number">{selectedCnt}期</MUView>
              </MUView>
            ) : null}
            {adjustCntList.length > 1 && adjustCntList.length <= 6 ? (
              <MUView className="block-item__period" beaconId="ShowCntPicker" onClick={() => this.setState({ showPeriodPickeSlider: !this.state.showPeriodPickeSlider })}>
                {!showPeriodPickeSlider ? <MUView className="block-item__period--number">{selectedCnt}期</MUView> : null}
                <MUIcon className="block-item__period--icon" value={showPeriodPickeSlider ? 'arrow-up' : 'arrow-down'} size="14" color="#CACACA" />
              </MUView>
            ) : null}
            {adjustCntList.length > 6 ? (
              <MUView className="block-item__period" beaconId="ShowCntPicker" onClick={() => this.showPeriodPicker('1')}>
                <MUView className="block-item__period--number">{selectedCnt}期</MUView>
                <MUIcon className="block-item__period--icon" value="arrow-right" size="14" color="#CACACA" />
              </MUView>
            ) : null}
          </MUView>) : null}
          {extendModeAndScene === '11' && showPeriodPickeSlider && adjustCntList.length > 1 && adjustCntList.length <= 6 ? (
            <MUView className="period-area small-bottom">
              <MUScrollView
                className="period-area__scroll"
                scrollX
              >
                {adjustCntList.map((item, index) => (
                  <MUView
                    className={`period-area__item${selectedCnt === item ? ' brand-selected' : ''}`}
                    beaconId="SelectedCnt"
                    onClick={() => {
                      if (selectedCnt !== item) {
                        this.pickerType = '1';
                        this.onChoosePeridConfirm(index);
                      }
                    }}
                  >
                    <MUView>{item}期</MUView>
                    {selectedCnt === item ? (<MUImage className="period-area__item--checked" src={checkBlue} />) : null}
                  </MUView>
                ))}
              </MUScrollView>
            </MUView>
          ) : null}
          <MUView className="block-item padding30">
            <MUView className="block-item__title">首月还款金额</MUView>
          </MUView>
          {Number(originFirstPhaseRepayAmt) >= Number(extendFirstPhaseRepayAmt) ? (<MUView className="amout-area">
            <MUImage className="amout-area__bg" src={amoutChange} />
            <MUView className="amout-area__before">
              <MUView className="amout-area__before--text">办理前</MUView>
              <MUView className="amout-area__before--number">{`月还款${originFirstPhaseRepayAmt}元`}</MUView>
            </MUView>
            <MUView className="amout-area__after">
              <MUView className="amout-area__after--text">办理后</MUView>
              <MUView className="amout-area__after--number">{`月还款${extendFirstPhaseRepayAmt}元`}</MUView>
            </MUView>
          </MUView>) : (<MUView className="amout-area">
            <MUImage className="amout-area__bg" src={amoutChange} />
            <MUView className="amout-area__before1">
              <MUView className="amout-area__before1--text">办理后</MUView>
              <MUView className="amout-area__before1--number">{`月还款${extendFirstPhaseRepayAmt}元`}</MUView>
            </MUView>
            <MUView className="amout-area__after1">
              <MUView className="amout-area__after1--text">办理前</MUView>
              <MUView className="amout-area__after1--number">{`月还款${originFirstPhaseRepayAmt}元`}</MUView>
            </MUView>
          </MUView>)}
        </MUView>) : null}
        {/* 逐笔办理：金额上下限不一致 */}
        {extendModeAndScene === '22' ? (<MUView className="re-installment-block">
          <MUView className="block-item padding50">
            <MUView className="block-item__title">
              首月还款金额约<MUView className="block-item__title--number">{setExpectTotalRepayAmt || minExtendAmt}</MUView>元
              {reduceAmt > 0 ? (<MUView className="block-item__title--tip">
                {`预计比办理前降低${reduceAmt}元`}
              </MUView>) : null}
            </MUView>
          </MUView>
          <MUView className="slider-area">
            <MUSlider
              beaconId="AmtChooseSlider"
              step={1}
              value={sliderValue}
              max={10}
              marks={this.sliderMarks}
              showScale
              onChange={(e) => this.onSliderChange(e.detail.value)}
            />
          </MUView>
        </MUView>) : null}
        {/* 停息停费、未来期打折 */}
        {(waiveInteFeeFlag === 'Y' || (['21', '22'].includes(extendModeAndScene) && Number(waiveRate) > 0 && Number(surplusTotalinteFeeWaiveAmt) > 0))
          ? (<MUView className={`re-installment-block discounts ${(Number(waiveRate) > 0 && Number(surplusTotalinteFeeWaiveAmt) > 0) ? 'small-padding' : ''}`}>
            <MUView className="block-item nopadding">
              <MUView className="block-item__title">
                <MUImage className="block-item__title--icon" src={huiIcon} />
                <MUText className="block-item__title--text">专享优惠</MUText>
              </MUView>
              <MUView className="block-item__right">
                <MUView className="block-item__right--main">
                  <MUText className="block-item__right--main--text">
                    {(['21', '22'].includes(extendModeAndScene) && Number(waiveRate) > 0 && Number(surplusTotalinteFeeWaiveAmt) > 0)
                      ? `办理的借据，息费减免${waiveRate}%`
                      : '还款时，可减免全部息费'}
                  </MUText>
                  <MUIcon className="block-item__right--main--icon" beaconId="ShowDiscountExplainDialog" onClick={() => this.showModal(3)} value="info" size="15" color="#FF8844" />
                </MUView>
                {(Number(waiveRate) > 0 && Number(surplusTotalinteFeeWaiveAmt) > 0) ? (<MUView className="block-item__right--sub">
                  累计优惠
                  <MUText className="block-item__right--sub--light">{surplusTotalinteFeeWaiveAmt}元</MUText>
                  ，逾期后优惠失效
                </MUView>) : null}
              </MUView>
            </MUView>
          </MUView>) : null}
        {/* 多笔合一：年利率 */}
        {['11', '12'].includes(extendModeAndScene) ? (<MUView className="re-installment-block">
          <MUView className={`block-item ${(Number(extensionWaivedYearRate) > 0 && (Number(extensionWaivedYearRate) < Number(extensionYearRate)) && Number(surplusTotalinteFeeWaiveAmt) > 0) ? 'padding50' : ''}`}>
            <MUView className="block-item__left--title">
              <MUView className="block-item__left--title--text">年利率(单利)</MUView>
              <MUIcon className="block-item__left--title--icon" beaconId="ShowInterestExplainDialog" onClick={() => this.showModal(1)} value="info" size="15" color="#A6A6A6" />
            </MUView>
            <MUView className="block-item__right">
              {(Number(extensionWaivedYearRate) > 0 && (Number(extensionWaivedYearRate) < Number(extensionYearRate))) ? (
                <MUView className="block-item__right--interest">{Util.stringToPersent(extensionWaivedYearRate, 4)}<MUText className="block-item__right--interest--line">{Util.stringToPersent(extensionYearRate, 4)}</MUText></MUView>
              ) : (<MUView className="block-item__right--interest">{Util.stringToPersent(extensionYearRate, 4)}</MUView>)}
              {(Number(extensionWaivedYearRate) > 0 && (Number(extensionWaivedYearRate) < Number(extensionYearRate)) && Number(surplusTotalinteFeeWaiveAmt) > 0) ? (
                <MUView className="block-item__right--desc">优惠<MUText className="block-item__right--desc--light">{surplusTotalinteFeeWaiveAmt}元</MUText>，逾期后优惠失效</MUView>
              ) : null}
            </MUView>
          </MUView>
          {extensionPrincipalType ? (<MUView className="block-item">
            <MUView className="block-item__left--title">
              <MUView className="block-item__left--title--text">还款方式</MUView>
              <MUIcon className="block-item__left--title--icon" beaconId="ShowRepayWayExplainDialog" onClick={() => this.showModal(2)} value="info" size="15" color="#A6A6A6" />
            </MUView>
            <MUView className="block-item__right">{extensionPrincipalType}</MUView>
          </MUView>) : null}
          <MUView className="block-item nopadding">
            <MUView className="block-item__title">提前还款规则</MUView>
            <MUView className="block-item__light">提前还款无违约金</MUView>
          </MUView>
        </MUView>) : null}
        {/* 多笔合一：新还款计划 */}
        {['11', '12'].includes(extendModeAndScene) ? (<MUView className={`re-installment-block ${(waiveInteFeeFlag === 'Y' && Number(stopFeeAmt) > 0) ? 'small-padding' : ''}`}>
          <MUView className="block-item nopadding">
            <MUView className="block-item__title">新还款计划</MUView>
            <MUView className="block-item__plan" onClick={() => this.setState({ isReInstallmentPlanDrawerOpen: true })}>
              <MUView className="block-item__plan--main">
                <MUView>{`${payDate.substring(4, 6)}月${payDate.substring(6, 8)}日 应还${surplusPayTotalAmt}元`}</MUView>
                {/* 展示下月预计减免的息费，应该是取第一期的停息停费减免金额stopFeeAmt */}
                {waiveInteFeeFlag === 'Y' && Number(stopFeeAmt) > 0 ? (<MUView className="block-item__plan--sub">
                  还款时，预计减免息费
                  <MUText className="block-item__plan--sub--light">{stopFeeAmt}元</MUText>
                </MUView>) : null}
              </MUView>
              <MUIcon className="block-item__plan--icon" beaconId="ShowRepayPlanDrawer" value="arrow-right" size="15" color="#A6A6A6" />
            </MUView>
          </MUView>
        </MUView>) : null}
        {/* 逐笔办理：新还款计划 */}
        {['21', '22'].includes(extendModeAndScene) ? (<MUView className="re-installment-plan">
          <MUView className="plan-title">
            <MUView className="plan-title-left">
              <MUText className="main">新还款计划</MUText>
              <MUText className="sub brand-selected">{`共${repayPlanAfterExtend && repayPlanAfterExtend.length || 0}期`}</MUText>
            </MUView>
          </MUView>
          <MUView className="plan-desc">
            <MUView>
              总息费{surplusTotalPayInteAmtAfterExtend}元
              {Number(stopFeeOrderNum) > 0 ? (
                <MUText>，<MUText className="plan-desc__orange">{stopFeeOrderNum}笔</MUText>借据还款时<MUText className="plan-desc__orange">享息费全免</MUText></MUText>
              ) : null}
            </MUView>
          </MUView>
          <MUView className="plan-content">
            <RepayPlan
              repayPlanAfterExtend={repayPlanAfterExtend}
              showFeeWaiveTip={waiveInteFeeFlag === 'Y'}
              surplusTotalinteFeeWaiveAmt={(Number(waiveRate) > 0 && Number(surplusTotalinteFeeWaiveAmt) > 0) ? surplusTotalinteFeeWaiveAmt : ''}
            />
          </MUView>
        </MUView>) : null}
        {/* 占位（留出底部区域位置） */}
        <MUView className="re-installment-space" />
        {/* 底部区域位置 */}
        <MUView className="re-installment-bottom">
          <Protocol
            trackPrefix="repayment.ReInstallmentApply"
            onChecked={(v) => { this.setState({ isCheckedContract: v }); }}
            contractApplyList={contractApplyList}
            contractInfoList={contractInfoList}
            billExtendInfo={{ mergeOrderFlag: this.mergeOrderFlag,
              orderInfoListAfterMerge: this.orderInfoListAfterMerge,
              originalOrderInfoListCanExtend: this.originalOrderInfoListCanExtend,
              orderInfoListCanExtend: this.orderInfoListCanExtend
            }}
          />

          <MUView className="confirm-button">
            <MUButton
              type="primary"
              onClick={throttle(this.confirmReinstallmentHandler, 2000)}
              full
              beaconId="ConfirmHandle"
              beaconContent={{
                cus: {
                  selectedRepayWay: 1,
                }
              }}
            >
              确认办理
            </MUButton>
          </MUView>
        </MUView>
        {/* 多笔合一：新还款计划弹窗 */}
        <MUDrawer
          beaconId="ReInstallmentPlanDrawer"
          show={isReInstallmentPlanDrawerOpen}
          placement="bottom"
          height={Taro.pxTransform(1000)}
          onClose={() => {
            this.setState({
              isReInstallmentPlanDrawerOpen: false
            });
          }}
        >
          <MUView className="re-installment-plan-drawer">
            <MUView
              beaconId="ReInstallmentPlanDrawerClose"
              className="re-installment-plan-drawer-closeImg"
              onClick={() => {
                this.setState({
                  isReInstallmentPlanDrawerOpen: false
                });
              }}
            >
              <MUIcon className="re-installment-plan-drawer-closeImg-close" value="close2" size={18} color="#A6A6A6" />
            </MUView>

            <MUView className="re-installment-plan-drawer-title">
              新还款计划
            </MUView>
            <MUView className="re-installment-plan-drawer-bubble">
              <MUView className="re-installment-plan-drawer-bubble-text">
                总息费{surplusTotalPayInteAmtAfterExtend}元
                { // 多笔合一：未来期息费打折（Number(waiveRate) > 0），无打折（Number(waiveRate) = 0）
                  Number(waiveRate) > 0 && Number(surplusTotalinteFeeWaiveAmt) > 0 ? (
                    <MUText>
                      ，共优惠
                      <MUText className="re-installment-plan-drawer-bubble-text-highlight">
                        {surplusTotalinteFeeWaiveAmt}元
                      </MUText>
                    </MUText>
                  ) : null
                }
                { // 新还款计划：停息停费时（多笔合一就只有1期）
                  waiveInteFeeFlag === 'Y' && (
                    <MUText>
                      ，
                      <MUText className="re-installment-plan-drawer-bubble-text-highlight">
                        1笔
                      </MUText>
                      借据还款时
                      <MUText className="re-installment-plan-drawer-bubble-text-highlight">
                        享息费全免
                      </MUText>
                    </MUText>
                  )
                }
              </MUView>
            </MUView>

            <MUView className="re-installment-plan-drawer-main">
              <RepayPlan
                repayPlanAfterExtend={repayPlanAfterExtend}
                surplusTotalinteFeeWaiveAmt={(Number(waiveRate) > 0 && Number(surplusTotalinteFeeWaiveAmt) > 0) ? surplusTotalinteFeeWaiveAmt : ''}
                showFeeWaiveTip={waiveInteFeeFlag === 'Y'}
                useInDrawer
              />
            </MUView>
          </MUView>
        </MUDrawer>
        {/* 期数选择：showPeriodPicker为真时再渲染此组件，否则无法拿到DOM，影响组件交互 */}
        {showPeriodPicker ? (<MyPicker
          show={showPeriodPicker}
          beaconId="PeriodPicker"
          title="请选择分期数"
          value={this.pickerType === '1' ? selectedCnt : selectedPreCnt}
          options={pickerOptions}
          onConfirm={this.onChoosePeridConfirm}
          onCancel={this.onChoosePeridCancel}
        />) : null}
        <RepayModal
          className="icon-modal"
          beaconId={commonModalBeaconId}
          title={commonModalTitle}
          isOpened={showCommonModal}
          onClose={() => {
            this.setState({ showCommonModal: false, commonModalInfo: {} });
          }}
        >
          <MUView className="icon-modal-content">
            {/* 组件中传入subTitle，居中的。这里需要左对齐，所以单独写 */}
            {commonModalSubTitle ? (<MUView className="icon-modal-content__subTitle" style={{ color: commonModalSubTitleColor || '' }}>{commonModalSubTitle}</MUView>) : null}
            {
              commonModalContent && commonModalContent.map((item) => {
                if (item && item.length >= 2) {
                  return (<MUView>
                    <MUText className="icon-modal-content__bold">{item[0]}</MUText>
                    <MUText>{item[1]}</MUText>
                  </MUView>);
                }
                return (<MUView>{item}</MUView>);
              })
            }
          </MUView>
          <MUImage className={classNames('icon-modal-slogan', { 'icon-modal-slogan-plus': this.isVplus })} src={this.isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
        </RepayModal>
        {/* 原始借据列表 */}
        <OriginalIousList
          ref={(ref) => { this.OriginalIousList = ref; }}
          allBillList={this.allBills && this.allBills.advanceBillList}
          orderInfoListCanExtend={this.originalOrderInfoListCanExtend}
          orderInfoListCanNotExtend={this.originalOrderInfoListCanNotExtend}
          selectedOrderInfoList={this.originalOrderInfoListCanExtend}
          isVplus={this.isVplus}
          needRepayType={this.needRepayType}
        />
        {/* 交互式运营组件 */}
        <OpRepayment pageId={pageId} opEventKey="opPageLeave" />
      </MUView>
    ) : <MUView />;
  }
}
