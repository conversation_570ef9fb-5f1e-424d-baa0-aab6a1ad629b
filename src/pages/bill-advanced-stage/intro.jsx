/* eslint-disable react/sort-comp */
import { Component } from '@tarojs/taro';
import {
  MUView, MUImage, MUButton
} from '@mu/zui';
import {
  track, EventTypes
} from '@mu/madp-track';
import Madp from '@mu/madp';
import classNames from 'classnames';
import pageHoc from '@utils/pageHoc';
import Util from '@utils/maxin-util';
import { Url } from '@mu/madp-utils';
import { sloganUrl } from '@utils/constants';
import { getStore } from '@api/store';
import './intro.scss';

import IMG_TOP from './img/bg_top_new.png';
import IMG_MIDDLE from './img/bg_middle_new.png';

@track({ event: EventTypes.PO }, {
  pageId: 'ReInstallmentIntro',
  dispatchOnMount: true,
})
@pageHoc({ title: '了解再分期' })
export default class ReinstallmentIntro extends Component {
  config = {
    navigationBarTitleText: '了解再分期',
    navigationStyle: (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'tt') ? 'custom' : 'default'
  }

  backHandle = () => {
    const param = Url.getParam('external') || '';
    if (param) {
      Util.router.replace('/pages/bill-advanced-stage/list?repaymentFlag=Y');
    } else {
      Madp.navigateBack();
    }
  }

  render() {
    const isVplus = getStore('isVplus') || false; // 是否为v+会员
    return (
      <MUView className="intro-bg">
        <MUView className="intro-bg-blue">
          <MUView className="intro-bg-content">
            <MUView className="intro-bg-content-img">
              <MUImage className={classNames('slogan', { 'slogan-plus': isVplus })} src={isVplus ? sloganUrl.middleVplus : sloganUrl.middle} />
              <MUImage className="img_top" src={IMG_TOP} />
              <MUImage className="img_middle" src={IMG_MIDDLE} />
            </MUView>
            <MUView className="intro-bg-content-blank" />
            <MUView className="intro-bg-content-btn">
              <MUButton type="primary" className="btn" beaconId="IntroBtnClick" onClick={() => { this.backHandle(); }}>立即办理</MUButton>
            </MUView>
          </MUView>
        </MUView>
      </MUView>
    );
  }
}