import Madp from '@mu/madp';
import mutations from './mutations';

const ADVANCED_STAGE_DEFAULTS = {
  isNeedRepay: false,
  shouldRepayAmt: '',
  installDetails: {},
  installAmt: '',
  extendPackageList: [],
  selectedBillDetailList: [], // 已选的账单详情列表
};

const FEE_REDUCE_DEFAULTS = {
  feeReduce: '',
  feeReducedOrderList: [],
};

const BILLS_STAGING_DEFAULTS = {
  awardNo: '',
  bankCardId: '',
  cnt: '',
};

const BILL_EXTEND_DEAFULTS = {
  applyScene: '',
  applyNo: '',
  userTag: '',
  nextRepayDate: '',
  custType: '',
};

const RENEW_LOANS_INFO = {
  applyNo: '',
  orderNo: '',
  adjustPeriod: 0,
  canAdjustPeriodRange: [],
  loanDate: '',
  installTotalCnt: '',
  firstRepayDate: '',
  repayAmt: '', // 分期还本后首期还款金额
  installTotalAmt: '', // 借款本金
};

const TRANSIT_APPLY_CASE_DEFAULTS = {
  applyNo: '',
  applyStatus: '',
  serviceType: '',
  canClose: '',
};

const REPAYMENT_DEFAULTS = {
  settleQuery: '',
  repayTrialDetailList: [], // 息费减免试算后的账单明细
  // 优惠券list
  awardInfoList: [],
  // 新旧配置开关 Y 开启优惠券
  // enablePrivilegeCoupon: '',
  sessionInfo: {},
  // 账单类型
  repayBillsType: '',
  // 7天待还总额
  nearBillsTotalAmount: '',
  // 七日待还账单
  nearBills: {},
  // 未来期账单
  futureBillList: [],
  // 全部待还
  allBills: {},
  // 已选的银行卡
  selectedCard: {},
  // 优惠券列表
  couponList: [],
  // 已选的优惠券
  selectedCoupon: {},
  // 已选的账单
  selectedBillList: [],
  // 查看的账单详情
  currentBillDetail: {},
  // 已选分期数
  selectedLoanPeriod: null,
  advancedStageInfo: {
    ...ADVANCED_STAGE_DEFAULTS
  },
  // 在途还款交易列表
  pendingRepayTransInfo: {},
  // 再分期：待补充资料信息
  advancedStageIApplyMaterial: {},
  feeReduceInfo: {
    ...FEE_REDUCE_DEFAULTS
  },
  billsStagingInfo: {
    ...BILLS_STAGING_DEFAULTS
  },
  renewLoansInfo: {
    ...RENEW_LOANS_INFO
  },
  // 首页还款状态
  repayStatus: '',
  // 是否逾期客户
  isOverdueCust: false,
  // 是否随机立减
  canRandomDiscount: false,
  // 可延长宽限期账单列表
  adjustList: [],
  // 可延长还款日账单列表
  extendtList: [],
  // 是否隐藏支付宝微信支付方式
  uniconHideWxAli: false,
  // 延期还款信息
  extendRepayInfo: {},
  // 延期还：待补充资料信息
  extendRepayApplyMaterial: {},
  // 延长宽限期信息
  adjustRepayInfo: {},
  // 延长还款日期数，默认1
  extendTerm: '1',
  // 外部跳转时用到的数据储存
  externalFillObj: {},
  // 还款特权服务列表
  serviceList: [],
  // 还款之后回到功能页面
  redirectFirstPage: '',
  // 溢缴款余额
  overPayAmtIndex: 0.00,
  // 客服拓展参数
  chatEntryExtraParam: {
    needLogin: 1
  },
  // 还款合同列表
  contractApplyList: [],
  // 合同3.0列表
  contractInfos: [],
  ...BILL_EXTEND_DEAFULTS,
  // 信用保护账单列表
  creditProductList: [],
  // 信用保护提交信息
  creditProductRepayInfo: {},
  // 信用保护结果页信息
  creditProductResultInfo: {},
  // 还款服务栏目列表数据
  repayServiceColumnsParam: {},
  // 他人待还信息
  repayOthersInfo: {},
  // 标识是否为V+会员
  isVplus: false,
  // 在途服务案件信息
  transitApplyCase: {},
  // 单笔交易记录查结果-本次还款是否有入账溢缴款
  overRepayAmt: 0.00,
  // 指定还款之后回到功能页面
  assignedRedirectPage: '',
  // 协商还信息
  consultReapayInfo: {},
};

let REPAYMENT_STORE = {
  ...BILL_EXTEND_DEAFULTS,
  ...REPAYMENT_DEFAULTS,
  advancedStageInfo: {
    ...ADVANCED_STAGE_DEFAULTS
  },
  feeReduceInfo: {
    ...FEE_REDUCE_DEFAULTS
  },
  billsStagingInfo: {
    ...BILLS_STAGING_DEFAULTS
  },
  transitApplyCase: {
    ...TRANSIT_APPLY_CASE_DEFAULTS
  },
};

let TARO_REPAYMENT_DATA_CURRENT = {
  ...REPAYMENT_STORE,
  NEED_STORAGE: true
};

const isSaveLocal = Madp.getChannel() === '3CMBAPP' || Madp.getChannel() === '0BDXCX';

Object.keys(REPAYMENT_STORE).forEach((key) => {
  Object.defineProperty(REPAYMENT_STORE, key, {
    set(val) {
      if (TARO_REPAYMENT_DATA_CURRENT.NEED_STORAGE) {
        TARO_REPAYMENT_DATA_CURRENT = Madp.getStorageSync('taro_repayment_data', isSaveLocal ? 'LOCAL' : 'SESSION') || {};
        TARO_REPAYMENT_DATA_CURRENT.NEED_STORAGE = false;
      }
      if (TARO_REPAYMENT_DATA_CURRENT[key] !== val) {
        TARO_REPAYMENT_DATA_CURRENT[key] = val;
        Madp.setStorage({ key: 'taro_repayment_data', data: TARO_REPAYMENT_DATA_CURRENT, type: isSaveLocal ? 'LOCAL' : 'SESSION' });
      }
    },
    get() {
      if (TARO_REPAYMENT_DATA_CURRENT.NEED_STORAGE) {
        TARO_REPAYMENT_DATA_CURRENT = Madp.getStorageSync('taro_repayment_data', isSaveLocal ? 'LOCAL' : 'SESSION') || {};
        TARO_REPAYMENT_DATA_CURRENT.NEED_STORAGE = false;
      }
      const data = TARO_REPAYMENT_DATA_CURRENT;
      if (data[key] === undefined) {
        data[key] = REPAYMENT_DEFAULTS[key];
      }
      return data[key];
    }
  });
});

export const setStore = (obj) => {
  Object.keys(obj).forEach((key) => {
    REPAYMENT_STORE[key] = obj[key];
  });
};

export const getStore = (opt) => {
  // 若输出 { ...REPAYMENT_STORE }，后续对store修改不会在输出中生效
  // 读取整个store时直接输出，保证后续对store的修改，也能在输出中生效
  if (!opt) return REPAYMENT_STORE;
  if (typeof opt === 'object') {
    // 输入数组读取的情况没考虑后续修改的同步更新
    const store = {};
    Object.keys(opt).forEach((key) => {
      const storeKey = opt[key];
      store[storeKey] = REPAYMENT_STORE[storeKey];
    });
    return { ...store };
  } else {
    return REPAYMENT_STORE[opt];
  }
};

setStore.commit = (funcKey, data) => {
  mutations[funcKey](REPAYMENT_STORE, data);
};

setStore.clear = (key) => {
  if (key) {
    REPAYMENT_STORE[key] = REPAYMENT_DEFAULTS[key];
    return;
  }
  REPAYMENT_STORE = {
    ...BILL_EXTEND_DEAFULTS,
    ...REPAYMENT_DEFAULTS,
    advancedStageInfo: {
      ...ADVANCED_STAGE_DEFAULTS
    },
    feeReduceInfo: {
      ...FEE_REDUCE_DEFAULTS
    },
    billsStagingInfo: {
      ...BILLS_STAGING_DEFAULTS
    },
    transitApplyCase: {
      ...TRANSIT_APPLY_CASE_DEFAULTS
    },
  };
};

export default REPAYMENT_DEFAULTS;
