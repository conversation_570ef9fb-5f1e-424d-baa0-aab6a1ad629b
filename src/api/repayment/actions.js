/* eslint-disable no-param-reassign */
/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
/* eslint-disable no-unused-expressions */
import fetch from '@utils/mgp-fetch.js';
import Madp from '@mu/madp';
import formDataFetch from '@utils/mgp-formData-fetch.js';
import {
  getStore,
  setStore
} from '@api/store';
import Util from '@utils/maxin-util';
// import ChannelConfig from '@config/index';
import { repayStatusType } from '@utils/constants';

const actions = {};

const OPERATIONS = {
  // 查询 7 天待还信息
  RECENT_BILLS: 'mucfc.repayment.bill.recentBill',
  // 未来期账单
  ACQUIRE_FUTURE_PLANS: 'mucfc.repayment.bill.futureBill',
  // 查询全部待还信息
  ALL_BILLS: 'mucfc.repayment.bill.advanceBill',
  // 查询用户银行卡列表
  // QUERY_BANK_CARDS: 'mucfc.user.bankCard.queryBankCards',
  // 查询预约状态
  QUERY_RESERVE_STATE: 'mucfc.repayment.remit.queryRepayReservation',
  // 新银行卡接口，ubs迁移到tbs
  QUERY_BANK_CARDS_LIST: 'mucfc.loan.bankcard.queryAvailableBankCardList',
  // 立即还款
  PAY_IMMEDIATE: 'mucfc.repayment.normalRepay.repayApply',
  SESSION_INFO: 'mucfc.user.infoMaintain.getUserInfo',
  SESSION_INFO_NEW: 'mucfc.user.infoMaintain.getCustInfo',
  // 账单分期接口
  RENEWED_BILL_APPLY: 'mucfc.loan.renewedBill.renewedBillApply',
  // 优惠券试算
  AWARD_TRIAL: 'mucfc.repayment.normalRepay.awardTrial',
  // 支付宝SDK回调
  ALIPAY_CALLBACK: 'mucfc.repayment.normalRepay.alipaySdkCallback',
  // 获取银行卡还款限额
  GET_ORGS: 'mucfc.user.bankCard.getBankInfo',
  // 提前还款试算
  CHANGE_AMOUNT_REPAY: 'mucfc.repayment.normalRepay.repayCalculate',
  // 查询还款计划
  QUERY_REPAY_PLAN: 'mucfc.loan.order.orderQuery',
  // 省心分信息
  QUERY_ADVANCED_STAGE_INFO: 'mucfc.repayment.extend.cal',
  // 省心分提交还款
  ADVANCED_STAGE_REPAY: 'mucfc.repayment.extend.repayApply',
  // 省心分提交还款
  ADVANCED_STAGE_REPAY_NEW: 'mucfc.repayment.adjust.extendRepay',
  // 再分期试算接口
  QUERY_EXTEND_REPAY_CAL: 'mucfc.postLoan.service.extendRepayCal',
  // 省心分提交
  ADVANCED_STAGE_APPLY: 'mucfc.repayment.extend.apply',
  // 检查补充身份信息场景
  CHECK_SUPPLY_INFO: 'mucfc.loan.check.param',
  // 参数配置系统
  COMMON_CONFIG: 'mucfc.basic.app.queryConfigParams',
  // 查询还款服务列表
  GET_SERVICE_LIST: 'mucfc.repayment.repayService.query',
  // 挽留弹窗
  CHECK_RETAIN: 'mucfc.repayment.repayStay.trial',
  // 额度信息
  CHECK_CREDIT: 'mucfc.loan.loanInfo.queryCreditsPriceParam',
  // 汇款流水无主账识别前置检查
  CHECK_IDENTITY_TRANSFER: 'mucfc.repayment.remit.identityCheck',
  // 查询预约状态
  CHECK_RESERVATION: 'mucfc.repayment.remit.queryReservation',
  // 线下还款预约
  SUBMIT_RESERVATION: 'mucfc.repayment.remit.reserveRepay',
  // 线下还款查银行卡，卡号和用户信息有关
  CHECK_TRANSFER_PAYWAY: 'mucfc.repayment.remit.queryAcct',
  // 取消线下预约
  CANCEL_RESERVATION: 'mucfc.repayment.remit.cancelReservation',
  // 查询无主账申请结果
  CHECK_TRANSFER_RESULT: 'mucfc.repayment.remit.applyQuery',
  // 申请无主入账
  UPLOAD_TRANSFER_INFO: 'mucfc.repayment.remit.identity',
  // 查询是否签约支付宝代扣协议
  CHECK_ALIPAY_SIGN: 'mucfc.user.contract.whetherSignContract',
  // 查询签署支付宝代扣协议链接
  SIGN_ALIPAY_CONTRACT: 'mucfc.user.contract.signContract',
  // 获取当前还款日
  GET_CURRENT_PAYDAY: 'mucfc.repayment.repayDay.query',
  // 获取当前还款日-新接口
  GET_CURRENT_PAYDAY_NEW: 'mucfc.loan.account.getAccount',
  // 变更还款日
  ADJUST_PAYDAY: 'mucfc.repayment.repayDay.adjust',
  // 息费减免试算
  // REDUCE_FEE_CAL: 'mucfc.repayment.inteFee.waiveTrail',
  // 查询账户特权
  REPAY_SERVICE_DETAIL: 'mucfc.loan.account.queryPrivilege',
  // 提交延长还款日申请
  APPLY_EXTEND_REPAY: 'mucfc.loan.delay.delayPay',
  // 提交延期还款申请单
  COMMIT_EXTEND_FORM: 'mucfc.custservice.CustomerVoice.addCvApprovalApply',
  // 变更还款日借据试算
  MODIFY_REPAY_DATE: 'mucfc.repayment.repayDay.trial',
  // 还款前置校验
  REPAY_PRE: 'mucfc.repayment.check.preRepayCheck',
  // 代还款查询借款人信息
  OFFER_REPAY_HRLP: 'mucfc.repayment.repayHelp.offerRepayHelp',
  // 代还款 立即还款
  OFFER_REPAY_APPLY: 'mucfc.repayment.normal.helpRepayApply',
  // 新客户账户查询接口3.0
  ACCOUNT_INFO_NEW: 'mucfc.loan.account.getAccount',
  // 新: 借款试算接口
  LOAN_TRANS_TRIAL: 'mucfc.loan.trial.loanTransTrial',
  // 新：账单分期提交接口
  BILL_INSTALL_APPLY: 'mucfc.loan.billInstall.billInstallApply',
  // 代还款查询待还金额
  OFFER_REPAY_GET_AMOUNT: 'mucfc.repayment.bill.helpRepayInfoQuery',
  // 溢缴款账户查询接口
  OVER_ACCOUNT_INFO: 'mucfc.loan.account.queryOverpayAccountInfo',
  // 查单笔交易记录
  QUERY_TRANS_RECORD_SINGLE: 'mucfc.loan.trans.transRecordSingleQuery',
  // 取消对应交易的短信发送
  CANCEL_SEND_MSG: 'mucfc.user.userCommon.cancelSendDelayMessage',
  // 查询用户的优惠券信息[改券模式-交易特权权益化改造]
  QERRY_COUPON_LIST: 'mucfc.promotion.coupon.queryCouponList',
  // 判断是否是柳下客
  CHECK_GUEST_CONDITION: 'mucfc.activity.common.runRule',
  // 多功能试算接口，可支持优惠券试算、提前还款试算、逾期试算，账单修改试算
  REPAY_TRANS_TRIAL: 'mucfc.repayment.trial.repayTransTrial',
  // 多功能试算接口（无状态）
  REPAY_TRANS_TRIAL_SPECIAL: 'mucfc.repayment.trial.repayTransTrialSpecial',
  // 根据商户号查询商户名称
  QUERY_MERCHANT_INFO: 'mucfc.loan.query.queryMerchantInfo',
  // 客群梯度减免批量试算接口
  REPAY_WAIVE_TRIAL_TRANS: 'mucfc.repayment.trial.repayWaiveTrialTrans',
  // 客群梯度减免批量试算接口（无状态）
  REPAY_WAIVE_TRIAL_TRANS_SPECIAL: 'mucfc.repayment.trial.repayWaiveTrialTransSpecial',
  // 客户提交还款网关接口，支持多种还款模式
  NORMAL_REPAY_APPLY: 'mucfc.repayment.normal.repayApply',
  // 关闭在途还款订单接口
  REPAY_CLOSE: 'mucfc.repayment.normal.repayClose',
  // 前端查询渠道参数配置, 查合同条款
  QUERY_CHANNEL_PARAMS: 'mucfc.content.channel.queryChannelParam',
  // 在途借据降价试算接口：满足降价条件的用户，批量在途借据进行降价试算，返回降价前后的还款明细
  ADJUST_REPAY_TRANS_TRIAL: 'mucfc.repayment.trial.adjustExecutePriceTrial',
  // 发送动码
  SEND_SMS: 'mucfc.basic.sms.send',
  // 验证动码
  VERIFY_SMS: 'mucfc.basic.sms.verify',
  // 权益账户信息查询接口
  QUERY_AWARD_ACCOUNT: 'mucfc.promotion.account.queryAwardAccount',
  CONTRACT_INFO: 'mucfc.user.contract.getContractInfo',
  // 查询今日预还款记录
  QUERY_TODAY_PREREPAY: 'mucfc.cust.queryTodayPreRepay',
  // 预还款提交
  PRE_REPAY_APPLY: 'mucfc.repayment.normal.preRepayApply',
  // 贷后调整准入
  ADJUST_ACCESS_CHECK: 'mucfc.repayment.adjust.adjustAccessCheck',
  // 贷后调整申请
  ADJUST_APPLY: 'mucfc.repayment.adjust.adjustApply',
  // 延长宽限期提交
  EXTEND_GRACE_PERIOD: 'mucfc.repayment.adjust.extendGracePeriod',
  // 查询待办
  QUERY_TODO: 'mucfc.content.userQuery.queryTodo',
  // 编码AB查询
  QUERY_AB_SPLIT: 'mucfc.content.abtest.codingAbSplit',
  // 推荐优惠券查询
  QUERY_RECOMMEND_COUPONS: 'mucfc.promotion.coupon.queryRecommendCoupons',
  // 获取渠道上下文
  GET_CHANNEL_CONTEXT: 'mucfc.content.channel.getChannelContext',
  // 协商还自主申请建案，获取建案相关信息
  APPLY_CONSULT_REPAY_CASE: 'mucfc.postLoan.service.applyCase',
  // 协商还服务提交建案
  SUBMIT_CONSULT_REPAY_CASE: 'mucfc.postLoan.service.submitCase',
  // 协商还还款状态及还款计划查询
  QUERY_REPAY_PLAN_STATUS: 'mucfc.postLoan.service.queryRepayPlanAndStatus',
  // 协商还试算
  QUERY_NEGOTIATE_REPAY_TRIAL: 'mucfc.postLoan.service.negotiateRepayTrial',
  // 贷后服务案件提交审核接口（替换原接口
  POST_LOAN_SUBMIT_CASE: 'mucfc.postLoan.service.submitCase',
  // 贷后服务申请资料补充接口（替换原接口）
  POST_LOAN_ADD_DATA: 'mucfc.postLoan.service.addData',
  // 贷后服务案件详情查询
  QUERY_CUST_CASE_DETAIL: 'mucfc.postLoan.service.queryCustCaseDetail',
  // 借款详情页: 查询借据详情
  QUERY_DETAIL: 'mucfc.loan.orderInfo.queryDetail',
  // 借款详情页: 查询借据详情
  QUERY_DETAIL_NEW: 'mucfc.loan.loanInfo.queryDetail',
  // 贷后服务自主建案提交接口
  POST_LOAN_APPLY_CASE: 'mucfc.postLoan.service.applyCase',
  // 贷后服务申请案件详情查询接口
  POST_LOAN_QUERY_CUST_CASE_DETAIL: 'mucfc.postLoan.service.queryCustCaseDetail',
  // 延后还试算
  DELAY_REPAY_CAL: 'mucfc.postLoan.service.delayRepayCal',
  // 查询推荐问题
  QUERY_RECOMMEND_FAQS: 'mucfc.custservice.gbotchat.queryKycQuestion',
  // 查询用户的优惠券信息，交易接口
  GET_REPAY_COUPON_LIST: 'mucfc.loan.assist.queryAvailableAward',
  // 查询合同配置ais
  CONTRACT_CONFIG: 'mucfc.basic.face.queryContractConfig',
  // 退出登录，不解绑
  LOGIN_OUT: 'mucfc.user.loginRegister.logout',
  // 客户信息查询接口
  GRTCUSTINFO: 'mucfc.user.infoMaintain.getCustInfo',
  // 贷后服务案件申请（新）
  POST_LOAN_CUST_APPLY_CASE: 'mucfc.postLoan.service.custApplyCase',
  // 贷后服务套餐信息查询（新）
  POST_LOAN_QUERY_PACKAGE_INFO: 'mucfc.postLoan.service.queryServicePackage',
  // 展期试算（新）
  POST_LOAN_EXTEND_REPAY_CAL: 'mucfc.postLoan.service.extendRepayTrial',
  // 贷后服务案件详情查询接口（新）
  POST_LOAN_QUERY_CASE_DETAIL: 'mucfc.postLoan.service.queryCaseDetail',
  // 贷后服务案件提交审核接口（新）
  POST_LOAN_SERVICE_SUBMIT_CASE: 'mucfc.postLoan.service.serviceSubmitCase',
  // 贷后服务资料提交（新）
  POST_LOAN_ADD_CASE_DATA: 'mucfc.postLoan.service.addCaseData',
  CHECKSETTLEMENTORNONLOANCERTAPPLY: 'mucfc.loan.certificate.checkSettlementOrNonLoanCertApply', // 开具无贷款证明、结清证明资格校验
  SETTLEMENTORNONLOANCERTAPPLYQUERYSNOWBALLLIST: 'mucfc.loan.certificate.settlementOrNonLoanCertApplyQuerySnowballList', // 无贷款证明、结清证明雪球列表查询
  SETTLEMENTORNONLOANCERTAPPLY: 'mucfc.loan.certificate.settlementOrNonLoanCertApply', // 开具无贷款证明、结清证明
  GET_RECORDS: 'mucfc.loan.invoice.invoiceApplyQuery',
  APPLY_INVOICE: 'mucfc.loan.invoice.invoiceApply',
  RE_SEND: 'mucfc.loan.invoice.invoiceReSend',
  INVOICE_HISTORY: 'mucfc.loan.invoice.invoiceInfoQuery',
};

// 查询用户的优惠券信息-交易特权权益化改造
actions.getCouponList = async (data) => {
  const ret = await fetch(OPERATIONS.QERRY_COUPON_LIST, {
    data
  });
  return ret;
};

// 查询7天待还信息
actions.getNearBills = async (data, opt = {}) => {
  // 因为后台代码先上，后台需兼容蝶变前后的代码，前端额外传newVersionFlag告知后台走蝶变后的代码
  // 需求上线后，该逻辑应当被遗弃，向后台@张辉煌 确认后可以删除newversion的传递
  const res = await fetch(OPERATIONS.RECENT_BILLS, {
    data: {
      newVersionFlag: 'Y', // 新版本标识，Y:新版本；N或者不传：旧版本
      needQueryBadDebtsFlag: 'Y', // 呆账标识，要查呆账的话可以解除注释就行，其他逻辑都是现成的了
      ...data
    }
  }, true);
  const ret = (res && res.data) || {};
  // const ret = nearBillsRet;
  // 需要筛选出可见不可还账单，同时将请求中返回的各种数据赋值给nearBills
  // 由于ret中repayBillList含所有账单，无论可不可还，前端处理，先覆盖repayBillList，后续筛选
  const nearBills = {
    ...ret,
    errCode: res.errCode,
    repayBillList: [],
    showBillList: [],
    overDueTagFlag: false,
    d07Flag: false,
    overdueDays: 0,
  };
  const additionParam = {};
  if (ret.isDueTagCust === 'Y') {
    (ret.repayControlDetailList || []).forEach((item = {}) => {
      if (item.controlCode === 'C401' && item.eventCode === 'GKSJ011') nearBills.d07Flag = true;
      if (item.controlCode === 'C401' && (item.eventCode === 'GKSJ032' || item.eventCode === 'GKSJ033' || item.eventCode === 'GKSJ003')) nearBills.overDueTagFlag = true;
    });
    if (ret.repayBillList) { // 新还款有返回repayBillList
      nearBills.repayBillList = ret.repayBillList.map((bill) => {
        if (bill.overdueDays > nearBills.overdueDays) nearBills.overdueDays = bill.overdueDays;
        return {
          ...bill,
          canPartPayFlag: 'Y',
        };
      });
    } else { // 兼容不返回repayBillList的场景。fix: 接口过度阶段,不兼容没repayBillList导致bug
      nearBills.repayBillList = [{
        canPartPayFlag: 'Y',
        surplusPayTotalAmt: ret.dueRepayInfo && ret.dueRepayInfo.duePayTotalAmt,
      }];
    }
    // 判断setNearAmount纯粹为了避免重复设置
    // 防止以后不经过首页进入账单页面，有时需要知道打标状态，避免重复使用isDueTagCust === 'Y'判断
    // 目前没有实际作用，删了也无妨
    if (!opt.setNearAmount) additionParam.repayStatus = repayStatusType.dueTagCust;
  } else if (ret.repayBillList) {
    ret.repayBillList.forEach((bill) => {
      if (bill.displayStatus === '2') {
        // 因为前端展示时考虑3天宽限期，此处处理，后续逾期判断使用该字段
        // eslint-disable-next-line no-param-reassign
        // bill.displayOverdueDays = bill.surplusDays < -3 ? -bill.surplusDays : 0;
        // 前端统一不考虑3天宽限期了
        // bill.surplusDays < 0认为是逾期的借据
        bill.displayOverdueDays = bill.surplusDays < 0 ? -bill.surplusDays : 0;
        nearBills.repayBillList.push(bill);
      } else if (bill.displayStatus === '0') {
        nearBills.showBillList.push(bill);
      }
    });
  }
  if (opt && opt.onlyLL) {
    nearBills.repayBillList = nearBills.repayBillList.filter(({
      loanType
    }) => loanType === 'I');
    nearBills.showBillList = nearBills.showBillList.filter(({
      loanType
    }) => loanType === 'I');
  }
  console.log(ret, 'ret');
  const contractApplyList = ret.data ? ret.data.contractApplyList : ret.contractApplyList;
  (contractApplyList || []).forEach((i) => {
    if (!i.contractVersion) i.contractVersion = '';
  });
  setStore({
    nearBills,
    uniconHideWxAli: ret.unionHideZfbWec === 'Y',
    contractApplyList,
    ...additionParam
  });
  // 设置首页的近期待还金额和还款状态
  if (opt.setNearAmount) {
    // 处理首页七天待还展示
    let repayStatus = repayStatusType.loading;
    if (ret.isDueTagCust === 'Y' && ret.dueRepayInfo && Number(ret.dueRepayInfo.duePayTotalAmt) > 0) {
      repayStatus = repayStatusType.dueTagCust;
      // eslint-disable-next-line brace-style
    }
    // 由于无借据或当期已还清无法在近期待还接口判断，需要在getFuturePlans中判断
    // repayBillList存在且不为空数组则非上述两种情况，同时排除设置一些未知的状态
    else if (ret.repayBillList && ret.repayBillList.length && Object.values(repayStatusType).indexOf(ret.showStatus) > -1) {
      repayStatus = ret.showStatus;
    }

    let nearBillsTotalAmount = 0;
    if (ret.isDueTagCust === 'Y') {
      nearBillsTotalAmount = ret && ret.dueRepayInfo && (ret.dueRepayInfo.duePayTotalAmt || 0);
    } else {
      nearBillsTotalAmount = (ret && ret.surplusTotalAmt) || 0;
    }
    nearBills.nearBillsTotalAmount = nearBillsTotalAmount;
    setStore({
      nearBillsTotalAmount,
      repayStatus,
    });
  }
  return nearBills;
};

// 查询全部待还信息
actions.getAllBills = async (params, opts = {}, ignoreRet = false) => {
  const res = await fetch(OPERATIONS.ALL_BILLS, {
    data: params
  }, ignoreRet) || {};
  // 需要筛选出可见不可还账单，同时将请求中返回的各种数据赋值给nearBills
  // 由于ret中advanceBillList含所有账单，无论可不可还，前端处理，先覆盖advanceBillList，后续筛选
  const ret = (ignoreRet ? res.data : res) || {};
  const allBills = {
    ...ret,
    advanceBillList: [],
    showBillList: [],
  };
  if (ret && ret.advanceBillList) {
    ret.advanceBillList.forEach((bill) => {
      if (bill.displayStatus === '2') { // 可还
        // 因为前端展示时考虑3天宽限期，此处处理，后续逾期判断使用该字段
        // eslint-disable-next-line no-param-reassign
        // bill.displayOverdueDays = bill.surplusDays < -3 ? -bill.surplusDays : 0;
        // 前端统一不考虑3天宽限期了
        bill.displayOverdueDays = bill.surplusDays < 0 ? -bill.surplusDays : 0;
        allBills.advanceBillList.push(bill);
      } else if (bill.displayStatus === '0') {
        allBills.showBillList.push(bill);
      }
    });
  }
  if (opts && opts.onlyLL) {
    allBills.advanceBillList = allBills.advanceBillList.filter(({
      loanType
    }) => loanType === 'I');
    allBills.showBillList = allBills.showBillList.filter(({
      loanType
    }) => loanType === 'I');
  }
  let advanceBillsTotalAmount = 0;
  if (ret && ret.isDueTagCust === 'Y') {
    advanceBillsTotalAmount = ret && ret.dueRepayInfo && (ret.dueRepayInfo.duePayTotalAmt || 0);
  } else {
    advanceBillsTotalAmount = (ret && ret.surplusTotalAmt) || 0;
  }
  allBills.advanceBillsTotalAmount = advanceBillsTotalAmount;
  setStore({
    allBills,
    uniconHideWxAli: ret.unionHideZfbWec === 'Y'
  });
  return allBills;
};

// 查询用户银行卡列表
// actions.getBankCards = async () => {
//   const dataBody = {
//     cardTypes: ['1'], // 交易卡类型(1:储蓄卡,2:信用卡,3:对公账户,4:支付宝账号,5:支付宝登陆账号,6:招行一网通,7:微信,8:支付宝无忧收,9:支付宝代扣,10:原信用卡待还客户卡,999:未知类型);
//     scene: 'repayment'
//   };
//   if (ChannelConfig.cmbOneNetCardPay) dataBody.cardTypes = ['1', '6'];
//   const ret = await fetch(OPERATIONS.QUERY_BANK_CARDS, {
//     data: dataBody
//   });
//   setStore({
//     bankCards: ret.bankCards
//   });
//   return ret;
// };

// 查询可用银行卡接口
actions.getBankCardsList = async (data) => {
  const dataBody = {
    transScene: 'REPAY',
    ...data
  };
  const ret = await fetch(OPERATIONS.QUERY_BANK_CARDS_LIST, {
    data: dataBody,
    autoLoading: false
  }) || {};
  setStore({
    bankCards: ret.bankCardInfoList || []
  });
  return ret;
};

// 查询预约状态
actions.queryReserveState = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_RESERVE_STATE, {
    data
  });
  setStore({
    reserveRepayInfo: ret
  });
  return ret;
};

/**
 * 立即还款
 * @param { Object } 还款所需参数
 * @return { Boolean } 还款结果
 */
actions.payImmediate = async (params) => {
  const ret = await fetch(OPERATIONS.PAY_IMMEDIATE, {
    data: params,
  }, true);
  return ret;
};

/*
 *  请求用户信息
 *  data is an object
 */
// actions.sessionInfo = async (data) => {
//   const ret = await fetch(OPERATIONS.SESSION_INFO, {
//     data
//   });
//   setStore({
//     sessionInfo: ret && ret.apiUserInfo
//   });
//   return ret && ret.apiUserInfo;
// };

actions.getUserInfo = async (data) => {
  const ret = await fetch(OPERATIONS.SESSION_INFO, {
    data
  });
  return ret;
};

actions.getNewUserInfo = async () => {
  const ret = await fetch(OPERATIONS.SESSION_INFO_NEW);
  // 存储用户信息
  setStore({
    sessionInfo: ret
  });
  return ret;
};

// 判断是否是柳下客
actions.checkGuestCondition = async (data) => {
  const ret = await fetch(OPERATIONS.CHECK_GUEST_CONDITION, {
    data,
    autoLoading: false
  });
  return ret;
};

actions.getAdjustBills = async () => {
  const {
    repayBillList
  } = await fetch(OPERATIONS.RECENT_BILLS);
  const adjustList = [];
  repayBillList && repayBillList.forEach((bill) => {
    if (bill.canExtendInteDate === 'Y' && bill.displayStatus === '2') {
      // 由于业务特殊性，此处直接显示用户的延期天数，不考虑3天宽限期
      // eslint-disable-next-line no-param-reassign
      bill.displayOverdueDays = -bill.surplusDays > 0 ? -bill.surplusDays : 0;
      adjustList.push(bill);
    }
  });
  setStore({
    adjustList
  });
  return adjustList;
};

actions.getExtendRepayBills = async () => {
  const {
    advanceBillList,
    contractApplyList,
    isDueTagCust
  } = await fetch(OPERATIONS.ALL_BILLS, {
    data: {
      queryType: 'delayRepayDay'
    } // 后
  }) || {};
  const extendList = [];
  const cannotExtendList = [];
  let isOverdueCust = false;
  advanceBillList && advanceBillList.forEach((bill) => {
    if (bill.orderExtendPayDate === 'Y') {
      // 因为前端展示时考虑3天宽限期，此处处理，后续逾期判断使用该字段
      // eslint-disable-next-line no-param-reassign
      // bill.displayOverdueDays = bill.surplusDays < -3 ? -bill.surplusDays : 0;
      // 前端统一不考虑3天宽限期了
      bill.displayOverdueDays = bill.surplusDays < 0 ? -bill.surplusDays : 0;
      if (bill.displayOverdueDays) {
        isOverdueCust = true;
      }
      extendList.push(bill);
    }
    if (bill.orderExtendPayDate === 'N') {
      if (bill.displayStatus === '2') {
        bill.displayOverdueDays = bill.surplusDays < 0 ? -bill.surplusDays : 0;
        if (bill.displayOverdueDays) {
          isOverdueCust = true;
        }
      }
      cannotExtendList.push(bill);
    }
  });
  (contractApplyList || []).forEach((i) => {
    if (!i.contractVersion) i.contractVersion = '';
  });
  setStore({
    extendList,
    cannotExtendList,
    contractApplyList: contractApplyList || [],
    isDueTagCust
  });
  // return extendList;
  return {
    extendList,
    cannotExtendList,
    isOverdueCust
  };
};

actions.awardTrial = async (data) => {
  const ret = await fetch(OPERATIONS.AWARD_TRIAL, {
    data
  });
  return ret;
};

actions.alipayCallback = async (data) => {
  const ret = await fetch(OPERATIONS.ALIPAY_CALLBACK, {
    data: {
      result: data.result,
      status: data.resultStatus
    }
  });
  return ret;
};

actions.renewedBillApply = async (data) => {
  const ret = await fetch(OPERATIONS.RENEWED_BILL_APPLY, {
    data
  });
  return ret;
};

actions.getBankOrgList = async () => {
  const {
    banks
  } = await fetch(OPERATIONS.GET_ORGS, {
    data: {
      authType: null,
      scene: 'repayment', // 中台需scene区分银行卡使用场景
      queryType: 'GET_ORGS', // 归属行信息
    }
  });
  return banks || [];
};

actions.changeAmtRepay = async (data) => {
  const ret = await fetch(OPERATIONS.CHANGE_AMOUNT_REPAY, {
    data
  });
  return ret;
};

actions.queryRepayPlan = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_REPAY_PLAN, {
    data
  });
  return ret;
};

actions.queryAdvancedStageInfo = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_ADVANCED_STAGE_INFO, {
    data
  }) || {};
  if (ret.orderInfoCanExtend) {
    ret.orderInfoCanExtend.forEach((orderList) => {
      if (orderList.orderInfoDetails) {
        orderList.orderInfoDetails.forEach((order) => {
          // 因为前端展示时考虑3天宽限期，此处处理，后续逾期判断使用该字段
          // eslint-disable-next-line no-param-reassign
          // order.displayOverdueDays = order.surplusDays < -3 ? -order.surplusDays : 0;
          // 前端统一不考虑3天宽限期了
          order.displayOverdueDays = order.surplusDays < 0 ? -order.surplusDays : 0;
        });
      }
    });
  }
  return ret;
};

actions.advancedStagePay = async (data) => {
  const ret = await fetch(OPERATIONS.ADVANCED_STAGE_REPAY, {
    data
  }, true);
  return ret;
};

actions.advancedStagePayNew = async (data) => {
  const ret = await fetch(OPERATIONS.ADVANCED_STAGE_REPAY_NEW, {
    data
  }, true);
  return ret;
};

actions.queryReinstallRepayInfo = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_EXTEND_REPAY_CAL, {
    data
  }, true);
  return ret;
};

actions.advancedStageApply = async (data = {}) => {
  // const selectedBillList = getStore('selectedBillList') || [];
  const {
    installDetails
  } = getStore('advancedStageInfo') || {};
  const ret = await fetch(OPERATIONS.ADVANCED_STAGE_APPLY, {
    data: {
      transRefNo: Util.getTransRefNo(),
      installDetails,
      ...data
    }
  });
  return ret;
};

actions.checkSupplyInfo = async (scene, types) => {
  const ret = await fetch(OPERATIONS.CHECK_SUPPLY_INFO, {
    data: {
      scence: scene,
      types: types || ['COMPENSATE_ID_INFO', 'COMPENSATE_CONTACT_INFO']
    }
  });
  return ret;
};

actions.getRepayServiceList = async () => {
  // const ret = await fetch(OPERATIONS.GET_SERVICE_LIST);
  const {
    acctPriInfoList
  } = await fetch(OPERATIONS.REPAY_SERVICE_DETAIL, {
    // data: { openScene: 'S01' } // S01博弈互动
    data: {
      openScene: 'S01',
      queryMode: 'ALL'
    } // S01博弈互动
  });
  setStore({
    serviceList: acctPriInfoList || []
  });
  return acctPriInfoList || [];
  // const ret = [
  //   { functionType: 'F01', isSpecialService: 'N' },
  //   { functionType: 'F02', isSpecialService: 'N' },
  //   { functionType: 'F03', isSpecialService: 'Y' },
  //   { functionType: 'F04', isSpecialService: 'N' },
  //   { functionType: 'F05', isSpecialService: 'Y' },
  //   { functionType: 'F06', isSpecialService: 'N' },
  //   { functionType: 'F07', isSpecialService: 'N' },
  //   { functionType: 'F08', isSpecialService: 'N' },
  // ];
  // return ret;
};

actions.getCommonConfig = async (configName) => {
  let config = {};
  try {
    const ret = await fetch(OPERATIONS.COMMON_CONFIG, {
      data: {
        configItemKeys: [{
          configName,
          namespace: 'repayment'
        }]
      }
    });
    config = (ret.configDatas[0] || {}).configData || {};
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log(err);
  }
  return config;
};

actions.getMultipleCommonConfig = async (configName) => {
  let config = {};
  try {
    const ret = await fetch(OPERATIONS.COMMON_CONFIG, {
      data: {
        configItemKeys: configName.map((i) => ({
          configName: i,
          namespace: 'repayment'
        }))
      }
    });
    config = (ret.configDatas[0] || {}).configData || {};
    ((ret && ret.configDatas) || []).forEach((i) => {
      config = {
        ...config,
        ...i.configData
      };
    });
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log(err);
  }
  return config;
};

actions.checkRetain = async (data) => {
  // return {
  //   showType: 5, totalOrder: 2, totalInadvanceFee: 3, totalInterestFee: 4,
  // }
  const ret = await fetch(OPERATIONS.CHECK_RETAIN, {
    data
  });
  return ret;
};

actions.checkIdentity = async (data) => {
  const {
    accountType,
    loanPersonName,
    loanPersonMobile,
    loanPersonIdNo
  } = data;
  const response = await fetch(OPERATIONS.CHECK_IDENTITY_TRANSFER, {
    data: {
      accountType,
      loanPersonName,
      loanPersonMobile,
      loanPersonIdNo
    }
  });
  return response;
};

actions.submitTransferCert = async (data) => {
  const {
    remitImageUrlList,
    remitIdentityDetailList,
    serialNo
  } = data;
  const response = await formDataFetch(OPERATIONS.UPLOAD_TRANSFER_INFO, {
    srcList: {
      remitImageUrlList
    },
    data: {
      remitIdentityDetailList,
      serialNo,
      applySource: 'AUTO'
    }
  });
  return response;
};

actions.sendAccountDetail = async (data) => {
  const response = await fetch(OPERATIONS.CHECK_TRANSFER_PAYWAY, {
    data
  });
  return response;
};

actions.checkReservation = async (reservationType) => {
  const data = reservationType ? {
    reservationType
  } : null;
  const response = await fetch(OPERATIONS.CHECK_RESERVATION, {
    data
  });
  return response;
};

actions.submitReservation = async (data) => {
  const response = await fetch(OPERATIONS.SUBMIT_RESERVATION, {
    data
  });
  return response;
};

actions.cancelReservation = async (data) => {
  const response = await fetch(OPERATIONS.CANCEL_RESERVATION, {
    data
  });
  return response;
};

actions.checkCurrentPayDay = async () => {
  const response = await fetch(OPERATIONS.GET_CURRENT_PAYDAY, {
    data: {
      accountType: '01'
    }
  });
  return response;
};

actions.checkCurrentPayDayNew = async (data, ignoreRet) => {
  const response = await fetch(OPERATIONS.GET_CURRENT_PAYDAY_NEW, {
    data
  }, ignoreRet);
  return response;
};


actions.adjustPayDay = async (data) => {
  const response = await fetch(OPERATIONS.ADJUST_PAYDAY, {
    data: {
      ...data
    }
  }, true);
  return response;
};

// actions.feeReductCal = async (data) => {
//   const response = await fetch(OPERATIONS.REDUCE_FEE_CAL, {
//     data: {
//       waiveSource: 'AUTO',
//       waiveAmtType: '********',
//       waiveType: '02',
//       ...data
//     }
//   });
//   return response;
// };

actions.checkTransferResult = async (data) => {
  const response = await fetch(OPERATIONS.CHECK_TRANSFER_RESULT, {
    data
  });
  return response;
};

actions.checkAlipaySign = async (data) => {
  const ret = await fetch(OPERATIONS.CHECK_ALIPAY_SIGN, {
    data
  });
  return ret;
};

actions.toSignAlipayContract = async (data) => {
  const {
    signUrl
  } = await fetch(OPERATIONS.SIGN_ALIPAY_CONTRACT, {
    data
  });
  return signUrl;
};

actions.acquireFuturePlans = async (data) => {
  // 是否是首页
  // const flag = window.location.hash === '#/index' || window.location.hash === '#/' || window.location.hash === '';
  // const params = flag ? {} : { queryMonth: '1200' }; // // 百年大计~
  const ret = await fetch(OPERATIONS.ACQUIRE_FUTURE_PLANS, {
    data: {
      queryMonth: '1200', // 百年大计~
      // ...params,
      ...data,
    }
  }, true);
  return ret;
};

actions.getExtendRepayDetail = async (data) => {
  const {
    acctPriInfoList
  } = await fetch(OPERATIONS.REPAY_SERVICE_DETAIL, {
    data
  });
  return acctPriInfoList && acctPriInfoList[0] ? acctPriInfoList[0] : {};
};

actions.applyExtendRepay = async (data, fileParam) => {
  const response = await fetch(OPERATIONS.APPLY_EXTEND_REPAY, { data, fileParam });
  return response;
};

actions.commitExtendForm = async (data, fileParam) => {
  const res = await fetch(OPERATIONS.COMMIT_EXTEND_FORM, {
    data,
    fileParam
  });
  return res;
};

/**
 * 获取变更还款日借据试算列表
 * @return { Array } 借据试算列表
 */
actions.getModifyRepayDateList = async (data) => {
  const response = await fetch(OPERATIONS.MODIFY_REPAY_DATE, {
    data
  });
  return response;
};
/** 代还款信息查询 */
actions.offerRepayHelp = async (data) => {
  const response = await fetch(OPERATIONS.OFFER_REPAY_HRLP, {
    data
  });
  return response;
};
/** 代还款信息查询 */
actions.offerRepayApply = async (data) => {
  const res = await fetch(OPERATIONS.OFFER_REPAY_APPLY, {
    data
  }, true);
  return res || {};
};
/** 代还款信息查询 */
actions.offerRepayAmount = async (data) => {
  const response = await fetch(OPERATIONS.OFFER_REPAY_GET_AMOUNT, {
    data
  });
  return response;
};

actions.RepayPretreatment = async (data) => {
  const response = await fetch(OPERATIONS.REPAY_PRE, {
    data
  }, true);
  return response;
};

actions.getAccount = async (data) => {
  const response = await fetch(OPERATIONS.ACCOUNT_INFO_NEW, {
    data
  });
  return response;
};

actions.loanTransTrial = async (data) => {
  const response = await fetch(OPERATIONS.LOAN_TRANS_TRIAL, {
    data
  });
  return response;
};

actions.billInstallApply = async (data) => {
  const ret = await fetch(OPERATIONS.BILL_INSTALL_APPLY, {
    data
  });
  return ret;
};

actions.queryOverpayAccountInfo = async (data) => {
  const ret = await fetch(OPERATIONS.OVER_ACCOUNT_INFO, {
    data
  });
  return ret || {};
};


actions.queryTransRecordSingle = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_TRANS_RECORD_SINGLE, {
    data,
    autoLoading: false
  });
  return ret || {};
};

actions.cancelSendMsg = async (data) => {
  const ret = await fetch(OPERATIONS.CANCEL_SEND_MSG, {
    data
  });
  return ret || {};
};

actions.repayTransTrial = async (data, ignoreRet = false) => {
  const ret = await fetch(OPERATIONS.REPAY_TRANS_TRIAL, {
    data,
    autoLoading: false
  }, ignoreRet);
  return ret || {};
};

actions.repayTransTrialSpecial = async (data) => {
  const ret = await fetch(OPERATIONS.REPAY_TRANS_TRIAL_SPECIAL, {
    data
  });
  return ret || {};
};

actions.queryMerchantInfo = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_MERCHANT_INFO, {
    data
  });
  return ret || {};
};

actions.repayWaiveTrialTrans = async (data, ignoreRet = false) => {
  const ret = await fetch(OPERATIONS.REPAY_WAIVE_TRIAL_TRANS, {
    data
  }, ignoreRet);
  return ret || {};
};

actions.repayWaiveTrialTransSpecial = async (data) => {
  const ret = await fetch(OPERATIONS.REPAY_WAIVE_TRIAL_TRANS_SPECIAL, {
    data
  });
  return ret || {};
};

// 新还款提交接口
actions.normalRepayApply = async (data) => {
  const ret = await fetch(OPERATIONS.NORMAL_REPAY_APPLY, {
    data
  }, true);
  return ret || {};
};

// 息费减免 查询全部待还信息
actions.getReduceFeeAllBills = async (params) => {
  const ret = await fetch(OPERATIONS.ALL_BILLS, {
    data: params
  });
  const allBills = {
    ...ret
  };
  return allBills;
};

// 客户网关关闭还款订单
actions.orderClose = async (data) => {
  const ret = await fetch(OPERATIONS.REPAY_CLOSE, {
    data,
    autoToast: false
  }, true);
  return ret || {};
};

// 查询渠道参数及还款协议
actions.getChannelParams = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_CHANNEL_PARAMS, {
    data
  }, true);
  return ret || {};
};

// 客户账户详情查询接口
actions.queryAccountDetail = () => {
  const { basicCustDto } = getStore('sessionInfo') || {};
  const { custId } = basicCustDto || {};
  return fetch(OPERATIONS.ACCOUNT_DETAIL, {
    data: {
      custNo: custId,
      channelNo: Madp.getChannel(),
      queryType: '01',
      queryUnclearOrderFlag: 'Y',
      queryProductPriceFlag: 'N',
      queryControlInfoFlag: 'Y',
      queryLimitInfoFlag: 'Y',
    }
  });
};

// 二维码还款查询
actions.qrcodeRepayQuery = (data) => fetch('mucfc.repayment.bill.convenientRepayInfoQuery', {
  data
}, true);

// 二维码还款提交
actions.qrcodeRepaySubmit = (data) => fetch('mucfc.repayment.normal.convenientRepayApply', {
  data
});

// 二维码检测用户名
actions.qrcodeRepayCheck = (data) => fetch('mucfc.repayment.bill.userInfoCheck', {
  data
}, true);

// 查询对公转账详情
actions.queryRemitDetail = () => fetch('mucfc.repayment.remit.queryRemitDetail');

// 三方代还调用贷后接口派发博弈券
actions.take216Coupon = (data) => fetch('mucfc.cust.thiDistributionCoupon', { data });

actions.adjustRepayTransTrial = async (data) => {
  const ret = await fetch(OPERATIONS.ADJUST_REPAY_TRANS_TRIAL, {
    data
  }, true);
  return ret || {};
};

actions.sendSms = async (data) => {
  const ret = await fetch(OPERATIONS.SEND_SMS, {
    data
  }, true);
  return ret || {};
};

actions.verifySms = async (data) => {
  const ret = await fetch(OPERATIONS.VERIFY_SMS, {
    data
  }, true);
  return ret || {};
};

/*
 *  请求用户待办信息
 *  data is an object
 */
actions.userTodoInfo = async (data) => {
  const ret = await fetch(OPERATIONS.SESSION_INFO, {
    data
  });
  return ret && ret.userTodoList;
};

// 提交贷后资料通过签署协议实现
actions.submitServiceMaterial = async (data, fileParam) => {
  const ret = await fetch(OPERATIONS.SIGN_ALIPAY_CONTRACT, {
    data,
    fileParam
  }, true);
  return ret;
};

// 权益账户信息查询接口
actions.queryAwardAccount = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_AWARD_ACCOUNT, {
    data
  });
  return ret;
};

// 合同信息查询接口
actions.queryContractInfo = async (data) => {
  const ret = await fetch(OPERATIONS.CONTRACT_INFO, {
    data
  }, true);
  return ret || {};
};

// 查询今日预还款记录
actions.queryTodayPreRepay = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_TODAY_PREREPAY, {
    data
  }, true);
  return ret || {};
};

// 预还款提交接口
actions.PreRepayApply = async (data) => {
  const ret = await fetch(OPERATIONS.PRE_REPAY_APPLY, {
    data
  }, true);
  return ret || {};
};

// 贷后调整准入
actions.adjustAccessCheck = async (data) => {
  const ret = await fetch(OPERATIONS.ADJUST_ACCESS_CHECK, {
    data,
    autoLoading: false,
    autoToast: false
  }, true);
  return ret || {};
};

// 包装延长宽限期账单查询
actions.getCreditProductList = async () => {
  const {
    repayBillList
  } = await fetch(OPERATIONS.RECENT_BILLS);
  const creditProductList = [];
  repayBillList && repayBillList.forEach((bill) => {
    if (bill.displayStatus === '2') {
      // 由于业务特殊性，此处直接显示用户的延期天数，不考虑3天宽限期
      // eslint-disable-next-line no-param-reassign
      bill.displayOverdueDays = -bill.surplusDays > 0 ? -bill.surplusDays : 0;
      creditProductList.push(bill);
    }
  });
  setStore({
    creditProductList
  });
  return creditProductList;
};

// 贷后调整申请
actions.adjustApply = async (data) => {
  const ret = await fetch(OPERATIONS.ADJUST_APPLY, {
    data,
    autoLoading: false,
    autoToast: false
  }, true);
  return ret || {};
};

// 贷后调整申请
actions.extendGracePeriod = async (data) => {
  const ret = await fetch(OPERATIONS.EXTEND_GRACE_PERIOD, {
    data,
    autoToast: false
  }, true);
  return ret || {};
};

// 贷后调整申请
actions.queryCommonTodo = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_TODO, {
    data,
    autoToast: false
  }, true);
  return ret || {};
};

// 编码灰度
actions.queryAbSplit = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_AB_SPLIT, {
    data,
    autoToast: false
  }, true);
  return ret || {};
};

// 推荐优惠券查询
actions.queryRecommendCoupons = async (params) => {
  const ret = await fetch(OPERATIONS.QUERY_RECOMMEND_COUPONS, {
    data: {
      ...params
    },
    autoLoading: false,
  }, true);
  return ret || {};
};

// 获取渠道上下文
actions.getTagContext = async (data) => {
  const ret = await fetch(OPERATIONS.GET_CHANNEL_CONTEXT, {
    data,
    autoLoading: false,
  }, true);
  return ret || {};
};


// 协商还自主申请建案，获取建案相关信息（再分期建案也使用该接口）
actions.applyConsultRepayCase = async (data) => {
  const ret = await fetch(OPERATIONS.APPLY_CONSULT_REPAY_CASE, {
    data,
  }, true);

  return ret || {};
};

// 协商还服务提交建案
actions.submitConsultRepayCase = async (data) => {
  const ret = await fetch(OPERATIONS.SUBMIT_CONSULT_REPAY_CASE, {
    data,
  }, true);

  return ret || {};
};

// 协商还还款状态及还款计划查询
actions.queryRepayPlanAndStatus = async (data) => {
  // mucfc.postLoan.service.queryRepayPlanAndStatus
  const ret = await fetch(OPERATIONS.QUERY_REPAY_PLAN_STATUS, {
    data,
  }, true);

  return ret || {};
};

// 协商还试算
actions.queryNegotiateRepayTrial = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_NEGOTIATE_REPAY_TRIAL, {
    data,
    autoLoading: false
  }, true);
  return ret || {};
};

// 提交案件审核
actions.postLoanSubmitCase = async (data, fileParam) => {
  const ret = await fetch(OPERATIONS.POST_LOAN_SUBMIT_CASE, {
    data,
    autoLoading: !(data && data.ignoreLoading) || false,
    fileParam
  }, true);
  return ret || {};
};

// 提交资料
actions.postLoanAddData = async (data, fileParam) => {
  const ret = await fetch(OPERATIONS.POST_LOAN_ADD_DATA, {
    data,
    fileParam
  }, true);
  return ret || {};
};

// 案件详情查询
actions.queryCustCaseDetail = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_CUST_CASE_DETAIL, {
    data,
    autoLoading: !(data && data.ignoreLoading) || false,
  }, true);
  return ret || {};
};

// 借款详情页: 查询借据详情
actions.queryDetail = async (data) => {
  const response = await fetch(OPERATIONS.QUERY_DETAIL, {
    data,
  }, true);
  return response || {};
};

// 借款详情页: 查询借据详情
actions.queryDetailNew = async (data) => {
  const response = await fetch(OPERATIONS.QUERY_DETAIL_NEW, {
    data,
  }, true);
  return response || {};
};

// 贷后服务自主建案
actions.postLoanApplyCase = async (data) => {
  const ret = await fetch(OPERATIONS.POST_LOAN_APPLY_CASE, {
    data,
    autoLoading: false
  }, true);

  return ret || {};
};

// 贷后服务申请案件详情查询接口
actions.postLoanQueryCustCaseDetail = async (data) => {
  const ret = await fetch(OPERATIONS.POST_LOAN_QUERY_CUST_CASE_DETAIL, {
    data,
    autoLoading: false
  }, true);

  return ret || {};
};

// 延后还试算
actions.delayRepayCal = async (data) => {
  const ret = await fetch(OPERATIONS.DELAY_REPAY_CAL, {
    data,
  }, true);

  return ret || {};
};

// 延后还试算
actions.queryRecommendFaqs = async (data) => {
  const ret = await fetch(OPERATIONS.QUERY_RECOMMEND_FAQS, {
    data,
  }, true);

  return ret || {};
};

// 查询用户的优惠券信息-交易特权权益化改造
actions.getRepayCouponList = async (data) => {
  const ret = await fetch(OPERATIONS.GET_REPAY_COUPON_LIST, {
    data
  });
  return ret;
};

// 查询合同配置（ais）
actions.queryContractConfig = async (data) => {
  const ret = await fetch(OPERATIONS.CONTRACT_CONFIG, {
    data
  }, true);
  return ret || {};
};

// 退出登录
actions.loginOut = async (data) => {
  const ret = await fetch(OPERATIONS.LOGIN_OUT, {
    data,
    autoLoading: false,
    autoToast: false
  }, true);
  return ret;
};

// 客户信息查询接口
actions.getCustInfo = async (data) => {
  const ret = await fetch(OPERATIONS.GRTCUSTINFO, {
    data,
    autoLoading: false,
    autoToast: false
  }, true);
  return ret;
};

// 贷后服务自主建案（新）
actions.postLoanCustApplyCase = async (data) => {
  const ret = await fetch(OPERATIONS.POST_LOAN_CUST_APPLY_CASE, {
    data,
    autoLoading: false
  }, true);

  return ret || {};
};

// 贷后服务套餐信息查询接口
actions.postLoanQueryPackageInfo = async (data) => {
  const ret = await fetch(OPERATIONS.POST_LOAN_QUERY_PACKAGE_INFO, {
    data,
    autoLoading: false
  }, true);

  return ret || {};
};

// 贷后服务展期试算（新）
actions.postLoanExtendRepayCal = async (data) => {
  const ret = await fetch(OPERATIONS.POST_LOAN_EXTEND_REPAY_CAL, {
    data,
    autoLoading: false
  }, true);

  return ret || {};
};

// 贷后服务申请案件详情查询接口（新）
actions.postLoanQueryCaseDetail = async (data) => {
  const ret = await fetch(OPERATIONS.POST_LOAN_QUERY_CASE_DETAIL, {
    data,
    autoLoading: false
  }, true);

  return ret || {};
};

// 贷后服务提交案件接口（新）
actions.postLoanServiceSubmitCase = async (data) => {
  const ret = await fetch(OPERATIONS.POST_LOAN_SERVICE_SUBMIT_CASE, {
    data,
    autoLoading: false
  }, true);

  return ret || {};
};

// 贷后服务案件补充资料接口（新）
actions.postLoanAddCaseData = async (data, fileParam) => {
  const ret = await fetch(OPERATIONS.POST_LOAN_ADD_CASE_DATA, {
    data,
    autoLoading: false,
    fileParam
  }, true);

  return ret || {};
};

/**
 *开具无贷款证明、结清证明资格校验
 */
 actions.checkSettlementOrNonLoanCertApply = async (data) => {
  const response = await fetch(OPERATIONS.CHECKSETTLEMENTORNONLOANCERTAPPLY, {
    data,
    autoLoading: false
  }, true);
  return response;
};

/**
 *无贷款证明、结清证明雪球列表查询
 */
actions.settlementOrNonLoanCertApplyQuerySnowballList = async (data) => {
  const response = await fetch(OPERATIONS.SETTLEMENTORNONLOANCERTAPPLYQUERYSNOWBALLLIST, {
    data,
    autoLoading: false
  }, true);
  return response;
};

/**
 *开具无贷款证明、结清证明
 */
actions.settlementOrNonLoanCertApply = async (data) => {
  const response = await fetch(OPERATIONS.SETTLEMENTORNONLOANCERTAPPLY, {
    data,
    autoLoading: false
  }, true);
  return response;
};

/**
 *查询待开发票
 */
actions.queryRecords = async (data) => {
  const ret = await fetch(OPERATIONS.GET_RECORDS, {
    data,
    autoLoading: true
  });
  return ret;
};

/**
 *申请开票
 */
actions.applyInv = async (data) => {
  const ret = await fetch(OPERATIONS.APPLY_INVOICE, {
    data,
    autoLoading: true
  });
  return ret;
};

/**
 *查询开票历史
 */
actions.queryInvHistory = async (data) => {
  const ret = await fetch(OPERATIONS.INVOICE_HISTORY, {
    data,
    autoLoading: true
  });
  return ret;
};

/**
 *重新开票
 */
actions.reApply = async (data) => {
  const ret = await fetch(OPERATIONS.RE_SEND, {
    data,
    autoLoading: true
  });
  return ret;
};

actions.injectConfigParams = async (configName) => {
  let config = {};
  try {
    const ret = await fetch(OPERATIONS.COMMON_CONFIG, {
      data: {
        configItemKeys: [{
          configName,
          namespace: 'usercenter'
        }]
      }
    });
    config = (ret.configDatas[0] || {}).configData || {};
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log(err);
  }
  return config;
};

export default actions;
