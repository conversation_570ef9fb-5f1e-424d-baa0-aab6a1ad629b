/* eslint-disable max-len */
/**
 * 仅用于发起ajax请求
 *
 */
import fetch from '@utils/mgp-fetch.js';
import OPERATIONS from './api-config';
import {
  setStore
} from '@api/store';
import {
  repayStatusType
} from '@utils/constants';

// 查询用户银行卡列表
const getBankCardList = async (paraDic) => {
  const dataBody = {
    transScene: 'REPAY',
    ...paraDic
  };
  const ret = await fetch(OPERATIONS.QUERY_BANK_CARDS_LIST, {
    data: dataBody
  });

  return ret;
};

const getCouponList = async (paraDic) => {
  const ret = await fetch(OPERATIONS.QERRY_COUPON_LIST, {
    data: paraDic
  });

  return ret;
};

const getRepaymentCommonConfig = async (configName) => {
  const ret = await fetch(OPERATIONS.COMMON_CONFIG, {
    data: {
      configItemKeys: [{
        configName,
        namespace: 'repayment'
      }]
    }
  });

  return ret;
};

// 查询7天待还信息
const getNearBills = async (data, opt = {}) => {
  // 因为后台代码先上，后台需兼容蝶变前后的代码，前端额外传newVersionFlag告知后台走蝶变后的代码
  // 需求上线后，该逻辑应当被遗弃，向后台@张辉煌 确认后可以删除newversion的传递
  const res = await fetch(OPERATIONS.RECENT_BILLS, {
    data: {
      newVersionFlag: 'Y', // 新版本标识，Y:新版本；N或者不传：旧版本
      needQueryBadDebtsFlag: 'Y', // 呆账标识，要查呆账的话可以解除注释就行，其他逻辑都是现成的了
      ...data
    }
  }, true);
  const ret = (res && res.data) || {};
  // const ret = nearBillsRet;
  // 需要筛选出可见不可还账单，同时将请求中返回的各种数据赋值给nearBills
  // 由于ret中repayBillList含所有账单，无论可不可还，前端处理，先覆盖repayBillList，后续筛选
  const nearBills = {
    ...ret,
    errCode: res.errCode,
    repayBillList: [],
    showBillList: [],
    overDueTagFlag: false,
    d07Flag: false,
    overdueDays: 0,
  };
  const additionParam = {};
  if (ret.isDueTagCust === 'Y') {
    (ret.repayControlDetailList || []).forEach((item = {}) => {
      if (item.controlCode === 'C401' && item.eventCode === 'GKSJ011') nearBills.d07Flag = true;
      if (item.controlCode === 'C401' && (item.eventCode === 'GKSJ032' || item.eventCode === 'GKSJ033' || item.eventCode === 'GKSJ003')) nearBills.overDueTagFlag = true;
    });
    if (ret.repayBillList) { // 新还款有返回repayBillList
      nearBills.repayBillList = ret.repayBillList.map((bill) => {
        if (bill.overdueDays > nearBills.overdueDays) nearBills.overdueDays = bill.overdueDays;
        return {
          ...bill,
          canPartPayFlag: 'Y',
        };
      });
    } else { // 兼容不返回repayBillList的场景。fix: 接口过度阶段,不兼容没repayBillList导致bug
      nearBills.repayBillList = [{
        canPartPayFlag: 'Y',
        surplusPayTotalAmt: ret.dueRepayInfo && ret.dueRepayInfo.duePayTotalAmt,
      }];
    }
    // 判断setNearAmount纯粹为了避免重复设置
    // 防止以后不经过首页进入账单页面，有时需要知道打标状态，避免重复使用isDueTagCust === 'Y'判断
    // 目前没有实际作用，删了也无妨
    if (!opt.setNearAmount) additionParam.repayStatus = repayStatusType.dueTagCust;
  } else if (ret.repayBillList) {
    ret.repayBillList.forEach((bill) => {
      if (bill.displayStatus === '2') {
        // 因为前端展示时考虑3天宽限期，此处处理，后续逾期判断使用该字段
        // eslint-disable-next-line no-param-reassign
        // bill.displayOverdueDays = bill.surplusDays < -3 ? -bill.surplusDays : 0;
        // 前端统一不考虑3天宽限期了
        bill.displayOverdueDays = bill.surplusDays < 0 ? -bill.surplusDays : 0;
        nearBills.repayBillList.push(bill);
      } else if (bill.displayStatus === '0') {
        nearBills.showBillList.push(bill);
      }
    });
  }
  if (opt && opt.onlyLL) {
    nearBills.repayBillList = nearBills.repayBillList.filter(({
      loanType
    }) => loanType === 'I');
    nearBills.showBillList = nearBills.showBillList.filter(({
      loanType
    }) => loanType === 'I');
  }
  console.log(ret, 'ret');
  setStore({
    nearBills,
    uniconHideWxAli: ret.unionHideZfbWec === 'Y',
    contractApplyList: ret.data ? ret.data.contractApplyList : ret.contractApplyList,
    ...additionParam
  });
  // 设置首页的近期待还金额和还款状态
  if (opt.setNearAmount) {
    // 处理首页七天待还展示
    let repayStatus = repayStatusType.loading;
    if (ret.isDueTagCust === 'Y' && ret.dueRepayInfo && Number(ret.dueRepayInfo.duePayTotalAmt) > 0) {
      repayStatus = repayStatusType.dueTagCust;
      // eslint-disable-next-line brace-style
    }
    // 由于无借据或当期已还清无法在近期待还接口判断，需要在getFuturePlans中判断
    // repayBillList存在且不为空数组则非上述两种情况，同时排除设置一些未知的状态
    else if (ret.repayBillList && ret.repayBillList.length && Object.values(repayStatusType).indexOf(ret.showStatus) > -1) {
      repayStatus = ret.showStatus;
    }

    let nearBillsTotalAmount = 0;
    if (ret.isDueTagCust === 'Y') {
      nearBillsTotalAmount = ret && ret.dueRepayInfo && (ret.dueRepayInfo.duePayTotalAmt || 0);
    } else {
      nearBillsTotalAmount = (ret && ret.surplusTotalAmt) || 0;
    }
    nearBills.nearBillsTotalAmount = nearBillsTotalAmount;
    setStore({
      nearBillsTotalAmount,
      repayStatus,
    });

    let nearBillsTotalPrincipalAmt = 0;
    if (ret.isDueTagCust === 'Y') {
      nearBillsTotalPrincipalAmt = ret && ret.dueRepayInfo && (ret.dueRepayInfo.duePayTotalPrincipalAmt || 0);
    } else {
      (ret.repayBillList || []).forEach((item) => {
        if (item && item.usedToCalculate === 'Y') {
          nearBillsTotalPrincipalAmt += Number(item.surplusPayPrincipalAmt || 0);
        }
      });
    }
    nearBills.nearBillsTotalPrincipalAmt = Number(nearBillsTotalPrincipalAmt || 0).toFixed(2);
  }
  return nearBills;
};

const acquireFuturePlans = async (data) => {
  const ret = await fetch(OPERATIONS.ACQUIRE_FUTURE_PLANS, {
    data: {
      queryMonth: '1200', // 百年大计~
      ...data,
    }
  }, true);

  return ret;
};

const getRepayServiceList = async () => {
  const ret = await fetch(OPERATIONS.REPAY_SERVICE_DETAIL, {
    data: {
      openScene: 'S01',
      queryMode: 'ALL'
    } // S01博弈互动
  });

  return ret;
};

const getRepayPretreatment = async (data) => {
  const response = await fetch(OPERATIONS.REPAY_PRE, {
    data
  }, true);

  return response;
};

const getAccount = async (data) => {
  const response = await fetch(OPERATIONS.ACCOUNT_INFO_NEW, {
    data
  });

  return response;
};

const getTransferDetail = async (data) => {
  const response = await fetch(OPERATIONS.TRANSFER_DETAIL, {
    data
  });

  return response;
};

const getAllBills = async (data, ignoreRet = false) => {
  const res = await fetch(OPERATIONS.ALL_BILLS, {
    data: data
  }, ignoreRet);

  return res;
};

const checkRetain = async (data) => {
  const ret = await fetch(OPERATIONS.CHECK_RETAIN, {
    data
  });
  return ret;
};


const repayPretreatment = async (data) => {
  const ret = await fetch(OPERATIONS.REPAY_PRE, {
    data
  }, true);

  return ret;
};

// 延期还款（风险）审核接口
const adjustApply = (data) => fetch('mucfc.repayment.adjust.adjustApply', {
  data,
  autoToast: false,
});

// 延期还款审核状态查询接口
const adjustApplyQuery = (data) => fetch('mucfc.repayment.adjust.adjustApplyQuery', {
  data,
  autoLoading: false,
  autoToast: false,
});

// 延期还款申请提交接口
const delayRepayApply = (data) => fetch('mucfc.repayment.adjust.delayRepayApply', {
  data
});

// 延期还款准入查询接口
const adjustAccessCheck = (data) => fetch('mucfc.repayment.adjust.adjustAccessCheck', {
  data
}, true);

// 贷后调整申请资料补充(延期还款人工审核资料提交)
const adjustApplyAddInfo = (data) => fetch('mucfc.repayment.adjust.adjustApplyAddInfo', { data });

// 资格检查
const checkQualify = async (data) => {
  const response = await fetch(OPERATIONS.CHECK_QUALIFY, {
    data,
    autoLoading: false,
    autoToast: false,
  });

  return response;
};

// 资格检查
const checkQualifyNew = async (data) => {
  const response = await fetch(OPERATIONS.CHECK_QUALIFY_NEW, {
    data,
    autoLoading: false,
    autoToast: false,
  });

  return response;
};

export default {
  getBankCardList,
  getCouponList,
  getRepaymentCommonConfig,
  getNearBills,
  acquireFuturePlans,
  getRepayServiceList,
  getRepayPretreatment,
  getAccount,
  getTransferDetail,
  getAllBills,
  checkRetain,
  repayPretreatment,
  adjustApply,
  adjustApplyQuery,
  delayRepayApply,
  adjustAccessCheck,
  adjustApplyAddInfo,
  checkQualify,
  checkQualifyNew
};
