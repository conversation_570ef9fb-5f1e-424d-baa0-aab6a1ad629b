const OPERATIONS = {
  // 查询 7 天待还信息
  RECENT_BILLS: 'mucfc.repayment.bill.recentBill',
  // 未来期账单
  ACQUIRE_FUTURE_PLANS: 'mucfc.repayment.bill.futureBill',
  // 查询全部待还信息
  ALL_BILLS: 'mucfc.repayment.bill.advanceBill',
  // 查询用户银行卡列表
  QUERY_BANK_CARDS: 'mucfc.user.bankCard.queryBankCards',
  // 查询预约状态
  QUERY_RESERVE_STATE: 'mucfc.repayment.remit.queryRepayReservation',
  // 新银行卡接口，ubs迁移到tbs
  QUERY_BANK_CARDS_LIST: 'mucfc.loan.bankcard.queryAvailableBankCardList',
  // 立即还款
  PAY_IMMEDIATE: 'mucfc.repayment.normalRepay.repayApply',
  SESSION_INFO: 'mucfc.user.userInformation.getUserInfo',
  GET_PARAMS_FOR_TRADE: 'mucfc.loan.loanInfo.getParamsForTrade',
  // 账单分期接口
  RENEWED_BILL_APPLY: 'mucfc.loan.renewedBill.renewedBillApply',
  // 优惠券试算
  AWARD_TRIAL: 'mucfc.repayment.normalRepay.awardTrial',
  // 支付宝SDK回调
  ALIPAY_CALLBACK: 'mucfc.repayment.normalRepay.alipaySdkCallback',
  // 获取银行卡还款限额
  GET_ORGS: 'mucfc.user.bankCard.getOrgs',
  // 提前还款试算
  CHANGE_AMOUNT_REPAY: 'mucfc.repayment.normalRepay.repayCalculate',
  // 查询还款计划
  QUERY_REPAY_PLAN: 'mucfc.loan.order.orderQuery',
  // 省心分信息
  QUERY_ADVANCED_STAGE_INFO: 'mucfc.repayment.extend.cal',
  // 省心分提交还款
  ADVANCED_STAGE_REPAY: 'mucfc.repayment.extend.repayApply',
  // 省心分提交
  ADVANCED_STAGE_APPLY: 'mucfc.repayment.extend.apply',
  // 检查补充身份信息场景
  CHECK_SUPPLY_INFO: 'mucfc.loan.check.param',
  // 提交身份补全资料
  SUBMIT_SUPPLY_INFO: 'mucfc.user.person.updatePersonalInfo',
  // 查询个人信息
  QUERY_PERSON_INFO: 'mucfc.user.person.queryPersonalInfo',
  // 参数配置系统
  COMMON_CONFIG: 'mucfc.basic.app.queryConfigParams',
  // 查询还款服务列表
  GET_SERVICE_LIST: 'mucfc.repayment.repayService.query',
  // 挽留弹窗
  CHECK_RETAIN: 'mucfc.repayment.repayStay.trial',
  // 额度信息
  CHECK_CREDIT: 'mucfc.loan.loanInfo.queryCreditsPriceParam',
  // 根据卡号查机构
  CHECK_BANK_ORG: 'mucfc.user.bankCard.getBankName',
  // 汇款流水无主账识别前置检查
  CHECK_IDENTITY_TRANSFER: 'mucfc.repayment.remit.identityCheck',
  // 查询预约状态
  CHECK_RESERVATION: 'mucfc.repayment.remit.queryReservation',
  // 线下还款预约
  SUBMIT_RESERVATION: 'mucfc.repayment.remit.reserveRepay',
  // 线下还款查银行卡，卡号和用户信息有关
  CHECK_TRANSFER_PAYWAY: 'mucfc.repayment.remit.acctQuery',
  // 取消线下预约
  CANCEL_RESERVATION: 'mucfc.repayment.remit.cancelReservation',
  // 查询无主账申请结果
  CHECK_TRANSFER_RESULT: 'mucfc.repayment.remit.applyQuery',
  // 申请无主入账
  UPLOAD_TRANSFER_INFO: 'mucfc.repayment.remit.identity',
  // 查询是否签约支付宝代扣协议
  CHECK_ALIPAY_SIGN: 'mucfc.user.contract.whetherSignContract',
  // 查询签署支付宝代扣协议链接
  SIGN_ALIPAY_CONTRACT: 'mucfc.user.contract.signContract',
  // 获取当前还款日
  GET_CURRENT_PAYDAY: 'mucfc.repayment.repayDay.query',
  // 变更还款日
  ADJUST_PAYDAY: 'mucfc.repayment.repayDay.adjust',
  // 息费减免试算
  // REDUCE_FEE_CAL: 'mucfc.repayment.inteFee.waiveTrail',
  // 查询账户特权
  REPAY_SERVICE_DETAIL: 'mucfc.loan.account.queryPrivilege',
  // 提交延长还款日申请
  APPLY_EXTEND_REPAY: 'mucfc.loan.delay.delayPay',
  // 提交延期还款申请单
  COMMIT_EXTEND_FORM: 'mucfc.custservice.CustomerVoice.addCvApprovalApply',
  // 变更还款日借据试算
  MODIFY_REPAY_DATE: 'mucfc.repayment.repayDay.trial',
  // 还款前置校验
  REPAY_PRE: 'mucfc.repayment.check.preRepayCheck',
  // 代还款查询借款人信息
  OFFER_REPAY_HRLP: 'mucfc.repayment.repayHelp.offerRepayHelp',
  // 代还款 立即还款
  OFFER_REPAY_APPLY: 'mucfc.repayment.repayHelp.repayApply',
  // 新: 客户账户查询接口3.0
  ACCOUNT_INFO_NEW: 'mucfc.loan.account.getAccount',
  // 客户账户资产转让信息
  TRANSFER_DETAIL: 'mucfc.loan.account.queryTransferDetail',
  // 新: 借款试算接口
  LOAN_TRANS_TRIAL: 'mucfc.loan.trial.loanTransTrial',
  // 新：账单分期提交接口
  BILL_INSTALL_APPLY: 'mucfc.loan.billInstall.billInstallApply',
  // 代还款查询待还金额
  OFFER_REPAY_GET_AMOUNT: 'mucfc.repayment.bill.helpRepayInfoQuery',
  // 溢缴款账户查询接口
  OVER_ACCOUNT_INFO: 'mucfc.loan.account.queryOverpayAccountInfo',
  // 查单笔交易记录
  QUERY_TRANS_RECORD_SINGLE: 'mucfc.loan.trans.transRecordSingleQuery',
  // 取消对应交易的短信发送
  CANCEL_SEND_MSG: 'mucfc.user.userCommon.cancelSendDelayMessage',
  // 查询用户的优惠券信息[改券模式-交易特权权益化改造]
  QERRY_COUPON_LIST: 'mucfc.promotion.coupon.queryCouponList',
  // 领取优惠券/查看是否符合还款结清降价
  CHECK_DEPRECIATE_CONDITION: 'mucfc.activity.coupon.take',
  // 判断是否是柳下客
  CHECK_GUEST_CONDITION: 'mucfc.activity.common.runRule',
  // 查看还款降价明细
  CHECK_DEPRECIATE_DETAIL: 'mucfc.activity.adjust.queryTransDetailList',
  // 多功能试算接口，可支持优惠券试算、提前还款试算、逾期试算，账单修改试算
  REPAY_TRANS_TRIAL: 'mucfc.repayment.trial.repayTransTrial',
  // 根据商户号查询商户名称
  QUERY_MERCHANT_INFO: 'mucfc.loan.query.queryMerchantInfo',
  // 客群梯度减免批量试算接口
  REPAY_WAIVE_TRIAL_TRANS: 'mucfc.repayment.trial.repayWaiveTrialTrans',
  // 客户提交还款网关接口，支持多种还款模式
  NORMAL_REPAY_APPLY: 'mucfc.repayment.normal.repayApply',
  // 客户网关关闭还款订单
  REPAY_CLOSE: 'mucfc.repayment.normal.repayClose',
  // 前端查询渠道参数配置, 查合同条款
  QUERY_CHANNEL_PARAMS: 'mucfc.content.channel.queryChannelParam',
  // 贷后服务资格检查
  CHECK_QUALIFY: 'mucfc.postLoan.service.queryService',
  // 贷后服务资格检查（新）
  CHECK_QUALIFY_NEW: 'mucfc.postLoan.service.queryServiceQualify',
};

export default OPERATIONS;
