import Madp from '@mu/madp';
import { getCurrentPageUrlWithArgs } from '@mu/madp-utils';
import { getToken } from '@mu/dev-finger';
import ApiSign from '@mucfc.com/webapp-api-sign/dist/wxapp';
import { apiHost } from '@utils/constants';
const CommonRequest = {};

CommonRequest.getFullUrlWithName = (name) => {
  let mgpHost = apiHost.mgp;
  let fullUrl = `${mgpHost}?operationId=${name}`;
  return fullUrl;
};

CommonRequest.getFinalParamV2 = async (para) => {
  let reqEnvParams = { channel: Madp.getChannel(), appType: 'H5' };
  const pageUrl = getCurrentPageUrlWithArgs();
  let sign = '';
  try {
    sign = ApiSign.sign()._s || '';
  } catch (error) {
    sign = '';
  }
  // 设备指纹引入,接口签名已经默认在 Madp.request集成，无需在环境参数再做处理
  let token = '';
  try {
    token = await getToken(true, Madp.getChannel());
  } catch (err) {
  }
  // 业务mapCode获取，详情查看app.js 的 eventCenter 相关代码
    const mapCode = process.env.TARO_ENV === 'h5'
      ? (Madp.getStorageSync('mapCode', 'SESSION') || '')
      : '';

  reqEnvParams.pageUrl = pageUrl;
  reqEnvParams.token = token;
  reqEnvParams.mapCode = mapCode;
  reqEnvParams.sign = sign;
  let jsonString = '{}';
  if (para) {
    jsonString = JSON.stringify(para);
  }
  return { data: jsonString, reqEnvParams: JSON.stringify(reqEnvParams) };
};

export default CommonRequest;
