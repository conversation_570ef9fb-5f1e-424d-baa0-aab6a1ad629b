/* eslint-disable indent */
import channelConfig from './index';

const themeColor = channelConfig.theme || 'default';
const weapp = process.env.TARO_ENV === 'weapp';
/**
 * 可在channels 中自定义每个channel下对应的主题颜色, 其对应key为`theme`，默认为招联蓝
*/
const setCustomTheme = () => {
  if (weapp) {
    require('../styles/themes/orange.scss');
  } else {
    switch (themeColor) {
      case 'orange':
        // eslint-disable-next-line no-unused-expressions
        import('../styles/themes/orange.scss');
        break;
      case 'red':
      case 'unicomRed':
        // eslint-disable-next-line no-unused-expressions
        import('../styles/themes/red.scss');
        break;
      case 'purple':
        // eslint-disable-next-line no-unused-expressions
        import('../styles/themes/purple.scss');
        break;
      default:
        // eslint-disable-next-line no-unused-expressions
        import('../styles/themes/default.scss');
        break;
      }
  }
};

export default setCustomTheme;
