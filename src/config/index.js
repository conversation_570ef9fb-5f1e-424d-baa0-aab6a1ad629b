/* eslint-disable object-curly-newline */
import Madp from '@mu/madp';
import { Url, isMuapp, isAlipay, isWechat, getEnv } from '@mu/madp-utils';
import { getAllLocalConfigByNs, getLocalConfig } from '@mu/business-basic';
import channels from './channels';

const currentEnv = process.env.BUILD_ENV || getEnv();
const channel = Madp.getChannel();

const getMerchantId = () => {
  const MERCHANTID_V = JSON.stringify(Url.getParam('merchantId'));
  if (MERCHANTID_V) {
    return JSON.parse(MERCHANTID_V);
  }
  return '10000';
};

// 读取线上[local-config文件]配置文件
const localConfig = getAllLocalConfigByNs && getAllLocalConfigByNs('repayment') || {};

// 测试环境公用了同一个
if(currentEnv === 'st1') {
  localConfig.appWechatAppId = 'wxa6a29c0f2bfc6302'
}

// 动态获取的或非string类型的value值维护在本地
const otherConfig = {
  // 以下三个字段维护在common中
  theme:getLocalConfig && getLocalConfig({
    configName: 'theme',
    nameSpace: 'common',
  }),
  // 能否支持支付宝还款
  alipay:getLocalConfig && getLocalConfig({
    configName: 'alipay',
    nameSpace: 'common',
  }),
  // 能否支持微信还款
  wxPay: getLocalConfig && getLocalConfig({
    configName: 'wxPay',
    nameSpace: 'common',
  }),
  
  // 实验室功能
  labFunc: {
    wxPay: '..-..',
    wxPayCallback: () => { }
  },
  // 点击银行卡列表某一银行卡触发
  handleClickCardWrap: () => { },
  // 处理特殊绑卡逻辑，传了当前卡信息
  handleBankcard: () => { },
  priceParams: { // 定价参数
    merchantId: '10000',
    accessPortal: '********',
    transBuisType: 'XFXHFL',
    productNo: 'XFZD001',
  },
  // 合同三要素，用于请求模板，括号为默认值
  // 产品码productCode(ALL),渠道码channelCode(getchannel方法获得),商户号merchantCode(ALL)
  // 商户号不建议用ALL，ALL仅作为兜底，不同渠道使用组件是需要设置对应三要素进行覆盖
  contractParams: {
    channelCode: channel,
    merchantCode: getMerchantId(),
    productCode: 'ALL',
  },
  familyContactList: [
    { code: '11', name: '配偶' },
    { code: '13', name: '父母' },
    { code: '15', name: '子女' },
    { code: '17', name: '兄弟' },
    { code: '18', name: '姐妹' },
  ],
  otherContactList: [
    { code: '20', name: '朋友' },
    { code: '21', name: '同事' },
    { code: '22', name: '同学' },
  ]
}

if (isWechat() || process.env.TARO_ENV === 'weapp') {
  // 有好几个第三方微信不能用微信支付，注意后续开放迁移，具体渠道参考vmuwechat工程
  otherConfig.alipay = false;
  otherConfig.wxPay = true;
}


// 配置合并，优先使用自定义的配置
export default { ...localConfig, ...otherConfig };
