import {
  observable,
  action,
} from 'mobx';
import repaymentApi from '@api/new/repaymentApi';
import {
  setStore
} from '@api/store';
import Util from '@utils/maxin-util';

class IndexStore {
  @observable bankCards = [] // 银行卡
  // 查询可用银行卡接口
  @action.bound
  getBankCardsList = async (data) => {
    let ret = await repaymentApi.getBankCardList(data);
    let {
      bankCardInfoList
    } = ret || {};
    this.bankCards = bankCardInfoList || [];

    return ret || {};
  }

  @observable couponList = [] // 优惠券列表
  @action.bound
  getCouponList = async (data) => {
    let ret = await repaymentApi.getCouponList(data);

    this.couponList = ret;
  }

  @observable repaymentCommonConfig = {}
  @action.bound
  getRepaymentCommonConfig = async (data) => {
    let ret = await repaymentApi.getRepaymentCommonConfig(data);
    let {
      configDatas
    } = ret || {};
    let {
      configData
    } = configDatas && configDatas[0] || {};

    this.repaymentCommonConfig = configData;
  }

  @observable nearBills = {}
  @observable negotiateRepayTaskBill = {}
  // 查询7天待还信息
  @action.bound
  getNearBills = async (data, opt = {}) => {
    const result = await repaymentApi.getNearBills(data, opt);
    this.nearBills = result;
    if (result && result.negotiateRepayTaskBill && (JSON.stringify(result.negotiateRepayTaskBill) !== '{}')) {
      this.negotiateRepayTaskBill = result.negotiateRepayTaskBill;
      // 协商还逾期天数、待还金额
      let billOverdueDays = 0;
      const { overdueDays, repayBillList, nearBillsTotalAmount } = result;
      if (overdueDays && Number(overdueDays) > 0) {
        billOverdueDays = overdueDays;
      } else if (repayBillList && repayBillList.length > 0) {
        repayBillList.forEach((bill) => {
          if (billOverdueDays < bill.displayOverdueDays) billOverdueDays = bill.displayOverdueDays;
        });
      }
      this.negotiateRepayTaskBill.billOverdueDays = billOverdueDays;
      this.negotiateRepayTaskBill.nearBillsTotalAmount = nearBillsTotalAmount;
      // 协商还当期信息；当期应还金额，保留两位小数；当期是否最后一期，Y/N；下期还款日、还款金额；
      const {
        nextDueInstallNo, repayPlanList
      } = result.negotiateRepayTaskBill;
      if (nextDueInstallNo && repayPlanList && repayPlanList.length > 0) {
        this.negotiateRepayTaskBill.showNegotiatePlan = true;
        repayPlanList.forEach((item, i) => {
          this.negotiateRepayTaskBill.repayPlanList[i].date = Util.getDateCollection(item.repayDate);
        });
        /* eslint-disable-next-line max-len */
        this.negotiateRepayTaskBill = Object.assign(this.negotiateRepayTaskBill, { ...repayPlanList.filter((item) => Number(item.installNo) === Number(nextDueInstallNo))[0] });
        const { currentShouldAmt } = this.negotiateRepayTaskBill;
        /* eslint-disable-next-line max-len */
        this.negotiateRepayTaskBill.taskShouldAmt = Number(currentShouldAmt || 0).toFixed(2);
        /* eslint-disable-next-line max-len */
        this.negotiateRepayTaskBill.lastCntFlag = Number(nextDueInstallNo) === repayPlanList.filter((item) => (item.repayStatus !== '3' && item.repayStatus !== '4')).length ? 'Y' : 'N';
        if (Number(this.negotiateRepayTaskBill.taskShouldAmt) <= 0 && this.negotiateRepayTaskBill.lastCntFlag !== 'Y') {
          /* eslint-disable-next-line max-len */
          const nextTaskPlan = repayPlanList.filter((item) => Number(item.installNo) === (Number(nextDueInstallNo) + 1))[0] || {};
          this.negotiateRepayTaskBill.nextRepayDate = nextTaskPlan.repayDate;
          this.negotiateRepayTaskBill.nextTaskShouldAmt = nextTaskPlan.taskThresholdAmt;
        }
      }
    }
  }

  @observable futurePlans = {}
  @action.bound
  acquireFuturePlans = async (data) => {
    let ret = await repaymentApi.acquireFuturePlans(data);

    this.futurePlans = ret;
  }

  // 博弈互动 获取还款特权
  @observable serviceList = []
  @action.bound
  getRepayServiceList = async (data) => {
    let result = await repaymentApi.getRepayServiceList(data);
    let {
      acctPriInfoList
    } = result || {};

    setStore({
      serviceList: acctPriInfoList || []
    });
    this.serviceList = acctPriInfoList;
  }


  // 还款前校验接口, 特殊接口把ret判断逻辑拿出fetch.js
  @observable repayPretreatment = {}
  @action.bound
  getRepayPretreatment = async (data) => {
    let result = await repaymentApi.getRepayPretreatment(data);

    this.repayPretreatment = result;
  }

  // 获取账户信息列表
  @observable accountInfoList = []
  @observable awardInfoList = []
  @observable diversionBillInfo = {}
  @action.bound
  getAccount = async (data) => {
    const result = await repaymentApi.getAccount(data);
    const {
      accountList, awardInfoList, diversionBillInfo,
    } = result || {};
    this.accountInfoList = accountList || [];
    this.awardInfoList = awardInfoList || [];
    this.diversionBillInfo = diversionBillInfo || {};
  }

  // 获取账户资产转让信息
  @observable assetTransferInfo = []
  @action.bound
  getTransferDetail = async (data) => {
    const result = await repaymentApi.getTransferDetail(data);
    this.assetTransferInfo = result || {};
  }

  // 贷后服务资格检查
  @observable qualifyFlag = 'N'
  @observable negotiateRepayPackageList = []
  @action.bound
  checkQualify = async (data) => {
    let result = await repaymentApi.checkQualify(data);
    const {
      negotiateRepayPackageInfo
    } = result || {};
    if (negotiateRepayPackageInfo && JSON.stringify(negotiateRepayPackageInfo) !== '{}') {
      this.qualifyFlag = 'Y';
      this.negotiateRepayPackageList = negotiateRepayPackageInfo.negotiateRepayPackageList;
    }
  }

  // 贷后服务资格检查（新）
  @observable billExtendQualifyFlag = ''
  @observable advancedStageQualifyFlag = ''
  @observable consultQualifyFlag = ''
  @observable renewLoansQualifyFlag = ''
  @observable repayPromiseFlag = ''
  @action.bound
  checkQualifyNew = async (data) => {
    let result = await repaymentApi.checkQualifyNew(data);
    const {
      supportServiceList
    } = result || {};
    (supportServiceList || []).forEach((item) => {
      const {
        serviceType, applyNo, couponNo, strategyCustCode, installCnt
      } = item || {};
      const formatStrategyCustCode = (strategyCustCode || '').slice(0, 3);
      switch (serviceType) {
        case '001': // 延后还
          if (applyNo || couponNo || formatStrategyCustCode === 'I11' || formatStrategyCustCode === 'I13') {
            this.billExtendQualifyFlag = 'Y';
          } else {
            this.billExtendQualifyFlag = 'N';
          }
          break;
        case '002': // 再分期
          if (applyNo || couponNo || formatStrategyCustCode === 'I21' || formatStrategyCustCode === 'I22' || formatStrategyCustCode === 'I23') {
            this.advancedStageQualifyFlag = 'Y';
          } else {
            this.advancedStageQualifyFlag = 'N';
          }
          break;
        case '006': // 协商还
          if (Number(installCnt || 0) > 0) {
            this.consultQualifyFlag = 'Y';
          } else {
            this.consultQualifyFlag = 'N';
          }
          break;
        case '016': // 分期还本
          this.renewLoansQualifyFlag = 'Y';
          break;
        case '010': // 还款承诺
          this.repayPromiseFlag = 'Y';
          break;
        default:
          break;
      }
    });
    this.billExtendQualifyFlag = this.billExtendQualifyFlag || 'N';
    this.advancedStageQualifyFlag = this.advancedStageQualifyFlag || 'N';
    this.consultQualifyFlag = this.consultQualifyFlag || 'N';
    this.renewLoansQualifyFlag = this.renewLoansQualifyFlag || 'N';
    this.repayPromiseFlag = this.repayPromiseFlag || 'N';
  }
}

export default IndexStore;
