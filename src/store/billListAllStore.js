import {
  observable,
  action,
} from 'mobx';
import repaymentApi from '@api/new/repaymentApi';
import {
  setStore
} from '@api/store';

class BillListAllStore {
  @observable allBills
  @observable hasControlTag = false;

  @action.bound
  getAllBills = async (data, opts = {}, ignoreRet = false) => {
    let result = await repaymentApi.getAllBills(data, ignoreRet);

    // 需要筛选出可见不可还账单，同时将请求中返回的各种数据赋值给nearBills
    // 由于ret中advanceBillList含所有账单，无论可不可还，前端处理，先覆盖advanceBillList，后续筛选
    const ret = (ignoreRet ? result.data : result) || {};
    const allBills = {
      ...ret,
      advanceBillList: [],
      showBillList: [],
      negotiateRepayTaskBill: (ret && ret.negotiateRepayTaskBill) || {},
    };
    (ret.repayControlDetailList || []).forEach((item = {}) => {
      if (item.controlCode === 'C402') this.hasControlTag = true;
    });
    if (ret && ret.advanceBillList) {
      ret.advanceBillList.forEach((bill) => {
        if (bill.displayStatus === '2') {
          // 因为前端展示时考虑3天宽限期，此处处理，后续逾期判断使用该字段
          // eslint-disable-next-line no-param-reassign
          // bill.displayOverdueDays = bill.surplusDays < -3 ? -bill.surplusDays : 0;
          // 前端统一不考虑3天宽限期了
          bill.displayOverdueDays = bill.surplusDays < 0 ? -bill.surplusDays : 0;
          allBills.advanceBillList.push(bill);
        } else if (bill.displayStatus === '0') {
          allBills.showBillList.push(bill);
        }
      });
    }
    if (opts && opts.onlyLL) {
      allBills.advanceBillList = allBills.advanceBillList.filter(({
        loanType
      }) => loanType === 'I');
      allBills.showBillList = allBills.showBillList.filter(({
        loanType
      }) => loanType === 'I');
    }
    setStore({
      allBills,
      uniconHideWxAli: ret.unionHideZfbWec === 'Y'
    });

    this.allBills = allBills;
  }

  @observable repaymentCommonConfig = {}

  @action.bound
  getRepaymentCommonConfig = async (data) => {
    let ret = await repaymentApi.getRepaymentCommonConfig(data);
    let {
      configDatas
    } = ret || {};
    let {
      configData
    } = configDatas && configDatas[0] || {};

    this.repaymentCommonConfig = configData;
  }

  @observable checkRetainData = {}
  @action.bound
  checkRetain = async (data) => {
    const ret = await repaymentApi.checkRetain(data);

    this.checkRetainData = ret;
  }

  @observable repayPretreatment = {}
  @action.bound
  getRepayPretreatment = async (data) => {
    const ret = await repaymentApi.repayPretreatment(data);

    this.repayPretreatment = ret;
  }
}

export default BillListAllStore;
