import {
  observable,
  action,
} from 'mobx';
import repaymentApi from '@api/new/repaymentApi';
import Dispatch from '@api/actions';

class RepaymentStore {
  @observable accountInfoList = []

  @action.bound
  queryAccountInfo = async (data, opts = {}, ignoreRet = false) => {
    const {
      accountList: accountInfoList = []
    } = await Dispatch.repayment.getAccount({
      queryScene: '10'
    }) || {};

    this.accountInfoList = accountInfoList;
  }

  @observable repaymentCommonConfig = {}

  @action.bound
  getRepaymentCommonConfig = async (data) => {
    let ret = await repaymentApi.getRepaymentCommonConfig(data);
    let {
      configDatas
    } = ret || {};
    let {
      configData
    } = configDatas && configDatas[0] || {};

    this.repaymentCommonConfig = configData;
  }

  @observable checkRetainData = {}
  @action.bound
  checkRetain = async (data) => {
    const ret = await repaymentApi.checkRetain(data);

    this.checkRetainData = ret;
  }

  @observable repayPretreatment = {}
  @action.bound
  getRepayPretreatment = async (data) => {
    const ret = await repaymentApi.repayPretreatment(data);

    this.repayPretreatment = ret;
  }
}

export default RepaymentStore;
