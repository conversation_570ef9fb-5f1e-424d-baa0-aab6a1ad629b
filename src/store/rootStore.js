import indexStore from './indexStore';
import billListAllStore from './billListAllStore';
import repaymentStore from './repaymentStore';
import billExtendStore from './billExtendStore';

const repaymentGlobalStore = {
  indexStore,
  billListAllStore,
  repaymentStore,
  billExtendStore
};

export default class GlobalStore {
  constructor() {
    const self = this;
    Object.keys(repaymentGlobalStore).forEach((k) => {
      self[k] = new repaymentGlobalStore[k]() || {};
      Object.defineProperty(self[k], 'repaymentGlobalStore', {
        get() {
          return self;
        }
      });
    });
  }
}
