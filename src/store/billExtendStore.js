import {
  observable,
  action,
  flow,
  computed
} from 'mobx';
import repaymentApi from '@api/new/repaymentApi';
import Dispatch from '@api/actions';
import Util from '@utils/maxin-util';
import { couponFilter } from '@utils/repay-util';
import { getStore, setStore } from '@api/store';

// 延期还款状态管理，后续可以扩展到整个特享还款服务
class BillExtendStore {
  @observable applyScene = getStore('applyScene') || ''; // 1 催收通用场景, 2 催收纾困场景, 3 微光卡, 4 客户常态化经营, 5 突发场景
  @observable awardInfoList = []; // 资格券
  @observable orderList = ''; // 申请调整的借据
  @observable adjustCnt = ''; // 申请延后期数
  @observable serialNo = getStore('applyNo') || ''; // 审核流水号
  @observable userTag = getStore('userTag') || 'normal'; // 用户标签，用于显示对应的banner图片 normal:一般用户 < delay:逾期用户 < v:v+用户 < benefitcard:微光卡用户
  @observable cannotExtendList = []; // 不可延期借据

  @observable nextRepayDate = getStore('nextRepayDate') || '';
  @observable custType = getStore('custType') || ''; // 人工审核资料页 09-受灾群众 10-疫情确诊人群 11-自雇受困人群 12-灾/疫区援助人员 99-其他

  // 初始化storage
  @action.bound
  clearStorage = flow(function* () {
    setStore({
      applyScene: '',
      userTag: 'normal',
      custType: '',
      applyNo: '',
    });
  })

  // 延期还款（风险）审核接口
  @action.bound
  adjustApplyAudit = async (data, nextRepayDate) => {
    setStore({ nextRepayDate });
    const ret = await repaymentApi.adjustApply(data);
    const { applyNo } = ret || {};
    setStore({ applyNo });
    this.serialNo = applyNo || '';
    return ret || {};
  }

  // 延期还款审核状态查询接口
  @action.bound
  adjustApplyQuery = async (data) => {
    if (!this.serialNo) return;
    let ret = await repaymentApi.adjustApplyQuery({ applyNo: this.serialNo });
    return ret || {};
  }

  // 提交人工审核资料
  @action.bound
  adjustApplyAddInfo = async (data) => {
    const { additionalApplyInfo } = data || {};
    const { custType } = additionalApplyInfo || {};
    setStore({ custType });
    const params = {
      ...data,
      applyNo: this.serialNo,
    };
    let ret = await repaymentApi.adjustApplyAddInfo(params);
    return ret || {};
  }

  // 提交延期还款
  @action.bound
  delayRepayApply = async (data) => {
    const params = {
      ...data,
      applyNo: this.serialNo,
    };
    let ret = await repaymentApi.delayRepayApply(params);
    return ret || {};
  }

  // 延期还款准入查询
  @action.bound
  adjustAccessCheck = async () => {
    const ret = await repaymentApi.adjustAccessCheck({
      adjustType: '1'
    });
    return ret || {};
  }

  // 用于判断v+客户
  @action.bound
  queryAwardAccount= async () => {
    const { awardAccountInfo } = await Dispatch.repayment.queryAwardAccount() || {};
    if (awardAccountInfo) {
      const { accountStatus, awardAccountLevel } = awardAccountInfo;
      if (accountStatus === 1 && awardAccountLevel === 9) {
        return true;
      }
    }
    return false;
  }

  // 初始化借据列表
  @action.bound
  initBills = flow(function* () {
    yield Dispatch.repayment.getExtendRepayBills();
  })

  @computed get isOverdue() {
    let overdueDayFlag = false;
    const extendList = getStore('extendList');
    const isDueTagCust = getStore('isDueTagCust');
    extendList.forEach((bill) => {
      if (bill.displayOverdueDays > 0) {
        overdueDayFlag = true;
      }
    });
    return overdueDayFlag || isDueTagCust === 'Y';
  }
}

export default BillExtendStore;
