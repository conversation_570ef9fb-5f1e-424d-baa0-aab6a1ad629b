$color-brand: #E60027;
$color-brand-light: #E60027;
$color-brand-dark: #E60027;
$color-brand-bg: rgba(230, 0, 39, 0.15);

@import '../common.scss';
// 问卷组件，需要业务模块区分主题色
@import "~@mu/survey/dist/code/ButtonListPro/index.scss";

// MUText支持换肤前，先自定义类
.brand-text {
  color: $color-brand !important;
}

.brand-fill {
  fill: $color-brand !important;
}

.brand-border {
  border-color: $color-brand !important;
}

.protocal-link {
  color: $color-brand !important;
}

.brand-bg {
  background: $color-brand !important;
}

.brand-selected {
  color: $color-brand !important;
  background: $color-brand-bg !important;
}

.mu-dialog__footer .mu-dialog__action .at-button.button-2 {
  background: $color-brand;
}

.at-button--primary {
  background: $color-brand !important;
  border-color: $color-brand !important;
}

.brand-circle {
  background: $color-brand;
  border-color: $color-brand;
}

.card-quota-item.header .bank {
  background: $color-brand !important;
  opacity: 1;
}

.card-quota-item.header .limit {
  background: $color-brand !important;
  opacity: 0.7;
}

.card-quota-item.header .count {
  background: $color-brand !important;
  opacity: 0.5;
}

.check_but {
  color: $color-brand !important;
}

.textTwo {
  color: $color-brand !important;
}

.at-input__input {
  caret-color: $color-brand !important;
}

.repay-detain-dialog__btn-short__confirm {
  background: $color-brand !important;
}

.mu-text__default__brand {
  color: $color-brand !important;
}

:export {
  brand: $color-brand;
  brandlight: $color-brand-light;
  branddark: $color-brand-dark;
}
