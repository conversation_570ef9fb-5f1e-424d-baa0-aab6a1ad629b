$color-brand: #FE7F05;
$color-brand-light: #F76F63;
$color-brand-dark: #F76F63;
$color-brand-bg: #FFE7D0;

@import '../common.scss';
// 问卷组件，需要业务模块区分主题色
@import "~@mu/survey/dist/code/ButtonListPro/index.scss";

// MUText支持换肤前，先自定义类
// MUText支持换肤前，先自定义类
.brand-text {
  color: $color-brand !important;
}

.brand-fill {
  fill: $color-brand !important;
}

.brand-border {
  border-color: $color-brand !important;
}

.protocal-link {
  color: $color-brand !important;
}

.brand-bg {
  background: $color-brand !important;
}

.brand-selected {
  color: $color-brand !important;
  background: $color-brand-bg !important;
}

.mu-dialog__footer .mu-dialog__action .at-button.button-2 {
  background: $color-brand;
}

.at-button--primary {
  background: $color-brand !important;
}

.brand-circle {
  background: $color-brand;
  border-color: $color-brand;
}

:export {
  brand: $color-brand !important;
  brandlight: $color-brand-light !important;
  branddark: $color-brand-dark !important;
}
