$color-brand: #3477FF;
$color-brand-light: #3477FF;
$color-brand-dark: #3477FF;
$color-brand-bg: #E1EBFF;

@import '../common.scss';
// 问卷组件，需要业务模块区分主题色
@import "~@mu/survey/dist/code/ButtonListPro/index.scss";

// MUText支持换肤前，先自定义类
.brand-text {
  color: $color-brand !important;
}

.brand-fill {
  fill: $color-brand !important;
}

.brand-border {
  border-color: $color-brand !important;
}

.protocal-link {
  color: $color-brand !important;
}

.brand-bg {
  background: $color-brand !important;
}

.brand-selected {
  color: $color-brand !important;
  background: $color-brand-bg !important;
}

.mu-dialog__footer .mu-dialog__action .at-button.button-2 {
  background: $color-brand;
}

.at-button--primary {
  background: $color-brand !important;
}

.brand-circle {
  background: $color-brand;
  border-color: $color-brand;
}

:export {
  brand: $color-brand;
  brandlight: $color-brand-light;
  branddark: $color-brand-dark;
}
