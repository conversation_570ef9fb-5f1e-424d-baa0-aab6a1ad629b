<!DOCTYPE html>
<html>
<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
  <meta content="width=device-width,initial-scale=1,user-scalable=no" name="viewport">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-touch-fullscreen" content="yes">
  <meta name="format-detection" content="telephone=no,address=no">
  <meta name="apple-mobile-web-app-status-bar-style" content="white">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" >
  <meta name="viewport" content="viewport-fit=cover, width=device-width, initial-scale=1, user-scalable=0, minimun-scale=1, maximun-scale=1">
  <link rel="preconnect" href="https://m-zl.mucfc.com/">
  <link rel="preconnect" href="https://mgp.api.mucfc.com/">
  <link rel="dns-prefetch" href="https://m-zl.mucfc.com/">
  <link rel="dns-prefetch" href="https://mgp.api.mucfc.com/">
  <title>还款</title>
  <link rel="preload" as="script" href="../muwa/madp-fe-script1.sc.js?version=1.0.0">
  <link rel="preload" as="script" href="../muwa/madp-fe-script2.sc.js?version=1.0.0">
  <link rel="preload" as="script" href="../muwa/madp-fe-script3.sc.js?version=1.0.0">
  <link rel="preload" as="script" href="../operation/cdn/config.sc.js">
  <link rel="preload" as="script" href="../operation/cdn/components_OpRepayment.umd.sc.js">
<script>
  window._MU_MODULE_VERSION = '3.7.20-50';

  function getAllCookies() {
    var cookies = document.cookie.split(";");
    var cookieObj = {};
    for (var i = 0; i < cookies.length; i++) {
      var cookieArr = cookies[i].split("=");
      var key = cookieArr[0].replace(/^s+|s+$/g, '');
      var value = cookieArr[1];
      cookieObj[key] = value;
    }
    return cookieObj;
  }

  var cookies = getAllCookies();
  var greyCookie = cookies.grey;
  var date = new Date();
  date.setTime(date.getTime() + (7 * 24 * 60 * 60 * 1000));
  if (!greyCookie) {
    if (Math.random() <= 0.0001) {
      document.cookie = 'grey=1;expires=' + date.toUTCString() + ';path=/;domain=.mucfc.com';
      location.reload();
    } else {
      document.cookie = 'grey=0;expires=' + date.toUTCString() + ';path=/;domain=.mucfc.com';
    }
  }

 </script>
  <link rel="preconnect" href="https://file.mucfc.com">
  <link rel="preconnect" href="https://arms-retcode.aliyuncs.com">
  <link rel="preconnect" href="https://das.api.mucfc.com">
  <script language=javascript>
    // 还款打开时间的起始点
    var TARO_REPAYMENT_STARTTIME = new Date().getTime();
    // 性能优化
    var hostname = window.location.hostname;
    var _env = (
      hostname.indexOf('localhost') !== -1 ||
      hostname.indexOf('127.0.0.1') !== -1
    ) ? 'se' : 'prod';

    var matches = /(st\d|se\d?|uat)/.exec(hostname);
    if (matches) {
      _env = matches[0];
    }
    var _path = _env === 'prod' ? '.mucfc.com' : '-' + _env + '.cfcmu.cn';
  </script>
  <script>
    !function(x){function w(){var v,u,t,tes,s=x.document,r=s.documentElement,a=r.getBoundingClientRect().width;if(!v&&!u){var n=!!x.navigator.appVersion.match(/AppleWebKit.*Mobile.*/);v=x.devicePixelRatio;tes=x.devicePixelRatio;v=n?v:1,u=1/v}if(a>=640){r.style.fontSize="40px"}else{if(a<=320){r.style.fontSize="20px"}else{r.style.fontSize=a/320*20+"px"}}}x.addEventListener("resize",function(){w()});w()}(window);
  </script>
</head>
<body>
  <div id="app"></div>
  <script>
  	//定义三段式版本号比较大小的函数
  	function compareVersion(v1, v2) {
        var parts1 = v1.split('.').map(Number);
        var parts2 = v2.split('.').map(Number);
        var num1, num2;
        for (var i = 0; i < parts1.length || i < parts2.length; i++) {
          num1 = parts1[i] || 0;
          num2 = parts2[i] || 0;

          if (num1 > num2) return 1;
          if (num1 < num2) return -1;
        }
        return 0;
    }
  
    //定义是否需要执行document.write xxx.js的条件函数
    function needDocumentWriteJS() {
        var ua = window.navigator.userAgent.toLowerCase();
        var munaRegex = /muna\((\d+\.\d+\.\d+)\)/; //取ua中muna版本号的正则表达式
	    var munaMatch = ua.match(munaRegex); //正则匹配
	    var currentMuna = '0.0.0'; //设置初始值
	    if (munaMatch) {
            currentMuna = munaMatch[1]; //正则提取出当前muna版本号
        }
	    var targetMuna = '4.12.0'; //优化后的muna版本为4.12.0
      var isMusdkAndroid = ((/musdk/i).test(ua) && (/android/i).test(ua)); //匹配是否是浦发安卓（musdk 渠道-浦发 App 用的是我们提供的 sdk，里面的标识是 musdk）

        //只有muapp渠道且muna版本号小于4.12.0，才需要document.write xxx.js
        if (((/muapp\//i).test(ua) || isMusdkAndroid)  && compareVersion(currentMuna, targetMuna) === -1) {
            return true;
        }
        //其余情况都不需要
        return false;
    }
    if (needDocumentWriteJS()) {
      document.write(
         '<script src="https://munaResource/HttpRequestPlugin.js"><\/script>'
        +'<script src="https://munaResource/UiKitPlugin.js"><\/script>'
        +'<script src="https://munaResource/PagerJumpPlugin.js"><\/script>'
        +'<script src="https://munaResource/BankCardPlugin.js"><\/script>'
        +'<script src="https://munaResource/ContactPlugin.js"><\/script>'
        +'<script src="https://munaResource/H5TitleBarPlugin.js"><\/script>'
        +'<script src="https://munaResource/GeolocationPlugin.js"><\/script>'
        +'<script src="https://munaResource/CameraPlugin.js"><\/script>'
        +'<script src="https://munaResource/SharePlugin.js"><\/script>'
        +'<script src="https://munaResource/AliPayPlugin.js"><\/script>'
        +'<script src="https://munaResource/WeChatPayPlugin.js"><\/script>'
        +'<script src="https://munaResource/DeviceFingerPlugin.js"><\/script>'
        +'<script src="https://munaResource/HtmlCopyPlugin.js"><\/script>'
        +'<script src="https://munaResource/IdScannerPlugin.js"><\/script>'
        +'<script src="https://munaResource/LocalBiometricPlugin.js"><\/script>'
        +'<script src="https://munaResource/RemotePreferencePlugin.js"><\/script>'
        +'<script src="https://munaResource/WebViewLongPressPlugin.js"><\/script>'
        +'<script src="https://munaResource/UploadFilePlugin.js"><\/script>'
        +'<script src="https://munaResource/NotificationPlugin.js"><\/script>'
        +'<script src="https://munaResource/ApplyOrderPlugin.js"><\/script>'
        +'<script src="https://munaResource/TransferPaymentPlugin.js"><\/script>'
        +'<script src="https://munaResource/UpdateApplyInfoPlugin.js"><\/script>'
        +'<script src="https://munaResource/BasePlugin.js"><\/script>'
        +'<script src="https://munaResource/LoginRegisterPlugin.js"><\/script>'
        +'<script src="https://munaResource/AppPlugin.js"><\/script>'
        +'<script src="https://munaresource/NotificationPlugin.js"><\/script>'
      );
    }
  </script>
  <script>
    if (typeof window.__wxjs_environment !== 'undefined' && window.__wxjs_environment === 'miniprogram') {
      var script = document.createElement('script');
      script.src = 'https://res.wx.qq.com/open/js/jweixin-1.3.2.js';
      script.defer = true;
      document.body.appendChild(script);
    }
    if (navigator.userAgent.indexOf('AliApp') > -1) {
      document.writeln('<script src="https://appx/web-view.min.js" defer' + '>' + '<' + '/' + 'script>');
      // 支付宝交易号唤起支付
      // 参考文档 https://myjsapi.alipay.com/alipayjsapi/util/pay/tradePay.html#3__E6_94_AF_E4_BB_98_E5_AE_9D_E4_BA_A4_E6_98_93_E5_8F_B7_E5_94_A4_E8_B5_B7_E6_94_AF_E4_BB_98
      document.writeln('<script src="https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.inc.min.js" defer' + '>' + '<' + '/' + 'script>');
    }
  </script>
  <script src="../muwa/madp-fe-script1.sc.js?version=1.0.0"></script>
  <script src="../muwa/madp-fe-script2.sc.js?version=1.0.0"></script>
  <script src="../muwa/madp-fe-script3.sc.js?version=1.0.0"></script>
</body>
</html>
