@import '~@mu/zui/dist/style/index.scss';
body {
  background-color: #f3f3f3;
  height: 100%;
}

#app {
  background-color: #f3f3f3;
  height: 100%;
  min-height: 100vh;
}

.at-frozen {
  position: fixed;
  width: 100%;
  overflow: hidden;
}

.original-detail-item-frozen {
  position: fixed;
  width: 100%;
  overflow: hidden;
}

.reinstallment-frozen {
  position: fixed;
  width: 100%;
  overflow: hidden;
}

/*
  修复safari浏览器中，input标签在disable属性下，透明度变化的问题
  https://stackoverflow.com/questions/262158/disabled-input-text-color-on-ios
*/
input:disabled {
  -webkit-text-fill-color: currentcolor; /* 1. sets text fill to current `color` for safari */
  opacity: 1; /* 2. correct opacity on iOS */
}

@font-face {
  font-family: DINAlternate;
  src: url("./styles/DIN-Alternate-Bold.ttf");
}

.agreement-drawer {
  .at-tabs {
    &__header {
      &--flex {
        align-items: stretch;
      }
    }
  }
}

@import '~@mu/agreement/dist/style/components/drawer.scss';
@import '~@mu/chat-entry-component/dist/style/components/index.scss';
@import '~@mu/safe-sms-shell/dist/style/index.scss';
@import '~@mu/survey/dist/style/index.scss';

//~@mu/lui按需引入;
// 由于lui做了styleIsolation配置（小程序中最近可突破样式隔离），所以不需要在app.jsx入口设置lui样式
// @import '~@mu/lui/dist/style/components/standard-detain-dialog.scss';
// @import '~@mu/lui/dist/style/components/subscribe-banner.scss';
// @import '~@mu/lui/dist/style/components/marquee-tips.scss';
// @import '~@mu/lui/dist/style/components/pick-reward.scss';
// @import '~@mu/lui/dist/style/components/banner-with-title.scss';
// @import '~@mu/lui/dist/style/components/multi-func-range.scss';
// @import '~@mu/lui/dist/style/components/dialog.scss';
// @import '~@mu/lui/dist/style/components/banner-without-border.scss';
// @import '~@mu/lui/dist/style/components/repay-detain-dialog.scss';
// @import '~@mu/lui/dist/style/components/public-detain-dialog.scss';
// @import '~@mu/lui/dist/style/components/loan-index-banner.scss';
// @import '~@mu/lui/dist/style/components/privilege-guide-dialog.scss';
// @import '~@mu/lui/dist/style/components/activity-coupon-dialog.scss';
// @import '~@mu/lui/dist/style/components/repayment-settlement-dialog.scss';
// @import '~@mu/lui/dist/style/components/adjust-adjprice-dialog.scss';
// @import '~@mu/lui/dist/style/components/activity-adjust-dialog.scss';
// @import '~@mu/lui/dist/style/components/inflation-coupon-dialog.scss';
@import '~@mu/subscribe-btn/dist/style/index.scss';
