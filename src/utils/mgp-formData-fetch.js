import formDataFetch from '@utils/formData-fetch.js';
import muappFormDataFetch from '@utils/formData-fetch-muapp.js';
import { isMuapp } from '@mu/madp-utils';

const toBlob = (data) => {
  const arr = data.split(',');
  const byteString = atob(arr[1]);
  const mimeString = arr[0].match(/:(.*?);/)[1];
  const ia = new Uint8Array(byteString.length);
  for (let i = 0; i < byteString.length; i += 1) {
    ia[i] = byteString.charCodeAt(i);
  }
  return new Blob([ia], { type: mimeString });
};

export default async (operationId, options = {}) => {
  const { srcList, data = {}, autoLoading = true } = options;
  let ret = null;
  if (isMuapp()) {
    const params = {
      fileList: {},
      data,
      autoLoading
    };
    Object.keys(srcList).forEach((key) => {
      // 自有app的古怪规定
      if (srcList[key] instanceof Array) {
        params.fileList[key] = [...srcList[key]];
      } else {
        params.fileList[key] = [srcList[key]];
      }
    });
    ret = await muappFormDataFetch(operationId, params);
  } else {
    const formData = new FormData();
    Object.keys(srcList).forEach((key) => {
      if (srcList[key] instanceof Array) {
        srcList[key].forEach((src) => formData.append(key, toBlob(src)));
      } else {
        formData.append(key, toBlob(srcList[key]));
      }
    });
    formData.append('data', JSON.stringify(data));
    ret = await formDataFetch(operationId, { data: formData, autoLoading });
  }
  return ret;
};
