/* eslint-disable no-plusplus */
import {getBusinessFullName } from '@mu/business-basic';
import {
  getCurrentPageUrl
} from '@mu/madp-utils';
const Utility = {};

Utility.isValidateIdNum = (idNum) => {
  if (!idNum || idNum.length !== 18) {
    return false;
  }

  const year = idNum.substring(6, 10);
  const month = idNum.substring(10, 12);
  const day = idNum.substring(12, 14);
  const birthday = new Date(year, parseFloat(month) - 1, parseFloat(day));
  // 这里用getFullYear()获取年份，避免千年虫问题

  if (
    birthday.getFullYear() !== parseFloat(year)
        || birthday.getMonth() !== parseFloat(month) - 1
        || birthday.getDate() !== parseFloat(day)
  ) {
    return false;
  }

  const Wi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1]; // 加权因子
  const Y = ['1', '0', '10', '9', '8', '7', '6', '5', '4', '3', '2']; // 身份证验证位值.10代表X

  // 验证校验位
  let sum = 0; // 声明加权求和变量
  const cardNoArr = idNum.split('');

  if (cardNoArr[17].toLowerCase() === 'x') {
    cardNoArr[17] = '10'; // 将最后位为x的验证码替换为10方便后续操作
  }
  for (let i = 0; i < 17; i++) {
    sum += Wi[i] * cardNoArr[i]; // 加权求和
  }
  const i = sum % 11; // 得到验证码所位置

  if (cardNoArr[17] !== Y[i]) {
    return false;
  }

  return true;
};

Utility.isValidatePhoneNum = (phoneNum) => {
  if (!phoneNum) {
    return false;
  }
  const reg = /^(1[3456789][0-9])\d{8}$/;

  if (reg.test(phoneNum)) {
    return true;
  }

  return false;
};

// 调整电话号码格式：去掉+86、空格、-、()、首几位连续的0
Utility.modifyPhoneNum = (phoneNum) => {
  if (!phoneNum) {
    return '';
  }
  let modifyNum = phoneNum;

  if (modifyNum.indexOf('+86') !== -1) {
    modifyNum = modifyNum.replace(/\+86/, '');
  }
  if (modifyNum.indexOf('86') === 0) {
    modifyNum = modifyNum.replace(/86/, '');
  }
  if (modifyNum.indexOf('0086') !== -1) {
    modifyNum = modifyNum.replace(/0086/, '');
  }
  // 两种空格
  if (modifyNum.indexOf(' ') !== -1 || modifyNum.indexOf(' ') !== -1) {
    modifyNum = modifyNum.replace(/\s/g, '');
  }
  if (modifyNum.indexOf('(') !== -1 && modifyNum.indexOf(')') !== -1) {
    modifyNum = modifyNum.replace(/\([^)]*\)/g, '');
  }
  if (modifyNum.indexOf('-') !== -1) {
    modifyNum = modifyNum.replace(/-/g, '');
  }
  if (modifyNum.indexOf('0') === 0) {
    modifyNum = modifyNum.replace(/\b(0+)/gi, '');
  }

  return modifyNum;
};

Utility.isValidateName = (name) => {
  if (!name) {
    return false;
  }
  const reg = /^[\u4E00-\u9FA5]{2,10}(·[\u4E00-\u9FA5]{1,10})?$/;

  return reg.test(name);
};

Utility.isValidateQQNumber = (qq) => {
  const reg = /[1-9][0-9]{5,}/;

  if (!qq) {
    return false;
  }

  return reg.test(qq);
};

Utility.isValidateLoginPassword = (password) => {
  const reg = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{6,16}$/;

  if (!password) {
    return false;
  }
  return reg.test(password);
};

Utility.isValidateEmail = (email) => {
  const reg = /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;

  if (!email) {
    return false;
  }

  return reg.test(email);
};

Utility.addMTag = (url, code) => {
  let addUrl;

  if (url.indexOf('?') >= 0) {
    addUrl = url.concat('&mtagc=').concat(code);
  } else {
    addUrl = url.concat('?&mtagc=').concat(code);
  }

  return addUrl;
};

Utility.getFullCompanyName = async () => {
  let fullCompanyName = "招联消费金融股份有限公司";
  try {
    const res = await getBusinessFullName();
    if (res && res.data) {
      fullCompanyName = res.data;
    }
  } catch (error) {
  }
  return fullCompanyName;
};

Utility.tranformDate = (businessDate) => {
  const pattern = /(\d{4})(\d{2})(\d{2})/;
  return businessDate.replace(pattern, '$1年$2月$3日');
};

Utility.getBusiEntrance = () => {
  const url = getCurrentPageUrl();
  let busiEntrance = '';
  if (process.env.TARO_ENV === 'h5') {
    const keyIndex = url.indexOf('#');
    const str = keyIndex > -1 ? url.substring(keyIndex) : url;
    busiEntrance = `usercenter/${str}`;
  } else {
    busiEntrance = url;
  }
  return busiEntrance;
};

Utility.isValidateEmailV2 = (email) => {
  const reg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  if (!email || email === '') {
    return false;
  }
  return reg.test(email);
};

export default Utility;
