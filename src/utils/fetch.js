/* eslint-disable max-len */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-undef */
/* eslint-disable no-underscore-dangle */
/* eslint-disable no-nested-ternary */
import Madp from '@mu/madp';
import {
  getCurrentPageUrlWithArgs, getEnv
} from '@mu/madp-utils';
import { getToken } from '@mu/dev-finger';
import { getStore } from '@api/store';
import Util from '@utils/maxin-util';
import { mapCodeMap } from '@utils/constants';
import MultipartFetch from '@utils/multipart-fetch';
import { platLogin } from './login';
// 不要加下面这玩意，有bug，加了无法用流的方式发送
// import { handleMgpRedirect, addMgpRedirectInterceptor } from './mgp-redirect';

/**
 * 解决加载中图标由于接口频繁不停闪动
 *
 * 并行请求，可以知道当前请求的个数，使用一个变量保存当前请求数，新增一个请求加+1，
 * 结束一个请求-1，每个请求结束都判断下变量是不是等于0，
 * 如果等于0，说明这时候请求已经全部结束，则loading可以关闭。
 *
 * 串行请求，下一个请求的结果依赖于上一个请求的结果，
 * 上一个请求回调事件中把showhide放到setTimeout中执行，
 * 下一个请求的同步任务中把上一个请求生成的hideLoading定时器清除。
 */
let isToast = false; // 可关闭loading弹窗标识
const duration = 2000;
let invokedLoadingCount = 0; // 并行请求数量
let hideLoadingTimeout = 0; // 定时器 隐藏loading的Timeout

// TODO 登录逻辑
let canNextLogin = true;
function doLogin(options) {
  if (process.env.TARO_ENV === 'alipay') {
    const throttleTime = getEnv() === 'prod' ? 2000 : 3000;
    if (canNextLogin) {
      canNextLogin = false;
      setTimeout(() => {
        canNextLogin = true;
      }, throttleTime);
      return platLogin(options);
    }
  } else {
    return platLogin(options);
  }
}

// 小程序获取mapCode
function getMapCodeFromMap() {
  if (process.env.TARO_ENV === 'h5') return '';
  return mapCodeMap[Madp.getChannel()] || '';
}

const rejectFunc = async (res, options) => {
  const { status } = res;
  const { autoToast } = options || {};
  if (status === 401) {
    doLogin(options);
    return res.clone();
  } else if (status === 200) {
    const json = await res.json();
    // 业务错误
    if (json.ret === '1') {
      const errMsg = json.errMsg || '系统繁忙，请稍后再试';
      // 触发全局事件，页面中订阅该事件可用于更新界面
      Madp.eventCenter.trigger('madp_biz_error_event', json);
      if (autoToast) {
        isToast = true;
        Util.showErrorMsg(errMsg);
        setTimeout(() => {
          isToast = false;
        }, duration);
      }
    }
    return Promise.reject(res.clone());
  } else if (status >= 300) {
    if (autoToast) {
      isToast = true;
      Madp.showToast({
        title: '网络异常，请稍后再试',
        icon: 'none',
        duration
      });
      setTimeout(() => {
        isToast = false;
      }, duration);
    }
    return Promise.reject(`网络异常: ${status}`);
  } else {
    return Promise.reject(`网络异常: ${status}`);
  }
};

const fetch = async (url, options, ignoreRet) => {
  // addMgpRedirectInterceptor();
  const requestOptions = {};
  const {
    autoToast = true,
  } = options;
  requestOptions.autoToast = autoToast;
  // 适配apollo-link-http参数
  if (!options.body || typeof options.body !== 'string') {
    return Promise.reject('body should be stringified');
  }

  const body = JSON.parse(options.body) || {};
  if (body.variables && body.variables.operationId) {
    requestOptions.url = `${url}?operationId=${body.variables.operationId}`;
  } else if (body.variables) {
    requestOptions.url = url;
  } else {
    body.variables = {};
  }

  requestOptions.method = ((body.variables.method) || 'POST').toUpperCase();
  const pageUrl = getCurrentPageUrlWithArgs();

  // 设备指纹引入,接口签名已经默认在 Madp.request集成，无需在环境参数再做处理
  const token = await getToken(true, Madp.getChannel());

  // 业务mapCode获取，详情查看app.js 的 eventCenter 相关代码
  const mapCode = process.env.TARO_ENV === 'h5'
    ? (Madp.getStorageSync('mapCode', 'SESSION') || '')
    : getMapCodeFromMap();

  const { EnvParams } = options;
  const repayLocation = Madp.getStorageSync('repayLocation', 'SESSION') || '';
  const location = repayLocation ? JSON.parse(repayLocation) : {};
  const reqEnvParams = {
    channel: Madp.getChannel(),
    appType: 'H5',
    pageUrl,
    token,
    mapCode,
    module: 'repayment',
    coordinateSystem: (location || {}).coordinateSystem || 'WGS-84',
    longitude: (location || {}).longitude || '',
    latitude: (location || {}).latitude || '',
    ...EnvParams
  };

  let params = {
    reqEnvParams: JSON.stringify(reqEnvParams)
  };

  if (body.variables.data) params.data = JSON.stringify(body.variables.data);
  if (options.fileParam) {
    params = Object.assign(params, options.fileParam);
    requestOptions.data = Util.paramsToFormData(params);
    requestOptions.reqEnvParams = params.reqEnvParams;
    requestOptions.header = { 'content-type': 'multipart/form-data' };
  } else {
    requestOptions.data = params;
  }

  const { autoLoading = true } = options;
  if (autoLoading) {
    if (process.env.TARO_ENV === 'alipay') {
      // 支付宝小程序异常显示loading，解决办法
      Madp.showLoading({ title: '加载中' });
    } else if (!isToast && invokedLoadingCount <= 0) {
      if (hideLoadingTimeout) {
        clearTimeout(hideLoadingTimeout);
      } else {
        Madp.showLoading({ title: '加载中' });
      }
    }
    invokedLoadingCount += 1;
  }

  function hideLoading() {
    if (autoLoading) {
      if (process.env.TARO_ENV === 'alipay') {
        // 支付宝小程序异常显示loading，解决办法
        Madp.hideLoading();
        return;
      }
      invokedLoadingCount -= 1;
      if (!isToast && invokedLoadingCount <= 0) {
        hideLoadingTimeout = setTimeout(() => {
          Madp.hideLoading();
          hideLoadingTimeout = 0;
        });
      }
    }
  }

  // 获取request开始时间
  const begin = Date.now();

  const finalRequest = options.fileParam ? ((MultipartFetch || {}).default || {}).request : Madp.request;

  return finalRequest(requestOptions)
    // .then(handleMgpRedirect(async (res) => {
    .then(async (res) => {
      hideLoading();
      const { status } = res;
      if (status === 200) {
        // 进行ARMS API手动上报
        const rqTime = Date.now() - begin;
        let flowNo = '';
        try {
          flowNo = res.headers.get('x-flow-no');
        } catch (e) {}
        window && window.__bl && __bl.api && __bl.api(requestOptions.url, true, rqTime, 200, flowNo);

        const json = await res.json();
        if (json.ret === '0' || ignoreRet) {
          // apollo-link-http 需要的data 格式返回
          return Promise.resolve(res.clone());
        }
        return Promise.reject(res.clone());
      }
      return Promise.reject(res.clone());
    })
    .catch((res) => {
      if (res && res.status) {
        // 进行ARMS API手动上报
        const rqTime = Date.now() - begin;
        let flowNo = '';
        try {
          flowNo = res.headers.get('x-flow-no');
        } catch (e) {}
        window && window.__bl && __bl.api && __bl.api(requestOptions.url, false, rqTime, res.status, `${flowNo},${res.statusText}`);
      }

      hideLoading();
      rejectFunc(res, requestOptions);
    });
};

export default fetch;
