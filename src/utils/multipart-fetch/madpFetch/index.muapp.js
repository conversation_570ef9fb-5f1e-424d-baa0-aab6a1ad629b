/* eslint-disable */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('./_rollupPluginBabelHelpers.js');
var fetchBaseCls = require('./fetch-base-cls.js');
var response = require('./response.js');

var support = {
  searchParams: 'URLSearchParams' in self,
  iterable: 'Symbol' in self && 'iterator' in Symbol,
  blob: 'FileReader' in self && 'Blob' in self && function () {
    try {
      new Blob();
      return true;
    } catch (e) {
      return false;
    }
  }(),
  formData: 'FormData' in self,
  arrayBuffer: 'ArrayBuffer' in self
};

function normalizeName(name) {
  if (typeof name !== 'string') {
    name = String(name);
  }

  if (/[^a-z0-9\-#$%&'*+.^_`|~]/i.test(name)) {
    throw new TypeError('Invalid character in header field name');
  }

  return name.toLowerCase();
}

function normalizeValue(value) {
  if (typeof value !== 'string') {
    value = String(value);
  }

  return value;
}

function iteratorFor(items) {
  var iterator = {
    next: function next() {
      var value = items.shift();
      return {
        done: value === undefined,
        value: value
      };
    }
  };

  if (support.iterable) {
    iterator[Symbol.iterator] = function () {
      return iterator;
    };
  }

  return iterator;
}

function Headers(headers) {
  this.map = {};

  if (headers instanceof Headers) {
    headers.forEach(function (value, name) {
      this.append(name, value);
    }, this);
  } else if (Array.isArray(headers)) {
    headers.forEach(function (header) {
      this.append(header[0], header[1]);
    }, this);
  } else if (headers) {
    Object.getOwnPropertyNames(headers).forEach(function (name) {
      this.append(name, headers[name]);
    }, this);
  }
}

Headers.prototype.append = function (name, value) {
  name = normalizeName(name);
  value = normalizeValue(value);
  var oldValue = this.map[name];
  this.map[name] = oldValue ? oldValue + ', ' + value : value;
};

Headers.prototype['delete'] = function (name) {
  delete this.map[normalizeName(name)];
};

Headers.prototype.get = function (name) {
  name = normalizeName(name);
  return this.has(name) ? this.map[name] : null;
};

Headers.prototype.has = function (name) {
  return this.map.hasOwnProperty(normalizeName(name));
};

Headers.prototype.set = function (name, value) {
  this.map[normalizeName(name)] = normalizeValue(value);
};

Headers.prototype.forEach = function (callback, thisArg) {
  for (var name in this.map) {
    if (this.map.hasOwnProperty(name)) {
      callback.call(thisArg, this.map[name], name, this);
    }
  }
};

Headers.prototype.keys = function () {
  var items = [];
  this.forEach(function (value, name) {
    items.push(name);
  });
  return iteratorFor(items);
};

Headers.prototype.values = function () {
  var items = [];
  this.forEach(function (value) {
    items.push(value);
  });
  return iteratorFor(items);
};

Headers.prototype.entries = function () {
  var items = [];
  this.forEach(function (value, name) {
    items.push([name, value]);
  });
  return iteratorFor(items);
};

function parseHeaders(rawHeaders) {
  var headers = new Headers();
  var preProcessedHeaders = rawHeaders.replace(/\r?\n[\t ]+/g, ' ');
  preProcessedHeaders.split(/\r?\n/).forEach(function (line) {
    var parts = line.split(':');
    var key = parts.shift().trim();

    if (key) {
      var value = parts.join(':').trim();
      headers.append(key, value);
    }
  });
  return headers;
}

function dataToJson(data) {
  if (data === void 0) {
    data = {};
  }

  var jsonData = _rollupPluginBabelHelpers._typeof(data.data) === 'object' ? {
    data: JSON.stringify(data.data)
  } : {
    data: data.data
  };

  if (!data.data) {
    jsonData = {};
  }

  return jsonData;
}

function request(options) {
  var _a = options || {},
      _b = _a.useMunaAjax,
      useMunaAjax = _b === void 0 ? false : _b,
      _c = _a.data,
      data = _c === void 0 ? {} : _c;

  var munaRequest = useMunaAjax ? window.top.muna.ajax : window.top.muna.httpRequest || window.top.muna.ajax;

  var tempData = _rollupPluginBabelHelpers.__assign(_rollupPluginBabelHelpers.__assign({}, data), dataToJson(data));

  return new Promise(function (resolve, reject) {
    return munaRequest({
      url: options.url,
      method: options.method || 'GET',
      data: tempData,
      headers: options.header,
      dataType: options.dataType,
      success: function success(json, status, xhr) {
        var opt = {
          status: xhr.status,
          statusCode: xhr.status,
          statusText: xhr.statusText,
          headers: parseHeaders(xhr.getAllResponseHeaders() || ''),
          data: JSON.parse(json),
          url: xhr.requestUrl
        };
        var resonse = new response.Response(opt, options);
        var result = {
          statusCode: '',
          header: {},
          data: {}
        };
        result.statusCode = xhr.status;
        result.header = {};
        result.data = resonse;
        resolve(result);
      },
      error: function error(xhr, status) {
        if (xhr.status < 599) {
          var opt = {
            status: xhr.status,
            statusCode: xhr.status,
            statusText: xhr.statusText,
            headers: parseHeaders(xhr.getAllResponseHeaders() || '')
          };
          opt.url = xhr.requestUrl;
          var res = new response.Response(opt, options);
          var result = {
            statusCode: xhr.status,
            header: {},
            data: {}
          };
          result.data = res;
          resolve(result);
        } else {
          reject(xhr.statusText);
        }
      }
    });
  });
}

function requestInterceptor(chain) {
  return request(chain.requestParams);
}

var link = new fetchBaseCls.Link(requestInterceptor);
var fetchRequest = link.request.bind(link);
var invokeAddInterceptor = link.addInterceptor.bind(link);
var invokeCleanInterceptors = link.cleanInterceptors.bind(link);
var index_muapp = new fetchBaseCls.FetchBase(fetchRequest, invokeAddInterceptor, invokeCleanInterceptors);

exports.default = index_muapp;
