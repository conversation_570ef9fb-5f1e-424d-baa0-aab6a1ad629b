/* eslint-disable */
'use strict';

var _rollupPluginBabelHelpers = require('./_rollupPluginBabelHelpers.js');

var Chain = function () {
  function Chain(requestParams, interceptors, index) {
    if (interceptors === void 0) {
      interceptors = [];
    }

    if (index === void 0) {
      index = 0;
    }

    this.index = index;
    this.requestParams = requestParams;
    this.interceptors = interceptors;
  }

  Chain.prototype.proceed = function (requestParams) {
    this.requestParams = requestParams;

    if (this.index >= this.interceptors.length) {
      throw new Error('chain 参数错误, 请勿直接修改 request.chain');
    }

    var nextInterceptor = this._getNextInterceptor();

    var nextChain = this._getNextChain();

    var p = nextInterceptor(nextChain);
    var res = p["catch"](function (err) {
      return Promise.reject(err);
    });
    if (typeof p.abort === 'function') res.abort = p.abort;
    return res;
  };

  Chain.prototype._getNextInterceptor = function () {
    return this.interceptors[this.index];
  };

  Chain.prototype._getNextChain = function () {
    return new Chain(this.requestParams, this.interceptors, this.index + 1);
  };

  return Chain;
}();

var Link = function () {
  function Link(interceptor) {
    this.linkInterceptor = interceptor;
    this.chain = new Chain();
  }

  Link.prototype.request = function (requestParams) {
    var _this = this;

    this.chain.interceptors = this.chain.interceptors.filter(function (interceptor) {
      return interceptor !== _this.linkInterceptor;
    });

    if (this.chain.interceptors.length > 1 && typeof this.currentIndex !== 'undefined') {
      var tempInterceptor = this.chain.interceptors.splice(this.currentIndex, 1);
      this.chain.interceptors.push(tempInterceptor[0]);
      this.currentIndex = undefined;
    }

    this.chain.interceptors.push(this.linkInterceptor);
    return this.chain.proceed(_rollupPluginBabelHelpers.__assign({}, requestParams));
  };

  Link.prototype.addInterceptor = function (interceptor, flag) {
    if (flag) {
      this.currentIndex = this.chain.interceptors.length;
    }

    this.chain.interceptors.push(interceptor);
  };

  Link.prototype.cleanInterceptors = function () {
    this.chain = new Chain();
  };

  return Link;
}();

var FetchBase = function () {
  function FetchBase(request, addInterceptor, cleanInterceptors) {
    this.request = request;
    this.addInterceptor = addInterceptor;
    this.cleanInterceptors = cleanInterceptors;
  }

  FetchBase.prototype.getInstance = function () {
    return this;
  };

  return FetchBase;
}();

exports.FetchBase = FetchBase;
exports.Link = Link;
