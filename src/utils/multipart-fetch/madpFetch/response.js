/* eslint-disable */
'use strict';

var Response = function () {
  function Response(res, options) {
    this.resInit = res;
    this.optionsInit = options;
    this.ok = res.statusCode >= 200 && res.statusCode < 300;
    this.statusText = '';
    this.status = res.statusCode;
    this.url = options.url;
    this.headers = {
      keys: function keys() {
        return Object.keys(res.header);
      },
      entries: function entries() {
        var entries = [];
        Object.keys(res.header).forEach(function (key) {
          entries.push([key, res.header[key]]);
        });
        return entries;
      },
      get: function get(n) {
        return res.header[n.toLowerCase()];
      },
      has: function has(n) {
        return n.toLowerCase() in res.header;
      }
    };
  }

  Response.prototype.text = function () {
    return Promise.resolve(JSON.stringify(this.resInit.data));
  };

  Response.prototype.json = function () {
    return Promise.resolve(this.resInit.data);
  };

  Response.prototype.blob = function () {
    return Promise.resolve(new Blob([this.resInit.data]));
  };

  Response.prototype.clone = function () {
    return new Response(this.resInit, this.optionsInit);
  };

  return Response;
}();

exports.Response = Response;
