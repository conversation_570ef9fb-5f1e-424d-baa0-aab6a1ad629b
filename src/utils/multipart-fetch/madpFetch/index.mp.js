/* eslint-disable */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('./_rollupPluginBabelHelpers.js');
var helper = require('./helper.js');
var fetchBaseCls = require('./fetch-base-cls.js');
var response = require('./response.js');
var madpUtils = require('@mu/madp-utils');

var domain = {
  se: 'https://m-zl-se.cfcmu.cn',
  se1: 'https://m-zl-se1.cfcmu.cn',
  st1: 'https://m-zl-st1.cfcmu.cn',
  st2: 'https://m-zl-st2.cfcmu.cn',
  uat: 'https://m-zl-uat.cfcmu.cn',
  uat1: 'https://m-zl-uat1.cfcmu.cn',
  prod: 'https://m-zl.mucfc.com'
};
var mode = process.env.BUILD_ENV || madpUtils.getEnv();
var currentDomain = domain[mode];
function getMpRequest() {
  if (process.env.TARO_ENV === 'weapp') {
    return wx.request;
  } else if (process.env.TARO_ENV === 'alipay') {
    var nativeRequest = my.canIUse('request') ? my.request : my.httpRequest;
    return nativeRequest;
  } else if (process.env.TARO_ENV === 'tt') {
    return tt.request;
  } else if (process.env.TARO_ENV === 'swan') {
    return swan.request;
  } else if (process.env.TARO_ENV === 'qq') {
    return qq.request;
  }
}

function domainAddPath(domain) {
  return domain.replace(/api\.(cfcmu\.cn|mucfc\.com)\?/, 'api.$1/?');
}

function setRequestBeforeHandler(options) {
  if (process.env.TARO_ENV === 'alipay') {
    var defaultHeaders = {
      'content-type': 'application/json'
    };
    options['headers'] = defaultHeaders;

    if (options['header']) {
      for (var k in options['header']) {
        var lowerK = k.toLocaleLowerCase();
        options['headers'][lowerK] = options['header'][k];
      }

      delete options['header'];
    }

    options['url'] = domainAddPath(options['url']);
  } else if (process.env.TARO_ENV === 'weapp') {
    var defaultHeaders = _rollupPluginBabelHelpers.__assign({
      Origin: currentDomain
    }, options['header']);

    options['header'] = defaultHeaders;
  }
}

var nativeRequest = getMpRequest();
var RequestQueue = {
  MAX_REQUEST: 10,
  queue: [],
  pendingQueue: [],
  request: function request(options) {
    this.queue.push(options);
    return this.run();
  },
  run: function run() {
    var _this = this;

    if (!this.queue.length) {
      return;
    }

    var _loop_1 = function _loop_1() {
      var options = this_1.queue.shift();
      var successFn = options.success;
      var failFn = options.fail;

      options.success = function () {
        var args = [];

        for (var _i = 0; _i < arguments.length; _i++) {
          args[_i] = arguments[_i];
        }

        _this.pendingQueue = _this.pendingQueue.filter(function (item) {
          return item !== options;
        });

        _this.run();

        successFn && successFn.apply(options, args);
      };

      options.fail = function () {
        var args = [];

        for (var _i = 0; _i < arguments.length; _i++) {
          args[_i] = arguments[_i];
        }

        _this.pendingQueue = _this.pendingQueue.filter(function (item) {
          return item !== options;
        });

        _this.run();

        failFn && failFn.apply(options, args);
      };

      helper.transformRequestData(options);
      setRequestBeforeHandler(options);
      this_1.pendingQueue.push(options);
      options.enableCookie = true;
      return {
        value: nativeRequest(options)
      };
    };

    var this_1 = this;

    while (this.pendingQueue.length < this.MAX_REQUEST) {
      var state_1 = _loop_1();

      if (_rollupPluginBabelHelpers._typeof(state_1) === "object") return state_1.value;
    }
  }
};

function request(options) {
  options = options || {};

  if (typeof options === 'string') {
    options = {
      url: options
    };
  }

  var response$1 = {};
  var originSuccess = options['success'];
  var originFail = options['fail'];
  var originComplete = options['complete'];
  var requestTask;
  var p = new Promise(function (resolve, reject) {
    options['success'] = function (res) {
      if (process.env.TARO_ENV === 'alipay') {
        res.header = res.headers;
        res.statusCode = res.status;
      }

      response$1 = {
        statusCode: res.statusCode,
        header: res.header,
        data: new response.Response(res, options)
      };
      originSuccess && originSuccess(response$1);
      resolve(response$1);
    };

    options['fail'] = function (res) {
      res.text = function () {
        return Promise.resolve(res.data);
      };

      originFail && originFail(res);
      reject(res);
    };

    options['complete'] = function (res) {
      res.text = function () {
        return Promise.resolve(res.data);
      };

      originComplete && originComplete(res);
    };

    requestTask = RequestQueue.request(options);
  });

  p.abort = function (cb) {
    cb && cb();

    if (requestTask) {
      requestTask.abort();
    }

    return p;
  };

  return p;
}

function requestInterceptor(chain) {
  return request(chain.requestParams);
}

var link = new fetchBaseCls.Link(requestInterceptor);
var fetchRequest = link.request.bind(link);
var invokeAddInterceptor = link.addInterceptor.bind(link);
var invokeCleanInterceptors = link.cleanInterceptors.bind(link);
var index_mp = new fetchBaseCls.FetchBase(fetchRequest, invokeAddInterceptor, invokeCleanInterceptors);

exports.default = index_mp;
