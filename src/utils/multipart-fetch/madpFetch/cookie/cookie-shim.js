/* eslint-disable */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

function _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }

var CookieParser = _interopDefault(require('set-cookie-parser'));

/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __values(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
}

function __read(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
}

var Util = function () {
  function Util() {}

  Util.prototype.getCookieScopeDomain = function (domain) {
    if (domain === void 0) {
      domain = '';
    }

    if (!domain) return [];
    domain = domain.replace(/^\.+/gi, '');
    var scopes = domain.split('.').map(function (k) {
      return ['.', domain.slice(domain.indexOf(k))].join('');
    });
    return [domain].concat(scopes);
  };

  Util.prototype.normalizeDomain = function (domain) {
    if (domain === void 0) {
      domain = '';
    }

    return domain.replace(/^(\.*)?(?=\S)/gi, '.');
  };

  return Util;
}();

var util = new Util();

var Cookie = function () {
  function Cookie(props) {
    this.name = props.name || '';
    this.value = props.value || '';
    this.domain = props.domain || '';
    this.path = props.path || '/';
    this.expires = props.expires ? new Date(props.expires) : null;
    this.maxAge = props.maxAge ? parseInt(props.maxAge) : null;
    this.httpOnly = !!props.httpOnly;
    this.dateTime = props.dateTime ? new Date(props.dateTime) : new Date();
  }

  Cookie.prototype.set = function (setCookieStr) {
    if (setCookieStr === void 0) {
      setCookieStr = '';
    }

    var cookie = CookieParser.parse(setCookieStr, {
      decodeValues: false
    })[0];

    if (cookie) {
      Object.assign(this, cookie);
      this.dateTime = new Date();
    }

    return this;
  };

  Cookie.prototype.merge = function (cookie) {
    return Object.assign(this, cookie);
  };

  Cookie.prototype.isExpired = function () {
    if (this.maxAge === 0) {
      return true;
    }

    if (this.maxAge > 0) {
      var seconds = (Date.now() - this.dateTime.getTime()) / 1000;
      return seconds > this.maxAge;
    }

    if (this.expires && this.expires < new Date()) {
      return true;
    }

    return false;
  };

  Cookie.prototype.isPersistence = function () {
    return this.maxAge ? this.maxAge > 0 : true;
  };

  Cookie.prototype.isInDomain = function (domain) {
    var scopeDomains = util.getCookieScopeDomain(domain);
    return scopeDomains.indexOf(this.domain) >= 0;
  };

  Cookie.prototype.isInPath = function (path) {
    return path.indexOf(this.path) === 0 || this.path.replace(/\/$/, '') === path;
  };

  Cookie.prototype.toString = function () {
    return [this.name, this.value].join('=');
  };

  return Cookie;
}();

function getApi() {
  if (typeof my !== 'undefined') {
    my.platform = 'my';
    return my;
  } else if (typeof tt !== 'undefined') {
    tt.platform = 'tt';
    return tt;
  } else if (typeof swan !== 'undefined') {
    swan.platform = 'swan';
    return swan;
  } else if (typeof qq !== 'undefined') {
    qq.platform = 'qq';
    return qq;
  } else if (typeof wx !== 'undefined') {
    wx.platform = typeof window !== 'undefined' && typeof location !== 'undefined' ? 'h5' : 'wx';
    return wx;
  }

  return {
    platform: 'none'
  };
}

var MiniApi = getApi();

var LocalStorage = function () {
  function LocalStorage(args) {}

  LocalStorage.prototype.getItem = function (key) {
    if (MiniApi.platform === 'my') {
      return MiniApi.getStorageSync({
        key: key
      }).data;
    }

    return MiniApi.getStorageSync(key);
  };

  LocalStorage.prototype.setItem = function (key, value) {
    if (MiniApi.platform === 'my') {
      return MiniApi.setStorageSync({
        key: key,
        data: value
      });
    }

    return MiniApi.setStorageSync(key, value);
  };

  return LocalStorage;
}();

var localStorage = new LocalStorage(MiniApi);

var CookieStore = function () {
  function CookieStore() {
    this.__storageKey = '__cookie_store__';
    this.__cookiesMap = this.readFromStorage() || new Map();
  }

  CookieStore.prototype.getCookiesArray = function (domain, path) {
    var e_1, _a, e_2, _b;

    if (domain === void 0) {
      domain = '';
    }

    if (path === void 0) {
      path = '/';
    }

    var cookiesArr = [];
    var scopeDomains = util.getCookieScopeDomain(domain);

    try {
      for (var _c = __values(this.__cookiesMap.entries()), _d = _c.next(); !_d.done; _d = _c.next()) {
        var _e = __read(_d.value, 2),
            key = _e[0],
            cookies = _e[1];

        if (domain && scopeDomains.indexOf(key) < 0) continue;

        try {
          for (var _f = (e_2 = void 0, __values(cookies.values())), _g = _f.next(); !_g.done; _g = _f.next()) {
            var cookie = _g.value;

            if (cookie.isInPath(path) && !cookie.isExpired()) {
              cookiesArr.push(cookie);
            }
          }
        } catch (e_2_1) {
          e_2 = {
            error: e_2_1
          };
        } finally {
          try {
            if (_g && !_g.done && (_b = _f["return"])) _b.call(_f);
          } finally {
            if (e_2) throw e_2.error;
          }
        }
      }
    } catch (e_1_1) {
      e_1 = {
        error: e_1_1
      };
    } finally {
      try {
        if (_d && !_d.done && (_a = _c["return"])) _a.call(_c);
      } finally {
        if (e_1) throw e_1.error;
      }
    }

    return cookiesArr;
  };

  CookieStore.prototype.stringify = function (cookies) {
    return cookies.map(function (item) {
      return item.toString();
    }).join('; ');
  };

  CookieStore.prototype.getRequestCookies = function (domain, path) {
    var cookiesArr = this.getCookiesArray(domain, path);
    return this.stringify(cookiesArr);
  };

  CookieStore.prototype.saveToStorage = function () {
    var e_3, _a, e_4, _b;

    try {
      var saveCookies = [];

      try {
        for (var _c = __values(this.__cookiesMap.values()), _d = _c.next(); !_d.done; _d = _c.next()) {
          var cookies = _d.value;

          try {
            for (var _e = (e_4 = void 0, __values(cookies.values())), _f = _e.next(); !_f.done; _f = _e.next()) {
              var cookie = _f.value;

              if (cookie.isExpired()) {
                cookies["delete"](cookie.name);
              } else if (cookie.isPersistence()) {
                saveCookies.push(cookie);
              }
            }
          } catch (e_4_1) {
            e_4 = {
              error: e_4_1
            };
          } finally {
            try {
              if (_f && !_f.done && (_b = _e["return"])) _b.call(_e);
            } finally {
              if (e_4) throw e_4.error;
            }
          }
        }
      } catch (e_3_1) {
        e_3 = {
          error: e_3_1
        };
      } finally {
        try {
          if (_d && !_d.done && (_a = _c["return"])) _a.call(_c);
        } finally {
          if (e_3) throw e_3.error;
        }
      }

      localStorage.setItem(this.__storageKey, saveCookies);
    } catch (err) {
      console.warn('Cookie 存储异常：', err);
    }
  };

  CookieStore.prototype.setCookiesArray = function (cookies) {
    var _this = this;

    if (cookies === void 0) {
      cookies = [];
    }

    this.__cookiesMap = this.__cookiesMap || new Map();
    cookies.forEach(function (cookie) {
      var cookieMap = _this.__cookiesMap.get(cookie.domain);

      if (!cookieMap) {
        cookieMap = new Map();

        _this.__cookiesMap.set(cookie.domain, cookieMap);
      }

      cookieMap.set(cookie.name, cookie);
    });
    this.saveToStorage();
    return this.__cookiesMap;
  };

  CookieStore.prototype.setMiniCookies = function (setCookieStr, domain) {
    var parsedCookies = this.parse(setCookieStr, domain);
    return this.setCookiesArray(parsedCookies);
  };

  CookieStore.prototype.parse = function (setCookieStr, domain) {
    if (setCookieStr === void 0) {
      setCookieStr = '';
    }

    var cookies = CookieParser.parse(CookieParser.splitCookiesString(setCookieStr), {
      decodeValues: false
    });
    return cookies.map(function (item) {
      item.domain = util.normalizeDomain(item.domain) || domain;
      return new Cookie(item);
    });
  };

  CookieStore.prototype.setResponseCookies = function (setCookieStr, domain) {
    var parsedCookies = this.parse(setCookieStr, domain);
    return this.setCookiesArray(parsedCookies);
  };

  CookieStore.prototype.readFromStorage = function () {
    try {
      var cookies = localStorage.getItem(this.__storageKey) || [];
      cookies = cookies.map(function (item) {
        return new Cookie(item);
      });
      return this.setCookiesArray(cookies);
    } catch (err) {
      console.warn('Cookie 读取异常：', err);
    }
  };

  return CookieStore;
}();

var cookieStore;

if (process.env.TARO_ENV === 'alipay' && my) {
  if (my.muCookieStore) {
    cookieStore = my.muCookieStore;
  } else {
    cookieStore = new CookieStore();
    my.muCookieStore = cookieStore;
  }
} else {
  cookieStore = new CookieStore();
}

function setCookieWhenRequest(options) {
  if (options === void 0) {
    options = {};
  }

  var domainList = /(https?:\/\/(?:[^\.])+([^\/]+\.(?:mucfc|cfcmu|mucf).(?:cn|com|cc))(?:$|\/|\?))|(^\/[^\/])/.exec(options.url);
  var domain = domainList && domainList[2];
  var path = '/';
  var requestCookies = cookieStore.getRequestCookies(domain, path);
  options.header['Cookie'] = requestCookies;
  var successCallback = options.success;

  options.success = function (response) {
    response.header = response.header || response.headers;
    var responseCookies = response.header ? response.header['Set-Cookie'] || response.header['set-cookie'] : '';
    if (responseCookies) cookieStore.setResponseCookies(responseCookies, domain);
    successCallback && successCallback(response);
  };

  return options;
}
function setMiniCookies(setCookieStr, domain) {
  return cookieStore.setMiniCookies(setCookieStr, domain);
}

exports.setCookieWhenRequest = setCookieWhenRequest;
exports.setMiniCookies = setMiniCookies;
