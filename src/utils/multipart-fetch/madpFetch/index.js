/* eslint-disable */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('./_rollupPluginBabelHelpers.js');
var helper = require('./helper.js');

var commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};

function createCommonjsModule(fn, module) {
	return module = { exports: {} }, fn(module, module.exports), module.exports;
}

var wxapp = createCommonjsModule(function (module, exports) {
(function (global, factory) {
	 module.exports = factory() ;
}(commonjsGlobal, (function () {
/**
 * Timestamp
 */
var ts = (function () {
  return Date.now();
});
var chrsz = 8; /* bits per input character. 8 - ASCII; 16 - Unicode      */

/*
 * These are the functions you'll usually want to call
 * They take string arguments and return either hex or base-64 encoded strings
 */
function hex_md5(s) {
  return binl2hex(core_md5(str2binl(s), s.length * chrsz));
}

/*
 * Calculate the MD5 of an array of little-endian words, and a bit length
 */
function core_md5(x, len) {
  /* append padding */
  x[len >> 5] |= 0x80 << len % 32;
  x[(len + 64 >>> 9 << 4) + 14] = len;

  var a = 1732584193;
  var b = -271733879;
  var c = -1732584194;
  var d = 271733878;

  for (var i = 0; i < x.length; i += 16) {
    var olda = a;
    var oldb = b;
    var oldc = c;
    var oldd = d;

    a = md5_ff(a, b, c, d, x[i + 0], 7, -680876936);
    d = md5_ff(d, a, b, c, x[i + 1], 12, -389564586);
    c = md5_ff(c, d, a, b, x[i + 2], 17, 606105819);
    b = md5_ff(b, c, d, a, x[i + 3], 22, -1044525330);
    a = md5_ff(a, b, c, d, x[i + 4], 7, -176418897);
    d = md5_ff(d, a, b, c, x[i + 5], 12, 1200080426);
    c = md5_ff(c, d, a, b, x[i + 6], 17, -1473231341);
    b = md5_ff(b, c, d, a, x[i + 7], 22, -45705983);
    a = md5_ff(a, b, c, d, x[i + 8], 7, 1770035416);
    d = md5_ff(d, a, b, c, x[i + 9], 12, -1958414417);
    c = md5_ff(c, d, a, b, x[i + 10], 17, -42063);
    b = md5_ff(b, c, d, a, x[i + 11], 22, -1990404162);
    a = md5_ff(a, b, c, d, x[i + 12], 7, 1804603682);
    d = md5_ff(d, a, b, c, x[i + 13], 12, -40341101);
    c = md5_ff(c, d, a, b, x[i + 14], 17, -1502002290);
    b = md5_ff(b, c, d, a, x[i + 15], 22, 1236535329);

    a = md5_gg(a, b, c, d, x[i + 1], 5, -165796510);
    d = md5_gg(d, a, b, c, x[i + 6], 9, -1069501632);
    c = md5_gg(c, d, a, b, x[i + 11], 14, 643717713);
    b = md5_gg(b, c, d, a, x[i + 0], 20, -373897302);
    a = md5_gg(a, b, c, d, x[i + 5], 5, -701558691);
    d = md5_gg(d, a, b, c, x[i + 10], 9, 38016083);
    c = md5_gg(c, d, a, b, x[i + 15], 14, -660478335);
    b = md5_gg(b, c, d, a, x[i + 4], 20, -405537848);
    a = md5_gg(a, b, c, d, x[i + 9], 5, 568446438);
    d = md5_gg(d, a, b, c, x[i + 14], 9, -1019803690);
    c = md5_gg(c, d, a, b, x[i + 3], 14, -187363961);
    b = md5_gg(b, c, d, a, x[i + 8], 20, 1163531501);
    a = md5_gg(a, b, c, d, x[i + 13], 5, -1444681467);
    d = md5_gg(d, a, b, c, x[i + 2], 9, -51403784);
    c = md5_gg(c, d, a, b, x[i + 7], 14, 1735328473);
    b = md5_gg(b, c, d, a, x[i + 12], 20, -1926607734);

    a = md5_hh(a, b, c, d, x[i + 5], 4, -378558);
    d = md5_hh(d, a, b, c, x[i + 8], 11, -2022574463);
    c = md5_hh(c, d, a, b, x[i + 11], 16, 1839030562);
    b = md5_hh(b, c, d, a, x[i + 14], 23, -35309556);
    a = md5_hh(a, b, c, d, x[i + 1], 4, -1530992060);
    d = md5_hh(d, a, b, c, x[i + 4], 11, 1272893353);
    c = md5_hh(c, d, a, b, x[i + 7], 16, -155497632);
    b = md5_hh(b, c, d, a, x[i + 10], 23, -1094730640);
    a = md5_hh(a, b, c, d, x[i + 13], 4, 681279174);
    d = md5_hh(d, a, b, c, x[i + 0], 11, -358537222);
    c = md5_hh(c, d, a, b, x[i + 3], 16, -722521979);
    b = md5_hh(b, c, d, a, x[i + 6], 23, 76029189);
    a = md5_hh(a, b, c, d, x[i + 9], 4, -640364487);
    d = md5_hh(d, a, b, c, x[i + 12], 11, -421815835);
    c = md5_hh(c, d, a, b, x[i + 15], 16, 530742520);
    b = md5_hh(b, c, d, a, x[i + 2], 23, -995338651);

    a = md5_ii(a, b, c, d, x[i + 0], 6, -198630844);
    d = md5_ii(d, a, b, c, x[i + 7], 10, 1126891415);
    c = md5_ii(c, d, a, b, x[i + 14], 15, -1416354905);
    b = md5_ii(b, c, d, a, x[i + 5], 21, -57434055);
    a = md5_ii(a, b, c, d, x[i + 12], 6, 1700485571);
    d = md5_ii(d, a, b, c, x[i + 3], 10, -1894986606);
    c = md5_ii(c, d, a, b, x[i + 10], 15, -1051523);
    b = md5_ii(b, c, d, a, x[i + 1], 21, -2054922799);
    a = md5_ii(a, b, c, d, x[i + 8], 6, 1873313359);
    d = md5_ii(d, a, b, c, x[i + 15], 10, -30611744);
    c = md5_ii(c, d, a, b, x[i + 6], 15, -1560198380);
    b = md5_ii(b, c, d, a, x[i + 13], 21, 1309151649);
    a = md5_ii(a, b, c, d, x[i + 4], 6, -145523070);
    d = md5_ii(d, a, b, c, x[i + 11], 10, -1120210379);
    c = md5_ii(c, d, a, b, x[i + 2], 15, 718787259);
    b = md5_ii(b, c, d, a, x[i + 9], 21, -343485551);

    a = safe_add(a, olda);
    b = safe_add(b, oldb);
    c = safe_add(c, oldc);
    d = safe_add(d, oldd);
  }
  return [a, b, c, d];
}

/*
 * These functions implement the four basic operations the algorithm uses.
 */
function md5_cmn(q, a, b, x, s, t) {
  return safe_add(bit_rol(safe_add(safe_add(a, q), safe_add(x, t)), s), b);
}
function md5_ff(a, b, c, d, x, s, t) {
  return md5_cmn(b & c | ~b & d, a, b, x, s, t);
}
function md5_gg(a, b, c, d, x, s, t) {
  return md5_cmn(b & d | c & ~d, a, b, x, s, t);
}
function md5_hh(a, b, c, d, x, s, t) {
  return md5_cmn(b ^ c ^ d, a, b, x, s, t);
}
function md5_ii(a, b, c, d, x, s, t) {
  return md5_cmn(c ^ (b | ~d), a, b, x, s, t);
}

/*
 * Add integers, wrapping at 2^32. This uses 16-bit operations internally
 * to work around bugs in some JS interpreters.
 */
function safe_add(x, y) {
  var lsw = (x & 0xFFFF) + (y & 0xFFFF);
  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
  return msw << 16 | lsw & 0xFFFF;
}

/*
 * Bitwise rotate a 32-bit number to the left.
 */
function bit_rol(num, cnt) {
  return num << cnt | num >>> 32 - cnt;
}

/*
 * Convert a string to an array of little-endian words
 * If chrsz is ASCII, characters >255 have their hi-byte silently ignored.
 */
function str2binl(str) {
  var bin = [];
  var mask = (1 << chrsz) - 1;
  for (var i = 0; i < str.length * chrsz; i += chrsz) {
    bin[i >> 5] |= (str.charCodeAt(i / chrsz) & mask) << i % 32;
  }return bin;
}

/*
 * Convert an array of little-endian words to a hex string.
 */
function binl2hex(binarray) {
  var hex_tab =  "0123456789abcdef";
  var str = "";
  for (var i = 0; i < binarray.length * 4; i++) {
    str += hex_tab.charAt(binarray[i >> 2] >> i % 4 * 8 + 4 & 0xF) + hex_tab.charAt(binarray[i >> 2] >> i % 4 * 8 & 0xF);
  }
  return str;
}

/**
 * 字符串转化为字符码数组
 * 如： 'ab' => [97, 98]
 */

var arr = (function (s) {
  var arr = s.split('');
  var result = [],
      length = arr.length;
  for (var i = 0; i < length; i++) {
    result.push(arr[i].charCodeAt());
  }
  return result;
});

/**
 * 计算数组总和
 * @param a {Array}
 * @return {Number}
 */

var sum = (function (a) {
  var sum = 0;
  for (var i = 0; i < a.length; i++) {
    sum += parseInt(a[i]);
  }
  return sum;
});

/**
 * 整数转为二进制字符串表示，等效于 Number.toString(2)
 * @param n {Number}
 * @returns {String}
 */

var bin = (function (n) {
  var r = [],
      neg = n < 0;
  n = parseInt(neg ? -n : n, 10);

  do {
    var remain = n % 2;
    r.unshift(remain);
    n = (n - remain) / 2;
  } while (n > 0);

  return '' + (neg ? '-' : '') + r.join('');
});

/**
 * 异或运算，等效于 ^
 * @param a {Number} a > 0
 * @param b {Number} b > 0
 * @returns {Number}
 */

var xor = (function (a, b) {
  var binA = bin(a).split('');
  var binB = bin(b).split('');

  var lengthA = binA.length,
      lengthB = binB.length;
  var length = Math.min(lengthA, lengthB);

  var result = [];
  for (var i = 0; i < length; i++) {
    result.unshift([] + [] + (binA.pop() == binB.pop() ? +[] : +!![])); // '0' : '1'
  }

  if (lengthA > length) {
    result = binA.concat(result);
  }
  if (lengthB > length) {
    result = binB.concat(result);
  }
  return parseInt(result.join(''), 2);
});

var s = "_" + ([] + [] + ![])[!![] + !![] + !![]]; // 's' == ('false')[3]

/**
 * 合并签名和时间戳
 * @param s {String} 长度 16 位
 * @param t {Number} 时间戳
 * @return {String}
 */
var mix = (function (s, t) {
  var tsHex = t.toString(36);
  var fn = ([] + [] + ![])[!![] + !![] + !![]] + ([] + [] + [][+[]])[+[]] + 'b' + ([] + [] + ![])[!![] + !![] + !![]] + ([] + [] + !![])[+[]] + 'r'; // substr
  return '' + s[fn](+[], 8) + tsHex + s[fn](-8);
});

/**
 * Array.filter
 * @param a {Array}
 * @param f {Function} (item, index) => Boolean
 * @returns {Array}
 */

var filter = (function (a, f) {
  var result = [];
  for (var i = 0; i < a.length; i++) {
    f(a[i], i) && result.push(a[i]);
  }
  return result;
});

/**
 * 对时间戳字符串进行反转，第一位不动，最后一位可能为 0，反转后会丢失
 * @params ts {number}
 */
var reverse = (function (ts) {
  ts = ts.toString();
  var length = ts.length;
  var result = ts[0];
  for (var i = 0; i < length - 1; i++) {
    result += ts[length - 1 - i];
  }
  return parseInt(result);
});

function _sign() {

  var arg = ts();

  var count = function count(int) {
    var binary = bin(int);
    return sum(binary.split(''));
  };

  var xorTs = function xorTs(int) {
    return xor(count(arg), int);
  };

  var tsSum = sum(arr(arg.toString())); // 求 ts 的 Char Code 总和
  var tsCount = count(tsSum); // 求总和的二进制表示数中 1 的个数
  var tsXor = xorTs(tsCount); // 与 ts 中 1 的个数异或

  var md5String = hex_md5('' + tsSum + tsXor + arg);
  var offset = arg % 2;
  var signature = filter(md5String.split(''), function (_, i) {
    return (i + offset) % 2;
  }).join('');

  return mix(signature, reverse(arg));
}

function sign() {
  var result = {};
  result[s] = _sign();
  return result;
}

function appendTo(api) {
  var signature = _sign();

  var signatureStr = s + '=' + signature;

  var parser = document.createElement('a');
  parser.href = api;

  var search = parser.search;
  parser.search = search + (search ? '&' : '') + signatureStr;

  return parser.href;
}

var index = {
  sign: sign,
  appendTo: appendTo
};

return index;

})));
});

var ranges = ["\uD83C[\uDF00-\uDFFF]", "\uD83D[\uDC00-\uDE4F]", "\uD83D[\uDE80-\uDEFF]"];
var reg = new RegExp(ranges.join('|'), 'g');
var isEmoji = function isEmoji(str) {
  if (str === void 0) {
    str = '';
  }

  return ("" + str).match(reg) !== null;
};

var loadFetchApis = function loadFetchApis() {
  var fetchInstance;

  if (process.env.TARO_ENV === 'h5') {
    var UA = window && window.navigator.userAgent.toLowerCase();

    if (/muapp\//i.test(UA)) {
      fetchInstance = require('./index.h5')["default"];
    } else {
      fetchInstance = require('./index.h5')["default"];
    }
  } else if (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'alipay' || process.env.TARO_ENV === 'swan' || process.env.TARO_ENV === 'tt' || process.env.TARO_ENV === 'qq') {
    fetchInstance = require('./index.mp')["default"];
  } else {
    fetchInstance = require('./index.h5')["default"];
  }

  return fetchInstance;
};

var fetchInstance = loadFetchApis();

function isObject(value) {
  var type = _rollupPluginBabelHelpers._typeof(value);

  return value != null && (type === 'object' || type === 'function');
}
var freeGlobal = (typeof global === "undefined" ? "undefined" : _rollupPluginBabelHelpers._typeof(global)) === 'object' && global !== null && global.Object === Object && global;
var freeGlobalThis = (typeof globalThis === "undefined" ? "undefined" : _rollupPluginBabelHelpers._typeof(globalThis)) === 'object' && globalThis !== null && globalThis.Object == Object && globalThis;
var freeSelf = (typeof self === "undefined" ? "undefined" : _rollupPluginBabelHelpers._typeof(self)) === 'object' && self !== null && self.Object === Object && self;
var root = freeGlobalThis || freeGlobal || freeSelf || Function('return this')();
function debounce(func, wait, options) {
  var lastArgs;
  var lastThis;
  var maxWait;
  var result;
  var timerId;
  var lastCallTime;
  var lastInvokeTime = 0;
  var leading = false;
  var maxing = false;
  var trailing = true;
  var useRAF = !wait && wait !== 0 && typeof root.requestAnimationFrame === 'function';

  if (typeof func !== 'function') {
    throw new TypeError('Expected a function');
  }

  wait = +wait || 0;

  if (isObject(options)) {
    leading = !!options.leading;
    maxing = 'maxWait' in options;
    maxWait = maxing ? Math.max(+options.maxWait || 0, wait) : maxWait;
    trailing = 'trailing' in options ? !!options.trailing : trailing;
  }

  function invokeFunc(time) {
    var args = lastArgs;
    var thisArg = lastThis;
    lastArgs = lastThis = undefined;
    lastInvokeTime = time;
    result = func.apply(thisArg, args);
    return result;
  }

  function startTimer(pendingFunc, wait) {
    if (useRAF) {
      root.cancelAnimationFrame(timerId);
      return root.requestAnimationFrame(pendingFunc);
    }

    return setTimeout(pendingFunc, wait);
  }

  function cancelTimer(id) {
    if (useRAF) {
      return root.cancelAnimationFrame(id);
    }

    clearTimeout(id);
  }

  function trailingEdge(time) {
    timerId = undefined;

    if (trailing && lastArgs) {
      return invokeFunc(time);
    }

    lastArgs = lastThis = undefined;
    return result;
  }

  function remainingWait(time) {
    var timeSinceLastCall = time - lastCallTime;
    var timeSinceLastInvoke = time - lastInvokeTime;
    wait = wait || 0;
    var timeWaiting = wait - timeSinceLastCall;
    return maxing ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting;
  }

  function shouldInvoke(time) {
    var timeSinceLastCall = time - lastCallTime;
    var timeSinceLastInvoke = time - lastInvokeTime;
    return lastCallTime === undefined || timeSinceLastCall >= (wait || 0) || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;
  }

  function timerExpired() {
    var time = Date.now();

    if (shouldInvoke(time)) {
      return trailingEdge(time);
    }

    timerId = startTimer(timerExpired, remainingWait(time));
  }

  function leadingEdge(time) {
    lastInvokeTime = time;
    timerId = startTimer(timerExpired, wait);
    return leading ? invokeFunc(time) : result;
  }

  function cancel() {
    if (timerId !== undefined) {
      cancelTimer(timerId);
    }

    lastInvokeTime = 0;
    lastArgs = lastCallTime = lastThis = timerId = undefined;
  }

  function flush() {
    return timerId === undefined ? result : trailingEdge(Date.now());
  }

  function pending() {
    return timerId !== undefined;
  }

  function debounced() {
    var args = [];

    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }

    var time = Date.now();
    var isInvoking = shouldInvoke(time);
    lastArgs = args;
    lastThis = this;
    lastCallTime = time;

    if (isInvoking) {
      if (timerId === undefined) {
        return leadingEdge(lastCallTime);
      }

      if (maxing) {
        timerId = startTimer(timerExpired, wait);
        return invokeFunc(lastCallTime);
      }
    }

    if (timerId === undefined) {
      timerId = startTimer(timerExpired, wait);
    }

    return result;
  }

  debounced.cancel = cancel;
  debounced.flush = flush;
  debounced.pending = pending;
  return debounced;
}

function throttle(func, wait, options) {
  var leading = true;
  var trailing = true;

  if (typeof func !== 'function') {
    throw new TypeError('Expected a function');
  }

  if (isObject(options)) {
    leading = 'leading' in options ? !!options.leading : leading;
    trailing = 'trailing' in options ? !!options.trailing : trailing;
  }

  return debounce(func, wait, {
    leading: leading,
    trailing: trailing,
    maxWait: wait
  });
}

var nativeRequest = fetchInstance.request;

var pureRequest = function pureRequest(options) {
  return _rollupPluginBabelHelpers.__awaiter(void 0, void 0, Promise, function () {
    var contentTypeArr, formDataFilter, setCookieWhenRequest, k;
    return _rollupPluginBabelHelpers.__generator(this, function (_a) {
      if (!options.url) {
        console.error('url is required');
        return [2];
      }

      options.header = _rollupPluginBabelHelpers.__assign({
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
      }, options.header);

      if (options.headers) {
        options.header = _rollupPluginBabelHelpers.__assign(_rollupPluginBabelHelpers.__assign({}, options.header), options.headers);

        try {
          delete options.headers;
        } catch (error) {
          console.log("delete options headers occur error: " + error);
        }
      }

      contentTypeArr = ['Content-Type', 'content-type'];

      if (options.header) {
        formDataFilter = contentTypeArr.filter(function (key) {
          return options.header[key] === 'multipart/form-data';
        });

        if (formDataFilter && formDataFilter.length > 0) {
          contentTypeArr.map(function (key) {
            try {
              delete options.header[key];
            } catch (error) {
              console.log("delete options header Content-Type occur error: " + error);
            }
          });
        }

        try {
          delete options.header['content-type'];
        } catch (error) {
          console.log("delete options header content-type occur error: " + error);
        }
      }

      if (process.env.TARO_ENV === 'h5' || typeof window !== 'undefined') {
        options.credentials = options.credentials || 'include';
      } else {
        setCookieWhenRequest = require('./cookie/cookie-shim').setCookieWhenRequest;
        setCookieWhenRequest(options);
      }

      if (options.data) {
        for (k in options.data) {
          if (isEmoji(options.data[k])) {
            console.error('不支持Emoji表情符');
            return [2];
          }
        }
      }

      options.invokeApiType = options.invokeApiType || 'pureRequest';

      if (options.throttle) {
        nativeRequest = throttle(nativeRequest, options.throttleWait || 200);
      }

      return [2, nativeRequest(options).then(function (res) {
        var data = res.data;
        return Promise.resolve(data.clone());
      })["catch"](function (error) {
        return Promise.reject(error);
      })];
    });
  });
};

function timeoutInterceptor(chain) {
  var requestParams = chain.requestParams;
  var p;
  var res = new Promise(function (resolve, reject) {
    var timeout = setTimeout(function () {
      timeout = null;
      reject(new Error('网络链接超时,请稍后再试！'));
    }, requestParams && requestParams.timeout || 60000);
    p = chain.proceed(requestParams);
    p.then(function (res) {
      if (!timeout) return;
      clearTimeout(timeout);
      resolve(res);
    })["catch"](function (err) {
      timeout && clearTimeout(timeout);
      reject(err);
    });
  });
  if (p.abort) res.abort = p.abort;
  return res;
}
function logInterceptor(chain) {
  var requestParams = chain.requestParams;
  var method = requestParams.method,
      data = requestParams.data,
      url = requestParams.url;
  console.log("http " + (method || 'GET') + " --> " + url + " data: ", data);
  var p = chain.proceed(requestParams);
  var res = p.then(function (res) {
    console.log("http <-- " + url + " result:", res);
    return res;
  });
  if (p.abort) res.abort = p.abort;
  return res;
}

var request = function request(options) {
  return _rollupPluginBabelHelpers.__awaiter(void 0, void 0, Promise, function () {
    var sign, defaultSign, reqEnvParams, reqEnvData, supplementEnvData, optionMethod;
    return _rollupPluginBabelHelpers.__generator(this, function (_a) {
      sign = '';

      try {
        sign = wxapp.sign()._s || '';
      } catch (error) {
        sign = '';
      }

      defaultSign = {
        sign: sign
      };
      options.data = options.data || {};
      reqEnvParams = '';
      reqEnvData = {};

      try {
        reqEnvParams = options.reqEnvParams || '';
        reqEnvData = JSON.parse(reqEnvParams);
      } catch (error) {
        reqEnvParams = '';
        reqEnvData = {};
      }

      supplementEnvData = reqEnvParams ? reqEnvData : {};

      try {
        options.data.reqEnvParams = JSON.stringify(_rollupPluginBabelHelpers.__assign(_rollupPluginBabelHelpers.__assign({}, defaultSign), supplementEnvData));
      } catch (error) {
        options.data.reqEnvParams = '';
      }

      optionMethod = (options.method || 'GET').toUpperCase();

      if (optionMethod === 'GET') {
        options.data = _rollupPluginBabelHelpers.__assign({}, options.data);
      } else if (optionMethod === 'POST') {
        if (typeof FormData !== 'undefined' && options.data instanceof FormData) {
          options.data.set('reqEnvParams', options.data.reqEnvParams);
        }
      }

      options.invokeApiType = 'request';
      return [2, pureRequest(options).then(function (res) {
        return _rollupPluginBabelHelpers.__awaiter(void 0, void 0, Promise, function () {
          return _rollupPluginBabelHelpers.__generator(this, function (_a) {
            return [2, Promise.resolve(res.clone())];
          });
        });
      })["catch"](function (error) {
        return Promise.reject(error);
      })];
    });
  });
};

var customFetch = function customFetch(options) {
  return _rollupPluginBabelHelpers.__awaiter(void 0, void 0, Promise, function () {
    var sign, defaultSign, EnvParams, reqEnvParams, reqEnvData, isEnvParamObject, isEnvParamString, isEnvDefined, paramDefine, target, supplementEnvData, envData, optionMethod;
    return _rollupPluginBabelHelpers.__generator(this, function (_a) {
      sign = '';

      try {
        sign = wxapp.sign()._s || '';
      } catch (error) {
        sign = '';
      }

      defaultSign = {
        sign: sign
      };
      options.data = options.data || {};

      try {
        EnvParams = options.data.EnvParams || '';
        reqEnvParams = options.data.reqEnvParams || '';
      } catch (error) {
        EnvParams = '';
        reqEnvParams = '';
      }

      isEnvParamObject = helper.isObject(EnvParams) && Object.keys(EnvParams || {}).length !== 0;
      isEnvParamString = helper.isString(EnvParams) && EnvParams.length > 0;
      isEnvDefined = isEnvParamObject || isEnvParamString;
      paramDefine = ['EnvParams', 'reqEnvParams'];
      target = isEnvDefined ? EnvParams : reqEnvParams;

      try {
        reqEnvData = helper.isString(target) ? JSON.parse(target) : target;
      } catch (error) {
        reqEnvData = {};
      }

      supplementEnvData = helper.isObject(reqEnvData) ? reqEnvData : {};
      envData = JSON.stringify(_rollupPluginBabelHelpers.__assign(_rollupPluginBabelHelpers.__assign({}, defaultSign), supplementEnvData));
      paramDefine.forEach(function (element) {
        options.data[element] = envData;
      });
      optionMethod = (options.method || 'GET').toUpperCase();

      if (optionMethod === 'GET') {
        options.data = _rollupPluginBabelHelpers.__assign({}, options.data);
      } else if (optionMethod === 'POST') {
        if (typeof FormData !== 'undefined' && options.data instanceof FormData) {
          try {
            paramDefine.forEach(function (element) {
              options.data.set(element, envData);
            });
          } catch (error) {
            console.warn("convert data into FormData occur error: " + JSON.stringify(error));
          }
        }
      }

      options.invokeApiType = 'customFetch';
      return [2, pureRequest(options).then(function (res) {
        return _rollupPluginBabelHelpers.__awaiter(void 0, void 0, Promise, function () {
          var resClone;
          return _rollupPluginBabelHelpers.__generator(this, function (_a) {
            resClone = res.clone();
            return [2, Promise.resolve(resClone.json())];
          });
        });
      })["catch"](function (error) {
        return Promise.reject(error);
      })];
    });
  });
};

var addInterceptor = fetchInstance.addInterceptor;
var cleanInterceptors = fetchInstance.cleanInterceptors;

var setMiniCookies = function () {
  if (process.env.TARO_ENV !== 'h5') {
    var setMiniCookies_1 = require('./cookie/cookie-shim').setMiniCookies;

    return setMiniCookies_1;
  } else {
    return function () {
      console.error('h5渠道不支持该api');
    };
  }
}();
var index = {
  request: request,
  pureRequest: pureRequest,
  customFetch: customFetch,
  addInterceptor: addInterceptor,
  cleanInterceptors: cleanInterceptors,
  timeoutInterceptor: timeoutInterceptor,
  logInterceptor: logInterceptor,
  setMiniCookies: setMiniCookies
};

exports.addInterceptor = addInterceptor;
exports.cleanInterceptors = cleanInterceptors;
exports.customFetch = customFetch;
exports.default = index;
exports.logInterceptor = logInterceptor;
exports.pureRequest = pureRequest;
exports.request = request;
exports.setMiniCookies = setMiniCookies;
exports.timeoutInterceptor = timeoutInterceptor;
