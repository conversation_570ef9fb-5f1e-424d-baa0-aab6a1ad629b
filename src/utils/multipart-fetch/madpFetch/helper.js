'use strict';

var _rollupPluginBabelHelpers = require('./_rollupPluginBabelHelpers.js');

function isObject(target) {
  return Object.prototype.toString.call(target) === '[object Object]';
}
function isString(target) {
  return Object.prototype.toString.call(target) === '[object String]';
}
function serializeParams(params) {
  if (!params) {
    return '';
  }

  return Object.keys(params).map(function (key) {
    return encodeURIComponent(key) + "=" + (_rollupPluginBabelHelpers._typeof(params[key]) === 'object' ? encodeURIComponent(JSON.stringify(params[key])) : encodeURIComponent(params[key]));
  }).join('&');
}
function generateRequestUrlWithParams(url, params) {
  params = typeof params === 'string' ? params : serializeParams(params);

  if (params) {
    url += (~url.indexOf('?') ? '&' : '?') + params;
  }

  url = url.replace('?&', '?');
  return url;
}
function transformRequestData(options) {
  options = options || {};
  var _a = options.method,
      method = _a === void 0 ? 'GET' : _a,
      url = options.url;

  if (!options || !options.url) {
    return '';
  }

  var methodUpper = method.toUpperCase();

  if (methodUpper === 'HEAD') {
    options.url = generateRequestUrlWithParams(url, options.data);
  } else if (methodUpper === 'GET') {
    options.data = options.data;
  } else if (_rollupPluginBabelHelpers._typeof(options.data) === 'object') {
    var contentType = options.header && (options.header['Content-Type'] || options.header['content-type']);

    if (contentType && contentType.indexOf('application/json') >= 0) {
      options.data = JSON.stringify(options.data);
    } else if (contentType && contentType.indexOf('application/x-www-form-urlencoded') >= 0) {
      options.data = serializeParams(options.data);
    } else {
      options.data = options.data;
    }
  } else {
    options.data = options.data;
  }
}

exports.generateRequestUrlWithParams = generateRequestUrlWithParams;
exports.isObject = isObject;
exports.isString = isString;
exports.serializeParams = serializeParams;
exports.transformRequestData = transformRequestData;
