import Madp from '@mu/madp';

function redirectFunc(url) {
  if (!url || typeof url !== 'string') return;

  let redirectUrl = url;

  if (process.env.TARO_ENV !== 'h5') {
    redirectUrl = `/pages/web-view/index?jumpUrl=${encodeURIComponent(url)}&type=default`;

    Madp.redirectTo({
      url: redirectUrl
    });
  } else {
    window.location.replace(redirectUrl);
  }
}

/**
 * 网关重定向处理装饰器
 *
 * @param {*} callback
 */
export function handleMgpRedirect(callback) {
  return async function handlerFunc(res) {
    if (res.status === 200 && typeof res.json === 'function') {
      const json = await res.json();
      const resData = json.data || {};
      if (json.ret === '0' && resData.status === 302) {
        redirectFunc(resData.location);
        return Promise.reject(res.clone());
      }
    }

    return callback(res.clone());
  };
}

/**
 * 携带重定向所需要的环境参数
 *
 */
export function addMgpRedirectInterceptor() {
  Madp.addInterceptor((chain) => {
    const {
      requestParams = {}
    } = chain;
    const {
      data = {}, invokeApiType = ''
    } = requestParams;
    // 如果是`pureRequest`，则不进行添加请求环境参数(`reqEnvParams`)
    if (invokeApiType === 'pureRequest') {
      return chain.proceed(requestParams);
    }

    const {
      reqEnvParams = {}
    } = data;

    let envParamsObject = {};
    if (reqEnvParams && typeof reqEnvParams === 'object') {
      envParamsObject = {
        ...reqEnvParams
      };
    } else if (reqEnvParams && typeof reqEnvParams === 'string') {
      try {
        envParamsObject = JSON.parse(reqEnvParams);
      } catch (error) {
        envParamsObject = {};
      }
    }

    const newEnvParams = {
      channel: Madp.getChannel(),
      appType: 'H5',
      module: 'repayment',
      ...envParamsObject
    };

    const newData = {
      ...data,
      reqEnvParams: JSON.stringify(newEnvParams)
    };
    const newRequestParams = {
      ...requestParams,
      data: newData
    };

    return chain.proceed(newRequestParams);
  });
}
