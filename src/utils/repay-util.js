/* eslint-disable max-len */
import Madp from '@mu/madp';
import Util from '@utils/maxin-util';
import CustomConfig from '@config/index';
import {
  isMuapp,
  isWechat,
  isAlipay,
  getWebViewName,
  isQQEnv,
} from '@mu/madp-utils';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { wechatAppId, wechatH5AppId, alipayAppId } from '@utils/url_config';
import { getStore, setStore } from '@api/store';
import appInfos from '@utils/app-config-infos';
import { apiHost, errCodeMap, errCodeToMsgMap } from '@utils/constants';
import { transformErrCode, accSub, getDisplaySettleWaiveAmt } from '@utils/payMethod';
import { getPageData, getProductAllParams } from '@mu/business-basic';

// 优惠券接口账单入参处理
export function parseRepayOrderList(billList) {
  let parseList = [];
  parseList = (billList || []).map((bill) => {
    if (!bill.orderNo) return {};
    const newBill = {};
    let feeAmt = (+bill.surplusPayInteAmt) + (+bill.surplusPayPeriodFeeAmt) + (+bill.surplusPayFineAmt);
    feeAmt = String(parseFloat(feeAmt).toFixed(2));
    // newBill.interestAmt = bill.surplusPayInteAmt; // 利息或费用
    newBill.orderNo = bill.orderNo; // 订单号
    newBill.topMerchantId = bill.merchantNo; // 商户号
    newBill.installCnt = JSON.stringify(bill.installCnt || bill.installTotalCnt || ''); // 期数 当期期数
    newBill.debtDate = (bill.loanDate).toString().replace(/\./g, ''); // 借款日期
    newBill.discount = bill.useCouponFlag; // 是否已优惠
    let periodRepayCnt = '';
    if (bill.onTimeNum === 0 || bill.onTimeNum) {
      periodRepayCnt = bill.onTimeNum;
    } else if (bill.installTotalCnt && bill.surplusInstallCnt) {
      periodRepayCnt = bill.installTotalCnt - bill.surplusInstallCnt;
    }
    newBill.periodRepayCnt = periodRepayCnt; // 按时还款期数
    newBill.limitType = bill.limitType;
    newBill.feeAmt = feeAmt; // 服务费+利息或费用
    newBill.rateType = bill.loanType === 'F' ? '0' : '1'; //  利率还是费率 string 0是费率 1是利率
    newBill.debtPrincipalAmt = bill.installTotalAmt; // 借据金额
    newBill.productCode = bill.productNo; // 产品编码
    newBill.loanStatus = bill.loanStatus; // 借据状态
    newBill.prepayAmt = bill.payPrepayFee || ''; // 还款手续费
    if (bill.waiveChannelsPrepayFee && +bill.waiveChannelsPrepayFee > 0) {
      newBill.prepayAmt = '0.00';
    }
    newBill.principalAmt = bill.surplusPayPrincipalAmt; //  当期应还本金本金
    newBill.businessType = bill.businessType; // 类别
    newBill.debtChannel = bill.transChannelCode; // 借据渠道
    newBill.supportSplitFlag = bill.canUseManualCoupon; // 是否可以使用216券 默认Y；N-不可使用(0:100拆分用券新增)
    newBill.useTempPriceFlag = bill.useTempPriceFlag; // 是否可以叠加价格，新权益接口
    return newBill;
  });
  return parseList;
}

/**
 * @description: 除了216券，其他券都放在不能用券
 * @param { Object } 优惠券信息对象 原始信息
 * @param { is7day: Boolean } 是否近期待还（近期代还需要判断借据是否结清，以识别是否能使用216结清券）
 * @param { isOnly216: Boolean } 是否仅能使用216券
 * @param { selectedBills: Array } 待还借据列表
 * @return { Object } 筛选过后的优惠券信息对象信息
 */
export function filterCoupon(CouponObj, is7day, isOnly216, selectedBills) {
  const {
    availableCouponDetailList,
    appExclusiveCouponDetailList,
    unavailableCouponDetailList,
  } = CouponObj || {};
  // console.log('***********filterCoupon', CouponObj, is7day, isOnly216, selectedBills);
  const { availableList, unavailablelist } = traverseCoupon(availableCouponDetailList, is7day, isOnly216, selectedBills);
  const {
    availableList: availableAPPList,
    unavailablelist: unavailableAPPlist,
  } = traverseCoupon(appExclusiveCouponDetailList, is7day, isOnly216, selectedBills);
  // 不可用券中查找216券
  unavailableCouponDetailList.forEach((item) => {
    if (item.awardType === '216') {
      if (item.waiveWayCode === '02' && !judgeClearCouponAvailable(selectedBills)) {
        item.unavailableReason = '结清借款才可用';
      }
    }
  });
  return {
    availableCouponDetailList: availableList,
    appExclusiveCouponDetailList: availableAPPList,
    unavailableCouponDetailList: [...unavailablelist, ...unavailableAPPlist, ...unavailableCouponDetailList]
  };
}

function traverseCoupon(couponList, is7day, isOnly216, selectedBills) {
  // console.log('***********traverseCoupon', couponList, is7day, isOnly216, selectedBills);
  const availableList = [];
  const unavailablelist = [];
  (couponList || []).forEach((item) => {
    let is216Coupon = item.awardType === '216'; // 是否216券
    let is216ClearCoupon = is216Coupon && item.waiveWayCode === '02'; // 是否216结清券

    // 结清券特殊在近期代还场景特殊判断，当前选中借据是否全部结清
    if (is216ClearCoupon) {
      if (is7day && !judgeClearCouponAvailable(selectedBills)) {
        item.unavailableReason = '结清借款才可用';
        unavailablelist.push(item);
      } else {
        availableList.push(item);
      }
      return;
    }
    if (isOnly216) {
      if (is216Coupon) {
        availableList.push(item);
      } else {
        unavailablelist.push(item);
      }
    } else {
      availableList.push(item);
    }
  });
  // console.log('***********traverseCouponEND', availableList, unavailablelist);
  return { availableList, unavailablelist };
}

/**
 * @description: 判断7天待还场景下，当前选中还款的借据是否能使用216结清券
 * @return boolean
 */
function judgeClearCouponAvailable(selectedBills) {
  const allOrderSet = new Set(); // 全部借据列表（去重）
  const clearOrderSet = new Set(); // 结清借据列表（去重）
  selectedBills.forEach((bill) => {
    allOrderSet.add(bill.orderNo);
    if (bill.installCnt === bill.installTotalCnt) {
      clearOrderSet.add(bill.orderNo);
    }
  });
  // console.log('judgeClearCouponAvailable', allOrderSet.size, clearOrderSet.size);
  return allOrderSet.size === clearOrderSet.size;
}

/**
 * @description: 获取可以设置为默认优惠券的优惠券，目前只有息费减免券可以
 * @param { Array } couponList 优惠券列表
 * @param { String } awardNo 优惠券号
 * @return { Object } awardNo 一个优惠券的
 */
export const getdefaultCheckCouponId = (couponList, awardNo) => {
  let award = {};
  let defaultCoupon = {};
  if (awardNo) {
    defaultCoupon = couponList.find((item) => item.awardNo === awardNo) || {};
  } else {
    defaultCoupon = couponList.find((item) => item.awardType === '216') || {};
  }
  if (Object.keys(defaultCoupon).length) {
    award = defaultCoupon;
  }
  return award;
};

/**
 * @description: 组装参数字段 repayDetailList。逾期打标返回空
 * @param { Array } billList 账单列表
 * @param { string } billType 账单类型
 * @return { Object } 账单参数
 */
export function prepareRepayDetailList(billList, billType) {
  const result = [];
  (billList || []).forEach((bill) => {
    const temp = {};
    temp.orderNo = bill.orderNo;
    temp.repayAmt = bill.surplusPayTotalAmt;
    temp.debtorSplitDetailList = bill.debtorSplitDetails;
    if (billType === '7days' || billType === 'extend') {
      temp.instCnt = bill.installCnt;
    } else {
      temp.instCnt = 0;
    }
    result.push(temp);
  });
  return result;
}

// 是否包含0:100 借据
export function is0100Bills(selectedBills) {
  if (!selectedBills || selectedBills.length === 0) return 'normalBill';
  let mixedBillFlag;
  let normalBillFlag;
  let only0100Bill;
  let normalBill;
  mixedBillFlag = selectedBills.some((item) => item.debtorSplitDetailList && item.debtorSplitDetailList.length === 1); // 是否含0:100
  normalBillFlag = selectedBills.some((item) => !item.debtorSplitDetailList); // 是否含非 0:100
  // 试算后 仅0:100借据判断条件
  only0100Bill = selectedBills.every((item) => item.debtorSplitDetailList && item.debtorSplitDetailList.length === 1);
  // 试算后 非0:100 也就是 招联借据判断条件
  normalBill = selectedBills.every((item) => !item.debtorSplitDetailList);

  // 混合借据判断条件
  const minxBill = mixedBillFlag && normalBillFlag;
  let flag;
  if (only0100Bill) {
    flag = 'only0100Bill'; // 仅 0:100
  } else if (minxBill) {
    flag = 'minxBill'; // 0:100 混合借据（0:100 + 非0:100）
  } else if (normalBill) {
    flag = 'normalBill'; // 仅 非0:100
  } else {
    flag = 'normalBill'; // 仅 非0:100
  }
  return flag;
}
/*
 * 对银行卡进行筛选排序
 * 第1部分：默认卡（可用而且不用拆分，所以可能不存在）
 * 第2部分：不需要补签而且不用拆分；
 * 第3部分：需要补签才能使用的；
 * 第4部分：不需要补签但要拆分（包括超限额的）
 * 第5部分：不能使用的（例如无法补签资方的、有优惠券场景又要拆分的）；
 * 2345部分都按照单笔金额降序排序 (产品：陈福明)
 */
export function commonSortBankList(originalCards, coupon, selectedBillCapitalsList, setSelectCardsStatus, billType) {
  // 筛选支持的卡，将资方不支持的卡放入bankCardErrorStatusList
  const allSignedCard = []; // 签约所有资方的银行卡id。第1部分+第2部分
  const needSignCard = []; // 支持所有资方但需要补签的银行卡id。第3部分
  const limitCard = []; // 需要拆分的卡。第4部分
  const errorCard = []; // 临时设置一个值来承载不支持银行卡卡号。第5部分
  const overLimitCards = [];
  let bankCards = [];
  originalCards.forEach((card) => {
    const {
      bankCardId,
      signedFundList = [], // 银行卡已签约资方
      needSignFundList = [], // 银行卡需补签资方
      satisfyLimitAmtStatus,
      supplementSignFlag,
      status, // 银行卡状态，0：启用，1：禁用-已销户
    } = card;
    // 销户卡不可用
    if (status === '1') {
      setSelectCardsStatus(bankCardId, '卡已销户', false, false, 'close');
      errorCard.push(card);
      return;
    }
    // 省心分/息费减免不能大额拆分
    if (['advanced-stage', 'fee-reduce'].indexOf(billType) !== -1 && satisfyLimitAmtStatus === 'N') {
      setSelectCardsStatus(bankCardId, '还款金额超限额', false, false);
      errorCard.push(card);
      return;
    }
    // 如果不含0：100借据，那么allSignedCard包含所有的银行卡
    // console.log(selectedBillCapitalsList, 'selectedBillCapitalsList1111111');

    for (let i = 0, len = selectedBillCapitalsList.length; i < len; i += 1) {
      const capital = selectedBillCapitalsList[i];
      // 银行卡的资方已签约列表和待签约列表都没有选中账单的资方信息，则为不支持
      if (signedFundList.indexOf(capital.fundNo) === -1
        && needSignFundList.indexOf(capital.fundNo) === -1) {
        setSelectCardsStatus(bankCardId, '暂不支持还款', false, false);
        errorCard.push(card);
        return;
      }
    }
    // 需要大额拆分的银行卡
    if (satisfyLimitAmtStatus === 'N' && supplementSignFlag === 'N') {
      overLimitCards.push(bankCardId);
      // 选了优惠券，那么需要拆分支付的银行卡就不能使用
      if (coupon.awardNo) {
        setSelectCardsStatus(bankCardId, '暂不支持还款', false, false, 'limit');
        errorCard.push(card);
        return;
      } else {
        limitCard.push(card);
        return;
      }
    }
    if (supplementSignFlag === 'N' && (!needSignFundList || needSignFundList.length === 0)) { // 不需要补签的卡
      allSignedCard.push(card);
    } else {
      needSignCard.push(card);
    }
  });

  const firstCard = allSignedCard.shift(); // 第一部分
  // 按照单笔限额排序
  allSignedCard.sort((min, max) => +max.singleDeductionLimit - +min.singleDeductionLimit);
  needSignCard.sort((min, max) => +max.singleDeductionLimit - +min.singleDeductionLimit);
  limitCard.sort((min, max) => +max.singleDeductionLimit - +min.singleDeductionLimit);
  errorCard.sort((min, max) => (min.status === max.status ? +max.singleDeductionLimit - +min.singleDeductionLimit : +min.status - +max.status));
  if (!firstCard) {
    bankCards = [...allSignedCard, ...needSignCard, ...limitCard, ...errorCard];
  } else {
    bankCards = [firstCard, ...allSignedCard, ...needSignCard, ...limitCard, ...errorCard];
  }

  return {
    overLimitCards,
    bankCards,
  };
}

/**
 * 新-对银行卡进行排序，和旧的共存
 */
export function commonSortNewBankList(originalCards, coupon, selectedBillCapitalsList, setSelectCardsStatus, bill0100Flag) {
  const errorCard = []; // 不支持使用的卡 比如 不满足条件 或者被禁用的卡
  originalCards.forEach((card) => {
    const {
      bankCardId,
      signedFundList = [], // 银行卡已签约资方
      needSignFundList = [], // 银行卡需补签资方
      status, // 银行卡状态，0：启用，1：禁用-已销户
    } = card;

    // 销户卡不可用
    if (status === '1') {
      setSelectCardsStatus(bankCardId, '卡已销户', false, false, 'close');
      errorCard.push(card);
    }

    // 如果不含0：100借据
    // console.log(selectedBillCapitalsList, 'selectedBillCapitalsList新绑卡');
    for (let i = 0, len = selectedBillCapitalsList.length; i < len; i++) {
      const capital = selectedBillCapitalsList[i];
      // 银行卡的资方已签约列表和待签约列表都没有选中账单的资方信息，则为不支持
      if (signedFundList.indexOf(capital.fundNo) === -1
        && needSignFundList.indexOf(capital.fundNo) === -1) {
        setSelectCardsStatus(bankCardId, '本次还款不支持该银行卡', false, false);
        errorCard.push(card);
        return;
      }
    }

    // 需要大额拆分的银行卡 [0:100不送大额拆分标识也不要让用户选择此卡]
    // if (satisfyLimitAmtStatus === 'N' && supplementSignFlag === 'N') {
    //   // 选了优惠券，那么需要拆分支付的银行卡就不能使用
    //   if (coupon.awardNo) {
    //     this.setSelectCardsStatus(bankCardId, '暂不支持还款', false, false, 'limit');
    //     errorCard.push(card);
    //   }
    // }
  });
  // 排序
  // 大前提 签了资方+招联（已经签约）> 部分签约 + 支持补签  > 部分签约 + 不支持补签
  // 全部签约的卡
  const signedBankList = originalCards.filter((item) => item.signedFundList && item.signedFundList.length > 1);
  // 只签中银 + 补签招联
  const sectionSignBocBankList = originalCards.filter((item) => (item.signedFundList && item.signedFundList.length === 1 && item.signedFundList.includes('********')) && item.needSignFundList && item.needSignFundList.length === 1 && item.needSignFundList.includes('10000'));
  // 只签招联 + 补签中银
  const sectionSignMucBankList = originalCards.filter((item) => (item.signedFundList && item.signedFundList.length === 1 && item.signedFundList.includes('10000')) && item.needSignFundList && item.needSignFundList.length === 1 && item.needSignFundList.includes('********'));
  // 不属于 已签约 和 部分签约 并且 不支持补签的卡放在第三类
  const noSupplyBankList = originalCards.filter((item) => !item.signedFundList.length && item.supplementSignFlag === 'N');
  // console.log(sectionSignBocBankList, '只签中银 + 补签招联');
  // console.log(sectionSignMucBankList, '只签招联 + 补签中银');
  // console.log(errorCard, '错误卡');
  let signedBankTempList = []; // 承接全部签约的
  let sectionSignBocBankListTemp = []; // 承接 只签中银 + 补签招联
  let sectionSignMucBankListTemp = []; // 只签招联 + 补签中银

  // 代表有多张全部签约 然后继续按照 默认卡 + 限额 +  卡优先级 排序
  if (signedBankList.length > 1) {
    const defaultBankList = signedBankList.filter((item) => item.isDefault === 'Y'); // 默认卡
    const notDefaultBankList = signedBankList.filter((item) => item.isDefault === 'N'); // 非默认卡
    signedBankTempList = [...defaultBankList, ...notDefaultBankList];
    signedBankTempList.sort((min, max) => +max.singleDeductionLimit - +min.singleDeductionLimit); // 限额降序
    signedBankTempList.sort((min, max) => max.cardPri - min.cardPri); // 卡优先级降序
  } else if (signedBankList.length === 1) {
    // 只有一张这种卡时候
    signedBankTempList = [...signedBankList];
  }

  // 只签招联 + 补签中银 多张
  if (sectionSignMucBankList.length > 1) {
    const defaultBankList = sectionSignMucBankList.filter((item) => item.isDefault === 'Y'); // 默认卡
    const notDefaultBankList = sectionSignMucBankList.filter((item) => item.isDefault === 'N'); // 非默认卡
    sectionSignMucBankListTemp = [...defaultBankList, ...notDefaultBankList];
    sectionSignMucBankListTemp.sort((min, max) => +max.singleDeductionLimit - +min.singleDeductionLimit); // 限额降序
    sectionSignMucBankListTemp.sort((min, max) => max.cardPri - min.cardPri); // 卡优先级降序
  } else if (sectionSignMucBankList.length === 1) {
    // 只有一张这种卡时候
    sectionSignMucBankListTemp = [...sectionSignMucBankList];
  }

  // 只签中银 + 补签招联 多张
  if (sectionSignBocBankList.length > 1) {
    const defaultBankList = sectionSignBocBankList.filter((item) => item.isDefault === 'Y'); // 默认卡
    const notDefaultBankList = sectionSignBocBankList.filter((item) => item.isDefault === 'N'); // 非默认卡
    sectionSignBocBankListTemp = [...defaultBankList, ...notDefaultBankList];
    sectionSignBocBankListTemp.sort((min, max) => +max.singleDeductionLimit - +min.singleDeductionLimit); // 限额降序
    sectionSignBocBankListTemp.sort((min, max) => max.cardPri - min.cardPri); // 卡优先级降序
  } else if (sectionSignBocBankList.length === 1) {
    // 只有一张这种卡时候
    sectionSignBocBankListTemp = [...sectionSignBocBankList];
  }

  let allSectionBankList = []; // 部分签约所有卡
  let allSectionDefaultBankList = []; // 部分签约默认卡
  let supplementSignBankList = []; // 部分签约 + 支持补签
  let supplementNotSignBankList = []; // 部分签约 + 不支持补签
  let allSectionCardList = []; // 最终部分签约银行卡排序
  // 部分签约 如果AB只签了招联 CD只签了中银借据在混合借据和0:100时候是ABCD 纯0:100时候是CDAB
  if (bill0100Flag === 'minxBill') {
    // 如果是混合借据
    allSectionBankList = [...sectionSignMucBankListTemp, ...sectionSignBocBankListTemp];
    allSectionDefaultBankList = allSectionBankList.filter((item) => item.isDefault === 'Y'); // 默认卡
    supplementSignBankList = allSectionBankList.filter((item) => item.supplementSignFlag === 'Y' && item.needSignFundList.length === 1); // 支持补签
    supplementNotSignBankList = allSectionBankList.filter((item) => item.supplementSignFlag === 'N' && !item.needSignFundList.length); // 不支持补签
    allSectionCardList = [...allSectionDefaultBankList, ...allSectionBankList, ...supplementSignBankList, ...supplementNotSignBankList];
  } else if (bill0100Flag === 'only0100Bill') {
    // 如果是纯（仅仅）0:100
    allSectionBankList = [...sectionSignBocBankListTemp, ...sectionSignMucBankListTemp];
    allSectionDefaultBankList = allSectionBankList.filter((item) => item.isDefault === 'Y'); // 默认卡
    supplementSignBankList = allSectionBankList.filter((item) => item.supplementSignFlag === 'Y' && item.needSignFundList.length === 1); // 支持补签
    supplementNotSignBankList = allSectionBankList.filter((item) => item.supplementSignFlag === 'N' && !item.needSignFundList.length); // 不支持补签
    allSectionCardList = [...allSectionDefaultBankList, ...allSectionBankList, ...supplementSignBankList, ...supplementNotSignBankList];
  }

  const cardList = [...signedBankTempList, ...allSectionCardList, ...noSupplyBankList, ...errorCard];
  // 是否有新绑卡回来的卡
  // const allCardList = originalCards; // 承接所有卡接口返回的数据
  // const newCardInfo = Madp.getStorageSync('BANK_CARD_INFO_DATA', 'SESSION') || {};
  // const hasFirstCard = newCardInfo && newCardInfo.bankCard && newCardInfo.bankCard.cardId;
  // let firstCardList = []; // 第一张卡
  // if (hasFirstCard) {
  //   firstCardList = [allCardList.find((item) => item.bankCardId === hasFirstCard)];
  //   // console.log(firstCardList, '第一张卡', hasFirstCard, allCardList);
  // }
  // let cardList = [];
  // if (firstCardList && firstCardList.length) {
  //   cardList = [...firstCardList, ...signedBankTempList, ...allSectionCardList, ...noSupplyBankList, ...errorCard];
  // } else {
  //   cardList = [...signedBankTempList, ...allSectionCardList, ...noSupplyBankList, ...errorCard];
  // }
  // console.log('cardList', cardList);
  // console.log('firstCardList', firstCardList);
  // console.log('hasFirstCard', hasFirstCard);
  // 每张卡取第一次出现的位置作为排序顺序
  const cardTempList = cardList && cardList.filter((item, index) => cardList.findIndex((e) => (e.bankCardId === item.bankCardId)) === index);
  return {
    cardTempList
  };
}

/**
 * 获取查询银行卡接口需要的参数
 * trilFlag 金额或优惠券修改后是否进行了试算
 * bill0100Flag 是否是0:100 借据
 * afterTrialBillDetail 试算后的借据明细
 * selectedBillList 初始化的借据列表
 */
export function getFundTransAmtDetails(trilFlag, bill0100Flag, afterTrialBillDetail, selectedBillList) {
  // this.isAmountTrilFlag 是否修改金额的试算  fundTransAmtDetails 仅非0:100 送 []  仅0:100 送 中银商户号 + 金额 混合借据 一样送商户号 + 金额
  // this.isConpTrilFlag 是否修改优惠券试算
  let fundTransAmtList = [];
  let selectedBillAmountList = [];
  if (trilFlag) {
    if (bill0100Flag === 'only0100Bill') {
      fundTransAmtList = afterTrialBillDetail.map((item) => ({
        fundNo: item.debtorSplitDetailList && item.debtorSplitDetailList.length === 1 && item.debtorSplitDetailList[0].merchantNo,
        transAmt: item.canPayAmt
      }));
      selectedBillAmountList = Object.values(fundTransAmtList.reduce((res, item) => {
        // eslint-disable-next-line no-unused-expressions
        res[item.fundNo] || (res[item.fundNo] = { ...item, transAmt: 0 });
        res[item.fundNo].transAmt += Number(item.transAmt);
        return res;
      }, {}));

      selectedBillAmountList = selectedBillAmountList.map((e) => ({
        fundNo: e.fundNo,
        transAmt: (`${e.transAmt}`)
      }));
      // console.log(fundTransAmtList, 'fundTransAmtList试算后仅0:100借据');
    } else if (bill0100Flag === 'minxBill') {
      fundTransAmtList = afterTrialBillDetail.map((item) => ({
        fundNo: item.debtorSplitDetailList && item.debtorSplitDetailList.length === 1 ? item.debtorSplitDetailList[0].merchantNo : '10000',
        transAmt: item.canPayAmt
      }));

      selectedBillAmountList = Object.values(fundTransAmtList.reduce((res, item) => {
        // eslint-disable-next-line no-unused-expressions
        res[item.fundNo] || (res[item.fundNo] = { ...item, transAmt: 0 });
        res[item.fundNo].transAmt += Number(item.transAmt);
        return res;
      }, {}));

      selectedBillAmountList = selectedBillAmountList.map((e) => ({
        fundNo: e.fundNo,
        transAmt: (`${e.transAmt}`)
      }));
      // console.log(fundTransAmtList, 'fundTransAmtList试算后混合借据', this.selectedBillAmountList);
    } else if (bill0100Flag === 'normalBill') {
      selectedBillAmountList = [];
    }
  } else {
    // 没有试算就取初始化的
    // eslint-disable-next-line no-lonely-if
    // console.log(bill0100Flag, 'bill0100Flag');
    if (bill0100Flag === 'only0100Bill') {
      fundTransAmtList = selectedBillList.map((item) => ({
        fundNo: (item.debtorSplitDetails && item.debtorSplitDetails.length === 1) && item.debtorSplitDetails[0].merchantNo,
        transAmt: item.surplusPayTotalAmt
      }));
      selectedBillAmountList = Object.values(fundTransAmtList.reduce((res, item) => {
        // eslint-disable-next-line no-unused-expressions
        res[item.fundNo] || (res[item.fundNo] = { ...item, transAmt: 0 });
        res[item.fundNo].transAmt += Number(item.transAmt);
        return res;
      }, {}));

      selectedBillAmountList = selectedBillAmountList.map((e) => ({
        fundNo: e.fundNo,
        transAmt: (`${e.transAmt}`)
      }));
    } else if (bill0100Flag === 'minxBill') {
      fundTransAmtList = selectedBillList.map((item) => ({
        fundNo: (item.debtorSplitDetails && item.debtorSplitDetails.length === 1) ? item.debtorSplitDetails[0].merchantNo : item.merchantNo,
        transAmt: item.surplusPayTotalAmt
      }));
      selectedBillAmountList = Object.values(fundTransAmtList.reduce((res, item) => {
        // eslint-disable-next-line no-unused-expressions
        res[item.fundNo] || (res[item.fundNo] = { ...item, transAmt: 0 });
        res[item.fundNo].transAmt += Number(item.transAmt);
        return res;
      }, {}));

      selectedBillAmountList = selectedBillAmountList.map((e) => ({
        fundNo: e.fundNo,
        transAmt: (`${e.transAmt}`)
      }));
      // console.log(fundTransAmtList, 'fundTransAmtList试算前混合借据', this.selectedBillAmountList);
    } else if (bill0100Flag === 'normalBill') {
      selectedBillAmountList = [];
    }
  }
  return {
    selectedBillAmountList
  };
}

// 溢缴款：
// 1、仅0：100借据：
// （1）还款金额<=溢缴款余额，展示溢缴款还款方式，可以使用溢缴款还款
// （2）还款金额>溢缴款余额，不展示溢缴款还款方式，只能使用银行卡还款
// 2、仅非0:100借据：溢缴款金额>0，展示溢缴款还款方式
// 3、0：100+非0：100混合借据：
// （1）还款金额<=溢缴款余额，展示溢缴款还款方式，可以使用溢缴款还款
// （2）还款金额>溢缴款余额时，溢缴款余额仅可用于非0:100借据的还款，
//   根据本次还款中，非0:100的借据的金额具体展示溢缴款能用多少余额
// 拆成仅0:100，按这个判断
// （1）还款金额（优惠后的金额）<=溢缴款余额，展示溢缴款还款方式，可以使用溢缴款还款，可抵扣金额为优惠后的金额
// （2）还款金额（优惠后的金额）>溢缴款余额，不展示溢缴款还款方式，只能使用银行卡还款
// 预还款金额不参与这个逻辑，预还款是针对逾期客户，逾期客户基本不会有溢缴款
/**
 * 判断
 * trilFlag 金额或优惠券修改后是否进行了试算
 * bill0100Flag 是否是0:100 借据
 * afterTrialBillDetail 试算后的借据明细
 * selectedBillList 试算后的借据列表
 */
export function commonGetOverPayAmtRepayFlag(normalBillFlag, overPayAmt, amount, billType, not0100Amt, bill0100Flag, changeTrilBillList, isAmountTrilFlag) {
  let overPayAmtRepayFlag;
  // 仅0:100借据 溢缴款展示条件
  let availableAmount = 0;
  let closeAlipay0100BillFlag = false;
  if (bill0100Flag === 'only0100Bill') {
    overPayAmtRepayFlag = Number(amount) <= Number(overPayAmt);
    availableAmount = Number(amount) > Number(overPayAmt) ? overPayAmt : amount;
    closeAlipay0100BillFlag = true;
  }
  // 混合借据 溢缴款展示条件
  if (bill0100Flag === 'minxBill') {
    closeAlipay0100BillFlag = true; // 支付宝支付条件
    // 本次可用余额 0 不展示
    const flag1 = (Number(amount) > Number(overPayAmt) || Number(overPayAmt) <= Number(not0100Amt)) && Number(overPayAmt) > 0;
    // （1）还款金额（优惠后的金额）<= 溢缴款余额，展示溢缴款还款方式，可以使用溢缴款还款，可抵扣金额为混合借据的还款金额（0：100+非0：100混合借据优惠后的金额）
    // （2）还款金额（优惠后的金额）>溢缴款余额时，溢缴款余额小于等于非0:100借据还款金额（优惠后）时，可抵扣金额为溢缴款余额，溢缴款余额大于非0:100借据还款金额(优惠后)时，溢缴款可抵扣余额显示成非0:100借据优惠后的金额
    const flag2 = normalBillFlag && Number(overPayAmt) > 0;
    // 还款金额 > 溢缴款余额时
    if (Number(amount) > Number(overPayAmt)) {
      if (Number(overPayAmt) <= Number(not0100Amt)) {
        // 溢缴款余额 小于等于 非0:100借据还款金额（优惠后）时，可抵扣金额为 溢缴款余额
        availableAmount = +overPayAmt;
      } else if (Number(overPayAmt) > Number(not0100Amt)) {
        // 溢缴款余额大于非0:100借据还款金额(优惠后)时，溢缴款可抵扣余额显示成非0:100借据优惠后的金额
        availableAmount = +not0100Amt;
      }
    } else {
      availableAmount = +amount;
    }
    // 混合借据是否展示溢缴款
    overPayAmtRepayFlag = flag1 || flag2;

    // 特殊的修改金额试算后混合借据 拆成仅0:100，按这个判断
    // （1）还款金额（优惠后的金额）<=溢缴款余额，展示溢缴款还款方式，可以使用溢缴款还款，可抵扣金额为优惠后的金额
    // （2）还款金额（优惠后的金额）>溢缴款余额，不展示溢缴款还款方式，只能使用银行卡还款
    const afterTrialOnly0100Bill = changeTrilBillList.every((item) => item.debtorSplitDetailList && item.debtorSplitDetailList.length === 1);
    const afterTrialNot0100Bill = changeTrilBillList.every((item) => !item.debtorSplitDetailList);

    // 试算后拆成仅0:100
    if (afterTrialOnly0100Bill && isAmountTrilFlag) {
      if (Number(amount) <= Number(overPayAmt)) {
        overPayAmtRepayFlag = true;
        availableAmount = +amount;
      } else {
        overPayAmtRepayFlag = false;
      }
    }
    // 混合借据 修改金额试算拆成仅 非0:100
    if (afterTrialNot0100Bill && isAmountTrilFlag) {
      overPayAmtRepayFlag = Number(overPayAmt) > 0;
      availableAmount = Number(amount) > Number(overPayAmt) ? overPayAmt : amount;
    }
  }
  // 非0:100借据 溢缴款展示条件
  if (bill0100Flag === 'normalBill') {
    overPayAmtRepayFlag = Number(overPayAmt) > 0;
    availableAmount = Number(amount) > Number(overPayAmt) ? overPayAmt : amount;
  }
  return {
    overPayAmtRepayFlag,
    availableAmount,
    closeAlipay0100BillFlag
  };
}

/**
 * 获取溢缴款的支付接口提交参数
 * overPayAmt 溢缴款
 * remitTotalAmount 在途资金
 * overPayAmtRepayFlag 是否展示小招荷包
 * xiaozhaoAmount 小招荷包使用金额
 */
export function commonGetOverPayAmtRepayParams(overPayAmt, remitTotalAmount, overPayAmtRepayFlag, xiaozhaoAmount) {
  let transOverpayAmt = '0.00';
  let transRemitAmt = '0.00';
  if (Number(overPayAmt) && overPayAmtRepayFlag) {
    if (xiaozhaoAmount > 0) {
      transRemitAmt = Math.min(Number(remitTotalAmount), xiaozhaoAmount).toFixed(2);
      transOverpayAmt = xiaozhaoAmount > transRemitAmt ? (xiaozhaoAmount - Number(remitTotalAmount)).toFixed(2) : 0;
    }
  }
  transRemitAmt = Util.numToStr(transRemitAmt);
  transOverpayAmt = Util.numToStr(transOverpayAmt);
  return { transOverpayAmt, transRemitAmt };
}


/**
 * 组装提交还款接口的公共请求参数
 * bankCard 选择的银行卡
 * noToken
 * needBankCardInfo
 * smsCodeToken
 * bankCardLimitSplitFlag 是否需要大额拆分。选中了优惠券不能拆分
 * selectedCoupon 选择的优惠券
 * transRefNo 流水号
 * repayMode 还款方式
 * bill0100Flag 是否是0:100 借据
 * pwdResultToken
 */
export function commonGetCommonRepayInfo(bankCard, noToken, needBankCardInfo, smsCodeToken, bankCardLimitSplitFlag, selectedCoupon, transRefNo, repayMode, bill0100Flag, pwdResultToken) {
  const repayInfo = {
    transRefNo: transRefNo,
    currency: '156', // 代表人民币
    repayMode: repayMode,
  };
  // 银行卡还款
  let repayWay = 'BANK';
  if (bankCard.isAlipay && isMuapp()) {
    // app的支付宝
    repayWay = 'ALIPAY-SDK';
  } else if (bankCard.isAlipay && !isMuapp() && process.env.TARO_ENV !== 'alipay') {
    // h5 非app的支付宝 ALIPAY-H5  支付宝生活号 ALIPAY-H5_PRO
    repayWay = isAlipay() ? 'ALIPAY-H5_PRO' : 'ALIPAY-H5';
  } else if (bankCard.isWxPay && isWechat() && process.env.TARO_ENV === 'h5') {
    //  微信环境 h5 用各自公众号的appid
    repayWay = 'WEIXIN-JSAPI';
    repayInfo.appId = wechatAppId;
  } else if (bankCard.isWxPay && isMuapp() && process.env.TARO_ENV === 'h5') {
    // app的微信支付 APP用各自app的appid
    repayWay = 'WEIXIN-APP';
    repayInfo.appId = CustomConfig.appWechatAppId;
  } else if (bankCard.isWxPay && process.env.TARO_ENV === 'weapp') {
    // 微信小程序 todo 应该用小程序的appid
    repayWay = 'WEIXIN-JSAPI';
    repayInfo.appId = wechatAppId;
  } else if (bankCard.isAlipay && process.env.TARO_ENV === 'alipay') {
    // 支付宝小程序
    repayWay = 'ALIPAY-PRO';
    repayInfo.appId = alipayAppId;
  } else if (bankCard.isWxPay) {
    repayWay = 'WEIXIN-H5';
    repayInfo.appId = wechatH5AppId;
  }
  repayInfo.repayWay = repayWay;
  if (repayWay === 'BANK') {
    // 银行卡相关
    // 新接口
    const billFlag = bill0100Flag === 'only0100Bill' || bill0100Flag === 'minxBill';
    if (needBankCardInfo) {
      repayInfo.bankCardInfo = {
        bankCardId: bankCard.bankCardId,
        // 多传bankCardNo，就必须把其他银行卡参数都传
        bankCardNo: bankCard.bankCardNo,
        bankCustName: bankCard.bankCustName,
        bankCardType: bankCard.bankCardType,
        bankName: bankCard.bankName,
        bankMobileNo: bankCard.bankMobileNo,
        // 选了优惠券 或者是 0:100借据就是不能拆分了
        bankCardLimitSplit: (selectedCoupon.awardNo || billFlag) ? 'N' : bankCardLimitSplitFlag
      };
    } else {
      // 旧接口
      repayInfo.bankCardId = bankCard.bankCardId;
      // 不可补签而且不满足限额的时候，拆分银行卡还款。选中了优惠券不能拆分
      repayInfo.bankCardLimitSplit = (selectedCoupon.awardNo || billFlag) ? 'N' : bankCardLimitSplitFlag;
    }
  }
  /**
     * noToken一般是false,就走正常流程
     * 需要补签的操作之后，noToken为true，不传token了
     */
  if (!noToken && repayWay === 'BANK') { // 补签之后，不传任何已验证的token
    if (CustomConfig.payBySms) {
      repayInfo.smsCodeToken = smsCodeToken;
    } else {
      repayInfo.passwordToken = pwdResultToken;
    }
  }
  return repayInfo;
}

/**
 * 获取用户实际支付金额
 * actualAmt 总支付金额-优惠券金额
 * overPayAmt 溢缴款金额
 * settleWaiveAmtAll 超限减免金额
 * preRepayAmt 预还款金额
 * not0100Amt 非0:100借据还款金额
 * immutableAmount url上的传参金额
 * topShowAmt 展示金额
 * availableAmount 溢缴款+在途资金使用金额
 * isCutSettleWaiveAmt 是否有超限减免
 * bill0100Flag 是否是0:100 借据
 * overPayAmtRepayFlag 是否展示小招荷包
 */
export function commonGetActualRepayAmt(
  actualAmt, overPayAmt, settleWaiveAmtAll, preRepayAmt,
  not0100Amt, immutableAmount, topShowAmt, availableAmount,
  isCutSettleWaiveAmt, bill0100Flag, overPayAmtRepayFlag,
) {
  // 预还款金额优先级最高，在这里直接减去
  let needPayAcount = Util.floatMinus(actualAmt, preRepayAmt) || Util.floatMinus(immutableAmount, preRepayAmt);
  // 对预还款金额刚好等于还款金额的情况做特殊处理
  if (Number(actualAmt) === Number(preRepayAmt)) {
    needPayAcount = Util.floatMinus(actualAmt, preRepayAmt);
  }
  // 待还总金额要大于溢缴款，然后减去超限后的金额要小于溢缴款 这种情况需要额外处理
  const overAmtFlag = Number(needPayAcount) > Number(overPayAmt) && (topShowAmt < Number(overPayAmt));
  const overNum = overAmtFlag ? availableAmount : overPayAmt;
  if (isCutSettleWaiveAmt && Number(settleWaiveAmtAll)) {
    needPayAcount = Util.floatMinus(needPayAcount, settleWaiveAmtAll);
  }

  // 混合借据 可抵扣显示 非0:100 时候
  if (bill0100Flag === 'minxBill') {
    // 还款金额 > 溢缴款余额时
    if (Number(actualAmt) > Number(overPayAmt)) {
      if (Number(overPayAmt) <= Number(not0100Amt)) {
        // 溢缴款余额 小于等于 非0:100借据还款金额（优惠后）时，可抵扣金额为溢缴款余额
        if (Number(overNum) > 0) {
          return Number(needPayAcount) > Number(overNum) ? parseFloat(needPayAcount - overNum).toFixed(2) : 0;
        }
      } else if (Number(overPayAmt) > Number(not0100Amt)) {
        // 溢缴款余额大于非0:100借据还款金额(优惠后)时，溢缴款可抵扣余额显示成非0:100借据优惠后的金额
        if (Number(overNum) > 0) {
          return Number(needPayAcount) > Number(overNum) ? parseFloat(needPayAcount - not0100Amt).toFixed(2) : 0;
        }
      }
    } else if (Number(actualAmt) <= Number(overPayAmt)) {
      if (Number(overNum) > 0) {
        return Number(needPayAcount) > Number(overNum) ? parseFloat(needPayAcount - overNum).toFixed(2) : 0;
      }
    }
  } else if (bill0100Flag === 'only0100Bill') {
    if (Number(overNum) > 0 && (overPayAmtRepayFlag === false)) {
      return Number(needPayAcount) > Number(overNum) ? parseFloat(needPayAcount).toFixed(2) : 0;
    } else {
      return Number(needPayAcount) > Number(overNum) ? parseFloat(needPayAcount - overNum).toFixed(2) : 0;
    }
  } else if (bill0100Flag === 'normalBill') {
    if (Number(overNum) > 0) {
      return Number(needPayAcount) > Number(overNum) ? parseFloat(needPayAcount - overNum).toFixed(2) : 0;
    }
  }
  if (Number(needPayAcount) <= 0) {
    needPayAcount = 0;
  }

  return parseFloat(needPayAcount).toFixed(2);
}

/**
   * @description: 根据展位数据和cc配置决定是否只能使用支付宝
   */
export function commonGetSetZFBOnly(repayConfig, bankCardErrorStatusList, selectedBillCapitalsList, ZFBSwitchData) {
  // 0ZFBMNPJD 暂时要关闭支付宝，打开银行卡, 后续会下线这个设定
  const closeAlipayCC = repayConfig.closeAlipayChannel && repayConfig.closeAlipayChannel.indexOf(Madp.getChannel()) > -1;
  // 是否需要设置仅支持支付宝。支付宝h5和支付宝小程序都应支持。
  const needSwitch = process.env.TARO_ENV === 'alipay' || (isAlipay() && CustomConfig.onlyAlipay && !closeAlipayCC);
  let alipayOnly = false;
  let repayWayNoticeConfig = {};
  // 如果借据中有0:100的借据，那么不用判断是否只能用支付宝
  if (selectedBillCapitalsList.length) {
    alipayOnly = false;
  } else if (needSwitch) {
    // 根据
    alipayOnly = ZFBSwitchData;
    const filterData = (repayConfig.onlyZFBTips && repayConfig.onlyZFBTips[Madp.getChannel()]) || {};
    if (filterData && Object.keys(filterData).length > 0) {
      repayWayNoticeConfig = {
        content: filterData.repaywayTips || '支付方式有疑问',
        title: filterData.repayTipsAlert01 || '支付方式说明',
        btnContent: filterData.repayTipsAlert02 || '好的',
      };
      if (filterData.items && filterData.items.length > 0) {
        const dialog = filterData.items.map((item) => ({
          imgUrl: item.imgUrl,
          defaultUrl: item.repayTipsAlert03,
          newUrl: item.repayTipsAlert04,
        }));
        repayWayNoticeConfig = {
          ...repayWayNoticeConfig,
          dialog
        };
      }
    }
  }
  return {
    alipayOnly, repayWayNoticeConfig
  };
}

/**
 * 支付方式初始化，主要用来自动选择一种支付方式
 * cardsList 银行卡列表
 * bankCardErrorStatusList 错误状态的银行卡列表
 * isRepayCheckout 是否还款收银台
 * isAlipayOnly 是否只支持支付宝
 * bill0100Flag 是否0:100借据
 * isFirstEnter 是否首次加载
 * aliRepayAvailable 支付宝是否可用
 * wxRepayAvailable 微信是否可用
 */
export function repayCheckOutInitDefaultCard(cardsList, bankCardErrorStatusList, isRepayCheckout, isAlipayOnly, bill0100Flag, isFirstEnter = false, aliRepayAvailable, wxRepayAvailable, modeOfPayment, firstRepayWay) {
  // 根据渠道技参确定优先展示的支付方式
  let firstPayWay = '';
  // 新收银台才根据渠道参数返回走下面逻辑
  if (isRepayCheckout) {
    // 设置默认选择的支付方式，cc配置的优先展示支付方式当且仅当包含在当前渠道支付的还款支付方式内才生效（管理端已做限制，这里在兜底判断一下）
    if (modeOfPayment && modeOfPayment.length > 0 && modeOfPayment.indexOf(firstRepayWay) !== -1) {
      switch (firstRepayWay) {
        case '1':
          firstPayWay = 'bankCard';
          break;
        case '2':
          firstPayWay = 'aliPay';
          break;
        case '3':
          firstPayWay = 'wxpay';
          break;
        case '4':
          firstPayWay = 'transfer';
          break;
        default:
          break;
      }
    } else if (aliRepayAvailable) {
      firstPayWay = 'aliPay';
    } else if (wxRepayAvailable) {
      firstPayWay = 'wxpay';
    } else if (modeOfPayment && modeOfPayment.length > 0 && modeOfPayment.indexOf('1') === -1 && modeOfPayment.indexOf('4') !== -1) {
      // 当前渠道即不支持微信、支付宝，也不支持银行卡，且只支持银行卡转账，此时就选中银行卡转账
      return {
        needSelectCardType: 'transfer',
        isManual: true,
      };
    }
  }

  // 没银行卡的情况
  if (!cardsList || cardsList.length === 0) {
    if (aliRepayAvailable && firstPayWay === 'aliPay') {
      return {
        needSelectCardType: 'aliPay',
        isManual: true,
      };
    } else if (wxRepayAvailable && firstPayWay === 'wxpay') {
      return {
        needSelectCardType: 'wxpay',
        isManual: true,
      };
    } else if (firstPayWay === 'transfer') {
      return {
        needSelectCardType: 'transfer',
        isManual: true,
      };
    }
    if (aliRepayAvailable) {
      return {
        needSelectCardType: 'aliPay',
        isManual: true,
      };
    } else if (wxRepayAvailable) {
      return {
        needSelectCardType: 'wxpay',
        isManual: true,
      };
    } else {
      return null;
    }
  }
  // 绑卡回来默认选中刚绑的卡
  const cardInfo = Madp.getStorageSync('BANK_CARD_INFO_DATA', 'SESSION') || {};
  // console.log('cardInfo', cardInfo);
  // console.log('BANK_CARD_INFO_DATA', Madp.getStorageSync('BANK_CARD_INFO_DATA', 'SESSION'));
  if (Object.keys(cardInfo).length && cardInfo.bankCard) {
    const cardData = cardsList.filter((card) => card.bankCardId === cardInfo.bankCard.cardId);
    // 新绑的卡在查出来的卡列表里面，并且这张卡可用
    if (cardData && cardData.length > 0 && (!bankCardErrorStatusList || Object.keys(bankCardErrorStatusList).length === 0 || !bankCardErrorStatusList[cardInfo.bankCard.cardId])) {
      return {
        needSelectCardType: 'newBindCard',
        needSelectCard: cardData[0],
      };
    }
    // 还款收银台的特殊处理，防止出现初始化的时候一直选择新绑定的卡
    if (isRepayCheckout) {
      Madp.removeStorageSync('BANK_CARD_INFO_DATA', 'SESSION');
    }
  }
  if (isAlipayOnly) {
    // 只支持支付宝只能选支付宝，系统的初始化不需要弹窗，给false
    return {
      needSelectCardType: 'aliPay',
      isManual: false,
    };
  }

  // 上一次选择的银行卡,可通过isFirstEnter=true设置首次加载忽略tempCard
  const tempCard = isFirstEnter ? {} : (getStore('selectedCard') || {});
  // 筛选自动选的目标银行卡 有银行卡时
  if (cardsList && cardsList.length) {
    if (aliRepayAvailable && firstPayWay === 'aliPay') {
      return {
        needSelectCardType: 'aliPay',
        isManual: true,
      };
    } else if (wxRepayAvailable && firstPayWay === 'wxpay') {
      return {
        needSelectCardType: 'wxpay',
        isManual: true,
      };
    } else if (firstPayWay === 'transfer') {
      return {
        needSelectCardType: 'transfer',
        isManual: true,
      };
    }
    const availableCards = cardsList.filter((card) => {
      const { bankCardId, } = card;
      // 可以选的卡，可以自动选择。
      if ((!bankCardErrorStatusList || Object.keys(bankCardErrorStatusList).length === 0 || !bankCardErrorStatusList[bankCardId])) {
        return card;
      }
    });

    // 所有的卡都支付失败了，自动选一个支付宝/微信
    if (availableCards.length === 0) {
      if (aliRepayAvailable) {
        return {
          needSelectCardType: 'aliPay',
          isManual: true,
        };
      } else if (wxRepayAvailable) {
        return {
          needSelectCardType: 'wxpay',
          isManual: true,
        };
      } else {
        return null;
      }
    }

    // 选择不置灰的默认银行卡或卡列表第一张卡
    const isDefaultCard = availableCards.find((item) => item.isDefault === 'Y'); // 是否是默认卡
    const reNewSelectedCard = availableCards.find((item) => item.bankCardId === (tempCard && tempCard.bankCardId)); // 已选中的银行卡数据可能更新，使用接口返回的最新数据

    // 用户选过支付方式，但是支付宝微信
    if (tempCard.bankCardId && (tempCard.bankCardId === 'alipay' || tempCard.bankCardId === 'wxpay')) {
      if (tempCard.bankCardId === 'alipay') {
        return {
          needSelectCardType: 'aliPay',
          isManual: true,
        };
      } else if (tempCard.bankCardId === 'wxpay') {
        return {
          needSelectCardType: 'wxpay',
          isManual: true,
        };
      }
    } else if (tempCard.bankCardId && reNewSelectedCard) { // 用户选过卡，并且卡在可用列表里面
      return {
        needSelectCardType: 'bankCard',
        needSelectCard: reNewSelectedCard,
        isManual: false,
      };
    } else {
      // 用户选过卡，但卡不在可用列表中，两种可能，一种是新页面上个用户数据残留，一种是之前的卡支付失败了，都为用户选张能用的卡
      return {
        needSelectCardType: 'bankCard',
        needSelectCard: isDefaultCard || availableCards[0],
        isManual: false,
      };
    }
  }
}

/**
 * 银行卡补签判断
 * card 银行卡
 */
export function isNeedSignSupplement(card, selectedBillCapitals) {
  const {
    needSignFundList = [], supplementSignFlag, istTransferGuide = false, satisfyLimitAmtStatus, forceSignFlag
  } = card;
  // 补签列表中去除10000，要补签招联的话，根据supplementSignFlag字段判断
  const otherNeedSignFundList = needSignFundList ? needSignFundList.filter((item) => item !== '10000') : [];
  let showFPAYContract = false;
  let capitalConstractNum = '';
  // 正常补签招联，宝付强制补签走正常补签流程
  if (supplementSignFlag === 'Y' || (needSignFundList && needSignFundList.indexOf('10000') > -1) || (forceSignFlag === 'Y')) {
    // dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPrefix}.ShowContractChecker` });
    showFPAYContract = true;
    capitalConstractNum = '';
  } else if (otherNeedSignFundList.length) { // 补签资方
    // 将需要补签的资方商户，按照对应金额的降序排序
    const sortList = otherNeedSignFundList.sort((min, max) => selectedBillCapitals[max] - selectedBillCapitals[min]);
    showFPAYContract = true;
    capitalConstractNum = sortList.length ? sortList[0] : '';
  }
  return {
    istTransferGuide,
    showFPAYContract,
    capitalConstractNum,
    bankCardLimitSplitFlag: satisfyLimitAmtStatus === 'N'
      && supplementSignFlag === 'N' ? 'Y' : 'N'
  };
}

/**
 * 立即还款错误码处理
 * errMsg 错误信息
 * errCode 错误码
 * refreshOverPayAoumnt 调接口刷新溢缴款的函数
 * trackPrefix 埋点前缀
 * showContractMUModal 补签弹窗唤醒
 * setIsCantAffordDialogOpened
 * themeColor
 * updateIgnoreLeave
 * handleRepayErrorDailog 处理错误码弹窗展示内容
 * closeRepayErrorDailog 关闭弹窗
 * bankCardErrorToastFn 银行卡错误弹窗展示处理
 */
export async function handleErrorCode(errMsg,
  errCode,
  refreshOverPayAoumnt,
  trackPrefix,
  showContractMUModal,
  setIsCantAffordDialogOpened = () => { },
  themeColor,
  updateIgnoreLeave,
  handleRepayErrorDailog,
  closeRepayErrorDailog,
  bankCardErrorToastFn) {
  let errorMsg = errMsg || errCodeToMsgMap[errCode];
  let showErrMsg = true;
  let setSelectCardsStatusMsg = '';
  let showCeilingIcon = false;
  let execRefreshOverPayAoumnt = true;
  const code = transformErrCode(errCode);
  switch (code) {
    case 'UMDP01152':// 超过限额
      setSelectCardsStatusMsg = '不可用，已超交易限额';
      showCeilingIcon = true;
      execRefreshOverPayAoumnt = false;
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPrefix}.BankCardOverLimit` });
      break;
    case 'UMDP01155':// 余额不足
      setSelectCardsStatusMsg = '余额不足';
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPrefix}.BalanceNotEnough` });
      break;
    case 'UMDP01156':// 协议不存在、协议异常 激活补签？？？
      showErrMsg = false;
      // todo: 需要唤醒补签弹窗
      showContractMUModal();
      dispatchTrackEvent({ event: EventTypes.SO, beaconId: `${trackPrefix}.ShowFPAYContractModal` });
      break;
    // case 'UMDP01151':// 账户异常、密码异常
    //   setSelectCardsStatusMsg = '卡异常';
    //   dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPrefix}.BankCardError` });
    //   break;
    case 'UMDP01708':// 银行卡已销户
      setSelectCardsStatusMsg = '您的银行卡已销户，请选择可用的银行卡或添加新卡';
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPrefix}.BankCardError` });
      break;
    // case 'UMDP01728':// 还款金额校验失败
    //   errorMsg = '还款信息已过期，请关闭当前页面后重新提交还款';
    //   dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPrefix}.repayInfoOutDated` });
    //   break;
    case 'bankCardError': // 银行卡异常
    case 'cardholderInformationError': // 持卡人信息异常
      setSelectCardsStatusMsg = '不可用，卡账户异常';
      execRefreshOverPayAoumnt = false;
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPrefix}.BankCardError` });
      break;
    default:
      break;
  }
  if (showErrMsg) {
    setTimeout(() => {
      if (code === 'UMDP01155') { // 余额不足单独弹框提示
        setIsCantAffordDialogOpened(true);
      } else if (code === 'UMDP02510') {
        Madp.showModal({
          title: '支付提示',
          content: errorMsg || '您有一笔自动还款正在处理中，为避免重复还款，请耐心等待还款结果通知',
          confirmText: '返回首页',
          confirmColor: themeColor,
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              if (typeof updateIgnoreLeave === 'function') updateIgnoreLeave();
              const url = '%2Fpages%2Findex%2Findex';
              Madp.redirectTo({
                url: decodeURIComponent(url),
              });
            }
          }
        });
        return;
      } else if (code === 'bankCardError' || code === 'cardholderInformationError') {
        // 银行卡异常、持卡人信息异常
        bankCardErrorToastFn(errorMsg, setSelectCardsStatusMsg, showCeilingIcon);
        return;
      } else if (code === 'UMDP01728') { // 提交还款的金额与客户实际欠款不一致
        handleRepayErrorDailog(
          {
            title: '温馨提示',
            contentText: errorMsg,
            confirmText: '返回首页',
            confirmFn: () => {
              if (typeof updateIgnoreLeave === 'function') updateIgnoreLeave();
              const url = '%2Fpages%2Findex%2Findex';
              Madp.redirectTo({
                url: decodeURIComponent(url),
              });
            },
          }
        );
        return;
      } else if (code === 'UMDP01152') {
        // 交易限额
        handleRepayErrorDailog(
          {
            title: '温馨提示',
            contentText: errorMsg,
            confirmText: '我知道了',
            confirmFn: () => closeRepayErrorDailog(setSelectCardsStatusMsg, true, showCeilingIcon)
          }
        );
        return;
      } else if (code === 'UMDP03683') { // 身份证过期
        Madp.showModal({
          title: '温馨提示',
          content: errorMsg || '很抱歉，您在招联登记的身份证已失效，请更新后再重新办理，如有疑问可咨询客服',
          confirmText: '返回首页',
          confirmColor: themeColor,
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              if (typeof updateIgnoreLeave === 'function') updateIgnoreLeave();
              const url = '%2Fpages%2Findex%2Findex';
              Madp.redirectTo({
                url: decodeURIComponent(url),
              });
            }
          }
        });
      } else {
        // 无特殊要求场景，弹窗关闭即可；无其他交互
        if (errorMsg && (errorMsg.length> 10)) {
          handleRepayErrorDailog(
            {
              title: '温馨提示',
              contentText: errorMsg,
              confirmText: '我知道了',
              confirmFn: closeRepayErrorDailog
            }
          );
        } else {
          Util.showErrorMsg(errorMsg || '系统繁忙，请稍后再试');
        }
        return;
      }
      dispatchTrackEvent({ event: EventTypes.EV, beaconId: `${trackPrefix}.SystemError` });
    }, 50);
  }
  // 还款接口报错，自动刷新溢缴款接口，延迟2000ms让上面这个showToast能够显示出来（还款补签时不调该方法，否则会换卡导致补签报错）
  if (typeof refreshOverPayAoumnt === 'function' && code !== 'UMDP01156' && execRefreshOverPayAoumnt) {
    refreshOverPayAoumnt(setSelectCardsStatusMsg);
  }
}

/**
 * 账单涉及资方信息(0:100类型的资方)对象。{ "10000": 13123 }
 * selectedBills 借据信息
 */
export function getSelectedBillCapitalsAndList(selectedBills) {
  let selectedBillCapitals = {};
  const mixedBill = selectedBills.find((item) => item.debtorSplitDetails && item.debtorSplitDetails.length === 1);
  selectedBills.forEach((bill) => {
    // 债权信息只有一条的是0：100借据。
    // 只有一条是0:100联合贷
    if (bill.debtorSplitDetails && bill.debtorSplitDetails.length === 1) {
      const capital = bill.debtorSplitDetails[0];
      // 如果已存在该资方，就增加对应金额
      if (selectedBillCapitals[capital.merchantNo]) {
        selectedBillCapitals[capital.merchantNo] += +capital.splitAmt;
      } else {
        selectedBillCapitals[capital.merchantNo] = +capital.splitAmt;
      }
    } else if (mixedBill && bill.debtorSplitDetails && bill.debtorSplitDetails.length === 2) { // 有两条是正常联合贷。算招联的
      const capital1 = bill.debtorSplitDetails[0];
      const capital2 = bill.debtorSplitDetails[1];
      if (selectedBillCapitals['10000']) {
        selectedBillCapitals['10000'] += (+capital1.splitAmt) + (+capital2.splitAmt);
      } else {
        selectedBillCapitals['10000'] = (+capital1.splitAmt) + (+capital2.splitAmt);
      }
    }
    if (mixedBill && !bill.debtorSplitDetails) { // 混合借据状态
      if (selectedBillCapitals['10000']) {
        selectedBillCapitals['10000'] += Number(bill.surplusPayTotalAmt);
      } else {
        selectedBillCapitals['10000'] = Number(bill.surplusPayTotalAmt);
      }
    }
  });
  if (!selectedBillCapitals || !Object.keys(selectedBillCapitals).length) {
    return {};
  }
  return selectedBillCapitals;
}

export function isMiniProgram() {
  return (process.env.TARO_ENV !== 'h5');
}

/**
 * 支付宝是否可用,用于支付方式选择组件
 */
export function isAliRepayAvailable(
  isAlipayOnly, closeWxAlipayFromCC, closeAlipay0100BillFlag,
  isOverDueUserFlag, isDueTagCust, isShowDescTitle,
  isRepayCheckout, isHideWxAliRepay, modeOfPayment
) {
  // 新收银台才先走渠道参数的判断，旧的继续走旧逻辑
  if (isRepayCheckout) {
    if (modeOfPayment && modeOfPayment.indexOf('2') !== -1) {
      // 渠道开关打开，在该渠道下对所有客群都开放，但支付宝渠道不展示微信支付
      if (isWechat() || process.env.TARO_ENV === 'weapp') {
        return false;
      }
      return true;
    } else if (!isDueTagCust) {
      // 渠道开关关闭，在该渠道下对所有客群都关闭（除了逾期客户）
      return false;
    }
  }

  if (!isShowDescTitle && !isRepayCheckout) {
    // 预还款要展示支付宝，参数isShowDescTitle暂时仅有预支付使用
    return true;
  }
  if (appInfos.repayment && appInfos.repayment.isHideWxZfbRepayWay) {
    // 1、app参数控制关闭
    return false;
  } else if (isAlipayOnly) {
    // 2、ABtest来判断仅支持支付宝还款
    return true;
  } else if (CustomConfig.alipayOnOverdue && ((isRepayCheckout && isDueTagCust) || (!isRepayCheckout && (isOverDueUserFlag || isDueTagCust)))) {
    // 逾期时显示支付宝
    return true;
  } else if (!CustomConfig.alipay || isHideWxAliRepay || closeWxAlipayFromCC || closeAlipay0100BillFlag) {
    // 3、默认配置不支持支付宝支付的关闭，有联合带账单的关闭，cc配置的特殊渠道关闭, app参数控制关闭, 有0：100借据的关闭
    return false;
  } else {
    return true;
  }
}

/**
 * 微信是否可用,用于支付方式选择组件
 */
export function isWxRepayAvailable(
  closeWxAlipayFromCC, isOverDueUserFlag, isDueTagCust,
  isShowDescTitle, isRepayCheckout, isHideWxAliRepay, modeOfPayment
) {
  // 手Q不展示微信支付
  const qqEnv = (typeof isQQEnv === 'function') ? isQQEnv() : false;
  if (qqEnv) {
    return false;
  }
  // 新收银台才先走渠道参数的判断，旧的继续走旧逻辑
  if (isRepayCheckout) {
    if (modeOfPayment && modeOfPayment.indexOf('3') !== -1) {
      // 渠道开关打开，在该渠道下对所有客群都开放，但微信渠道不展示支付宝支付
      if (isAlipay() || process.env.TARO_ENV === 'alipay') {
        return false;
      }
      return true;
    } else if (!isDueTagCust) {
      // 渠道开关关闭，在该渠道下对所有客群都关闭（除了逾期客户）
      return false;
    }
  }

  if (!isRepayCheckout && !isShowDescTitle) {
    // 预还款要展示微信，参数isShowDescTitle暂时仅有预支付使用
    return true;
  }

  if (appInfos.repayment && appInfos.repayment.isHideWxZfbRepayWay) {
    // 1、app参数控制关闭
    return false;
  } else if (isHideWxAliRepay || closeWxAlipayFromCC || getWebViewName() === 'wx' || appInfos.repayment.isHideWxZfbRepayWay) {
    // 3、联合带账单关闭、cc配置的特殊渠道关闭、微信小程序里面的h5要关闭, app参数控制关闭， 有0：100借据的关闭
    return false;
  } else if (CustomConfig.wxPay && ((isRepayCheckout && isDueTagCust) || (!isRepayCheckout && (isOverDueUserFlag || isDueTagCust)))) {
    dispatchTrackEvent({
      target: this,
      event: EventTypes.SO,
      beaconId: 'wxPayOverDueCust',
      beaconContent: { cus: { wxPay: CustomConfig.wxPay, isOverDueUserFlag, isDueTagCust } }
    });
    // 4、 微信环境或者app环境，而且是逾期用户,开启
    return true;
  } else {
    return false;
  }
}

/**
 * 是否隐藏支付宝微信支付方式,用于支付方式选择组件
 */
export function getIsHideWxAliRepay() {
  const uniconHideWxAli = getStore('uniconHideWxAli');
  if (uniconHideWxAli) {
    // sydicatedLoanTag，01联合贷，00普通
    const unionList = getStore('selectedBillList').filter((list) => list.sydicatedLoanTag === '01');
    if (unionList.length) {
      return true;
    }
  }
  return false;
}

/**
 * 过滤权益返回的券列表
   * @param { Array } couponList 权益接口返回的券列表
   * @param { Array<Object> } filters 券列表项的过滤条件
   * @return { Array } 过滤后的券列表
 */
export function couponFilter(couponList, filters) {
  // 拉平券列表
  couponList = couponList && couponList.couponQryGroupList && couponList.couponQryGroupList.length && couponList.couponQryGroupList.map((e) => e.couponQryInfoList).flat(1);
  // 根据入参条件的awardType和specialScene列表过滤
  couponList = couponList && couponList.length && couponList.filter((item) => {
    let isFit = false; // 满足filters条件（满足其中一项即可）
    (filters || []).forEach((filter) => {
      // awardType条件为空或者awardType与规则一致
      if ((!filter || !filter.awardType || filter.awardType === item.awardType)
        // specialScene条件为空或者specialScene与规则一致
        && (!filter || !filter.specialScene || filter.specialScene === item.specialScene)) {
        isFit = true;
      }
    });
    // 过滤可用券
    return isFit && item.usable === 'Y';
  });
  // 券去重
  couponList = couponList && couponList.length && couponList.filter((item, index) => couponList.findIndex((e) => (e.awardType === item.awardType && e.specialScene === item.specialScene)) === index);
  return couponList || [];
}

// 筛选资格券
// awardType  306   免提前还违约金资格券
// awardType  216   specialScene SSA08 博弈催收 博弈梯度息费减免券(提前还款场景)
// awardType  216   specialScene SSA02 博弈催收 还款减免券
// awardType  117   specialScene SSA02 博弈催收 延期还款券  （延长还款日/下月还）
// awardType  117   specialScene SSA01 博弈催收 人工作业券  （延期还款/下月还）
// awardType  217   博弈催收 省心分资格券 【展期】 【展期还款】 【展期试算】
// awardType  305   再分期未来减免券
// awardType  105   specialScene SSA04 博弈催收 延长宽限期资格券
// awardType  303   specialScene SSA06 延长宽限期（客服机器人） 延长宽限期资格券
export function filterQualCoupons(originCouponList, repayDetailList = [], custOverDueStatus = false) {
  originCouponList = (originCouponList || []).map((item = {}) => {
    item.awardNo = `${item.awardNo}` || '';
    return item;
  });
  let usableQualConponList = [];
  const usableQualAwardTypeRules = [
    { awardType: '306', usable: 'Y' },
    { awardType: '216', subUseSceneCode: 'SSA02', usable: 'Y' },
    { awardType: '216', subUseSceneCode: 'SSA08', usable: 'Y' },
    { awardType: '117', subUseSceneCode: 'SSA02', usable: 'Y' },
    { awardType: '117', subUseSceneCode: 'SSA01', usable: 'Y' },
    { awardType: '217', usable: 'Y' },
    { awardType: '305', usable: 'Y' },
    { awardType: '105', subUseSceneCode: 'SSA04', usable: 'Y' },
    { awardType: '303', subUseSceneCode: 'SSA06', usable: 'Y' },
  ];
  (originCouponList || []).forEach((couponItem) => {
    let usableSinglePass = false;
    usableQualAwardTypeRules.forEach((ruleItem) => {
      const keys = Object.keys(ruleItem);
      let itemAllPass = true;
      keys.forEach((key) => {
        if ((couponItem || {})[key] !== ruleItem[key]) {
          itemAllPass = false;
        }
      });
      if (itemAllPass) usableSinglePass = true;
    });
    if (usableSinglePass
        && usableQualConponList.filter((qualCoupon) => (JSON.stringify(qualCoupon) === JSON.stringify(couponItem))).length <= 0
    ) usableQualConponList.push(couponItem);
  });
  usableQualConponList.forEach((usableQualConpon = {}) => {
    if (Object.keys(usableQualConpon.awardUseCondition || {}).length) {
      const {
        transTerminal, transTerminalCondition, custLoanStatus, custLoanStatusCondition
      } = usableQualConpon.awardUseCondition;
      if (transTerminal) {
        if (transTerminalCondition === 'in' && transTerminal.indexOf(Madp.getChannel()) < 0) {
          usableQualConponList = usableQualConponList.filter((item = {}) => (JSON.stringify(item) !== JSON.stringify(usableQualConpon)));
          return;
        }
        if (transTerminalCondition === 'notin' && transTerminal.indexOf(Madp.getChannel()) > -1) {
          usableQualConponList = usableQualConponList.filter((item = {}) => (JSON.stringify(item) !== JSON.stringify(usableQualConpon)));
          return;
        }
      }
      if (custLoanStatus) {
        if (custLoanStatus.indexOf('0') > -1 && custLoanStatusCondition === 'in' && custOverDueStatus) {
          usableQualConponList = usableQualConponList.filter((item = {}) => (JSON.stringify(item) !== JSON.stringify(usableQualConpon)));
          return;
        }
      }
    }
    if (usableQualConpon.awardType === '216') {
      const formatRepayDetailList = parseRepayOrderList(repayDetailList);
      let availOrderNoList = formatRepayDetailList.map((item = {}) => item.orderNo) || [];
      if (formatRepayDetailList.length > 0 && (formatRepayDetailList.filter((item = {}) => item.supportSplitFlag === 'N').length === formatRepayDetailList.length)) {
        usableQualConponList = usableQualConponList.filter((item = {}) => (JSON.stringify(item) !== JSON.stringify(usableQualConpon)));
        return;
      }
      const availOrderNoListCur = formatRepayDetailList.filter((item = {}) => item.supportSplitFlag !== 'N').map((item = {}) => item.orderNo);
      availOrderNoList = availOrderNoList.filter((item) => availOrderNoListCur.indexOf(item) >= 0);
      usableQualConpon.orderNoList = Array.from(new Set(availOrderNoList || []));
    }
  });
  return { usableQualConponList };
}

// 新券增加旧券信息，用于优惠券组件展示
export function transformNewCouponAPIRes(coupon = {}) {
  if (!coupon || !coupon.awardNo) return;
  // 膨胀券属性修正
  if (coupon.awardInflationStatus === 'Y') {
    const {
      inflationAvailChannelCode, inflationBeforeAwardValue, inflationBeforeWaiveDurationCount, inflationBeforeDeductionUpLmt, inflationBeforeLongDesc, inflationBeforeShortDesc,
      inflationAfterAwardValue, inflationAfterWaiveDurationCount, inflationAfterDeductionUpLmt, inflationAfterLongDesc, inflationAfterShortDesc
    } = coupon.inflationProperty || {};
    if ((inflationAvailChannelCode || '').indexOf(Madp.getChannel()) > -1) {
      coupon.awardAmt = coupon.awardAmt ? (inflationAfterAwardValue || coupon.awardAmt) : null;
      coupon.discountDurationCount = coupon.discountDurationCount ? (inflationAfterWaiveDurationCount || coupon.discountDurationCount) : null;
      coupon.discountUpLimit = coupon.discountUpLimit ? (inflationAfterDeductionUpLmt || coupon.discountUpLimit) : null;
      coupon.longDesc = coupon.longDesc ? (inflationAfterLongDesc || coupon.longDesc) : null;
      coupon.shortDesc = coupon.shortDesc ? (inflationAfterShortDesc || coupon.shortDesc) : null;
    } else {
      if (coupon.inflationCoverFlag === 'Y') {
        coupon.awardAmt = coupon.awardAmt ? (inflationAfterAwardValue || coupon.awardAmt) : null;
        coupon.discountDurationCount = coupon.discountDurationCount ? (inflationAfterWaiveDurationCount || coupon.discountDurationCount) : null;
        coupon.discountUpLimit = coupon.discountUpLimit ? (inflationAfterDeductionUpLmt || coupon.discountUpLimit) : null;
        coupon.longDesc = coupon.longDesc ? (inflationAfterLongDesc || coupon.longDesc) : null;
        coupon.shortDesc = coupon.shortDesc ? (inflationAfterShortDesc || coupon.shortDesc) : null;
      } else {
        coupon.awardAmt = coupon.awardAmt ? (inflationBeforeAwardValue || coupon.awardAmt) : null;
        coupon.discountDurationCount = coupon.discountDurationCount ? (inflationBeforeWaiveDurationCount || coupon.discountDurationCount) : null;
        coupon.discountUpLimit = coupon.discountUpLimit ? (inflationBeforeDeductionUpLmt || coupon.discountUpLimit) : null;
        coupon.longDesc = coupon.longDesc ? (inflationBeforeLongDesc || coupon.longDesc) : null;
        coupon.shortDesc = coupon.shortDesc ? (inflationBeforeShortDesc || coupon.shortDesc) : null;
      }
    }
  }
  const transformedCoupon = { ...coupon };
  const {
    awardNo, awardOutDisplayName, longDesc, shortDesc, awardMark, subUseSceneCode, awardType, awardAmtType, discountUpLimitType, discountUpLimit,
    receiveDate,
  } = coupon;
  transformedCoupon.awardNo = `${awardNo || ''}`;
  transformedCoupon.awardName = awardOutDisplayName;
  transformedCoupon.compoundLongDesc = longDesc;
  transformedCoupon.compoundShortDesc = shortDesc;
  transformedCoupon.couponSpecialFlag = awardMark;
  transformedCoupon.specialScene = subUseSceneCode;
  // transformedCoupon.discountPeriodCount = discountDurationCount;
  // transformedCoupon.reduceCnt = discountDurationCount;
  if (awardType === '216') {
    if (awardAmtType === '1') {
      transformedCoupon.waiveWayCode = '01';
    } else if (awardAmtType === '4') {
      transformedCoupon.waiveWayCode = '02';
    } else if (awardAmtType === '5') {
      transformedCoupon.waiveWayCode = '03';
    }
  }
  if (discountUpLimitType === '3') {
    transformedCoupon.maxDeductionUnitCode = '1';
    transformedCoupon.maxReductAmt = discountUpLimit === '-1.00' ? '1.00' : discountUpLimit;
  } else if (discountUpLimitType === '5') {
    transformedCoupon.maxDeductionUnitCode = '2';
    transformedCoupon.maxReductAmt = discountUpLimit;
  } else if (discountUpLimitType === '2') {
    transformedCoupon.discountModelFlag = '2';
    transformedCoupon.perDeductionUplmt = discountUpLimit;
  } else if (discountUpLimitType === '1') {
    transformedCoupon.discountModelFlag = '0';
    transformedCoupon.transDiscountLimitAmt = discountUpLimit;
  }
  transformedCoupon.justArrived = Util.timeMinus(new Date().getTime(), receiveDate) <= 3 ? 'Y' : 'N';
  return transformedCoupon;
}

// 筛选可用券
// 判断有易入难；注意判断之间的依赖关系，如总本金应是满足要求的借据的本金之和
// 1.仅保留券类型：108、114、216
// 2.仅保留已激活的券，券状态为101
// 3.若券类型为216，过滤细分使用场景为博弈的券（subUseSceneCode为SSA02）、过滤细分使用场景为促提还的券（subUseSceneCode为SSA08）
// 4.若券类型为114，券不可用，不可用原因为：仅提前还款可使用
// 5.招行渠道查券，若使用渠道规则不满足，需过滤--需按名单制豁免
// 6.招行渠道查券，过滤不保留原券模式的已膨胀券
// 7.已膨胀券的膨胀属性是否覆盖问题：0APP渠道，均展示膨胀后属性值；非0APP渠道：不保留原券模式-展示膨胀后属性值；保留原券模式-展示膨胀前属性值
// 8.规则校验（有一笔借据可用，即券可用）：
// 单笔借据层面：
// 1）若券类型为还款抵扣券，单笔借据利息>0才可用
// 2）校验还款商户-topMerchantId、额度类型-limitType、借据放款本金-debtPrincipalAmt规则、客户类型限制（过交易中台）
// 所有借据层面：
// 1）校验transAmt条件，
// 若券类型为息费减免券，通过repayTotalAmt比较；
// 若为其他券类型，通过累加所有借据的principalAmt得到还款总本金再比较；
// 2）若券类型为108、114，校验叠加规则
// 3）若券类型为还款抵扣券，校验还款总利息>0才可用
// 4）非0APP渠道查券，不保留原券模式的已膨胀券不可用（不可用原因为 仅限招联APP可用）
// 5）校验还款渠道transTerminal条件
// 分券类型判断，先判断券展示与否，再判断券在哪个列表
/**
 * 优惠券不可用原因
 * */
const DISABLE_REASON_MAP = {
  transTerminal: '仅限于特定的渠道使用',
  normalPayOnly: '提前还款不可使用',
  repayAmt: '还款金额未达到使用条件',
  prepayOnly: '仅提前还款可使用',
  appOnly: '仅限招联APP可用',
  subUseSceneCode: '交易要素不满足条件',
  unsupportLoanWaive: '待还款的借据已享受过借款优惠',
  unsupportPartRepay: '还清整期账单可用'
};
export async function filterWaiveCoupons(originCouponList, waiveCheckParam) {
  let usableWaiveConponList = [];
  let appWaiveCouponList = [];
  let unUsableWaiveConponList = [];
  let needFilterWaiveCouponAwardNos = []; // 需过滤不返回优惠券列表
  // 还款可用优惠券（含需过滤）
  const repayWaiveCouponList = (originCouponList || []).filter((couponItem = {}) => (
    (couponItem.awardType === '108'
      || couponItem.awardType === '114'
      || (couponItem.awardType === '216'
        && (couponItem.subUseSceneCode !== 'SSA02' && couponItem.subUseSceneCode !== 'SSA08')))
    && couponItem.awardStatus === '101'
  ));
  // 获取需过滤不对客展示优惠券
  let hitCmbCdpFlag = '';
  await Promise.all((repayWaiveCouponList || []).map(async (waiveItem = {}) => {
    if (Madp.getChannel() === '3CMBAPP') {
      if (Object.keys(waiveItem.awardUseCondition || {}).length) {
        const {
          transTerminal, transTerminalCondition
        } = waiveItem.awardUseCondition;
        if (transTerminal && ((transTerminalCondition === 'in' && transTerminal.indexOf(Madp.getChannel()) < 0)
          || (transTerminalCondition === 'notin' && transTerminal.indexOf(Madp.getChannel()) > -1))) {
          if (!hitCmbCdpFlag) {
            const { recommendRsp } = await getPageData({
              recommendReq: {
                operateVariableCodeList: ['l_a3_sy_list', 'zy_fst_lvl1_rgl_bus_cls_cd']
              }
            }) || {};
            const {
              operateVariableMap
            } = recommendRsp || {};
            const cdpFlag1 = ((operateVariableMap || {})['l_a3_sy_list'] || {}).resultVal || 'N';
            const cdpFlag2 = ((operateVariableMap || {})['zy_fst_lvl1_rgl_bus_cls_cd'] || {}).resultVal || '';
            // 1、名单判断：l_a3_sy_list，值为Y展示回家券，值不为Y屏蔽回家券
            // 2、标签判断：zy_fst_lvl1_rgl_bus_cls_cd的标签值=A1\A2\A4\A6时展示回家券；当zy_fst_lvl1_rgl_bus_cls_cd的标签值=A3\空值\未来有新增值的情况，时保持屏蔽回家券。
            let showBackHomeCouponChannel = [];
            if (getProductAllParams && typeof getProductAllParams === 'function') {
              const { terminalTagArr = '' } = await getProductAllParams('YHQ.YHQ01');
              showBackHomeCouponChannel = JSON.parse(terminalTagArr.replace(/'/g, '"'));
            }
            // 3、结论：l_a3_sy_list值为Y，或者zy_fst_lvl1_rgl_bus_cls_cd的标签值=A1\A2\A4\A6时展示回家券，否则过滤掉不展示
            hitCmbCdpFlag = !(cdpFlag1 === 'Y' || (showBackHomeCouponChannel || []).includes(cdpFlag2));
          }
          if (hitCmbCdpFlag) {
            needFilterWaiveCouponAwardNos.push(waiveItem.awardNo); // 过滤掉招行不可用券
            return;
          }
        }
      }
      if (waiveItem.awardInflationStatus === 'Y' && waiveItem.inflationCoverFlag === 'Y') {
        needFilterWaiveCouponAwardNos.push(waiveItem.awardNo);
      }
    }
  }));
  const filteredWaiveCouponList = (repayWaiveCouponList || []).filter((item = {}) => (needFilterWaiveCouponAwardNos.indexOf(item.awardNo) < 0));
  // 券可用、不可用判断
  (filteredWaiveCouponList || []).forEach(async (waiveItem = {}) => {
    // 覆盖模式膨胀券仅在膨胀券可用渠道内应用
    if (waiveItem.awardInflationStatus === 'Y'
      && waiveItem.inflationCoverFlag === 'Y'
      && (((waiveItem.inflationProperty || {}).inflationAvailChannelCode || '').indexOf(Madp.getChannel()) < 0)) {
      if (((waiveItem.inflationProperty || {}).inflationAvailChannelCode || '').indexOf('0APP') >= 0) {
        appWaiveCouponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.appOnly });
      } else {
        unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.transTerminal });
      }
      return;
    }
    const {
      repayType, repayDetailList, repayTotalAmt, currentBillClear, amtHasChanged,
    } = waiveCheckParam || {};
    // 108仅按期还款可使用
    if (waiveItem.awardType === '108') {
      if (repayType === 'prepay') {
        unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.normalPayOnly });
        return;
      }
    }
    // 114仅提前还款可使用
    if (waiveItem.awardType === '114') {
      if (repayType === 'normalPay') {
        unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.prepayOnly });
        return;
      }
    }
    // 券支持借据列表判断、筛选，先通用，再定制
    const formatRepayDetailList = parseRepayOrderList(repayDetailList);
    let availOrderNoList = formatRepayDetailList.map((item = {}) => item.orderNo) || [];
    // 价券叠加
    if ((waiveItem.availSuperpositionTempPrice || '').indexOf('0') > -1) {
      if (formatRepayDetailList.length > 0 && (formatRepayDetailList.filter((item = {}) => (item.useTempPriceFlag === 'Y')).length === formatRepayDetailList.length)) {
        unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.unsupportLoanWaive });
        return;
      }
      const availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (item.useTempPriceFlag !== 'Y')).map((item = {}) => item.orderNo);
      availOrderNoList = availOrderNoList.filter((item) => availOrderNoListCur.indexOf(item) >= 0);
    }
    if ((waiveItem.availSuperpositionLoanCoupon || '').indexOf('0') > -1) {
      if (formatRepayDetailList.length > 0 && (formatRepayDetailList.filter((item = {}) => (item.discount === 'Y')).length === formatRepayDetailList.length)) {
        unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.unsupportLoanWaive });
        return;
      }
      const availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (item.discount !== 'Y')).map((item = {}) => item.orderNo);
      availOrderNoList = availOrderNoList.filter((item) => availOrderNoListCur.indexOf(item) >= 0);
    }
    // 108券息费校验
    if (waiveItem.awardType === '108') {
      if (formatRepayDetailList.filter((item = {}) => (Number(item.feeAmt || '0.00') > 0)).length <= 0) {
        unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.subUseSceneCode });
        return;
      }
      const availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (Number(item.feeAmt || '0.00') > 0)).map((item = {}) => item.orderNo);
      availOrderNoList = availOrderNoList.filter((item) => availOrderNoListCur.indexOf(item) >= 0);
    }
    // 114券提还违约金校验
    if (waiveItem.awardType === '114') {
      if (formatRepayDetailList.filter((item = {}) => (Number(item.prepayAmt || '0.00') > 0)).length <= 0) {
        unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.subUseSceneCode });
        return;
      }
      const availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (Number(item.prepayAmt || '0.00') > 0)).map((item = {}) => item.orderNo);
      availOrderNoList = availOrderNoList.filter((item) => availOrderNoListCur.indexOf(item) >= 0);
    }
    // 216券商户配置可用校验
    if (waiveItem.awardType === '216') {
      if (formatRepayDetailList.length > 0 && (formatRepayDetailList.filter((item = {}) => item.supportSplitFlag === 'N').length === formatRepayDetailList.length)) {
        unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.subUseSceneCode });
        return;
      }
      const availOrderNoListCur = formatRepayDetailList.filter((item = {}) => item.supportSplitFlag !== 'N').map((item = {}) => item.orderNo);
      availOrderNoList = availOrderNoList.filter((item) => availOrderNoListCur.indexOf(item) >= 0);
    }
    // 券规则判断、筛选
    if (Object.keys(waiveItem.awardUseCondition || {}).length) {
      const {
        transAmt, transAmtCondition, topMerchantId, topMerchantIdCondition, limitType, limitTypeCondition,
        debtPrincipalAmt, debtPrincipalAmtCondition, transTerminal, transTerminalCondition, repayBillCondition,
      } = waiveItem.awardUseCondition;
      // 还款渠道限制
      if (transTerminal) {
        if (transTerminalCondition === 'in' && transTerminal.indexOf(Madp.getChannel()) < 0) {
          if (transTerminal.indexOf('0APP') >= 0) {
            appWaiveCouponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.appOnly });
          } else {
            unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.transTerminal });
          }
          return;
        }
        if (transTerminalCondition === 'notin' && transTerminal.indexOf(Madp.getChannel()) > -1) {
          if (transTerminal.indexOf('0APP') < 0) {
            appWaiveCouponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.appOnly });
          } else {
            unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.transTerminal });
          }
          return;
        }
      }
      // 108券当期应结清账单限制
      if (waiveItem.awardType === '108' && repayBillCondition && repayBillCondition.indexOf('11') >= 0) {
        waiveItem.repayBillCondition = '11';
        if (currentBillClear !== 'Y' || amtHasChanged) {
          unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.unsupportPartRepay });
          return;
        }
      }
      // 借据商户限制
      if (topMerchantId) {
        if ((topMerchantIdCondition === 'in' && formatRepayDetailList.filter((item = {}) => (topMerchantId.indexOf(item.topMerchantId) >= 0)).length === 0)
          || (topMerchantIdCondition === 'notin' && formatRepayDetailList.filter((item) => (topMerchantId.indexOf(item.topMerchantId) < 0)).length === 0)) {
          unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.subUseSceneCode });
          return;
        }
        let availOrderNoListCur = [];
        if (topMerchantIdCondition === 'in') {
          availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (topMerchantId.indexOf(item.topMerchantId) >= 0)).map((item = {}) => item.orderNo);
        } else if (topMerchantIdCondition === 'notin') {
          availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (topMerchantId.indexOf(item.topMerchantId) < 0)).map((item = {}) => item.orderNo);
        }
        availOrderNoList = availOrderNoList.filter((item) => availOrderNoListCur.indexOf(item) >= 0);
      }
      // 借据额度类型限制
      if (limitType) {
        if ((limitTypeCondition === 'in' && formatRepayDetailList.filter((item = {}) => (limitType.indexOf(item.limitType) >= 0)).length === 0)
          || (limitTypeCondition === 'notin' && formatRepayDetailList.filter((item = {}) => (limitType.indexOf(item.limitType) < 0)).length === 0)) {
          unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.subUseSceneCode });
          return;
        }
        let availOrderNoListCur = [];
        if (limitTypeCondition === 'in') {
          availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (limitType.indexOf(item.limitType) >= 0)).map((item = {}) => item.orderNo);
        } else if (limitTypeCondition === 'notin') {
          availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (limitType.indexOf(item.limitType) < 0)).map((item = {}) => item.orderNo);
        }
        availOrderNoList = availOrderNoList.filter((item) => availOrderNoListCur.indexOf(item) >= 0);
      }
      // 借据放款本金校验
      if (debtPrincipalAmt) {
        if ((debtPrincipalAmtCondition === '==' && formatRepayDetailList.filter((item = {}) => (Number(debtPrincipalAmt) === Number(item.debtPrincipalAmt || '0.00'))).length === 0)
          || (debtPrincipalAmtCondition === '>=' && formatRepayDetailList.filter((item = {}) => (Number(item.debtPrincipalAmt || '0.00') >= Number(debtPrincipalAmt))).length === 0)
          || (debtPrincipalAmtCondition === '<=' && formatRepayDetailList.filter((item = {}) => (Number(item.debtPrincipalAmt || '0.00') <= Number(debtPrincipalAmt))).length === 0)
          || (debtPrincipalAmtCondition === '>' && formatRepayDetailList.filter((item = {}) => (Number(item.debtPrincipalAmt || '0.00') > Number(debtPrincipalAmt))).length === 0)
          || (debtPrincipalAmtCondition === '<' && formatRepayDetailList.filter((item = {}) => (Number(item.debtPrincipalAmt || '0.00') < Number(debtPrincipalAmt))).length === 0)
          || (debtPrincipalAmtCondition === '!=' && formatRepayDetailList.filter((item = {}) => (Number(item.debtPrincipalAmt || '0.00') !== Number(debtPrincipalAmt))).length === 0)) {
          unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.subUseSceneCode });
          return;
        }
        let availOrderNoListCur = [];
        if (debtPrincipalAmtCondition === '==') {
          availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (Number(debtPrincipalAmt) === Number(item.debtPrincipalAmt || '0.00'))).map((item = {}) => item.orderNo);
        } else if (debtPrincipalAmtCondition === '>=') {
          availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (Number(item.debtPrincipalAmt || '0.00') >= Number(debtPrincipalAmt))).map((item = {}) => item.orderNo);
        } else if (debtPrincipalAmtCondition === '<=') {
          availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (Number(item.debtPrincipalAmt || '0.00') <= Number(debtPrincipalAmt))).map((item = {}) => item.orderNo);
        } else if (debtPrincipalAmtCondition === '>') {
          availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (Number(item.debtPrincipalAmt || '0.00') > Number(debtPrincipalAmt))).map((item = {}) => item.orderNo);
        } else if (debtPrincipalAmtCondition === '<') {
          availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (Number(item.debtPrincipalAmt || '0.00') < Number(debtPrincipalAmt))).map((item = {}) => item.orderNo);
        } else if (debtPrincipalAmtCondition === '!=') {
          availOrderNoListCur = formatRepayDetailList.filter((item = {}) => (Number(item.debtPrincipalAmt || '0.00') !== Number(debtPrincipalAmt))).map((item = {}) => item.orderNo);
        }
        availOrderNoList = availOrderNoList.filter((item) => availOrderNoListCur.indexOf(item) >= 0);
      }
      // 记录券要求最低还款金额
      if (transAmt && transAmtCondition === '>=') {
        waiveItem.transAmt = transAmt;
      }
      // 还款金额（总本金）校验
      let totalTransAmt = '';
      if (waiveItem.awardType === '216') {
        totalTransAmt = repayTotalAmt;
      } else {
        let totalPrincipalAmt = 0;
        formatRepayDetailList.forEach((item = {}) => {
          if (availOrderNoList.indexOf(item.orderNo) >= 0) {
            totalPrincipalAmt = Util.floatAdd(totalPrincipalAmt, item.principalAmt || 0);
          }
        });
        let useCouponAmtSwitch = '';
        if (getProductAllParams && typeof getProductAllParams === 'function') {
          ({ useCouponAmtSwitch } = await getProductAllParams('HK.HK01') || {});
        }
        totalTransAmt = useCouponAmtSwitch === 'Y' ? repayTotalAmt : totalPrincipalAmt;
      }
      if (transAmt && Number(totalTransAmt || 0) > 0 && ((transAmtCondition === '==' && (Number(transAmt) !== Number(totalTransAmt)))
        || (transAmtCondition === '>=' && (Number(totalTransAmt) < Number(transAmt)))
        || (transAmtCondition === '<=' && (Number(totalTransAmt) > Number(transAmt)))
        || (transAmtCondition === '>' && (Number(totalTransAmt) <= Number(transAmt)))
        || (transAmtCondition === '<' && (Number(totalTransAmt) >= Number(transAmt)))
        || (transAmtCondition === '!=' && (Number(totalTransAmt) === Number(transAmt))))) {
        dispatchTrackEvent({ event: EventTypes.SO, beaconId: `UnUsableCouponTransAmt-${totalTransAmt}` });
        unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.repayAmt });
        return;
      }
    }
    // 券支持借据列表是否存在判断
    if (!(availOrderNoList || []).length && (formatRepayDetailList || []).length) {
      unUsableWaiveConponList.push({ ...waiveItem, usable: 'N', unavailableReason: DISABLE_REASON_MAP.subUseSceneCode });
      return;
    }
    waiveItem.orderNoList = Array.from(new Set(availOrderNoList || []));
    usableWaiveConponList.push({ ...waiveItem, usable: 'Y' });
  });
  return {
    availableCouponDetailList: usableWaiveConponList,
    appExclusiveCouponDetailList: appWaiveCouponList,
    unavailableCouponDetailList: unUsableWaiveConponList,
  };
}

// 新权益对象信息
export function getFeeInteRightInfo(selectedCoupon) {
  const {
    awardNo, awardOutDisplayName, awardType, subUseSceneCode,
    awardAmtType, awardAmt, discountDurationType,
    discountDurationCount, discountUpLimit, discountUpLimitType,
    waiveRatioTag, transAmt, orderNoList, discountGradientList, repayBillCondition,
  } = selectedCoupon || {};
  const feeInteRightInfo = {
    rightsNo: awardNo || '',
    rightsName: awardOutDisplayName || '',
    rightsType: awardType || '',
    specialScene: subUseSceneCode || '',
    rightsDeductType: awardAmtType || '',
    rightsValue: awardAmt || '',
    discountDurationType: discountDurationType || '',
    discountDurationCount: discountDurationCount || 0,
    discountUpLimit: discountUpLimit || '',
    discountUpLimitType: discountUpLimitType || '',
    relatedValue: waiveRatioTag || '',
    transAmtLowerLimit: Number(transAmt || '0.00').toFixed(2),
    relateOrderList: orderNoList,
    discountGradientList: discountGradientList || [],
    repayBillCondition: repayBillCondition || '',
  };
  return feeInteRightInfo;
}
