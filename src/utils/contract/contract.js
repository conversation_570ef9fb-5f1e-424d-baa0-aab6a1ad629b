// import fetch from '@utils/mgp-fetch';
import Template7 from 'template7/dist/template7.min.js';
import T7HelperRegister from './t7';
import { WeappTemplate, getHtmlBodyContent } from './weappHandler';
import paramHandler from './paramHandler';
T7HelperRegister(Template7);

const contractApi = {};
const isWeapp = process.env.TARO_ENV === 'weapp';
/**
 * 用queryHtmlTemplate接口返回的模板，前端要自行渲染（填充数据）
 * h5用Template7，weapp用WeappTemplate
 */
contractApi.handleResult = async (ret, opt, contractData) => {
  opt = opt || {};
  contractData = contractData || {};
  // 合同填充参数
  let agreementData = await contractApi.setAgreementData(ret, opt, contractData);
  // agreementDate为合同的日期，部分协议如征信协议的日期为&nbsp年&nbsp月&nbsp日，与其他协议日期不一致，这里做统一处理
  // agreementData.agreementDate = '&nbsp;&nbsp;'; // 还是要显示日期
  // weapp不支持Template7,使用WeappTemplate
  let afterHtml;
  if (isWeapp) {
    // if (agreementData.agreementDate == '&nbsp;&nbsp;') {
    //   agreementData.agreementDate = '  ';
    // }
    const weHtml = new WeappTemplate(agreementData);
    const htmlBlocks = weHtml.stringToBlocks(ret.htmlTemplate);
    afterHtml = weHtml.computeBlocks(htmlBlocks);
  } else {
    const temp = Template7.compile(ret.htmlTemplate);
    afterHtml = temp(agreementData);
  }
  return afterHtml;
};

/**
 * 处理要填充到合同里面的参数
 */
contractApi.setAgreementData = async (ret, opt, contractData) => {
  let agreementData = { ...contractData };
  // if (ret.contractData) {
  //   // 若根据合同号查得合同信息，则填充参数从中台返回无需处理
  //   agreementData = ret.contractData;
  //   agreementData = JSON.parse(agreementData);
  // } else {
  // 否则填充参数从外部传入，需统一处理
  agreementData = await paramHandler(agreementData, opt);
  // }
  return agreementData;
};

/**
 * h5: 签约后接口返回的合同自带样式，要去除
 * weapp: 小程序要对合同格式进行调整
 */
contractApi.getHtmlBodyContent = async (htmlFile) => {
  return getHtmlBodyContent(htmlFile);
};

export default contractApi;