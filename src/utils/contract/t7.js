/* eslint-disable */
'use strict';

/**
 *
 * @file 注册Template7 helper
 * <AUTHOR>
 * @since 2.3.0
 */


export default function T7HelperRegister(T7) {
  /** *
   * new Date(String).format("yyyy-MM-dd")
   */
  function dateFormatter(date, format) {
    /*
     * eg:format="yyyy-MM-dd hh:mm:ss";
     */
    let o = {
      'M+': date.getMonth() + 1, // month
      'd+': date.getDate(), // day
      'h+': date.getHours(), // hour
      'm+': date.getMinutes(), // minute
      's+': date.getSeconds(), // second
      'q+': Math.floor((date.getMonth() + 3) / 3), // quarter
      'S': date.getMilliseconds()
      // millisecond
    };

    if (/(y+)/.test(format)) {
      format = format.replace(RegExp.$1, (`${date.getFullYear()}`)
        .substr(4 - RegExp.$1.length));
    }

    for (let k in o) {
      if (new RegExp(`(${k})`).test(format)) {
        format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k]
          : (`00${o[k]}`).substr((`${o[k]}`).length));
      }
    }
    return format;
  }

  // 去掉字符首尾的空格
  T7.registerHelper('trim', (value, options) => {
    return (`${value}`).trim();
  });

  // 去掉金额小数点后面的0
  T7.registerHelper('trimTailZeros', (value, options) => {
    value = parseFloat(value);
    if (typeof value === 'number') {
      return value;
    } else {
      console.error(`invalid value: ${value}, it must be number`);
    }
  });

  // 格式化金额为两位小数点显示
  T7.registerHelper('toAmount', (value, options) => {
    value = parseFloat(value);
    if (typeof value === 'number') {
      return value.toFixed(2);
    } else {
      console.error(`invalid value: ${value}, it must be number`);
    }
  });

  T7.registerHelper('toDateTime', (timestamp, format, options) => {
    if (typeof format === 'object') {
      options = format;
      format = '';
    }

    if (!format) {
      format = 'yyyy-MM-dd hh:mm:ss';
    }

    let d = new Date(timestamp);
    return dateFormatter(d, format);
  });

  T7.registerHelper('toDate', (timestamp, format, options) => {
    if (typeof format === 'object') {
      options = format;
      format = '';
    }

    if (!format) {
      format = 'yyyy-MM-dd';
    }
    let d = new Date(timestamp);
    return dateFormatter(d, format);
  });

  /**
   * for "YYYYMMDD" or "YYMMDD" to Date (YYYY-MM-DD)
   */
  T7.registerHelper('strToDate', (timestamp, format, options) => {
    if (typeof format === 'object') {
      options = format;
      format = '';
    }

    if (!format) {
      format = 'yyyyMMdd';
    }

    let pattern = format.replace('yyyy', '(\\~1{4})').replace('yy', '(\\~1{2})')
      .replace('MM', '(\\~1{2})').replace('M', '(\\~1{1,2})')
      .replace('dd', '(\\~1{2})').replace('d', '(\\~1{1,2})').replace(/~1/g, 'd');

    let returnDate;
    if (new RegExp(pattern).test(timestamp)) {
      let yPos = format.indexOf('yyyy');
      let mPos = format.indexOf('MM');
      let dPos = format.indexOf('dd');
      if (mPos == -1) mPos = format.indexOf('M');
      if (yPos == -1) yPos = format.indexOf('yy');
      if (dPos == -1) dPos = format.indexOf('d');
      let pos = new Array(`${yPos}y`, `${mPos}m`, `${dPos}d`).sort();
      let data = { 'y': 0, 'm': 0, 'd': 0 };
      let m = timestamp.match(pattern);
      for (let i = 1; i < m.length; i++) {
        if (i == 0) return;
        let flag = pos[i - 1].split('')[1];
        data[flag] = m[i];
      }

      if (data.y.toString().length == 2) {
        data.y = parseInt(`20${data.y}`);
      }
      data.m = data.m - 1;
      returnDate = new Date(data.y, data.m, data.d);
    }
    if (returnDate == null || isNaN(returnDate)) returnDate = new Date();

    return dateFormatter(returnDate, 'yyyy.MM.dd');
  });

  T7.registerHelper('getDate', (str, options) => {
    if (str && str.length == 19) {
      return str.substr(0, 10);
    }
  });

  T7.registerHelper('getTime', (str, options) => {
    if (str && str.length == 19) {
      return str.substr(11, 5);
    }
  });

  T7.registerHelper('toChineseNumber', (n, options) => {
    let fraction = ['角', '分'];
    let digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    let unit = [['元', '万', '亿'], ['', '拾', '佰', '仟']];
    let head = n < 0 ? '欠' : '';
    n = Math.abs(n);

    let s = '';

    for (var i = 0; i < fraction.length; i++) {
      s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
    }
    s = s || '整';
    n = Math.floor(n);

    for (var i = 0; i < unit[0].length && n > 0; i++) {
      let p = '';
      for (let j = 0; j < unit[1].length && n > 0; j++) {
        p = digit[n % 10] + unit[1][j] + p;
        n = Math.floor(n / 10);
      }
      s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }
    return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
  });

  // 为手机号的中间4位加掩码，从第四位开始
  T7.registerHelper('maskPhoneNumber', (str) => {
    if (typeof str === 'string') {
      let output = `${str.slice(0, 3)}****${str.slice(7)}`;
      return output;
    } else {
      console.error(`invalid value: ${str}, it must be string`);
    }
  });

  // 显示字符串的后几位
  T7.registerHelper('showNumberTail', (str, n) => {
    if (typeof str === 'string') {
      var n = parseInt(n) || 4;
      let output = str.slice(-n);
      return output;
    } else {
      console.error(`invalid value: ${str}, it must be string`);
    }
  });

  // add more here

  return T7;
}