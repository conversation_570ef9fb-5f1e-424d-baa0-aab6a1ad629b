import Madp from '@mu/madp';
import {
  getCurrentPageUrlWithArgs
} from '@mu/madp-utils';
import {
  getToken
} from '@mu/dev-finger';
import {
  apiHost
} from '@utils/constants';
let isToast = false;
const duration = 2000;

const rejectFunc = async (res, options) => {
  const {
    autoToast = true
  } = options;
  const FALLBACK_TEXT = '系统繁忙，请稍后再试';
  Madp.eventCenter.trigger('madp_biz_error_event', res.errMsg || FALLBACK_TEXT);
  if (autoToast) {
    isToast = true;
    Madp.showToast({
      title: res.errMsg || FALLBACK_TEXT,
      icon: 'none',
      duration
    });
    setTimeout(() => {
      isToast = false;
    }, duration);
  }
  return Promise.reject(res || FALLBACK_TEXT);
};

const fetch = async (operationId, options) => {
  const pageUrl = getCurrentPageUrlWithArgs();

  // 设备指纹引入,接口签名已经默认在 Madp.request集成，无需在环境参数再做处理
  const token = await getToken(true, Madp.getChannel());

  // 业务mapCode获取，详情查看app.js 的 eventCenter 相关代码
  const mapCode = Madp.getStorageSync(
    'mapCode',
    'SESSION',
  ) || '';

  const {
    EnvParams = {}
  } = options;

  const reqEnvParams = {
    channel: Madp.getChannel(),
    appType: 'H5',
    pageUrl,
    token,
    mapCode,
    module: 'repayment',
    ...EnvParams
  };

  const {
    fileList,
    data
  } = options;

  const requestOptions = {
    url: `${apiHost.mgp}?operationId=${operationId}`,
    method: 'POST',
    data: {
      ...fileList,
      data: JSON.stringify(data),
      reqEnvParams
    },
  };

  const {
    autoLoading = true
  } = options;
  if (autoLoading && !isToast) {
    Madp.showLoading({
      title: '加载中'
    });
  }
  return Madp.uploadFileList(requestOptions)
    .then(async (res) => {
      const json = JSON.parse(res);
      Madp.hideLoading();
      if (json.ret === '0') {
        return Promise.resolve(json.data);
      }
      return Promise.reject(json);
    })
    .catch((res) => {
      Madp.hideLoading();
      return rejectFunc(res, {
        ...requestOptions
      });
    });
};

export default fetch;
