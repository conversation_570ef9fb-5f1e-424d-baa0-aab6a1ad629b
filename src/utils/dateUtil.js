/**
 *
 * @file date util
 * <AUTHOR>
 */

/**
 * get GET param
 * @param key
 */
export const dateFormat = (date, format) => {
  /*
   * eg:format="yyyy-MM-dd hh:mm:ss";
   */
  let formatRet = format;
  const o = {
    'M+': date.getMonth() + 1, // month
    'd+': date.getDate(), // day
    'h+': date.getHours(), // hour
    'm+': date.getMinutes(), // minute
    's+': date.getSeconds(), // second
    'q+': Math.floor((date.getMonth() + 3) / 3), // quarter
    S: date.getMilliseconds()
    // millisecond
  };

  if (/(y+)/.test(formatRet)) {
    formatRet = formatRet.replace(RegExp.$1, (`${date.getFullYear()}`)
      .substr(4 - RegExp.$1.length));
  }

  /* eslint-disable-next-line */
  for (const k in o) {
    if (new RegExp(`(${k})`).test(formatRet)) {
      formatRet = formatRet.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k]
        : (`00${o[k]}`).substr((`${o[k]}`).length));
    }
  }
  return formatRet;
};

export default {
  dateFormat,
};
