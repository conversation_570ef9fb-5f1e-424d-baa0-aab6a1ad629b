/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
/**
 * 根据当前域名确定运行环境: st1 st2 uat prod
 */

import { isMuapp, isAlipay, isWechat, isUnicom, getEnv } from '@mu/madp-utils';
import { getAllLocalConfigByNs } from '@mu/business-basic';
import Madp from '@mu/madp';
import appInfos from '@utils/app-config-infos';

let mucfcst = '';

if (window && window.location && window.location.hostname === 'm-zl-st2.mucfc.com') {
  mucfcst = 'st2';
} else if (window && window.location && window.location.hostname === 'm-zl-st1.mucfc.com') {
  mucfcst = 'st1';
}

const currentEnv = process.env.BUILD_ENV || mucfcst || getEnv();
const currentChannel = Madp.getChannel();
const header = (process.env.TARO_ENV !== 'h5' || process.env.BUILD_TYPE === 'offline') ? 'https:' : window.location.protocol;

const mallApiHost = {
  se: `${header}//mall-se.api${appInfos.stStaticDomain}/api`,
  se1: `${header}//mall-se1.api${appInfos.stStaticDomain}/api`,
  se2: `${header}//mall-se2.api${appInfos.stStaticDomain}/api`,
  st1: `${header}//st1.mall.api${appInfos.stStaticDomain}/api`,
  st2: `${header}//st2-mall.api${appInfos.stStaticDomain}/api`,
  uat: `${header}//uat-mall.api${appInfos.stStaticDomain}/api`,
  prod: `https://mall.api${appInfos.prodStaticDomain}/api`,
  mock: `${header}//mock${appInfos.prodStaticDomain}/mock/5a4de62c0f4f5111778edd5d/api`
};

const aliAuth = {
  st: `${header}//auth-st1.api${appInfos.stStaticDomain}/st-ali/alipay/window/auth.jhtml`,
  st1: `${header}//auth-st1.api${appInfos.stStaticDomain}/st1-ali/alipay/window/auth.jhtml`,
  st2: `${header}//auth-st1.api${appInfos.stStaticDomain}/st2-ali/alipay/window/auth.jhtml`,
  uat: `${header}//auth-st1.api${appInfos.stStaticDomain}/uat-ali/alipay/window/auth.jhtml`,
  prod: `https://auth-ali.api${appInfos.prodStaticDomain}/alipay/window/auth.jhtml`
};

const muHostSubfix = {
  st: `.api${appInfos.stStaticDomain}`,
  st1: `.api${appInfos.stStaticDomain}`,
  st2: `.api${appInfos.stStaticDomain}`,
  se: `.api${appInfos.stStaticDomain}`,
  se1: `.api${appInfos.stStaticDomain}`,
  se2: `.api${appInfos.stStaticDomain}`,
  uat: `.api${appInfos.stStaticDomain}`,
  prod: `.api${appInfos.prodStaticDomain}`,
  mock: 'mock',
  rap: 'rap',
};

const muApiModules = ['mgp', 'forum', 'user', 'auth', 'buy', 'loan', 'order', 'bill', 'apply', 'promote', 'mgr', 'pay'];

// mock服务器的模块ID，http://mock.mucfc.com/
const mockModuleIds = {
  user: '585109e0831f1f7be4d42a94',
  auth: '5851040272f1207b247a272a',
  buy: '58510a32831f1f7be4d42a97',
  loan: '58510a46831f1f7be4d42a98',
  order: '585109f6831f1f7be4d42a95',
  bill: '58510a05831f1f7be4d42a96',
  apply: '58510a5a831f1f7be4d42a99',
  promote: '585110e7831f1f7be4d42aa0',
  mgr: '58511106831f1f7be4d42aa1',
  pay: '5a4f1c820f4f5111778edd65',
};

// rap 的模块 ID, http://rap.mucfc.com:8080/org/index.do
const rapModuleIds = {
  user: '2',
  auth: '1',
  buy: '6',
  loan: '7',
  order: '3',
  bill: '5',
  apply: '8',
  promote: '4',
  mgr: '12',
  pay: '48'
};

// muApiModules + muHostSubfix
const apiHost = {};

const muHostSubfixKeys = Object.keys(muHostSubfix);
muApiModules.forEach((m) => {
  muHostSubfixKeys.forEach((key) => {
    /* eslint-disable no-prototype-builtins */
    if (muHostSubfix.hasOwnProperty(key)) {
      apiHost[key] = apiHost[key] || {};

      if (muHostSubfix[key].indexOf('mucfc.com') !== -1) {
        if (isAlipay() && m !== 'pay' && m !== 'mgp' && m !== 'forum') {
          // 支付宝渠道
          apiHost[key][m] = `https://${m}-ali${muHostSubfix[key]}`;
        } else {
          apiHost[key][m] = `https://${m}${muHostSubfix[key]}`;
        }
      } else if (muHostSubfix[key] === 'mock') {
        apiHost[key][m] = `http://mock${appInfos.prodStaticDomain}/mock/${mockModuleIds[m]}`;
      } else if (muHostSubfix[key] === 'rap') {
        apiHost[key][m] = `http://rap${appInfos.prodStaticDomain}:8080/mockjsdata/${rapModuleIds[m]}`;
      } else {
        let moduleSuffix = '';
        switch (key) {
          case 'se': moduleSuffix = '-se'; break;
          case 'se1': moduleSuffix = '-se1'; break;
          case 'se2': moduleSuffix = '-se2'; break;
          case 'st1': moduleSuffix = '-st1'; break;
          case 'st2': moduleSuffix = '-st2'; break;
          case 'uat': moduleSuffix = '-uat'; break;
          default: break;
        }
        if (isAlipay() && m !== 'pay' && m !== 'mgp' && m !== 'forum') {
          apiHost[key][m] = `${header}//${m}-ali${moduleSuffix}${muHostSubfix[key]}`;
        } else {
          apiHost[key][m] = `${header}//${m}${moduleSuffix}${muHostSubfix[key]}`;
        }

        // 对pos模块的特殊处理, 支付宝渠道的由pos通过ua路由
        if (m === 'pay' && key === 'uat') {
          apiHost[key][m] = `http://uat-pay.api${appInfos.stStaticDomain}`;
        }
        if (m === 'pay' && key === 'st2') {
          apiHost[key][m] = `http://st2-pay.api${appInfos.stStaticDomain}`;
        }
        if (m === 'pay' && key === 'sit') {
          apiHost[key][m] = `http://buy.api${appInfos.stStaticDomain}`;
        }
      }
    }
  });
});

const domain = {
  se: `${header}//mall-se${appInfos.stStaticDomain}`,
  se1: `${header}//mall-se1${appInfos.stStaticDomain}`,
  se2: `${header}//mall-se2${appInfos.stStaticDomain}`,
  st1: `${header}//mall-st1${appInfos.stStaticDomain}`,
  st2: `${header}//st2-mall${appInfos.stStaticDomain}`,
  uat: `${header}//uat-mall${appInfos.stStaticDomain}`,
  prod: `https://mall${appInfos.prodStaticDomain}`
};

const muUrlDomainTemp = {
  st: `${header}//m-zl-st${appInfos.stStaticDomain}`,
  st1: `${header}//m-zl-st1${appInfos.stStaticDomain}`,
  st2: `${header}//m-zl-st2${appInfos.stStaticDomain}`,
  uat: `${header}//m-zl-uat${appInfos.stStaticDomain}`,
  se: `${header}//m-zl-se${appInfos.stStaticDomain}`,
  se1: `${header}//m-zl-se1${appInfos.stStaticDomain}`,
  se2: `${header}//m-zl-se2${appInfos.stStaticDomain}`,
  prod: `https://m-zl${appInfos.prodStaticDomain}`
};

// 安全中心
const safeUrlDomainTemp = {
  rap: `${header}//safe-st1${appInfos.stStaticDomain}/m`,
  se: `${header}//safe-se${appInfos.stStaticDomain}/m`,
  se1: `${header}//safe-se1${appInfos.stStaticDomain}/m`,
  se2: `${header}//safe-se2${appInfos.stStaticDomain}/m`,
  st1: `${header}//safe-st1${appInfos.stStaticDomain}/m`,
  st2: `${header}//safe-st2${appInfos.stStaticDomain}/m`,
  uat: `${header}//safe-st1${appInfos.stStaticDomain}/m`,
  prod: `https://safe${appInfos.prodStaticDomain}/m`
};

const cfgUrlDomainTemp = {
  st: `${header}//cfg-st${appInfos.stStaticDomain}/config`,
  st1: `${header}//cfg-st1${appInfos.stStaticDomain}/config`,
  st2: `${header}//cfg-st2${appInfos.stStaticDomain}/config`,
  uat: `${header}//cfg-uat${appInfos.stStaticDomain}/config`,
  se: `${header}//cfg-se${appInfos.stStaticDomain}/config`,
  se1: `${header}//cfg-se1${appInfos.stStaticDomain}/config`,
  se2: `${header}//cfg-se2${appInfos.stStaticDomain}/config`,
  prod: `https://cfg${appInfos.prodStaticDomain}/config`,
};

// 获取local-config统一配置的id
const appidConfig = getAllLocalConfigByNs && getAllLocalConfigByNs('common') || {};
const { appIdSe2, appIdUat, appIdSt1, appIdSt2, appIdPrd } = appidConfig;

const APPID_MAP = {
  alipayDefault: {
    // 招联金融微信
    se: '2021002113634235',
    se1: '2021002113634235',
    st1: '2021002113634235',
    st2: '2021002113634235',
    uat: '2021002113634235',
    prod: '2021002113634235'
  },
  default: {
    // 招联金融微信
    se: 'wx2c5ee5eaf031f7d4',
    se1: 'wx2c5ee5eaf031f7d4',
    st1: 'wxa6a29c0f2bfc6302',
    st2: 'wx2ff17bcc587d29b0',
    uat: 'wx7dbd8d74122e7689',
    prod: 'wx34a2dec2bb3e9b96'
  },
  se: appIdSe2,
  se1: appIdSe2,
  st1: appIdSt1,
  st2: appIdSt2,
  uat: appIdUat,
  prod: appIdPrd
};

// 分享到小程序的版本
const miniType = { // 正式版:0，测试版:1，体验版:2
  se: 1,
  st1: 2,
  st2: 2,
  uat: 2,
  prod: 0,
};

// 小程序原始id
const miniUserName = {
  se: 'gh_f2fb40b59273',
  st1: 'gh_a030a6983ce7',
  st2: 'gh_a030a6983ce7',
  uat: 'gh_a030a6983ce7',
  prod: 'gh_f2fb40b59273',
};

const groupbuySpecialIdTemp = {
  st1: '210',
  st2: '54',
  uat: '42',
  prod: '205'
};

export const env = getEnv();
export const mallApiDomain = mallApiHost[currentEnv];
export const muApiDomain = apiHost[currentEnv];
export const mallUrlDomain = domain[currentEnv];
export const urlDomain = muUrlDomainTemp[currentEnv];
export const payUrlDomain = `${domain[currentEnv]}/mobile`; // 收银台
export const wechatAppId = APPID_MAP[currentEnv] ? APPID_MAP[currentEnv] : APPID_MAP.default[currentEnv];
export const alipayAppId = APPID_MAP[currentEnv] ? APPID_MAP[currentEnv] : APPID_MAP.alipayDefault[currentEnv];
export const safeUrlDomain = safeUrlDomainTemp[currentEnv];
export const aliCallbackUrl = aliAuth[currentEnv];
export const miniProgramType = miniType[currentEnv];
export const miniProgramUserName = miniUserName[currentEnv];
export const groupbuySpecialId = groupbuySpecialIdTemp[currentEnv];
export const cfgUrlDomain = cfgUrlDomainTemp[currentEnv];
export { currentEnv };

export const wechatH5AppId = currentEnv === 'prod' ? wechatAppId : 'wx1654e5f4411f327c';
