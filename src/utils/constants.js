/* eslint-disable max-len */
import {
  muApiDomain,
  urlDomain
} from '@utils/url_config.js';
import Madp from '@mu/madp';
import { isMiniProgram } from '@utils/repay-util';

export const channel = {
  WEAPP_DEFAULT_CHANNEL: '0WEC',
  SWAN_DEFAULT_CHANNEL: 'SWAN_DEFAULT_CHANNEL',
  ALIPAY_DEFAULT_CHANNEL: 'ALIPAY_DEFAULT_CHANNEL',
  TT_DEFAULT_CHANNEL: 'TT_DEFAULT_CHANNEL',
  H5_DEFAULT_CHANNEL: '0APP',
};

export const isSwanMini = process.env.TARO_ENV === 'swan';

export const mapCodeMap = {
  '2WEC': '63d6eaf6de4fa439',
  '2APP': '63d6eaf6de4fa439',
  '3APP': '63d6eaf6de4fa439',
  '17ZFB': '2b294d0cb3908f54',
  '0WEC': '63d6eaf6de4fa439',
  '0APP': '63d6eaf6de4fa439',
  '1WEC': '63d6eaf6de4fa439',
  '3CUAPP': '4b30143939397cb5',
  '0ZFB': '2b294d0cb3908f54',
  '2ZFB': '2b294d0cb3908f54',
  '16ZFB': '2b294d0cb3908f54',
  '3CMBAPP': '2207727ca5fb2fa6',
  '15ZFB': '2b294d0cb3908f54',
  '14WEC': '63d6eaf6de4fa439',
  '3CEBAPP': 'e336e903ea144652',
  '3ICBCAPP': '04c12fee52da203a',
  '0XZYXDYH': '63d6eaf6de4fa439',
  '3CCCXAPP': '5902b48f32c752d2',
  '3TLZFWEC': '5be01c881678b99c',
  '26WEC': '63d6eaf6de4fa439',
  '12WEC': '63d6eaf6de4fa439',
  '3SPDAPP': '2de112a6a10947ca',
  '3PHDAPP': '2de112a6a10947ca',
  '0MNP': '63d6eaf6de4fa439',
  '1MNP': '63d6eaf6de4fa439',
  '14MNP': '63d6eaf6de4fa439',
  '0ZFBMNPJD': '2b294d0cb3908f54',
};

// 当借据的交易商户是下列商户时，屏蔽账单/借据中的来源文案“来自 XXX”
export const merchantNoList = [
  '10002', '10900002', '20700002', '20700004', '30700001', '30700002', '30700004'
];

const merchantMap = {
  P03: '20900011',
  P02: '20900009',
  P04: '20900017',
};

export const miniProgramChannel = ['0MNP', '1MNP', '0WX1XCX', '0WX2XCX', '0WX3XCX', '3JSCUMNP', '0ZFBMNPJD', '0JD1ZFBMNP', '0JD2ZFBMNP', '0JD3ZFBMNP', '0JD4ZFBMNP', '0JD5ZFBMNP', '0JD6ZFBMNP', '0JD7ZFBMNP', '0JD8ZFBMNP', '92TEST'];
export const isMiniProgramChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;

export const apiHost = muApiDomain;

// 首页还款状态
export const repayStatusType = {
  // 数字的状态是中台返回的，其余文字都是前端自己判断的
  // 中台会返回3，3是empty和clear的集合
  // 请求接口后设置的逻辑会判断repaystatustype里没有的值不会被设置，避免设置一些不存在的状态
  loading: 'loading', // 加载中，非后台返回，自己设的值
  dueTagCust: 'dueTag', // 打标用户，非后台返回，自己设的值，根据7天待还上面isDueTagCust === Y 判断
  overDue: '0', // 逾期未打标
  inSeven: '1', // 7天内单还款日
  today: '2', // 当天待还
  empty: 'empty', // 无待还，近期待还判断不出状态，剩余待还里查询没有账单则空
  clear: 'clear', // 当期已结清，近期待还接口判断不出状态，剩余待还有借据，则clear
  outSeven: '4', // 7天外
  inSevenMutil: '5', // 7天内多还款日
};

export const errCodeMap = {
  UMDP01152: ['UMDP01152', 'TBSTFC011', 'TBCTFC004'],
  UMDP01155: ['UMDP01155', 'TBSTFC008', 'TBCTFC005'],
  UMDP01156: ['UMDP01156', 'TBSTFC006', 'TBCTFC007'],
  UMDP01151: ['UMDP01151', 'TBSTFC014', 'TBSTFC013', 'TBSTFC005', 'TBSTFC004', 'TBSTFC003', 'TBSTFC002', 'TBSTFC016', 'TBSTFC017', 'TBCTFC019', 'TBCTFC018', 'TBCTFC013', 'TBCTFC012', 'TBCTFC011', 'TBCTFC010', 'TBCTFC002', 'TBCTFC003'],
  UMDP01708: ['UMDP01708', 'TBSTFC025'],
  UMDP01728: ['UMDP01728', 'TBCQUERY0010', 'UMDP02506'],
  UMDP01580: ['UMDP01580', 'TBCREPAY018'],
  UMDP02510: ['UMDP02510', 'TBS14002'],
  UMDP01511: ['UMDP01511', 'TBCREPAY015'],
  // 细分对客场景，原UMDP01151拓展为持卡人信息异常、银行卡异常两种对客场景
  // 持卡人信息异常:
  // UMDP02830-户名不匹配、UMDP02831-身份证过期、UMDP02832-身份证不匹配、UMDP02833-手机号不匹配、UMDP02622-客户信息异常
  cardholderInformationError: ['UMDP02830', 'UMDP02831', 'UMDP02832', 'UMDP02833', 'UMDP02622'],
  // 银行卡异常
  // UMDP02837-卡号错误、UMDP02838-因密码异常被锁定、UMDP02840-暂停非柜面交易、UMDP02842-卡被锁定、UMDP02844-卡已挂失、UMDP02846-卡未激活、UMDP01151-卡异常通用错误
  bankCardError: ['UMDP02837', 'UMDP02838', 'UMDP02840', 'UMDP02842', 'UMDP02844', 'UMDP02846', 'UMDP01151']
};

export const errCodeToMsgMap = {
  TBCEF002: '用户未实名',
  TBCTFC002: '您选择的银行卡异常，请更换银行卡或选择其他支付方式还款',
  TBSTCM001: '抱歉，系统异常，请联系在线客服',
  TBC11111: '系统繁忙，请重新登录试试，如有疑问请联系在线客服',
  TBCREPAY018: '对不起，您当前暂时不满足微光省钱卡的申请条件，请重新还款',
  TBCREPAY015: '您有一笔还款银行正在处理中，为避免重复还款，请5分钟后再试。',
  TBCREPAY011: '还款金额不足以还提前还款手续费，请修改金额后重试',
  TBCREPAY001: '您有一笔还款银行正在处理中，为避免重复还款，请1小时后再试',
  TBCREPAY002: '有正在处理的还款，请稍后再试！',
  TBSTFC022: '验证码已发送，请注意查收手机短信',
  TBSTFC021: '动态验证码已失效，请重新获取验证码再试',
  TBSTFC020: '动态验证码发送失败，请重试',
  TBSTFC019: '动态验证码错误，请核对后输入或重新获取验证码',
  TBSTFC014: '您选择的银行卡异常，请更换银行卡或选择其他支付方式还款',
  TBSTFC013: '您选择的银行卡异常，请更换银行卡或选择其他支付方式还款',
  TBSTFC024: '抱歉，还款失败，请更换银行卡再试 ,如有疑问请联系在线客服!',
  TBSTFC025: '您的的银行卡已销户，请选择可用的银行卡或添加新卡',
  TBSTFC023: '您的支付交易已取消， 请重新发起还款',
  TBSTFC015: '您选择的银行卡类型不支持还款，请选择储蓄卡或其他支付方式还款',
  TBSTFC005: '您选择的银行卡异常，请更换银行卡或选择其他支付方式还款',
  TBSTFC004: '您选择的银行卡异常，请更换银行卡或选择其他支付方式还款',
  TBSTFC003: '您选择的银行卡异常，请更换银行卡或选择其他支付方式还款',
  TBSTFC002: '您选择的银行卡异常，请更换银行卡或选择其他支付方式还款',
  TBSTFC016: '您选择的银行卡异常，请更换银行卡或选择其他支付方式还款',
  TBSTFC006: '您选择的银行卡代扣协议异常，请前往`我的-设置-银行卡管理"重新绑定银行卡',
  TBSTFC017: '您选择的银行卡异常，请更换银行卡或选择其他支付方式还款',
  TBSTFC011: '您选择的银行卡已超累计交易限额，请调整银行卡交易限额或选择其他支付方式',
  TBSTFC008: '您选择的银行卡余额不足，请更换银行卡或选择其他支付方式还款',
};

function getTaroHostName() { // taro外部模块域名
  return `${urlDomain}/${Madp.getChannel()}/`;
}

export const taroHostName = getTaroHostName();

// 息费减免页，瓜分倒计时活动玩法
export const activityIds = {
  st1: ['202307050000001', '202307070000003', '202307070000004'],
  st2: ['202307180000004', '202307180000009', '202307180000010'],
  uat: ['202307180000012', '202307180000013', '202307180000014'],
  prd: ['202307260000137', '202307260000138', '202307260000139'],
  prod: ['202307260000137', '202307260000138', '202307260000139']
};

// 首页四宫格，跳转页面链接
export const repayServiceColumnsUrls = {
  FeeReduce: isMiniProgram() ? '/repayment/pages/fee-reduce/index?repaymentFlag=Y' : '/pages/fee-reduce/index?repaymentFlag=Y',
  Bargain: isMiniProgram() ? `${taroHostName}repayment/#/pages/bargain/index?needLogin=1&fromIndex=1&repaymentFlag=Y` : '/pages/bargain/index?fromIndex=1&repaymentFlag=Y',
  Promise: isMiniProgram() ? `${taroHostName}repayment/#/pages/repay-promise/index?needLogin=1&repaymentFlag=Y` : '/pages/repay-promise/index?repaymentFlag=Y',
  BillExtend: isMiniProgram() ? `${taroHostName}repayment/#/pages/bill-extend/list?needLogin=1&repaymentFlag=Y` : '/pages/bill-extend/list?repaymentFlag=Y',
  BillAdvancedStage: isMiniProgram() ? `${taroHostName}repayment/#/pages/bill-advanced-stage/list?needLogin=1&repaymentFlag=Y` : '/pages/bill-advanced-stage/list?repaymentFlag=Y',
  BillsStaging: isMiniProgram() ? `${taroHostName}repayment/#/pages/bills-staging/repay-staging?mapCode=da32c1e2ecaab366&needLogin=1&repaymentFlag=Y` : '/pages/bills-staging/repay-staging?mapCode=da32c1e2ecaab366&repaymentFlag=Y',
  ConsultRepay: isMiniProgram() ? `${taroHostName}repayment/#/pages/consult-repay-apply/index?needLogin=1&repaymentFlag=Y` : '/pages/consult-repay-apply/index?repaymentFlag=Y',
  More: isMiniProgram() ? '/repayment/pages/home/<USER>' : '/pages/home/<USER>',
  Traderecords: isMiniProgram() ? '/traderecords/pages/trade-records/index' : `${taroHostName}traderecords/#/pages/trade-records/index`,
  BillListAll: isMiniProgram() ? '/repayment/pages/bill-list-all/index?repaymentFlag=Y' : '/pages/bill-list-all/index?repaymentFlag=Y',
  SettleProof: isMiniProgram() ? `${taroHostName}repayment/#/pages/settle-proof/SettleHomePage?needLogin=1` : `${taroHostName}repayment/#/pages/settle-proof/SettleHomePage`,
  BankcardList: isMiniProgram() ? '/usercenter/pages/bankcard/CardList' : `${taroHostName}usercenter/#/bankcard/list`,
  PaydayModify: isMiniProgram() ? '/repayment/pages/payday-modify/index?repaymentFlag=Y' : '/pages/payday-modify/index?repaymentFlag=Y',
  OverPay: isMiniProgram() ? '/loan/pages/over-pay/index' : `${taroHostName}loan/#/pages/over-pay/index`,
  SelfInvoice: isMiniProgram() ? `${taroHostName}repayment/#/pages/self-invoice/index?needLogin=1` : `${taroHostName}repayment/#/pages/self-invoice/index`,
  RenewLoans: isMiniProgram() ? `${taroHostName}repayment/#/pages/renew-loans/index?needLogin=1&repaymentFlag=Y` : '/pages/renew-loans/index?repaymentFlag=Y',
};

export const sloganUrl = {
  middle: 'https://file.mucfc.com/zlh/3/0/202401/20240108115727805126.png',
  middleVplus: 'https://file.mucfc.com/zlh/3/0/202401/20240108115727b41c01.png',
  upperLeft: 'https://file.mucfc.com/zlh/3/0/202401/202401081157276b6781.png',
  upperLeftVplus: 'https://file.mucfc.com/zlh/3/0/202401/202401081157274825cc.png',
  upperRight: 'https://file.mucfc.com/zlh/3/0/202401/20240108115727ee8334.png',
  upperRightVplus: 'https://file.mucfc.com/zlh/3/0/202401/20240108115727420a3a.png',
};

// 首页还款状态
export const repayGuideSceneInfo = {
  empty: 'Repayment_WD', // 无贷
  clear: 'Repayment_WYQ', // 当期已结清，有贷未逾期
  4: 'Repayment_WYQ', // 7天外，有贷未逾期
  1: 'Repayment_WYQ', // 7天内单还款日，有贷未逾期
  5: 'Repayment_WYQ', // 7天内多还款日，有贷未逾期
  2: 'Repayment_WYQ', // 当天待还，有贷未逾期
  0: 'Repayment_YQ', // 逾期未打标，有贷已逾期
  protect: 'Repayment_KXQ', // 逾期未超宽限期，有贷宽限期内
  dueTag: 'Repayment_YQ', // 打标客户，有贷已逾期
  DEFAULT: 'Repayment_DEFAULT', // 兜底
};

export const loanUrl = `${taroHostName}loan/#/pages/index/index?cashLoanMode=1&needLogin=1&mtagc=31031.01.01&redirectUrl=${encodeURIComponent(`${taroHostName}repayment/#/pages/index/index?needLogin=1`)}`;
export const chatPageUrl = `${taroHostName}csp/#pages/chat/index?needLogin=1`;
export const clipboardUrl = `${urlDomain}/0WAP2/mainpage/#/pages/download/index?pageId=463117a2-8398-4fbf-87d4-4c8e3cdfd2cb&covenantModule=repayment`;

export const EVENT_CODE_MAP = {
  // 正常还
  repayIndexOpen: '3006', // 还款首页-进入
  repayIndexExit: '3002', // 还款首页-退出
  repayIndexClick: '3001', // 还款首页-点击
  expressRepayClick: '3007', // 还款支付页-点击
  expressRepayStay: '3008', // 还款支付页-停留
  expressRepayExit: '3003', // 还款支付页-退出
  repaySuccessOpen: '3004', // 还款成功页（非结清）-进入
  repaySuccessStay: '3009', // 还款成功页（非结清）-停留
  repaySuccessExit: '3010', // 还款成功页（非结清）-退出
  repaySuccessSettleOpen: '3005', // 还款成功页（结清）-进入
  repaySuccessSettleStay: '3011', // 还款成功页（结清）-停留
  repaySuccessSettleExit: '3012', // 还款成功页（结清）-退出
  repaySuccessClick: '3013', // 还款成功页（非结清）--点击
  repaySuccessSettleClick: '3013', // 还款成功页（结清）--点击
  repaySuccessPendingClick: '3013', // 还款处理中页--点击
  repayFailClick: '3013', // 还款失败页--点击

  // 提前还
  repayIndexAdvanceOpen: 'HKPG000021', // 还款首页-进入
  billListAllAdvanceClick: '4001', // 全部待还页-点击
  billListAllAdvanceStay: '4005', // 全部待还页-停留
  billListAllAdvanceExit: '4006', // 全部待还页-退出
  expressRepayAdvanceClick: '4002', // 还款支付页-点击
  expressRepayAdvanceStay: '4007', // 还款支付页-停留
  expressRepayAdvanceExit: '4008', // 还款支付页-退出
  repaySuccessAdvanceSettleOpen: '4003', // 还款成功页（结清）-进入
  repaySuccessAdvancePartSettleOpen: 'HKPG000011', // 还款成功页（部分结清）-进入
  repaySuccessAdvanceSettleStay: '4009', // 还款成功页（结清）-停留
  repaySuccessAdvanceSettleExit: '4010', // 还款成功页（结清）-退出
  repaySuccessAdvanceClick: '4011', // 还款成功页（非结清）--点击
  repaySuccessAdvanceSettleClick: '4011', // 还款成功页（结清）--点击
  repaySuccessAdvancePartSettleClick: '4011', // 还款成功页（部分结清）--点击
  repaySuccessAdvancePendingClick: '4011', // 还款处理中页--点击
  repayFailAdvanceClick: '4011', // 还款失败页--点击

  // 逾期还
  repayIndexOverDueOpen: '8006', // 还款首页-进入
  repayIndexOverDueStay: '8001', // 还款首页-停留
  repayIndexOverDueExit: '8002', // 还款首页-退出
  expressRepayOverdueClick: '8007', // 还款支付页-点击
  expressRepayOverdueStay: '8008', // 还款支付页-停留
  expressRepayOverdueExit: '8003', // 还款支付页-退出
  repaySuccessOverdueClick: '8009', // 还款成功页（非结清）--点击
  repaySuccessOverdueSettleClick: '8009', // 还款成功页（结清）--点击
  repaySuccessOverduePendingClick: '8009', // 还款处理中页--点击
  repayFailOverdueClick: '8009', // 还款失败页--点击

  // 贷后退出挽留
  feeReduce: '9001', // 息费减免
  billAdvancedStageRetain: '9003', // 再分期办理-逐笔办理
  billAdvancedMergeRetain: '9010', // 再分期办理-多笔合一
  billExtendRetain: '9005', // 延后还
  consultRepayRetain: '9006', // 协商还
  repayPromiseExit: 'HCPG000011', // 还款承诺
  renewLoansIndexRetain: '9011', // 分期还本首页挽留
  renewLoansConfirmRetain: '9012', // 分期还本办理页挽留

  // 结清证明开具成功
  settleProofSuccess: '9014'
};

export const isA2Channel = [
  '0ZFB', '2ZFB', '16ZFB', '17ZFB', '18ZFB', '0ZFBMNPJD', '0JD1ZFBMNP', '0JD2ZFBMNP', '0JD3ZFBMNP', '0JD4ZFBMNP',
  '0JD5ZFBMNP', '0JD6ZFBMN', '0JD7ZFBMNP', '0JD8ZFBMNP', '0JRTTXCX', '0DY1XCX', '0DY2XCX', '0DY3XCX', '0BDXCX', '0KSMNP'
].includes(Madp.getChannel());

export const isA3Channel = ['3CMBAPP'].includes(Madp.getChannel());

export const StandardService = {
  ConsultRepayApply: 'ConsultRepayApply', // 协商还
  BillAdvancedStage: 'BillAdvancedStage', // 再分期
  BillExtend: 'BillExtend', // 延后还
};

export default {
  channel,
  merchantMap,
  apiHost,
  mapCodeMap,
  merchantNoList,
  miniProgramChannel,
  isMiniProgramChannel,
  repayServiceColumnsUrls
};
