import Util from '@utils/maxin-util';
import { getLoginInfo } from '@mu/business-basic';

const getBaseParam = async (newContract) => {
  const { custName, mobile, idNo } = await getLoginInfo();
  const nowDate = new Date();
  const contractData = {
    name: custName || '',
    mobile: mobile || '',
    certId: idNo || '',
    yearNow: nowDate.getFullYear(),
    monthNow: nowDate.getMonth() + 1,
    dayNow: nowDate.getDate(),
    bringParam: 1
  };
  const { dateFormat } = Util.getCurrentDateTimeInFormat();
  if (newContract === '3.0') {
    contractData.baseContractInfo = {
      signDate: dateFormat,
      certType: '身份证'
    };
  }
  return contractData;
};

// 获取贷后资料授权合同需要的参数
const getInfoAuthParam = async (contractParam, newContract = '') => {
  const {
    needCompanySignatureFlag, needCustSignatureFlag
  } = contractParam || {};
  const baseParam = await getBaseParam(newContract);
  return {
    ...baseParam,
    isNeedCompanySignature: needCompanySignatureFlag, // 是否需要公司签章
    isNeedCustSignature: needCustSignatureFlag, // 是否需要个人签章
    needCompanySignature: needCompanySignatureFlag, // 3.0-是否需要公司签章
    needCustSignature: needCustSignatureFlag, // 3.0-是否需要个人签章
  };
};

export default {
  getBaseParam,
  getInfoAuthParam
};


