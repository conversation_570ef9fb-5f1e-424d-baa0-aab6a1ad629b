import Madp from '@mu/madp';
import { apiHost, fetch } from '@mu/business-basic';

const isWeapp = process.env.TARO_ENV === 'weapp';
const isAlipay = process.env.TARO_ENV === 'alipay';
const CHANNEL = Madp.getChannel() || '';

export const queryUserSubscribeInfo = async (params = {}) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.app.subscribe.queryUserSubscribeInfo`, {
      autoLoading: false,
      data: {
        data: params
      }
    });
    return res || {};
  } catch (e) {
    return {};
  }
};

export const templateSubscribe = async (params = {}) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.app.subscribe.templateSubscribe`, {
      data: {
        data: params
      }
    });
    return res || {};
  } catch (e) {
    return {};
  }
};

export const mnp = () => {
  const env = process.env.TARO_ENV;
  if (env === 'weapp') {
    return wx;// eslint-disable-line
  } else if (env === 'alipay') {
    return my;// eslint-disable-line
  } else {
    return {};
  }
};

const ALIPAY_APPID = {
  '0ZFBMNPJD': '****************',
  '0JD1ZFBMNP': '****************',
  '0JD2ZFBMNP': '****************',
  '0JD3ZFBMNP': '****************',
  '0JD4ZFBMNP': '****************',
  '0JD5ZFBMNP': '****************',
  '0JD6ZFBMNP': '****************',
  '0JD7ZFBMNP': '****************',
  '0JD8ZFBMNP': '****************',
};

export const getAppId = () => {
  // 查询appId
  let appId = '';
  if (isWeapp) {
    const accountInfo = wx.getAccountInfoSync();// eslint-disable-line
    appId = (accountInfo && accountInfo.miniProgram && accountInfo.miniProgram.appId) || '';
  } else if (isAlipay) {
    appId = ALIPAY_APPID[CHANNEL] || ''; // 支付宝基础库过低无法使用my.getAccountInfoSync()
  }
  return appId;
};

export const getSubscribedIdList = async (userId, appId, channelCode) => { // 获取用户已订阅的模板id list
  const templateRes = await queryUserSubscribeInfo({ userId, appId, channelCode });
  const { templateSubscribeInfoList = [] } = templateRes || {};
  const userSubscribedIdList = [];
  for (let i = 0; i < templateSubscribeInfoList.length; i++) {
    if (templateSubscribeInfoList[i].subscribeCount > 0) {
      userSubscribedIdList.push(templateSubscribeInfoList[i].templateId);
    }
  }
  return userSubscribedIdList;
};

export const FilterContentList = (contentList = [], userSubscribedIdList = []) => {
  if (contentList.length === 0) {
    return [];
  } else if (userSubscribedIdList.length === 0) {
    return contentList;
  }
  const contentListFiltered = contentList.filter((item) => !userSubscribedIdList.includes(item.tplId)); // 去掉已经被订阅的模板id
  return contentListFiltered;
};

export const getSetting = async () => new Promise(
  (resolve) => {
    if (mnp()) {
      mnp().getSetting({
        withSubscriptions: true,
        success(res) {
          const { itemSettings = {} } = res.subscriptionsSetting || {};
          resolve(itemSettings);
        },
      });
    } else {
      resolve({});
    }
  }
);

export const hasUnrefused = (contentList, tplIdsInSetting) => {
  const tmplIds = [];
  if (contentList) {
    contentList.forEach((element = {}) => {
      if (element.tplIdScene === 'subscribe' && element.tplId) {
        tmplIds.push(element.tplId);
      }
    });
  }
  if (tmplIds.length === 0) {
    return false;
  }
  let flag = true;// 是否全部被reject
  for (let i = 0; i < tmplIds.length; i++) {
    if (tplIdsInSetting[tmplIds[i]] !== 'reject') {
      flag = false;
      break;
    }
  }
  return !flag;
};

const templateType = new Map([
  // 微信小程序0MNP和1MNP
  ['MUtXq5d16lqDkNdw3dBypZ_vW4rySs0oj6fYO0tSkOQ', '2'],
  ['sHoB4-kVTiSBvMMiKIr-ye2eZBqeRjqsBj4LYyGjYDU', '2'],
  ['YlbNxCLFzIUFhOG3yjRGOYmXd-GAyYDzdxqKvKqoOas', '2'],
  ['NByxZ7zzRm5-ptR2FsvbsOkX5Wu4P-PhHbsXxyjxbbE', '2'],
  ['Lp60_0gm_aZspRMVbLXJ6v1VmZPp-KllGIBLeajRSF8', '2'],
  ['wE5RDXZR6kep4qI-SABjYUM4ih9p_zTQNwHi1zT6Qew', '2'],
  ['aoREWLsPaj0vaUDEJ2h3AxeLFcIZC_7lzkkVMORwSOg', '2'],
  ['nPl1Lfoo7o5Sf7VVNIFB4YnXGgcNSQutY_byEWIpLHw', '2'],
  ['Srqg58EGHY90WmY_XCpeEXGKmQrKYjkFomPWckuFkCo', '2'],
  ['q5s_USpt8-Bkhcqyz5nU0gXMQZ9gAAdlg6D2wOm6NxU', '2'],
  ['sPT3OfONFUlsg2yfTC8-hSQz39ES1u9-ObzAHo0-Es4', '2'],
  ['HMPbM7VbfDQFOqGn-dxA3DhNRvPQlLO2v-6U_EGDQP0', '2'],
  ['loMpJGDtebEYBQvYyyHwPJuFoSQuLdVDdqqPr5hfWXU', '2'],
  ['m6MXtR6bXU2vjtqioI78D7jJ9XeqRMZMIwKp-I_UIqE', '2'],
  ['W2krgRStaE216jWuFzKgvJQlNwaB9vMjfWT-IYALPgw', '2'],
  ['4uFyLLIt3YUAsqrBOK67ekv_ncT975k1p2ir6slc8s8', '2'],
  ['C3BJzPqbnxf_RHtHS0x-SA0KXUuwp33qjpixhYKMfgI', '3'],
  ['dL8jieTeDinmwuBvh1AFtHRIQZZCxPkk8ddQZvISg7I', '3'],
  ['zSW8M97hYXtwitCkpRbgQFG3cthxqd6_9ZZllrlyKV8', '3'],
  ['g8Nx8QLsfg6CYQAfyAaJ9I3Zck6GiaMWOphV7dsLbws', '3'],
  ['yNZmIxhdHZvc14zLj-tAaaqfkCVdRPxalFTOVmGTJb0', '2'],
  ['_DOvck3ZNR-m9z75SvDAiCVS-IaPJIphTgXd5BeW468', '2'],
  ['I5i2klRvInBtu8ONpOMMwB0rYuihFjwg8a2RrpUKoZY', '3'],
  ['Pg9coCStGvnvwz_76sHHg_nNMWqk7lwzPRBCjnc569s', '3'],
  ['cJ58u-otJo8T1pjwW6D6hAlxq2KKflh0fqFxpQLlhG8', '3'],
  ['aUrTy1UNN-YvLTib3-2xe9hPpnjJmJF8TxL5DiiFD5c', '3'],
  ['3vt0FnFYeqIS4pwHC71F-zHjdOkxWAH1Y3TR0tybpEE', '2'],
  ['Bs9aPIRJhMZyHqkmZ7AmSN7GoaoYmFxvAM079hfRkVU', '2'],
  ['VfM6Vlol-zDEssJ2piohXoPvun9oU-pniU3ALCuHabs', '2'],
  ['uQETorTTAtQ_wW6zR-ivsyZlxU19QDbZ_c83UYug6es', '2'],

  ['NUfsh7rOP9Sb55jLhYwJW5284TDCgb8tt_apCMKuCpk', '2'],
  ['yNZmIxhdHZvc14zLj-tAaaTc4Ue8e-Ih1Rk34TrWlXg', '2'],
  ['m6MXtR6bXU2vjtqioI78DxTil6Ca0QOFP41m_sITRto', '2'],

  ['6Tp_I-GcV_lraDAvWF8SOgz4sSkXHV8KHYZiGw9Iyls', '2'],
  ['_DOvck3ZNR-m9z75SvDAiMa2j_Z8jGdGobxblsNG8tM', '2'],
  ['uQETorTTAtQ_wW6zR-ivs3-xpjfwQ9b3Mw2E9GX03os', '2'],
  // 招联金融plus测试版 wx小程序
  ['kB5NZnQHlFBtg7qgDf-irkXxFEWZHbbXMOU-UoLHI8g', '2'],
  ['f06oHs0nj6hRVNyvIh7F48bgdpzGhRTJdXfr169cHMM', '2'],
  ['G5VEXDFkQNPBMqnmpg-Vj4rbhWSJcTHjseYG6j1g-nw', '3'],
  ['I-OH8tJ1gGQjWF50PS0srtXvkD2RnuqAWwMArSlIB74', '3'],
  ['uUCtDroDbNobdPb91HxdkE1dGTu9gey98XNwv14VGs4', '2'],
  ['2cEBK7q8JH3ApcRV0JesXOpf3J8ut1WNZ5QIuQdZ9tg', '2'],
  ['VgLsDBLBVuAxkuFfzaXMvm-vCCI5tDVIXZ8Vbgq91-Q', '2'],
  ['YiM7HO5gB_Tc01pQXax4D2p6cKG3exk6YTBkwB-BaHk', '2'],
  ['iIPZOKNQPHCSELl3RBz6TuFaCd8u87kOpeduGSEtpI0', '2'],
  ['b21X-PTNHo39ufUNKrjI-DWp_iJC4eJdQiCFWjQENhk', '2'],
  ['xfaR9r6F_hiaYJhaRYXxcn2bcFsl5o4-NL29aXSYhgg', '2'],
  ['ihZHyKXZRa48OuXErnkF4JbqJznt_KRuuD9J6BlW4TU', '2'],
  ['Q0cfJQWzBc5y5Eet6JSxAP1V4lbTcaVY0lsb_FYRMy4', '2'],
  ['onk90GqSxfLZWUdl8rV9kL-8gXzcEznM2QMyTxP6P_Q', '3'],
  ['4cCpI_f-t829L4N731bd-xRShbKctpR-NJjOr3Ylr1I', '3'],
  ['Hb2SryZXzNQq0OKNXPiJLl5jMiurcJyLabybgW2uuYs', '3'],
  ['PKNRb287iztJdy0ivg2gTlSHkuEgwhcLl9xCliGgjT4', '2'],
  ['GJ05B1OnQnjZC5M4RroK4pynYG1Wl89bMyVeKFxsxks', '3'],
  ['GJ05B1OnQnjZC5M4RroK4sfDWfKAEWe5I-jhkFIe85o', '3'],
  ['WXOOHQ4ZfUxmw1BgUY35mAAhC8akW7ASdwoDVJL206k', '2'],
  ['PKNRb287iztJdy0ivg2gTgkuBwHCaKat1whTOZkVzKA', '2'],
  // 招联消费plus测试版 wx小程序
  ['DizI2ntTqHe1S22BIGxm-drXPJCr2gZuW2n6GHBBVvM', '2'],
  ['Vssr4xh9hfXMYgbBdkFM-BrwdofB0eeck4AXCEEP8hI', '2'],
  ['AfmbqwjhMyFxwrS3iHZTOrVBuVMrNweV3tnhUHCeLmY', '2'],
  ['4Txuy8DWb_QIeSb_Q9WfDFzJUY3wrhYErknIwxPZ_JE', '2'],
  ['KPf1QNBPsmIQdbj70ZhYawbZzFgNfvJGjxiIEkVM1aA', '2'],
  ['RV88lj4TyQT1V5kr66YCGegAOcM2VcHfNHQNUt6eZi4', '2'],
  ['Uhx0FjFyd9GEJtBEcwOKlBhUwZcdQgKFQKJf_IKZrlA', '3'],
  ['qZ5otDci9NJe6x9nEXXqOPFX8vU2lwXPYVe1h-9YtNg', '2'],
  ['kfROEcfeyOVAQ9qpCx5MPGzqvv2G3z_9rSj1e-OmXgc', '2'],
  ['_0DSmwb8PJWqbs5_uWkPoQY5bUPa8BekdbABdJWvp9g', '2'],
  ['_xyicOnajSRzIvlP1o-YlYDDJFYWjY4MbRzBQVuD91o', '2'],
  ['ASeYH5dOt-TfoMnn4f7UM6rgEur8yNWPZaZ43ZjCehU', '3'],
  ['lRz4AhCl1q9qj3oB0QXl-4xsCKue6K4onBOewPlDhdc', '3'],
  ['lzeQmMylmw4gzdovA-gEk37DDY14089H9XG-7gkZcOY', '3'],
  ['JwxsGcd5RKIDCXIuV5tJtYtE-KypScCLxNB0daJu-fg', '2'],
  ['XIiKtg3tdZi4i_feDwFMujMBJ7EvXjphTAwbsFuZImI', '2'],
  ['B_jQmr42HTS9PJT70bMls7y__unZ6ViCPTLL2BieHGQ', '2'],
  ['BXedpb60ndkyX3uEIwusqeq4BYZgk-4mY33YOb-9yNI', '2'],
  ['ARIzS5ewNVqecDEE3PnFoNSktQrk1ZPqePKMGp1ZQec', '3'],
  ['8rNF4RuiVagDAJxYnUwCv_MxLKbaundGajpyKh6ZQPM', '3'],
  ['PCGJxbrPnOhMwzKRukT5TMJw0XF3QTCGQ4JPKiscS6E', '3'],
  ['WyuFmw5uWKG9BVWcyHU5odUQ7A9cU_LksgVfu4Pt7ZY', '3'],
  ['bWawseANtSqC1m9bgZ7u-Q3-BsA0Qj61ReR5ya73fVg', '2'],

  // 支付宝小程序 0ZFBMNPJD
  ['bdf579d158d74be8aa9bfcb6224a7d88', '2'],
  ['40fd9f1998e346ee9596d2b64fec1293', '2'],
  ['a4ce5219093549d59e467d2a8cb3dbeb', '3'],
  ['fa7fe728cfde4a4a83397d4e783c1427', '3'],
  ['f0548c1f59264981b9ee84763b05ee05', '3'],
  ['8d03d1b4df6b4fef8d088d4c4d85aa04', '2'],
  ['3778e86a58a9499b941cbc12693b9953', '3'],
  ['5be5be47b207402d92aa39cd5b3055e2', '2'],
  ['db3df6d63224469496312f221464608e', '3'],
  ['0efbe1941891409da7b6043e15a1d492', '3'],
  ['6e67156404a64fa0a5ccc87d6301f581', '2'],
  ['440caa03627f42c990436c833b24db7a', '3'],
  ['f9222cab98cf4d50a4d89f3c97aea68f', '3'],
  ['5b3dd601b5084ff8b67ade441337f14b', '3'],
  ['01a66ea287bc4177bfa213380bb54cc3', '3'],
  ['f72a71e83c994143b2e875e5dd956786', '3'],
  // 支付宝小程序 0JD1ZFBMNP
  ['834e4ee66f854c619a48e7e389ecbbf9', '2'],
  ['2ed4f4d88ee84e37974d62cae9ab1137', '2'],
  ['3e1308a04af64815b4fcda4b5ef8decb', '3'],
  ['6bf6b29a80ae42b38c0618598d1c289b', '3'],
  ['bb5458f6d0b84aac9bc6be7beb6085a9', '3'],
  ['a232e3713b414438bd34a498d7bed827', '2'],
  ['f6890243ad2840558d04a2407afdffae', '3'],
  ['8efb640145cf4d5983035266375faf2f', '3'],
  ['05800a4f180d4e97a053c8bf97edb9b3', '3'],
  ['d882141c766b4687ad146a5f303b3752', '3'],
  ['c0865bfbfb534b83bf4670c93ff6798c', '2'],
  ['7f9d687527ae4d97ad5dc1bbd7e80ac6', '3'],
  ['89785e8122e944ffa63ffc7c7d712570', '3'],
  ['f7053eec32fd4743b181853b1defdb24', '3'],
  ['b8ce98c653a64c1692e0f6fe9eb9e823', '3'],
  ['71021839aeb344c898cce0473f642773', '3'],
  // 支付宝小程序 0JD2ZFBMNP
  ['1e6575ccc6dc44edbaa9a635fa571f8e', '2'],
  ['f12cf2b0b71943f69f2a7ff01188609a', '2'],
  ['f57665b00ff04f43959edb5ad2c3b8ee', '3'],
  ['3af453f7098e46789cfb374d81138c8a', '3'],
  ['e6b72c0ae63f4ec2877f75d9d0ab2568', '3'],
  ['b4b6a6cca6374c8fa2867709925de53a', '2'],
  ['322910f32e544a67ba44b81d94491a22', '3'],
  ['73e83508d17f476bbc45664f24ce6e06', '3'],
  ['427d8b8108544638a23f96043e895eec', '3'],
  ['e04b472d4e3e4880a615a85c99954576', '3'],
  ['7ade88698ec4417ba1cb006cd4502e0a', '2'],
  ['496d47e16e0547b48cbd74dccd7336cb', '3'],
  ['5ac960f825204566950a01ce726eba76', '3'],
  ['78041c129c4c4cf394b0c6f835acac95', '3'],
  ['f6c91bc475eb43099a7ae54cdc874a67', '3'],
  ['4ae7a534f39145189fe4fe447bdb106f', '3'],
  // 支付宝小程序 0JD3ZFBMNP
  ['b24956d928034b72ada25bb89ce20fa6', '2'],
  ['d844d80f1e2f41d29292937e6f63913d', '2'],
  ['e9fc1f44a3f540b69853f7b49801cfe3', '3'],
  ['5abca52756b549b7b0b5352946bd1adb', '3'],
  ['64d07f50d5f64812be2cad878372fd32', '3'],
  ['bad4d30e9cc74ce5b88bd2c8eb8ca870', '2'],
  ['b9dc4acd9aed4e30bb40c98a2c7d1300', '3'],
  ['d20ea2dbba8e46ef94f907e5f4e1a6c2', '3'],
  ['5d9307e685ad473c838feaf4a06c9be6', '3'],
  ['ba6df4a2500a474cb41a7a12118047f0', '3'],
  ['44c3c364941d4ffa9cd0c7a4f8a1a05a', '2'],
  ['66ba782b554541e697fe1ab83d5b263f', '3'],
  ['9003fbbd20694fb6994544423d85f880', '3'],
  ['1c631449f31b41fbbfb608ef83f1deb6', '3'],
  ['69d7b3b0995043f0ae7688d6e7c9a68a', '3'],
  ['525916f6ee514101962f9a31f6869a32', '3'],
  // 支付宝小程序 0JD4ZFBMNP
  ['b4bef7e2bb0a48f787c440a5e43a3133', '2'],
  ['3ff652e6a2a940c1ae7877670a2d71ad', '2'],
  ['3380695f03c24df586e024a65c6968f0', '3'],
  ['81558d78debe43fd886872a05cdd0009', '3'],
  ['0fcc0d71852d4af1ace390429b61bdf8', '3'],
  ['74479b54a72e4be098b3e48d9b948508', '2'],
  ['74c0ef2299bb4229a9931bb07bdc96bd', '3'],
  ['c3cdec95afd54027aaf0343260dc1793', '3'],
  ['b3bd8b9e967f4abe9974f9bc32572506', '3'],
  ['e78cfea504b94cd9ab26c1704ab4e970', '3'],
  ['c1803c1682bf4689b460f204bfc67c3b', '2'],
  ['bcdd8ebbcf28488185b372d8efed3a37', '3'],
  ['615a207f843a4f2896b37e9344c02678', '3'],
  ['0ebe55499e8e47d4b00c1744e9e05073', '3'],
  ['0f7243bd62494b5e984c384a73f1315c', '3'],
  ['1098fa330f4f43949943d07b2259cd24', '3'],
  // 支付宝小程序 0JD5ZFBMNP
  ['d912df389bd342519d5b78d84e87305c', '2'],
  ['72dfc7365569432aa092ccd02b63f2bc', '2'],
  ['7d58da52d83c4c0088d701d5eaea052d', '3'],
  ['f5d1dd0ae2894e7194ec8a3a16261412', '3'],
  ['b1907a122d7a425a9579b37279de02c1', '3'],
  ['0f5b584d427b43e589f9eeab204bd7ee', '2'],
  ['e1ec598ded0e4b23b151ccaef9b8f121', '3'],
  ['0e542cd2fd0f40378654fb27ab2b051a', '3'],
  ['e1b7f2c0aafa45c684f9d5cbc54ac710', '3'],
  ['c9e827f1c0de4786813a57290f4dc481', '3'],
  ['8aec119c7bb14b1581b48ab53bc2871c', '2'],
  ['c59eec054b174446932ace166ec0c417', '3'],
  ['a5529e8e15d74e04b214af22c283f5b7', '3'],
  // 支付宝小程序 0JD6ZFBMNP
  ['201df54e09a1440ab83e2f73dfeb94f2', '2'],
  ['88b08d7496954dd6a4966d324fe61ce3', '2'],
  ['2c32fa6ded91443aaf09ec7b3f62836a', '3'],
  ['6bbef1f7760a45de9a9fd3dec84df74c', '3'],
  ['f7af5f88298649b890a5f18d74ac9990', '3'],
  ['fab4b89e0eca4968a306a12f59addbdc', '2'],
  ['4628e526f54748059178e2c13e6938c7', '3'],
  ['0916165956f04775b0a72ae2afb2a29c', '3'],
  ['a2d807286d4f4bc3a047b40719e0f70c', '3'],
  ['62da3d86a8224c78b0ddc781cf9b31c7', '3'],
  ['412ff7ed426942a5bdf214d9ef156012', '2'],
  ['0cfa20ad64354676b10d497b16143053', '3'],
  ['5c3fc6f0f6ff4be596bcd146d7fdb529', '3'],
  // 支付宝小程序 0JD7ZFBMNP
  ['d05b8db3a27640b4b777a7b1271098b7', '2'],
  ['e108ae5bef9c412da62b8394439c7255', '2'],
  ['7de9d351ab8b431984930fcfc78e197b', '3'],
  ['612d24bdb26c4fea9bb2a6198378fb3b', '3'],
  ['1828b873fce24f059e1c36305a665825', '3'],
  ['1050686b44894bb6af1cdeb3ec4ad60c', '2'],
  ['0cfa5c20a31d4441bc77ce6aab408194', '3'],
  ['df4b0cbba73248aaa4ac6e9b768aa0ed', '3'],
  ['4317b28c5c6143ada2e6dba13a55b2e6', '3'],
  ['083ac05429274c938bc3384f50ed11be', '3'],
  ['ebc5e8879c974ec39c971ac8273b81cf', '2'],
  ['94f5d19627bc4840abf124ff98c20233', '3'],
  ['0ced014fbb89402c9a438e770dc577bd', '3'],
  // 支付宝小程序 0JD8ZFBMNP
  ['9bd6b4fa38dd4dbdaf7b79de2d76af7b', '2'],
  ['2082158758424bd0879998b2aa440839', '2'],
  ['3ffb4ff61a2e4ca38f07529297a741f3', '3'],
  ['e9a6c202a94843c889b5307e0f8aff1a', '3'],
  ['f155f9289a5f49d8a64f5a0e89a20469', '3'],
  ['5cc099d065194f619f1d63515b7d6c25', '2'],
  ['a6baaf65d95d41f48afdfb5629d66a2d', '3'],
  ['9992e70a2fb84cec91dd4f8de2c05edf', '3'],
  ['cc65c66b5a934604b3e9e8893a7863de', '3'],
  ['e966cf659cbe49ca920e3e157852a022', '3'],
  ['7e2073b153754f70ac14813964d83167', '2'],
  ['63a8c5ff322145ffa9a03f4d80c19bcb', '3'],
  ['2a6c161df2de47afbaac53b0cf59a08f', '3'],
]);

export const getTemplateType = (id) => templateType.get(id);
