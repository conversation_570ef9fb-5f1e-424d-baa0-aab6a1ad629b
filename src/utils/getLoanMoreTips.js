export function getIsYyjOrYjkLoan(selectedBillList = []) {
  let isYyjOrYkjFlag = false;
  if (selectedBillList && selectedBillList.length <= 0) {
    return isYyjOrYkjFlag
  }
  selectedBillList.forEach(item => {
    const { specialFlags = [] } = item || {}
    // 6为一口借 7为一元借
    if (specialFlags && (specialFlags.indexOf('6') > -1 || specialFlags.indexOf('7') > -1)) {
      isYyjOrYkjFlag = true
      return isYyjOrYkjFlag
    }
  })

  return isYyjOrYkjFlag
}

// 是否可借款、有借款优惠判断
export function getAvailLoanFlag(accountInfoList, awardInfoList) {
  let availLoanFlag = false;
  let availLoanWaiveFlag = false;
  // 过滤账户数据，01账户正常，里面的现金D01额度可用额度大于0为可借
  const [formatAccountInfoList = {}] = (accountInfoList || []).filter(item => item.acctBusiType === '01');
  const [limitInfo = {}] = (formatAccountInfoList.limitInfoList || []).filter((o) => o.limitType === 'D01' && o.busiType === 'XJ');
  if (formatAccountInfoList.acctStatus === '1'
    && (formatAccountInfoList.controlCode === '0000' || formatAccountInfoList.controlCode === 'C206' || formatAccountInfoList.controlCode === 'C207')
    && limitInfo.status === 'Y' && +limitInfo.availLimit >= 100) {
    availLoanFlag = true;
    if ((awardInfoList || []).filter((item) => (
      item.awardType === '11'
      || item.awardType === '21'
      || item.awardType === '22'
      || item.awardType === '31'
      || item.awardType === '32'
      || item.awardType === '33'
      || item.awardType === '34'
    )).length) {
      availLoanWaiveFlag = true;
    }
  }
  return { availLoanFlag, availLoanWaiveFlag };
}
