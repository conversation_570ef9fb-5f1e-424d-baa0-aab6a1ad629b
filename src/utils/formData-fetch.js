import Madp from '@mu/madp';
import {
  getCurrentPageUrlWithArgs
} from '@mu/madp-utils';
import { getToken } from '@mu/dev-finger';
import { apiHost } from '@utils/constants';
// import Util from '@utils/maxin-util';
import { platLogin } from './login';

// TODO 登录逻辑
function doLogin(options) {
  return platLogin(options);
}

let isToast = false;
const duration = 2000;

const rejectFunc = async (res, options) => {
  const { status } = res;
  const { autoToast = true } = options;
  const FALLBACK_TEXT = '系统繁忙，请稍后再试';
  if (status === 401) {
    doLogin(options);
    return Promise.reject('未登录');
  } else if (status === 200) { // 业务错误
    // 触发全局事件，页面中订阅该事件可用于更新界面
    Madp.eventCenter.trigger('madp_biz_error_event', res.errMsg || FALLBACK_TEXT);
    if (autoToast) {
      isToast = true;
      Madp.showToast({
        title: res.errMsg || FALLBACK_TEXT,
        icon: 'none',
        duration
      });
      setTimeout(() => {
        isToast = false;
      }, duration);
    }
    return Promise.reject(res || FALLBACK_TEXT);
  } else if (status >= 300) {
    isToast = true;
    // 网络异常，status:${status}
    Madp.showToast({
      title: '网络异常，请稍后再试',
      icon: 'none',
      duration
    });
    setTimeout(() => {
      isToast = false;
    }, duration);
    return Promise.reject(`网络异常: ${status}`);
  }
  return Promise.reject(`网络异常: ${status}`);
};

const fetch = async (operationId, options) => {
  const pageUrl = getCurrentPageUrlWithArgs();

  // 设备指纹引入,接口签名已经默认在 Madp.request集成，无需在环境参数再做处理
  const token = await getToken(true, Madp.getChannel());

  // 业务mapCode获取，详情查看app.js 的 eventCenter 相关代码
  const mapCode = Madp.getStorageSync(
    'mapCode',
    'SESSION',
  ) || '';

  const { EnvParams = {} } = options;

  const reqEnvParams = {
    channel: Madp.getChannel(),
    appType: 'H5',
    pageUrl,
    token,
    mapCode,
    module: 'repayment',
    ...EnvParams
  };

  const { data } = options;

  data.reqEnvParams = JSON.stringify(reqEnvParams);

  const requestOptions = {
    url: `${apiHost.mgp}?operationId=${operationId}`,
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  };

  const { autoLoading = true } = options;
  if (autoLoading && !isToast) {
    Madp.showLoading({ title: '加载中' });
  }

  return Madp.request(requestOptions)
    .then(async (res) => {
      Madp.hideLoading();
      const { status } = res;
      if (status === 200) {
        const json = await res.json();
        if (json.ret === '0') {
          return Promise.resolve(json.data);
        }
        return Promise.reject({
          status: res.status,
          ...json,
        });
      }
      return Promise.reject(res.clone());
    })
    .catch((res) => {
      Madp.hideLoading();
      return rejectFunc(res, { ...requestOptions });
    });
};

export default fetch;
