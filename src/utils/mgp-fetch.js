import fetch from '@utils/fetch.js';
import { apiHost } from '@utils/constants';

export default async (operationId, options = {}, ignoreRet) => {
  const {
    data, autoToast = true, autoLoading = true, fileParam,
  } = options;
  let { method } = options;
  method = (method || 'POST').toUpperCase();
  const response = await fetch(apiHost.mgp, {
    body: JSON.stringify(
      {
        variables: {
          operationId,
          data,
          method
        },
        ignoreCache: true,
      }
    ),
    autoLoading,
    autoToast,
    fileParam,
  }, ignoreRet);

  const json = response ? await response.json() : {};
  const ret = ignoreRet ? json : json.data;
  return ret;
};
