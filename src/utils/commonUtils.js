// 转换渠道参数回调回来的数据格式

export const transChannelParams = (channels, viewParams, name, typeObj) => {
  if (!channels) return;
  const newChannels = [];
  channels.forEach((channel, index) => {
    newChannels.push({
      catalogueCode: channel.catalogueCode,
      list: [],
      contractList: [],
      checkBoxChecked: false,
      contracts: [],
      showContract: false,
      readDuration: 5,
      contractText: ''
    });

    console.log(viewParams, 'viewParams');
    channel.channelCatalogueAgreementDtoList.forEach((item) => {
      if (item.forceReadFlag === 'Y' && typeObj && typeObj.contractType === item.contractType) {
        newChannels[index].list.push({
          title: item.custShowName || name,
          params: {
            ...item,
            ...viewParams
          }
        });
        newChannels[index].readDuration = item.readDuration;
        // newChannels[index].contracts.push({
        //   id: key + 1,
        //   text: item.custShowName
        // });
      }
      // newChannels[index].contractList.push(item.custShowName);
    });
    // newChannels[index].contractText = newChannels[index].contractList.join('、');
  });
  console.log(newChannels, 'newChannels');
  return newChannels;
};


// 获取链接所有参数

export const getUrlAllParams = (URL) => {
  if (!URL) return;
  // const url = location.search; // 项目中可直接通过search方法获取url中"?"符后的字串
  const url = URL.split('?')[1];
  const obj = {}; // 声明参数对象
  const arr = url.split('&'); // 以&符号分割为数组
  for (let i = 0; i < arr.length;) {
    const arrNew = arr[i].split('='); // 以"="分割为数组
    obj[arrNew[0]] = decodeURIComponent(arrNew[1]);
    i += 1;
  }
  return obj;
};
