import Madp from '@mu/madp';
import Dispatch from '@api/actions';
import { getCurrentPageUrlWithArgs, Url } from '@mu/madp-utils';
import { urlDomain } from '@utils/url_config';
import Util from '@utils/maxin-util';
import { miniProgramChannel } from '@utils/constants';
import Taro from '@tarojs/taro';

class ContactApi {
  constructor(props) {
    ({ scene: this.scene, params: this.params } = props);
    this.supplyContactSuccess = Url.getParam('supplyContactSuccess');
  }

  static of(scene, params) {
    return new ContactApi({ scene, params });
  }

  queryContact = async () => {
    const { scene } = this;
    let [familyContactInfoStatus, otherContactInfoStatus] = ['1', '1'];
    try {
      ({
        queryStatusInfo: {
          familyContactInfoStatus = '1', otherContactInfoStatus = '1',
        } = {},
      } = await Dispatch.repayment.getUserInfo({
        sceneFuction: 'QUERY_STATUS',
        componentTypeList: ['CP_TYPE_CONTACT'],
        scene,
      }) || {});
    } catch (e) {}
    [this.familyContactInfoStatus, this.otherContactInfoStatus] = [familyContactInfoStatus, otherContactInfoStatus];
    return familyContactInfoStatus === '0' && otherContactInfoStatus === '0';
  }

  check = async (callback) => {
    const { supplyContactSuccess, queryContact, supply } = this;
    if (`${supplyContactSuccess}` === '1' || await queryContact()) {
      await callback();
    } else {
      supply();
    }
    return this;
  }

  supply = () => {
    const {
      scene, params,
      familyContactInfoStatus = '1', otherContactInfoStatus = '1',
    } = this;
    const supplyContactUrl = process.env.TARO_ENV === 'h5'
      ? `${urlDomain}/${Madp.getChannel()}/usercenter/#/pages/add-personalinfo/contactPage`
      : '/usercenter/pages/add-personalinfo/contactPage';
    let redirectUrl = getCurrentPageUrlWithArgs();
    redirectUrl = process.env.TARO_ENV !== 'h5' ? `/${redirectUrl}` : redirectUrl;
    redirectUrl = Url.addParam(redirectUrl, params || {});
    if (miniProgramChannel.indexOf(Madp.getChannel()) > -1 && (scene === 'SCENE_SXF' || scene === 'SCENE_DELAY_PAY')) {
      if (scene === 'SCENE_DELAY_PAY') {
        Madp.setStorageSync('EXTEND_BACK', 'Y', 'SESSION');
      } else {
        Madp.setStorageSync('SXF_BACK', 'Y', 'SESSION');
      }
      Taro.redirectTo({
        url: `${supplyContactUrl}?familyContactInfoStatus=${familyContactInfoStatus}&otherContactInfoStatus=${otherContactInfoStatus}&redirectUrl=${encodeURIComponent(redirectUrl)}&scene=${scene}`
      });
    } else {
      Util.router.replace({
        path: supplyContactUrl,
        query: {
          familyContactInfoStatus,
          otherContactInfoStatus,
          redirectUrl,
          scene,
        },
      });
    }
    return this;
  }
}

export default ContactApi;
