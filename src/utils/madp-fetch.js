import Madp from '@mu/madp';
import { getToken } from '@mu/dev-finger';
import { isMuapp } from '@mu/madp-utils';
// import muFetch from '@mucfc.com/services/fetch'; // todo 小程序原生
import { platLogin } from './login';
import { handleMuNotice } from './mu-notice';

// 非新网关请求方方法

// TODO 登录逻辑
function doLogin(options) {
  return platLogin(options);
}

const fetch = async (url, options = {}) => {
  const requestOptions = {};
  const {
    autoLoading = true,
    autoToast = true,
    method = 'GET',
    data = {},
  } = options;

  const rejectFunc = (res) => {
    const FALLBACK_TEXT = '系统繁忙，请稍后再试';
    const { status } = res;
    Madp.hideLoading();
    if (status === 401 || res === 401) {
      doLogin();
      return Promise.reject('未登录');
    }
    // eslint-disable-next-line no-unused-expressions
    autoToast && Madp.showToast({
      title: res.errMsg || FALLBACK_TEXT,
      icon: 'none',
      duration: 2000
    });
    return Promise.reject(res || FALLBACK_TEXT);
  };
  if (autoLoading) {
    Madp.showLoading({
      title: '加载中'
    });
  }
  // 设备指纹引入,接口签名已经默认在 Madp.request集成，无需在环境参数再做处理
  const token = await getToken();
  if (isMuapp()) {
    requestOptions.url = url;
  } else {
    requestOptions.url = `${url}?_t=${token}`;
  }
  requestOptions.method = method;
  const params = {
    ...data
  };
  const tempEnvParams = { ...options.EnvParams, channel: Madp.getChannel(), appType: 'H5' };
  params.EnvParams = JSON.stringify(tempEnvParams);
  requestOptions.data = params;
  requestOptions.useMunaAjax = true;

  // 临时用 @mucfc.com/service 的 fetch 处理 form 表单里包含多层嵌套数据的 api 接口的请求
  // if (options.useMuFetch) {
  //   requestOptions.body = params;
  //   if(rocess.env.TARO_ENV === 'h5'){
  //     return muFetch(requestOptions.url, requestOptions)
  //     .then((res) => {
  //       console.log(66, res);
  //       Madp.hideLoading();
  //       return Promise.resolve(res);
  //     }, (res) => rejectFunc(res));
  //   } else {
  //     // todo
  //   }
  // }

  return Madp.request(requestOptions)
    .then(handleMuNotice(async (res) => {
      Madp.hideLoading();
      const { status } = res;
      if (status === 200) {
        const json = await res.json();
        if (json.code === 200 || json.ret === '0') { // 加入ret判断和data是为了兼容rap的mock数据结构
          return Promise.resolve(json.datas || json.data);
        }
        // 抛出业务错误信息
        const retData = json.datas || json;
        return Promise.reject({
          status: res.status,
          ...retData,
        });
      }
      return Promise.reject({
        status: res.status,
      });
    }))
    .catch((res) => rejectFunc(res));
};

export default fetch;
