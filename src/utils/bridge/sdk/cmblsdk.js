/* eslint-disable */
! function (e, a) {
    "object" == typeof exports && "object" == typeof module ? module.exports = a() : "function" == typeof define &&
        define.amd ? define("cmblapi", [], a) : "object" == typeof exports ? exports.cmblapi = a() : e.cmblapi = a()
}(window, function () {
    return function (e) {
        var a = {};

        function t(n) {
            if (a[n]) return a[n].exports;
            var i = a[n] = {
                i: n,
                l: !1,
                exports: {}
            };
            return e[n].call(i.exports, i, i.exports, t), i.l = !0, i.exports
        }
        return t.m = e, t.c = a, t.d = function (e, a, n) {
            t.o(e, a) || Object.defineProperty(e, a, {
                enumerable: !0,
                get: n
            })
        }, t.r = function (e) {
            "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
                value: "Module"
            }), Object.defineProperty(e, "__esModule", {
                value: !0
            })
        }, t.t = function (e, a) {
            if (1 & a && (e = t(e)), 8 & a) return e;
            if (4 & a && "object" == typeof e && e && e.__esModule) return e;
            var n = Object.create(null);
            if (t.r(n), Object.defineProperty(n, "default", {
                    enumerable: !0,
                    value: e
                }), 2 & a && "string" != typeof e)
                for (var i in e) t.d(n, i, function (a) {
                    return e[a]
                }.bind(null, i));
            return n
        }, t.n = function (e) {
            var a = e && e.__esModule ? function () {
                return e.default
            } : function () {
                return e
            };
            return t.d(a, "a", a), a
        }, t.o = function (e, a) {
            return Object.prototype.hasOwnProperty.call(e, a)
        }, t.p = "", t(t.s = 0)
    }([function (e, a, t) {
        var n, i, l;
        i = [a], void 0 === (l = "function" == typeof (n = function (t) {
            "use strict";

            function n(e, a) {
                for (var t = 0; t < a.length; t++) {
                    var n = a[t];
                    n.enumerable = n.enumerable || !1, n.configurable = !0, "value" in n &&
                        (n.writable = !0), Object.defineProperty(e, n.key, n)
                }
            }
            Object.defineProperty(t, "__esModule", {
                value: !0
            }), t.default = void 0;
            var i = function () {
                function e() {
                    ! function (e, a) {
                        if (!(e instanceof a)) throw new TypeError(
                            "Cannot call a class as a function")
                    }(this, e), this.callbackFuncs = {}
                }
                return a = e, (t = [{
                    key: "version",
                    value: function () {
                        return {
                            version: "7.4.0",
                            buildTime: "20190614165114"
                        }
                    }
                }, {
                    key: "cmblapiCall",
                    value: function (e, a) {
                        try {
                            a = a || {};
                            var t = (new Date).getTime().toString() +
                                Math.round(1e4 * Math.random()).toString();
                            if (null == t || "" === t) return;
                            for (var n in a)
                                if ("function" == typeof a[n]) {
                                    var i = t + n;
                                    this.callbackFuncs[i] = a[n]
                                } var l = JSON.stringify(a);
                            if (null == l || "" === l) return;
                            window.cmblapiBridge ? window.cmblapiBridge
                                .invoke(t, e, l) : document.addEventListener(
                                    "cmblapiJSBridgeReady",
                                    function () {
                                        window.cmblapiBridge.invoke(t,
                                            e, l)
                                    }, !1)
                        } catch (e) {
                            console.log(e)
                        }
                    }
                }, {
                    key: "callback",
                    value: function (e, a, t) {
                        try {
                            if (null == e || "" === e) return;
                            if (null == a || "" === a) return;
                            var n = this.callbackFuncs[e + a];
                            "function" == typeof n && (null == t || "" ===
                                t ? n() : n(JSON.parse(t)))
                        } catch (e) {
                            console.log(e)
                        }
                    }
                }, {
                    key: "checkJsApi",
                    value: function (e) {
                        this.cmblapiCall("checkJsApi", e)
                    }
                }, {
                    key: "getSystemInfo",
                    value: function (e) {
                        this.cmblapiCall("getSystemInfo", e)
                    }
                }, {
                    key: "requestUserPermission",
                    value: function (e) {
                        this.cmblapiCall("requestUserPermission", e)
                    }
                }, {
                    key: "config",
                    value: function (e) {
                        this.cmblapiCall("config", e)
                    }
                }, {
                    key: "getLocation",
                    value: function (e) {
                        this.cmblapiCall("getLocation", e)
                    }
                }, {
                    key: "showNavigationBar",
                    value: function (e) {
                        this.cmblapiCall("showNavigationBar", e)
                    }
                }, {
                    key: "hideNavigationBar",
                    value: function (e) {
                        this.cmblapiCall("hideNavigationBar", e)
                    }
                }, {
                    key: "setNavigationBarStyle",
                    value: function (e) {
                        this.cmblapiCall("setNavigationBarStyle", e)
                    }
                }, {
                    key: "setNavigationBarTitle",
                    value: function (e) {
                        this.cmblapiCall("setNavigationBarTitle", e)
                    }
                }, {
                    key: "setLeftNavigationBar",
                    value: function (e) {
                        this.cmblapiCall("setLeftNavigationBar", e)
                    }
                }, {
                    key: "setRightNavigationBar",
                    value: function (e) {
                        this.cmblapiCall("setRightNavigationBar", e)
                    }
                }, {
                    key: "pushWindow",
                    value: function (e) {
                        this.cmblapiCall("pushWindow", e)
                    }
                }, {
                    key: "popWindow",
                    value: function (e) {
                        this.cmblapiCall("popWindow", e)
                    }
                }, {
                    key: "setWindowProperties",
                    value: function (e) {
                        this.cmblapiCall("setWindowProperties", e)
                    }
                }, {
                    key: "setStatusBarStyle",
                    value: function (e) {
                        this.cmblapiCall("setStatusBarStyle", e)
                    }
                }, {
                    key: "shareInfo",
                    value: function (e) {
                        this.cmblapiCall("shareInfo", e)
                    }
                }, {
                    key: "shareInfoWithUI",
                    value: function (e) {
                        this.cmblapiCall("shareInfoWithUI", e)
                    }
                }, {
                    key: "createShortcut",
                    value: function (e) {
                        this.cmblapiCall("createShortcut", e)
                    }
                }, {
                    key: "makePhoneCall",
                    value: function (e) {
                        this.cmblapiCall("makePhoneCall", e)
                    }
                }, {
                    key: "watchShake",
                    value: function (e) {
                        this.cmblapiCall("watchShake", e)
                    }
                }, {
                    key: "queryContact",
                    value: function (e) {
                        this.cmblapiCall("queryContact", e)
                    }
                }, {
                    key: "callAndroidNFC",
                    value: function (e) {
                        this.cmblapiCall("callAndroidNFC", e)
                    }
                }, {
                    key: "callBluetoothFunc",
                    value: function (e) {
                        this.cmblapiCall("callBluetoothFunc", e)
                    }
                }, {
                    key: "callZhaoHuFunc",
                    value: function (e) {
                        this.cmblapiCall("callZhaoHuFunc", e)
                    }
                }, {
                    key: "makeBarcodeImage",
                    value: function (e) {
                        this.cmblapiCall("makeBarcodeImage", e)
                    }
                }, {
                    key: "scanBarcode",
                    value: function (e) {
                        this.cmblapiCall("scanBarcode", e)
                    }
                }, {
                    key: "scanIDCard",
                    value: function (e) {
                        this.cmblapiCall("scanIDCard", e)
                    }
                }, {
                    key: "scanBankCard",
                    value: function (e) {
                        this.cmblapiCall("scanBankCard", e)
                    }
                }, {
                    key: "callOtherApp",
                    value: function (e) {
                        this.cmblapiCall("callOtherApp", e)
                    }
                }, {
                    key: "jumpSetting",
                    value: function (e) {
                        this.cmblapiCall("jumpSetting", e)
                    }
                }, {
                    key: "appStorage",
                    value: function (e) {
                        this.cmblapiCall("appStorage", e)
                    }
                }, {
                    key: "chooseImage",
                    value: function (e) {
                        this.cmblapiCall("chooseImage", e)
                    }
                }, {
                    key: "saveImageToAlbum",
                    value: function (e) {
                        this.cmblapiCall("saveImageToAlbum", e)
                    }
                }, {
                    key: "snapshotPage",
                    value: function (e) {
                        this.cmblapiCall("snapshotPage", e)
                    }
                }, {
                    key: "addAppEventListener",
                    value: function (e) {
                        this.cmblapiCall("addAppEventListener", e)
                    }
                }, {
                    key: "merchantLogin",
                    value: function (e) {
                        this.cmblapiCall("merchantLogin", e)
                    }
                }, {
                    key: "setBrightness",
                    value: function (e) {
                        this.cmblapiCall("setBrightness", e)
                    }
                }, {
                    key: "applet",
                    value: function (e) {
                        this.cmblapiCall("applet", e)
                    }
                }, {
                    key: "merchantOpenAPI",
                    value: function (e) {
                        this.cmblapiCall("merchantOpenAPI", e)
                    }
                }, {
                    key: "footprint",
                    value: function (e) {
                        this.cmblapiCall("footprint", e)
                    }
                }]) && n(a.prototype, t), i && n(a, i), e;
                var a, t, i
            }();
            void 0 === window.cmblapi && (window.cmblapi = new i);
            var l = window.cmblapi;
            t.default = l, e.exports = a.default
        }) ? n.apply(a, i) : n) || (e.exports = l)
    }])
});