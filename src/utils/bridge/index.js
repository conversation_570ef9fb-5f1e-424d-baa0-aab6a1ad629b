/* eslint-disable no-restricted-syntax */
/* eslint-disable no-restricted-globals */
/* eslint-disable no-unused-expressions */
import './sdk/cmblsdk';

export function getMPBankVersion() {
  const version = navigator.userAgent.toLowerCase().match(/MPBank\/([\d.]+)/i);
  return (version && version[1]) || '';
}

export function MPBankVersionCompare(version) {
  const v1 = getMPBankVersion().split('.');
  if (v1.length < 3) {
    return -1;
  }
  const v2 = version.split('.');
  for (let i = 0; i < 3; i += 1) {
    if (v1[i] - v2[i] < 0) return -1;
    if (v1[i] - v2[i] > 0) return 1;
  }
  return 0;
}

export function setNavigationBarUI(funcName, options = {}) {
  if (MPBankVersionCompare('7.4.0') >= 0 || window.cmblapi) {
    window.cmblapi[funcName](options);
  }
}

export default {
  getMPBankVersion,
  MPBankVersionCompare,
  setNavigationBarUI,
};
