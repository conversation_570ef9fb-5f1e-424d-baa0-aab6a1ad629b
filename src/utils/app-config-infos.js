/* eslint-disable quotes */
/* eslint-disable quote-props */
const AppInfos = {
  "sbd": {
    "APP": "sbd",
    "userCenter": {
      "copywriting2": "用户中心文案2"
    },
    "logoStaicUrl": "https://file-st1.cfcmu.cn/cop/28/30/202108/202108261418554848f0.png",
    "apply": {
      "copywriting1": "申请模块文案1",
      "isHideBankcardEntry": true,
      "isHideIdentityEntry": true,
      "isHideIncrementEntry": true
    },
    "prodStaticDomain": ".stech.muftc.com",
    "name": "首帮贷",
    "fullName": "深圳市首帮科技有限公司",
    "shortName": "首帮科技",
    "stStaticDomain": ".stech.muftc.com",
    "repayment": {
      "isHideWxZfbRepayWay": true
    }
  },
  "mucfc": {
    "APP": "mucfc",
    "userCenter": {
      "copywriting2": "用户中心文案2"
    },
    "logoStaicUrl": "https://file-st1.cfcmu.cn/cop/28/30/202108/202108261418554848f0.png",
    "apply": {
      "copywriting1": "申请模块文案1",
      "isHideBankcardEntry": false,
      "isHideIdentityEntry": false,
      "isHideIncrementEntry": false
    },
    "prodStaticDomain": ".mucfc.com",
    "name": "招联",
    "fullName": "招联消费金融有限公司",
    "shortName": "招联金融",
    "stStaticDomain": ".cfcmu.cn",
    "repayment": {
      "isHideWxZfbRepayWay": false
    }
  }
};

const env = process.env.APP || 'mucfc';

export default AppInfos[env];
