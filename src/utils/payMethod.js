import Util from '@utils/maxin-util';
import {
  errCodeMap
} from '@utils/constants';

export const transformErrCode = (errCode) => {
  let mappedCode = errCode;
  Object.keys(errCodeMap).forEach((i) => {
    if (errCodeMap[i].indexOf(errCode) > -1) {
      mappedCode = i;
    }
  });
  return mappedCode;
};

export const accSub = (num1, num2) => {
  let r1;
  let r2;
  try {
    r1 = num1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = num2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  // eslint-disable-next-line no-restricted-properties
  const m = Math.pow(10, Math.max(r1, r2));
  const n = (r1 >= r2) ? r1 : r2;
  return (Math.round(num1 * m - num2 * m) / m).toFixed(n);
};

/**
 * @description: 用于展示的超限金额，当用户修改金额且命中超限区间时使用。
 */
export const getDisplaySettleWaiveAmt = (userSetAmt, totalCanWaiveAmt, totalCanPayAmt) => {
  const afterWaiveUserSetAmt = Util.floatMinus(userSetAmt, totalCanWaiveAmt);
  const displaySettleWaiveAmt = Util.floatMinus(afterWaiveUserSetAmt, totalCanPayAmt);
  return +displaySettleWaiveAmt > 0 ? displaySettleWaiveAmt : 0;
};
