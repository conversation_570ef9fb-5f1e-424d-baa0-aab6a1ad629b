import Madp from '@mu/madp';

let status = 'default';

const extractTpl = /<body.*?>([\s\S]*)<\/body>/;

function initMuNotice() {
  if (status === 'ready') return;

  const muNoticeStyle = document.createElement('style');
  muNoticeStyle.textContent = '.mu-notice-page{position:fixed;z-index:1001;top:0;left:0;bottom:0;width:100%;background:#eee;font-size:14px}.mu-notice-page strong{color:#488FF0;font-weight:normal}.mu-notice-page h4{padding:0;margin:15px auto;color:#333;font-weight:normal;font-size:17px;text-align:center}.mu-notice-page p{margin:0;padding:0;text-align:justify;margin-bottom:50px}.mu-notice-page .icon.icon-warning,.mu-notice-page .icon.icon-loading{background:url(https://m-zl.mucfc.com/static/serverImgs/icon_arning_x3.png) center center no-repeat;display:inline-block;margin:50px auto 0;background-size:contain;width:100%;height:120px}.mu-notice-page .icon.icon-loading{background-image:url(../img/<EMAIL>)}.mu-notice-page .notice,.mu-notice-page .btn-row{padding:0 15px}.mu-notice-page .notice .btn,.mu-notice-page .btn-row .btn{border-width:0;outline:0;-webkit-appearance:none;background-color:#488FF0;color:#FFFFFF;width:70%;height:44px;line-height:44px;font-size:17px;text-align:center;text-decoration:none;margin:0 auto;display:block;border-radius:2px}.mu-notice-page .notice .btn:focus,.mu-notice-page .btn-row .btn:focus{outline:0}';

  document.querySelector('head').appendChild(muNoticeStyle);

  status = 'ready';
}

function bindCloseEvent() {
  setTimeout(() => {
    document.querySelector('.button-close-webview').addEventListener('click', (e) => {
      e.preventDefault();

      // 关闭操作
      Madp.closeWebView();
    });
  });
}

function showNoticePage(htmlTpl) {
  initMuNotice();

  const isNoticeExist = document.querySelector('.mu-notice-page');

  if (!isNoticeExist) {
    const noticePageEl = document.createElement('div');

    noticePageEl.className = 'mu-notice-page';
    noticePageEl.innerHTML = htmlTpl;

    document.body.appendChild(noticePageEl);

    bindCloseEvent();
  }
}

/**
 * 处理老接口挂牌
 */
export function handleMuNotice(callback) {
  return async function handleFunc(res) {
    if (res.status === 200 && typeof res.json === 'function') {
      const json = await res.json();

      if (json.ret === '1' && json.tips && json.tips.type === 'noticePage') {
        const tpl = extractTpl.exec(json.tips.content || '')[1];
        if (process.env.TARO_ENV === 'h5') {
          showNoticePage(tpl);
        }
        return Promise.reject(res.clone());
      }
    }

    return typeof callback === 'function' && callback(res.clone());
  };
}
