import Madp from '@mu/madp';
import { getCurrentPageUrlWithArgs } from '@mu/madp-utils';
import { login } from '@mu/business-basic';

export async function platLogin(options) {
  if (process.env.TARO_ENV === 'h5' && Madp.getChannel() === '0ZFBMNPJD') {
    let totastTimer = setTimeout(() => { // 写个延时防止有其他loading弹窗展示不出来
      Madp.showToast({
        title: '您当前未登录，当前页面即将关闭，请重新进入',
        duration: 5000,
        mask: true,
        icon: 'none'
      });
      if (totastTimer) {
        clearTimeout(totastTimer);
        totastTimer = null;
      }
    }, 1000);

    let timer = setTimeout(() => {
      // eslint-disable-next-line no-undef
      my.navigateBack({ delta: 9 });
      if (timer) {
        clearTimeout(timer);
        timer = null;
      }
    }, 5000);
  } else if (process.env.TARO_ENV === 'h5') {
    const { doLoginH5 } = require('./h5-login');
    return doLoginH5(options);
  } else {
    const { redirectUrl } = options || {};
    const currentUrl = getCurrentPageUrlWithArgs();
    const fromUrl = currentUrl ? `/${currentUrl}` : '';
    const redirectUrlNew = redirectUrl || fromUrl;
    login({
      redirectUrl: redirectUrlNew
    });
  }
}
