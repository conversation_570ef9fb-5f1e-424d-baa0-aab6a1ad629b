import Madp from '@mu/madp';

import { muApiDomain as apiHost } from '../url_config';

export const querySecurityLevel = async () => {
  const data = await Madp.request(`${apiHost.auth}/getChannelInfo.json`);
  const level = data.channelConfigInfo.securityLevel;
  let route = '';
  if (level === 'H') {
    route = '/vlogin/first-rank';
  } else if (level === 'M') {
    route = '/vlogin/second-rank';
  } else {
    route = '/vlogin/third-rank';
  }
  return route;
};
