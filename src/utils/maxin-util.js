/* eslint-disable no-undef */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-empty */
/* eslint-disable no-use-before-define */
/* eslint-disable no-restricted-properties */
/* eslint-disable camelcase */
/* eslint-disable max-len */
import Taro from '@tarojs/taro';
import Madp from '@mu/madp';
import {
  urlDomain
} from '@utils/url_config';
import {
  getStore, setStore
} from '@api/store';
import {
  md5Util
} from '@mu/madp-security';
import dayjs from 'dayjs';
import {
  Url,
  isMuapp,
  isUnicom,
  isAlipay,
  getCurrentPageUrlWithArgs,
  getMuappVersion,
  getCurrentPageUrl,
  throttle,
} from '@mu/madp-utils';
import { requestPermission, getLocation, getLocale } from '@mu/business-basic';
import { isMiniProgram } from './repay-util';
import { miniProgramChannel } from '@utils/constants';

/**
 * 还款接入优惠券组件，请求优惠券列表参数配置
 */
const getCouponQuery = (otherParams) => {
  const queryParams = {
    querySource: '01', // 查询来源:01-普通查询(后台系统可优先考虑使用；不进行打标、排序等额外处理);02-展示列表(渠道过滤;打标;排序);03-交易组件(渠道过滤;排序
    awardTypeList: ['117', '105', '216', '217', '303', '306'], // 五种券
    queryStatus: '000', // 券查询状态:有效000[未激活001|已激活002|已生效003];无效111[已分享113;已使用114;已过期122]
    ...otherParams, // 方便扩展参数
  };
  return queryParams;
};

const routerPush = (options) => {
  let url = '';
  let useAppRouterFlag = false;
  if (typeof options === 'string') {
    url = options;
  } else {
    const {
      query,
      path,
      useAppRouter,
    } = options;
    let param = Object.keys(query || {}).map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`).join('&');
    if (param && path.indexOf('?') === -1) {
      param = `?${param}`;
    } else if (param && path.indexOf('?') !== -1) {
      param = `&${param}`;
    }
    url = `${path}${param}`;
    if (useAppRouter) useAppRouterFlag = useAppRouter;
  }
  if (useAppRouterFlag) {
    Madp.navigateTo({
      url,
      useAppRouter: true
    });
    return;
  }
  Madp.navigateTo({
    url
  });
};

const routerPushDebounce = throttle((options) => {
  routerPush(options);
}, 1000, {
  trailing: false
});

const routerReplace = (options) => {
  let url = '';
  if (typeof options === 'string') {
    url = options;
  } else {
    const {
      query, path
    } = options;
    let param = Object.keys(query || {}).map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`).join('&');
    if (param && path.indexOf('?') === -1) {
      param = `?${param}`;
    } else if (param && path.indexOf('?') !== -1) {
      param = `&${param}`;
    }
    url = `${path}${param}`;

    // 解决支付页跳转结果页出现“双页面”或“闪现上一个页面”的问题
    const needRefresh = Madp.getStorageSync('needRefresh', 'SESSION');
    if (needRefresh === 'Y' && process.env.TARO_ENV === 'h5') {
      Madp.setStorageSync('needRefresh', '', 'SESSION');
      window.location.replace(`${urlDomain}/${Madp.getChannel()}/repayment/#${path}${param}`);
      return;
    }
  }
  Madp.redirectTo({
    url
  });
};

const externalJump = (key, opt = {}) => {
  const {
    jumpType = 'PUSH', redirectUrl = '', redirectNull = false
  } = opt;
  let currentPageUrl = '';
  if (process.env.TARO_ENV === 'h5') {
    currentPageUrl = window.location.href;
  } else {
    currentPageUrl = `/${getCurrentPageUrlWithArgs()}`;
  }
  const recirect_url = redirectNull ? '' : (redirectUrl || currentPageUrl);
  const recirect_param = recirect_url ? `?redirectUrl=${encodeURIComponent(recirect_url)}` : '';
  const paramObj = {
    ...opt
  };
  delete paramObj.jumpType;
  delete paramObj.redirectUrl;
  delete paramObj.redirectNull;
  let paramsStr = Object.keys(paramObj).map((k) => `${encodeURIComponent(k)}=${encodeURIComponent(paramObj[k])}`).join('&');
  const identifier = recirect_param ? '&' : '?';
  paramsStr = paramsStr ? `${identifier}${paramsStr}` : '';
  let urls = {};
  if (process.env.TARO_ENV === 'h5') {
    urls = {
      SET_PWD: `${urlDomain}/${Madp.getChannel()}/safecenter/#/member/reset-password/now-phone-check${recirect_param}${paramsStr}`,
      MODIFY_PWD: `${urlDomain}/${Madp.getChannel()}/safecenter/#/member/modify-password/now-password-check${recirect_param}${paramsStr}`,
      RESET_PHONE_URL: `${urlDomain}/${Madp.getChannel()}/safecenter/#/memeber/reset-phone/select${recirect_param}${paramsStr}`,
      ADD_BANK_CARD: `${urlDomain}/${Madp.getChannel()}/usercenter/#/bankcard/bind-card${recirect_param}${paramsStr}`,
    };
  } else {
    urls = {
      SET_PWD: `/safecenter/pages/reset-password/SetPsdCheckPhone${recirect_param}${paramsStr}`,
      MODIFY_PWD: `/safecenter/pages/modify-password/CheckOldPsd${recirect_param}${paramsStr}`,
      RESET_PHONE_URL: `/safecenter/pages/login-reset-phone/ChoosePage${recirect_param}${paramsStr}`,
      ADD_BANK_CARD: `/usercenter/pages/bankcard/AddOrVerifyBankCard${recirect_param}${paramsStr}`,
    };
  }
  if (jumpType.toUpperCase() === 'PUSH') Madp.navigateTo({
    url: urls[key]
  });
  else Madp.redirectTo({
    url: urls[key]
  });
};


const viewContract = (contractData, contractObj = false, onlyLinkUrl = false) => {
  if (contractObj) {
    // console.log('viewContract', contractObj)
    // 数组对象填充字段无法放到链接里，按约定规则放到缓存里
    // window.sessionStorage.setItem('CONTRACT_OBJ_DATA', JSON.stringify(contractObj));
    Madp.setStorageSync('CONTRACT_OBJ_DATA', contractObj, 'SESSION');
  }
  if (!contractData) return;
  const paramsStr = Object.keys(contractData).map((k) => {
    if (typeof contractData[k] === 'object' && contractData[k] !== null) {
      return `${encodeURIComponent(k)}=${encodeURIComponent(JSON.stringify(contractData[k]))}`;
    } else {
      return `${encodeURIComponent(k)}=${encodeURIComponent(contractData[k])}`;
    }
  }).join('&');
  let jumpUrl = '';
  let url = '';
  if (process.env.TARO_ENV !== 'h5') {
    jumpUrl = `/usercenter/pages/contract/view?${paramsStr}`;
  } else {
    url = `${urlDomain}/${Madp.getChannel()}/usercenter/#/pages/contract/view?${paramsStr}`;
    // jumpUrl = `/pages/web-view/index?pageUrl=${encodeURIComponent(url)}`;
    jumpUrl = `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/web-view/index?pageUrl=${encodeURIComponent(url)}`;
  }
  if (onlyLinkUrl) {
    return jumpUrl;
  } else {
    if (isMuapp()) {
      Madp.navigateTo({
        url: url,
        useAppRouter: true
      });
    } else {
      Madp.navigateTo({
        url: jumpUrl
      });
    }
  }
};


const getCurrentDateTimeInFormat = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = (`0${date.getMonth() + 1}`).slice(-2);
  const day = (`0${date.getDate()}`).slice(-2);

  const hours = (`0${date.getHours()}`).slice(-2);
  const minutes = (`0${date.getMinutes()}`).slice(-2);
  const seconds = (`0${date.getSeconds()}`).slice(-2);

  return {
    dateFormat: `${year}${month}${day}`,
    dateTime: `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  };
};

const generateUUID = () => {
  let d = new Date().getTime();
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    // eslint-disable-next-line
    const r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    // eslint-disable-next-line
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
  });
  return uuid;
};

/**
 * 生成具有唯一性的 transRefNo
 * 生成的规则为：时间戳 + userId + 随机数，得到的结果再 MD5
 */
const getTransRefNo = () => {
  let result = '';
  const { basicCustDto } = getStore('sessionInfo') || {};
  const { userId } = basicCustDto || {};
  result = `${Date.parse(new Date()).toString()}${userId}${Math.random()}`;
  result = md5Util(result);
  return result;
};


const toDateMethod = {
  // 2022年01月06日
  chinese: (yy, mm, dd) => `${yy}年${mm}月${dd}日`,
  // 20220106
  yyyymmdd: (yy, mm, dd) => {
    const plusZero = '0000';
    const yyZero = `${plusZero}${yy}`;
    const mmZero = `${plusZero}${mm}`;
    const ddZero = `${plusZero}${dd}`;
    return `${yyZero.slice(-4)}${mmZero.slice(-2)}${ddZero.slice(-2)}`;
  },
  // 2022.01.06
  datedot: (yy, mm, dd) => {
    const plusZero = '0000';
    const yyZero = `${plusZero}${yy}`;
    const mmZero = `${plusZero}${mm}`;
    const ddZero = `${plusZero}${dd}`;
    return `${yyZero.slice(-4)}.${mmZero.slice(-2)}.${ddZero.slice(-2)}`;
  },
  // 2022-01-06
  dateline: (yy, mm, dd) => {
    const plusZero = '0000';
    const yyZero = `${plusZero}${yy}`;
    const mmZero = `${plusZero}${mm}`;
    const ddZero = `${plusZero}${dd}`;
    return `${yyZero.slice(-4)}-${mmZero.slice(-2)}-${ddZero.slice(-2)}`;
  },
  // 2022/01/06
  dateslash: (yy, mm, dd) => {
    const plusZero = '0000';
    const yyZero = `${plusZero}${yy}`;
    const mmZero = `${plusZero}${mm}`;
    const ddZero = `${plusZero}${dd}`;
    return `${yyZero.slice(-4)}/${mmZero.slice(-2)}/${ddZero.slice(-2)}`;
  },
};

const getDateCollection = (val) => {
  if (!val) return [];
  const dateString = val.toString();
  let yy = '';
  let mm = '';
  let dd = '';
  if (!val) {
    return [];
  } else if (val instanceof Date) {
    yy = val.getFullYear().toString();
    mm = (val.getMonth() + 1).toString();
    dd = val.getDate().toString();
  } else if (/\d{4}\.\d{2}\.\d{2}/.test(dateString)) {
    const dateCollect = dateString.split('.');
    [yy, mm, dd] = dateCollect;
  } else if (/\d*-\d*-\d*/.test(dateString)) {
    const dateCollect = dateString.split('-');
    [yy, mm, dd] = dateCollect;
  } else if (dateString.length === 8 && (/^\d+$/).test(dateString)) {
    yy = String(dateString).slice(0, 4);
    mm = String(dateString).slice(4, 6);
    dd = String(dateString).slice(6, 8);
  } else {
    return [];
  }
  return [yy, mm, dd];
};

const dateFormatter = (val, type) => {
  let formattedDate = val;
  let yy = '';
  let mm = '';
  let dd = '';
  if (!val) {
    formattedDate = '';
    return formattedDate;
  } else {
    const dateCollect = getDateCollection(val);
    if (dateCollect.length) {
      [yy, mm, dd] = dateCollect;
    } else {
      formattedDate = val;
      return formattedDate;
    }
  }
  if (typeof type === 'string' && toDateMethod[type.toLowerCase()]) {
    formattedDate = toDateMethod[type.toLowerCase()](yy, mm, dd);
  } else if (type instanceof Array) {
    formattedDate = type.map((key) => toDateMethod[key.toLowerCase()](yy, mm, dd));
  } else {
    formattedDate = toDateMethod.chinese(yy, mm, dd);
  }
  return formattedDate;
};

const checkFinishAction = () => {
  const finishClose = Madp.getStorageSync('repayment_finishClose', 'SESSION') === '1'; // 等于1就关掉
  const finishRedirect = Madp.getStorageSync('repayment_finishRedirect', 'SESSION') || '';
  return {
    close: finishClose,
    redirect: finishRedirect
  };
};

export class ModalControl {
  constructor(that) {
    this.modalList = []; // 控制modal是否展示的变量名
    this.currentShow = null; // 正在展示的modal
    this.instance = that;
  }

  show(modalKeys) {
    if (modalKeys instanceof Array) {
      this.modalList = this.modalList.concat(modalKeys);
    } else {
      this.modalList.push(String(modalKeys));
    }
    if (!this.currentShow) this.execute(this.modalList[0]);
  }

  hide(key) {
    this.instance.setState({
      [key]: false
    });
    if (this.modalList.length) {
      this.next();
    } else {
      this.currentShow = null;
    }
  }

  quit(key) {
    // 跳出循环，通常用于页面跳转，跳转后不再展示
    this.instance.setState({
      [key]: false
    });
    this.currentShow = null;
  }

  next() {
    this.modalList.shift();
    if (this.modalList.length) {
      this.execute(this.modalList[0]);
    } else {
      this.currentShow = null;
    }
  }

  execute(key) {
    this.instance.setState({
      [key]: true
    });
    this.currentShow = key;
  }
}

/**
 * 增加一个假的上一页，在window.history里面，用于beforeRouteLeave判断
 */
const pushUrlState = (checkKey) => {
  if (!Url.getParam(checkKey) && window.location.href.indexOf('repayment') > -1) {
    const checkParam = {};
    checkParam[checkKey] = '1';
    const newUrl = Url.addParam(window.location.href, checkParam);
    window.history.pushState({
      key: window.history.state.key
    }, null, newUrl);
  }
};

function getUserAgent() {
  return typeof window !== 'undefined' && typeof window.navigator !== 'undefined' ? window.navigator.userAgent.toLowerCase() : '';
}

function cmpVersionNums(nums1, nums2) {
  // 说明：NaN通过[>,<,===,>=,<=]与任何数字或NaN比较都为false，通过[!==]与任何数字或NaN比较都为true
  for (let i = 0; i < 3; i += 1) {
    if (nums1[i] > nums2[i]) return 1; // 版本1大于版本2
    if (nums1[i] < nums2[i]) return -1; // 版本1小于版本2
    if (nums1[i] !== nums2[i]) return -2; // 至少有一个是NaN
  }
  return 0; // 版本1等于版本2
}

function getVersionNums(version) {
  // 数字字串转换为数字，非数字字串转换为NaN。用0补全至最少3位
  return version.split('.').map((n) => +n).concat([0, 0, 0]);
}

function cmpVersion(appChannel, mode, targetVersion) {
  const UA = getUserAgent();
  // eslint-disable-next-line no-useless-escape
  const channelRegExp = new RegExp(`muapp\/.*\/${appChannel}`, 'i');
  if (!channelRegExp.test(UA)) {
    return false;
  }
  const version = getMuappVersion();
  if (!version) {
    return false;
  }
  try {
    const cmpResult = cmpVersionNums(getVersionNums(version), getVersionNums(targetVersion));
    switch (mode) {
      case '=':
        return cmpResult === 0;
      case '!=':
        return cmpResult !== 0;
      case '>':
        return cmpResult > 0;
      case '>=':
        return cmpResult >= 0;
      case '<':
        return cmpResult === -1;
      case '<=':
        return cmpResult === -1 || cmpResult === 0;
      default:
        return false;
    }
  } catch (err) {
    return false;
  }
}


const isAppSupport = () => cmpVersion('0app', '>=', '5.19.0');

// 展示客服组件判断
const showChatEntry = () => isMuapp() || isAlipay() || isUnicom() || Madp.getChannel() === '3CMBAPP' || Madp.getChannel() === '0MNP' || Madp.getChannel() === '0ZFBMNPJD';


// 浮点数减法
const floatMinus = (num1, num2) => {
  const num1Digits = ((num1 || 0).toString().split('.')[1] || '').length;
  const num2Digits = ((num2 || 0).toString().split('.')[1] || '').length;
  const baseNum = Math.pow(10, Math.max(num1Digits, num2Digits));
  return (floatTimes((num1 || 0), baseNum) - floatTimes((num2 || 0), baseNum)) / baseNum;
};

// 浮点数乘法
function floatTimes(num1, num2) {
  if (!num1 || !num2) return '';
  const num1String = num1.toString();
  const num2String = num2.toString();
  const num1Digits = (num1String.split('.')[1] || '').length;
  const num2Digits = (num2String.split('.')[1] || '').length;
  const baseNum = Math.pow(10, num1Digits + num2Digits);
  return Number(num1String.replace('.', '')) * Number(num2String.replace('.', '')) / baseNum;
}

// 浮点数加法
function floatAdd(num1, num2) {
  let r1;
  let r2;
  try {
    r1 = num1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = num2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  const m = Math.pow(10, Math.max(r1, r2));
  return Math.round(num1 * m + num2 * m) / m;
}

// 浮点数的乘法
const accMul = (arg1, arg2) => {
  if (arg1 && arg2) {
    let m = 0;
    const s1 = arg1.toString();
    const s2 = arg2.toString();
    try {
      m += s1.split('.')[1].length;
    } catch (e) { }
    try {
      m += s2.split('.')[1].length;
    } catch (e) { }
    return Number(s1.replace('.', '')) * Number(s2.replace('.', '')) / Math.pow(10, m);
  } else if (arg1) { // 一般是用来乘100 所以首位一定要有
    return Number(arg1);
  } else {
    return '';
  }
};

// 浮点数的除法
const divide = (num1, num2) => {
  if (num1 && num2) {
    const num1String = num1.toString();
    const num2String = num2.toString();
    const num1Digits = (num1String.split('.')[1] || '').length;
    const num2Digits = (num2String.split('.')[1] || '').length;
    const baseNum = Math.pow(10, num1Digits + num2Digits);
    const n1 = accMul(num1, baseNum);
    const n2 = accMul(num2, baseNum);
    return Number(n1) / Number(n2);
  } else if (num1) {
    return Number(num1);
  } else if (num2) {
    return Number(num2);
  } else {
    return '';
  }
};

// 进行优惠券类型的转换，优惠券查询接口与试算接口字段不一致，根据对应规则进行调整
const transformCouponItem = (coupon, loanType = 'F') => {
  const waiveObject = {};
  waiveObject.waiveType = '01';
  waiveObject.waiveRefno = coupon.awardNo;
  waiveObject.waiveDesc = coupon.awardName;
  if (coupon.awardType === '106') {
    if (coupon.reduceUnit === 'day') {
      waiveObject.waiveDeductType = '03';
      waiveObject.waiveAmtType = 'I';
      waiveObject.waiveValue = '0.000000'; // 必须要求小数点后四位，真奇葩
      waiveObject.waiveTimeRange = coupon.reduceCnt;
      waiveObject.waiveTimeSequence = coupon.reduceSeq === 'prv' ? 'B' : (coupon.reduceSeq === 'ltr' ? 'A' : '');
      waiveObject.waiveTimeUnit = 'D';
      waiveObject.waiveAmtUpperLimit = coupon.maxReductAmt;
    } else if (coupon.reduceUnit === 'mon') {
      waiveObject.waiveDeductType = '02';
      waiveObject.waiveAmtType = 'F';
      waiveObject.waiveValue = Number(divide(coupon.awardAmt, 10)).toFixed(6); // 必须要求小数点后四位，真奇葩
      waiveObject.waiveTimeRange = coupon.reduceCnt;
      waiveObject.waiveTimeSequence = coupon.reduceSeq === 'prv' ? 'B' : (coupon.reduceSeq === 'ltr' ? 'A' : '');
      waiveObject.waiveTimeUnit = 'P';
      waiveObject.waiveAmtUpperLimit = coupon.maxReductAmt;
    } else if (coupon.reduceUnit === 'all') {
      waiveObject.waiveDeductType = '02';
      waiveObject.waiveAmtType = loanType;
      waiveObject.waiveValue = Number(divide(coupon.awardAmt, 10)).toFixed(6);
      waiveObject.waiveTimeRange = '-1';
      waiveObject.waiveTimeSequence = coupon.reduceSeq === 'prv' ? 'B' : (coupon.reduceSeq === 'ltr' ? 'A' : '');
      waiveObject.waiveTimeUnit = 'P';
      waiveObject.waiveAmtUpperLimit = coupon.maxReductAmt;
    } else {
      waiveObject.waiveDeductType = '02';
      waiveObject.waiveAmtType = 'F';
      waiveObject.waiveValue = Number(divide(coupon.awardAmt, 10)).toFixed(6);
      waiveObject.waiveTimeRange = '-1';
      waiveObject.waiveTimeSequence = coupon.reduceSeq === 'prv' ? 'B' : (coupon.reduceSeq === 'ltr' ? 'A' : '');
      waiveObject.waiveTimeUnit = 'P';
      waiveObject.waiveAmtUpperLimit = coupon.maxReductAmt;
    }
  } else if (coupon.awardType === '113') {
    waiveObject.waiveDeductType = '02';
    waiveObject.waiveAmtType = 'I';
    waiveObject.waiveValue = Number(divide(coupon.awardAmt, 10)).toFixed(6);
    waiveObject.waiveTimeRange = coupon.reduceCnt;
    waiveObject.waiveTimeSequence = coupon.reduceSeq === 'prv' ? 'B' : (coupon.reduceSeq === 'ltr' ? 'A' : '');
    waiveObject.waiveAmtUpperLimit = coupon.maxReductAmt;
    if (coupon.reduceUnit === 'mon') {
      waiveObject.waiveTimeUnit = 'P';
    } else {
      waiveObject.waiveTimeUnit = 'D';
    }
  } else if (coupon.awardType === '108') {
    waiveObject.waiveDeductType = '01';
    waiveObject.waiveAmtType = 'F';
    waiveObject.waiveValue = Number(coupon.awardAmt).toFixed(6);
    waiveObject.waiveTimeRange = null;
    waiveObject.waiveTimeSequence = null;
    waiveObject.waiveTimeUnit = null;
    waiveObject.waiveAmtUpperLimit = coupon.maxReductAmt;
  } else if (coupon.awardType === '111') {
    waiveObject.waiveDeductType = '03';
    waiveObject.waiveAmtType = 'F';
    waiveObject.waiveValue = '0.000000';
    waiveObject.waiveTimeRange = coupon.reduceCnt;
    waiveObject.waiveTimeSequence = coupon.reduceSeq === 'prv' ? 'B' : (coupon.reduceSeq === 'ltr' ? 'A' : '');
    waiveObject.waiveTimeUnit = 'P';
    waiveObject.waiveAmtUpper = coupon.maxReductAmt;
  } else if (coupon.awardType === '213') {
    waiveObject.waiveDeductType = '04';
    waiveObject.waiveAmtType = 'I';
    waiveObject.waiveValue = Number(coupon.annualInterestRate).toFixed(6) || '0.000000'; // 必须要求小数点后四位，真奇葩
    waiveObject.waiveTimeRange = coupon.reduceCnt;
    waiveObject.waiveTimeSequence = coupon.reduceSeq === 'prv' ? 'B' : (coupon.reduceSeq === 'ltr' ? 'A' : '');
    if (coupon.reduceUnit === 'mon') {
      waiveObject.waiveTimeUnit = 'P';
    } else {
      waiveObject.waiveTimeUnit = 'D';
    }
    waiveObject.waiveAmtUpper = coupon.maxReductAmt;
  }
  return waiveObject;
};

const injectConfigParams = (configData, that) => {
  // eslint-disable-next-line dot-notation
  const configObj = {
    ...(configData['default'] || {}),
    ...(configData[Madp.getChannel()] || {})
  };
  if (that) {
    Object.keys(configObj).forEach((k) => {
      // eslint-disable-next-line no-param-reassign
      that[k] = configObj[k] || that[k];
    });
  }
  return configObj;
};

function isH5Env() {
  return process.env.TARO_ENV === 'h5';
}

function showErrorMsg(msg) {
  const errMsg = msg || '系统繁忙，请稍后再试';
  let useShowToast = true;
  // fix: 小程序原生化ios手机showToast展示两行文案限制。针对特殊情况使用showModal。
  if (process.env.TARO_ENV === 'weapp' && errMsg.length > 24) {
    try {
      const res = wx.getSystemInfoSync();
      useShowToast = res.platform !== 'ios';
    } catch (error) { }
  } else if (process.env.TARO_ENV === 'swan' && errMsg.length > 24) {
    useShowToast = false;
  }
  if (useShowToast) {
    Madp.showToast({
      title: errMsg,
      icon: 'none',
      duration: 2000
    });
  } else {
    Madp.showModal({
      showCancel: false,
      content: errMsg,
      success() { }
    });
  }
}

function toast(msg) {
  const content = msg;
  let useShowToast = true;
  // fix: 小程序原生化ios手机showToast展示两行文案限制。针对特殊情况使用showModal。
  if (process.env.TARO_ENV === 'weapp' && content.length > 24) {
    try {
      const res = wx.getSystemInfoSync();
      useShowToast = res.platform !== 'ios';
    } catch (error) { }
  } else if (process.env.TARO_ENV === 'swan' && content.length > 24) {
    useShowToast = false;
  }
  if (useShowToast) {
    Madp.showToast({
      title: content,
      icon: 'none'
    });
  } else {
    Madp.showModal({
      showCancel: false,
      content,
      success() { }
    });
  }
}

const handleBase64 = (src, resolve, sizeThreshold = false) => {
  if (!sizeThreshold) resolve({
    src
  });
  const img = new Image();
  img.src = src;
  img.onload = () => {
    const cvs = document.createElement('canvas');
    const ctx = cvs.getContext('2d');
    const fileSize = img.width * img.height;
    if (fileSize > sizeThreshold) {
      const ratio = Math.sqrt(sizeThreshold / fileSize * 1.0);
      img.width *= ratio;
      img.height *= ratio;
    }
    cvs.width = img.width;
    cvs.height = img.height;
    ctx.clearRect(0, 0, cvs.width, cvs.height);
    ctx.drawImage(img, 0, 0, img.width, img.height);

    const dataUrl = cvs.toDataURL('image/jpeg');
    resolve({
      src: dataUrl
    });
  };
};

const getImageInfo = (src) => {
  return new Promise((resolve, reject) => {
    Madp.getImageInfo({
      src,
      success: (res) => {
        resolve(res);
      },
      fail: (e) => {
        console.debug('getImageInfo失败', e);
        reject();
      }
    });
  });
};

// 小程序端专用图片压缩方法
// 需要在调用页面增加canvasId为compressImgCanvas的canvas组件
const compressByCanvas = (imgInfo, sizeThreshold) => {
  return new Promise((resolve, reject) => {
    const ctx = Madp.createCanvasContext('compressImgCanvas');
    const fileSize = imgInfo.width * imgInfo.height;
    if (fileSize > sizeThreshold) {
      const ratio = Math.sqrt(sizeThreshold / fileSize * 1.0);
      imgInfo.width *= ratio;
      imgInfo.height *= ratio;
    }
    ctx.drawImage(imgInfo.path, 0, 0, imgInfo.width, imgInfo.height);
    ctx.draw(false, setTimeout(() => {
      Madp.canvasToTempFilePath({
        canvasId: 'compressImgCanvas',
        x: 0,
        y: 0,
        width: imgInfo.width,
        height: imgInfo.height,
        fileType: 'jpg',
        success: (res) => {
          resolve(res);
        },
        fail: () => {
          console.debug('canvasToTempFilePath失败', e);
          reject();
        }
      });
    }, 300));
  });
};

// 小程序图片压缩
const compressImg = async (src, sizeThreshold, resolve, reject) => {
  const imgInfo = await getImageInfo(src);
  const compressedImg = await compressByCanvas(imgInfo, sizeThreshold);
  const { tempFilePath = '', apFilePath = '' } = compressedImg || {};
  return resolve ? mnpToBase64(tempFilePath || apFilePath, resolve, reject)
    : new Promise((rel, rej) => {
      mnpToBase64(tempFilePath || apFilePath, rel, rej);
    });
};

const toBase64 = (file, resolve, sizeThreshold = false) => {
  const reader = new FileReader();
  reader.onload = (el) => {
    handleBase64(el.target.result, resolve, sizeThreshold);
  };
  reader.readAsDataURL(file);
};

const mnpToBase64 = (filePath, resolve, reject, fileType = 'png') => {
  Madp.getFileSystemManager().readFile({
    filePath, // 选择图片返回的相对路径
    encoding: 'base64', // 编码格式
    success: (res) => resolve({
      src: `data:MUImage/${fileType.toLocaleLowerCase()};base64,${res.data}`,
      path: filePath,
    }),
    fail: (res) => reject(res.errMsg),
  });
};

const chooseImage = (options) => {
  const {
    sizeThreshold = false
  } = options;
  return new Promise((resolve, reject) => {
    Madp.chooseImage({
      sourceType: ['album', 'camera'],
      eventType: '13D',
      count: 1,
      success(res) {
        if (res.base64) {
          // 微信、支付宝H5
          handleBase64(res.base64, resolve, sizeThreshold);
        } else if (isMiniProgram()) {
          // 小程序
          // 小程序有毒，每一款变量名和结构都不一样
          const filePathName = process.env.TARO_ENV === 'alipay' ? 'apFilePaths' : 'tempFiles';
          const imgPath = process.env.TARO_ENV === 'alipay' ? res[filePathName] && res[filePathName][0]
            : res[filePathName] && res[filePathName][0] && res[filePathName][0].path;
          return compressImg(imgPath, sizeThreshold, resolve, reject);
        } else {
          // 浏览器
          toBase64(res.tempFiles[0].originalFileObj, resolve, sizeThreshold);
        }
      },
      fail() {
        reject('拍照接口调用失败');
      }
    });
  });
};

const getBusiEntrance = () => {
  const url = getCurrentPageUrl();
  let busiEntrance = '';
  if (process.env.TARO_ENV === 'h5') {
    const keyIndex = url.indexOf('#');
    const str = keyIndex > -1 ? url.substring(keyIndex) : url;
    busiEntrance = `repayment/${str}`;
  } else {
    busiEntrance = url;
  }
  return busiEntrance;
};

/**
 * @description: 将数字或者字符串转化为保留两位小数的字符串
 * @method numToStr
 * @param { String/Number } value
 * @return { String } 两位小数的字符传
 */
const numToStr = (value) => String(parseFloat(Number(value)).toFixed(2));

const getCantPayReason = (bill) => {
  let reason = bill.canPayRemark || '';
  switch (bill.canNotPayReasonCode) {
    case '01': {
      reason = '该笔借款不能进行提前还款哦';
      break;
    }
    case '02': {
      reason = '该笔借款不能进行提前还款哦';
      break;
    }
    case '03': {
      reason = `该笔借款${dayjs(bill.canPayDate).format('YYYY年MM月DD日')}后可提前还款哦`;
      break;
    }
    case '04': {
      reason = '今天借款，最快明天能提前还款哦';
      break;
    }
    case '05': {
      reason = '周周借不能进行提前还款';
      break;
    }
    default: {
      break;
    }
  }
  return reason;
};

const getThemeColor = (colorEng) => {
  let themeColor = '#3477FF';
  switch (colorEng) {
    case 'red':
    case 'unicomRed':
      themeColor = '#E60027';
      break;
    default:
      break;
  }
  return themeColor;
};

const baseToFile = (base, fileName) => {
  const dataArr = base.split(',');
  const byteString = atob(dataArr[1]);
  const type = dataArr[0].match(/:(.*?);/)[1];
  const fileExt = type.split('/')[1];
  const options = {
    type,
    endings: 'native'
  };
  const u8Arr = new Uint8Array(byteString.length);
  for (let i = 0; i < byteString.length;) {
    u8Arr[i] = byteString.charCodeAt(i);
    i += 1;
  }
  const fileOfBlob = new File([u8Arr], `${fileName}.${fileExt}`, options);
  return fileOfBlob;
};

const paramsToFormData = (params) => {
  const formData = new FormData();
  if (!params) {
    return formData;
  }
  Object.keys(params).map((key) => {
    if (Array.isArray(params[key]) && params[key].length > 0) {
      params[key].forEach((item, i) => {
        formData.append(key, baseToFile(item, `${key}${i}`));
      });
    } else if (params[key].indexOf('data:') !== -1 && params[key].indexOf('base64') !== -1) {
      formData.append(key, baseToFile(params[key], key));
    } else {
      formData.append(key, params[key]);
    }
  });
  return formData;
};

const getBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    let fileResult = '';
    reader.readAsDataURL(file);
    // 开始转
    reader.onload = () => {
      fileResult = reader.result;
    };
    // 转失败
    reader.onerror = (error) => {
      reject(error);
    };
    // 转成功
    reader.onloadend = () => {
      resolve(fileResult);
    };
  });
};

// 判断一个对象是否为空
const isNoEmptyObject = (object) => {
  // 这里不用Object.keys()来判断防止有兼容性问题
  if (object && JSON.stringify(object) !== '{}') {
    return true;
  }
  return false;
};

// 判断时间差，入参format为timestamp为时间戳形式，format为date是年月日形式的
export const timeMinus = (dateF, dateS, unit = 'day', format = 'timestamp') => {
  if (!dateF || !dateS) {
    return 0;
  }
  let date1 = dateF;
  let date2 = dateS;
  if (format === 'date') { // 日期转换成时间戳
    date1 = new Date(dateF.split('.').join('-')).getTime();
    date2 = new Date(dateS.split('.').join('-')).getTime();
  }
  const baseUnit = 1000;
  const date1Length = `${date1}`.length;
  const date2Length = `${date2}`.length;
  // eslint-disable-next-line no-restricted-properties
  const date1ToMs = date1 * Math.pow(10, (13 - date1Length));
  // eslint-disable-next-line no-restricted-properties
  const date2ToMs = date2 * Math.pow(10, (13 - date2Length));
  if (unit === 'seconds') {
    return (date1ToMs - date2ToMs) / (baseUnit);
  }
  if (unit === 'minutes') {
    return (date1ToMs - date2ToMs) / (baseUnit * 60);
  }
  if (unit === 'hours') {
    return (date1ToMs - date2ToMs) / (baseUnit * 60 * 60);
  }
  return (date1ToMs - date2ToMs) / (baseUnit * 60 * 60 * 24);
};

// 根据配置链接跳转
const configUrlJump = (url, scene = '') => {
  if (!url) return;
  let jumpUrl = url;
  if (url.indexOf('repaymentFlag=Y') > -1) {
    if (scene === 'homeIndex') {
      Madp.setStorageSync('repayJumpOut', '', 'SESSION');
    }
    jumpUrl = url.indexOf('?') > -1 ? `${url}&_windowSecureFlag=1` : `${url}?_windowSecureFlag=1`;
  } else {
    if (scene === 'homeIndex') {
      Madp.setStorageSync('repayJumpOut', 'Y', 'SESSION');
      Madp.setStorageSync('hideRepayPlan', 'Y', 'SESSION');
    }
  }
  if (jumpUrl.indexOf('/repay-staging') > -1) {
    setStore.clear('billsStagingInfo');
  }
  if (jumpUrl.indexOf('/consult-repay-apply') > -1 || jumpUrl.indexOf('/repay-staging') > -1 || jumpUrl.indexOf('/bill-extend') > -1) {
    const redirectUrl = isMiniProgram() ? '/repayment/pages/index/index' : `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/index/index`;
    jumpUrl = jumpUrl.indexOf('?') > -1 ? `${jumpUrl}&redirectUrl=${encodeURIComponent(redirectUrl)}` : `${jumpUrl}?redirectUrl=${encodeURIComponent(redirectUrl)}`;
  }
  routerPushDebounce(jumpUrl);
};
/**
 * @param {需要进行百分化的数字} strNum 0.239993
 * @param {保留的小数位数} num 4
 * @returns 百分化的结果 23.9993%
 */
const stringToPersent = (strNum, num = 2, displayZero = true) => {
  if (!strNum) return;
  const percentage = parseFloat(strNum) * 100; // 转换为百分数
  let formattedPercentage = '';
  if (displayZero) {
    formattedPercentage = `${percentage.toFixed(Number(num))}%`; // 格式化为带有num位小数的百分数
  } else {
    formattedPercentage = `${(percentage.toFixed(Number(num)) || '').replace(/\.?0+$/, '')}%`; // 格式化为带有num位小数的百分数(去掉末尾的0)
  }
  return formattedPercentage;
};

// 判断两个字符串数组内容是否一致，不考虑顺序（即内容一样但顺序不一样，也认为是相等的）
const arrayCompare = (arr1 = [], arr2 = []) => {
  let result = arr1.length === arr2.length && arr1.every(a => arr2.some(b => a === b)) && arr2.every(_b => arr1.some(_a => _a === _b));
  return result;
};

function getScrollTop() {
  if (document.scrollingElement) {
    return document.scrollingElement.scrollTop;
  }
  return (
    window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0
  );
}

/**
 * compact for scrollTop
 *
 * for more info
 * please see: https://www.zhangxinxu.com/wordpress/2019/02/document-scrollingelement/
 */
function setScrollTop(scrollTopIn) {
  if (document.scrollingElement) {
    document.scrollingElement.scrollTop = scrollTopIn;
  } else {
    document.documentElement.scrollTop = scrollTopIn;
    document.body.scrollTop = scrollTopIn;
  }
}

let scrollTop = 0;
/**
 * 在组件中使用该函数会在body挂上at-frozen样式，
 * 请必须在componentWillUnmount 中调用handleTouchScroll(false)
 * 以去掉样式
 */
const handleTouchScroll = (flag, className) => {
  const ENV = Taro.getEnv();
  if (ENV === Taro.ENV_TYPE.WEB) {
    if (flag) {
      scrollTop = getScrollTop();

      // 使body脱离文档流
      // 设置style属性会被转为 style="[object Object]" 从而实效，所以改用class
      document.body.classList.add(className);

      // 把脱离文档流的body拉上去！否则页面会回到顶部！
      document.body.style.top = `${-scrollTop}px`;
    } else {
      document.body.style.top = '';
      document.body.classList.remove(className);

      setScrollTop(scrollTop);
    }
  }
};

/**
 * 处理补充身份信息
 * scene：场景
 * billType：账单类型
 *    'advanced-stage'：再分期
 *    'extend'：延后还
 *    'adjust'：延长宽限期
 *    'credit-product'：诚信保护（原生）
 *    'renew-loans'： 分期还本
 *    'consult-repay': 协商还
 * applyNo：案件号（再分期、延后还、分期还本、协商还跳转时链接带上，保证成功回来的时候带着案件号）
 */
const gotoSupplyInfo = (supplyParams) => {
  const { scene, billType, applyNo } = supplyParams || {};
  const isMiniChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;

  // 再分期和延长宽限期、延后还、协商还没有原生化，小程序环境还是走的H5
  let redirectUrl = (isMiniChannel && billType === 'credit-product') ? `/${getCurrentPageUrlWithArgs()}` : getCurrentPageUrlWithArgs();
  if (billType === 'advanced-stage' || billType === 'extend' || billType === 'renew-loans' || billType === 'consult-repay') {
    redirectUrl = Url.addParam(redirectUrl, { applyNo });
  }

  const params = {
    isNeedIdCard: '1',
    savePhotoType: 'ID_CARD',
    scene,
    sceneType: billType === 'extend' ? '02' : '01',
    redirectUrl: encodeURIComponent(redirectUrl),
  };

  // 诚信保护走原生，其余走的H5
  let supplyUrl = (isMiniChannel && billType === 'credit-product') ? '/usercenter/pages/add-personalinfo/supplyPhoto?' : `${urlDomain}/${Madp.getChannel()}/usercenter/#/bio-addIdCard?needLogin=1&`;

  Object.keys(params).forEach((key) => {
    if (key === 'redirectUrl') {
      supplyUrl += `${key}=${params[key]}`;
    } else {
      supplyUrl += `${key}=${params[key]}&`;
    }
  });

  if (billType === 'renew-loans' || billType === 'consult-repay') {
    if (isMiniChannel) {
      Taro.navigateTo({ url: supplyUrl });
    } else {
      Madp.navigateTo({ url: supplyUrl });
    }
    return;
  }

  // 小程序环境，内嵌H5用Taro跳转，用madp跳转会有问题（会被madp转原生）
  if (isMiniChannel && billType !== 'credit-product') {
    Taro.redirectTo({
      url: supplyUrl
    });
  } else {
    Madp.redirectTo({
      url: supplyUrl
    });
  }
};

// 获取地理位置信息,由于ios直接调用Madp.getLocation方法不会请求权限所以进行兼容处理。16F->延期还款
async function checkLbsPermission(callback) {
  const permissionInfo = await requestPermission({ eventType: '16F', permission: 'SBXX|WZXX' }); // 无需判断渠道，非muapp渠道会resolve出空的字符串
  const { result = [], tag = [] } = permissionInfo;
  const location = {};
  if (tag[0] && tag[0].WZXX) {
    location.tag = tag[0].WZXX;
  } else if (tag[1] && tag[1].WZXX) {
    location.tag = tag[1].WZXX;
  }
  if ((result[0] && result[0].WZXX === '1') || (result[1] && result[1].WZXX === '1')) {
    const locationInfo = await getLocation({ scene: '16F', modalType: 0 });
    const { coordinateSystem, latitude, longitude } = locationInfo;
    location.coordinateSystem = coordinateSystem;
    location.latitude = latitude;
    location.longitude = longitude;
  } else if ((result[0] && result[0].WZXX === '2') || (result[1] && result[1].WZXX === '2')) {
    location.coordinateSystem = 'WGS-84';
    location.latitude = 999;
    location.longitude = 999;
  } else if ((result[0] && result[0].WZXX === '0') || (result[1] && result[1].WZXX === '0')) {
    location.coordinateSystem = '';
    location.latitude = '';
    location.longitude = '';
  }
  Madp.setStorageSync('repayLocation', JSON.stringify(location), 'SESSION');
  console.log({ location });
  callback(location);
}

/**
 * 查询优惠券入参处理
 */
const getCouponEnquiry = (otherParams) => {
  const queryParams = {
    useSceneList: ['repay'], // 使用场景
    awardStatus: '02', // 券状态，02为未激活或已激活，且券未过期
    queryUnAvailable: 'Y', // 需返回不可用的券
    sortRule: '01', // 按券优惠力度从大到小排序
    ...otherParams, // 方便扩展参数
  };
  return queryParams;
};

const closeOrBack = (confirmBtnLeaveFlag) => {
  const isMiniChannel = miniProgramChannel.indexOf(Madp.getChannel()) > -1;
  // h5返回小程序，关闭webview
  if (isMiniChannel) {
    // 离开时清楚缓存
    Madp.setStorageSync(confirmBtnLeaveFlag, '', 'SESSION');
    Madp.miniProgram.navigateBack();
    return;
  }
  if (process.env.TARO_ENV === 'h5') {
    if (isMuapp() && Url.getParam('repaymentFlag') !== 'Y') {
      // 离开时清楚缓存
      Madp.setStorageSync(confirmBtnLeaveFlag, '', 'SESSION');
      Madp.closeWebView();
    } else {
      // 直接返回首页
      const redirectUrl = '/pages/index/index';
      // 招行首页
      const cmbRedirectUrl = `${urlDomain}/${Madp.getChannel()}/ibfcmb/#/pages/index/index`;
      // 还款首页
      const repaymentRedirectUrl = `${urlDomain}/${Madp.getChannel()}/repayment/#/pages/index/index`;

      // 根据条件选择URL
      let selectedUrl;
      if (Madp.getChannel() === '3CMBAPP') {
        if (Url.getParam('repaymentFlag') !== 'Y') {
          selectedUrl = encodeURIComponent(cmbRedirectUrl); // 回到首页
        } else {
          selectedUrl = encodeURIComponent(repaymentRedirectUrl); // 回到还款页
        }
      } else {
        selectedUrl = encodeURIComponent(redirectUrl);
      }
      // 离开时清楚缓存
      Madp.setStorageSync(confirmBtnLeaveFlag, '', 'SESSION');
      const channel = Madp.getChannel();
      // 招行联通渠道reluanch返回有问题，故此修改
      const backFn = ['3CMBAPP', '3CUAPP'].includes(channel) ? Madp.navigateTo : Madp.reLaunch;
      backFn({ url: decodeURIComponent(selectedUrl) }).then().catch(() => {
        if (Madp.getChannel() === '3CMBAPP') {
          Madp.reLaunch({ url: decodeURIComponent(cmbRedirectUrl) }).then().catch(() => {
            Madp.closeWebView();
          });
        } else {
          Madp.closeWebView();
        }
      });
    }
  } else {
    // 离开时清楚缓存
    Madp.setStorageSync(confirmBtnLeaveFlag, '', 'SESSION');
    Madp.navigateBack({ delta: 1 }).then().catch(() => {
      const launchUrl = '%2Fpages%2Findex%2Findex';
      Madp.reLaunch({
        url: decodeURIComponent(launchUrl),
      });
    });
  }
};

// 全部结清判断
async function judgeAllClear(nearBillList, selectedBillList) {
  const isSelectAll = (nearBillList && nearBillList.length) === (selectedBillList && selectedBillList.length);
  let isAllOnTime = true;
  const allOrderSet = new Set(); // 全部借据列表（去重）
  const clearOrderSet = new Set(); // 结清借据列表（去重）
  (selectedBillList || []).forEach((bill) => {
    allOrderSet.add(bill.orderNo);
    if (bill.installCnt === bill.installTotalCnt) {
      clearOrderSet.add(bill.orderNo);
    }
    if (bill.overdueDays > 0) isAllOnTime = false;
  });
  const allOrderList = Array.from(allOrderSet);
  const clearOrderList = Array.from(clearOrderSet);
  const isAllClear = isSelectAll && (allOrderList.length === clearOrderList.length);
  return { isAllClear, isAllOnTime };
}

/**
 * 禁止页面滚动
 * element: 目标dom元素
 * type: 为'hidden'禁止滚动，为''允许滚动
 * 用于弹窗触发时，禁止底部页面禁止滚动
 */
const controlScroll = (element, type = '') => {
  try {
    const el = (document && document.getElementsByClassName(element)) || [];
    el[0].style.overflow = type;
  } catch (error) {
    console.log(error);
  }
};

/**
 * param：yyyymmdd 格式的日期
 * return：将传入日期增加1天后返回 yyyymmdd
 */
const addOneDayToDate = (dateStr) => {
  // 将字符串转换为Date对象
  const year = parseInt(dateStr.substring(0, 4), 10);
  const month = parseInt(dateStr.substring(4, 6), 10) - 1; // 月份从0开始计数
  const day = parseInt(dateStr.substring(6, 8), 10);

  const date = new Date(year, month, day);
  date.setDate(date.getDate() + 1); // 增加一天

  // 将Date对象转换回字符串格式
  const newYear = date.getFullYear();
  const newMonth = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份从0开始计数，所以加1
  const newDay = date.getDate().toString().padStart(2, '0');

  return `${newYear}${newMonth}${newDay}`;
};

/**
 * 获取运营管理端文案配置
 * param：文案编码和填充参数
 * return：富文本文案内容
 */
const getRepaymentLocale = (waMap, ...args) => {
  let text = '';
  try {
    text = getLocale(waMap, ...args);
  } catch (error) {
    console.log('获取文案失败', error);
  }
  return text;
};

const isEmpty = (value) => {
  if (value === null || value === undefined) { // 检查 null 和 undefined
    return true;
  }

  if (typeof value === 'string' || Array.isArray(value)) {
    return value.length === 0; // 检查空字符串和空数组
  }

  if (typeof value === 'object') {
    return Object.keys(value).length === 0; // 检查空对象
  }

  return false; // 其他类型默认不为空
};

export default {
  router: {
    push: routerPushDebounce,
    replace: routerReplace,
  },
  externalJump,
  getCouponQuery,
  viewContract,
  generateUUID,
  getTransRefNo,
  dateFormatter,
  getDateCollection,
  toast,
  checkFinishAction,
  pushUrlState,
  showChatEntry,
  isAppSupport,
  floatMinus,
  floatTimes,
  accMul,
  divide,
  transformCouponItem,
  injectConfigParams,
  isH5Env,
  showErrorMsg,
  chooseImage,
  previewImage: (options) => Madp.previewImage(options),
  getBusiEntrance,
  numToStr,
  floatAdd,
  getCantPayReason,
  getThemeColor,
  paramsToFormData,
  baseToFile,
  getBase64,
  compressImg,
  isNoEmptyObject,
  timeMinus,
  configUrlJump,
  stringToPersent,
  arrayCompare,
  handleTouchScroll,
  gotoSupplyInfo,
  checkLbsPermission,
  getCouponEnquiry,
  getCurrentDateTimeInFormat,
  judgeAllClear,
  controlScroll,
  addOneDayToDate,
  closeOrBack,
  getRepaymentLocale,
  isEmpty
};
