/* eslint-disable no-unused-vars */
import { Component } from '@tarojs/taro';
import NavBarView from '@components/nav-bar-view/index';
import Madp from '@mu/madp';
import { Url } from '@mu/madp-utils';

// eslint-disable-next-line import/no-mutable-exports
let pageHoc = null;

if (process.env.TARO_ENV === 'h5') {
  pageHoc = (options = {}) => (ComposedComponent) => class extends Component {
    // eslint-disable-next-line no-useless-constructor
    constructor(props) {
      super(props);
      this.wrappedInstance = null;
      this.state = {
        configHocTitle: ''
      };
    }

    config = {
      navigationBarTitleText: options.title || '还款'
    }

    // 提前设置标题，部分组件会在DidMount更新标题，这里用DidMount的话会覆盖设置顺序
    // componentWillMount() {
    //   const { title = '招联收银台' } = options;
    //   Madp.setNavigationBarTitle({
    //     title,
    //   });
    // }
    componentDidShow() {
      const { title = '还款' } = options;
      const { hocTitle } = this.$router.params;
      const { navTitle } = (this.wrappedInstance.hocConfig || {});
      // eslint-disable-next-line no-unused-expressions
      navTitle && this.setState({ configHocTitle: navTitle });
      Madp.setNavigationBarTitle({
        title: hocTitle || navTitle || title,
      });
      // eslint-disable-next-line no-unused-expressions
      ComposedComponent.prototype.componentDidShow
      && ComposedComponent.prototype.componentDidShow.call(this.wrappedInstance);
    }

    beforeRouteLeave(from, to, next) {
      if (typeof ComposedComponent.prototype.beforeRouteLeave === 'function') {
        // 再调用子组件的 beforeRouteLeave
        // eslint-disable-next-line prefer-rest-params
        ComposedComponent.prototype.beforeRouteLeave.call(this.wrappedInstance, ...arguments);
      } else {
        next(true);
      }
    }

    render() {
      const { title = '还款' } = options;
      const { hocTitle } = this.$router.params;
      const { configHocTitle } = this.state;
      return (
        <NavBarView
          title={
            hocTitle || configHocTitle || title
          }
          beaconId="RepaymentNav"
          onClick={
            Url.getParam('leftBarCloseWeb') === '1' ? () => {
              Madp.closeWebView();
            } : null
          }
        >
          <ComposedComponent {...this.props} ref={(ref) => { this.wrappedInstance = ref; }} />
        </NavBarView>
      );
    }
  };
} else {
  pageHoc = (trackInfo, options) => function decorator() { };
}

export default pageHoc;
