/* eslint-disable no-unused-vars */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-restricted-globals */
/* eslint-disable no-undef */
import 'babel-polyfill'; // 此依赖必须放在第一行
import '@utils/polyfill';
import Taro, { Component } from '@tarojs/taro';
import '@tarojs/async-await';
import Madp from '@mu/madp';
import { isWechat, Url, getEnv, isIOS } from '@mu/madp-utils';
import { setStore, getStore } from '@api/store';
import { channel, mapCodeMap } from '@utils/constants';
import Bridge from '@utils/bridge';
import { muApiDomain as apiHost } from '@utils/url_config';
// import VConsole from 'vconsole';
import { armsProxy, setup } from '@mu/business-basic';
import Dispatch from '@api/actions';
import TransferGuide from './pages/transfer/guide';
import './app.scss';

// 设置默认渠道
Madp.setConfig({
  channel
});

setup({
  constants: {
    pidMaps: {
      all_prod: 'brtgwl5o94@2fb16b024792eef', // 写入生产的pid
      all_dev: 'brtgwl5o94@43628f5cb1cdb4f', // 写入测试的pid
    },
    debug: true,
    locales: require('@config/locales/zh-CN.json'),
  }
});
armsProxy();

if (window && window.__bl && __bl.setConfig) {
  __bl.setConfig({
    setUsername() {
      const sessionInfo = getStore('sessionInfo');
      const { basicCustDto } = sessionInfo || {};
      const { custId } = basicCustDto || {};
      return custId || null;
    }
  });
}

// 应用初始化时获取相关url
function updateBusinessParams() {
  // 业务mapCode获取规则: 优先从url获取-->再从sessionStorage获取
  const param = Url.getParam('mapCode') || '';
  const finishClose = Url.getParam('finishClose'); // = 1就关闭
  const finishRedirect = Url.getParam('finishRedirect') || '';
  if (param) {
    // 当可以从url获取到，则设置到 sessionStorage
    Madp.setStorageSync(
      'mapCode',
      param,
      'SESSION'
    );
  }
  if (finishClose) { Madp.setStorageSync('repayment_finishClose', finishClose, 'SESSION'); }
  if (finishRedirect) { Madp.setStorageSync('repayment_finishRedirect', finishRedirect, 'SESSION'); }
}

// 应用初始化时主动初初始化一次，主要是解决应用初始化时并没有hashChange的问题
updateBusinessParams();

// 监听路由 hashChange
Madp.eventCenter.on('__taroRouterChange', () => {
  updateBusinessParams();
});

// 应用初始化时主动初始化一次，满足链接不带mapCode的场景
function getMapCodeFromMap() {
  const param = Url.getParam('mapCode') || '';
  if (param) return;
  const ch = Madp.getChannel();
  const mapCode = mapCodeMap[ch] || '';
  if (mapCode) {
    Madp.setStorageSync(
      'mapCode',
      mapCode,
      'SESSION'
    );
  }
}
getMapCodeFromMap();

function getPageName() {
  const { hostname, pathname, hash } = location;
  const pageName = hostname + pathname + hash.split('?')[0];
  return pageName;
}

// 微信api接口初始化
if (process.env.TARO_ENV === 'h5' && isWechat()) {
  Madp.initWechat({
    /* eslint-disable-next-line */
    error: (err) => {
      // 加签失败提示
    }
  });
}

// 引入相关主题设定脚本
const setCustomTheme = require('./config/theme').default;
// 设置默认主题
setCustomTheme();

if (Madp.getChannel() === '3CMBAPP') {
  // 招行隐藏导航栏，使用组件的
  Bridge.setNavigationBarUI('hideNavigationBar');
  // 修复ios跳转到展示原生导航栏的页面再返回时，不刷新导致出现双导航栏的问题
  window.addEventListener('pageshow', async (event) => {
    if (event.persisted || (window.performance && window.performance.navigation.type === 2)) {
      if (isIOS()) {
        Bridge.setNavigationBarUI('hideNavigationBar');
      }
    }
  });
}
class App extends Component {
  componentWillMount() {
    // __bl && __bl.setPage && __bl.setPage(getPageName(), false);
  }

  async componentDidMount() {
    if (getEnv().indexOf('st') !== -1 || process.argv.indexOf('debug') > -1) {
      // const vconsole = new VConsole();
      import('vconsole').then(vconsole => {
        /* eslint-disable-next-line */
        new vconsole.default();
      });
    }

    // 初始化用户信息
    setStore.clear('sessionInfo');
    await Dispatch.repayment.getNewUserInfo();
  }

  componentDidShow() { }

  componentDidHide() { }

  componentCatchError() { }

  componentDidCatchError() { }
  config = {
    pages: [
      'pages/index/index',
      'pages/index/over-due-effect',
      'pages/bill-list-near/index', // 近期待还
      'pages/bill-list-future/index', // 近期待还
      'pages/bill-list-all/index', // 全部待还
      'pages/bill-adjust/list', // 宽限期延长
      'pages/bill-extend/list', // 还款日延长
      'pages/bill-extend/confirm', // 还款日延长确认页
      'pages/bill-extend/intro', // 延后还介绍页
      'pages/bill-extend/loading', // 延后还审批中
      'pages/repay-countdown/index', // 还款倒计时页
      'pages/repay-success/index', // 还款成功页
      'pages/repay-fail/index',
      'pages/repay-detail/index', // 账单详情
      'pages/transfer/guide',
      'pages/transfer/record', // 线下转账详情
      'pages/identity/contact', // 补充身份证页面
      'pages/identity/information', // 补充资料
      'pages/identity/faqs', // 常见问题
      'pages/bill-advanced-stage/list', // 再分期办理页
      'pages/bill-advanced-stage/loading', // 再分期审批中
      'pages/bill-advanced-stage/confirm', // 再分期确认页
      'pages/bill-advanced-stage/result', // 再分期结果页
      'pages/bill-advanced-stage/intro', // 再分期介绍页
      'pages/bill-advanced-stage/bills-to-repay', // 再分期待还账单页
      'pages/bill-advanced-stage/reinstallment-bill-detail', // 再分期账单详情页
      'pages/payday-modify/index', // 修改还款日
      'pages/fee-reduce/index', // 息费减免
      'pages/common-result/result',
      'pages/transfer/identity',
      'pages/transfer/cert-upload',
      'pages/repay-others/index', // 代还款首页
      'pages/repay-others/confirm', // 代还款确认页
      'pages/bills-staging/repay-staging', // 新账单分期
      'pages/bills-staging/repay-staging-result', // 新账单分期结果页
      'pages/bills-staging/bill-list-stages', // 新账单分期借据列表
      'pages/dispatch/index', // 支付宝还款小程序对接中转页
      'pages/web-view/index', // 该页面是在小程序端跳转外部链接用的，请勿删除
      'pages/qrcode-repay/index', // 二维码还款落地页（快捷还款）
      'pages/qrcode-repay/success', // 二维码还款成功页
      'pages/collect/index', // 材料搜集页面
      'pages/pre-repay/new-index', // 预还款支付页
      'pages/pre-repay/before-pre-repay', // 预还款承接页
      'pages/bargain/index', // 自主讨价还价
      'pages/express-repay/index', // 快捷还款（新标准还款）
      'pages/credit-product/index', // 诚信保护首页
      'pages/credit-product/list', // 诚信保护待还账单页面
      'pages/credit-product/result', // 包装延长宽限期服务结果页
      'pages/home/<USER>', // 还款服务页
      'pages/consult-repay-apply/index', // 协商还申请页
      'pages/consult-repay-apply/loading', // 协商还审核页
      'pages/consult-repay-apply/comfirm', // 协商还确认页
      'pages/consult-repay-result/index', // 协商还结果页
      'pages/service-result/index', // 服务办理结果页
      'pages/service-pending/index', // 在途服务办理确认页
      'pages/service-refuse/index', // 服务办理拒绝页
      'pages/renew-loans/index', // 分期还本-首页
      'pages/renew-loans/intro', // 分期还本-介绍页
      'pages/renew-loans/confirm', // 分期还本-确认办理页
      'pages/renew-loans/result', // 分期还本-结果页
      'pages/standard-service-refuse/index', // 标准化准入拒绝页（目前接入协商还、待再分期延后还标准化后可复用同一页面）
      'pages/standard-service-result/index', // 标准化结果页（目前接入协商还、待再分期延后还标准化后可复用同一页面）
      'pages/repay-promise/index', // 还款承诺办理页
      'pages/repay-promise/result', // 还款承诺办理结果页
      'pages/diversion-guide/index', // 导流还款-引导页
      'pages/settle-proof/SettleHomePage', // 结清证明-主页
      'pages/settle-proof/SelectIOUPage', // 结清证明-办理页面
      'pages/settle-proof/ProofPreviewPage', // 结清证明-预览
      'pages/settle-proof/VerifyEmailPage', // 结清证明-验证邮箱
      'pages/settle-proof/SettleResultPage', // 结清证明-结算结果页
      'pages/settle-proof/NoLoanProofPreviewPage', // 结清证明-无贷款证明
      'pages/settle-proof/DiversionGuidePage', // 结清证明-无贷款证明
      'pages/self-invoice/index', // 自助开票
      'pages/self-invoice/invoiceHistoryDetail',
      'pages/self-invoice/reSend',
      'pages/self-invoice/invoiceDetail',
      'pages/self-invoice/invoiceSet',
      'pages/self-invoice/tradeRecords',
      'pages/test/index',
      'pages/test1/index',
      'pages/test2/index',
    ],
    window: {
      backgroundTextStyle: 'light',
      navigationBarBackgroundColor: '#fff',
      navigationBarTitleText: 'WeChat',
      navigationBarTextStyle: 'black',
    },
    plugins: {
      myPlugin: {
        version: '1.2.0',
        provider: 'wxefa63d84fe9f64a2'
      }
    }
  };

  // 在 App 类中的 render() 函数没有实际作用
  // 请勿修改此函数
  render() {
    return (
      <TransferGuide />
    );
  }
}

Taro.render(<App />, document.getElementById('app'));
