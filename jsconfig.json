{"compilerOptions": {"target": "es2017", "module": "commonjs", "removeComments": false, "preserveConstEnums": true, "moduleResolution": "node", "experimentalDecorators": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "outDir": "lib", "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "sourceMap": true, "baseUrl": ".", "rootDir": ".", "jsx": "preserve", "jsxFactory": "Taro.createElement", "allowJs": true, "resolveJsonModule": true, "paths": {"@components/*": ["src/components/*"], "@api/*": ["src/api/*"], "@config/*": ["src/config/*"], "@style/*": ["src/style/*"], "@utils/*": ["src/utils/*"], "@mu/repayment/*": ["src/repayment/components/*", "node_modules/@mu/*"]}, "typeRoots": ["node_modules/@types", "global.d.ts"]}, "exclude": ["node_modules", "dist"], "compileOnSave": false}