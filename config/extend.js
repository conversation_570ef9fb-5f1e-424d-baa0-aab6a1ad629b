const path = require('path');
// eslint-disable-next-line import/no-unresolved
const ScriptExtHtmlWebpackPlugin = require('script-ext-html-webpack-plugin');
// eslint-disable-next-line import/no-extraneous-dependencies
const PreloadWebpackPlugin = require('preload-webpack-plugin');

// 暴露webpackChain hook
const webpackChain = {
  hook: {}
};

// js优化配置
const optimization = {
  runtimeChunk: 'single',
  moduleIds: 'hashed',
  namedChunks: true, // 避免chunkId自增修改
  splitChunks: {
    chunks: 'all',
    name: true,
    maxInitialRequests: 30,
    maxAsyncRequests: 30,
    minSize: 200000,
    cacheGroups: {
      fixed: {
        // 固化的组件（基本不变）
        test: /[\\/]node_modules[\\/](dayjs|@mu[\\/]bl|mobx|aes-js|elliptic|crypto-js|dom7|intersection-observer|promise-polyfill|@tarojs[\\/]components|@mu[\\/]swiper-custom|@mu[\\/]user-bhvr-detector)[\\/]/,
        name: 'chunk-fixed',
        chunks: 'all',
        priority: 900,
        reuseExistingChunk: true
      },
      ui: {
        // ui组件库
        test: /[\\/]node_modules[\\/](@mu[\\/](zui|lui|taro-ui))/,
        name: 'chunk-ui',
        chunks: 'all',
        minChunks: 2,
        priority: 850,
        reuseExistingChunk: true
      },
      trade: {
        // 交易验证相关组件
        test: /[\\/]node_modules[\\/]@mu[\\/](trade-verify-shell|safe-sms-shell|geetest-shell|onepass-shell|agreement-new|trade-password-encrypted-shell|madp-security|bio-auth-zfb|biometrics-shell|biometrics-utils|tarosdk-mu-bio-auth)/,
        name: 'chunk-trade',
        chunks: 'all',
        minChunks: 2,
        priority: 800,
        reuseExistingChunk: true
      },
      mu: {
        // 除了交易验证组件外其他二方包
        test: /[\\/]node_modules[\\/]@mu/,
        name: 'chunk-mu',
        chunks: 'all',
        minChunks: 2,
        priority: 700,
        reuseExistingChunk: true
      },
      common: {
        // 三方包，一般也是固化的
        test: /[\\/]node_modules[\\/]/,
        name: 'chunk-fixed',
        chunks: 'all',
        minChunks: 2,
        priority: 500,
        reuseExistingChunk: true
      },
      vconsole: {
        test: /[\\/]node_modules[\\/]vconsole[\\/]dist.*/,
        name: 'vconsole',
        chunks: 'all',
        minChunks: 1,
        priority: 1000,
        // reuseExistingChunk: true
      },
    }
  },
  minimizer: [
    // eslint-disable-next-line arrow-parens
    compiler => {
      // eslint-disable-next-line import/no-extraneous-dependencies
      const TerserPlugin = require('terser-webpack-plugin');
      new TerserPlugin({
        cache: true,
        parallel: true,
        sourceMap: true,
      }).apply(compiler);
    }
  ]
};

function getConfig() {
  return {
    projectName: 'taro-repayment',
    env: {
      TRACK_MODULE_ID: '"repayment"',
    },
    weapp: {
      compile: {
        include: [
          // 'taro-ui',
          // '@mu\\zui',
          // '@mu/zui',
          // '@mu\\lui',
          // '@mu/lui',
          // '@tarojs\\components',
          // '@tarojs/components',
          // '@mu\\madp-utils',
          // '@mu/madp-utils',
          // '@mu\\repayment\\trade-verify',
          // '@mu/repayment/trade-verify',
          // '@mu\\coupon-selector',
          // '@mu/coupon-selector',
          // '@mu\\chat-entry-component',
          // '@mu/chat-entry-component',
          // '@mu\\survey',
          // '@mu/survey',
          // '@mu\\benefit-card',
          // '@mu/benefit-card',
          // '@mu\\agreement',
          // '@mu/agreement'
        ]
      }
    },
    h5: {
      // publicPath: process.env.NODE_ENV === 'development' ? '/repayment/' : './',
      router: {
        basename: '/',
        customRoutes: {
          '/pages/index/index': '/index',
          '/pages/bill-advanced-stage/list': '/bill-advanced-list',
          '/pages/payday-modify/index': '/payday-modify',
          '/pages/fee-reduce/index': '/fee-reduce',
          '/pages/transfer/identity': '/transfer-check',
          '/pages/transfer/guide': '/transfer-reserve',
          '/pages/bill-extend/list': '/extend-list',
          '/pages/common-result/result': '/result',
          '/pages/collect/index': '/collect',
        }
      },
      esnextModules: [
        'taro-ui',
        '@tarojs\\components',
        '@tarojs/components',
        '@tarojs_components',
        '@tarojs\\\\components',
        '@mu\\zui',
        '@mu/zui',
        '@mu\\lui',
        '@mu/lui',
        '@mu\\madp-utils',
        '@mu/madp-utils',
        '@mu\\safe-sms-shell',
        '@mu/safe-sms-shell',
        '@mu\\chat-entry-component',
        '@mu/chat-entry-component',
        '@mu\\coupon-selector',
        '@mu/coupon-selector',
        '@mu\\trade-verify-shell',
        '@mu/trade-verify-shell',
        '@mu\\repayment\\trade-verify',
        '@mu/repayment/trade-verify',
        '@mu\\survey',
        '@mu/survey',
        '@mu\\agreement',
        '@mu/agreement',
        '@mu/covenant-dialog',
        '@mu\\covenant-dialog',
        '@mu/op-ui',
        '@mu\\op-ui',
        '@mu/subscribe-btn',
        '@mu\\subscribe-btn',
        '@mu/wa-richtext',
        '@mu\\wa-richtext'
      ],
      module: {
        postcss: {
          autoprefixer: {
            enable: true,
            config: {
              browsers: ['Android >= 5', 'ios >= 12']
            }
          }
        }
      },
      /* eslint-disable-next-line */
      webpackChain(chain, webpack) {
        /* eslint-disable-next-line */
        webpackChain.hook.before && webpackChain.hook.before(chain, webpack);
        if (process.env.TARO_ENV === 'h5') {
          chain.merge({
            plugin: {
              runtimeChunkInline: {
                plugin: new ScriptExtHtmlWebpackPlugin({
                  inline: /runtime.+\.js$/ // 正则匹配runtime文件名
                })
              },
              preload: {
                plugin: new PreloadWebpackPlugin({
                  rel: 'preload',
                  include: 'allChunks',
                  fileBlacklist: [/runtime\./, /_/, /\.map$/, /vconsole\./],
                  fileWhiteList: [/chunk-/]
                })
              },
              vConsole: {
                // eslint-disable-next-line import/no-unresolved
                plugin: require('vconsole-webpack-plugin'),
                args: [
                  {
                    enable: process.argv.indexOf('debug') > -1
                  }
                ]
              },
              async: {
                // eslint-disable-next-line
                plugin: new webpack.NamedChunksPlugin((chunk) => {
                  if (chunk.name) {
                    return chunk.name;
                  }
                  // eslint-disable-next-line
                  return Array.from(chunk.modulesIterable, (m) => m.id.toString().replace(/(\/|\+)/g, '')).slice(0,2).join('_');
                })
              }
            }
          });
        }
        // splitChunks chain
        chain.merge({
          optimization,
          module: {
            rule: {
              imageCompressLoader: {
                test: /\.(png|jpe?g|gif|bpm|svg)(\?.*)?$/,
                use: [
                  {
                    loader: 'image-webpack-loader',
                    options: {
                      disable: process.env.NODE_ENV === 'development', // webpack@2.x and newer
                      bypassOnDebug: true,
                      mozjpeg: {
                        progressive: true
                      },
                      // optipng.enabled: false will disable optipng
                      optipng: {
                        enabled: false
                      },
                      pngquant: {
                        quality: [0.65, 0.9],
                        speed: 4
                      },
                      gifsicle: {
                        optimizationLevel: 3,
                        colors: 32
                      }
                    }
                  }
                ]
              }
            }
          },
          externals: {
            nervjs: '_nervGlobal',
            '@tarojs/taro': '_taroGlobal',
            '@tarojs/taro-h5': '_taroH5Global',
            '@mu/madp-security': '_securityGlobal',
            '@mu/madp-fetch': '_fetchGlobal',
            '@mu/madp-utils': '_utilsGlobal',
            '@mu/madp': '_madpGlobal',
            '@tarojs/router': '_routerGlobal',
            '@mu/dev-finger': '_fingerGlobal',
            '@mu/das-beacon': '_beaconGlobal',
            '@mu/business-basic': '_businessGlobal',
            '@mu/leda': '_ledaGlobal',
            '@mu/madp-track': '_trackGlobal',
            '@mu/bl': '_blGlobal'
          }
        });
        // analyzer only enabled when report argvs passed in
        if (process.argv.indexOf('report') > -1) {
          chain
            .plugin('analyzer')
            // eslint-disable-next-line import/no-unresolved
            .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [{
              analyzerMode: 'static',
              // analyzerMode: 'server',
              // analyzerMode: 'disabled',
              // analyzerHost: '127.0.0.1',
              analyzerPort: 8889,
              reportFilename: 'report.html',
              // defaultSizes: 'gzip',
              generateStatsFile: true, // 如果为true，则Webpack Stats JSON文件将在bundle输出目录中生成
              // openAnalyzer: false, // 默认在浏览器中自动打开报告
              statsFilename: 'stats.json', // 如果generateStatsFile为true，将会生成Webpack Stats JSON文件的名字
              statsOptions: { source: false }
            }]);
        }
        /* eslint-disable-next-line */
        webpackChain.hook.after && webpackChain.hook.after(chain, webpack);
      }
    },
    alias: {
      '@src': path.resolve(__dirname, '..', 'src'),
      '@components': path.resolve(__dirname, '..', 'src/components'),
      '@utils': path.resolve(__dirname, '..', 'src/utils'),
      '@config': path.resolve(__dirname, '..', 'src/config'),
      '@api': path.resolve(__dirname, '..', 'src', 'api'),
      '@styles': path.resolve(__dirname, '..', 'src/styles'),
      '@assets': path.resolve(__dirname, '..', 'src/assets'),
      '@dev': path.resolve(__dirname, '..', 'src/pages/dev'),
      '@mucfc.com': path.resolve(__dirname, '..', 'node_modules', '@mucfc.com'),
      '@repayment/store': path.resolve(__dirname, '..', 'src/store'),
      swiper: path.resolve(__dirname, '..', 'node_modules', '@mu', 'swiper-custom')
    }
  };
}

module.exports = getConfig;
