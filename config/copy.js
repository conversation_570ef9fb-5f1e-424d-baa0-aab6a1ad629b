const path = require('path');
const fse = require('fs-extra');

// 业务模块名
const moduleId = 'repayment';
// 组件名
const compList = [
  // '@mu/biometrics',
  // '@mu/coupon-selector',
  // '@mu/lui',
  // '@mu/benefit-card',
  // '@mu/agreement'
];
// 需要替换内容的文件类型
const fileTypeReg = new RegExp(`\\.(${[
  'jsx?',
  'tsx?'
].join('|')})$`, 'g');
// 需要去掉的组件名前缀
const filterReg = new RegExp(`^(${[
  '@mu'
].join('|')})\\/`, 'g');
// 替换内容的正则映射
const aliasReplacements = {};
compList.forEach((o) => {
  aliasReplacements[o] = o.replace(filterReg, `$1/${moduleId}/`);
});

// 获取指定目录下的所有文件地址（不包括文件夹）
const getFileSrcList = (src) => {
  if (!fse.lstatSync(src).isDirectory()) return [src];
  const files = fse
    .readdirSync(src)
    .map((child) => getFileSrcList(path.join(src, child)));
  return [].concat.apply([], files);
};

// 替换内容复制粘贴
const copyAndReplace = (srcPath, destPath, replacements = {}) => {
  const srcPermissions = fse.statSync(srcPath).mode;
  let content = fse.readFileSync(srcPath, 'utf8');
  Object.keys(replacements).forEach((regex) => {
    content = content.replace(new RegExp(regex, 'g'), replacements[regex]);
  });
  fse.writeFileSync(destPath, content, {
    encoding: 'utf8',
    mode: srcPermissions,
  });
};

// 注入组件入口文件
const injectEntry = (compPath) => {
  const distEntryPath = path.join(compPath, 'dist/index.js');
  const entryPath = path.join(compPath, 'index.js');
  if (!fse.existsSync(entryPath)) copyAndReplace(distEntryPath, entryPath, { ['\\.\\/([\\s\\S]*?)\\/index']: './dist/$1/index' });
};

// 把组件复制到业务模块的components目录下，并替换alias
const copy = () => {
  if (process.env.TARO_ENV !== 'h5') { // 感觉应该获取不到process.env.TARO_ENV，应该会稳定执行
    compList.forEach((comp) => {
      const compName = comp.replace(filterReg, '');
      const compSourcePath = path.join(__dirname, '..', 'node_modules', comp);
      const compTargetPath = path.join(__dirname, '..', `src/components/${compName}`);
      fse.ensureDirSync(compTargetPath);
      fse.copySync(compSourcePath, compTargetPath);
      injectEntry(compTargetPath);
      getFileSrcList(compTargetPath)
        .filter((o) => !!o.match(fileTypeReg))
        .forEach((filePath) => {
          copyAndReplace(filePath, filePath, aliasReplacements);
        });
    });
  }
};

copy();
