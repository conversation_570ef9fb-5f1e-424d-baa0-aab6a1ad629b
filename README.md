### MUModal使用说明
以下写法编译小程序是不支持的（content不该传jsx）
```js
            <MUModal
              type="image"
              isOpened={showStageTip}
              src={StagingTip}
              title="本月不想还？任性再分期"
              content={(
                <MUView>
                </MUView>
              )}
              confirmText="我知道了"
              beaconId="stagingTip"
              onConfirm={this.closeStageTip}
            />
```
建议使用以下两种写法
1. 已二次封装的RepayModal组件。适用于那些需要在各个小程序环境，用Model展示自定义jsx的场景。
```js
        <RepayModal
          title="账单调整说明"
          className="notice-dialog"
          isOpened={showNoticeDialog}
          closeOnClickOverlay={false}
          beaconId="NoticeDialog"
          confirmText="我知道了"
          onConfirm={() => this.setState({ showNoticeDialog: false })}
        >
          <MUView>
            111
          </MUView>
        </RepayModal>
```
2. MUModal子元素写法
```js
<MUModal>
  <MUView></MUView>
</MUModal>
```
### `小程序原生化相关的特殊组件`。git push 这些组件在components的改动最好注释.gitignore的 dist
1. 交易组件由于小程序主包大小限制，不在壳工程中引入，交由使用方放在各自的components目录下。
**如果是给h5升级交易组件，直接npm i 即可。
 如果还要同步到小程序，`需要手动将node_modules里面的组件复制到src\components\trade-verify`**
- （1）**必须删除组件的package.json。**
- （2）**trade-verify组件会引用biometrics组件，trade-verify中代码**
```js
import MUBioMetrics from '@mu/biometrics';
```
必成改成
```js
import MUBioMetrics from '@mu/repayment/biometrics';
2、@mu/coupon-selector组件也类似。
```
### ESlint 插件请保持开启状态。平时多麻烦，上线少麻烦。







# madp-template

madp template

## usage

用于`madp cli`初始化的模板，基于 git repo方式，下载到业务开发本地。

## 安装依赖

1. 首先保证有全局安装了 `madp-cli`， 通过如下脚本安装

```bash
npm i @mu/madp-cli -g --registry=http://npm.mucfc.com
```

2. 通过 `npm install` 或者 `yarn` 安装相关项目依赖；

3. 通过运行 `ENV=st1 CH=0WEC npm run dev:h5` 来执行H5环境下的测试环境st1的0WEC渠道构建调试;

4. 通过在运行命令之前，添加 `ENV=你的测试环境` 支持到相关测试环境调试

5. 通过在运行命令之前，添加 `CH=你的渠道码` 支持到相关渠道调试

## 如何运行 `webpack-bundle-analyzer` 分析当前代码依赖

可以运行命令 `npm run build:h5-report` 或者手动敲命令 `npm run dev:h5 report` 或者 `npm run build:h5 report` 都可以，
其打开的 bundle analyzer的页面端口默认为 8888


## 如何切换测试环境以及相关平台

1. 添加 `ENV=环境` 来进行调试相关环境api, 或者在 scripts 里添加 "cross-env ENV=st1" 类似来执行相关脚本。 环境支持 `se`，`st1`，`st2`，`uat`，`prod`。默认为prod环境
2. 通过 `dev:平台`来进行相关平台的开发构建, 支持 `h5`,`weapp`,`alipay`,`tt`
3. 通过 `build:平台`来进行相关平台的生产构建, 支持 `h5`,`weapp`,`alipay`,`tt`

## 如何运行 vConsole 在移动端进行调试

运行命令`npm run dev:h5 debug`，框架将在页面嵌入`vConsole`, 构建完成后即可看到vConsole

### 调试版--h5

在se环境下运行 H5

```bash
ENV=se npm run dev:h5
```

### 调试版--微信小程序

在se环境下运行 微信小程序

```bash
ENV=se npm run dev:weapp
```

### 小程序信息

|   渠道码     | APPID             | 支付宝开放平台名称  |
|:------------|:------------------|:-----------------|
| 0ZFBMNPJD   | 2021002113634235  | 招联消费金融       |
| 0JD1ZFBMNP  | 2021002113634618  | 好期贷信用借钱极速版 |
| 0JD2ZFBMNP  | 2021002172671460  | 小额贷款额度申请    |
| 0JD3ZFBMNP  | 2021002172654479  | 生活周转金贷款平台  |
| 0JD4ZFBMNP  | 2021002113640595  | 信用贷款额度分期借钱 |
| 0JD5ZFBMNP  | 2019092567801635  | 安益利率计算器      |
| 0JD6ZFBMNP  | 2017090408554265  | 利息计算器         |
| 0JD7ZFBMNP  | 2021002178680368  | 小招有车贷计算器    |
| 0JD8ZFBMNP  | 2021002178674365  | 招联用户故事汇      |
