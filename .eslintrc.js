module.exports = {
  parser: "babel-eslint",
  plugins: ['react', 'react-hooks', 'import'],
  parserOptions: {
    requireConfigFile: false,
    sourceType: "module",
    "ecmaFeatures": {
      "legacyDecorators": true
    }
  },
  env: {
    browser: true,
    es6: true,
    jasmine: true,
    jest: true,
    jquery: true,
    mocha: true,
    node: true,
  },
  'rules': {
    // 禁止使用未声明的变量
    'no-undef': 'warn',
    // 不要直接使用对象的 Object.prototype 上的内置方法
    'no-prototype-builtins': 'warn',
    // 强制使用 isNaN() 而不是使用 NaN 来进行比较
    'use-isnan': 'warn',
    // 强制 typeof 的计算值为固定为合法的字符串字面量
    'valid-typeof': ['warn', {
      'requireStringLiterals': true
    }],
    // 避免不必要的布尔类型转换
    'no-extra-boolean-cast': 'error',
    // 禁止不必要的分号
    'no-extra-semi': 'error',
    // 禁止在正则表达式中出现多个连续空格
    'no-regex-spaces': 'error',
    // 保障线上代码不存在 debugger 语句，避免浏览器因触发调试而停止执行
    'no-debugger': 'warn',
    // 禁止将全局对象当作函数进行调用
    'no-obj-calls': 'warn',
    // 在对象中不要使用重复的属性名称
    'no-dupe-keys': 'warn',
    // 禁止在 in 语句中的使用 ! 取反左侧运算符，降低运算符优先级的错误率
    'no-negated-in-lhs': 'warn',
    // 可以使用void
    'no-void': 'off',
    // 可以使用continue
    'no-continue': 'off',
    // 变量中允许带有悬挂下划线: 开头或末尾是下划线
    'no-underscore-dangle': 'off',
    // 重复声明变量
    'no-shadow': 'error',
    // 不允许空块语句
    'no-empty': ['error', {
      'allowEmptyCatch': true
    }],
    // 禁止定义没有使用的变量
    'no-unused-vars': ['error', {
      vars: 'local',
      args: 'none',
      ignoreRestSiblings: true,
    }],
    // 禁用使用特定的全局变量
    'no-restricted-globals': [
      'warn',
      {
        name: 'isFinite',
        message: 'Use Number.isFinite instead https://github.com/airbnb/javascript#standard-library--isfinite',
      },
      {
        name: 'isNaN',
        message: 'Use Number.isNaN instead https://github.com/airbnb/javascript#standard-library--isnan',
      },
    ],
    // 禁止删除变量
    'no-delete-var': 'warn',
    // 不要将变量初始化成 undefined
    'no-undef-init': 'error',
    //禁止使用eval
    'no-eval': 'warn',
    'no-implied-eval': 'warn',
    //禁止使用Function构造函数创建函数
    'no-new-func': 'warn',
    //优先使用.访问对象的属性
    'dot-notation': ['off', {
      'allowKeywords': true,
    }],
    //对象中不使用重复的key
    'no-dupe-keys': 'warn',
    //在数组方法中必须在回调函数callback中包含return语句，保证任何情况下都有返回
    'array-callback-return': ['warn', {
      'allowImplicit': true,
    }],
    //禁止使用不必要的转义字符
    'no-useless-escape': 'warn',
    //禁止使用重复的参数名称
    'no-dupe-args': 'warn',
    //case或default字句出现词法声明时，必须用块包裹
    'no-case-declarations': 'warn',
    //禁止使用arguments.caller和arguments.callee
    'no-caller': 'warn',
    //禁止使用newNumber/String/Boolean
    'no-new-wrappers': 'warn',
    //使用parseInt()方法时可以不用带上基数
    'radix': 'off',
    //禁止不必要的label
    'no-extra-label': 'error',
    //不要省略小数点前或小数点后的0
    'no-floating-decimal': 'error',
    //禁止出现多个连续空格
    'no-multi-spaces': [
      'error',
      {
        'ignoreEOLComments': false,
      },
    ],
    //禁止未使用的标签
    'no-unused-labels': 'error',
    //将立即执行函数表达式（IIFE）用小括号包裹
    'wrap-iife': ['error', 'any', {
      'functionPrototypeMethods': false
    }],
    //不推荐使用label语句
    'no-labels': ['warn', {
      'allowLoop': false,
      'allowSwitch': false
    }],
    //使用严格相等运算符
    'eqeqeq': ['error', 'always'],
    //使用+=而不是++
    'no-plusplus': 'warn',
    //在函数定义前使用
    'no-use-before-define': 'warn',
    //禁用in运算符对于forin，因为他的迭代是可以迭代原型链上所有可迭代的属性，这同样会产生意想不到的问题…
    //所以你应该使用forEachmap的这种函数式API去遍历数组
    'no-restricted-syntax': ['error', 'BinaryExpression[operator=\'in\']'],
    //generator函数的*号前面无空格，后面有一个空格
    'generator-star-spacing': ['error', {
      'before': false,
      'after': true,
    }],
    //禁止修改const声明的变量
    'no-const-assign': 'warn',
    //使用const或let声明变量，不要使用var
    'no-var': 'error',
    //使用const来进行变量引用的声明，如果要重新赋值变量，使用let而不是使用var
    'prefer-const': [
      'off',
      {
        'destructuring': 'any',
        'ignoreReadBeforeAssign': true,
      },
    ],
    //避免不必要的constructor
    'no-useless-constructor': 'warn',
    //从同一个位置引用同一个路径的内容
    'no-duplicate-imports': 'warn',
    //字符串拼接优先使用模板字符串
    'prefer-template': 'error',
    'template-curly-spacing': 'error',
    //禁止使用arguments对象，使用语法剩余参数操作符...代替
    'prefer-rest-params': 'warn',
    //避免重复的类成员命名
    'no-dupe-class-members': 'warn',
    //子类的constructor中必须使用super，非子类的constructor中不能使用super
    'constructor-super': 'warn',
    //禁止在super调用前使用this
    'no-this-before-super': 'warn',
    //回调函数使用箭头函数而不是匿名函数
    'prefer-arrow-callback': [
      'error',
      {
        'allowNamedFunctions': false,
        'allowUnboundThis': true,
      },
    ],
    //箭头函数的箭头前后各留一个空格
    'arrow-spacing': ['error', {
      'before': true,
      'after': true
    }],
    //避免箭头函数与比较操作符产生混淆
    'no-confusing-arrow': 'error',
    //对象的属性名不要使用无必要的计算属性
    'no-useless-computed-key': 'error',
    //禁止在解构/import/export时进行无用的重命名
    'no-useless-rename': [
      'error',
      {
        'ignoreDestructuring': false,
        'ignoreImport': false,
        'ignoreExport': false,
      },
    ],
    //使用对象和数组的解构
    'prefer-destructuring': [
      'error',
      {
        'VariableDeclarator': {
          'array': false,
          'object': true,
        },
        'AssignmentExpression': {
          'array': false,
          'object': false,
        },
      },
      {
        'enforceForRenamedProperties': false,
      },
    ],
    //使用2个空格缩进
    'indent': [
      'error',
      2,
      {
        'SwitchCase': 1,
        'VariableDeclarator': 1,
        'outerIIFEBody': 1,
        //MemberExpression:null,
        'FunctionDeclaration': {
          'parameters': 1,
          'body': 1,
        },
        'FunctionExpression': {
          'parameters': 1,
          'body': 1,
        },
        'CallExpression': {
          'arguments': 1,
        },
        'ArrayExpression': 1,
        'ObjectExpression': 1,
        'ImportDeclaration': 1,
        'flatTernaryExpressions': false,
        //listderivedfromhttps://github.com/benjamn/ast-types/blob/HEAD/def/jsx.js
        'ignoredNodes': [
          'TemplateLiteral', //FIXMEhttps://github.com/babel/babel-eslint/issues/799#issuecomment-568195009
          'JSXElement',
          'JSXElement>*',
          'JSXAttribute',
          'JSXIdentifier',
          'JSXNamespacedName',
          'JSXMemberExpression',
          'JSXSpreadAttribute',
          'JSXExpressionContainer',
          'JSXOpeningElement',
          'JSXClosingElement',
          'JSXText',
          'JSXEmptyExpression',
          'JSXSpreadChild',
          'PropertyDefinition',
        ],
        'ignoreComments': false,
      },
    ],
    //函数声明时，对于命名函数，参数的小括号前无空格；对于匿名函数和async箭头函数，参数的小括号前有空格
    'space-before-function-paren': [
      'error',
      {
        'named': 'never',
        'anonymous': 'always',
        'asyncArrow': 'always',
      },
    ],
    //在注释中//或/*使用一致的空格
    'spaced-comment': [
      'error',
      'always',
      {
        'line': {
          'exceptions': ['-', '+'],
          'markers': ['=', '!', '/'],
        },
        'block': {
          'exceptions': ['-', '+'],
          'markers': ['=', '!'],
          'balanced': true,
        },
      },
    ],
    //块的左大括号前有一个空格
    'space-before-blocks': 'error',
    //关键字前后各一个空格
    'keyword-spacing': [
      'error',
      {
        'before': true,
        'after': true,
        'overrides': {
          'return': {
            'after': true,
          },
          'throw': {
            'after': true,
          },
          'case': {
            'after': true,
          },
        },
      },
    ],
    //禁止用空行来填充块语句
    'padded-blocks': ['error', 'never'],
    //禁止使用多个连续空行来填充代码
    'no-multiple-empty-lines': ['error', {
      'max': 2,
      'maxBOF': 1
    }],
    //禁止在空格()中增加空格
    'space-in-parens': ['error', 'never'],
    //不要在方括号[]中增加空格
    'array-bracket-spacing': ['error', 'never'],
    //在多行情况下，使用末尾逗号的风格
    'comma-style': ['error', 'last'],
    //用逗号分隔的多行结构，始终加上最后一个逗号（单行不用）
    //'comma-dangle':['error','always-multiline'],
    //对象属性名必须单引号
    'quote-props': ['error', 'as-needed'],
    //使用字面量创建对象
    'no-new-object': 'warn',
    //可以使用newArray()和Array()创建数组
    'no-array-constructor': 'off',
    //禁止使用链式(chainvariableassignments)赋值
    'no-multi-assign': ['warn'],
    //避免使用=时的赋值语句造成的换行
    'operator-linebreak': ['error', 'before'],
    //禁用行尾空格
    'no-trailing-spaces': [
      'error',
      {
        'skipBlankLines': false,
        'ignoreComments': false,
      },
    ],
    //函数调用前后不需要空格
    'func-call-spacing': ['error', 'never'],
    //保证对象中的键与值有空格
    'key-spacing': ['error', {
      'beforeColon': false,
      'afterColon': true,
    }],
    //逗号的前面无空格，后面有空格
    'comma-spacing': ['error', {
      'before': false,
      'after': true,
    }],
    //保证块语句的{符号与其前面的字符以及}与其后面的字符保证有一个空格
    'block-spacing': ['error', 'always'],
    //操作符两侧有空格
    'space-infix-ops': 'error',
    //大括号内部两侧有空格
    'object-curly-spacing': ['error', 'always'],
    //优先使用单引号
    'quotes': ['error', 'single', {
      'avoidEscape': true,
    }],
    //使用分号
    'semi': ['error', 'always'],
    //一条声明语句声明一个变量
    'one-var': ['error', 'never'],
    //一行声明一个变量
    'one-var-declaration-per-line': ['error', 'always'],
    //使用花括号包裹多行的块语句
    'nonblock-statement-body-position': ['error', 'beside', {
      'overrides': {},
    }],
    //大括号换行风格：onetruebracestyle风格，且单行代码块可不换行
    'brace-style': ['error', '1tbs', {
      'allowSingleLine': true,
    }],
    //避免不必要的三元表达式
    'no-unneeded-ternary': ['error', {
      'defaultAssignment': false
    }],
    //启用否定的表达式
    'no-negated-condition': 'off',
    //方括号内部两侧无空格-计算属性
    'computed-property-spacing': ['error', 'never'],
    //JSX属性使用双引号，不要使用单引号
    'jsx-quotes': ['error', 'prefer-double'],
    //禁止在调用构造函数时省略小括号
    'new-parens': 'error',
    //禁止属性调用前有空格
    'no-whitespace-before-property': 'error',
    //对象的属性需遵循一致的换行风格：即所有属性要么都换行，要么都写在一行
    'object-property-newline': [
      'error',
      {
        'allowAllPropertiesOnSameLine': true,
      },
    ],
    //分号的前面无空格，后面有空格
    'semi-spacing': ['error', {
      'before': false,
      'after': true
    }],
    //分号必须写在行尾
    'semi-style': ['error', 'last'],
    //nonwords:一元操作符两侧无空格，例如：-、+、--、++、!、!!
    //words:-单词类一元操作符两侧有空格，例如：new、delete、typeof、void、yield
    'space-unary-ops': [
      'error',
      {
        'words': true,
        'nonwords': false,
        'overrides': {},
      },
    ],
    //switch的case和default子句冒号前面无空格，后面有空格
    'switch-colon-spacing': ['error', {
      'after': true,
      'before': false
    }],
    //模板字符串的tag后面无空格
    'template-tag-spacing': ['error', 'never'],
    //tab和space不要混合使用，保持风格一致
    'no-mixed-spaces-and-tabs': 'warn',
    //代码长度最大不超过120个字符
    'max-len': [
      'warn',
      120,
      {
        'ignoreUrls': true,
        'ignoreComments': false,
        'ignoreRegExpLiterals': true,
        // 'ignoreStrings': true,
        'ignoreTemplateLiterals': true,
      },
    ],
    //可以使用单个字符
    'id-length': 'off',
    //使用小驼峰命名风格,{'properties':'never','ignoreDestructuring':false}
    'camelcase': ['warn'],
    //使用大驼峰(PascalCase)来命名构造器函数或类
    'new-cap': ['warn', {
      'newIsCap': true,
      'newIsCapExceptions': [],
      'capIsNew': false,
      'capIsNewExceptions': ['Immutable.Map', 'Immutable.Set', 'Immutable.List'],
    }],
    //剩余和扩展操作符与操作对象间不应有空格
    'rest-spread-spacing': ['error', 'never'],
    /**
     *React规则
     *@linkhttps://github.com/yannickcr/eslint-plugin-react
     */
    //react中生命周期函数和自定义函数的顺序
    'react/sort-comp': 'warn',
    //有无用的state
    'react/no-unused-state': 'warn',
    //对no-unused-vars规则的补充，防止React被标记为未使用
    'react/jsx-uses-react': 'error',
    //对no-unused-vars规则的补充，防止React变量被标记为未使用
    'react/jsx-uses-vars': 'error',
    //JSX语法使用两个空格缩进
    'react/jsx-indent': ['error', 2],
    'react/jsx-indent-props': ['error', 2],
    //JSX属性大括号内部两侧无空格
    'react/jsx-curly-spacing': 'error',
    //JSX属性的等号两边不加空格
    'react/jsx-equals-spacing': 'error',
    //JSX行内属性之间只有一个空格
    'react/jsx-props-no-multi-spaces': 'error',
    //检查JSX元素中的开始和结束标签的空格
    //1.闭合斜线左边不允许有空格</，闭合斜线右边不允许有空格/>
    //2.自闭合标签中闭合斜线左边有空格，右边无空格<xx/>
    //3.开始标签前不允许有空格<a>
    'react/jsx-tag-spacing': 'error',
    //当大括号中的JSX属性和表达式占用多行时，则大括号需要换行，如果是单行，则不需要换行
    'react/jsx-curly-newline': 'error',
    //标签有多个属性且换行，每个属性都独占一行
    'react/jsx-max-props-per-line': ['error', {
      maximum: 1,
      when: 'multiline'
    }],
    //如果JSX标签占用多行并且有多个属性，则第一个属性应始终放在新行上
    'react/jsx-first-prop-new-line': 'error',
    //如果JSX标签是多行的，则需要用小括号包裹，并且小括号需要换行
    'react/jsx-wrap-multilines': 'error',
    //没有子组件的标签需要写成自闭合标签
    'react/self-closing-comp': 'error',
    //标签属性写成多行时，结束标签另起一行，并且与包含开标签的行对齐
    'react/jsx-closing-bracket-location': ['error', 'line-aligned'],
    //生命周期方法不应该使用箭头函数，而是原型上的方法
    //'react/no-arrow-function-lifecycle': 'error',
    //组件props值为true时，可以忽略其值
    'react/jsx-boolean-value': 'error',
    //防止使用未知的DOM属性。在JSX中，所有的DOM属性和属性都应该使用CAMELCASE，与标准的DOMAPI保持一致。
    'react/no-unknown-property': 'error',
    /**
     *ReactHooks规则
     *@linkhttps://www.npmjs.com/package/eslint-plugin-react-hooks
     *@linkhttps://reactjs.org/docs/hooks-rules.html
     */
    //hooks调用规则
    'react-hooks/rules-of-hooks': 'warn'
  },
};
