This package has the following duplicate dependencies:

@mu/agreement:
[
  {
    name: '@mu/agreement',
    version: '1.5.22-nostyle.37',
    from: '@mu/agreement@1.5.22-nostyle.37',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/agreement',
    version: '1.5.22-nostyle.4',
    from: '@mu/agreement@1.5.22-nostyle.4',
    path: '@mumod/repayment/@mu/tarosdk-mu-bio-auth/@mu/bio-auth-zfb'
  }
] 

@mu/render-taro:
[
  {
    name: '@mu/render-taro',
    version: '1.4.0',
    from: '@mu/render-taro@1.4.0',
    path: '@mumod/repayment/@mu/agreement'
  },
  {
    name: '@mu/render-taro',
    version: '1.7.0-beta.1',
    from: '@mu/render-taro@1.7.0-beta.1',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/render-taro',
    version: '1.6.1',
    from: '@mu/render-taro@1.6.1',
    path: '@mumod/repayment/@mu/safe-sms-shell'
  },
  {
    name: '@mu/render-taro',
    version: '1.4.2',
    from: '@mu/render-taro@1.4.2',
    path: '@mumod/repayment/@mu/trade-password-encrypted-shell'
  }
] 

nervjs:
[
  {
    name: 'nervjs',
    version: '1.5.6',
    from: 'nervjs@1.5.6',
    path: '@mumod/repayment/@mu/agreement/@mu/render-taro'
  },
  {
    name: 'nervjs',
    version: '1.5.7',
    from: 'nervjs@1.5.7',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp'
  }
] 

@mu/bl:
[
  {
    name: '@mu/bl',
    version: '1.0.13-beta.1',
    from: '@mu/bl@1.0.13-beta.1',
    path: '@mumod/repayment/@mu/basic-library'
  },
  {
    name: '@mu/bl',
    version: '1.0.3-beta.4',
    from: '@mu/bl@1.0.3-beta.4',
    path: '@mumod/repayment/@mu/business-plugin'
  }
] 

@mu/business-basic:
[
  {
    name: '@mu/business-basic',
    version: '1.17.0-beta.17',
    from: '@mu/business-basic@1.17.0-beta.17',
    path: '@mumod/repayment/@mu/basic-library'
  },
  {
    name: '@mu/business-basic',
    version: '1.17.0-beta.16',
    from: '@mu/business-basic@1.17.0-beta.16',
    path: '@mumod/repayment/@mu/op-comp'
  }
] 

imagemin-svgo:
[
  {
    name: 'imagemin-svgo',
    version: '7.1.0',
    from: 'imagemin-svgo@7.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader'
  },
  {
    name: 'imagemin-svgo',
    version: '10.0.1',
    from: 'imagemin-svgo@10.0.1',
    path: '@mumod/repayment'
  }
] 

svgo:
[
  {
    name: 'svgo',
    version: '1.3.2',
    from: 'svgo@1.3.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo'
  },
  {
    name: 'svgo',
    version: '2.8.0',
    from: 'svgo@2.8.0',
    path: '@mumod/repayment/imagemin-svgo'
  }
] 

chalk:
[
  {
    name: 'chalk',
    version: '2.4.2',
    from: 'chalk@2.4.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/coa'
  },
  {
    name: 'chalk',
    version: '1.1.3',
    from: 'chalk@1.1.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/logalot/squeak'
  },
  {
    name: 'chalk',
    version: '4.1.2',
    from: 'chalk@4.1.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen'
  },
  {
    name: 'chalk',
    version: '3.0.0',
    from: 'chalk@3.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator'
  },
  {
    name: 'chalk',
    version: '2.4.1',
    from: 'chalk@^2.0.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/boxen'
  }
] 

ansi-styles:
[
  {
    name: 'ansi-styles',
    version: '3.2.1',
    from: 'ansi-styles@3.2.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/coa/chalk'
  },
  {
    name: 'ansi-styles',
    version: '4.3.0',
    from: 'ansi-styles@4.3.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen/chalk'
  },
  {
    name: 'ansi-styles',
    version: '2.2.1',
    from: 'ansi-styles@2.2.1',
    path: '@mumod/repayment/node-sass/chalk'
  }
] 

color-convert:
[
  {
    name: 'color-convert',
    version: '1.9.3',
    from: 'color-convert@1.9.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/coa/chalk/ansi-styles'
  },
  {
    name: 'color-convert',
    version: '2.0.1',
    from: 'color-convert@2.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen/chalk/ansi-styles'
  },
  {
    name: 'color-convert',
    version: '1.9.1',
    from: 'color-convert@^1.9.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/chalk/ansi-styles'
  }
] 

supports-color:
[
  {
    name: 'supports-color',
    version: '5.5.0',
    from: 'supports-color@5.5.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/coa/chalk'
  },
  {
    name: 'supports-color',
    version: '3.2.3',
    from: 'supports-color@3.2.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/postcss-pxtransform/postcss-pxtorem/postcss'
  },
  {
    name: 'supports-color',
    version: '7.2.0',
    from: 'supports-color@7.2.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen/chalk'
  },
  {
    name: 'supports-color',
    version: '5.4.0',
    from: 'supports-color@^5.3.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/chalk'
  },
  {
    name: 'supports-color',
    version: '2.0.0',
    from: 'supports-color@2.0.0',
    path: '@mumod/repayment/node-sass/chalk'
  }
] 

has-flag:
[
  {
    name: 'has-flag',
    version: '3.0.0',
    from: 'has-flag@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/coa/chalk/supports-color'
  },
  {
    name: 'has-flag',
    version: '1.0.0',
    from: 'has-flag@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/postcss-pxtransform/postcss-pxtorem/postcss/supports-color'
  },
  {
    name: 'has-flag',
    version: '4.0.0',
    from: 'has-flag@4.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen/chalk/supports-color'
  }
] 

css-select:
[
  {
    name: 'css-select',
    version: '2.1.0',
    from: 'css-select@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'css-select',
    version: '4.3.0',
    from: 'css-select@4.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid'
  }
] 

css-what:
[
  {
    name: 'css-what',
    version: '3.4.2',
    from: 'css-what@3.4.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select'
  },
  {
    name: 'css-what',
    version: '6.1.0',
    from: 'css-what@6.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid/css-select'
  }
] 

domutils:
[
  {
    name: 'domutils',
    version: '1.7.0',
    from: 'domutils@1.7.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select'
  },
  {
    name: 'domutils',
    version: '2.8.0',
    from: 'domutils@2.8.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid/htmlparser2'
  }
] 

dom-serializer:
[
  {
    name: 'dom-serializer',
    version: '0.2.2',
    from: 'dom-serializer@0.2.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select/domutils'
  },
  {
    name: 'dom-serializer',
    version: '1.4.1',
    from: 'dom-serializer@1.4.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid/htmlparser2/domutils'
  }
] 

domelementtype:
[
  {
    name: 'domelementtype',
    version: '2.3.0',
    from: 'domelementtype@2.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select/domutils/dom-serializer'
  },
  {
    name: 'domelementtype',
    version: '1.3.1',
    from: 'domelementtype@1.3.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select/domutils'
  }
] 

entities:
[
  {
    name: 'entities',
    version: '2.2.0',
    from: 'entities@2.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select/domutils/dom-serializer'
  },
  {
    name: 'entities',
    version: '1.1.2',
    from: 'entities@1.1.2',
    path: '@mumod/repayment/@mu/zui/@mu/mini-html-parser2'
  }
] 

nth-check:
[
  {
    name: 'nth-check',
    version: '1.0.2',
    from: 'nth-check@1.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-select'
  },
  {
    name: 'nth-check',
    version: '2.1.1',
    from: 'nth-check@2.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid/css-select'
  }
] 

css-tree:
[
  {
    name: 'css-tree',
    version: '1.0.0-alpha.37',
    from: 'css-tree@1.0.0-alpha.37',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'css-tree',
    version: '1.0.0-alpha.29',
    from: 'css-tree@1.0.0-alpha.29',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-csso/csso'
  },
  {
    name: 'css-tree',
    version: '1.1.3',
    from: 'css-tree@1.1.3',
    path: '@mumod/repayment/imagemin-svgo/svgo/csso'
  }
] 

mdn-data:
[
  {
    name: 'mdn-data',
    version: '2.0.4',
    from: 'mdn-data@2.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-tree'
  },
  {
    name: 'mdn-data',
    version: '1.1.4',
    from: 'mdn-data@1.1.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-csso/csso/css-tree'
  },
  {
    name: 'mdn-data',
    version: '2.0.14',
    from: 'mdn-data@2.0.14',
    path: '@mumod/repayment/imagemin-svgo/svgo/csso/css-tree'
  }
] 

source-map:
[
  {
    name: 'source-map',
    version: '0.6.1',
    from: 'source-map@0.6.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/css-tree'
  },
  {
    name: 'source-map',
    version: '0.5.7',
    from: 'source-map@0.5.7',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon'
  },
  {
    name: 'source-map',
    version: '0.7.4',
    from: 'source-map@0.7.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-stylus/stylus'
  },
  {
    name: 'source-map',
    version: '0.1.43',
    from: 'source-map@0.1.43',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/stylus'
  },
  {
    name: 'source-map',
    version: '0.2.0',
    from: 'source-map@0.2.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/escodegen-wallaby'
  },
  {
    name: 'source-map',
    version: '0.4.4',
    from: 'source-map@0.4.4',
    path: '@mumod/repayment/node-sass/sass-graph/scss-tokenizer'
  }
] 

csso:
[
  {
    name: 'csso',
    version: '4.2.0',
    from: 'csso@4.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'csso',
    version: '3.5.1',
    from: 'csso@3.5.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-csso'
  }
] 

sprintf-js:
[
  {
    name: 'sprintf-js',
    version: '1.0.3',
    from: 'sprintf-js@1.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/js-yaml/argparse'
  },
  {
    name: 'sprintf-js',
    version: '1.1.3',
    from: 'sprintf-js@1.1.3',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen/socks-proxy-agent/socks/ip-address'
  }
] 

esprima:
[
  {
    name: 'esprima',
    version: '4.0.1',
    from: 'esprima@4.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/js-yaml'
  },
  {
    name: 'esprima',
    version: '2.7.3',
    from: 'esprima@2.7.3',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/escodegen-wallaby'
  }
] 

mkdirp:
[
  {
    name: 'mkdirp',
    version: '0.5.6',
    from: 'mkdirp@0.5.6',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'mkdirp',
    version: '1.0.4',
    from: 'mkdirp@1.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-stylus/stylus'
  },
  {
    name: 'mkdirp',
    version: '0.5.1',
    from: 'mkdirp@0.5.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator'
  }
] 

define-properties:
[
  {
    name: 'define-properties',
    version: '1.2.1',
    from: 'define-properties@1.2.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/object.values'
  },
  {
    name: 'define-properties',
    version: '1.1.3',
    from: 'define-properties@1.1.3',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors'
  }
] 

sax:
[
  {
    name: 'sax',
    version: '1.2.4',
    from: 'sax@1.2.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'sax',
    version: '0.5.8',
    from: 'sax@0.5.8',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/stylus'
  }
] 

util.promisify:
[
  {
    name: 'util.promisify',
    version: '1.0.1',
    from: 'util.promisify@1.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo'
  },
  {
    name: 'util.promisify',
    version: '1.0.0',
    from: 'util.promisify@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin'
  }
] 

es-abstract:
[
  {
    name: 'es-abstract',
    version: '1.23.9',
    from: 'es-abstract@1.23.9',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify'
  },
  {
    name: 'es-abstract',
    version: '1.12.0',
    from: 'es-abstract@1.12.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors'
  }
] 

es-to-primitive:
[
  {
    name: 'es-to-primitive',
    version: '1.3.0',
    from: 'es-to-primitive@1.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify/es-abstract'
  },
  {
    name: 'es-to-primitive',
    version: '1.2.0',
    from: 'es-to-primitive@1.2.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors/es-abstract'
  }
] 

is-callable:
[
  {
    name: 'is-callable',
    version: '1.2.7',
    from: 'is-callable@1.2.7',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify/es-abstract/es-to-primitive'
  },
  {
    name: 'is-callable',
    version: '1.1.4',
    from: 'is-callable@1.1.4',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors/es-abstract/es-to-primitive'
  }
] 

is-date-object:
[
  {
    name: 'is-date-object',
    version: '1.1.0',
    from: 'is-date-object@1.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify/es-abstract/es-to-primitive'
  },
  {
    name: 'is-date-object',
    version: '1.0.1',
    from: 'is-date-object@1.0.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors/es-abstract/es-to-primitive'
  }
] 

is-symbol:
[
  {
    name: 'is-symbol',
    version: '1.1.1',
    from: 'is-symbol@1.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify/es-abstract/es-to-primitive'
  },
  {
    name: 'is-symbol',
    version: '1.0.2',
    from: 'is-symbol@1.0.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors/es-abstract/es-to-primitive'
  }
] 

has-symbols:
[
  {
    name: 'has-symbols',
    version: '1.1.0',
    from: 'has-symbols@1.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify/es-abstract/es-to-primitive/is-symbol'
  },
  {
    name: 'has-symbols',
    version: '1.0.0',
    from: 'has-symbols@1.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors/es-abstract/es-to-primitive/is-symbol'
  }
] 

is-regex:
[
  {
    name: 'is-regex',
    version: '1.2.1',
    from: 'is-regex@1.2.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify/es-abstract'
  },
  {
    name: 'is-regex',
    version: '1.0.4',
    from: 'is-regex@1.0.4',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors/es-abstract'
  }
] 

object-keys:
[
  {
    name: 'object-keys',
    version: '1.1.1',
    from: 'object-keys@1.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify/es-abstract'
  },
  {
    name: 'object-keys',
    version: '1.0.12',
    from: 'object-keys@1.0.12',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors/define-properties'
  }
] 

isarray:
[
  {
    name: 'isarray',
    version: '2.0.5',
    from: 'isarray@2.0.5',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify/es-abstract/safe-push-apply'
  },
  {
    name: 'isarray',
    version: '1.0.0',
    from: 'isarray@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/buffer'
  },
  {
    name: 'isarray',
    version: '0.0.1',
    from: 'isarray@0.0.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/sorted-union-stream/from2/readable-stream'
  }
] 

object.getownpropertydescriptors:
[
  {
    name: 'object.getownpropertydescriptors',
    version: '2.1.8',
    from: 'object.getownpropertydescriptors@2.1.8',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-svgo/svgo/util.promisify'
  },
  {
    name: 'object.getownpropertydescriptors',
    version: '2.0.3',
    from: 'object.getownpropertydescriptors@^2.0.3',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify'
  }
] 

file-type:
[
  {
    name: 'file-type',
    version: '12.4.2',
    from: 'file-type@12.4.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin'
  },
  {
    name: 'file-type',
    version: '5.2.0',
    from: 'file-type@5.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tar'
  },
  {
    name: 'file-type',
    version: '6.2.0',
    from: 'file-type@6.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tarbz2'
  },
  {
    name: 'file-type',
    version: '3.9.0',
    from: 'file-type@3.9.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-unzip'
  },
  {
    name: 'file-type',
    version: '4.4.0',
    from: 'file-type@4.4.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/archive-type'
  },
  {
    name: 'file-type',
    version: '8.1.0',
    from: 'file-type@8.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download'
  },
  {
    name: 'file-type',
    version: '10.11.0',
    from: 'file-type@10.11.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/is-gif'
  }
] 

globby:
[
  {
    name: 'globby',
    version: '10.0.2',
    from: 'globby@10.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin'
  },
  {
    name: 'globby',
    version: '7.1.1',
    from: 'globby@7.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin'
  },
  {
    name: 'globby',
    version: '6.1.0',
    from: 'globby@6.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/del'
  }
] 

array-union:
[
  {
    name: 'array-union',
    version: '2.1.0',
    from: 'array-union@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby'
  },
  {
    name: 'array-union',
    version: '1.0.2',
    from: 'array-union@1.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin/globby'
  }
] 

dir-glob:
[
  {
    name: 'dir-glob',
    version: '3.0.1',
    from: 'dir-glob@3.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby'
  },
  {
    name: 'dir-glob',
    version: '2.2.2',
    from: 'dir-glob@2.2.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin/globby'
  }
] 

path-type:
[
  {
    name: 'path-type',
    version: '4.0.0',
    from: 'path-type@4.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby/dir-glob'
  },
  {
    name: 'path-type',
    version: '3.0.0',
    from: 'path-type@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin/globby/dir-glob'
  },
  {
    name: 'path-type',
    version: '2.0.0',
    from: 'path-type@2.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs/read-pkg-up/read-pkg'
  },
  {
    name: 'path-type',
    version: '1.1.0',
    from: 'path-type@1.1.0',
    path: '@mumod/repayment/node-sass/meow/read-pkg-up/read-pkg'
  }
] 

glob-parent:
[
  {
    name: 'glob-parent',
    version: '5.1.2',
    from: 'glob-parent@5.1.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby/fast-glob'
  },
  {
    name: 'glob-parent',
    version: '3.1.0',
    from: 'glob-parent@3.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar'
  }
] 

micromatch:
[
  {
    name: 'micromatch',
    version: '4.0.8',
    from: 'micromatch@4.0.8',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby/fast-glob'
  },
  {
    name: 'micromatch',
    version: '3.1.10',
    from: 'micromatch@3.1.10',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch'
  }
] 

glob:
[
  {
    name: 'glob',
    version: '7.2.3',
    from: 'glob@7.2.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby'
  },
  {
    name: 'glob',
    version: '7.0.6',
    from: 'glob@7.0.6',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/stylus'
  },
  {
    name: 'glob',
    version: '8.1.0',
    from: 'glob@8.1.0',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/babel-plugin-module-resolver'
  },
  {
    name: 'glob',
    version: '7.1.7',
    from: 'glob@7.1.7',
    path: '@mumod/repayment/node-sass/gaze/globule'
  }
] 

ignore:
[
  {
    name: 'ignore',
    version: '5.3.2',
    from: 'ignore@5.3.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby'
  },
  {
    name: 'ignore',
    version: '3.3.10',
    from: 'ignore@3.3.10',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin/globby'
  }
] 

slash:
[
  {
    name: 'slash',
    version: '3.0.0',
    from: 'slash@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/globby'
  },
  {
    name: 'slash',
    version: '1.0.0',
    from: 'slash@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core'
  },
  {
    name: 'slash',
    version: '2.0.0',
    from: 'slash@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-include-assets-plugin'
  }
] 

graceful-fs:
[
  {
    name: 'graceful-fs',
    version: '4.2.11',
    from: 'graceful-fs@4.2.11',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin'
  },
  {
    name: 'graceful-fs',
    version: '4.2.10',
    from: 'graceful-fs@4.2.10',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/bin-links'
  }
] 

make-dir:
[
  {
    name: 'make-dir',
    version: '3.1.0',
    from: 'make-dir@3.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin'
  },
  {
    name: 'make-dir',
    version: '1.3.0',
    from: 'make-dir@1.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress'
  },
  {
    name: 'make-dir',
    version: '2.1.0',
    from: 'make-dir@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir'
  }
] 

semver:
[
  {
    name: 'semver',
    version: '6.3.1',
    from: 'semver@6.3.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin/make-dir'
  },
  {
    name: 'semver',
    version: '5.7.2',
    from: 'semver@5.7.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version/execa/cross-spawn'
  },
  {
    name: 'semver',
    version: '7.7.1',
    from: 'semver@7.7.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/git'
  },
  {
    name: 'semver',
    version: '5.7.1',
    from: 'semver@5.7.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/init-package-json'
  },
  {
    name: 'semver',
    version: '5.3.0',
    from: 'semver@5.3.0',
    path: '@mumod/repayment/node-sass/node-gyp'
  }
] 

execa:
[
  {
    name: 'execa',
    version: '0.7.0',
    from: 'execa@0.7.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/exec-buffer'
  },
  {
    name: 'execa',
    version: '1.0.0',
    from: 'execa@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version'
  },
  {
    name: 'execa',
    version: '0.10.0',
    from: 'execa@0.10.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-pngquant/pngquant-bin'
  }
] 

pify:
[
  {
    name: 'pify',
    version: '3.0.0',
    from: 'pify@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/exec-buffer'
  },
  {
    name: 'pify',
    version: '2.3.0',
    from: 'pify@2.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-unzip'
  },
  {
    name: 'pify',
    version: '4.0.1',
    from: 'pify@4.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper'
  }
] 

rimraf:
[
  {
    name: 'rimraf',
    version: '2.7.1',
    from: 'rimraf@2.7.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/exec-buffer'
  },
  {
    name: 'rimraf',
    version: '3.0.2',
    from: 'rimraf@3.0.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/run-script/node-gyp'
  }
] 

is-stream:
[
  {
    name: 'is-stream',
    version: '1.1.0',
    from: 'is-stream@1.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tar'
  },
  {
    name: 'is-stream',
    version: '2.0.1',
    from: 'is-stream@2.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-pngquant'
  }
] 

readable-stream:
[
  {
    name: 'readable-stream',
    version: '2.3.8',
    from: 'readable-stream@2.3.8',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tar/tar-stream/bl'
  },
  {
    name: 'readable-stream',
    version: '3.6.2',
    from: 'readable-stream@3.6.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/spdy/spdy-transport'
  },
  {
    name: 'readable-stream',
    version: '2.3.6',
    from: 'readable-stream@1 || 2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/fs-write-stream-atomic'
  },
  {
    name: 'readable-stream',
    version: '3.6.0',
    from: 'readable-stream@3.6.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm'
  },
  {
    name: 'readable-stream',
    version: '1.1.14',
    from: 'readable-stream@~1.1.10',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/sorted-union-stream/from2'
  }
] 

safe-buffer:
[
  {
    name: 'safe-buffer',
    version: '5.2.1',
    from: 'safe-buffer@5.2.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tar/tar-stream/bl'
  },
  {
    name: 'safe-buffer',
    version: '5.1.2',
    from: 'safe-buffer@~5.1.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/fs-write-stream-atomic/readable-stream'
  },
  {
    name: 'safe-buffer',
    version: '5.2.0',
    from: 'safe-buffer@~5.2.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/readable-stream/string_decoder'
  }
] 

end-of-stream:
[
  {
    name: 'end-of-stream',
    version: '1.4.4',
    from: 'end-of-stream@1.4.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tar/tar-stream'
  },
  {
    name: 'end-of-stream',
    version: '1.4.1',
    from: 'end-of-stream@^1.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mississippi/duplexify'
  }
] 

xtend:
[
  {
    name: 'xtend',
    version: '4.0.2',
    from: 'xtend@4.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tar/tar-stream'
  },
  {
    name: 'xtend',
    version: '4.0.1',
    from: 'xtend@~4.0.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mississippi/through2'
  }
] 

commander:
[
  {
    name: 'commander',
    version: '2.20.3',
    from: 'commander@2.20.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tarbz2/seek-bzip'
  },
  {
    name: 'commander',
    version: '2.17.1',
    from: 'commander@2.17.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/html-minifier'
  },
  {
    name: 'commander',
    version: '2.19.0',
    from: 'commander@2.19.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/html-minifier/uglify-js'
  },
  {
    name: 'commander',
    version: '2.14.1',
    from: 'commander@2.14.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin/uglify-es'
  },
  {
    name: 'commander',
    version: '4.0.1',
    from: 'commander@4.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator'
  },
  {
    name: 'commander',
    version: '7.2.0',
    from: 'commander@7.2.0',
    path: '@mumod/repayment/imagemin-svgo/svgo'
  }
] 

buffer:
[
  {
    name: 'buffer',
    version: '5.7.1',
    from: 'buffer@5.7.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-tarbz2/unbzip2-stream'
  },
  {
    name: 'buffer',
    version: '4.9.2',
    from: 'buffer@4.9.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser'
  }
] 

get-stream:
[
  {
    name: 'get-stream',
    version: '2.3.1',
    from: 'get-stream@2.3.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/decompress/decompress-unzip'
  },
  {
    name: 'get-stream',
    version: '3.0.0',
    from: 'get-stream@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download'
  },
  {
    name: 'get-stream',
    version: '4.1.0',
    from: 'get-stream@4.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version/execa'
  }
] 

download:
[
  {
    name: 'download',
    version: '6.2.5',
    from: 'download@6.2.5',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build'
  },
  {
    name: 'download',
    version: '7.1.0',
    from: 'download@7.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper'
  }
] 

mime-db:
[
  {
    name: 'mime-db',
    version: '1.54.0',
    from: 'mime-db@1.54.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/ext-name/ext-list'
  },
  {
    name: 'mime-db',
    version: '1.35.0',
    from: 'mime-db@~1.35.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/request/mime-types'
  },
  {
    name: 'mime-db',
    version: '1.52.0',
    from: 'mime-db@1.52.0',
    path: '@mumod/repayment/node-sass/request/mime-types'
  }
] 

sort-keys:
[
  {
    name: 'sort-keys',
    version: '1.1.2',
    from: 'sort-keys@1.1.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/ext-name/sort-keys-length'
  },
  {
    name: 'sort-keys',
    version: '2.0.0',
    from: 'sort-keys@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/cacheable-request/normalize-url'
  }
] 

got:
[
  {
    name: 'got',
    version: '7.1.0',
    from: 'got@7.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download'
  },
  {
    name: 'got',
    version: '8.3.2',
    from: 'got@8.3.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download'
  },
  {
    name: 'got',
    version: '6.7.1',
    from: 'got@^6.7.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/latest-version/package-json'
  }
] 

duplexer3:
[
  {
    name: 'duplexer3',
    version: '0.1.5',
    from: 'duplexer3@0.1.5',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got'
  },
  {
    name: 'duplexer3',
    version: '0.1.4',
    from: 'duplexer3@^0.1.4',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/latest-version/package-json/got'
  }
] 

lowercase-keys:
[
  {
    name: 'lowercase-keys',
    version: '1.0.1',
    from: 'lowercase-keys@1.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got'
  },
  {
    name: 'lowercase-keys',
    version: '1.0.0',
    from: 'lowercase-keys@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/cacheable-request'
  }
] 

p-cancelable:
[
  {
    name: 'p-cancelable',
    version: '0.3.0',
    from: 'p-cancelable@0.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got'
  },
  {
    name: 'p-cancelable',
    version: '0.4.1',
    from: 'p-cancelable@0.4.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got'
  }
] 

p-timeout:
[
  {
    name: 'p-timeout',
    version: '1.2.1',
    from: 'p-timeout@1.2.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got'
  },
  {
    name: 'p-timeout',
    version: '2.0.1',
    from: 'p-timeout@2.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got'
  }
] 

url-parse-lax:
[
  {
    name: 'url-parse-lax',
    version: '1.0.0',
    from: 'url-parse-lax@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got'
  },
  {
    name: 'url-parse-lax',
    version: '3.0.0',
    from: 'url-parse-lax@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got'
  }
] 

prepend-http:
[
  {
    name: 'prepend-http',
    version: '1.0.4',
    from: 'prepend-http@1.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download/got/url-parse-lax'
  },
  {
    name: 'prepend-http',
    version: '2.0.0',
    from: 'prepend-http@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/url-parse-lax'
  }
] 

p-event:
[
  {
    name: 'p-event',
    version: '1.3.0',
    from: 'p-event@1.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-build/download'
  },
  {
    name: 'p-event',
    version: '2.3.1',
    from: 'p-event@2.3.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download'
  }
] 

cross-spawn:
[
  {
    name: 'cross-spawn',
    version: '6.0.6',
    from: 'cross-spawn@6.0.6',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version/execa'
  },
  {
    name: 'cross-spawn',
    version: '5.1.0',
    from: 'cross-spawn@5.1.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs/os-locale/execa'
  },
  {
    name: 'cross-spawn',
    version: '3.0.1',
    from: 'cross-spawn@3.0.1',
    path: '@mumod/repayment/node-sass'
  }
] 

which:
[
  {
    name: 'which',
    version: '1.3.1',
    from: 'which@1.3.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version/execa/cross-spawn'
  },
  {
    name: 'which',
    version: '2.0.2',
    from: 'which@2.0.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/git'
  }
] 

pump:
[
  {
    name: 'pump',
    version: '3.0.2',
    from: 'pump@3.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version/execa/get-stream'
  },
  {
    name: 'pump',
    version: '2.0.1',
    from: 'pump@2.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/mississippi/pumpify'
  },
  {
    name: 'pump',
    version: '3.0.0',
    from: 'pump@^3.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/libnpmaccess/get-stream'
  }
] 

signal-exit:
[
  {
    name: 'signal-exit',
    version: '3.0.7',
    from: 'signal-exit@3.0.7',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/bin-version-check/bin-version/execa'
  },
  {
    name: 'signal-exit',
    version: '3.0.2',
    from: 'signal-exit@^3.0.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/lock-verify/@iarna/cli'
  }
] 

http-cache-semantics:
[
  {
    name: 'http-cache-semantics',
    version: '3.8.1',
    from: 'http-cache-semantics@3.8.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/cacheable-request'
  },
  {
    name: 'http-cache-semantics',
    version: '4.1.1',
    from: 'http-cache-semantics@4.1.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen'
  }
] 

query-string:
[
  {
    name: 'query-string',
    version: '5.1.1',
    from: 'query-string@5.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/cacheable-request/normalize-url'
  },
  {
    name: 'query-string',
    version: '6.14.1',
    from: 'query-string@6',
    path: '@mumod/repayment/find-duplicate-dependencies/npm'
  }
] 

strict-uri-encode:
[
  {
    name: 'strict-uri-encode',
    version: '1.1.0',
    from: 'strict-uri-encode@1.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/cacheable-request/normalize-url/query-string'
  },
  {
    name: 'strict-uri-encode',
    version: '2.0.0',
    from: 'strict-uri-encode@^2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/query-string'
  }
] 

from2:
[
  {
    name: 'from2',
    version: '2.3.0',
    from: 'from2@2.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/into-stream'
  },
  {
    name: 'from2',
    version: '1.3.0',
    from: 'from2@^1.3.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/sorted-union-stream'
  }
] 

p-is-promise:
[
  {
    name: 'p-is-promise',
    version: '1.1.0',
    from: 'p-is-promise@1.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper/download/got/into-stream'
  },
  {
    name: 'p-is-promise',
    version: '2.1.0',
    from: 'p-is-promise@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/os-locale/mem'
  }
] 

import-lazy:
[
  {
    name: 'import-lazy',
    version: '3.1.0',
    from: 'import-lazy@3.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/bin-wrapper'
  },
  {
    name: 'import-lazy',
    version: '2.1.0',
    from: 'import-lazy@^2.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier'
  }
] 

figures:
[
  {
    name: 'figures',
    version: '1.7.0',
    from: 'figures@1.7.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/logalot'
  },
  {
    name: 'figures',
    version: '2.0.0',
    from: 'figures@2.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/opencollective/inquirer'
  }
] 

indent-string:
[
  {
    name: 'indent-string',
    version: '2.1.0',
    from: 'indent-string@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/logalot/squeak/lpad-align'
  },
  {
    name: 'indent-string',
    version: '4.0.0',
    from: 'indent-string@4.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/cacache/p-map/aggregate-error'
  },
  {
    name: 'indent-string',
    version: '3.2.0',
    from: 'indent-string@3.2.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/conventional-changelog-writer/meow/redent'
  }
] 

meow:
[
  {
    name: 'meow',
    version: '3.7.0',
    from: 'meow@3.7.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-gifsicle/gifsicle/logalot/squeak/lpad-align'
  },
  {
    name: 'meow',
    version: '4.0.1',
    from: 'meow@4.0.1',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/conventional-changelog-writer'
  }
] 

type-fest:
[
  {
    name: 'type-fest',
    version: '0.5.2',
    from: 'type-fest@0.5.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/imagemin-pngquant/ow'
  },
  {
    name: 'type-fest',
    version: '0.20.2',
    from: 'type-fest@0.20.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen'
  }
] 

loader-utils:
[
  {
    name: 'loader-utils',
    version: '1.4.2',
    from: 'loader-utils@1.4.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader'
  },
  {
    name: 'loader-utils',
    version: '0.2.17',
    from: 'loader-utils@0.2.17',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin'
  }
] 

big.js:
[
  {
    name: 'big.js',
    version: '5.2.2',
    from: 'big.js@5.2.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/loader-utils'
  },
  {
    name: 'big.js',
    version: '3.2.0',
    from: 'big.js@3.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/loader-utils'
  }
] 

emojis-list:
[
  {
    name: 'emojis-list',
    version: '3.0.0',
    from: 'emojis-list@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/loader-utils'
  },
  {
    name: 'emojis-list',
    version: '2.1.0',
    from: 'emojis-list@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/loader-utils'
  }
] 

json5:
[
  {
    name: 'json5',
    version: '1.0.2',
    from: 'json5@1.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/image-webpack-loader/loader-utils'
  },
  {
    name: 'json5',
    version: '0.5.1',
    from: 'json5@0.5.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core'
  },
  {
    name: 'json5',
    version: '2.2.3',
    from: 'json5@2.2.3',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/@babel/core'
  }
] 

debug:
[
  {
    name: 'debug',
    version: '4.4.0',
    from: 'debug@4.4.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/script-ext-html-webpack-plugin'
  },
  {
    name: 'debug',
    version: '2.6.9',
    from: 'debug@2.6.9',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/extglob/expand-brackets'
  },
  {
    name: 'debug',
    version: '3.1.0',
    from: 'debug@3.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-stylus/stylus'
  },
  {
    name: 'debug',
    version: '3.2.7',
    from: 'debug@3.2.7',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/sockjs-client'
  }
] 

ms:
[
  {
    name: 'ms',
    version: '2.1.3',
    from: 'ms@2.1.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/script-ext-html-webpack-plugin/debug'
  },
  {
    name: 'ms',
    version: '2.0.0',
    from: 'ms@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/extglob/expand-brackets/debug'
  },
  {
    name: 'ms',
    version: '2.1.1',
    from: 'ms@^2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen/agentkeepalive/humanize-ms'
  }
] 

cacache:
[
  {
    name: 'cacache',
    version: '12.0.4',
    from: 'cacache@12.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin'
  },
  {
    name: 'cacache',
    version: '10.0.4',
    from: 'cacache@10.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin'
  },
  {
    name: 'cacache',
    version: '15.3.0',
    from: 'cacache@15.3.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen'
  }
] 

lru-cache:
[
  {
    name: 'lru-cache',
    version: '5.1.1',
    from: 'lru-cache@5.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache'
  },
  {
    name: 'lru-cache',
    version: '4.1.5',
    from: 'lru-cache@4.1.5',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin/cacache'
  },
  {
    name: 'lru-cache',
    version: '6.0.0',
    from: 'lru-cache@6.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/git'
  }
] 

yallist:
[
  {
    name: 'yallist',
    version: '3.1.1',
    from: 'yallist@3.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/lru-cache'
  },
  {
    name: 'yallist',
    version: '4.0.0',
    from: 'yallist@4.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/git/lru-cache'
  },
  {
    name: 'yallist',
    version: '3.0.3',
    from: 'yallist@^3.0.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/lru-cache'
  },
  {
    name: 'yallist',
    version: '2.1.2',
    from: 'yallist@^2.1.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/boxen/term-size/execa/cross-spawn/lru-cache'
  }
] 

chownr:
[
  {
    name: 'chownr',
    version: '1.1.4',
    from: 'chownr@1.1.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache'
  },
  {
    name: 'chownr',
    version: '2.0.0',
    from: 'chownr@2.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/run-script/node-gyp/tar'
  }
] 

mississippi:
[
  {
    name: 'mississippi',
    version: '3.0.0',
    from: 'mississippi@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache'
  },
  {
    name: 'mississippi',
    version: '2.0.0',
    from: 'mississippi@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin/cacache'
  }
] 

duplexify:
[
  {
    name: 'duplexify',
    version: '3.7.1',
    from: 'duplexify@3.7.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/mississippi'
  },
  {
    name: 'duplexify',
    version: '3.6.0',
    from: 'duplexify@^3.4.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mississippi'
  }
] 

inherits:
[
  {
    name: 'inherits',
    version: '2.0.4',
    from: 'inherits@2.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/mississippi/duplexify'
  },
  {
    name: 'inherits',
    version: '2.0.3',
    from: 'inherits@2.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/assert/util'
  }
] 

stream-shift:
[
  {
    name: 'stream-shift',
    version: '1.0.3',
    from: 'stream-shift@1.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/mississippi/duplexify'
  },
  {
    name: 'stream-shift',
    version: '1.0.0',
    from: 'stream-shift@^1.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mississippi/duplexify'
  }
] 

flush-write-stream:
[
  {
    name: 'flush-write-stream',
    version: '1.1.1',
    from: 'flush-write-stream@1.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/mississippi'
  },
  {
    name: 'flush-write-stream',
    version: '1.0.3',
    from: 'flush-write-stream@^1.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mississippi'
  }
] 

parallel-transform:
[
  {
    name: 'parallel-transform',
    version: '1.2.0',
    from: 'parallel-transform@1.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/mississippi'
  },
  {
    name: 'parallel-transform',
    version: '1.1.0',
    from: 'parallel-transform@^1.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mississippi'
  }
] 

cyclist:
[
  {
    name: 'cyclist',
    version: '1.0.2',
    from: 'cyclist@1.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/mississippi/parallel-transform'
  },
  {
    name: 'cyclist',
    version: '0.2.2',
    from: 'cyclist@~0.2.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mississippi/parallel-transform'
  }
] 

stream-each:
[
  {
    name: 'stream-each',
    version: '1.2.3',
    from: 'stream-each@1.2.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/mississippi'
  },
  {
    name: 'stream-each',
    version: '1.2.2',
    from: 'stream-each@^1.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mississippi'
  }
] 

through2:
[
  {
    name: 'through2',
    version: '2.0.5',
    from: 'through2@2.0.5',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/mississippi'
  },
  {
    name: 'through2',
    version: '2.0.3',
    from: 'through2@^2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mississippi'
  }
] 

aproba:
[
  {
    name: 'aproba',
    version: '1.2.0',
    from: 'aproba@1.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/move-concurrently'
  },
  {
    name: 'aproba',
    version: '2.0.0',
    from: 'aproba@2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm'
  }
] 

iferr:
[
  {
    name: 'iferr',
    version: '0.1.5',
    from: 'iferr@0.1.5',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/move-concurrently/copy-concurrently'
  },
  {
    name: 'iferr',
    version: '1.0.2',
    from: 'iferr@1.0.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm'
  }
] 

ssri:
[
  {
    name: 'ssri',
    version: '6.0.2',
    from: 'ssri@6.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache'
  },
  {
    name: 'ssri',
    version: '5.3.0',
    from: 'ssri@5.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin/cacache'
  },
  {
    name: 'ssri',
    version: '8.0.1',
    from: 'ssri@8.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen/cacache'
  }
] 

unique-slug:
[
  {
    name: 'unique-slug',
    version: '2.0.2',
    from: 'unique-slug@2.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache/unique-filename'
  },
  {
    name: 'unique-slug',
    version: '2.0.0',
    from: 'unique-slug@^2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/unique-filename'
  }
] 

y18n:
[
  {
    name: 'y18n',
    version: '4.0.3',
    from: 'y18n@4.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/cacache'
  },
  {
    name: 'y18n',
    version: '3.2.2',
    from: 'y18n@3.2.2',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs'
  },
  {
    name: 'y18n',
    version: '4.0.1',
    from: 'y18n@4.0.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/cacache'
  }
] 

find-cache-dir:
[
  {
    name: 'find-cache-dir',
    version: '2.1.0',
    from: 'find-cache-dir@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin'
  },
  {
    name: 'find-cache-dir',
    version: '1.0.0',
    from: 'find-cache-dir@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader'
  }
] 

pkg-dir:
[
  {
    name: 'pkg-dir',
    version: '3.0.0',
    from: 'pkg-dir@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir'
  },
  {
    name: 'pkg-dir',
    version: '2.0.0',
    from: 'pkg-dir@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir'
  }
] 

find-up:
[
  {
    name: 'find-up',
    version: '3.0.0',
    from: 'find-up@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir/pkg-dir'
  },
  {
    name: 'find-up',
    version: '2.1.0',
    from: 'find-up@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir'
  },
  {
    name: 'find-up',
    version: '1.1.2',
    from: 'find-up@1.1.2',
    path: '@mumod/repayment/node-sass/meow/read-pkg-up'
  }
] 

locate-path:
[
  {
    name: 'locate-path',
    version: '3.0.0',
    from: 'locate-path@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/find-cache-dir/pkg-dir/find-up'
  },
  {
    name: 'locate-path',
    version: '2.0.0',
    from: 'locate-path@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir/find-up'
  }
] 

schema-utils:
[
  {
    name: 'schema-utils',
    version: '1.0.0',
    from: 'schema-utils@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin'
  },
  {
    name: 'schema-utils',
    version: '0.4.7',
    from: 'schema-utils@0.4.7',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack'
  }
] 

serialize-javascript:
[
  {
    name: 'serialize-javascript',
    version: '4.0.0',
    from: 'serialize-javascript@4.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin'
  },
  {
    name: 'serialize-javascript',
    version: '1.9.1',
    from: 'serialize-javascript@1.9.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/uglifyjs-webpack-plugin'
  }
] 

terser:
[
  {
    name: 'terser',
    version: '4.8.1',
    from: 'terser@4.8.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin'
  },
  {
    name: 'terser',
    version: '5.39.0',
    from: 'terser@5.39.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize'
  }
] 

source-map-support:
[
  {
    name: 'source-map-support',
    version: '0.5.21',
    from: 'source-map-support@0.5.21',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/terser'
  },
  {
    name: 'source-map-support',
    version: '0.4.18',
    from: 'source-map-support@0.4.18',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-register'
  },
  {
    name: 'source-map-support',
    version: '0.5.16',
    from: 'source-map-support@0.5.16',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator'
  }
] 

errno:
[
  {
    name: 'errno',
    version: '0.1.8',
    from: 'errno@0.1.8',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/terser-webpack-plugin/worker-farm'
  },
  {
    name: 'errno',
    version: '0.1.7',
    from: 'errno@~0.1.7',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/worker-farm'
  }
] 

acorn:
[
  {
    name: 'acorn',
    version: '5.7.4',
    from: 'acorn@5.7.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack'
  },
  {
    name: 'acorn',
    version: '8.14.1',
    from: 'acorn@8.14.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/terser'
  },
  {
    name: 'acorn',
    version: undefined,
    from: undefined,
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/espree'
  }
] 

fast-json-stable-stringify:
[
  {
    name: 'fast-json-stable-stringify',
    version: '2.1.0',
    from: 'fast-json-stable-stringify@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/ajv'
  },
  {
    name: 'fast-json-stable-stringify',
    version: '2.0.0',
    from: 'fast-json-stable-stringify@^2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/request/har-validator/ajv'
  }
] 

punycode:
[
  {
    name: 'punycode',
    version: '2.3.1',
    from: 'punycode@2.3.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/ajv/uri-js'
  },
  {
    name: 'punycode',
    version: '1.4.1',
    from: 'punycode@1.4.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser'
  },
  {
    name: 'punycode',
    version: '2.1.1',
    from: 'punycode@^2.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/request/har-validator/ajv/uri-js'
  }
] 

memory-fs:
[
  {
    name: 'memory-fs',
    version: '0.5.0',
    from: 'memory-fs@0.5.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/enhanced-resolve'
  },
  {
    name: 'memory-fs',
    version: '0.4.1',
    from: 'memory-fs@0.4.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack'
  }
] 

estraverse:
[
  {
    name: 'estraverse',
    version: '5.3.0',
    from: 'estraverse@5.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/eslint-scope/esrecurse'
  },
  {
    name: 'estraverse',
    version: '4.3.0',
    from: 'estraverse@4.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/eslint-scope'
  },
  {
    name: 'estraverse',
    version: '1.9.3',
    from: 'estraverse@1.9.3',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/escodegen-wallaby'
  }
] 

util:
[
  {
    name: 'util',
    version: '0.10.4',
    from: 'util@0.10.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/assert'
  },
  {
    name: 'util',
    version: '0.11.1',
    from: 'util@0.11.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser'
  }
] 

bn.js:
[
  {
    name: 'bn.js',
    version: '5.2.1',
    from: 'bn.js@5.2.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/crypto-browserify/browserify-sign'
  },
  {
    name: 'bn.js',
    version: '4.12.1',
    from: 'bn.js@4.12.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/crypto-browserify/browserify-sign/elliptic'
  }
] 

string_decoder:
[
  {
    name: 'string_decoder',
    version: '1.1.1',
    from: 'string_decoder@1.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser'
  },
  {
    name: 'string_decoder',
    version: '1.3.0',
    from: 'string_decoder@^1.1.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/readable-stream'
  },
  {
    name: 'string_decoder',
    version: '0.10.31',
    from: 'string_decoder@~0.10.x',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/sorted-union-stream/from2/readable-stream'
  }
] 

qs:
[
  {
    name: 'qs',
    version: '6.14.0',
    from: 'qs@6.14.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/node-libs-browser/url'
  },
  {
    name: 'qs',
    version: '6.13.0',
    from: 'qs@6.13.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/body-parser'
  },
  {
    name: 'qs',
    version: '6.5.3',
    from: 'qs@~6.5.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/request'
  }
] 

chokidar:
[
  {
    name: 'chokidar',
    version: '3.6.0',
    from: 'chokidar@3.6.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack'
  },
  {
    name: 'chokidar',
    version: '2.1.8',
    from: 'chokidar@2.1.8',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2'
  },
  {
    name: 'chokidar',
    version: '4.0.3',
    from: 'chokidar@4.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/sass'
  }
] 

anymatch:
[
  {
    name: 'anymatch',
    version: '3.1.3',
    from: 'anymatch@3.1.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar'
  },
  {
    name: 'anymatch',
    version: '2.0.0',
    from: 'anymatch@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar'
  }
] 

normalize-path:
[
  {
    name: 'normalize-path',
    version: '3.0.0',
    from: 'normalize-path@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/anymatch'
  },
  {
    name: 'normalize-path',
    version: '2.1.1',
    from: 'normalize-path@2.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch'
  }
] 

braces:
[
  {
    name: 'braces',
    version: '3.0.3',
    from: 'braces@3.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar'
  },
  {
    name: 'braces',
    version: '2.3.2',
    from: 'braces@2.3.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch/micromatch'
  }
] 

fill-range:
[
  {
    name: 'fill-range',
    version: '7.1.1',
    from: 'fill-range@7.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/braces'
  },
  {
    name: 'fill-range',
    version: '4.0.0',
    from: 'fill-range@4.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces'
  }
] 

to-regex-range:
[
  {
    name: 'to-regex-range',
    version: '5.0.1',
    from: 'to-regex-range@5.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/braces/fill-range'
  },
  {
    name: 'to-regex-range',
    version: '2.1.1',
    from: 'to-regex-range@2.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces/fill-range'
  }
] 

is-number:
[
  {
    name: 'is-number',
    version: '7.0.0',
    from: 'is-number@7.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/braces/fill-range/to-regex-range'
  },
  {
    name: 'is-number',
    version: '3.0.0',
    from: 'is-number@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces/fill-range'
  }
] 

fsevents:
[
  {
    name: 'fsevents',
    version: '2.3.3',
    from: 'fsevents@2.3.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar'
  },
  {
    name: 'fsevents',
    version: '1.2.13',
    from: 'fsevents@1.2.13',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar'
  }
] 

is-glob:
[
  {
    name: 'is-glob',
    version: '4.0.3',
    from: 'is-glob@4.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/glob-parent'
  },
  {
    name: 'is-glob',
    version: '3.1.0',
    from: 'is-glob@3.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/glob-parent'
  }
] 

is-binary-path:
[
  {
    name: 'is-binary-path',
    version: '2.1.0',
    from: 'is-binary-path@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar'
  },
  {
    name: 'is-binary-path',
    version: '1.0.1',
    from: 'is-binary-path@1.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar'
  }
] 

binary-extensions:
[
  {
    name: 'binary-extensions',
    version: '2.3.0',
    from: 'binary-extensions@2.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar/is-binary-path'
  },
  {
    name: 'binary-extensions',
    version: '1.13.1',
    from: 'binary-extensions@1.13.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/is-binary-path'
  }
] 

readdirp:
[
  {
    name: 'readdirp',
    version: '3.6.0',
    from: 'readdirp@3.6.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/chokidar'
  },
  {
    name: 'readdirp',
    version: '2.2.1',
    from: 'readdirp@2.2.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar'
  },
  {
    name: 'readdirp',
    version: '4.1.2',
    from: 'readdirp@4.1.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/sass/chokidar'
  }
] 

define-property:
[
  {
    name: 'define-property',
    version: '2.0.2',
    from: 'define-property@2.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch/micromatch'
  },
  {
    name: 'define-property',
    version: '0.2.5',
    from: 'define-property@0.2.5',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/extglob/expand-brackets'
  },
  {
    name: 'define-property',
    version: '1.0.0',
    from: 'define-property@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/extglob'
  }
] 

extend-shallow:
[
  {
    name: 'extend-shallow',
    version: '3.0.2',
    from: 'extend-shallow@3.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch/micromatch'
  },
  {
    name: 'extend-shallow',
    version: '2.0.1',
    from: 'extend-shallow@2.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces'
  }
] 

kind-of:
[
  {
    name: 'kind-of',
    version: '6.0.3',
    from: 'kind-of@6.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/anymatch/micromatch'
  },
  {
    name: 'kind-of',
    version: '3.2.2',
    from: 'kind-of@3.2.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces/fill-range/is-number'
  },
  {
    name: 'kind-of',
    version: '4.0.0',
    from: 'kind-of@4.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base/has-value/has-values'
  },
  {
    name: 'kind-of',
    version: '5.1.0',
    from: 'kind-of@5.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/sass-loader/clone-deep/shallow-clone'
  }
] 

isobject:
[
  {
    name: 'isobject',
    version: '3.0.1',
    from: 'isobject@3.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces'
  },
  {
    name: 'isobject',
    version: '2.1.0',
    from: 'isobject@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base/unset-value/has-value'
  }
] 

is-extendable:
[
  {
    name: 'is-extendable',
    version: '0.1.1',
    from: 'is-extendable@0.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/watchpack/watchpack-chokidar2/chokidar/braces/extend-shallow'
  },
  {
    name: 'is-extendable',
    version: '1.0.1',
    from: 'is-extendable@1.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/extend-shallow'
  }
] 

is-descriptor:
[
  {
    name: 'is-descriptor',
    version: '1.0.3',
    from: 'is-descriptor@1.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/define-property'
  },
  {
    name: 'is-descriptor',
    version: '0.1.7',
    from: 'is-descriptor@0.1.7',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/extglob/expand-brackets/define-property'
  }
] 

has-value:
[
  {
    name: 'has-value',
    version: '1.0.0',
    from: 'has-value@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base'
  },
  {
    name: 'has-value',
    version: '0.3.1',
    from: 'has-value@0.3.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base/unset-value'
  }
] 

has-values:
[
  {
    name: 'has-values',
    version: '1.0.0',
    from: 'has-values@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base/has-value'
  },
  {
    name: 'has-values',
    version: '0.1.4',
    from: 'has-values@0.1.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/cache-base/unset-value/has-value'
  }
] 

for-in:
[
  {
    name: 'for-in',
    version: '1.0.2',
    from: 'for-in@1.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/business-basic/webpack/micromatch/snapdragon/base/mixin-deep'
  },
  {
    name: 'for-in',
    version: '0.1.8',
    from: 'for-in@0.1.8',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/sass-loader/clone-deep/shallow-clone/mixin-object'
  }
] 

intersection-observer:
[
  {
    name: 'intersection-observer',
    version: '0.12.0',
    from: 'intersection-observer@0.12.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/leda'
  },
  {
    name: 'intersection-observer',
    version: '0.7.0',
    from: 'intersection-observer@0.7.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/components'
  }
] 

browserslist:
[
  {
    name: 'browserslist',
    version: '4.24.4',
    from: 'browserslist@4.24.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@babel/plugin-proposal-object-rest-spread/@babel/helper-compilation-targets'
  },
  {
    name: 'browserslist',
    version: '3.2.8',
    from: 'browserslist@3.2.8',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/autoprefixer'
  }
] 

@babel/preset-env:
[
  {
    name: '@babel/preset-env',
    version: '7.26.9',
    from: '@babel/preset-env@7.26.9',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp'
  },
  {
    name: '@babel/preset-env',
    version: '7.22.7',
    from: '@babel/preset-env@7.22.7',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils'
  }
] 

jsesc:
[
  {
    name: 'jsesc',
    version: '3.0.2',
    from: 'jsesc@3.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@babel/preset-env/@babel/plugin-syntax-unicode-sets-regex/@babel/helper-create-regexp-features-plugin/regexpu-core/regjsparser'
  },
  {
    name: 'jsesc',
    version: '1.3.0',
    from: 'jsesc@1.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-generator'
  },
  {
    name: 'jsesc',
    version: '3.1.0',
    from: 'jsesc@3.1.0',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/@babel/core/@babel/generator'
  }
] 

globals:
[
  {
    name: 'globals',
    version: '11.12.0',
    from: 'globals@11.12.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@babel/preset-env/@babel/plugin-transform-classes'
  },
  {
    name: 'globals',
    version: '9.18.0',
    from: 'globals@9.18.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-traverse'
  }
] 

@babel/runtime:
[
  {
    name: '@babel/runtime',
    version: '7.27.0',
    from: '@babel/runtime@7.27.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@babel/preset-env/@babel/plugin-transform-regenerator/regenerator-transform'
  },
  {
    name: '@babel/runtime',
    version: '7.7.4',
    from: '@babel/runtime@7.7.4',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator'
  }
] 

@babel/preset-modules:
[
  {
    name: '@babel/preset-modules',
    version: '0.1.6-no-external-plugins',
    from: '@babel/preset-modules@0.1.6-no-external-plugins',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@babel/preset-env'
  },
  {
    name: '@babel/preset-modules',
    version: '0.1.6',
    from: '@babel/preset-modules@0.1.6',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/@babel/preset-env'
  }
] 

@babel/helper-define-polyfill-provider:
[
  {
    name: '@babel/helper-define-polyfill-provider',
    version: '0.6.4',
    from: '@babel/helper-define-polyfill-provider@0.6.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@babel/preset-env/babel-plugin-polyfill-corejs2'
  },
  {
    name: '@babel/helper-define-polyfill-provider',
    version: '0.4.4',
    from: '@babel/helper-define-polyfill-provider@0.4.4',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/@babel/preset-env/babel-plugin-polyfill-corejs3'
  },
  {
    name: '@babel/helper-define-polyfill-provider',
    version: '0.5.0',
    from: '@babel/helper-define-polyfill-provider@0.5.0',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/@babel/preset-env/babel-plugin-polyfill-regenerator'
  }
] 

resolve:
[
  {
    name: 'resolve',
    version: '1.22.10',
    from: 'resolve@1.22.10',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@babel/preset-env/babel-plugin-polyfill-corejs2/@babel/helper-define-polyfill-provider'
  },
  {
    name: 'resolve',
    version: '1.8.1',
    from: 'resolve@1.8.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner'
  },
  {
    name: 'resolve',
    version: '1.10.0',
    from: 'resolve@^1.10.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/normalize-package-data'
  }
] 

babel-plugin-polyfill-corejs3:
[
  {
    name: 'babel-plugin-polyfill-corejs3',
    version: '0.11.1',
    from: 'babel-plugin-polyfill-corejs3@0.11.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@babel/preset-env'
  },
  {
    name: 'babel-plugin-polyfill-corejs3',
    version: '0.8.7',
    from: 'babel-plugin-polyfill-corejs3@0.8.7',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/@babel/preset-env'
  }
] 

babel-plugin-polyfill-regenerator:
[
  {
    name: 'babel-plugin-polyfill-regenerator',
    version: '0.6.4',
    from: 'babel-plugin-polyfill-regenerator@0.6.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@babel/preset-env'
  },
  {
    name: 'babel-plugin-polyfill-regenerator',
    version: '0.5.5',
    from: 'babel-plugin-polyfill-regenerator@0.5.5',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/@babel/preset-env'
  }
] 

whatwg-fetch:
[
  {
    name: 'whatwg-fetch',
    version: '3.0.0',
    from: 'whatwg-fetch@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@mu/madp-fetch'
  },
  {
    name: 'whatwg-fetch',
    version: '2.0.4',
    from: 'whatwg-fetch@2.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/taro-h5'
  }
] 

@tarojs/async-await:
[
  {
    name: '@tarojs/async-await',
    version: '1.3.45',
    from: '@tarojs/async-await@1.3.45',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp'
  },
  {
    name: '@tarojs/async-await',
    version: '2.2.10',
    from: '@tarojs/async-await@2.2.10',
    path: '@mumod/repayment/@mu/chat-entry-component'
  }
] 

regenerator-runtime:
[
  {
    name: 'regenerator-runtime',
    version: '0.11.1',
    from: 'regenerator-runtime@0.11.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/async-await'
  },
  {
    name: 'regenerator-runtime',
    version: '0.14.1',
    from: 'regenerator-runtime@0.14.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/@babel/runtime'
  },
  {
    name: 'regenerator-runtime',
    version: '0.13.11',
    from: 'regenerator-runtime@0.13.11',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/@babel/runtime'
  },
  {
    name: 'regenerator-runtime',
    version: '0.10.5',
    from: 'regenerator-runtime@0.10.5',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/opencollective/babel-polyfill'
  }
] 

classnames:
[
  {
    name: 'classnames',
    version: '2.5.1',
    from: 'classnames@2.5.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/components'
  },
  {
    name: 'classnames',
    version: '2.2.6',
    from: 'classnames@2.2.6',
    path: '@mumod/repayment/@mu/taro-adv/@mu/zui'
  }
] 

resolve-pathname:
[
  {
    name: 'resolve-pathname',
    version: '3.0.0',
    from: 'resolve-pathname@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/components'
  },
  {
    name: 'resolve-pathname',
    version: '2.2.0',
    from: 'resolve-pathname@2.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/router'
  }
] 

ssr-window:
[
  {
    name: 'ssr-window',
    version: '1.0.1',
    from: 'ssr-window@1.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/components/swiper'
  },
  {
    name: 'ssr-window',
    version: '2.0.0',
    from: 'ssr-window@2.0.0',
    path: '@mumod/repayment/@mu/swiper-custom/dom7'
  }
] 

babel-core:
[
  {
    name: 'babel-core',
    version: '6.26.3',
    from: 'babel-core@6.26.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel'
  },
  {
    name: 'babel-core',
    version: '6.26.0',
    from: 'babel-core@6.26.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner'
  }
] 

js-tokens:
[
  {
    name: 'js-tokens',
    version: '3.0.2',
    from: 'js-tokens@3.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-code-frame'
  },
  {
    name: 'js-tokens',
    version: '4.0.0',
    from: 'js-tokens@4.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-terser/@babel/code-frame'
  }
] 

detect-indent:
[
  {
    name: 'detect-indent',
    version: '4.0.0',
    from: 'detect-indent@4.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-generator'
  },
  {
    name: 'detect-indent',
    version: '5.0.0',
    from: 'detect-indent@5.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm'
  }
] 

lodash:
[
  {
    name: 'lodash',
    version: '4.17.21',
    from: 'lodash@4.17.21',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core/babel-generator'
  },
  {
    name: 'lodash',
    version: '4.17.13',
    from: 'lodash@4.17.13',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/router'
  },
  {
    name: 'lodash',
    version: '4.17.15',
    from: 'lodash@4.17.15',
    path: '@mumod/repayment/@mu/taro-adv/@mu/zui'
  }
] 

convert-source-map:
[
  {
    name: 'convert-source-map',
    version: '1.9.0',
    from: 'convert-source-map@1.9.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core'
  },
  {
    name: 'convert-source-map',
    version: '0.3.5',
    from: 'convert-source-map@0.3.5',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader/rework'
  }
] 

minimatch:
[
  {
    name: 'minimatch',
    version: '3.1.2',
    from: 'minimatch@3.1.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-babel/babel-core'
  },
  {
    name: 'minimatch',
    version: '5.1.6',
    from: 'minimatch@5.1.6',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/babel-plugin-module-resolver/glob'
  },
  {
    name: 'minimatch',
    version: '3.0.8',
    from: 'minimatch@3.0.8',
    path: '@mumod/repayment/node-sass/gaze/globule/glob'
  }
] 

less:
[
  {
    name: 'less',
    version: '3.13.1',
    from: 'less@3.13.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-less'
  },
  {
    name: 'less',
    version: '3.0.4',
    from: 'less@3.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner'
  }
] 

mime:
[
  {
    name: 'mime',
    version: '1.6.0',
    from: 'mime@1.6.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-less/less'
  },
  {
    name: 'mime',
    version: '2.6.0',
    from: 'mime@2.6.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/url-loader'
  }
] 

tslib:
[
  {
    name: 'tslib',
    version: '1.14.1',
    from: 'tslib@1.14.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-less/less'
  },
  {
    name: 'tslib',
    version: '1.10.0',
    from: 'tslib@1.10.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator'
  }
] 

fs-extra:
[
  {
    name: 'fs-extra',
    version: '5.0.0',
    from: 'fs-extra@5.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle'
  },
  {
    name: 'fs-extra',
    version: '11.3.0',
    from: 'fs-extra@11.3.0',
    path: '@mumod/repayment/@mu/op-comp'
  }
] 

jsonfile:
[
  {
    name: 'jsonfile',
    version: '4.0.0',
    from: 'jsonfile@4.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/fs-extra'
  },
  {
    name: 'jsonfile',
    version: '6.1.0',
    from: 'jsonfile@6.1.0',
    path: '@mumod/repayment/@mu/op-comp/fs-extra'
  }
] 

universalify:
[
  {
    name: 'universalify',
    version: '0.1.2',
    from: 'universalify@0.1.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/fs-extra'
  },
  {
    name: 'universalify',
    version: '2.0.1',
    from: 'universalify@2.0.1',
    path: '@mumod/repayment/@mu/op-comp/fs-extra/jsonfile'
  }
] 

promise:
[
  {
    name: 'promise',
    version: '8.3.0',
    from: 'promise@8.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle'
  },
  {
    name: 'promise',
    version: '7.3.1',
    from: 'promise@7.3.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/less'
  }
] 

yargs:
[
  {
    name: 'yargs',
    version: '13.3.2',
    from: 'yargs@13.3.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle'
  },
  {
    name: 'yargs',
    version: '12.0.2',
    from: 'yargs@12.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server'
  },
  {
    name: 'yargs',
    version: '8.0.2',
    from: 'yargs@8.0.2',
    path: '@mumod/repayment/@mu/taro-adv/standard-version'
  },
  {
    name: 'yargs',
    version: '14.2.3',
    from: 'yargs@^14.2.3',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/libnpx'
  },
  {
    name: 'yargs',
    version: '7.1.2',
    from: 'yargs@7.1.2',
    path: '@mumod/repayment/node-sass/sass-graph'
  }
] 

cliui:
[
  {
    name: 'cliui',
    version: '5.0.0',
    from: 'cliui@5.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs'
  },
  {
    name: 'cliui',
    version: '4.1.0',
    from: 'cliui@4.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs'
  },
  {
    name: 'cliui',
    version: '3.2.0',
    from: 'cliui@3.2.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs'
  }
] 

string-width:
[
  {
    name: 'string-width',
    version: '3.1.0',
    from: 'string-width@3.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/cliui'
  },
  {
    name: 'string-width',
    version: '2.1.1',
    from: 'string-width@2.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/cliui'
  },
  {
    name: 'string-width',
    version: '4.2.3',
    from: 'string-width@4.2.3',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen/ansi-align'
  },
  {
    name: 'string-width',
    version: '1.0.2',
    from: 'string-width@1.0.2',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs/cliui'
  }
] 

strip-ansi:
[
  {
    name: 'strip-ansi',
    version: '5.2.0',
    from: 'strip-ansi@5.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/cliui'
  },
  {
    name: 'strip-ansi',
    version: '3.0.1',
    from: 'strip-ansi@3.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid'
  },
  {
    name: 'strip-ansi',
    version: '4.0.0',
    from: 'strip-ansi@4.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/mini-css-extract-plugin/@webpack-contrib/schema-utils'
  },
  {
    name: 'strip-ansi',
    version: '6.0.1',
    from: 'strip-ansi@6.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen/ansi-align/string-width'
  }
] 

ansi-regex:
[
  {
    name: 'ansi-regex',
    version: '4.1.1',
    from: 'ansi-regex@4.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/cliui/strip-ansi'
  },
  {
    name: 'ansi-regex',
    version: '3.0.1',
    from: 'ansi-regex@3.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/mini-css-extract-plugin/@webpack-contrib/schema-utils/strip-ansi'
  },
  {
    name: 'ansi-regex',
    version: '5.0.1',
    from: 'ansi-regex@5.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen/ansi-align/string-width/strip-ansi'
  },
  {
    name: 'ansi-regex',
    version: '3.0.0',
    from: 'ansi-regex@^3.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/cli-columns/string-width/strip-ansi'
  },
  {
    name: 'ansi-regex',
    version: '2.1.1',
    from: 'ansi-regex@2.1.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/cli-columns/strip-ansi'
  },
  {
    name: 'ansi-regex',
    version: '4.1.0',
    from: 'ansi-regex@4.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/libnpx/yargs/string-width/strip-ansi'
  }
] 

wrap-ansi:
[
  {
    name: 'wrap-ansi',
    version: '5.1.0',
    from: 'wrap-ansi@5.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/cliui'
  },
  {
    name: 'wrap-ansi',
    version: '2.1.0',
    from: 'wrap-ansi@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/cliui'
  },
  {
    name: 'wrap-ansi',
    version: '7.0.0',
    from: 'wrap-ansi@7.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen'
  }
] 

get-caller-file:
[
  {
    name: 'get-caller-file',
    version: '2.0.5',
    from: 'get-caller-file@2.0.5',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs'
  },
  {
    name: 'get-caller-file',
    version: '1.0.3',
    from: 'get-caller-file@1.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs'
  }
] 

require-main-filename:
[
  {
    name: 'require-main-filename',
    version: '2.0.0',
    from: 'require-main-filename@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs'
  },
  {
    name: 'require-main-filename',
    version: '1.0.1',
    from: 'require-main-filename@1.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs'
  }
] 

emoji-regex:
[
  {
    name: 'emoji-regex',
    version: '7.0.3',
    from: 'emoji-regex@7.0.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/string-width'
  },
  {
    name: 'emoji-regex',
    version: '8.0.0',
    from: 'emoji-regex@8.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen/ansi-align/string-width'
  }
] 

is-fullwidth-code-point:
[
  {
    name: 'is-fullwidth-code-point',
    version: '2.0.0',
    from: 'is-fullwidth-code-point@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/string-width'
  },
  {
    name: 'is-fullwidth-code-point',
    version: '3.0.0',
    from: 'is-fullwidth-code-point@3.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen/ansi-align/string-width'
  },
  {
    name: 'is-fullwidth-code-point',
    version: '1.0.0',
    from: 'is-fullwidth-code-point@1.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npmlog/gauge/string-width'
  }
] 

which-module:
[
  {
    name: 'which-module',
    version: '2.0.1',
    from: 'which-module@2.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs'
  },
  {
    name: 'which-module',
    version: '2.0.0',
    from: 'which-module@^2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/libnpx/yargs'
  },
  {
    name: 'which-module',
    version: '1.0.0',
    from: 'which-module@1.0.0',
    path: '@mumod/repayment/node-sass/sass-graph/yargs'
  }
] 

yargs-parser:
[
  {
    name: 'yargs-parser',
    version: '13.1.2',
    from: 'yargs-parser@13.1.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs'
  },
  {
    name: 'yargs-parser',
    version: '10.1.0',
    from: 'yargs-parser@10.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs'
  },
  {
    name: 'yargs-parser',
    version: '7.0.0',
    from: 'yargs-parser@7.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs'
  },
  {
    name: 'yargs-parser',
    version: '15.0.1',
    from: 'yargs-parser@^15.0.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/libnpx/yargs'
  },
  {
    name: 'yargs-parser',
    version: '5.0.1',
    from: 'yargs-parser@5.0.1',
    path: '@mumod/repayment/node-sass/sass-graph/yargs'
  }
] 

decamelize:
[
  {
    name: 'decamelize',
    version: '1.2.0',
    from: 'decamelize@1.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/yargs-parser'
  },
  {
    name: 'decamelize',
    version: '2.0.0',
    from: 'decamelize@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs'
  }
] 

camelcase:
[
  {
    name: 'camelcase',
    version: '5.3.1',
    from: 'camelcase@5.3.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-sass/scss-bundle/yargs/yargs-parser'
  },
  {
    name: 'camelcase',
    version: '1.2.1',
    from: 'camelcase@1.2.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader/adjust-sourcemap-loader'
  },
  {
    name: 'camelcase',
    version: '4.1.0',
    from: 'camelcase@4.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader'
  },
  {
    name: 'camelcase',
    version: '6.3.0',
    from: 'camelcase@6.3.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen'
  },
  {
    name: 'camelcase',
    version: '2.1.1',
    from: 'camelcase@2.1.1',
    path: '@mumod/repayment/node-sass/meow/camelcase-keys'
  },
  {
    name: 'camelcase',
    version: '3.0.0',
    from: 'camelcase@3.0.0',
    path: '@mumod/repayment/node-sass/sass-graph/yargs/yargs-parser'
  }
] 

stylus:
[
  {
    name: 'stylus',
    version: '0.54.8',
    from: 'stylus@0.54.8',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-stylus'
  },
  {
    name: 'stylus',
    version: '0.54.5',
    from: 'stylus@0.54.5',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner'
  }
] 

css-parse:
[
  {
    name: 'css-parse',
    version: '2.0.0',
    from: 'css-parse@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-stylus/stylus'
  },
  {
    name: 'css-parse',
    version: '1.7.0',
    from: 'css-parse@1.7.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/stylus'
  }
] 

uglify-js:
[
  {
    name: 'uglify-js',
    version: '3.19.3',
    from: 'uglify-js@3.19.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/plugin-uglifyjs'
  },
  {
    name: 'uglify-js',
    version: '3.4.10',
    from: 'uglify-js@3.4.10',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/html-minifier'
  }
] 

rollup-plugin-alias:
[
  {
    name: 'rollup-plugin-alias',
    version: '1.4.0',
    from: 'rollup-plugin-alias@1.4.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/router'
  },
  {
    name: 'rollup-plugin-alias',
    version: '2.2.0',
    from: 'rollup-plugin-alias@2.2.0',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils'
  }
] 

prop-types:
[
  {
    name: 'prop-types',
    version: '15.8.1',
    from: 'prop-types@15.8.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/taro-alipay'
  },
  {
    name: 'prop-types',
    version: '15.7.2',
    from: 'prop-types@15.7.2',
    path: '@mumod/repayment/@mu/op-comp'
  }
] 

color-name:
[
  {
    name: 'color-name',
    version: '1.1.3',
    from: 'color-name@1.1.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/chalk/ansi-styles/color-convert'
  },
  {
    name: 'color-name',
    version: '1.1.4',
    from: 'color-name@1.1.4',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen/chalk/ansi-styles/color-convert'
  }
] 

postcss:
[
  {
    name: 'postcss',
    version: '6.0.23',
    from: 'postcss@6.0.23',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/autoprefixer'
  },
  {
    name: 'postcss',
    version: '5.2.18',
    from: 'postcss@5.2.18',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/postcss-pxtransform/postcss-pxtorem'
  }
] 

p-locate:
[
  {
    name: 'p-locate',
    version: '2.0.0',
    from: 'p-locate@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir/find-up/locate-path'
  },
  {
    name: 'p-locate',
    version: '3.0.0',
    from: 'p-locate@3.0.0',
    path: '@mumod/repayment/@mu/onepass-shell/babel-plugin-module-resolver/pkg-up/find-up/locate-path'
  }
] 

p-limit:
[
  {
    name: 'p-limit',
    version: '1.3.0',
    from: 'p-limit@1.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir/find-up/locate-path/p-locate'
  },
  {
    name: 'p-limit',
    version: '2.3.0',
    from: 'p-limit@2.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin'
  },
  {
    name: 'p-limit',
    version: '2.2.0',
    from: 'p-limit@^2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/libnpm/libnpmconfig/find-up/locate-path/p-locate'
  }
] 

p-try:
[
  {
    name: 'p-try',
    version: '1.0.0',
    from: 'p-try@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir/find-up/locate-path/p-locate/p-limit'
  },
  {
    name: 'p-try',
    version: '2.2.0',
    from: 'p-try@2.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin/p-limit'
  }
] 

path-exists:
[
  {
    name: 'path-exists',
    version: '3.0.0',
    from: 'path-exists@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/babel-loader/find-cache-dir/pkg-dir/find-up/locate-path'
  },
  {
    name: 'path-exists',
    version: '2.1.0',
    from: 'path-exists@2.1.0',
    path: '@mumod/repayment/node-sass/meow/read-pkg-up/find-up'
  }
] 

webpack-log:
[
  {
    name: 'webpack-log',
    version: '2.0.0',
    from: 'webpack-log@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/copy-webpack-plugin'
  },
  {
    name: 'webpack-log',
    version: '1.2.0',
    from: 'webpack-log@1.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/mini-css-extract-plugin/@webpack-contrib/schema-utils'
  }
] 

async:
[
  {
    name: 'async',
    version: '2.6.4',
    from: 'async@2.6.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/csso-webpack-plugin'
  },
  {
    name: 'async',
    version: '3.2.6',
    from: 'async@3.2.6',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/portfinder'
  }
] 

domhandler:
[
  {
    name: 'domhandler',
    version: '4.3.1',
    from: 'domhandler@4.3.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/html-webpack-plugin/pretty-error/renderkid/htmlparser2'
  },
  {
    name: 'domhandler',
    version: '2.4.2',
    from: 'domhandler@2.4.2',
    path: '@mumod/repayment/@mu/zui/@mu/mini-html-parser2'
  }
] 

clone:
[
  {
    name: 'clone',
    version: '2.1.2',
    from: 'clone@2.1.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/less-loader'
  },
  {
    name: 'clone',
    version: '1.0.4',
    from: 'clone@1.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/ora/wcwidth/defaults'
  }
] 

opn:
[
  {
    name: 'opn',
    version: '5.3.0',
    from: 'opn@5.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner'
  },
  {
    name: 'opn',
    version: '4.0.2',
    from: 'opn@4.0.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/opencollective'
  }
] 

mimic-fn:
[
  {
    name: 'mimic-fn',
    version: '1.2.0',
    from: 'mimic-fn@1.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/ora/cli-cursor/restore-cursor/onetime'
  },
  {
    name: 'mimic-fn',
    version: '2.1.0',
    from: 'mimic-fn@2.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/os-locale/mem'
  }
] 

defaults:
[
  {
    name: 'defaults',
    version: '1.0.4',
    from: 'defaults@1.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/ora/wcwidth'
  },
  {
    name: 'defaults',
    version: '1.0.3',
    from: 'defaults@^1.0.3',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/columnify/wcwidth'
  }
] 

parse-json:
[
  {
    name: 'parse-json',
    version: '4.0.0',
    from: 'parse-json@4.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/postcss-loader/postcss-load-config/cosmiconfig'
  },
  {
    name: 'parse-json',
    version: '2.2.0',
    from: 'parse-json@2.2.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs/read-pkg-up/read-pkg/load-json-file'
  }
] 

resolve-from:
[
  {
    name: 'resolve-from',
    version: '3.0.0',
    from: 'resolve-from@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/postcss-loader/postcss-load-config/cosmiconfig/import-fresh'
  },
  {
    name: 'resolve-from',
    version: '4.0.0',
    from: 'resolve-from@^4.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-lifecycle'
  }
] 

lodash.defaults:
[
  {
    name: 'lodash.defaults',
    version: '3.1.2',
    from: 'lodash.defaults@3.1.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader/adjust-sourcemap-loader'
  },
  {
    name: 'lodash.defaults',
    version: '4.2.0',
    from: 'lodash.defaults@4.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader'
  }
] 

lodash.assign:
[
  {
    name: 'lodash.assign',
    version: '3.2.0',
    from: 'lodash.assign@3.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader/adjust-sourcemap-loader/lodash.defaults'
  },
  {
    name: 'lodash.assign',
    version: '4.2.0',
    from: 'lodash.assign@4.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/resolve-url-loader/adjust-sourcemap-loader'
  }
] 

array-flatten:
[
  {
    name: 'array-flatten',
    version: '2.1.2',
    from: 'array-flatten@2.1.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/bonjour'
  },
  {
    name: 'array-flatten',
    version: '1.1.1',
    from: 'array-flatten@1.1.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express'
  }
] 

ip:
[
  {
    name: 'ip',
    version: '1.1.9',
    from: 'ip@1.1.9',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/bonjour/multicast-dns/dns-packet'
  },
  {
    name: 'ip',
    version: '1.1.5',
    from: 'ip@^1.1.5',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen/socks-proxy-agent/socks'
  }
] 

negotiator:
[
  {
    name: 'negotiator',
    version: '0.6.4',
    from: 'negotiator@0.6.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/compression'
  },
  {
    name: 'negotiator',
    version: '0.6.3',
    from: 'negotiator@0.6.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/accepts'
  }
] 

del:
[
  {
    name: 'del',
    version: '3.0.0',
    from: 'del@3.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server'
  },
  {
    name: 'del',
    version: '4.1.1',
    from: 'del@4.1.1',
    path: '@mumod/repayment/@mu/trade-password-encrypted-shell/clean-webpack-plugin'
  }
] 

is-path-cwd:
[
  {
    name: 'is-path-cwd',
    version: '1.0.0',
    from: 'is-path-cwd@1.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/del'
  },
  {
    name: 'is-path-cwd',
    version: '2.2.0',
    from: 'is-path-cwd@2.2.0',
    path: '@mumod/repayment/@mu/trade-password-encrypted-shell/clean-webpack-plugin/del'
  }
] 

is-path-in-cwd:
[
  {
    name: 'is-path-in-cwd',
    version: '1.0.1',
    from: 'is-path-in-cwd@1.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/del'
  },
  {
    name: 'is-path-in-cwd',
    version: '2.1.0',
    from: 'is-path-in-cwd@2.1.0',
    path: '@mumod/repayment/@mu/trade-password-encrypted-shell/clean-webpack-plugin/del'
  }
] 

is-path-inside:
[
  {
    name: 'is-path-inside',
    version: '1.0.1',
    from: 'is-path-inside@1.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/del/is-path-in-cwd'
  },
  {
    name: 'is-path-inside',
    version: '2.1.0',
    from: 'is-path-inside@2.1.0',
    path: '@mumod/repayment/@mu/trade-password-encrypted-shell/clean-webpack-plugin/del/is-path-in-cwd'
  }
] 

p-map:
[
  {
    name: 'p-map',
    version: '1.2.0',
    from: 'p-map@1.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/del'
  },
  {
    name: 'p-map',
    version: '4.0.0',
    from: 'p-map@4.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen/cacache'
  },
  {
    name: 'p-map',
    version: '2.1.0',
    from: 'p-map@2.1.0',
    path: '@mumod/repayment/@mu/trade-password-encrypted-shell/clean-webpack-plugin/del'
  }
] 

mime-types:
[
  {
    name: 'mime-types',
    version: '2.1.35',
    from: 'mime-types@2.1.35',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/accepts'
  },
  {
    name: 'mime-types',
    version: '2.1.19',
    from: 'mime-types@2.1.19',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/request/form-data'
  }
] 

depd:
[
  {
    name: 'depd',
    version: '2.0.0',
    from: 'depd@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/body-parser'
  },
  {
    name: 'depd',
    version: '1.1.2',
    from: 'depd@1.1.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/serve-index/http-errors'
  }
] 

http-errors:
[
  {
    name: 'http-errors',
    version: '2.0.0',
    from: 'http-errors@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/body-parser'
  },
  {
    name: 'http-errors',
    version: '1.6.3',
    from: 'http-errors@1.6.3',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/serve-index'
  }
] 

iconv-lite:
[
  {
    name: 'iconv-lite',
    version: '0.4.24',
    from: 'iconv-lite@0.4.24',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/body-parser'
  },
  {
    name: 'iconv-lite',
    version: '0.6.3',
    from: 'iconv-lite@0.6.3',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/minipass-fetch/encoding'
  },
  {
    name: 'iconv-lite',
    version: '0.4.23',
    from: 'iconv-lite@~0.4.13',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen/node-fetch-npm/encoding'
  }
] 

encodeurl:
[
  {
    name: 'encodeurl',
    version: '2.0.0',
    from: 'encodeurl@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express'
  },
  {
    name: 'encodeurl',
    version: '1.0.2',
    from: 'encodeurl@1.0.2',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/send'
  }
] 

statuses:
[
  {
    name: 'statuses',
    version: '2.0.1',
    from: 'statuses@2.0.1',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/finalhandler'
  },
  {
    name: 'statuses',
    version: '1.5.0',
    from: 'statuses@1.5.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/serve-index/http-errors'
  }
] 

setprototypeof:
[
  {
    name: 'setprototypeof',
    version: '1.2.0',
    from: 'setprototypeof@1.2.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/express/http-errors'
  },
  {
    name: 'setprototypeof',
    version: '1.1.0',
    from: 'setprototypeof@1.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/serve-index/http-errors'
  }
] 

eventemitter3:
[
  {
    name: 'eventemitter3',
    version: '4.0.7',
    from: 'eventemitter3@4.0.7',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/http-proxy-middleware/http-proxy'
  },
  {
    name: 'eventemitter3',
    version: '4.0.0',
    from: 'eventemitter3@4.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator'
  }
] 

faye-websocket:
[
  {
    name: 'faye-websocket',
    version: '0.10.0',
    from: 'faye-websocket@0.10.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/sockjs'
  },
  {
    name: 'faye-websocket',
    version: '0.11.4',
    from: 'faye-websocket@0.11.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/sockjs-client'
  }
] 

os-locale:
[
  {
    name: 'os-locale',
    version: '3.1.0',
    from: 'os-locale@3.1.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs'
  },
  {
    name: 'os-locale',
    version: '2.1.0',
    from: 'os-locale@2.1.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs'
  },
  {
    name: 'os-locale',
    version: '1.4.0',
    from: 'os-locale@1.4.0',
    path: '@mumod/repayment/node-sass/sass-graph/yargs'
  }
] 

mem:
[
  {
    name: 'mem',
    version: '4.3.0',
    from: 'mem@4.3.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/os-locale'
  },
  {
    name: 'mem',
    version: '1.1.0',
    from: 'mem@1.1.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs/os-locale'
  }
] 

lcid:
[
  {
    name: 'lcid',
    version: '2.0.0',
    from: 'lcid@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/os-locale'
  },
  {
    name: 'lcid',
    version: '1.0.0',
    from: 'lcid@1.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs/os-locale'
  }
] 

invert-kv:
[
  {
    name: 'invert-kv',
    version: '2.0.0',
    from: 'invert-kv@2.0.0',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/@tarojs/webpack-runner/webpack-dev-server/yargs/os-locale/lcid'
  },
  {
    name: 'invert-kv',
    version: '1.0.0',
    from: 'invert-kv@1.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs/os-locale/lcid'
  }
] 

has:
[
  {
    name: 'has',
    version: '1.0.4',
    from: 'has@1.0.4',
    path: '@mumod/repayment/@mu/basic-library/@mu/madp/eslint-plugin-taro'
  },
  {
    name: 'has',
    version: '1.0.3',
    from: 'has@1.0.3',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors/es-abstract'
  }
] 

babel-plugin-module-resolver:
[
  {
    name: 'babel-plugin-module-resolver',
    version: '5.0.0',
    from: 'babel-plugin-module-resolver@5.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils'
  },
  {
    name: 'babel-plugin-module-resolver',
    version: '4.1.0',
    from: 'babel-plugin-module-resolver@4.1.0',
    path: '@mumod/repayment/@mu/onepass-shell'
  }
] 

brace-expansion:
[
  {
    name: 'brace-expansion',
    version: '2.0.1',
    from: 'brace-expansion@2.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/babel-plugin-module-resolver/glob/minimatch'
  },
  {
    name: 'brace-expansion',
    version: '1.1.11',
    from: 'brace-expansion@^1.1.7',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/glob/minimatch'
  }
] 

find-babel-config:
[
  {
    name: 'find-babel-config',
    version: '2.1.2',
    from: 'find-babel-config@2.1.2',
    path: '@mumod/repayment/@mu/biometrics-shell/@mu/biometrics-utils/babel-plugin-module-resolver'
  },
  {
    name: 'find-babel-config',
    version: '1.2.2',
    from: 'find-babel-config@1.2.2',
    path: '@mumod/repayment/@mu/onepass-shell/babel-plugin-module-resolver'
  }
] 

function-bind:
[
  {
    name: 'function-bind',
    version: '1.1.2',
    from: 'function-bind@1.1.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-commonjs/resolve/is-core-module/hasown'
  },
  {
    name: 'function-bind',
    version: '1.1.1',
    from: 'function-bind@1.1.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/read-package-tree/util-promisify/object.getownpropertydescriptors/es-abstract'
  }
] 

boxen:
[
  {
    name: 'boxen',
    version: '5.1.2',
    from: 'boxen@5.1.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize'
  },
  {
    name: 'boxen',
    version: '1.3.0',
    from: 'boxen@^1.2.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier'
  }
] 

ansi-align:
[
  {
    name: 'ansi-align',
    version: '3.0.1',
    from: 'ansi-align@3.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen'
  },
  {
    name: 'ansi-align',
    version: '2.0.0',
    from: 'ansi-align@^2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/boxen'
  }
] 

cli-boxes:
[
  {
    name: 'cli-boxes',
    version: '2.2.1',
    from: 'cli-boxes@2.2.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen'
  },
  {
    name: 'cli-boxes',
    version: '1.0.0',
    from: 'cli-boxes@^1.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/boxen'
  }
] 

widest-line:
[
  {
    name: 'widest-line',
    version: '3.1.0',
    from: 'widest-line@3.1.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/boxen'
  },
  {
    name: 'widest-line',
    version: '2.0.1',
    from: 'widest-line@^2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/boxen'
  }
] 

duplexer:
[
  {
    name: 'duplexer',
    version: '0.1.1',
    from: 'duplexer@0.1.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/brotli-size'
  },
  {
    name: 'duplexer',
    version: '0.1.2',
    from: 'duplexer@0.1.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/gzip-size'
  }
] 

colors:
[
  {
    name: 'colors',
    version: '1.4.0',
    from: 'colors@1.4.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize'
  },
  {
    name: 'colors',
    version: '1.3.3',
    from: 'colors@^1.1.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/cli-table3'
  }
] 

pacote:
[
  {
    name: 'pacote',
    version: '11.3.5',
    from: 'pacote@11.3.5',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize'
  },
  {
    name: 'pacote',
    version: '9.5.12',
    from: 'pacote@9.5.12',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/libcipm'
  }
] 

npm-pick-manifest:
[
  {
    name: 'npm-pick-manifest',
    version: '6.1.1',
    from: 'npm-pick-manifest@6.1.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/git'
  },
  {
    name: 'npm-pick-manifest',
    version: '3.0.2',
    from: 'npm-pick-manifest@3.0.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm'
  }
] 

promise-retry:
[
  {
    name: 'promise-retry',
    version: '2.0.1',
    from: 'promise-retry@2.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/git'
  },
  {
    name: 'promise-retry',
    version: '1.1.1',
    from: 'promise-retry@^1.1.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen'
  }
] 

npm-bundled:
[
  {
    name: 'npm-bundled',
    version: '1.1.2',
    from: 'npm-bundled@1.1.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/installed-package-contents'
  },
  {
    name: 'npm-bundled',
    version: '1.1.1',
    from: 'npm-bundled@^1.0.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-packlist'
  }
] 

node-gyp:
[
  {
    name: 'node-gyp',
    version: '7.1.2',
    from: 'node-gyp@7.1.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/run-script'
  },
  {
    name: 'node-gyp',
    version: '5.1.1',
    from: 'node-gyp@5.1.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm'
  },
  {
    name: 'node-gyp',
    version: '3.8.0',
    from: 'node-gyp@3.8.0',
    path: '@mumod/repayment/node-sass'
  }
] 

nopt:
[
  {
    name: 'nopt',
    version: '5.0.0',
    from: 'nopt@5.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/run-script/node-gyp'
  },
  {
    name: 'nopt',
    version: '4.0.3',
    from: 'nopt@4.0.3',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/node-gyp'
  },
  {
    name: 'nopt',
    version: '3.0.6',
    from: 'nopt@3.0.6',
    path: '@mumod/repayment/node-sass/node-gyp'
  }
] 

tar:
[
  {
    name: 'tar',
    version: '6.2.1',
    from: 'tar@6.2.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/run-script/node-gyp'
  },
  {
    name: 'tar',
    version: '4.4.19',
    from: 'tar@4.4.19',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/node-gyp'
  },
  {
    name: 'tar',
    version: '2.2.2',
    from: 'tar@2.2.2',
    path: '@mumod/repayment/node-sass/node-gyp'
  }
] 

minipass:
[
  {
    name: 'minipass',
    version: '5.0.0',
    from: 'minipass@5.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/run-script/node-gyp/tar'
  },
  {
    name: 'minipass',
    version: '3.3.6',
    from: 'minipass@3.3.6',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/fs-minipass'
  },
  {
    name: 'minipass',
    version: '2.9.0',
    from: 'minipass@^2.3.5',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/pacote'
  }
] 

fs-minipass:
[
  {
    name: 'fs-minipass',
    version: '2.1.0',
    from: 'fs-minipass@2.1.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/run-script/node-gyp/tar'
  },
  {
    name: 'fs-minipass',
    version: '1.2.7',
    from: 'fs-minipass@^1.2.5',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/tar'
  }
] 

minizlib:
[
  {
    name: 'minizlib',
    version: '2.1.2',
    from: 'minizlib@2.1.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/@npmcli/run-script/node-gyp/tar'
  },
  {
    name: 'minizlib',
    version: '1.3.3',
    from: 'minizlib@^1.2.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/tar'
  }
] 

npm-package-arg:
[
  {
    name: 'npm-package-arg',
    version: '8.1.5',
    from: 'npm-package-arg@8.1.5',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote'
  },
  {
    name: 'npm-package-arg',
    version: '6.1.1',
    from: 'npm-package-arg@6.1.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/init-package-json'
  }
] 

hosted-git-info:
[
  {
    name: 'hosted-git-info',
    version: '4.1.0',
    from: 'hosted-git-info@4.1.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-package-arg'
  },
  {
    name: 'hosted-git-info',
    version: '2.8.9',
    from: 'hosted-git-info@2.8.9',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/get-pkg-repo'
  }
] 

npm-packlist:
[
  {
    name: 'npm-packlist',
    version: '2.2.2',
    from: 'npm-packlist@2.2.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote'
  },
  {
    name: 'npm-packlist',
    version: '1.4.8',
    from: 'npm-packlist@1.4.8',
    path: '@mumod/repayment/find-duplicate-dependencies/npm'
  }
] 

ignore-walk:
[
  {
    name: 'ignore-walk',
    version: '3.0.4',
    from: 'ignore-walk@3.0.4',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-packlist'
  },
  {
    name: 'ignore-walk',
    version: '3.0.3',
    from: 'ignore-walk@^3.0.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-packlist'
  }
] 

npm-install-checks:
[
  {
    name: 'npm-install-checks',
    version: '4.0.0',
    from: 'npm-install-checks@4.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-pick-manifest'
  },
  {
    name: 'npm-install-checks',
    version: '3.0.2',
    from: 'npm-install-checks@3.0.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm'
  }
] 

npm-registry-fetch:
[
  {
    name: 'npm-registry-fetch',
    version: '11.0.0',
    from: 'npm-registry-fetch@11.0.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote'
  },
  {
    name: 'npm-registry-fetch',
    version: '4.0.7',
    from: 'npm-registry-fetch@4.0.7',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/libnpm/libnpmpublish'
  }
] 

make-fetch-happen:
[
  {
    name: 'make-fetch-happen',
    version: '9.1.0',
    from: 'make-fetch-happen@9.1.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch'
  },
  {
    name: 'make-fetch-happen',
    version: '5.0.2',
    from: 'make-fetch-happen@5.0.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch'
  }
] 

agentkeepalive:
[
  {
    name: 'agentkeepalive',
    version: '4.6.0',
    from: 'agentkeepalive@4.6.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen'
  },
  {
    name: 'agentkeepalive',
    version: '3.5.2',
    from: 'agentkeepalive@^3.4.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen'
  }
] 

http-proxy-agent:
[
  {
    name: 'http-proxy-agent',
    version: '4.0.1',
    from: 'http-proxy-agent@4.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen'
  },
  {
    name: 'http-proxy-agent',
    version: '2.1.0',
    from: 'http-proxy-agent@^2.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen'
  }
] 

agent-base:
[
  {
    name: 'agent-base',
    version: '6.0.2',
    from: 'agent-base@6.0.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen/http-proxy-agent'
  },
  {
    name: 'agent-base',
    version: '4.3.0',
    from: 'agent-base@4',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen/http-proxy-agent'
  },
  {
    name: 'agent-base',
    version: '4.2.1',
    from: 'agent-base@~4.2.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen/socks-proxy-agent'
  }
] 

https-proxy-agent:
[
  {
    name: 'https-proxy-agent',
    version: '5.0.1',
    from: 'https-proxy-agent@5.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen'
  },
  {
    name: 'https-proxy-agent',
    version: '2.2.4',
    from: 'https-proxy-agent@^2.2.3',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen'
  }
] 

socks-proxy-agent:
[
  {
    name: 'socks-proxy-agent',
    version: '6.2.1',
    from: 'socks-proxy-agent@6.2.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen'
  },
  {
    name: 'socks-proxy-agent',
    version: '4.0.2',
    from: 'socks-proxy-agent@^4.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen'
  }
] 

socks:
[
  {
    name: 'socks',
    version: '2.8.4',
    from: 'socks@2.8.4',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen/socks-proxy-agent'
  },
  {
    name: 'socks',
    version: '2.3.3',
    from: 'socks@~2.3.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen/socks-proxy-agent'
  }
] 

jsbn:
[
  {
    name: 'jsbn',
    version: '1.1.0',
    from: 'jsbn@1.1.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen/socks-proxy-agent/socks/ip-address'
  },
  {
    name: 'jsbn',
    version: '0.1.1',
    from: 'jsbn@~0.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/request/http-signature/sshpk/ecc-jsbn'
  }
] 

smart-buffer:
[
  {
    name: 'smart-buffer',
    version: '4.2.0',
    from: 'smart-buffer@4.2.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/make-fetch-happen/socks-proxy-agent/socks'
  },
  {
    name: 'smart-buffer',
    version: '4.1.0',
    from: 'smart-buffer@^4.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen/socks-proxy-agent/socks'
  }
] 

encoding:
[
  {
    name: 'encoding',
    version: '0.1.13',
    from: 'encoding@0.1.13',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/npm-registry-fetch/minipass-fetch'
  },
  {
    name: 'encoding',
    version: '0.1.12',
    from: 'encoding@^0.1.11',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npm-registry-fetch/make-fetch-happen/node-fetch-npm'
  }
] 

err-code:
[
  {
    name: 'err-code',
    version: '2.0.3',
    from: 'err-code@2.0.3',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/promise-retry'
  },
  {
    name: 'err-code',
    version: '1.1.2',
    from: 'err-code@^1.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/pacote/promise-retry'
  }
] 

retry:
[
  {
    name: 'retry',
    version: '0.12.0',
    from: 'retry@0.12.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/pacote/promise-retry'
  },
  {
    name: 'retry',
    version: '0.10.1',
    from: 'retry@^0.10.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/pacote/promise-retry'
  }
] 

buffer-from:
[
  {
    name: 'buffer-from',
    version: '1.1.2',
    from: 'buffer-from@1.1.2',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-filesize/terser/source-map-support'
  },
  {
    name: 'buffer-from',
    version: '1.0.0',
    from: 'buffer-from@^1.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mississippi/concat-stream'
  }
] 

minimist:
[
  {
    name: 'minimist',
    version: '0.0.8',
    from: 'minimist@0.0.8',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/mkdirp'
  },
  {
    name: 'minimist',
    version: '1.2.0',
    from: 'minimist@1.2.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/opencollective'
  },
  {
    name: 'minimist',
    version: '1.2.8',
    from: 'minimist@1.2.8',
    path: '@mumod/repayment/@mu/onepass-shell/babel-plugin-module-resolver/find-babel-config/json5'
  },
  {
    name: 'minimist',
    version: '1.2.6',
    from: 'minimist@1.2.6',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/mkdirp'
  }
] 

arrify:
[
  {
    name: 'arrify',
    version: '2.0.1',
    from: 'arrify@2.0.1',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/multimatch'
  },
  {
    name: 'arrify',
    version: '1.0.1',
    from: 'arrify@1.0.1',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-recommended-bump/conventional-commits-parser/meow/minimist-options'
  }
] 

@types/minimatch:
[
  {
    name: '@types/minimatch',
    version: '3.0.5',
    from: '@types/minimatch@3.0.5',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/multimatch'
  },
  {
    name: '@types/minimatch',
    version: '5.1.2',
    from: '@types/minimatch@5.1.2',
    path: '@mumod/repayment/@mu/trade-password-encrypted-shell/clean-webpack-plugin/del/@types/glob'
  }
] 

babel-polyfill:
[
  {
    name: 'babel-polyfill',
    version: '6.23.0',
    from: 'babel-polyfill@6.23.0',
    path: '@mumod/repayment/@mu/biometrics-shell/rollup-plugin-javascript-obfuscator/javascript-obfuscator/opencollective'
  },
  {
    name: 'babel-polyfill',
    version: '6.26.0',
    from: 'babel-polyfill@6.26.0',
    path: '@mumod/repayment'
  }
] 

dayjs:
[
  {
    name: 'dayjs',
    version: '1.11.6',
    from: 'dayjs@1.11.6',
    path: '@mumod/repayment/@mu/business-plugin'
  },
  {
    name: 'dayjs',
    version: '1.10.7',
    from: 'dayjs@1.10.7',
    path: '@mumod/repayment/@mu/lui'
  },
  {
    name: 'dayjs',
    version: '1.8.20',
    from: 'dayjs@1.8.20',
    path: '@mumod/repayment/@mu/survey/taro-ui'
  }
] 

mo:
[
  {
    name: 'mo',
    version: '1.2.0',
    from: 'mo@1.2.0',
    path: '@mumod/repayment/@mu/business-plugin/nerv/eventmaster'
  },
  {
    name: 'mo',
    version: '1.5.6',
    from: 'mo@1.5.6',
    path: '@mumod/repayment/@mu/business-plugin/nerv'
  }
] 

@mu/onepass-shell:
[
  {
    name: '@mu/onepass-shell',
    version: '1.0.9-beta.4',
    from: '@mu/onepass-shell@1.0.9-beta.4',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/onepass-shell',
    version: '1.0.9-beta.2',
    from: '@mu/onepass-shell@1.0.9-beta.2',
    path: '@mumod/repayment/@mu/safe-sms-shell'
  },
  {
    name: '@mu/onepass-shell',
    version: '1.0.9',
    from: '@mu/onepass-shell@1.0.9',
    path: '@mumod/repayment/@mu/trade-verify-shell'
  }
] 

@mu/safe-sms-shell:
[
  {
    name: '@mu/safe-sms-shell',
    version: '1.2.3-beta.2',
    from: '@mu/safe-sms-shell@1.2.3-beta.2',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/safe-sms-shell',
    version: '1.2.3-beta.3',
    from: '@mu/safe-sms-shell@1.2.3-beta.3',
    path: '@mumod/repayment/@mu/trade-verify-shell'
  }
] 

@types/react:
[
  {
    name: '@types/react',
    version: '16.14.63',
    from: '@types/react@16.14.63',
    path: '@mumod/repayment/@mu/survey/taro-ui'
  },
  {
    name: '@types/react',
    version: '16.9.21',
    from: '@types/react@16.9.21',
    path: '@mumod/repayment/@mu/taro-adv/@mu/zui'
  }
] 

csstype:
[
  {
    name: 'csstype',
    version: '3.1.3',
    from: 'csstype@3.1.3',
    path: '@mumod/repayment/@mu/survey/taro-ui/@types/react'
  },
  {
    name: 'csstype',
    version: '2.6.21',
    from: 'csstype@2.6.21',
    path: '@mumod/repayment/@mu/taro-adv/@mu/zui/@types/react'
  }
] 

@mu/zui:
[
  {
    name: '@mu/zui',
    version: '1.21.0',
    from: '@mu/zui@1.21.0',
    path: '@mumod/repayment/@mu/taro-adv'
  },
  {
    name: '@mu/zui',
    version: '1.24.5-beta.43',
    from: '@mu/zui@1.24.5-beta.43',
    path: '@mumod/repayment'
  }
] 

read-pkg-up:
[
  {
    name: 'read-pkg-up',
    version: '2.0.0',
    from: 'read-pkg-up@2.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs'
  },
  {
    name: 'read-pkg-up',
    version: '3.0.0',
    from: 'read-pkg-up@3.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/conventional-changelog-writer/meow'
  },
  {
    name: 'read-pkg-up',
    version: '1.0.1',
    from: 'read-pkg-up@1.0.1',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core'
  }
] 

read-pkg:
[
  {
    name: 'read-pkg',
    version: '2.0.0',
    from: 'read-pkg@2.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs/read-pkg-up'
  },
  {
    name: 'read-pkg',
    version: '3.0.0',
    from: 'read-pkg@3.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/conventional-changelog-writer/meow/read-pkg-up'
  },
  {
    name: 'read-pkg',
    version: '1.1.0',
    from: 'read-pkg@1.1.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core'
  }
] 

load-json-file:
[
  {
    name: 'load-json-file',
    version: '2.0.0',
    from: 'load-json-file@2.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs/read-pkg-up/read-pkg'
  },
  {
    name: 'load-json-file',
    version: '4.0.0',
    from: 'load-json-file@4.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/conventional-changelog-writer/meow/read-pkg-up/read-pkg'
  },
  {
    name: 'load-json-file',
    version: '1.1.0',
    from: 'load-json-file@1.1.0',
    path: '@mumod/repayment/node-sass/meow/read-pkg-up/read-pkg'
  }
] 

strip-bom:
[
  {
    name: 'strip-bom',
    version: '3.0.0',
    from: 'strip-bom@3.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/yargs/read-pkg-up/read-pkg/load-json-file'
  },
  {
    name: 'strip-bom',
    version: '2.0.0',
    from: 'strip-bom@2.0.0',
    path: '@mumod/repayment/node-sass/meow/read-pkg-up/read-pkg/load-json-file'
  }
] 

dot-prop:
[
  {
    name: 'dot-prop',
    version: '3.0.0',
    from: 'dot-prop@3.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-angular/compare-func'
  },
  {
    name: 'dot-prop',
    version: '4.2.1',
    from: 'dot-prop@^4.2.1',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/configstore'
  }
] 

camelcase-keys:
[
  {
    name: 'camelcase-keys',
    version: '4.2.0',
    from: 'camelcase-keys@4.2.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/conventional-changelog-writer/meow'
  },
  {
    name: 'camelcase-keys',
    version: '2.1.0',
    from: 'camelcase-keys@2.1.0',
    path: '@mumod/repayment/node-sass/meow'
  }
] 

map-obj:
[
  {
    name: 'map-obj',
    version: '2.0.0',
    from: 'map-obj@2.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/conventional-changelog-writer/meow/camelcase-keys'
  },
  {
    name: 'map-obj',
    version: '1.0.1',
    from: 'map-obj@1.0.1',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-recommended-bump/conventional-commits-parser/meow/decamelize-keys'
  }
] 

redent:
[
  {
    name: 'redent',
    version: '2.0.0',
    from: 'redent@2.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/conventional-changelog-writer/meow'
  },
  {
    name: 'redent',
    version: '1.0.0',
    from: 'redent@1.0.0',
    path: '@mumod/repayment/node-sass/meow'
  }
] 

strip-indent:
[
  {
    name: 'strip-indent',
    version: '2.0.0',
    from: 'strip-indent@2.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/conventional-changelog-writer/meow/redent'
  },
  {
    name: 'strip-indent',
    version: '1.0.1',
    from: 'strip-indent@1.0.1',
    path: '@mumod/repayment/node-sass/meow/redent'
  }
] 

trim-newlines:
[
  {
    name: 'trim-newlines',
    version: '2.0.0',
    from: 'trim-newlines@2.0.0',
    path: '@mumod/repayment/@mu/taro-adv/standard-version/conventional-changelog/conventional-changelog-core/conventional-changelog-writer/meow'
  },
  {
    name: 'trim-newlines',
    version: '1.0.0',
    from: 'trim-newlines@1.0.0',
    path: '@mumod/repayment/node-sass/meow'
  }
] 

@mu/tarosdk-mu-bio-auth:
[
  {
    name: '@mu/tarosdk-mu-bio-auth',
    version: '1.7.4-beta.12',
    from: '@mu/tarosdk-mu-bio-auth@1.7.4-beta.12',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/tarosdk-mu-bio-auth',
    version: '1.7.3-beta.3',
    from: '@mu/tarosdk-mu-bio-auth@1.7.3-beta.3',
    path: '@mumod/repayment/@mu/trade-verify-shell'
  }
] 

@mu/bio-auth-zfb:
[
  {
    name: '@mu/bio-auth-zfb',
    version: '2.4.16-beta.27',
    from: '@mu/bio-auth-zfb@2.4.16-beta.27',
    path: '@mumod/repayment/@mu/tarosdk-mu-bio-auth'
  },
  {
    name: '@mu/bio-auth-zfb',
    version: '2.4.17-beta.2',
    from: '@mu/bio-auth-zfb@2.4.17-beta.2',
    path: '@mumod/repayment/@mu/trade-verify-shell'
  }
] 

@mu/trade-password-encrypted-shell:
[
  {
    name: '@mu/trade-password-encrypted-shell',
    version: '1.1.3-beta.4',
    from: '@mu/trade-password-encrypted-shell@1.1.3-beta.4',
    path: '@mumod/repayment'
  },
  {
    name: '@mu/trade-password-encrypted-shell',
    version: '1.1.3-beta.6',
    from: '@mu/trade-password-encrypted-shell@1.1.3-beta.6',
    path: '@mumod/repayment/@mu/trade-verify-shell'
  }
] 

@mu/mini-html-parser2:
[
  {
    name: '@mu/mini-html-parser2',
    version: '1.1.0-beta.3',
    from: '@mu/mini-html-parser2@1.1.0-beta.3',
    path: '@mumod/repayment/@mu/wa-richtext'
  },
  {
    name: '@mu/mini-html-parser2',
    version: '1.0.0',
    from: '@mu/mini-html-parser2@1.0.0',
    path: '@mumod/repayment/@mu/zui'
  }
] 

ci-info:
[
  {
    name: 'ci-info',
    version: '2.0.0',
    from: 'ci-info@2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm'
  },
  {
    name: 'ci-info',
    version: '1.6.0',
    from: 'ci-info@^1.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/update-notifier/is-ci'
  }
] 

core-util-is:
[
  {
    name: 'core-util-is',
    version: '1.0.2',
    from: 'core-util-is@~1.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/fs-write-stream-atomic/readable-stream'
  },
  {
    name: 'core-util-is',
    version: '1.0.3',
    from: 'core-util-is@1.0.3',
    path: '@mumod/repayment/node-sass/stdout-stream/readable-stream'
  }
] 

process-nextick-args:
[
  {
    name: 'process-nextick-args',
    version: '2.0.0',
    from: 'process-nextick-args@~2.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/fs-write-stream-atomic/readable-stream'
  },
  {
    name: 'process-nextick-args',
    version: '2.0.1',
    from: 'process-nextick-args@2.0.1',
    path: '@mumod/repayment/node-sass/stdout-stream/readable-stream'
  }
] 

are-we-there-yet:
[
  {
    name: 'are-we-there-yet',
    version: '1.1.4',
    from: 'are-we-there-yet@~1.1.2',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npmlog'
  },
  {
    name: 'are-we-there-yet',
    version: '1.1.7',
    from: 'are-we-there-yet@1.1.7',
    path: '@mumod/repayment/node-sass/npmlog'
  }
] 

wide-align:
[
  {
    name: 'wide-align',
    version: '1.1.2',
    from: 'wide-align@^1.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/npmlog/gauge'
  },
  {
    name: 'wide-align',
    version: '1.1.5',
    from: 'wide-align@1.1.5',
    path: '@mumod/repayment/node-sass/npmlog/gauge'
  }
] 

aws4:
[
  {
    name: 'aws4',
    version: '1.11.0',
    from: 'aws4@^1.8.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/request'
  },
  {
    name: 'aws4',
    version: '1.13.2',
    from: 'aws4@1.13.2',
    path: '@mumod/repayment/node-sass/request'
  }
] 

sshpk:
[
  {
    name: 'sshpk',
    version: '1.17.0',
    from: 'sshpk@^1.7.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/request/http-signature'
  },
  {
    name: 'sshpk',
    version: '1.18.0',
    from: 'sshpk@1.18.0',
    path: '@mumod/repayment/node-sass/request/http-signature'
  }
] 

psl:
[
  {
    name: 'psl',
    version: '1.9.0',
    from: 'psl@^1.1.28',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/request/tough-cookie'
  },
  {
    name: 'psl',
    version: '1.15.0',
    from: 'psl@1.15.0',
    path: '@mumod/repayment/node-sass/request/tough-cookie'
  }
] 

spdx-correct:
[
  {
    name: 'spdx-correct',
    version: '3.0.0',
    from: 'spdx-correct@^3.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/validate-npm-package-license'
  },
  {
    name: 'spdx-correct',
    version: '3.2.0',
    from: 'spdx-correct@3.2.0',
    path: '@mumod/repayment/node-sass/meow/normalize-package-data/validate-npm-package-license'
  }
] 

spdx-expression-parse:
[
  {
    name: 'spdx-expression-parse',
    version: '3.0.0',
    from: 'spdx-expression-parse@^3.0.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/validate-npm-package-license/spdx-correct'
  },
  {
    name: 'spdx-expression-parse',
    version: '3.0.1',
    from: 'spdx-expression-parse@3.0.1',
    path: '@mumod/repayment/node-sass/meow/normalize-package-data/validate-npm-package-license/spdx-correct'
  }
] 

spdx-license-ids:
[
  {
    name: 'spdx-license-ids',
    version: '3.0.5',
    from: 'spdx-license-ids@3.0.5',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/validate-npm-package-license/spdx-correct'
  },
  {
    name: 'spdx-license-ids',
    version: '3.0.21',
    from: 'spdx-license-ids@3.0.21',
    path: '@mumod/repayment/node-sass/meow/normalize-package-data/validate-npm-package-license/spdx-correct'
  }
] 

spdx-exceptions:
[
  {
    name: 'spdx-exceptions',
    version: '2.1.0',
    from: 'spdx-exceptions@^2.1.0',
    path: '@mumod/repayment/find-duplicate-dependencies/npm/validate-npm-package-license/spdx-expression-parse'
  },
  {
    name: 'spdx-exceptions',
    version: '2.5.0',
    from: 'spdx-exceptions@2.5.0',
    path: '@mumod/repayment/node-sass/meow/normalize-package-data/validate-npm-package-license/spdx-expression-parse'
  }
] 

ts-invariant:
[
  {
    name: 'ts-invariant',
    version: '0.4.4',
    from: 'ts-invariant@0.4.4',
    path: '@mumod/repayment/graphql-anywhere/apollo-utilities'
  },
  {
    name: 'ts-invariant',
    version: '0.3.3',
    from: 'ts-invariant@0.3.3',
    path: '@mumod/repayment/graphql-anywhere'
  }
] 